<template>
  <div class="job-portal">
    <!-- 顶部导航 -->
    <div class="portal-header">
      <div class="header-container">
        <div class="logo">
          <img src="/logo.png" alt="杭科院">
          <span>杭州科技职业技术学院招聘</span>
        </div>
        <div class="nav-menu">
          <a href="#home" class="nav-item active">首页</a>
          <a href="#jobs" class="nav-item">职位列表</a>
          <a href="#about" class="nav-item">关于我们</a>
          <a href="#contact" class="nav-item">联系方式</a>
        </div>
        <div class="user-actions">
          <el-button v-if="!isLoggedIn" @click="handleLogin">登录</el-button>
          <el-button v-if="!isLoggedIn" type="primary" @click="handleRegister">注册</el-button>
          <el-dropdown v-else>
            <el-button type="primary">
              {{ currentUser.name }}
              <el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="goToMyApplications">我的申请</el-dropdown-item>
                <el-dropdown-item @click="goToMyProfile">个人信息</el-dropdown-item>
                <el-dropdown-item divided @click="handleLogout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>

    <!-- Banner区域 -->
    <div class="hero-banner">
      <div class="banner-content">
        <h1 class="banner-title">加入杭科院，共创美好未来</h1>
        <p class="banner-subtitle">我们正在寻找优秀的你，一起打造一流的教育事业</p>
        <div class="search-box">
          <el-input 
            v-model="searchKeyword" 
            placeholder="搜索职位名称、关键词"
            size="large"
            @keyup.enter="handleSearch"
          >
            <template #append>
              <el-button type="primary" @click="handleSearch">
                <el-icon><search /></el-icon>
                搜索职位
              </el-button>
            </template>
          </el-input>
        </div>
        <div class="hot-tags">
          <span class="tag-label">热门搜索：</span>
          <el-tag 
            v-for="tag in hotTags" 
            :key="tag"
            @click="quickSearch(tag)"
            style="margin-right: 10px; cursor: pointer;"
          >
            {{ tag }}
          </el-tag>
        </div>
      </div>
    </div>

    <!-- 职位分类 -->
    <div class="job-categories">
      <div class="container">
        <h2 class="section-title">职位分类</h2>
        <div class="category-grid">
          <div 
            v-for="category in jobCategories" 
            :key="category.name"
            class="category-card"
            @click="filterByCategory(category.name)"
          >
            <el-icon :size="40" :color="category.color">
              <component :is="category.icon" />
            </el-icon>
            <h3>{{ category.name }}</h3>
            <p>{{ category.count }}个职位</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 最新职位 -->
    <div class="latest-jobs">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">最新职位</h2>
          <el-button type="text" @click="viewAllJobs">
            查看全部职位
            <el-icon><arrow-right /></el-icon>
          </el-button>
        </div>
        
        <div v-loading="loading" class="job-list">
          <div 
            v-for="job in latestJobs" 
            :key="job.id"
            class="job-card"
            @click="viewJobDetail(job)"
          >
            <div class="job-header">
              <h3 class="job-title">
                {{ job.title }}
                <el-tag v-if="job.isUrgent" type="danger" size="small" style="margin-left: 10px">
                  急聘
                </el-tag>
                <el-tag v-if="job.isHot" type="warning" size="small" style="margin-left: 5px">
                  热门
                </el-tag>
              </h3>
              <div class="job-salary">{{ job.salaryMin }}-{{ job.salaryMax }}{{ job.salaryUnit }}</div>
            </div>
            <div class="job-info">
              <span class="info-item">
                <el-icon><location /></el-icon>
                {{ job.workLocation }}
              </span>
              <span class="info-item">
                <el-icon><briefcase /></el-icon>
                {{ job.experience }}
              </span>
              <span class="info-item">
                <el-icon><school /></el-icon>
                {{ job.education }}
              </span>
            </div>
            <div class="job-desc">{{ truncateText(job.jobDescription, 100) }}</div>
            <div class="job-footer">
              <span class="department">{{ job.departmentName }}</span>
              <span class="publish-time">{{ formatTime(job.publishTime) }}</span>
            </div>
          </div>
        </div>

        <div v-if="!loading && latestJobs.length === 0" class="empty-state">
          <el-empty description="暂无职位信息"  />
        </div>
      </div>
    </div>

    <!-- 申请流程 -->
    <div class="application-process">
      <div class="container">
        <h2 class="section-title">申请流程</h2>
        <div class="process-steps">
          <div class="step-item">
            <div class="step-number">1</div>
            <h4>注册登录</h4>
            <p>创建账号并完善个人信息</p>
          </div>
          <div class="step-arrow">→</div>
          <div class="step-item">
            <div class="step-number">2</div>
            <h4>浏览职位</h4>
            <p>查找适合您的职位</p>
          </div>
          <div class="step-arrow">→</div>
          <div class="step-item">
            <div class="step-number">3</div>
            <h4>投递简历</h4>
            <p>在线提交申请材料</p>
          </div>
          <div class="step-arrow">→</div>
          <div class="step-item">
            <div class="step-number">4</div>
            <h4>面试通知</h4>
            <p>通过初筛后安排面试</p>
          </div>
          <div class="step-arrow">→</div>
          <div class="step-item">
            <div class="step-number">5</div>
            <h4>录用入职</h4>
            <p>通过面试后办理入职</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部信息 -->
    <div class="portal-footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h4>关于我们</h4>
            <p>杭州科技职业技术学院是一所由杭州市人民政府主办的公办高等职业院校。</p>
          </div>
          <div class="footer-section">
            <h4>联系方式</h4>
            <p>地址：浙江省杭州市西湖区留和路318号</p>
            <p>电话：0571-88888888</p>
            <p>邮箱：<EMAIL></p>
          </div>
          <div class="footer-section">
            <h4>快速链接</h4>
            <ul>
              <li><a href="#">学院官网</a></li>
              <li><a href="#">人事处</a></li>
              <li><a href="#">招聘公告</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>关注我们</h4>
            <div class="qrcode">
              <img src="/qrcode.png" alt="微信公众号">
              <p>微信公众号</p>
            </div>
          </div>
        </div>
        <div class="copyright">
          © 2025 杭州科技职业技术学院 版权所有 | 浙ICP备12345678号
        </div>
      </div>
    </div>

    <!-- 职位详情对话框 -->
    <el-dialog 
      v-model="jobDetailVisible" 
      :title="currentJob?.title" 
      width="800px"
      top="5vh"
    >
      <div v-if="currentJob" class="job-detail-content">
        <el-descriptions :column="2" border style="margin-bottom: 20px">
          <el-descriptions-item label="部门">{{ currentJob.departmentName }}</el-descriptions-item>
          <el-descriptions-item label="工作地点">{{ currentJob.workLocation }}</el-descriptions-item>
          <el-descriptions-item label="薪资范围">
            {{ currentJob.salaryMin }}-{{ currentJob.salaryMax }}{{ currentJob.salaryUnit }}
          </el-descriptions-item>
          <el-descriptions-item label="招聘人数">{{ currentJob.recruitCount }}人</el-descriptions-item>
          <el-descriptions-item label="学历要求">{{ currentJob.education }}</el-descriptions-item>
          <el-descriptions-item label="经验要求">{{ currentJob.experience }}</el-descriptions-item>
          <el-descriptions-item label="工作性质">{{ currentJob.jobType }}</el-descriptions-item>
          <el-descriptions-item label="发布时间">{{ formatTime(currentJob.publishTime) }}</el-descriptions-item>
        </el-descriptions>

        <div class="detail-section">
          <h4>职位描述</h4>
          <div class="content">{{ currentJob.jobDescription }}</div>
        </div>

        <div class="detail-section">
          <h4>任职要求</h4>
          <div class="content">{{ currentJob.requirements }}</div>
        </div>

        <div v-if="currentJob.benefits" class="detail-section">
          <h4>福利待遇</h4>
          <div class="content">{{ currentJob.benefits }}</div>
        </div>
      </div>

      <template #footer>
        <el-button @click="jobDetailVisible = false">关闭</el-button>
        <el-button 
          type="primary" 
          @click="applyJob"
          :disabled="!isLoggedIn || hasApplied"
        >
          {{ hasApplied ? '已申请' : '立即申请' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  Search, 
  Location, 
  Briefcase, 
  School, 
  ArrowRight,
  ArrowDown,
  Monitor,
  Reading,
  Management,
  DataAnalysis,
  Promotion
} from '@element-plus/icons-vue'
import { jobPostApi } from '@/api/recruitment'
import { candidateApi } from '@/api/recruitment/candidate'
import type { JobPost } from '@/types/recruitment'

const router = useRouter()

// 搜索关键词
const searchKeyword = ref('')

// 热门标签
const hotTags = ref(['教师', '行政管理', '实验员', '辅导员', '工程师'])

// 职位分类
const jobCategories = ref([
  { name: 'HrHr教学科研', icon: 'Reading', color: '#409EFF', count: 15 },
  { name: '行政管理', icon: 'Management', color: '#67C23A', count: 8 },
  { name: '技术支持', icon: 'Monitor', color: '#E6A23C', count: 5 },
  { name: '学生工作', icon: 'Promotion', color: '#F56C6C', count: 3 },
  { name: '后勤保障', icon: 'DataAnalysis', color: '#909399', count: 2 }
])

// 最新职位
const loading = ref(false)
const latestJobs = ref<JobPost[]>([])

// 用户状态
const isLoggedIn = ref(false)
const currentUser = ref({ name: '张三' })

// 职位详情
const jobDetailVisible = ref(false)
const currentJob = ref<JobPost>()
const hasApplied = ref(false)

// 格式化时间
const formatTime = (time: string) => {
  if (!time) return ''
  const date = new Date(time)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) return '今天'
  if (days === 1) return '昨天'
  if (days < 7) return `${days}天前`
  if (days < 30) return `${Math.floor(days / 7)}周前`
  return `${Math.floor(days / 30)}个月前`
}

// 截断文本
const truncateText = (text: string, length: number) => {
  if (!text || text.length <= length) return text
  return text.substring(0, length) + '...'
}

// 获取最新职位
const fetchLatestJobs = async () => {
  loading.value = true
  try {
    const {data: _data} =  await jobPostApi.getList({
      status: '已发布',
      page: 1,
      size: 6,
      sortBy: 'publishTime',
      sortDir: 'desc'
    })
    latestJobs.value 
  background-color: #f5f7fa;
  
  // 头部导航
  .portal-header {
    background: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
    
    .header-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .logo {
        display: flex;
        align-items: center;
        font-size: 18px;
        font-weight: 500;
        
        img {
          height: 40px;
          margin-right: 10px;
        }
      }
      
      .nav-menu {
        display: flex;
        gap: 30px;
        
        .nav-item {
          color: #606266;
          text-decoration: none;
          transition: color 0.3s;
          
          &:hover,
          &.active {
            color: #409eff;
          }
        }
      }
      
      .user-actions {
        display: flex;
        gap: 10px;
      }
    }
  }
  
  // Banner区域
  .hero-banner {
    background: linear-gradient(135deg, #409eff 0%, #53a8ff 100%);
    padding: 80px 20px;
    color: white;
    
    .banner-content {
      max-width: 800px;
      margin: 0 auto;
      text-align: center;
      
      .banner-title {
        font-size: 48px;
        margin-bottom: 20px;
        font-weight: 600;
      }
      
      .banner-subtitle {
        font-size: 20px;
        margin-bottom: 40px;
        opacity: 0.9;
      }
      
      .search-box {
        margin-bottom: 20px;
        
        :deep(.el-input-group__append) {
          background-color: #409eff;
          border-color: #409eff;
          
          .el-button {
            color: white;
          }
        }
      }
      
      .hot-tags {
        .tag-label {
          margin-right: 10px;
          opacity: 0.9;
        }
        
        .el-tag {
          background-color: rgba(255, 255, 255, 0.2);
          border-color: rgba(255, 255, 255, 0.3);
          color: white;
          
          &:hover {
            background-color: rgba(255, 255, 255, 0.3);
          }
        }
      }
    }
  }
  
  // 通用容器
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
  
  // 区块标题
  .section-title {
    font-size: 32px;
    text-align: center;
    margin-bottom: 40px;
    color: #303133;
  }
  
  // 职位分类
  .job-categories {
    padding: 80px 0;
    background: white;
    
    .category-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      
      .category-card {
        padding: 30px;
        text-align: center;
        border: 1px solid #ebeef5;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s;
        
        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
          border-color: #409eff;
        }
        
        h3 {
          margin: 15px 0 10px;
          color: #303133;
        }
        
        p {
          color: #909399;
          margin: 0;
        }
      }
    }
  }
  
  // 最新职位
  .latest-jobs {
    padding: 80px 0;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 40px;
      
      .section-title {
        margin-bottom: 0;
      }
    }
    
    .job-list {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
      gap: 20px;
      
      .job-card {
        background: white;
        padding: 25px;
        border-radius: 8px;
        border: 1px solid #ebeef5;
        cursor: pointer;
        transition: all 0.3s;
        
        &:hover {
          transform: translateY(-3px);
          box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
          border-color: #409eff;
        }
        
        .job-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 15px;
          
          .job-title {
            font-size: 18px;
            color: #303133;
            margin: 0;
            flex: 1;
          }
          
          .job-salary {
            color: #f56c6c;
            font-size: 16px;
            font-weight: 500;
            white-space: nowrap;
          }
        }
        
        .job-info {
          display: flex;
          gap: 20px;
          margin-bottom: 15px;
          
          .info-item {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #909399;
            font-size: 14px;
            
            .el-icon {
              font-size: 16px;
            }
          }
        }
        
        .job-desc {
          color: #606266;
          line-height: 1.6;
          margin-bottom: 15px;
          height: 48px;
          overflow: hidden;
        }
        
        .job-footer {
          display: flex;
          justify-content: space-between;
          color: #909399;
          font-size: 14px;
        }
      }
    }
    
    .empty-state {
      padding: 60px 0;
      background: white;
      border-radius: 8px;
    }
  }
  
  // 申请流程
  .application-process {
    padding: 80px 0;
    background: white;
    
    .process-steps {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 20px;
      
      .step-item {
        text-align: center;
        
        .step-number {
          width: 60px;
          height: 60px;
          background: #409eff;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;
          font-weight: 500;
          margin: 0 auto 15px;
        }
        
        h4 {
          margin: 0 0 10px;
          color: #303133;
        }
        
        p {
          margin: 0;
          color: #909399;
          font-size: 14px;
        }
      }
      
      .step-arrow {
        font-size: 24px;
        color: #dcdfe6;
      }
    }
  }
  
  // 底部
  .portal-footer {
    background: #2c3e50;
    color: white;
    padding: 40px 0 20px;
    
    .footer-content {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 40px;
      margin-bottom: 30px;
      
      .footer-section {
        h4 {
          margin: 0 0 15px;
          font-size: 18px;
        }
        
        p {
          margin: 5px 0;
          opacity: 0.8;
          line-height: 1.6;
        }
        
        ul {
          list-style: none;
          padding: 0;
          margin: 0;
          
          li {
            margin: 5px 0;
            
            a {
              color: white;
              opacity: 0.8;
              text-decoration: none;
              
              &:hover {
                opacity: 1;
              }
            }
          }
        }
        
        .qrcode {
          text-align: center;
          
          img {
            width: 120px;
            height: 120px;
            background: white;
            padding: 10px;
            border-radius: 8px;
          }
          
          p {
            margin-top: 10px;
            font-size: 14px;
          }
        }
      }
    }
    
    .copyright {
      text-align: center;
      padding-top: 20px;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
      opacity: 0.6;
      font-size: 14px;
    }
  }
}

// 职位详情
.job-detail-content {
  .detail-section {
    margin-bottom: 20px;
    
    h4 {
      margin: 0 0 10px;
      color: #303133;
      font-size: 16px;
    }
    
    .content {
      color: #606266;
      line-height: 1.8;
      white-space: pre-wrap;
    }
  }
}

// 响应式
@media (max-width: 768px) {
  .job-portal {
    .portal-header {
      .nav-menu {
        display: none;
      }
    }
    
    .hero-banner {
      padding: 40px 20px;
      
      .banner-content {
        .banner-title {
          font-size: 32px;
        }
        
        .banner-subtitle {
          font-size: 16px;
        }
      }
    }
    
    .application-process {
      .process-steps {
        flex-direction: column;
        
        .step-arrow {
          transform: rotate(90deg);
        }
      }
    }
  }
}
</style>
</script>
