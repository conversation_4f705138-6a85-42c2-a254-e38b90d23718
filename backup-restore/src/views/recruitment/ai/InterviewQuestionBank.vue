<template>
  <div class="interview-question-bank">
    <el-row :gutter="20">
      <!-- 左侧：题库管理 -->
      <el-col :span="8">
        <el-card class="question-bank-card">
          <template #header>
            <div class="card-header">
              <span>题库管理</span>
              <el-button type="primary" size="small" @click="showAddQuestion = true">
                新增题目
              </el-button>
            </div>
          </template>

          <!-- 搜索和筛选 -->
          <div class="search-filter">
            <el-input 
              v-model="searchKeyword" 
              placeholder="搜索题目" 
              :prefix-icon="Search"
              clearable
              @clear="handleSearch"
              @keyup.enter="handleSearch"
              />
            
            <el-select 
              v-model="filterCategory" 
              placeholder="题目分类" 
              clearable
              @change="handleFilter"
              style="margin-top: 10px"
            >
              <el-option label="技术基础" value="technical"  />
              <el-option label="项目经验" value="project"  />
              <el-option label="算法题" value="algorithm"  />
              <el-option label="系统设计" value="design"  />
              <el-option label="行为面试" value="behavioral"  />
              <el-option label="情景题" value="situational"  />
            </el-select>
          </div>

          <!-- 题目列表 -->
          <el-scrollbar height="500px">
            <div class="question-list">
              <div 
                v-for="question in filteredQuestions" 
                :key="question.id"
                class="question-item"
                :class="{ active: selectedQuestions.includes(question.id) }"
                @click="toggleQuestion(question.id)"
              >
                <el-checkbox 
                  :model-value="selectedQuestions.includes(question.id)"
                  @click.stop
                  @change="toggleQuestion(question.id)"
                 />
                <div class="question-content">
                  <div class="question-title">{{ question.title }}</div>
                  <div class="question-meta">
                    <el-tag size="small" :type="getCategoryType(question.category)">
                      {{ getCategoryLabel(question.category) }}
                    </el-tag>
                    <el-tag size="small" type="info">{{ question.difficulty }}</el-tag>
                    <span class="usage-count">使用{{ question.usageCount }}次</span>
                  </div>
                </div>
              </div>
            </div>
          </el-scrollbar>

          <!-- 题库统计 -->
          <div class="bank-stats">
            <el-row :gutter="10">
              <el-col :span="8">
                <div class="stat-item">
                  <div class="value">{{ questionStats.total }}</div>
                  <div class="label">题目总数</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="stat-item">
                  <div class="value">{{ questionStats.categories }}</div>
                  <div class="label">分类数</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="stat-item">
                  <div class="value">{{ selectedQuestions.length }}</div>
                  <div class="label">已选题目</div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>

      <!-- 中间：AI推荐 -->
      <el-col :span="8">
        <el-card class="ai-recommend-card">
          <template #header>
            <span>AI智能推荐</span>
          </template>

          <!-- 推荐设置 -->
          <el-form label-position="top">
            <el-form-item label="选择职位">
              <el-select v-model="recommendForm.position" placeholder="请选择" style="width: 100%">
                <el-option label="前端工程师" value="frontend"  />
                <el-option label="后端工程师" value="backend"  />
                <el-option label="全栈工程师" value="fullstack"  />
                <el-option label="算法工程师" value="algorithm"  />
                <el-option label="测试工程师" value="test"  />
                <el-option label="产品经理" value="product"  />
              </el-select>
            </el-form-item>

            <el-form-item label="面试轮次">
              <el-radio-group v-model="recommendForm.round">
                <el-radio label="first">初试</el-radio>
                <el-radio label="second">复试</el-radio>
                <el-radio label="final">终试</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="候选人级别">
              <el-select v-model="recommendForm.level" placeholder="请选择" style="width: 100%">
                <el-option label="初级 (1-3年)" value="junior"  />
                <el-option label="中级 (3-5年)" value="middle"  />
                <el-option label="高级 (5-8年)" value="senior"  />
                <el-option label="专家 (8年+)" value="expert"  />
              </el-select>
            </el-form-item>

            <el-form-item label="题目数量">
              <el-slider 
                v-model="recommendForm.count" 
                :min="5" 
                :max="20"
                :marks="{ 5: '5题', 10: '10题', 15: '15题', 20: '20题' }"
               />
            </el-form-item>

            <el-form-item label="难度分布">
              <div class="difficulty-config">
                <div class="diff-item">
                  <span>简单</span>
                  <el-input-number 
                    v-model="recommendForm.difficulty.easy" 
                    :min="0" 
                    :max="100"
                    size="small"
                    controls-position="right"
                    />
                  <span>%</span>
                </div>
                <div class="diff-item">
                  <span>中等</span>
                  <el-input-number 
                    v-model="recommendForm.difficulty.medium" 
                    :min="0" 
                    :max="100"
                    size="small"
                    controls-position="right"
                    />
                  <span>%</span>
                </div>
                <div class="diff-item">
                  <span>困难</span>
                  <el-input-number 
                    v-model="recommendForm.difficulty.hard" 
                    :min="0" 
                    :max="100"
                    size="small"
                    controls-position="right"
                    />
                  <span>%</span>
                </div>
              </div>
            </el-form-item>

            <el-button 
              type="primary" 
              :icon="MagicStick" 
              @click="getAIRecommendation"
              :loading="recommending"
              style="width: 100%"
            >
              获取AI推荐
            </el-button>
          </el-form>

          <!-- 推荐结果 -->
          <div v-if="recommendedQuestions.length > 0" class="recommend-result">
            <el-divider>推荐题目</el-divider>
            <div class="recommended-list">
              <div 
                v-for="(item, index) in recommendedQuestions" 
                :key="item.id"
                class="recommended-item"
              >
                <div class="item-header">
                  <span class="index">{{ index + 1 }}</span>
                  <el-tag size="small" :type="getCategoryType(item.category)">
                    {{ getCategoryLabel(item.category) }}
                  </el-tag>
                  <el-tag size="small" :type="getDifficultyType(item.difficulty)">
                    {{ item.difficulty }}
                  </el-tag>
                </div>
                <div class="item-content">
                  <p class="question">{{ item.title }}</p>
                  <p class="reason">推荐理由：{{ item.recommendReason }}</p>
                </div>
              </div>
            </div>
            
            <div class="recommend-actions">
              <el-button @click="addAllRecommended">全部添加</el-button>
              <el-button type="primary" @click="replaceWithRecommended">
                替换当前选择
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：面试题单 -->
      <el-col :span="8">
        <el-card class="interview-sheet-card">
          <template #header>
            <div class="card-header">
              <span>面试题单</span>
              <div class="actions">
                <el-button link type="primary" @click="previewSheet">预览</el-button>
                <el-button link type="primary" @click="exportSheet">导出</el-button>
              </div>
            </div>
          </template>

          <!-- 题单信息 -->
          <el-form label-width="80px">
            <el-form-item label="面试官">
              <el-input v-model="sheetInfo.interviewer" placeholder="请输入面试官姓名"   />
            </el-form-item>
            <el-form-item label="候选人">
              <el-input v-model="sheetInfo.candidate" placeholder="请输入候选人姓名"   />
            </el-form-item>
            <el-form-item label="职位">
              <el-input v-model="sheetInfo.position" placeholder="请输入面试职位"   />
            </el-form-item>
            <el-form-item label="时长">
              <el-select v-model="sheetInfo.duration" style="width: 100%">
                <el-option label="30分钟" value="30"  />
                <el-option label="45分钟" value="45"  />
                <el-option label="60分钟" value="60"  />
                <el-option label="90分钟" value="90"  />
              </el-select>
            </el-form-item>
          </el-form>

          <el-divider>已选题目 ({{ selectedQuestions.length }})</el-divider>

          <!-- 已选题目列表 -->
          <el-scrollbar height="350px">
            <draggable 
              v-model="selectedQuestionList" 
              item-key="id"
              handle=".drag-handle"
              animation="200"
            >
              <template #item="{ element, index }">
                <div class="selected-question">
                  <el-icon class="drag-handle"><rank /></el-icon>
                  <span class="number">{{ index + 1 }}</span>
                  <div class="content">
                    <div class="title">{{ element.title }}</div>
                    <div class="info">
                      <el-tag size="small">{{ getCategoryLabel(element.category) }}</el-tag>
                      <el-tag size="small" type="info">{{ element.difficulty }}</el-tag>
                      <span class="time">预计{{ element.estimatedTime }}分钟</span>
                    </div>
                  </div>
                  <el-button 
                    link 
                    type="danger" 
                    :icon="Delete" 
                    @click="removeQuestion(element.id)"
                    />
                </div>
              </template>
            </draggable>
          </el-scrollbar>

          <!-- 时间分配 -->
          <div class="time-allocation">
            <el-progress 
              :percentage="timePercentage" 
              :color="timeProgressColor"
            >
              <span>{{ totalTime }}/{{ sheetInfo.duration }}分钟</span>
            </el-progress>
          </div>

          <!-- 操作按钮 -->
          <div class="sheet-actions">
            <el-button @click="saveAsTemplate">保存为模板</el-button>
            <el-button type="primary" @click="generateSheet">生成题单</el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 新增题目对话框 -->
    <el-dialog 
      v-model="showAddQuestion" 
      title="新增题目" 
      width="600px"
    >
      <el-form :model="questionForm" label-width="100px">
        <el-form-item label="题目标题" required>
          <el-input 
            v-model="questionForm.title" 
            type="textarea" 
            :rows="3"
            placeholder="请输入题目内容"
            />
        </el-form-item>
        <el-form-item label="题目分类" required>
          <el-select v-model="questionForm.category" placeholder="请选择">
            <el-option label="技术基础" value="technical"  />
            <el-option label="项目经验" value="project"  />
            <el-option label="算法题" value="algorithm"  />
            <el-option label="系统设计" value="design"  />
            <el-option label="行为面试" value="behavioral"  />
            <el-option label="情景题" value="situational"  />
          </el-select>
        </el-form-item>
        <el-form-item label="难度等级" required>
          <el-radio-group v-model="questionForm.difficulty">
            <el-radio label="简单">简单</el-radio>
            <el-radio label="中等">中等</el-radio>
            <el-radio label="困难">困难</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="预计时长">
          <el-input-number 
            v-model="questionForm.estimatedTime" 
            :min="1" 
            :max="30"
            />
          <span style="margin-left: 10px">分钟</span>
        </el-form-item>
        <el-form-item label="参考答案">
          <el-input 
            v-model="questionForm.answer" 
            type="textarea" 
            :rows="4"
            placeholder="请输入参考答案或答题要点"
            />
        </el-form-item>
        <el-form-item label="适用标签">
          <el-select 
            v-model="questionForm.tags" 
            multiple 
            placeholder="请选择或输入标签"
            filterable
            allow-create
          >
            <el-option label="Vue.js" value="Vue.js"  />
            <el-option label="React" value="React"  />
            <el-option label="JavaScript" value="JavaScript"  />
            <el-option label="算法" value="算法"  />
            <el-option label="数据结构" value="数据结构"  />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showAddQuestion = false">取消</el-button>
        <el-button type="primary" @click="saveQuestion">保存</el-button>
      </template>
    </el-dialog>

    <!-- 题单预览对话框 -->
    <el-dialog 
      v-model="showPreview" 
      title="题单预览" 
      width="800px"
    >
      <div class="sheet-preview">
        <div class="preview-header">
          <h2>面试题单</h2>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="面试官">{{ sheetInfo.interviewer }}</el-descriptions-item>
            <el-descriptions-item label="候选人">{{ sheetInfo.candidate }}</el-descriptions-item>
            <el-descriptions-item label="职位">{{ sheetInfo.position }}</el-descriptions-item>
            <el-descriptions-item label="预计时长">{{ sheetInfo.duration }}分钟</el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="preview-content">
          <div 
            v-for="(question, index) in selectedQuestionList" 
            :key="question.id"
            class="preview-question"
          >
            <h4>{{ index + 1 }}. {{ question.title }}</h4>
            <div class="question-meta">
              <span>分类：{{ getCategoryLabel(question.category) }}</span>
              <span>难度：{{ question.difficulty }}</span>
              <span>预计时长：{{ question.estimatedTime }}分钟</span>
            </div>
            <div v-if="showAnswers" class="question-answer">
              <strong>参考答案：</strong>
              <p>{{ question.answer || '暂无参考答案' }}</p>
            </div>
          </div>
        </div>

        <div class="preview-footer">
          <el-checkbox v-model="showAnswers">显示参考答案</el-checkbox>
        </div>
      </div>

      <template #footer>
        <el-button @click="showPreview = false">关闭</el-button>
        <el-button type="primary" @click="printSheet">打印</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'HrInterviewQuestionBank'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, MagicStick, Delete, Rank } from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import { interviewApi, interviewEvaluationApi } from '@/api/recruitment'

// 数据状态
const searchKeyword = ref('')
const filterCategory = ref('')
const selectedQuestions = ref<string[]>([])
const showAddQuestion = ref(false)
const showPreview = ref(false)
const showAnswers = ref(false)
const recommending = ref(false)
const recommendedQuestions = ref<any[]>([])

// 表单数据
const questionForm = reactive({
  title: '',
  category: '',
  difficulty: '中等',
  estimatedTime: 5,
  answer: '',
  tags: []
})

const recommendForm = reactive({
  position: '',
  round: 'first',
  level: 'middle',
  count: 10,
  difficulty: {
    easy: 30,
    medium: 50,
    hard: 20
  }
})

const sheetInfo = reactive({
  interviewer: '',
  candidate: '',
  position: '',
  duration: '60'
})

// 题库统计
const questionStats = reactive({
  total: 156,
  categories: 6
})

// 模拟题目数据
const questions = ref([
  {
    id: '1',
    title: '请解释JavaScript中的闭包是什么，并举例说明其应用场景',
    category: 'technical',
    difficulty: '中等',
    estimatedTime: 5,
    usageCount: 45,
    answer: '闭包是指有权访问另一个函数作用域中变量的函数...',
    tags: ['JavaScript', '基础']
  },
  {
    id: '2',
    title: '如何实现一个简单的Promise',
    category: 'algorithm',
    difficulty: '困难',
    estimatedTime: 10,
    usageCount: 32,
    answer: 'Promise的基本实现需要考虑状态管理、then链式调用...',
    tags: ['JavaScript', 'Promise']
  },
  {
    id: '3',
    title: '介绍一下你最有成就感的项目，遇到了什么挑战，如何解决的',
    category: 'project',
    difficulty: '简单',
    estimatedTime: 8,
    usageCount: 68,
    answer: '考察项目经验、问题解决能力、技术深度...',
    tags: ['项目经验']
  },
  {
    id: '4',
    title: '设计一个秒杀系统，需要考虑哪些问题',
    category: 'design',
    difficulty: '困难',
    estimatedTime: 15,
    usageCount: 25,
    answer: '需要考虑高并发、库存扣减、防止超卖、限流...',
    tags: ['系统设计', '高并发']
  },
  {
    id: '5',
    title: '如何处理与同事意见不合的情况',
    category: 'behavioral',
    difficulty: '简单',
    estimatedTime: 5,
    usageCount: 52,
    answer: '考察沟通能力、团队协作、冲突处理...',
    tags: ['软技能']
  }
])

// 过滤后的题目
const filteredQuestions = computed(() => {
  let result = questions.value
  
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(q => 
      q.title.toLowerCase().includes(keyword) ||
      q.tags.some(tag => tag.toLowerCase().includes(keyword))
    )
  }
  
  if (filterCategory.value) {
    result = result.filter(q => q.category === filterCategory.value)
  }
  
  return result
})

// 已选题目列表
const selectedQuestionList = computed({
  get: () => {
    return selectedQuestions.value.map(id => 
      questions.value.find(q => q.id === id)
    ).filter(Boolean)
  },
  set: (newList) => {
    selectedQuestions.value = newList.map(q => q.id)
  }
})

// 总时长
const totalTime = computed(() => {
  return selectedQuestionList.value.reduce((sum, q) => sum + q.estimatedTime, 0)
})

// 时间百分比
const timePercentage = computed(() => {
  const duration = parseInt(sheetInfo.duration)
  return Math.round((totalTime.value / duration) * 100)
})

// 时间进度条颜色
const timeProgressColor = computed(() => {
  if (timePercentage.value <= 80) return '#67c23a'
  if (timePercentage.value <= 100) return '#e6a23c'
  return '#f56c6c'
})

// 获取分类类型
const getCategoryType = (category: string) => {
  const map: Record<string, string> = {
    technical: '',
    project: 'success',
    algorithm: 'warning',
    design: 'danger',
    behavioral: 'info',
    situational: 'info'
  }
  return map[category] || ''
}

// 获取分类标签
const getCategoryLabel = (category: string) => {
  const map: Record<string, string> = {
    technical: '技术基础',
    project: '项目经验',
    algorithm: '算法题',
    design: '系统设计',
    behavioral: '行为面试',
    situational: '情景题'
  }
  return map[category] || category
}

// 获取难度类型
const getDifficultyType = (difficulty: string) => {
  const map: Record<string, string> = {
    '简单': 'success',
    '中等': 'warning',
    '困难': 'danger'
  }
  return map[difficulty] || ''
}

// 搜索处理
const handleSearch = () => {
  // 搜索逻辑已通过computed实现
}

// 筛选处理
const handleFilter = () => {
  // 筛选逻辑已通过computed实现
}

// 切换题目选择
const toggleQuestion = (id: string) => {
  const index = selectedQuestions.value.indexOf(id)
  if (index > -1) {
    selectedQuestions.value.splice(index, 1)
  } else {
    selectedQuestions.value.push(id)
  }
}

// 移除题目
const removeQuestion = (id: string) => {
  const index = selectedQuestions.value.indexOf(id)
  if (index > -1) {
    selectedQuestions.value.splice(index, 1)
  }
}

// 获取AI推荐
const getAIRecommendation = async () => {
  if (!recommendForm.position) {
    ElMessage.warning('请选择职位')
    return
  }
  
  recommending.value = true
  
  // 模拟AI推荐过程
  setTimeout(() => {
    recommendedQuestions.value = [
      {
        id: 'r1',
        title: 'Vue 3和Vue 2的主要区别是什么',
        category: 'technical',
        difficulty: '中等',
        estimatedTime: 5,
        recommendReason: '考察框架理解深度，适合前端岗位'
      },
      {
        id: 'r2',
        title: '如何优化前端性能',
        category: 'technical',
        difficulty: '中等',
        estimatedTime: 8,
        recommendReason: '考察性能优化经验，重要技能点'
      },
      {
        id: 'r3',
        title: '实现一个防抖函数',
        category: 'algorithm',
        difficulty: '简单',
        estimatedTime: 5,
        recommendReason: '基础编程能力考察'
      },
      {
        id: 'r4',
        title: '介绍一个你解决的技术难题',
        category: 'project',
        difficulty: '中等',
        estimatedTime: 10,
        recommendReason: '了解问题解决能力和技术深度'
      },
      {
        id: 'r5',
        title: '如何设计一个组件库',
        category: 'design',
        difficulty: '困难',
        estimatedTime: 12,
        recommendReason: '考察架构设计能力，适合高级岗位'
      }
    ]
    
    recommending.value = false
    ElMessage.success('AI推荐完成')
  }, 2000)
}

// 添加所有推荐题目
const addAllRecommended = async () => {
  // 将推荐题目添加到题库并选中
  try {
    for (const question of recommendedQuestions.value) {
      // 检查是否已存在
      const exists = questions.value.some(q => q.title === question.title)
      if (!exists) {
        const newQuestion = {
          id: Date.now().toString() + Math.random(),
          title: question.title,
          category: question.category,
          difficulty: question.difficulty,
          estimatedTime: question.estimatedTime,
          answer: question.answer || '',
          tags: [],
          usageCount: 0
        }
        questions.value.unshift(newQuestion)
        selectedQuestions.value.push(newQuestion.id)
        questionStats.total++
      }
    }
    ElMessage.success('已添加所有推荐题目')
  } catch (__error) {
    console.error('添加推荐题目失败:', error)
    ElMessage.error('添加失败')
  }
}

// 替换为推荐题目
const replaceWithRecommended = () => {
  // 清空当前选择，替换为推荐题目
  selectedQuestions.value = []
  
  // 添加推荐题目到题库并选中
  recommendedQuestions.value.forEach(recQuestion => {
    const exists = questions.value.find(q => q.title === recQuestion.title)
    if (exists) {
      selectedQuestions.value.push(exists.id)
    } else {
      const newQuestion = {
        id: Date.now().toString() + Math.random(),
        title: recQuestion.title,
        category: recQuestion.category,
        difficulty: recQuestion.difficulty,
        estimatedTime: recQuestion.estimatedTime,
        answer: recQuestion.answer || '',
        tags: [],
        usageCount: 0
      }
      questions.value.unshift(newQuestion)
      selectedQuestions.value.push(newQuestion.id)
      questionStats.total++
    }
  })
  
  ElMessage.success('已替换为推荐题目')
}

// 保存题目
const saveQuestion = async () => {
  if (!questionForm.title || !questionForm.category) {
    ElMessage.warning('请填写必填项')
    return
  }
  
  try {
    // 调用API保存题目
    const questionData = {
      questionTitle: questionForm.title,
      questionContent: questionForm.title,
      questionType: 'ESSAY' as const,
      category: questionForm.category,
      difficulty: questionForm.difficulty === '简单' ? 'EASY' : questionForm.difficulty === '中等' ? 'MEDIUM' : 'HARD' as const,
      skillTags: questionForm.tags,
      positionTags: [],
      departmentTags: [],
      estimatedTime: questionForm.estimatedTime,
      standardAnswer: questionForm.answer
    }
    
    try {

    
      try {


    
        const response = await interviewApi.createQuestion(questionData)


    
        // TODO: 处理获取到的数据


    
      } catch (error) {


    
        console.error('获取数据失败:', error)


    
      }
      // TODO: 处理获取到的数据

    
    } catch (error) {

    
      console.error('获取数据失败:', error)

    
    }
    const newQuestion = {
      id: response.data.id.toString(),
      title: questionForm.title,
      category: questionForm.category,
      difficulty: questionForm.difficulty,
      estimatedTime: questionForm.estimatedTime,
      answer: questionForm.answer,
      tags: questionForm.tags,
      usageCount: 0
    }
    
    questions.value.unshift(newQuestion)
    questionStats.total++
    
    ElMessage.success('题目保存成功')
    showAddQuestion.value = false
    
    // 重置表单
    Object.assign(questionForm, {
      title: '',
      category: '',
      difficulty: '中等',
      estimatedTime: 5,
      answer: '',
      tags: []
    })
  } catch (__error) {
    console.error('保存题目失败:', error)
    ElMessage.error('保存失败')
  }
}

// 预览题单
const previewSheet = () => {
  if (selectedQuestions.value.length === 0) {
    ElMessage.warning('请先选择题目')
    return
  }
  showPreview.value = true
}

// 导出题单
const exportSheet = async () => {
  if (selectedQuestions.value.length === 0) {
    ElMessage.warning('请先选择题目')
    return
  }
  
  try {
    // 导出为PDF或Word
    const exportParams = {
      questionIds: selectedQuestions.value.map(id => Number(id)),
      exportFormat: 'PDF' as const,
      exportType: 'WITH_ANSWERS' as const,
      includeMetadata: true,
      includeUsageStats: false,
      templateFormat: true
    }
    
    try {

    
      try {


    
        const response = await interviewApi.exportQuestions(exportParams)


    
        // TODO: 处理获取到的数据


    
      } catch (error) {


    
        console.error('获取数据失败:', error)


    
      }
      // TODO: 处理获取到的数据

    
    } catch (error) {

    
      console.error('获取数据失败:', error)

    
    }
    if (response.data.exportStatus === 'COMPLETED') {
      // 下载文件
      const link = document.createElement('a')
      link.href = response.data.downloadUrl
      link.download = response.data.fileName
      link.click()
      ElMessage.success('题单导出成功')
    } else {
      ElMessage.info('正在处理导出请求，请稍后...')
    }
  } catch (__error) {
    console.error('导出题单失败:', error)
    ElMessage.error('导出失败')
  }
}

// 保存为模板
const saveAsTemplate = async () => {
  if (selectedQuestions.value.length === 0) {
    ElMessage.warning('请先选择题目')
    return
  }
  
  try {
    const {value: templateName} =  await ElMessageBox.prompt(
      '请输入模板名称',
      '保存模板',
      {
        confirmButtonText: '保存',
        cancelButtonText: '取消',
        inputPattern: /\S+/,
        inputErrorMessage: '模板名称不能为空'
      }
    )
    
    // 调用API保存模板
    const selectedQuestionDetails 
  
  .question-bank-card,
  .ai-recommend-card,
  .interview-sheet-card {
    height: 100%;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  // 搜索筛选
  .search-filter {
    margin-bottom: 15px;
  }
  
  // 题目列表
  .question-list {
    .question-item {
      display: flex;
      align-items: flex-start;
      padding: 10px;
      border-bottom: 1px solid #ebeef5;
      cursor: pointer;
      transition: background-color 0.3s;
      
      &:hover {
        background-color: #f5f7fa;
      }
      
      &.active {
        background-color: #ecf5ff;
      }
      
      .question-content {
        flex: 1;
        margin-left: 10px;
        
        .question-title {
          font-size: 14px;
          color: #303133;
          margin-bottom: 5px;
        }
        
        .question-meta {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
  
  // 题库统计
  .bank-stats {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #ebeef5;
    
    .stat-item {
      text-align: center;
      
      .value {
        font-size: 20px;
        font-weight: bold;
        color: #409eff;
      }
      
      .label {
        font-size: 12px;
        color: #909399;
        margin-top: 5px;
      }
    }
  }
  
  // 难度配置
  .difficulty-config {
    .diff-item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      
      span:first-child {
        width: 40px;
        text-align: right;
        margin-right: 10px;
      }
      
      .el-input-number {
        width: 100px;
      }
    }
  }
  
  // 推荐结果
  .recommend-result {
    margin-top: 20px;
    
    .recommended-list {
      max-height: 300px;
      overflow-y: auto;
      
      .recommended-item {
        padding: 10px;
        border: 1px solid #ebeef5;
        border-radius: 4px;
        margin-bottom: 10px;
        
        .item-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;
          
          .index {
            width: 24px;
            height: 24px;
            background-color: #409eff;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
          }
        }
        
        .item-content {
          .question {
            font-size: 14px;
            color: #303133;
            margin-bottom: 5px;
          }
          
          .reason {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }
    
    .recommend-actions {
      margin-top: 15px;
      text-align: center;
    }
  }
  
  // 已选题目
  .selected-question {
    display: flex;
    align-items: flex-start;
    padding: 10px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    margin-bottom: 8px;
    
    .drag-handle {
      cursor: move;
      color: #909399;
      margin-right: 10px;
    }
    
    .number {
      width: 20px;
      text-align: center;
      color: #409eff;
      font-weight: bold;
      margin-right: 10px;
    }
    
    .content {
      flex: 1;
      
      .title {
        font-size: 13px;
        color: #303133;
        margin-bottom: 5px;
      }
      
      .info {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 12px;
        color: #909399;
      }
    }
  }
  
  // 时间分配
  .time-allocation {
    margin: 15px 0;
    padding: 15px 0;
    border-top: 1px solid #ebeef5;
    border-bottom: 1px solid #ebeef5;
  }
  
  // 操作按钮
  .sheet-actions {
    text-align: center;
  }
}

// 题单预览
.sheet-preview {
  .preview-header {
    margin-bottom: 20px;
    
    h2 {
      text-align: center;
      margin-bottom: 20px;
    }
  }
  
  .preview-content {
    .preview-question {
      margin-bottom: 20px;
      padding-bottom: 20px;
      border-bottom: 1px solid #ebeef5;
      
      h4 {
        margin-bottom: 10px;
        color: #303133;
      }
      
      .question-meta {
        font-size: 12px;
        color: #909399;
        margin-bottom: 10px;
        
        span {
          margin-right: 15px;
        }
      }
      
      .question-answer {
        background-color: #f5f7fa;
        padding: 10px;
        border-radius: 4px;
        font-size: 13px;
        color: #606266;
        
        strong {
          color: #303133;
        }
        
        p {
          margin-top: 5px;
          line-height: 1.6;
        }
      }
    }
  }
  
  .preview-footer {
    margin-top: 20px;
    text-align: center;
  }
}

@media print {
  .sheet-preview {
    .preview-footer {
      display: none;
    }
  }
}
</style>
</script>
