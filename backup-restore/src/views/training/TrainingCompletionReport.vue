<template>
  <div class="training-completion-report">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>培训完成报告</h3>
          <div class="header-actions">
            <el-select v-model="selectedReport" @change="handleReportChange" placeholder="选择报告类型">
              <el-option label="个人学习档案" value="personal"  />
              <el-option label="课程完成报告" value="course"  />
              <el-option label="部门培训报告" value="department"  />
              <el-option label="年度培训总结" value="annual"  />
            </el-select>
            <el-date-picker
              v-model="reportDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handleDateRangeChange"
             />
            <el-button @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="primary" @click="handleGenerateReport">
              <el-icon><Document /></el-icon>
              生成报告
            </el-button>
          </div>
        </div>
      </template>

      <!-- 报告概览 -->
      <div class="report-overview">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="overview-card">
              <div class="card-icon">
                <el-icon size="32" color="#409eff"><User /></el-icon>
              </div>
              <div class="card-content">
                <div class="card-value">{{ overviewData.totalParticipants }}</div>
                <div class="card-label">参与人数</div>
                <div class="card-trend">
                  <span :class="{ positive: overviewData.participantGrowth > 0 }">
                    {{ overviewData.participantGrowth > 0 ? '+' : '' }}{{ overviewData.participantGrowth }}%
                  </span>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="overview-card">
              <div class="card-icon">
                <el-icon size="32" color="#67c23a"><Trophy /></el-icon>
              </div>
              <div class="card-content">
                <div class="card-value">{{ overviewData.completionRate }}%</div>
                <div class="card-label">完成率</div>
                <div class="card-trend">
                  <span :class="{ positive: overviewData.completionGrowth > 0 }">
                    {{ overviewData.completionGrowth > 0 ? '+' : '' }}{{ overviewData.completionGrowth }}%
                  </span>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="overview-card">
              <div class="card-icon">
                <el-icon size="32" color="#e6a23c"><Medal /></el-icon>
              </div>
              <div class="card-content">
                <div class="card-value">{{ overviewData.certificatesIssued }}</div>
                <div class="card-label">证书颁发</div>
                <div class="card-trend">
                  <span :class="{ positive: overviewData.certificateGrowth > 0 }">
                    {{ overviewData.certificateGrowth > 0 ? '+' : '' }}{{ overviewData.certificateGrowth }}%
                  </span>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="overview-card">
              <div class="card-icon">
                <el-icon size="32" color="#f56c6c"><Star /></el-icon>
              </div>
              <div class="card-content">
                <div class="card-value">{{ overviewData.avgSatisfaction }}</div>
                <div class="card-label">满意度</div>
                <div class="card-trend">
                  <span :class="{ positive: overviewData.satisfactionGrowth > 0 }">
                    {{ overviewData.satisfactionGrowth > 0 ? '+' : '' }}{{ overviewData.satisfactionGrowth }}%
                  </span>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 报告内容 -->
      <div class="report-content">
        <!-- 个人学习档案 -->
        <div v-if="selectedReport === 'personal'" class="personal-report">
          <el-card>
            <template #header>
              <div class="section-header">
                <h4>个人学习档案</h4>
                <div class="header-actions">
                  <el-input
                    v-model="personalSearchKeyword"
                    placeholder="搜索员工姓名"
                    clearable
                    style="width: 200px;"
                    />
                  <el-button @click="handleExportPersonalReport">导出档案</el-button>
                </div>
              </div>
            </template>

            <el-table
              :data="filteredPersonalData"
              v-loading="tableLoading"
              @row-click="handleViewPersonalDetail"
              style="cursor: pointer;"
            >
              <el-table-column prop="employeeName" label="员工姓名" width="120"  />
              <el-table-column prop="department" label="部门" width="100"  />
              <el-table-column prop="position" label="职位" width="120"  />
              <el-table-column prop="totalCourses" label="总课程数" width="100" align="center"  />
              <el-table-column prop="completedCourses" label="已完成" width="100" align="center"  />
              <el-table-column prop="completionRate" label="完成率" width="100" align="center">
                <template #default="scope">
                  <el-progress
                    :percentage="scope.row.completionRate"
                    :stroke-width="6"
                    :show-text="false"
                    :color="getProgressColor(scope.row.completionRate)"
                   />
                  <span style="margin-left: 10px;">{{ scope.row.completionRate }}%</span>
                </template>
              </el-table-column>
              <el-table-column prop="totalHours" label="学习时长" width="100" align="center">
                <template #default="scope">
                  {{ scope.row.totalHours }}h
                </template>
              </el-table-column>
              <el-table-column prop="certificatesEarned" label="获得证书" width="100" align="center"  />
              <el-table-column prop="lastActivity" label="最后活动" width="120" align="center">
                <template #default="scope">
                  {{ formatDate(scope.row.lastActivity) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120">
                <template #default="scope">
                  <el-button type="text" @click.stop="handleViewPersonalDetail(scope.row)">
                    查看详情
                  </el-button>
                  <el-button type="text" @click.stop="handlePrintCertificate(scope.row)">
                    打印证书
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </div>

        <!-- 课程完成报告 -->
        <div v-if="selectedReport === 'course'" class="course-report">
          <el-card>
            <template #header>
              <div class="section-header">
                <h4>课程完成报告</h4>
                <div class="header-actions">
                  <el-select v-model="courseFilter" clearable placeholder="筛选课程">
                    <el-option
                      v-for="course in courses"
                      :key="course.id"
                      :label="course.name"
                      :value="course.id"
                     />
                  </el-select>
                  <el-button @click="handleExportCourseReport">导出报告</el-button>
                </div>
              </div>
            </template>

            <el-table
              :data="filteredCourseData"
              v-loading="tableLoading"
              show-summary
              :summary-method="getCourseSummaries"
            >
              <el-table-column prop="courseName" label="课程名称" min-width="200"  />
              <el-table-column prop="instructor" label="讲师" width="100"  />
              <el-table-column prop="startDate" label="开始时间" width="120" align="center">
                <template #default="scope">
                  {{ formatDate(scope.row.startDate) }}
                </template>
              </el-table-column>
              <el-table-column prop="endDate" label="结束时间" width="120" align="center">
                <template #default="scope">
                  {{ formatDate(scope.row.endDate) }}
                </template>
              </el-table-column>
              <el-table-column prop="enrolledCount" label="报名人数" width="100" align="center"  />
              <el-table-column prop="completedCount" label="完成人数" width="100" align="center"  />
              <el-table-column prop="completionRate" label="完成率" width="100" align="center">
                <template #default="scope">
                  <span :class="{ 'high-rate': scope.row.completionRate >= 90 }">
                    {{ scope.row.completionRate }}%
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="avgScore" label="平均分" width="100" align="center"  />
              <el-table-column prop="satisfaction" label="满意度" width="100" align="center">
                <template #default="scope">
                  <el-rate
                    v-model="scope.row.satisfaction"
                    disabled
                    size="small"
                    :show-text="false"
                   />
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100" align="center">
                <template #default="scope">
                  <el-tag :type="getStatusColor(scope.row.status)">
                    {{ getStatusText(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </div>

        <!-- 部门培训报告 -->
        <div v-if="selectedReport === 'department'" class="department-report">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card>
                <template #header>
                  <h4>部门培训统计</h4>
                </template>
                <div ref="departmentStatsRef" class="chart-container"></div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card>
                <template #header>
                  <h4>培训完成率对比</h4>
                </template>
                <div ref="departmentComparisonRef" class="chart-container"></div>
              </el-card>
            </el-col>
          </el-row>
          
          <el-card style="margin-top: 20px;">
            <template #header>
              <h4>部门培训详情</h4>
            </template>
            <el-table :data="departmentData" v-loading="tableLoading">
              <el-table-column prop="department" label="部门" width="120"  />
              <el-table-column prop="totalEmployees" label="总人数" width="100" align="center"  />
              <el-table-column prop="participantCount" label="参与人数" width="100" align="center"  />
              <el-table-column prop="participationRate" label="参与率" width="100" align="center">
                <template #default="scope">
                  {{ scope.row.participationRate }}%
                </template>
              </el-table-column>
              <el-table-column prop="completionRate" label="完成率" width="100" align="center">
                <template #default="scope">
                  {{ scope.row.completionRate }}%
                </template>
              </el-table-column>
              <el-table-column prop="avgHours" label="平均学时" width="100" align="center">
                <template #default="scope">
                  {{ scope.row.avgHours }}h
                </template>
              </el-table-column>
              <el-table-column prop="certificatesCount" label="证书数" width="100" align="center"  />
              <el-table-column prop="totalCost" label="培训成本" width="120" align="right">
                <template #default="scope">
                  ¥{{ formatNumber(scope.row.totalCost) }}
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </div>

        <!-- 年度培训总结 -->
        <div v-if="selectedReport === 'annual'" class="annual-report">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-card>
                <template #header>
                  <h4>年度培训趋势</h4>
                </template>
                <div ref="annualTrendRef" class="chart-container-large"></div>
              </el-card>
            </el-col>
          </el-row>
          
          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="12">
              <el-card>
                <template #header>
                  <h4>培训类型分布</h4>
                </template>
                <div ref="trainingTypeRef" class="chart-container"></div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card>
                <template #header>
                  <h4>培训成果统计</h4>
                </template>
                <div class="achievement-stats">
                  <div class="stat-item">
                    <div class="stat-label">累计培训人次</div>
                    <div class="stat-value">{{ annualData.totalTrainings }}</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-label">培训总时长</div>
                    <div class="stat-value">{{ annualData.totalHours }}h</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-label">颁发证书数</div>
                    <div class="stat-value">{{ annualData.certificatesIssued }}</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-label">培训总投入</div>
                    <div class="stat-value">¥{{ formatNumber(annualData.totalInvestment) }}</div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 证书打印对话框 -->
      <el-dialog
        v-model="certificateDialog.visible"
        title="证书打印"
        width="600px"
        @close="handleCloseCertificateDialog"
      >
        <div class="certificate-preview">
          <div class="certificate-header">
            <h2>培训完成证书</h2>
            <div class="certificate-number">证书编号: {{ certificateDialog.certificateNumber }}</div>
          </div>
          <div class="certificate-content">
            <p class="certificate-text">
              兹证明 <strong>{{ certificateDialog.employeeName }}</strong> 
              （{{ certificateDialog.department }}）于 {{ formatDate(certificateDialog.completionDate) }} 
              完成了《{{ certificateDialog.courseName }}》培训课程。
            </p>
            <div class="certificate-details">
              <div class="detail-item">
                <span>培训时长：</span>
                <span>{{ certificateDialog.trainingHours }}小时</span>
              </div>
              <div class="detail-item">
                <span>培训成绩：</span>
                <span>{{ certificateDialog.score }}分</span>
              </div>
              <div class="detail-item">
                <span>证书等级：</span>
                <span>{{ certificateDialog.level }}</span>
              </div>
            </div>
          </div>
          <div class="certificate-footer">
            <div class="signature-section">
              <div class="signature-item">
                <div class="signature-line"></div>
                <div class="signature-label">培训机构</div>
              </div>
              <div class="signature-item">
                <div class="signature-line"></div>
                <div class="signature-label">签发日期</div>
              </div>
            </div>
          </div>
        </div>
        
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="certificateDialog.visible = false">取消</el-button>
            <el-button type="primary" @click="handleConfirmPrintCertificate">
              打印证书
            </el-button>
          </div>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import {
  Refresh,
  Document,
  User,
  Trophy,
  Medal,
  Star
} from '@element-plus/icons-vue'

// 响应式数据
const selectedReport = ref('personal')
const reportDateRange = ref([])
const personalSearchKeyword = ref('')
const courseFilter = ref('')
const tableLoading = ref(false)

// 图表引用
const departmentStatsRef = ref()
const departmentComparisonRef = ref()
const annualTrendRef = ref()
const trainingTypeRef = ref()

// 概览数据
const overviewData = reactive({
  totalParticipants: 1285,
  participantGrowth: 12.5,
  completionRate: 87.2,
  completionGrowth: 5.8,
  certificatesIssued: 892,
  certificateGrowth: 15.2,
  avgSatisfaction: 4.6,
  satisfactionGrowth: 3.2
})

// 个人数据
const personalData = ref([
  {
    id: '1',
    employeeName: '张三',
    department: '技术部',
    position: '前端工程师',
    totalCourses: 12,
    completedCourses: 10,
    completionRate: 83,
    totalHours: 45,
    certificatesEarned: 3,
    lastActivity: new Date('2025-01-20')
  },
  {
    id: '2',
    employeeName: '李四',
    department: '技术部',
    position: '后端工程师',
    totalCourses: 8,
    completedCourses: 8,
    completionRate: 100,
    totalHours: 32,
    certificatesEarned: 2,
    lastActivity: new Date('2025-01-19')
  },
  {
    id: '3',
    employeeName: '王五',
    department: '销售部',
    position: '销售经理',
    totalCourses: 6,
    completedCourses: 4,
    completionRate: 67,
    totalHours: 24,
    certificatesEarned: 1,
    lastActivity: new Date('2025-01-18')
  }
])

// 课程数据
const courses = ref([
  { id: '1', name: 'HrVue.js 高级开发' },
  { id: '2', name: '项目管理实战' },
  { id: '3', name: '新员工培训' }
])

const courseData = ref([
  {
    id: '1',
    courseName: 'Vue.js 高级开发',
    instructor: '王老师',
    startDate: new Date('2025-01-15'),
    endDate: new Date('2025-01-20'),
    enrolledCount: 25,
    completedCount: 23,
    completionRate: 92,
    avgScore: 88,
    satisfaction: 4.8,
    status: 'completed'
  },
  {
    id: '2',
    courseName: '项目管理实战',
    instructor: '李老师',
    startDate: new Date('2025-01-10'),
    endDate: new Date('2025-01-18'),
    enrolledCount: 18,
    completedCount: 16,
    completionRate: 89,
    avgScore: 85,
    satisfaction: 4.5,
    status: 'completed'
  },
  {
    id: '3',
    courseName: '新员工培训',
    instructor: '张老师',
    startDate: new Date('2025-01-08'),
    endDate: new Date('2025-01-25'),
    enrolledCount: 45,
    completedCount: 32,
    completionRate: 71,
    avgScore: 82,
    satisfaction: 4.2,
    status: 'ongoing'
  }
])

// 部门数据
const departmentData = ref([
  {
    department: '技术部',
    totalEmployees: 85,
    participantCount: 78,
    participationRate: 92,
    completionRate: 87,
    avgHours: 35,
    certificatesCount: 56,
    totalCost: 180000
  },
  {
    department: '销售部',
    totalEmployees: 65,
    participantCount: 58,
    participationRate: 89,
    completionRate: 82,
    avgHours: 28,
    certificatesCount: 42,
    totalCost: 125000
  },
  {
    department: '市场部',
    totalEmployees: 45,
    participantCount: 38,
    participationRate: 84,
    completionRate: 78,
    avgHours: 32,
    certificatesCount: 28,
    totalCost: 98000
  }
])

// 年度数据
const annualData = reactive({
  totalTrainings: 2890,
  totalHours: 58750,
  certificatesIssued: 1245,
  totalInvestment: 1850000
})

// 证书对话框
const certificateDialog = reactive({
  visible: false,
  employeeName: '',
  department: '',
  courseName: '',
  completionDate: new Date(),
  trainingHours: 0,
  score: 0,
  level: '',
  certificateNumber: ''
})

// 计算属性
const filteredPersonalData = computed(() => {
  return personalData.value.filter(item => {
    return !personalSearchKeyword.value || 
      item.employeeName.includes(personalSearchKeyword.value) ||
      item.department.includes(personalSearchKeyword.value)
  })
})

const filteredCourseData = computed(() => {
  return courseData.value.filter(item => {
    return !courseFilter.value || item.id === courseFilter.value
  })
})

// 方法
const handleReportChange = () => {
  loadReportData()
}

const handleDateRangeChange = () => {
  loadReportData()
}

const handleRefresh = () => {
  ElMessage.success('数据已刷新')
  loadReportData()
}

const handleGenerateReport = () => {
  ElMessage.success('正在生成报告...')
}

const handleExportPersonalReport = () => {
  ElMessage.success('正在导出个人学习档案...')
}

const handleExportCourseReport = () => {
  ElMessage.success('正在导出课程完成报告...')
}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleViewPersonalDetail = (row: unknown) => {
  ElMessage.info(`查看${row.employeeName}的详细学习档案`)
}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handlePrintCertificate = (row: unknown) => {
  certificateDialog.visible = true
  certificateDialog.employeeName = row.employeeName
  certificateDialog.department = row.department
  certificateDialog.courseName = 'Vue.js 高级开发'
  certificateDialog.completionDate = new Date()
  certificateDialog.trainingHours = row.totalHours
  certificateDialog.score = 88
  certificateDialog.level = '优秀'
  certificateDialog.certificateNumber = 'CERT-' + Date.now()
}

const handleConfirmPrintCertificate = () => {
  ElMessage.success('证书打印成功')
  certificateDialog.visible = false
}

const handleCloseCertificateDialog = () => {
  certificateDialog.visible = false
}

// 渲染图表
const renderDepartmentStats = () => {
  if (!departmentStatsRef.value) return
  
  const chart = echarts.init(departmentStatsRef.value)
  const option = {
    title: {
      text: '部门培训统计',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['参与人数', '完成人数', '获证人数'],
      bottom: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['技术部', '销售部', '市场部', '人事部', '财务部']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '参与人数',
        type: 'bar',
        data: [78, 58, 38, 25, 18],
        itemStyle: { color: '#409eff' }
      },
      {
        name: '完成人数',
        type: 'bar',
        data: [68, 48, 30, 22, 15],
        itemStyle: { color: '#67c23a' }
      },
      {
        name: '获证人数',
        type: 'bar',
        data: [56, 42, 28, 18, 12],
        itemStyle: { color: '#e6a23c' }
      }
    ]
  }
  chart.setOption(option)
}

const renderDepartmentComparison = () => {
  if (!departmentComparisonRef.value) return
  
  const chart = echarts.init(departmentComparisonRef.value)
  const option = {
    title: {
      text: '培训完成率对比',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['参与率', '完成率'],
      bottom: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['技术部', '销售部', '市场部', '人事部', '财务部']
    },
    yAxis: {
      type: 'value',
      max: 100
    },
    series: [
      {
        name: '参与率',
        type: 'line',
        data: [92, 89, 84, 88, 85],
        smooth: true,
        itemStyle: { color: '#409eff' }
      },
      {
        name: '完成率',
        type: 'line',
        data: [87, 82, 78, 88, 83],
        smooth: true,
        itemStyle: { color: '#67c23a' }
      }
    ]
  }
  chart.setOption(option)
}

const renderAnnualTrend = () => {
  if (!annualTrendRef.value) return
  
  const chart = echarts.init(annualTrendRef.value)
  const option = {
    title: {
      text: '年度培训趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['培训人次', '完成人次', '证书颁发'],
      bottom: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '培训人次',
        type: 'line',
        data: [220, 182, 191, 234, 290, 330, 310, 250, 280, 320, 290, 310],
        smooth: true,
        itemStyle: { color: '#409eff' }
      },
      {
        name: '完成人次',
        type: 'line',
        data: [180, 165, 170, 210, 260, 290, 270, 220, 250, 280, 260, 280],
        smooth: true,
        itemStyle: { color: '#67c23a' }
      },
      {
        name: '证书颁发',
        type: 'line',
        data: [95, 88, 92, 115, 140, 158, 148, 120, 135, 152, 142, 152],
        smooth: true,
        itemStyle: { color: '#e6a23c' }
      }
    ]
  }
  chart.setOption(option)
}

const renderTrainingType = () => {
  if (!trainingTypeRef.value) return
  
  const chart = echarts.init(trainingTypeRef.value)
  const option = {
    title: {
      text: '培训类型分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '培训类型',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 1048, name: '技术培训' },
          { value: 735, name: '管理培训' },
          { value: 580, name: '新员工培训' },
          { value: 484, name: '合规培训' },
          { value: 300, name: '其他培训' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  chart.setOption(option)
}

const loadReportData = async () => {
  try {
    tableLoading.value = true
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 根据选择的报告类型渲染相应图表
    nextTick(() => {
      if (selectedReport.value === 'department') {
        renderDepartmentStats()
        renderDepartmentComparison()
      } else if (selectedReport.value === 'annual') {
        renderAnnualTrend()
        renderTrainingType()
      }
    })
  } catch (__error) {
    ElMessage.error('加载数据失败')
  } finally {
    tableLoading.value = false
  }
}

// 辅助方法
const formatDate = (date: Date) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

const formatNumber = (num: number) => {
  return num.toLocaleString()
}

const getProgressColor = (percentage: number) => {
  if (percentage >= 90) return '#67c23a'
  if (percentage >= 70) return '#409eff'
  if (percentage >= 50) return '#e6a23c'
  return '#f56c6c'
}

const getStatusColor = (status: string) => {
  const colorMap = {
    completed: 'success',
    ongoing: 'warning',
    planned: 'info'
  }
  return colorMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap = {
    completed: '已完成',
    ongoing: '进行中',
    planned: '计划中'
  }
  return textMap[status] || '未知'
}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const getCourseSummaries = (param: unknown) => {
  const {columns, data: _data} =  param
  const sums: string[] 
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.report-overview {
  margin-bottom: 30px;
}

.overview-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.card-icon {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.card-label {
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 5px;
}

.card-trend {
  font-size: 12px;
}

.positive {
  color: #67c23a;
}

.report-content {
  margin-bottom: 30px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-header .header-actions {
  display: flex;
  gap: 10px;
}

.chart-container {
  width: 100%;
  height: 300px;
}

.chart-container-large {
  width: 100%;
  height: 400px;
}

.high-rate {
  color: #67c23a;
  font-weight: bold;
}

.achievement-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  padding: 20px;
}

.stat-item {
  text-align: center;
  padding: 15px;
  border-radius: 8px;
  background: #f5f7fa;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.certificate-preview {
  border: 2px solid #409eff;
  border-radius: 8px;
  padding: 30px;
  background: #fafbfc;
  margin: 20px 0;
}

.certificate-header {
  text-align: center;
  margin-bottom: 30px;
}

.certificate-header h2 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 24px;
}

.certificate-number {
  font-size: 14px;
  color: #606266;
}

.certificate-content {
  margin-bottom: 30px;
}

.certificate-text {
  font-size: 16px;
  line-height: 1.8;
  text-align: center;
  margin-bottom: 20px;
}

.certificate-details {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-top: 20px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.certificate-footer {
  margin-top: 30px;
}

.signature-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.signature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.signature-line {
  width: 120px;
  height: 1px;
  background: #dcdfe6;
}

.signature-label {
  font-size: 14px;
  color: #606266;
}

@media (max-width: 768px) {
  .report-overview .el-col {
    margin-bottom: 20px;
  }
  
  .section-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .achievement-stats {
    grid-template-columns: 1fr;
  }
  
  .certificate-details {
    grid-template-columns: 1fr;
  }
  
  .signature-section {
    flex-direction: column;
    gap: 20px;
  }
}
</style>
</script>
