<template>
  <div class="modern-salary-management" :class="{ 'mobile-layout': isMobile }">
    <!-- 现代化页面头部 -->
    <div class="page-header" role="banner">
      <div class="header-content">
        <div class="header-title">
          <h1>薪酬福利管理</h1>
          <p class="subtitle">智能化薪酬数据管理、分析和报表系统</p>
        </div>

        <!-- 快速操作按钮 -->
        <div class="header-actions" v-if="!isMobile">
          <el-button type="primary" size="large" @click="handleQuickQuery">
            <el-icon><Search /></el-icon>
            快速查询
          </el-button>

          <el-dropdown trigger="click" placement="bottom-end">
            <el-button size="large">
              <el-icon><MoreFilled /></el-icon>
              更多操作
            </el-button>

            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="handleQuickImport">
                  <el-icon><Upload /></el-icon>
                  数据导入
                </el-dropdown-item>
                <el-dropdown-item @click="handleQuickExport">
                  <el-icon><Download /></el-icon>
                  数据导出
                </el-dropdown-item>
                <el-dropdown-item @click="handleQuickAnalysis">
                  <el-icon><TrendCharts /></el-icon>
                  薪酬分析
                </el-dropdown-item>
                <el-dropdown-item @click="navigateToSystemConfig" divided>
                  <el-icon><Setting /></el-icon>
                  系统配置
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>

        <!-- 移动端操作按钮 -->
        <div class="mobile-actions" v-if="isMobile">
          <el-button type="primary" circle @click="handleQuickQuery">
            <el-icon><Search /></el-icon>
          </el-button>
          <el-button circle @click="showMobileMenu = true">
            <el-icon><MoreFilled /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 安全提醒 -->
    <div class="security-notice">
      <el-alert
        title="数据安全提醒"
        description="薪酬数据属于敏感信息，请严格按照权限访问，所有操作将被记录审计。"
        type="warning"
        show-icon
        :closable="false"
      />
    </div>

    <!-- 现代化功能导航 -->
    <div class="function-navigation">
      <div class="nav-grid" :class="{ 'mobile-grid': isMobile }">
        <div
          v-for="nav in navigationItems"
          :key="nav.id"
          class="modern-nav-card"
          :class="[`nav-${nav.type}`, { 'clickable': nav.clickable }]"
          @click="nav.clickable && handleNavigation(nav.id)"
          role="button"
          :tabindex="nav.clickable ? 0 : -1"
          :aria-label="`${nav.title}: ${nav.description}`"
          @keydown.enter="nav.clickable && handleNavigation(nav.id)"
          @keydown.space.prevent="nav.clickable && handleNavigation(nav.id)"
        >
          <div class="nav-header">
            <div class="nav-icon" :style="{ background: nav.gradient }">
              <el-icon :size="28">
                <component :is="nav.icon" />
              </el-icon>
            </div>
            <div class="nav-badge" v-if="nav.badge">
              <span class="badge-text">{{ nav.badge }}</span>
            </div>
          </div>

          <div class="nav-content">
            <h3 class="nav-title">{{ nav.title }}</h3>
            <p class="nav-description">{{ nav.description }}</p>
            <div class="nav-stats" v-if="nav.stats">
              <span class="stats-value">{{ nav.stats.value }}</span>
              <span class="stats-label">{{ nav.stats.label }}</span>
            </div>
          </div>

          <div class="nav-footer">
            <el-icon class="nav-arrow">
              <ArrowRight />
            </el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 现代化统计概览 -->
    <div class="stats-overview">
      <div class="stats-grid" :class="{ 'mobile-grid': isMobile }">
        <div
          v-for="stat in statsCards"
          :key="stat.id"
          class="modern-stats-card"
          :class="[`stats-${stat.type}`, { 'clickable': stat.clickable }]"
          @click="stat.clickable && handleStatsClick(stat.id)"
          role="button"
          :tabindex="stat.clickable ? 0 : -1"
          :aria-label="`${stat.title}: ${stat.value}${stat.unit || ''}`"
          @keydown.enter="stat.clickable && handleStatsClick(stat.id)"
          @keydown.space.prevent="stat.clickable && handleStatsClick(stat.id)"
        >
          <div class="stats-header">
            <div class="stats-icon" :style="{ background: stat.gradient }">
              <el-icon :size="24">
                <component :is="stat.icon" />
              </el-icon>
            </div>
            <div class="stats-trend" v-if="stat.trend !== undefined">
              <el-icon :class="stat.trend >= 0 ? 'trend-up' : 'trend-down'">
                <component :is="stat.trend >= 0 ? ArrowUp : ArrowDown" />
              </el-icon>
              <span class="trend-value">{{ Math.abs(stat.trend) }}%</span>
            </div>
          </div>

          <div class="stats-content">
            <div class="stats-value">
              <span class="stats-number">{{ formatStatValue(stat.value, stat.type) }}</span>
              <span class="stats-unit" v-if="stat.unit">{{ stat.unit }}</span>
            </div>
            <div class="stats-title">{{ stat.title }}</div>
            <div class="stats-subtitle" v-if="stat.subtitle">{{ stat.subtitle }}</div>
          </div>

          <div class="stats-chart" v-if="stat.chartData">
            <div class="mini-chart">
              <div
                v-for="(point, index) in stat.chartData"
                :key="index"
                class="chart-bar"
                :style="{
                  height: `${point}%`,
                  background: stat.gradient
                }"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据可视化图表 -->
    <div class="charts-section">
      <el-row :gutter="24">
        <el-col :span="isMobile ? 24 : 16">
          <div class="chart-container">
            <div class="chart-header">
              <h3>薪酬趋势分析</h3>
              <div class="chart-controls">
                <el-button-group size="small">
                  <el-button
                    v-for="period in chartPeriods"
                    :key="period.key"
                    :type="activeChartPeriod === period.key ? 'primary' : 'default'"
                    @click="handleChartPeriodChange(period.key)"
                  >
                    {{ period.label }}
                  </el-button>
                </el-button-group>
                <el-button size="small" @click="handleChartFullscreen">
                  <el-icon><FullScreen /></el-icon>
                </el-button>
              </div>
            </div>
            <div class="chart-content" ref="salaryTrendChartRef" style="height: 300px;"></div>
          </div>
        </el-col>

        <el-col :span="isMobile ? 24 : 8">
          <div class="chart-container">
            <div class="chart-header">
              <h3>薪酬结构分布</h3>
              <el-button size="small" @click="handleStructureAnalysis">
                <el-icon><DataAnalysis /></el-icon>
                详细分析
              </el-button>
            </div>
            <div class="chart-content" ref="salaryStructureChartRef" style="height: 300px;"></div>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="24" style="margin-top: 24px;">
        <el-col :span="isMobile ? 24 : 12">
          <div class="chart-container">
            <div class="chart-header">
              <h3>部门薪酬对比</h3>
              <el-select v-model="selectedDepartmentType" size="small" style="width: 120px;">
                <el-option label="学院" value="college" />
                <el-option label="部门" value="department" />
                <el-option label="岗位" value="position" />
              </el-select>
            </div>
            <div class="chart-content" ref="departmentComparisonChartRef" style="height: 250px;"></div>
          </div>
        </el-col>

        <el-col :span="isMobile ? 24 : 12">
          <div class="chart-container">
            <div class="chart-header">
              <h3>预算执行监控</h3>
              <el-tag :type="getBudgetStatusType()" size="small">
                {{ getBudgetStatusText() }}
              </el-tag>
            </div>
            <div class="chart-content" ref="budgetMonitoringChartRef" style="height: 250px;"></div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 预算监控和异常提醒 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card class="monitoring-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>预算监控</span>
              <el-button size="small" type="primary" link @click="viewBudgetDetails">
                查看详情
              </el-button>
            </div>
          </template>
          <div class="budget-monitoring">
            <div
              v-for="item in budgetMonitoring"
              :key="item.organizationId"
              class="budget-item"
              :class="getBudgetItemClass(item.alertLevel)"
            >
              <div class="budget-info">
                <div class="budget-org">{{ item.organizationName }}</div>
                <div class="budget-progress">
                  <el-progress
                    :percentage="item.utilizationRate"
                    :status="getBudgetProgressStatus(item.alertLevel)"
                    :stroke-width="8"
                  />
                </div>
                <div class="budget-details">
                  <span>已用：{{ formatCurrency(item.actualAmount) }}</span>
                  <span>预算：{{ formatCurrency(item.budgetAmount) }}</span>
                </div>
              </div>
              <div class="budget-alert">
                <el-tag :type="getBudgetAlertTagType(item.alertLevel)" size="small">
                  {{ item.alertLevelName }}
                </el-tag>
              </div>
            </div>
            <div v-if="budgetMonitoring.length === 0" class="empty-state">
              暂无预算监控数据
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="exceptions-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>异常提醒</span>
              <el-button size="small" type="primary" link @click="viewAllExceptions">
                查看全部
              </el-button>
            </div>
          </template>
          <div class="exceptions-list">
            <div
              v-for="item in salaryExceptions"
              :key="item.id"
              class="exception-item"
              @click="handleViewException(item)"
            >
              <div class="exception-info">
                <div class="exception-title">{{ item.employeeName }} - {{ item.exceptionTypeName }}</div>
                <div class="exception-desc">{{ item.description }}</div>
                <div class="exception-time">{{ formatDateTime(item.detectedTime || '') }}</div>
              </div>
              <div class="exception-status">
                <el-tag :type="getExceptionStatusTagType(item.status)" size="small">
                  {{ item.statusName }}
                </el-tag>
              </div>
            </div>
            <div v-if="salaryExceptions.length === 0" class="empty-state">
              暂无异常数据
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据访问权限对话框 -->
    <el-dialog
      v-model="accessDialogVisible"
      title="申请敏感数据访问权限"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form ref="accessFormRef" :model="accessForm" :rules="accessRules" label-width="120px">
        <el-form-item label="访问原因" prop="reason">
          <el-input
            v-model="accessForm.reason"
            type="textarea"
            :rows="4"
            placeholder="请详细说明访问敏感数据的原因"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="accessDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleRequestAccess" :loading="accessLoading">
            申请权限
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 移动端操作菜单 -->
    <el-drawer
      v-model="showMobileMenu"
      title="操作菜单"
      direction="btt"
      size="auto"
    >
      <div class="mobile-menu-content">
        <div class="menu-item" @click="handleQuickQuery">
          <el-icon><Search /></el-icon>
          <span>快速查询</span>
        </div>
        <div class="menu-item" @click="handleQuickImport">
          <el-icon><Upload /></el-icon>
          <span>数据导入</span>
        </div>
        <div class="menu-item" @click="handleQuickExport">
          <el-icon><Download /></el-icon>
          <span>数据导出</span>
        </div>
        <div class="menu-item" @click="handleQuickAnalysis">
          <el-icon><TrendCharts /></el-icon>
          <span>薪酬分析</span>
        </div>
        <div class="menu-item" @click="navigateToSystemConfig">
          <el-icon><Setting /></el-icon>
          <span>系统配置</span>
        </div>
      </div>
    </el-drawer>

    <!-- 图表全屏对话框 -->
    <el-dialog
      v-model="chartFullscreenVisible"
      title="薪酬趋势分析"
      width="90%"
      :fullscreen="isMobile"
      append-to-body
    >
      <div ref="fullscreenChartRef" style="height: 500px;"></div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import {
  Search,
  Document,
  TrendCharts,
  Setting,
  User,
  Money,
  Wallet,
  Upload,
  Download,
  MoreFilled,
  ArrowRight,
  ArrowUp,
  ArrowDown,
  FullScreen,
  DataAnalysis
} from '@element-plus/icons-vue'
import { salaryApi, type BudgetMonitoring, type SalaryException } from '@/api/salary'
import { useMobile } from '@/composables/useMobile'
import { formatDateTime } from '@/utils/date'

const router = useRouter()

// 移动端适配
const { isMobile } = useMobile()

// UI状态
const showMobileMenu = ref(false)
const chartFullscreenVisible = ref(false)
const activeChartPeriod = ref('6months')
const selectedDepartmentType = ref('college')

// 图表引用
const salaryTrendChartRef = ref<HTMLElement>()
const salaryStructureChartRef = ref<HTMLElement>()
const departmentComparisonChartRef = ref<HTMLElement>()
const budgetMonitoringChartRef = ref<HTMLElement>()
const fullscreenChartRef = ref<HTMLElement>()

// 响应式数据
const stats = reactive({
  totalEmployees: 0,
  totalAmount: 0,
  averageAmount: 0,
  budgetUtilization: 0
})

// 图表周期选项
const chartPeriods = [
  { key: '3months', label: '近3月' },
  { key: '6months', label: '近6月' },
  { key: '1year', label: '近1年' },
  { key: '2years', label: '近2年' }
]

// 导航项配置
const navigationItems = computed(() => [
  {
    id: 'query',
    title: '工资查询',
    description: '查询、导出工资数据',
    icon: Search,
    type: 'primary',
    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    clickable: true,
    stats: { value: '1,250', label: '条记录' },
    badge: 'Hot'
  },
  {
    id: 'summary',
    title: '汇总导出',
    description: '部门汇总、批量导出',
    icon: Document,
    type: 'success',
    gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    clickable: true,
    stats: { value: '15', label: '个部门' }
  },
  {
    id: 'analysis',
    title: '薪酬分析',
    description: '竞争力、公平性分析',
    icon: TrendCharts,
    type: 'warning',
    gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    clickable: true,
    stats: { value: '85%', label: '满意度' }
  },
  {
    id: 'config',
    title: '系统配置',
    description: '对接配置、安全设置',
    icon: Setting,
    type: 'info',
    gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    clickable: true,
    stats: { value: '99.9%', label: '可用性' }
  }
])

// 统计卡片配置
const statsCards = computed(() => [
  {
    id: 'employees',
    title: '薪酬人数',
    value: stats.totalEmployees,
    unit: '人',
    icon: User,
    type: 'primary',
    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    trend: 3.2,
    subtitle: '较上月',
    clickable: true,
    chartData: [65, 70, 68, 75, 72, 78, 80, 85]
  },
  {
    id: 'amount',
    title: '本月总额',
    value: stats.totalAmount,
    unit: '',
    icon: Money,
    type: 'success',
    gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    trend: 5.8,
    subtitle: '较上月',
    clickable: true,
    chartData: [80, 85, 82, 88, 90, 92, 95, 98]
  },
  {
    id: 'average',
    title: '平均薪酬',
    value: stats.averageAmount,
    unit: '',
    icon: TrendCharts,
    type: 'warning',
    gradient: 'linear-gradient(135deg, #feca57 0%, #ff9ff3 100%)',
    trend: 2.1,
    subtitle: '较上月',
    clickable: true,
    chartData: [70, 72, 75, 73, 78, 80, 82, 85]
  },
  {
    id: 'budget',
    title: '预算执行率',
    value: stats.budgetUtilization,
    unit: '%',
    icon: Wallet,
    type: 'danger',
    gradient: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)',
    trend: -1.2,
    subtitle: '较上月',
    clickable: true,
    chartData: [85, 87, 90, 88, 85, 82, 80, 78]
  }
])

// 最近操作记录
const recentOperations = ref([
  {
    id: 1,
    title: '工资数据导入',
    description: '导入2025年6月工资数据，共1250条记录',
    type: 'import',
    status: 'success',
    statusText: '成功',
    operatorName: '张三',
    createTime: '2025-06-22 10:30:00'
  },
  {
    id: 2,
    title: '薪酬分析报告',
    description: '生成计算机学院薪酬竞争力分析报告',
    type: 'analysis',
    status: 'processing',
    statusText: '处理中',
    operatorName: '李四',
    createTime: '2025-06-22 09:15:00'
  },
  {
    id: 3,
    title: '预算监控预警',
    description: '机械学院预算执行率超过90%，触发预警',
    type: 'alert',
    status: 'pending',
    statusText: '待处理',
    operatorName: '系统',
    createTime: '2025-06-22 08:45:00'
  }
])

const budgetMonitoring = ref<BudgetMonitoring[]>([])
const salaryExceptions = ref<SalaryException[]>([])

// 数据访问权限相关
const accessDialogVisible = ref(false)
const accessFormRef = ref()
const accessLoading = ref(false)
const accessForm = reactive({
  reason: ''
})

const accessRules = {
  reason: [
    { required: true, message: '请填写访问原因', trigger: 'blur' },
    { min: 10, max: 200, message: '访问原因长度在 10 到 200 个字符', trigger: 'blur' }
  ]
}

// 导航处理
const handleNavigation = (navId: string) => {
  switch (navId) {
    case 'query':
      router.push('/salary/query')
      break
    case 'summary':
      router.push('/salary/summary')
      break
    case 'analysis':
      router.push('/salary/analysis')
      break
    case 'config':
      router.push('/salary/config')
      break
  }
}

// 统计卡片点击处理
const handleStatsClick = (statId: string) => {
  switch (statId) {
    case 'employees':
      router.push('/salary/query?filter=employees')
      break
    case 'amount':
      router.push('/salary/analysis?type=amount')
      break
    case 'average':
      router.push('/salary/analysis?type=average')
      break
    case 'budget':
      router.push('/salary/budget')
      break
  }
}

// 格式化统计值
const formatStatValue = (value: number, type: string) => {
  if (type === 'amount' || type === 'average') {
    return formatCurrency(value)
  }
  if (value >= 1000) {
    return (value / 1000).toFixed(1) + 'K'
  }
  return value.toString()
}

// 图表周期变化
const handleChartPeriodChange = (period: string) => {
  activeChartPeriod.value = period
  updateCharts()
}

// 图表全屏
const handleChartFullscreen = () => {
  chartFullscreenVisible.value = true
  nextTick(() => {
    initFullscreenChart()
  })
}

// 结构分析
const handleStructureAnalysis = () => {
  router.push('/salary/analysis?type=structure')
}

// 预算状态
const getBudgetStatusType = () => {
  const rate = stats.budgetUtilization
  if (rate >= 90) return 'danger'
  if (rate >= 80) return 'warning'
  return 'success'
}

const getBudgetStatusText = () => {
  const rate = stats.budgetUtilization
  if (rate >= 90) return '预算紧张'
  if (rate >= 80) return '预算正常'
  return '预算充足'
}

// 操作记录相关
const getOperationIcon = (type: string) => {
  switch (type) {
    case 'import': return Upload
    case 'export': return Download
    case 'analysis': return TrendCharts
    case 'alert': return 'Warning'
    default: return Document
  }
}

const getOperationTagType = (status: string) => {
  switch (status) {
    case 'success': return 'success'
    case 'processing': return 'primary'
    case 'pending': return 'warning'
    case 'failed': return 'danger'
    default: return 'info'
  }
}

const viewAllOperations = () => {
  router.push('/salary/operations')
}

// 导航到各个功能模块
const navigateToSalaryQuery = () => {
  router.push('/salary/query')
}

const navigateToSalarySummary = () => {
  router.push('/salary/summary')
}

const navigateToSalaryAnalysis = () => {
  router.push('/salary/analysis')
}

const navigateToSystemConfig = () => {
  router.push('/salary/config')
}

// 快速操作
const handleQuickQuery = () => {
  router.push('/salary/query?action=search')
}

const handleQuickImport = () => {
  router.push('/salary/config?tab=import')
}

const handleQuickExport = () => {
  router.push('/salary/summary?action=export')
}

const handleQuickAnalysis = () => {
  router.push('/salary/analysis?action=generate')
}

// 查看详情
const viewBudgetDetails = () => {
  router.push('/salary/budget')
}

const viewAllExceptions = () => {
  router.push('/salary/exceptions')
}

const handleViewException = (exception: SalaryException) => {
  router.push(`/salary/exceptions/${exception.id}`)
}

// 申请敏感数据访问权限
const handleRequestAccess = async () => {
  try {
    await accessFormRef.value.validate()
    
    accessLoading.value = true
    
    const result = await salaryApi.requestSensitiveDataAccess(accessForm.reason)
    
    // 存储访问令牌
    sessionStorage.setItem('salaryAccessToken', result.accessToken)
    sessionStorage.setItem('salaryAccessExpires', (Date.now() + result.expiresIn * 1000).toString())
    
    ElMessage.success('权限申请成功，可以访问敏感数据')
    accessDialogVisible.value = false
    
    // 刷新数据
    fetchStatistics()
  } catch (error) {
    console.error('申请权限失败:', error)
    ElMessage.error('申请权限失败')
  } finally {
    accessLoading.value = false
  }
}

// 格式化货币
const formatCurrency = (amount: number) => {
  if (!amount) return '¥0'
  return `¥${amount.toLocaleString()}`
}

// 获取预算项目样式类
const getBudgetItemClass = (alertLevel: string) => {
  switch (alertLevel) {
    case 'WARNING':
      return 'budget-warning'
    case 'CRITICAL':
      return 'budget-critical'
    default:
      return 'budget-normal'
  }
}

// 获取预算进度状态
const getBudgetProgressStatus = (alertLevel: string) => {
  switch (alertLevel) {
    case 'WARNING':
      return 'warning'
    case 'CRITICAL':
      return 'exception'
    default:
      return 'success'
  }
}

// 获取预算警告标签类型
const getBudgetAlertTagType = (alertLevel: string) => {
  switch (alertLevel) {
    case 'WARNING':
      return 'warning'
    case 'CRITICAL':
      return 'danger'
    default:
      return 'success'
  }
}

// 获取异常状态标签类型
const getExceptionStatusTagType = (status: string) => {
  switch (status) {
    case 'PENDING':
      return 'warning'
    case 'INVESTIGATING':
      return 'primary'
    case 'RESOLVED':
      return 'success'
    case 'DISMISSED':
      return 'info'
    default:
      return ''
  }
}

// 获取统计数据
const fetchStatistics = async () => {
  try {
    // 模拟统计数据
    Object.assign(stats, {
      totalEmployees: 1250,
      totalAmount: 8750000,
      averageAmount: 7000,
      budgetUtilization: 85
    })
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 获取预算监控数据
const fetchBudgetMonitoring = async () => {
  try {
    const result = await salaryApi.getBudgetMonitoring({})
    budgetMonitoring.value = result
  } catch (error) {
    console.error('获取预算监控数据失败:', error)
    // 模拟数据
    budgetMonitoring.value = [
      {
        organizationId: 1,
        organizationName: '计算机学院',
        budgetPeriod: '2025-06',
        budgetAmount: 2000000,
        actualAmount: 1800000,
        utilizationRate: 90,
        remainingBudget: 200000,
        alertLevel: 'WARNING',
        alertLevelName: '警告'
      },
      {
        organizationId: 2,
        organizationName: '机械学院',
        budgetPeriod: '2025-06',
        budgetAmount: 1500000,
        actualAmount: 1200000,
        utilizationRate: 80,
        remainingBudget: 300000,
        alertLevel: 'NORMAL',
        alertLevelName: '正常'
      }
    ]
  }
}

// 获取薪酬异常数据
const fetchSalaryExceptions = async () => {
  try {
    const result = await salaryApi.querySalaryExceptions({
      page: 0,
      size: 5,
      status: 'PENDING'
    })
    salaryExceptions.value = result.content
  } catch (error) {
    console.error('获取薪酬异常数据失败:', error)
    // 模拟数据
    salaryExceptions.value = [
      {
        id: 1,
        employeeId: 1001,
        employeeName: '张三',
        salaryMonth: '2025-06',
        exceptionType: 'CALCULATION_ERROR',
        exceptionTypeName: '计算错误',
        description: '绩效工资计算异常，实际金额与预期不符',
        detectedTime: '2025-06-19 10:30:00',
        status: 'PENDING',
        statusName: '待处理'
      }
    ]
  }
}

// 图表初始化
const initCharts = () => {
  nextTick(() => {
    initSalaryTrendChart()
    initSalaryStructureChart()
    initDepartmentComparisonChart()
    initBudgetMonitoringChart()
  })
}

// 薪酬趋势图表
const initSalaryTrendChart = () => {
  if (!salaryTrendChartRef.value) return

  const chart = echarts.init(salaryTrendChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' }
    },
    legend: {
      data: ['平均薪酬', '总薪酬']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: [
      {
        type: 'value',
        name: '平均薪酬(元)',
        position: 'left'
      },
      {
        type: 'value',
        name: '总薪酬(万元)',
        position: 'right'
      }
    ],
    series: [
      {
        name: '平均薪酬',
        type: 'line',
        data: [6800, 6900, 7100, 7000, 7200, 7300],
        smooth: true,
        itemStyle: { color: '#667eea' }
      },
      {
        name: '总薪酬',
        type: 'bar',
        yAxisIndex: 1,
        data: [850, 870, 890, 875, 900, 920],
        itemStyle: { color: '#43e97b' }
      }
    ]
  }
  chart.setOption(option)
}

// 薪酬结构图表
const initSalaryStructureChart = () => {
  if (!salaryStructureChartRef.value) return

  const chart = echarts.init(salaryStructureChartRef.value)
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '薪酬结构',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        data: [
          { value: 40, name: '基本工资', itemStyle: { color: '#667eea' } },
          { value: 25, name: '岗位工资', itemStyle: { color: '#43e97b' } },
          { value: 20, name: '绩效工资', itemStyle: { color: '#feca57' } },
          { value: 15, name: '津贴补贴', itemStyle: { color: '#ff6b6b' } }
        ]
      }
    ]
  }
  chart.setOption(option)
}

// 部门对比图表
const initDepartmentComparisonChart = () => {
  if (!departmentComparisonChartRef.value) return

  const chart = echarts.init(departmentComparisonChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: ['计算机学院', '机械学院', '电气学院', '管理学院', '外语学院']
    },
    series: [
      {
        name: '平均薪酬',
        type: 'bar',
        data: [7500, 7200, 6800, 6500, 6200],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#667eea' },
            { offset: 1, color: '#764ba2' }
          ])
        }
      }
    ]
  }
  chart.setOption(option)
}

// 预算监控图表
const initBudgetMonitoringChart = () => {
  if (!budgetMonitoringChartRef.value) return

  const chart = echarts.init(budgetMonitoringChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value',
      max: 100
    },
    series: [
      {
        name: '预算执行率',
        type: 'line',
        data: [75, 78, 82, 85, 88, 85],
        smooth: true,
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(255, 107, 107, 0.3)' },
            { offset: 1, color: 'rgba(255, 107, 107, 0.1)' }
          ])
        },
        itemStyle: { color: '#ff6b6b' }
      }
    ]
  }
  chart.setOption(option)
}

// 全屏图表
const initFullscreenChart = () => {
  if (!fullscreenChartRef.value) return

  const chart = echarts.init(fullscreenChartRef.value)
  // 使用更详细的数据
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' }
    },
    legend: {
      data: ['平均薪酬', '总薪酬', '预算执行率']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['2024-12', '2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
    },
    yAxis: [
      {
        type: 'value',
        name: '薪酬(元)',
        position: 'left'
      },
      {
        type: 'value',
        name: '执行率(%)',
        position: 'right',
        max: 100
      }
    ],
    series: [
      {
        name: '平均薪酬',
        type: 'line',
        data: [6500, 6800, 6900, 7100, 7000, 7200, 7300],
        smooth: true,
        itemStyle: { color: '#667eea' }
      },
      {
        name: '总薪酬',
        type: 'bar',
        data: [8125000, 8500000, 8625000, 8875000, 8750000, 9000000, 9125000],
        itemStyle: { color: '#43e97b' }
      },
      {
        name: '预算执行率',
        type: 'line',
        yAxisIndex: 1,
        data: [72, 75, 78, 82, 85, 88, 85],
        smooth: true,
        itemStyle: { color: '#ff6b6b' }
      }
    ]
  }
  chart.setOption(option)
}

// 更新图表
const updateCharts = () => {
  initCharts()
}

// 初始化
onMounted(() => {
  fetchStatistics()
  fetchBudgetMonitoring()
  fetchSalaryExceptions()
  initCharts()
})
</script>

<style scoped>
/* 现代化薪酬管理样式 */
.modern-salary-management {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 24px;
}

.mobile-layout {
  padding: 16px;
}

/* 页面头部 */
.page-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title h1 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  margin: 0;
  color: #7f8c8d;
  font-size: 16px;
  font-weight: 400;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.mobile-actions {
  display: flex;
  gap: 8px;
}

/* 安全提醒 */
.security-notice {
  margin-bottom: 24px;
}

/* 功能导航 */
.function-navigation {
  margin-bottom: 24px;
}

.nav-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.mobile-grid {
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 16px;
}

.modern-nav-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.modern-nav-card.clickable {
  cursor: pointer;
}

.modern-nav-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.nav-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.nav-icon {
  width: 64px;
  height: 64px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.nav-badge {
  background: #ff4757;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
}

.nav-content h3 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.nav-description {
  margin: 0 0 12px 0;
  color: #7f8c8d;
  font-size: 14px;
  line-height: 1.5;
}

.nav-stats {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.stats-value {
  font-size: 20px;
  font-weight: 700;
  color: #2c3e50;
}

.stats-label {
  font-size: 12px;
  color: #95a5a6;
}

.nav-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

.nav-arrow {
  color: #bdc3c7;
  transition: all 0.3s ease;
}

.modern-nav-card:hover .nav-arrow {
  color: #3498db;
  transform: translateX(4px);
}

/* 统计概览 */
.stats-overview {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.mobile-grid {
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 16px;
}

.modern-stats-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.modern-stats-card.clickable {
  cursor: pointer;
}

.modern-stats-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.stats-icon {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.stats-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
}

.trend-up {
  color: #27ae60;
}

.trend-down {
  color: #e74c3c;
}

.trend-value {
  color: inherit;
}

.stats-content {
  margin-bottom: 16px;
}

.stats-value {
  display: flex;
  align-items: baseline;
  gap: 4px;
  margin-bottom: 8px;
}

.stats-number {
  font-size: 36px;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
}

.stats-unit {
  font-size: 16px;
  color: #7f8c8d;
  font-weight: 500;
}

.stats-title {
  font-size: 16px;
  font-weight: 600;
  color: #34495e;
  margin-bottom: 4px;
}

.stats-subtitle {
  font-size: 12px;
  color: #95a5a6;
}

.stats-chart {
  margin-top: 16px;
}

.mini-chart {
  display: flex;
  align-items: end;
  gap: 2px;
  height: 40px;
}

.chart-bar {
  flex: 1;
  border-radius: 2px;
  min-height: 4px;
  transition: all 0.3s ease;
}

.modern-stats-card:hover .chart-bar {
  transform: scaleY(1.1);
}

/* 图表区域 */
.charts-section {
  margin-bottom: 24px;
}

.chart-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  height: 100%;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.chart-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
}

.chart-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.chart-content {
  position: relative;
}

/* 最近操作记录 */
.recent-operations {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.operations-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.operations-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.operations-timeline {
  position: relative;
}

.timeline-item {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  position: relative;
}

.timeline-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 15px;
  top: 32px;
  bottom: -20px;
  width: 2px;
  background: #e1e8ed;
}

.timeline-dot {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  flex-shrink: 0;
  z-index: 1;
  position: relative;
}

.dot-import {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.dot-export {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.dot-analysis {
  background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
}

.dot-alert {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.timeline-content {
  flex: 1;
  background: #f8fafc;
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #e2e8f0;
}

.operation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.operation-title {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.operation-time {
  font-size: 12px;
  color: #94a3b8;
}

.operation-desc {
  font-size: 13px;
  color: #64748b;
  margin-bottom: 12px;
  line-height: 1.5;
}

.operation-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.operation-user {
  font-size: 12px;
  color: #94a3b8;
}

/* 移动端菜单 */
.mobile-menu-content {
  padding: 20px 0;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  cursor: pointer;
  transition: background 0.3s ease;
  border-radius: 8px;
  margin: 0 20px 8px 20px;
}

.menu-item:hover {
  background: #f5f7fa;
}

.menu-item span {
  font-size: 16px;
  color: #2c3e50;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modern-salary-management {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .header-title {
    text-align: center;
  }

  .header-title h1 {
    font-size: 24px;
  }

  .nav-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .chart-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .chart-controls {
    justify-content: center;
  }

  .operations-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .timeline-item {
    flex-direction: column;
    gap: 8px;
  }

  .timeline-dot {
    align-self: flex-start;
  }

  .timeline-item::after {
    display: none;
  }
}

@media (max-width: 480px) {
  .modern-salary-management {
    padding: 12px;
  }

  .page-header {
    padding: 16px;
  }

  .header-title h1 {
    font-size: 20px;
  }

  .subtitle {
    font-size: 14px;
  }

  .modern-nav-card,
  .modern-stats-card,
  .chart-container,
  .recent-operations {
    padding: 16px;
  }

  .stats-number {
    font-size: 28px;
  }

  .nav-icon {
    width: 48px;
    height: 48px;
  }

  .stats-icon {
    width: 48px;
    height: 48px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .modern-salary-management {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  }

  .page-header,
  .modern-nav-card,
  .modern-stats-card,
  .chart-container,
  .recent-operations {
    background: rgba(31, 31, 31, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .header-title h1,
  .stats-number,
  .stats-title,
  .nav-content h3,
  .chart-header h3,
  .operations-header h3,
  .operation-title {
    color: #ffffff;
  }

  .subtitle,
  .stats-subtitle,
  .nav-description,
  .operation-desc,
  .operation-time,
  .operation-user {
    color: #a0a0a0;
  }

  .timeline-content {
    background: rgba(45, 45, 45, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .chart-header,
  .operations-header {
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }

  .timeline-item::after {
    background: rgba(255, 255, 255, 0.2);
  }

  .menu-item span {
    color: #ffffff;
  }

  .menu-item:hover {
    background: rgba(255, 255, 255, 0.1);
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modern-nav-card,
.modern-stats-card {
  animation: fadeInUp 0.6s ease-out;
}

.modern-nav-card:nth-child(1) { animation-delay: 0.1s; }
.modern-nav-card:nth-child(2) { animation-delay: 0.2s; }
.modern-nav-card:nth-child(3) { animation-delay: 0.3s; }
.modern-nav-card:nth-child(4) { animation-delay: 0.4s; }

.modern-stats-card:nth-child(1) { animation-delay: 0.1s; }
.modern-stats-card:nth-child(2) { animation-delay: 0.2s; }
.modern-stats-card:nth-child(3) { animation-delay: 0.3s; }
.modern-stats-card:nth-child(4) { animation-delay: 0.4s; }

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}
</style>
