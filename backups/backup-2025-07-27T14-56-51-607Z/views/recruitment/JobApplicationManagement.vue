<template>
  <div class="job-application-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon><Document /></el-icon>
        申请流程管理
      </h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleCreateFlow">
          <el-icon><Plus /></el-icon>
          创建流程
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出报告
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="申请编号">
          <el-input
            v-model="searchForm.applicationId"
            placeholder="请输入申请编号"
            clearable
            style="width: 200px"
            />
        </el-form-item>
        <el-form-item label="申请人">
          <el-input
            v-model="searchForm.applicantName"
            placeholder="请输入申请人姓名"
            clearable
            style="width: 150px"
            />
        </el-form-item>
        <el-form-item label="流程状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="待提交" value="draft"  />
            <el-option label="审核中" value="reviewing"  />
            <el-option label="面试中" value="interviewing"  />
            <el-option label="已完成" value="completed"  />
            <el-option label="已拒绝" value="rejected"  />
          </el-select>
        </el-form-item>
        <el-form-item label="申请岗位">
          <el-select
            v-model="searchForm.position"
            placeholder="请选择岗位"
            clearable
            style="width: 200px"
          >
            <el-option label="软件工程师" value="software"  />
            <el-option label="产品经理" value="product"  />
            <el-option label="UI设计师" value="ui"  />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 流程统计 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.total }}</div>
              <div class="stat-label">总申请数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon reviewing">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.reviewing }}</div>
              <div class="stat-label">审核中</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon interviewing">
              <el-icon><ChatDotRound /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.interviewing }}</div>
              <div class="stat-label">面试中</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon completed">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.completed }}</div>
              <div class="stat-label">已完成</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 申请流程列表 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="applicationId" label="申请编号" width="150"  />
        <el-table-column prop="applicantName" label="申请人" width="100"  />
        <el-table-column prop="position" label="申请岗位" min-width="150"  />
        <el-table-column prop="department" label="所属部门" width="120"  />
        <el-table-column prop="status" label="流程状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="currentStep" label="当前步骤" width="150"  />
        <el-table-column prop="progress" label="进度" width="120">
          <template #default="{ row }">
            <el-progress
              :percentage="row.progress"
              :color="getProgressColor(row.progress)"
              :stroke-width="8"
             />
          </template>
        </el-table-column>
        <el-table-column prop="submitTime" label="提交时间" width="120"  />
        <el-table-column prop="updateTime" label="更新时间" width="120"  />
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handleViewFlow(row)">
              查看流程
            </el-button>
            <el-button type="text" size="small" @click="handleViewTimeline(row)">
              流程时间线
            </el-button>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
            <el-dropdown @command="(command: unknown) => handleCommand(command, row)">
              <el-button type="text" size="small">
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="approve" v-if="row.status === 'reviewing'">
                    审核通过
                  </el-dropdown-item>
                  <el-dropdown-item command="interview" v-if="row.status === 'reviewing'">
                    安排面试
                  </el-dropdown-item>
                  <el-dropdown-item command="reject" divided>
                    拒绝申请
                  </el-dropdown-item>
                  <el-dropdown-item command="withdraw" v-if="row.status !== 'completed'">
                    撤回申请
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 流程详情对话框 -->
    <el-dialog
      v-model="flowDialogVisible"
      title="申请流程详情"
      width="900px"
    >
      <div class="flow-content" v-if="currentFlow">
        <el-descriptions :column="2" border class="flow-info">
          <el-descriptions-item label="申请编号">{{ currentFlow.applicationId }}</el-descriptions-item>
          <el-descriptions-item label="申请人">{{ currentFlow.applicantName }}</el-descriptions-item>
          <el-descriptions-item label="申请岗位">{{ currentFlow.position }}</el-descriptions-item>
          <el-descriptions-item label="所属部门">{{ currentFlow.department }}</el-descriptions-item>
          <el-descriptions-item label="流程状态">
            <el-tag :type="getStatusType(currentFlow.status)">
              {{ getStatusLabel(currentFlow.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="当前步骤">{{ currentFlow.currentStep }}</el-descriptions-item>
          <el-descriptions-item label="提交时间">{{ currentFlow.submitTime }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ currentFlow.updateTime }}</el-descriptions-item>
        </el-descriptions>

        <div class="flow-steps">
          <h4>流程步骤</h4>
          <el-steps :active="currentFlow.activeStep" finish-status="success">
            <el-step
              v-for="(step, index) in currentFlow.steps"
              :key="index"
              :title="step.title"
              :description="step.description"
              :status="step.status"
             />
          </el-steps>
        </div>

        <div class="flow-history">
          <h4>处理历史</h4>
          <el-timeline>
            <el-timeline-item
              v-for="(item, index) in currentFlow.history"
              :key="index"
              :timestamp="item.timestamp"
              :type="item.type"
            >
              <div class="history-item">
                <div class="history-title">{{ item.title }}</div>
                <div class="history-content">{{ item.content }}</div>
                <div class="history-operator">操作人：{{ item.operator }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
      <template #footer>
        <el-button @click="flowDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 时间线对话框 -->
    <el-dialog
      v-model="timelineDialogVisible"
      title="流程时间线"
      width="700px"
    >
      <div class="timeline-content" v-if="currentTimeline">
        <el-timeline>
          <el-timeline-item
            v-for="(item, index) in currentTimeline"
            :key="index"
            :timestamp="item.timestamp"
            :type="item.type"
            :icon="item.icon"
          >
            <el-card class="timeline-card">
              <div class="timeline-title">{{ item.title }}</div>
              <div class="timeline-description">{{ item.description }}</div>
              <div class="timeline-operator" v-if="item.operator">
                操作人：{{ item.operator }}
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
      <template #footer>
        <el-button @click="timelineDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'JobApplicationManagement'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Document,
  Plus,
  Download,
  Search,
  Refresh,
  Clock,
  ChatDotRound,
  CircleCheck,
  ArrowDown
} from '@element-plus/icons-vue'
import { candidateApi } from '@/api/recruitment/candidate'

// 响应式数据
const loading = ref(false)
const flowDialogVisible = ref(false)
const timelineDialogVisible = ref(false)
const currentFlow = ref<unknown>(null)
const currentTimeline = ref<unknown>(null)

const searchForm = reactive({
  applicationId: '',
  applicantName: '',
  status: '',
  position: ''
})

const stats = reactive({
  total: 156,
  reviewing: 45,
  interviewing: 28,
  completed: 83
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

const tableData = ref([
  {
    id: '1',
    applicationId: 'APP2024001',
    applicantName: '张三',
    position: '软件工程师',
    department: '计算机学院',
    status: 'reviewing',
    currentStep: '简历筛选',
    progress: 25,
    submitTime: '2024-06-15',
    updateTime: '2024-06-16',
    activeStep: 1,
    steps: [
      { title: '提交申请', description: '申请已提交', status: 'finish' },
      { title: '简历筛选', description: '正在进行简历筛选', status: 'process' },
      { title: '初试面试', description: '等待安排初试', status: 'wait' },
      { title: '复试面试', description: '等待安排复试', status: 'wait' },
      { title: '录用决定', description: '等待最终决定', status: 'wait' }
    ],
    history: [
      {
        timestamp: '2024-06-15 10:00',
        title: '申请提交',
        content: '申请人提交了软件工程师岗位申请',
        operator: '张三',
        type: 'primary'
      },
      {
        timestamp: '2024-06-16 14:30',
        title: '简历筛选开始',
        content: 'HR开始进行简历筛选',
        operator: '李四',
        type: 'success'
      }
    ]
  }
])

// 方法
const handleSearch = () => {
  console.log('搜索:', searchForm)
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    applicationId: '',
    applicantName: '',
    status: '',
    position: ''
  })
  loadData()
}

const handleCreateFlow = () => {
  ElMessage.info('创建流程功能开发中')
}

const handleExport = () => {
  ElMessage.info('导出报告功能开发中')
}

   
const handleViewFlow = (row: unknown) => {
  currentFlow.value = row
  flowDialogVisible.value = true
}

   
const handleViewTimeline = (row: unknown) => {
  currentTimeline.value = [
    {
      timestamp: '2024-06-15 10:00',
      title: '申请提交',
      description: '申请人提交了软件工程师岗位申请',
      operator: '张三',
      type: 'primary',
      icon: 'Document'
    },
    {
      timestamp: '2024-06-16 14:30',
      title: '简历筛选',
      description: 'HR开始进行简历筛选',
      operator: '李四',
      type: 'success',
      icon: 'View'
    },
    {
      timestamp: '2024-06-18 09:00',
      title: '初试安排',
      description: '安排初试面试，时间：2024-06-20 14:00',
      operator: '王五',
      type: 'warning',
      icon: 'ChatDotRound'
    }
  ]
  timelineDialogVisible.value = true
}

   
const handleCommand = (command: string, row: unknown) => {
  switch (command) {
    case 'approve':
      ElMessage.success(`申请 ${row.applicationId} 审核通过`)
      break
    case 'interview':
      ElMessage.info(`为申请 ${row.applicationId} 安排面试`)
      break
    case 'reject':
      ElMessageBox.confirm(`确定要拒绝申请 ${row.applicationId} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ElMessage.success('申请已拒绝')
      })
      break
    case 'withdraw':
      ElMessageBox.confirm(`确定要撤回申请 ${row.applicationId} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ElMessage.success('申请已撤回')
      })
      break
  }
}

const loadData = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params = {
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      keyword: searchForm.applicationId || searchForm.applicantName,
      status: searchForm.status,
      // 职位筛选可能需要通过标签或其他字段实现
      tags: searchForm.position ? [searchForm.position] : undefined
    }
    
    // 过滤掉undefined的参数
    const filteredParams = Object.fromEntries(
      Object.entries(params).filter(([_, v]) => v !== undefined && v !== '')
    )
    
    // 获取候选人列表（作为申请记录）
    const response = await candidateApi.getList(filteredParams)
    
    // 转换数据格式
    tableData.value = response.list.map((item, index) => ({
      id: item.id,
      applicationId: `APP2024${String(item.id).padStart(3, '0')}`,
      applicantName: item.personalInfo.name,
      position: item.jobPostId ? '软件工程师' : '教师', // 需要根据实际职位信息
      department: item.departmentId ? '计算机学院' : '人文学院',
      status: mapCandidateStatus(item.status),
      currentStep: mapCurrentStep(item.stage),
      progress: calculateProgress(item.stage),
      submitTime: item.createdAt || '2024-06-15',
      updateTime: item.updatedAt || '2024-06-16',
      // 流程步骤信息
      activeStep: getActiveStep(item.stage),
      steps: [
        { title: '提交申请', description: '申请已提交', status: 'finish' },
        { title: '简历筛选', description: getStepDescription(item.stage, 'screening'), status: getStepStatus(item.stage, 'screening') },
        { title: '初试面试', description: getStepDescription(item.stage, 'firstInterview'), status: getStepStatus(item.stage, 'firstInterview') },
        { title: '复试面试', description: getStepDescription(item.stage, 'secondInterview'), status: getStepStatus(item.stage, 'secondInterview') },
        { title: '录用决定', description: getStepDescription(item.stage, 'offer'), status: getStepStatus(item.stage, 'offer') }
      ],
      // 历史记录
      history: [
        {
          timestamp: item.createdAt || '2024-06-15 10:00',
          title: '申请提交',
          content: `申请人提交了${item.jobPostId ? '软件工程师' : '教师'}岗位申请`,
          operator: item.personalInfo.name,
          type: 'primary'
        }
      ]
    }))
    
    pagination.total = response.total
    
    // 更新统计数据
    stats.total = response.total
    stats.reviewing = response.list.filter(item => item.stage === 'resume_screening').length
    stats.interviewing = response.list.filter(item => 
      item.stage === 'first_interview' || item.stage === 'second_interview'
    ).length
    stats.completed = response.list.filter(item => 
      item.status === 'hired' || item.status === 'rejected'
    ).length
  } catch (__error) {
    console.error('加载申请记录失败:', error)
    ElMessage.error('加载申请记录失败')
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 辅助函数：映射候选人状态到申请状态
const mapCandidateStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    'new': 'draft',
    'screening': 'reviewing',
    'interview': 'interviewing',
    'hired': 'completed',
    'rejected': 'rejected',
    'withdrawn': 'rejected'
  }
  return statusMap[status] || 'draft'
}

// 辅助函数：映射当前步骤
const mapCurrentStep = (stage: string): string => {
  const stageMap: Record<string, string> = {
    'resume_screening': '简历筛选',
    'first_interview': '初试面试',
    'second_interview': '复试面试',
    'offer': '录用决定',
    'onboarding': '入职办理'
  }
  return stageMap[stage] || '提交申请'
}

// 辅助函数：计算进度
const calculateProgress = (stage: string): number => {
  const progressMap: Record<string, number> = {
    'new': 0,
    'resume_screening': 25,
    'first_interview': 50,
    'second_interview': 75,
    'offer': 90,
    'onboarding': 100
  }
  return progressMap[stage] || 0
}

// 辅助函数：获取活动步骤
const getActiveStep = (stage: string): number => {
  const stepMap: Record<string, number> = {
    'new': 0,
    'resume_screening': 1,
    'first_interview': 2,
    'second_interview': 3,
    'offer': 4,
    'onboarding': 4
  }
  return stepMap[stage] || 0
}

// 辅助函数：获取步骤描述
const getStepDescription = (currentStage: string, stepStage: string): string => {
  const descriptions: Record<string, string> = {
    'screening': '正在进行简历筛选',
    'firstInterview': '等待安排初试',
    'secondInterview': '等待安排复试',
    'offer': '等待最终决定'
  }
  return descriptions[stepStage] || '等待处理'
}

// 辅助函数：获取步骤状态
const getStepStatus = (currentStage: string, stepStage: string): string => {
  const stageOrder = ['new', 'resume_screening', 'first_interview', 'second_interview', 'offer']
  const stepStageMap: Record<string, string> = {
    'screening': 'resume_screening',
    'firstInterview': 'first_interview',
    'secondInterview': 'second_interview',
    'offer': 'offer'
  }
  
  const currentIndex = stageOrder.indexOf(currentStage)
  const stepIndex = stageOrder.indexOf(stepStageMap[stepStage])
  
  if (currentIndex > stepIndex) return 'finish'
  if (currentIndex === stepIndex) return 'process'
  return 'wait'
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadData()
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    draft: 'info',
    reviewing: 'warning',
    interviewing: 'primary',
    completed: 'success',
    rejected: 'danger'
  }
  return types[status] || ''
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    draft: '待提交',
    reviewing: '审核中',
    interviewing: '面试中',
    completed: '已完成',
    rejected: '已拒绝'
  }
  return labels[status] || status
}

const getProgressColor = (progress: number) => {
  if (progress < 30) return '#f56c6c'
  if (progress < 70) return '#e6a23c'
  return '#67c23a'
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.job-application-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.reviewing {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.interviewing {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.flow-content {
  max-height: 600px;
  overflow-y: auto;
}

.flow-info {
  margin-bottom: 20px;
}

.flow-steps {
  margin-bottom: 20px;
}

.flow-steps h4 {
  margin-bottom: 16px;
  color: #303133;
}

.flow-history h4 {
  margin-bottom: 16px;
  color: #303133;
}

.history-item {
  padding: 8px 0;
}

.history-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.history-content {
  color: #606266;
  margin-bottom: 4px;
}

.history-operator {
  font-size: 12px;
  color: #909399;
}

.timeline-content {
  max-height: 500px;
  overflow-y: auto;
}

.timeline-card {
  margin-bottom: 8px;
}

.timeline-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.timeline-description {
  color: #606266;
  margin-bottom: 8px;
}

.timeline-operator {
  font-size: 12px;
  color: #909399;
}
</style>
