<template>
  <div class="recruitment-progress">
    <!-- 报表头部 -->
    <el-card class="header-card">
      <div class="report-header">
        <div class="title-section">
          <h2>招聘进度报表</h2>
          <p>统计周期：{{ dateRange[0] }} 至 {{ dateRange[1] }}</p>
        </div>
        <div class="action-section">
          <el-button @click="refreshData" :icon="Refresh">刷新</el-button>
          <el-button type="primary" @click="exportReport" :icon="Download">导出报表</el-button>
          <el-button type="success" @click="printReport" :icon="Printer">打印</el-button>
        </div>
      </div>
    </el-card>

    <!-- 整体进度概览 -->
    <el-card class="overview-card">
      <template #header>
        <span>整体进度概览</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="progress-item">
            <div class="progress-circle">
              <el-progress type="circle" :percentage="overallProgress.completion" :width="120">
                <template #default="{ percentage }">
                  <span class="percentage-value">{{ percentage }}%</span>
                  <span class="percentage-label">完成率</span>
                </template>
              </el-progress>
            </div>
            <div class="progress-info">
              <p>需求总数：{{ overallProgress.total }}人</p>
              <p>已完成：{{ overallProgress.completed }}人</p>
              <p>进行中：{{ overallProgress.ongoing }}人</p>
            </div>
          </div>
        </el-col>
        <el-col :span="18">
          <div id="progressChart" style="height: 200px"></div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 部门招聘进度 -->
    <el-card class="department-card">
      <template #header>
        <span>各部门招聘进度</span>
        <el-checkbox v-model="showTimeline" style="float: right">显示时间轴</el-checkbox>
      </template>
      
      <el-table :data="departmentData" stripe>
        <el-table-column prop="department" label="部门" fixed width="120"  />
        <el-table-column prop="totalDemand" label="需求人数" align="center" width="100"  />
        <el-table-column prop="published" label="已发布" align="center" width="80">
          <template #default="{ row }">
            <span class="number-tag">{{ row.published }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="interviewing" label="面试中" align="center" width="80">
          <template #default="{ row }">
            <span class="number-tag warning">{{ row.interviewing }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="offered" label="已发Offer" align="center" width="90">
          <template #default="{ row }">
            <span class="number-tag primary">{{ row.offered }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="joined" label="已入职" align="center" width="80">
          <template #default="{ row }">
            <span class="number-tag success">{{ row.joined }}</span>
          </template>
        </el-table-column>
        <el-table-column label="完成进度" width="200" align="center">
          <template #default="{ row }">
            <el-progress 
              :percentage="row.progress" 
              :color="getProgressColor(row.progress)"
              :stroke-width="10"
             />
          </template>
        </el-table-column>
        <el-table-column prop="expectedComplete" label="预计完成" width="110" align="center"  />
        <el-table-column label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="100">
          <template #default="{ row }">
            <el-button link type="primary" @click="viewDepartmentDetail(row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 职位招聘进度甘特图 -->
    <el-card class="gantt-card" v-if="showTimeline">
      <template #header>
        <span>招聘进度时间轴</span>
      </template>
      <div id="ganttChart" style="height: 400px"></div>
    </el-card>

    <!-- 关键职位跟踪 -->
    <el-card class="key-positions-card">
      <template #header>
        <span>关键职位跟踪</span>
        <el-tag type="danger" size="small" style="margin-left: 10px">重点关注</el-tag>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6" v-for="position in keyPositions" :key="position.id">
          <div class="position-card" :class="{ urgent: position.urgent }">
            <div class="position-header">
              <h4>{{ position.title }}</h4>
              <el-tag :type="position.urgent ? 'danger' : 'primary'" size="small">
                {{ position.urgency }}
              </el-tag>
            </div>
            <div class="position-content">
              <div class="info-item">
                <span class="label">部门：</span>
                <span class="value">{{ position.department }}</span>
              </div>
              <div class="info-item">
                <span class="label">发布天数：</span>
                <span class="value">{{ position.publishDays }}天</span>
              </div>
              <div class="info-item">
                <span class="label">简历数：</span>
                <span class="value">{{ position.resumeCount }}</span>
              </div>
              <div class="info-item">
                <span class="label">面试中：</span>
                <span class="value">{{ position.interviewCount }}</span>
              </div>
            </div>
            <div class="position-progress">
              <el-progress 
                :percentage="position.progress" 
                :color="getProgressColor(position.progress)"
               />
            </div>
            <div class="position-footer">
              <span class="deadline">截止：{{ position.deadline }}</span>
              <el-button link type="primary" size="small" @click="viewPositionDetail(position)">
                查看
              </el-button>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 风险预警 -->
    <el-card class="risk-card">
      <template #header>
        <span>风险预警</span>
        <el-badge :value="risks.length" class="item" type="danger"  />
      </template>
      
      <el-alert
        v-for="risk in risks"
        :key="risk.id"
        :title="risk.title"
        :type="risk.level"
        :description="risk.description"
        show-icon
        :closable="false"
        style="margin-bottom: 10px"
      >
        <template #default>
          <div class="risk-content">
            <p>{{ risk.description }}</p>
            <div class="risk-action">
              <span>建议措施：{{ risk.suggestion }}</span>
              <el-button link type="primary" size="small" @click="handleRisk(risk)">
                立即处理
              </el-button>
            </div>
          </div>
        </template>
      </el-alert>
    </el-card>

    <!-- 进度报表说明 -->
    <el-card class="notes-card">
      <template #header>
        <span>报表说明</span>
      </template>
      <el-descriptions :column="2" border size="small">
        <el-descriptions-item label="生成时间">{{ reportTime }}</el-descriptions-item>
        <el-descriptions-item label="数据范围">{{ dateRange[0] }} 至 {{ dateRange[1] }}</el-descriptions-item>
        <el-descriptions-item label="包含部门">{{ includedDepartments }}</el-descriptions-item>
        <el-descriptions-item label="职位总数">{{ totalPositions }}个</el-descriptions-item>
        <el-descriptions-item label="更新频率">每日更新</el-descriptions-item>
        <el-descriptions-item label="负责人">人力资源部</el-descriptions-item>
      </el-descriptions>
    </el-card>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Download, Printer } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 数据状态
const dateRange = ref(['2025-01-01', '2025-01-31'])
const showTimeline = ref(true)
const reportTime = ref(new Date().toLocaleString())
const includedDepartments = ref('全部')
const totalPositions = ref(45)

// 整体进度
const overallProgress = reactive({
  total: 120,
  completed: 64,
  ongoing: 35,
  completion: 53
})

// 部门数据
const departmentData = ref([
  {
    department: '技术部',
    totalDemand: 50,
    published: 45,
    interviewing: 15,
    offered: 8,
    joined: 25,
    progress: 50,
    expectedComplete: '2025-02-15',
    status: '正常'
  },
  {
    department: '产品部',
    totalDemand: 20,
    published: 18,
    interviewing: 5,
    offered: 3,
    joined: 10,
    progress: 50,
    expectedComplete: '2025-02-20',
    status: '正常'
  },
  {
    department: '市场部',
    totalDemand: 15,
    published: 15,
    interviewing: 3,
    offered: 2,
    joined: 8,
    progress: 53,
    expectedComplete: '2025-02-10',
    status: '正常'
  },
  {
    department: '运营部',
    totalDemand: 25,
    published: 20,
    interviewing: 8,
    offered: 2,
    joined: 10,
    progress: 40,
    expectedComplete: '2025-03-01',
    status: '延迟风险'
  },
  {
    department: '财务部',
    totalDemand: 10,
    published: 10,
    interviewing: 4,
    offered: 3,
    joined: 5,
    progress: 50,
    expectedComplete: '2025-02-05',
    status: '正常'
  }
])

// 关键职位
const keyPositions = ref([
  {
    id: '1',
    title: '技术总监',
    department: '技术部',
    urgency: '紧急',
    urgent: true,
    publishDays: 15,
    resumeCount: 12,
    interviewCount: 3,
    progress: 30,
    deadline: '2025-02-01'
  },
  {
    id: '2',
    title: '高级算法工程师',
    department: '技术部',
    urgency: '紧急',
    urgent: true,
    publishDays: 20,
    resumeCount: 8,
    interviewCount: 2,
    progress: 25,
    deadline: '2025-02-10'
  },
  {
    id: '3',
    title: '产品总监',
    department: '产品部',
    urgency: '正常',
    urgent: false,
    publishDays: 10,
    resumeCount: 15,
    interviewCount: 5,
    progress: 60,
    deadline: '2025-02-20'
  },
  {
    id: '4',
    title: '市场经理',
    department: '市场部',
    urgency: '正常',
    urgent: false,
    publishDays: 8,
    resumeCount: 20,
    interviewCount: 6,
    progress: 70,
    deadline: '2025-02-15'
  }
])

// 风险预警
const risks = ref([
  {
    id: '1',
    level: 'error',
    title: '技术总监职位招聘进度严重滞后',
    description: '该职位已发布15天，仅收到12份简历，远低于预期。当前仅有3人进入面试阶段。',
    suggestion: '扩大招聘渠道，考虑猎头合作，提高薪资竞争力'
  },
  {
    id: '2',
    level: 'warning',
    title: '运营部整体招聘进度低于预期',
    description: '运营部招聘完成率仅40%，可能影响Q1业务目标达成。',
    suggestion: '增加招聘资源投入，优化招聘流程，加快面试节奏'
  },
  {
    id: '3',
    level: 'warning',
    title: '多个岗位Offer接受率偏低',
    description: '近期发出的15个Offer中，仅有8个被接受，接受率53%。',
    suggestion: '分析拒绝原因，优化薪资福利方案，改进候选人体验'
  }
])

// 获取进度颜色
const getProgressColor = (percentage: number) => {
  if (percentage >= 80) return '#67c23a'
  if (percentage >= 60) return '#409eff'
  if (percentage >= 40) return '#e6a23c'
  return '#f56c6c'
}

// 获取状态类型
const getStatusType = (status: string) => {
  const map: Record<string, string> = {
    '正常': 'success',
    '延迟风险': 'warning',
    '严重延迟': 'danger'
  }
  return map[status] || 'info'
}

// 初始化进度图表
const initProgressChart = () => {
  const chartDom = document.getElementById('progressChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['需求', '发布', '面试中', '已发Offer', '已入职'],
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      top: '10%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月第1周', '1月第2周', '1月第3周', '1月第4周']
    },
    yAxis: {
      type: 'value',
      name: 'HrHr人数'
    },
    series: [
      {
        name: '需求',
        type: 'line',
        data: [30, 50, 80, 120],
        itemStyle: { color: '#909399' }
      },
      {
        name: '发布',
        type: 'line',
        data: [25, 45, 75, 108],
        itemStyle: { color: '#409eff' }
      },
      {
        name: '面试中',
        type: 'line',
        data: [5, 15, 25, 35],
        itemStyle: { color: '#e6a23c' }
      },
      {
        name: '已发Offer',
        type: 'line',
        data: [0, 5, 12, 18],
        itemStyle: { color: '#409eff' }
      },
      {
        name: '已入职',
        type: 'line',
        data: [0, 3, 20, 64],
        itemStyle: { color: '#67c23a' }
      }
    ]
  }
  
  myChart.setOption(option)
}

// 初始化甘特图
const initGanttChart = () => {
  const chartDom = document.getElementById('ganttChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  
  // 甘特图数据
  const categories = ['技术总监', '算法工程师', '前端工程师', '产品经理', 'UI设计师']
  const data = [
    { name: '需求确认', value: [0, 0, 2], itemStyle: { color: '#409eff' } },
    { name: '职位发布', value: [1, 2, 5], itemStyle: { color: '#67c23a' } },
    { name: '简历收集', value: [1, 5, 15], itemStyle: { color: '#e6a23c' } },
    { name: '面试进行', value: [1, 10, 20], itemStyle: { color: '#f56c6c' } },
    { name: '发放Offer', value: [1, 18, 22], itemStyle: { color: '#909399' } },
    
    { name: '需求确认', value: [2, 0, 3], itemStyle: { color: '#409eff' } },
    { name: '职位发布', value: [2, 3, 6], itemStyle: { color: '#67c23a' } },
    { name: '简历收集', value: [2, 6, 18], itemStyle: { color: '#e6a23c' } },
    { name: '面试进行', value: [2, 15, 25], itemStyle: { color: '#f56c6c' } }
  ]
  
  const option = {
    tooltip: {
   
      formatter: function (params: unknown) {
        return params.name + ': ' + params.value[1] + '天 - ' + params.value[2] + '天'
      }
    },
    legend: {
      data: ['需求确认', '职位发布', '简历收集', '面试进行', '发放Offer']
    },
    grid: {
      left: '15%',
      right: '10%',
      top: '10%',
      bottom: '10%'
    },
    xAxis: {
      type: 'value',
      name: '天数',
      max: 30
    },
    yAxis: {
      type: 'category',
      data: categories
    },
    series: [
      {
        type: 'custom',
   
        renderItem: function (params: unknown, api: unknown) {
          const categoryIndex = api.value(0)
          const start = api.coord([api.value(1), categoryIndex])
          const end = api.coord([api.value(2), categoryIndex])
          const height = api.size([0, 1])[1] * 0.6
          
          return {
            type: 'rect',
            shape: {
              x: start[0],
              y: start[1] - height / 2,
              width: end[0] - start[0],
              height: height
            },
            style: api.style()
          }
        },
        data: data
      }
    ]
  }
  
  myChart.setOption(option)
}

// 刷新数据
const refreshData = () => {
  reportTime.value = new Date().toLocaleString()
  ElMessage.success('数据已刷新')
}

// 导出报表
const exportReport = () => {
  ElMessage.success('正在导出招聘进度报表...')
}

// 打印报表
const printReport = () => {
  window.print()
}

// 查看部门详情
   
const viewDepartmentDetail = (row: unknown) => {
  ElMessage.info(`查看${row.department}招聘详情`)
}

// 查看职位详情
   
const viewPositionDetail = (position: unknown) => {
  ElMessage.info(`查看${position.title}职位详情`)
}

// 处理风险
   
const handleRisk = async (risk: unknown) => {
  try {
    await ElMessageBox.confirm(
      risk.suggestion,
      '风险处理建议',
      {
        confirmButtonText: '立即处理',
        cancelButtonText: '稍后处理',
        type: 'warning'
      }
    )
    
    ElMessage.success('已转入处理流程')
  } catch (__error) {
    // 用户取消
  }
}

// 初始化
onMounted(() => {
  initProgressChart()
  if (showTimeline.value) {
    initGanttChart()
  }
  
  // 响应式处理
  window.addEventListener('resize', () => {
    const progressChart = echarts.getInstanceByDom(document.getElementById('progressChart')!)
    const ganttChart = echarts.getInstanceByDom(document.getElementById('ganttChart')!)
    
    progressChart?.resize()
    ganttChart?.resize()
  })
})

// 监听时间轴显示
watch(showTimeline, (val) => {
  if (val) {
    nextTick(() => {
      initGanttChart()
    })
  }
})
</script>

<style lang="scss" scoped>
.recruitment-progress {
  padding: 20px;
  
  // 头部卡片
  .header-card {
    margin-bottom: 20px;
    
    .report-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .title-section {
        h2 {
          margin: 0 0 10px;
          color: #303133;
        }
        
        p {
          margin: 0;
          color: #909399;
          font-size: 14px;
        }
      }
    }
  }
  
  // 概览卡片
  .overview-card {
    margin-bottom: 20px;
    
    .progress-item {
      display: flex;
      align-items: center;
      gap: 20px;
      
      .progress-circle {
        :deep(.el-progress) {
          .percentage-value {
            display: block;
            font-size: 24px;
            font-weight: bold;
          }
          
          .percentage-label {
            display: block;
            font-size: 12px;
            color: #909399;
            margin-top: 5px;
          }
        }
      }
      
      .progress-info {
        p {
          margin: 5px 0;
          font-size: 14px;
          color: #606266;
        }
      }
    }
  }
  
  // 部门卡片
  .department-card {
    margin-bottom: 20px;
    
    .number-tag {
      font-weight: bold;
      
      &.warning {
        color: #e6a23c;
      }
      
      &.primary {
        color: #409eff;
      }
      
      &.success {
        color: #67c23a;
      }
    }
  }
  
  // 甘特图卡片
  .gantt-card {
    margin-bottom: 20px;
  }
  
  // 关键职位卡片
  .key-positions-card {
    margin-bottom: 20px;
    
    .position-card {
      border: 1px solid #ebeef5;
      border-radius: 8px;
      padding: 15px;
      height: 100%;
      transition: all 0.3s;
      
      &:hover {
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      }
      
      &.urgent {
        border-color: #f56c6c;
        background-color: #fef0f0;
      }
      
      .position-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        
        h4 {
          margin: 0;
          font-size: 16px;
          color: #303133;
        }
      }
      
      .position-content {
        .info-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          font-size: 13px;
          
          .label {
            color: #909399;
          }
          
          .value {
            color: #606266;
            font-weight: bold;
          }
        }
      }
      
      .position-progress {
        margin: 15px 0;
      }
      
      .position-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .deadline {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
  
  // 风险卡片
  .risk-card {
    margin-bottom: 20px;
    
    :deep(.el-card__header) {
      .el-badge {
        margin-left: 10px;
      }
    }
    
    .risk-content {
      p {
        margin: 0 0 10px;
      }
      
      .risk-action {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 13px;
        color: #409eff;
      }
    }
  }
  
  // 说明卡片
  .notes-card {
    :deep(.el-descriptions) {
      .el-descriptions__body {
        background-color: #fafafa;
      }
    }
  }
}

// 打印样式
@media print {
  .action-section {
    display: none;
  }
  
  .el-card {
    box-shadow: none;
    border: 1px solid #ddd;
  }
}
</style>