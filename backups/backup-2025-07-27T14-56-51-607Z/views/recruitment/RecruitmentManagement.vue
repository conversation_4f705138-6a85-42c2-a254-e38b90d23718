<template>
  <div class="modern-recruitment-management" :class="{ 'mobile-layout': isMobile }">
    <!-- 现代化页面头部 -->
    <div class="page-header" role="banner">
      <div class="header-content">
        <div class="header-title">
          <h1>
            <el-icon class="title-icon"><UserFilled /></el-icon>
            招聘管理
          </h1>
          <p class="subtitle">智能化招聘流程管理与人才选拔平台</p>
        </div>

        <!-- 快速操作按钮 -->
        <div class="header-actions" v-if="!isMobile">
          <el-button type="primary" size="large" @click="createPlan">
            <el-icon><Plus /></el-icon>
            创建招聘计划
          </el-button>

          <el-dropdown trigger="click" placement="bottom-end">
            <el-button size="large">
              <el-icon><MoreFilled /></el-icon>
              更多操作
            </el-button>

            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="publishPosition">
                  <el-icon><Upload /></el-icon>
                  发布招聘岗位
                </el-dropdown-item>
                <el-dropdown-item @click="viewStatistics">
                  <el-icon><DataAnalysis /></el-icon>
                  查看招聘统计
                </el-dropdown-item>
                <el-dropdown-item @click="viewAllActivities" divided>
                  <el-icon><Clock /></el-icon>
                  查看所有活动
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>

        <!-- 移动端操作按钮 -->
        <div class="mobile-actions" v-if="isMobile">
          <el-button type="primary" circle @click="createPlan">
            <el-icon><Plus /></el-icon>
          </el-button>
          <el-button circle @click="showMobileMenu = true">
            <el-icon><MoreFilled /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon active">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.activePlans }}</div>
              <div class="stats-label">活跃招聘计划</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon positions">
              <el-icon><Briefcase /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.totalPositions }}</div>
              <div class="stats-label">招聘岗位</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon applicants">
              <el-icon><User /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.totalApplicants }}</div>
              <div class="stats-label">应聘人员</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon pending">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.pendingApproval }}</div>
              <div class="stats-label">待审批计划</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 功能导航 -->
    <el-row :gutter="20" class="function-nav">
      <el-col :span="6">
        <el-card class="function-card" @click="navigateTo('/recruitment/plans')">
          <div class="function-content">
            <div class="function-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="function-info">
              <h3>招聘计划管理</h3>
              <p>制定、审批和管理招聘计划</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="function-card" @click="navigateTo('/recruitment/positions')">
          <div class="function-content">
            <div class="function-icon">
              <el-icon><Briefcase /></el-icon>
            </div>
            <div class="function-info">
              <h3>岗位信息管理</h3>
              <p>发布和管理招聘岗位信息</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="function-card" @click="navigateTo('/recruitment/applicants')">
          <div class="function-content">
            <div class="function-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="function-info">
              <h3>应聘人员管理</h3>
              <p>管理应聘人员信息和简历</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="function-card" @click="navigateTo('/recruitment/applications')">
          <div class="function-content">
            <div class="function-icon">
              <el-icon><Files /></el-icon>
            </div>
            <div class="function-info">
              <h3>申请流程管理</h3>
              <p>管理招聘申请和面试流程</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作 -->
    <el-card class="quick-actions">
      <template #header>
        <div class="card-header">
          <span>快速操作</span>
        </div>
      </template>
      <el-row :gutter="16">
        <el-col :span="8">
          <el-button type="primary" size="large" @click="createPlan" style="width: 100%">
            <el-icon><Plus /></el-icon>
            创建招聘计划
          </el-button>
        </el-col>
        <el-col :span="8">
          <el-button type="success" size="large" @click="publishPosition" style="width: 100%">
            <el-icon><Upload /></el-icon>
            发布招聘岗位
          </el-button>
        </el-col>
        <el-col :span="8">
          <el-button type="info" size="large" @click="viewStatistics" style="width: 100%">
            <el-icon><DataAnalysis /></el-icon>
            查看招聘统计
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 最近活动 -->
    <el-card class="recent-activities">
      <template #header>
        <div class="card-header">
          <span>最近活动</span>
          <el-button type="text" @click="viewAllActivities">查看全部</el-button>
        </div>
      </template>
      <el-timeline>
        <el-timeline-item
          v-for="activity in recentActivities"
          :key="activity.id"
          :timestamp="activity.timestamp"
          :type="activity.type"
        >
          {{ activity.description }}
        </el-timeline-item>
      </el-timeline>
    </el-card>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'RecruitmentManagement'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  UserFilled,
  Document,
  Briefcase,
  User,
  Clock,
  Files,
  Plus,
  Upload,
  DataAnalysis,
  MoreFilled
} from '@element-plus/icons-vue'
import { useMobile } from '@/composables/useMobile'
import { recruitmentPlanApi, recruitmentAnalyticsApi } from '@/api/recruitment'

const router = useRouter()

// 移动端适配
const {isMobile: _isMobile} =  useMobile()

// UI状态
const showMobileMenu 
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 24px;
}

.mobile-layout {
  padding: 16px;
}

/* 页面头部 */
.page-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title h1 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 32px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  margin: 0;
  color: #7f8c8d;
  font-size: 16px;
  font-weight: 400;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.mobile-actions {
  display: flex;
  gap: 8px;
}

.stats-cards {
  margin-bottom: 24px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stats-icon.active {
  background: linear-gradient(135deg, #409eff, #67c23a);
}

.stats-icon.positions {
  background: linear-gradient(135deg, #e6a23c, #f56c6c);
}

.stats-icon.applicants {
  background: linear-gradient(135deg, #909399, #606266);
}

.stats-icon.pending {
  background: linear-gradient(135deg, #f56c6c, #e6a23c);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.function-nav {
  margin-bottom: 24px;
}

.function-card {
  cursor: pointer;
  transition: all 0.3s ease;
  height: 120px;
}

.function-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.function-content {
  display: flex;
  align-items: center;
  gap: 16px;
  height: 100%;
}

.function-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background: linear-gradient(135deg, #409eff, #67c23a);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.function-info h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.function-info p {
  margin: 0;
  font-size: 14px;
  color: #606266;
}

.quick-actions,
.recent-activities {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
