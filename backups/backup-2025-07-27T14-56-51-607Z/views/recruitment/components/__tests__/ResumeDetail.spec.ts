 
/* eslint-disable @typescript-eslint/no-unused-vars */
/**
 * ResumeDetail 组件测试
 * @description 自动生成的组件测试文件
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { mount, flushPromises } from '@vue/test-utils'
import ResumeDetail from '../ResumeDetail.vue'
describe('ResumeDetail', () => {
  let wrapper

  beforeEach(() => {
    wrapper = null
  })

  afterEach(() => {
    wrapper?.unmount()
  })

  it('应该正确渲染', async () => {
    const wrapper = mount(ResumeDetail)
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.hr-resume-detail').exists()).toBe(true)
  })

  it('应该匹配快照', () => {
    const wrapper = mount(ResumeDetail)
    expect(wrapper.html()).toMatchSnapshot()
  })
})
