 
 

/**
 * InterviewCalendar 组件测试
 * @description 自动生成的组件测试文件
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { mount, flushPromises } from '@vue/test-utils'
import InterviewCalendar from '../InterviewCalendar.vue'
describe('InterviewCalendar', () => {
  let wrapper

  beforeEach(() => {
    wrapper = null
  })

  afterEach(() => {
    wrapper?.unmount()
  })

  it('应该正确渲染', async () => {
    const wrapper = mount(InterviewCalendar)
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.hr-interview-calendar').exists()).toBe(true)
  })

  it('应该处理异步操作', async () => {
    const wrapper = mount(InterviewCalendar)

    // 等待异步操作完成
    await wrapper.vm.$nextTick()
    await flushPromises()

    // 验证异步操作结果
    expect(wrapper.find('[data-loaded="true"]').exists()).toBe(true)
  })

  it('应该显示加载状态', async () => {
    const wrapper = mount(InterviewCalendar, {
      props: {
        loading: true
      }
    })

    // 应该显示加载指示器
    expect(wrapper.find('.loading-indicator').exists()).toBe(true)

    // 更新为非加载状态
    await wrapper.setProps({ loading: false })
    expect(wrapper.find('.loading-indicator').exists()).toBe(false)
  })

  it('应该处理空数据状态', async () => {
    const wrapper = mount(InterviewCalendar, {
      props: {
        data: []
      }
    })

    // 应该显示空状态提示
    expect(wrapper.find('.empty-state').exists()).toBe(true)
    expect(wrapper.text()).toContain('暂无数据')
  })

  it('应该匹配快照', () => {
    const wrapper = mount(InterviewCalendar)
    expect(wrapper.html()).toMatchSnapshot()
  })
})
