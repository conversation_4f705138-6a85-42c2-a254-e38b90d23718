<template>
  <div class="resume-detail">
    <!-- 基本信息 -->
    <div class="section">
      <h3 class="section-title">基本信息</h3>
      <el-descriptions :column="3" border>
        <el-descriptions-item label="姓名">{{ resume.name }}</el-descriptions-item>
        <el-descriptions-item label="性别">{{ resume.gender }}</el-descriptions-item>
        <el-descriptions-item label="出生日期">{{ resume.birthDate }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ resume.phone }}</el-descriptions-item>
        <el-descriptions-item label="邮箱">{{ resume.email }}</el-descriptions-item>
        <el-descriptions-item label="身份证号">{{ resume.idCard }}</el-descriptions-item>
        <el-descriptions-item label="现居地" :span="2">{{ resume.currentLocation }}</el-descriptions-item>
        <el-descriptions-item label="期望工作地">{{ resume.expectedLocation }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 求职意向 -->
    <div class="section">
      <h3 class="section-title">求职意向</h3>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="期望职位">{{ resume.expectedPosition }}</el-descriptions-item>
        <el-descriptions-item label="期望薪资">
          {{ resume.expectedSalaryMin }}-{{ resume.expectedSalaryMax }}K/月
        </el-descriptions-item>
        <el-descriptions-item label="工作状态">{{ resume.jobStatus }}</el-descriptions-item>
        <el-descriptions-item label="到岗时间">{{ resume.availableTime }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 教育背景 -->
    <div class="section">
      <h3 class="section-title">教育背景</h3>
      <el-timeline>
        <el-timeline-item
          v-for="(edu, index) in resume.education"
          :key="index"
          :timestamp="`${edu.startDate} - ${edu.endDate}`"
          placement="top"
        >
          <el-card>
            <h4>{{ edu.school }}</h4>
            <p>{{ edu.major }} | {{ edu.degree }} | {{ edu.isFullTime ? '全日制' : '非全日制' }}</p>
            <p v-if="edu.achievements" class="achievements">{{ edu.achievements }}</p>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </div>

    <!-- 工作经历 -->
    <div class="section">
      <h3 class="section-title">工作经历</h3>
      <el-timeline>
        <el-timeline-item
          v-for="(work, index) in resume.workExperience"
          :key="index"
          :timestamp="`${work.startDate} - ${work.isCurrent ? '至今' : work.endDate}`"
          placement="top"
          :color="work.isCurrent ? '#0bbd87' : ''"
        >
          <el-card>
            <h4>{{ work.company }} - {{ work.position }}</h4>
            <p class="description">{{ work.description }}</p>
            <p v-if="work.achievements" class="achievements">
              <strong>工作成果：</strong>{{ work.achievements }}
            </p>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </div>

    <!-- 项目经验 -->
    <div v-if="resume.projectExperience?.length" class="section">
      <h3 class="section-title">项目经验</h3>
      <el-timeline>
        <el-timeline-item
          v-for="(project, index) in resume.projectExperience"
          :key="index"
          :timestamp="`${project.startDate} - ${project.endDate}`"
          placement="top"
        >
          <el-card>
            <h4>{{ project.name }}</h4>
            <p><strong>担任角色：</strong>{{ project.role }}</p>
            <p class="description">{{ project.description }}</p>
            <div v-if="project.technologies?.length" class="technologies">
              <strong>技术栈：</strong>
              <el-tag 
                v-for="tech in project.technologies" 
                :key="tech"
                size="small"
                style="margin-right: 5px"
              >
                {{ tech }}
              </el-tag>
            </div>
            <p v-if="project.achievements" class="achievements">
              <strong>项目成果：</strong>{{ project.achievements }}
            </p>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </div>

    <!-- 技能特长 -->
    <div class="section">
      <h3 class="section-title">技能特长</h3>
      <div class="skills-container">
        <div v-if="resume.skills?.length" class="skill-group">
          <h4>专业技能</h4>
          <el-tag 
            v-for="skill in resume.skills" 
            :key="skill"
            size="large"
            style="margin: 5px"
          >
            {{ skill }}
          </el-tag>
        </div>
        
        <div v-if="resume.languages?.length" class="skill-group">
          <h4>语言能力</h4>
          <div v-for="lang in resume.languages" :key="lang.language" class="language-item">
            <span>{{ lang.language }}：</span>
            <el-rate 
              :model-value="getProficiencyLevel(lang.proficiency)" 
              disabled
              :max="5"
             />
            <span class="proficiency-text">{{ lang.proficiency }}</span>
          </div>
        </div>
        
        <div v-if="resume.certificates?.length" class="skill-group">
          <h4>证书资质</h4>
          <el-table :data="resume.certificates" size="small">
            <el-table-column prop="name" label="证书名称"  />
            <el-table-column prop="issueOrg" label="颁发机构"  />
            <el-table-column prop="issueDate" label="获得时间" width="100"  />
            <el-table-column prop="expiryDate" label="有效期至" width="100"  />
          </el-table>
        </div>
      </div>
    </div>

    <!-- 自我评价 -->
    <div v-if="resume.selfEvaluation" class="section">
      <h3 class="section-title">自我评价</h3>
      <div class="content-box">
        {{ resume.selfEvaluation }}
      </div>
    </div>

    <!-- AI分析结果 -->
    <div v-if="resume.aiParsed" class="section">
      <h3 class="section-title">AI分析结果</h3>
      <el-descriptions :column="1" border>
        <el-descriptions-item label="匹配度评分">
          <el-progress 
            :percentage="resume.aiMatchScore || 0" 
            :color="getScoreColor(resume.aiMatchScore)"
           />
        </el-descriptions-item>
        <el-descriptions-item label="AI总结">
          {{ resume.aiSummary }}
        </el-descriptions-item>
        <el-descriptions-item label="能力标签">
          <el-tag 
            v-for="tag in resume.aiTags" 
            :key="tag"
            style="margin-right: 5px"
          >
            {{ tag }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 附件 -->
    <div v-if="resume.attachments?.length" class="section">
      <h3 class="section-title">附件材料</h3>
      <div class="attachments">
        <div 
          v-for="file in resume.attachments" 
          :key="file.url"
          class="attachment-item"
        >
          <el-icon><document /></el-icon>
          <span>{{ file.name }}</span>
          <el-button link type="primary" @click="downloadFile(file)">
            下载
          </el-button>
        </div>
      </div>
    </div>

    <!-- 评价记录 -->
    <div v-if="resume.comments?.length" class="section">
      <h3 class="section-title">评价记录</h3>
      <el-timeline>
        <el-timeline-item
          v-for="comment in resume.comments"
          :key="comment.time"
          :timestamp="comment.time"
          placement="top"
        >
          <el-card>
            <div class="comment-header">
              <strong>{{ comment.userName }}</strong>
            </div>
            <div class="comment-content">{{ comment.content }}</div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </div>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'ResumeDetail'
})
 
import { computed } from 'vue'
import { Document } from '@element-plus/icons-vue'
import type { Resume } from '@/types/recruitment'

interface Props {
  resume: Resume
}

const props = defineProps<Props>()

// 获取熟练度等级
const getProficiencyLevel = (proficiency: string) => {
  const map: Record<string, number> = {
    '精通': 5,
    '熟练': 4,
    '一般': 3,
    '了解': 2,
    '入门': 1
  }
  return map[proficiency] || 3
}

// 获取分数颜色
const getScoreColor = (score?: number) => {
  if (!score) return '#909399'
  if (score >= 80) return '#67C23A'
  if (score >= 60) return '#E6A23C'
  return '#F56C6C'
}

// 下载文件
   
const downloadFile = (file: unknown) => {
  window.open(file.url, '_blank')
}
</script>

<style lang="scss" scoped>
.resume-detail {
  padding: 20px;
  
  .section {
    margin-bottom: 30px;
    
    .section-title {
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 2px solid #409eff;
      color: #303133;
    }
  }
  
  .el-timeline {
    padding-left: 0;
    
    :deep(.el-timeline-item__wrapper) {
      padding-left: 40px;
    }
    
    .el-card {
      h4 {
        margin: 0 0 10px;
        color: #303133;
      }
      
      p {
        margin: 5px 0;
        color: #606266;
        line-height: 1.6;
      }
      
      .description {
        white-space: pre-wrap;
      }
      
      .achievements {
        margin-top: 10px;
        padding-top: 10px;
        border-top: 1px dashed #e4e7ed;
      }
      
      .technologies {
        margin: 10px 0;
      }
    }
  }
  
  .skills-container {
    .skill-group {
      margin-bottom: 20px;
      
      h4 {
        margin-bottom: 10px;
        color: #606266;
      }
      
      .language-item {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        
        .proficiency-text {
          margin-left: 10px;
          color: #909399;
        }
      }
    }
  }
  
  .content-box {
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;
    white-space: pre-wrap;
    line-height: 1.8;
  }
  
  .attachments {
    .attachment-item {
      display: flex;
      align-items: center;
      padding: 10px;
      margin-bottom: 10px;
      background-color: #f5f7fa;
      border-radius: 4px;
      
      .el-icon {
        margin-right: 10px;
        font-size: 20px;
        color: #909399;
      }
      
      span {
        flex: 1;
      }
    }
  }
  
  .comment-header {
    margin-bottom: 5px;
    color: #303133;
  }
  
  .comment-content {
    color: #606266;
    line-height: 1.6;
  }
}
</style>