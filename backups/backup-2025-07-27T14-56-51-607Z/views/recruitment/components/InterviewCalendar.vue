<template>
  <div class="interview-calendar">
    <!-- 顶部操作栏 -->
    <div class="calendar-header">
      <div class="header-left">
        <el-button-group>
          <el-button @click="handlePrev">
            <el-icon><arrow-left /></el-icon>
          </el-button>
          <el-button @click="handleToday">今天</el-button>
          <el-button @click="handleNext">
            <el-icon><arrow-right /></el-icon>
          </el-button>
        </el-button-group>
        
        <h3 class="current-date">{{ currentDateDisplay }}</h3>
      </div>
      
      <div class="header-right">
        <el-select v-model="viewType" style="width: 100px; margin-right: 10px">
          <el-option label="月视图" value="month"  />
          <el-option label="周视图" value="week"  />
          <el-option label="日视图" value="day"  />
        </el-select>
        
        <el-select 
          v-model="filterInterviewer" 
          clearable
          placeholder="筛选面试官"
          style="width: 150px; margin-right: 10px"
        >
          <el-option 
            v-for="interviewer in interviewerList" 
            :key="interviewer.id" 
            :label="interviewer.name" 
            :value="interviewer.id" 
           />
        </el-select>
        
        <el-button type="primary" @click="handleAddInterview">
          <el-icon><plus /></el-icon>
          安排面试
        </el-button>
      </div>
    </div>

    <!-- 日历主体 -->
    <div class="calendar-body" v-loading="loading">
      <!-- 月视图 -->
      <div v-if="viewType === 'month'" class="month-view">
        <div class="weekday-header">
          <div v-for="day in weekDays" :key="day" class="weekday">
            {{ day }}
          </div>
        </div>
        
        <div class="month-grid">
          <div 
            v-for="(day, index) in monthDays" 
            :key="index" 
            class="day-cell"
            :class="{
              'other-month': !day.isCurrentMonth,
              'today': day.isToday,
              'weekend': day.isWeekend
            }"
            @click="handleDayClick(day)"
          >
            <div class="day-header">
              <span class="day-number">{{ day.date }}</span>
              <span v-if="day.interviews.length" class="interview-count">
                {{ day.interviews.length }}场
              </span>
            </div>
            
            <div class="day-content">
              <div 
                v-for="(interview, idx) in day.interviews.slice(0, 3)" 
                :key="interview.id"
                class="interview-item"
                :style="{ backgroundColor: getInterviewColor(interview) }"
                @click.stop="handleInterviewClick(interview)"
              >
                <span class="time">{{ formatTime(interview.startTime) }}</span>
                <span class="candidate">{{ interview.candidateName }}</span>
              </div>
              
              <div v-if="day.interviews.length > 3" class="more-interviews">
                +{{ day.interviews.length - 3 }}场面试
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 周视图 -->
      <div v-else-if="viewType === 'week'" class="week-view">
        <div class="time-grid">
          <div class="time-column">
            <div class="time-header"></div>
            <div v-for="hour in 24" :key="hour" class="time-slot">
              {{ String(hour - 1).padStart(2, '0') }}:00
            </div>
          </div>
          
          <div class="days-container">
            <div class="days-header">
              <div 
                v-for="day in weekViewDays" 
                :key="day.date" 
                class="day-header"
                :class="{ 'today': day.isToday }"
              >
                <div class="weekday">{{ day.weekday }}</div>
                <div class="date">{{ day.dayOfMonth }}</div>
              </div>
            </div>
            
            <div class="days-content">
              <div 
                v-for="day in weekViewDays" 
                :key="day.date" 
                class="day-column"
              >
                <div 
                  v-for="interview in day.interviews" 
                  :key="interview.id"
                  class="interview-block"
                  :style="getInterviewStyle(interview)"
                  @click="handleInterviewClick(interview)"
                >
                  <div class="interview-time">
                    {{ formatTime(interview.startTime) }} - {{ formatTime(interview.endTime) }}
                  </div>
                  <div class="interview-info">
                    <div class="candidate">{{ interview.candidateName }}</div>
                    <div class="position">{{ interview.positionName }}</div>
                    <div class="interviewer">{{ interview.interviewerNames.join(', ') }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 日视图 -->
      <div v-else-if="viewType === 'day'" class="day-view">
        <div class="day-timeline">
          <div class="timeline-header">
            <h4>{{ selectedDateDisplay }}</h4>
          </div>
          
          <div class="timeline-content">
            <div v-for="hour in 24" :key="hour" class="hour-row">
              <div class="hour-label">{{ String(hour - 1).padStart(2, '0') }}:00</div>
              <div class="hour-content">
                <div 
                  v-for="interview in getInterviewsForHour(hour - 1)" 
                  :key="interview.id"
                  class="timeline-interview"
                  :style="{ backgroundColor: getInterviewColor(interview) }"
                  @click="handleInterviewClick(interview)"
                >
                  <div class="interview-header">
                    <span class="time">
                      {{ formatTime(interview.startTime) }} - {{ formatTime(interview.endTime) }}
                    </span>
                    <el-tag :type="getStatusTag(interview.status)" size="small">
                      {{ interview.status }}
                    </el-tag>
                  </div>
                  <div class="interview-body">
                    <div class="main-info">
                      <h5>{{ interview.candidateName }}</h5>
                      <p>{{ interview.positionName }}</p>
                    </div>
                    <div class="extra-info">
                      <p><el-icon><user /></el-icon> {{ interview.interviewerNames.join(', ') }}</p>
                      <p><el-icon><location /></el-icon> {{ interview.location || '待定' }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 面试详情对话框 -->
    <el-dialog 
      v-model="detailDialogVisible" 
      :title="currentInterview?.candidateName + ' - 面试详情'" 
      width="700px"
    >
      <el-descriptions v-if="currentInterview" :column="2" border>
        <el-descriptions-item label="候选人">{{ currentInterview.candidateName }}</el-descriptions-item>
        <el-descriptions-item label="应聘职位">{{ currentInterview.positionName }}</el-descriptions-item>
        <el-descriptions-item label="面试时间">
          {{ formatDateTime(currentInterview.startTime) }} - {{ formatTime(currentInterview.endTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="面试轮次">{{ currentInterview.round }}</el-descriptions-item>
        <el-descriptions-item label="面试形式">{{ currentInterview.type }}</el-descriptions-item>
        <el-descriptions-item label="面试地点">{{ currentInterview.location || '待定' }}</el-descriptions-item>
        <el-descriptions-item label="面试官" :span="2">
          {{ currentInterview.interviewerNames.join(', ') }}
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusTag(currentInterview.status)">
            {{ currentInterview.status }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ currentInterview.candidatePhone }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">
          {{ currentInterview.remark || '无' }}
        </el-descriptions-item>
      </el-descriptions>
      
      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleEditInterview">编辑</el-button>
        <el-button type="danger" @click="handleCancelInterview">取消面试</el-button>
      </template>
    </el-dialog>

    <!-- 安排面试对话框 -->
    <el-dialog 
      v-model="scheduleDialogVisible" 
      title="安排面试" 
      width="700px"
    >
      <el-form 
        ref="scheduleFormRef" 
        :model="scheduleForm" 
        :rules="scheduleRules" 
        label-width="100px"
      >
        <el-form-item label="候选人" prop="candidateId">
          <el-select 
            v-model="scheduleForm.candidateId" 
            filterable
            remote
            :remote-method="searchCandidates"
            placeholder="请搜索候选人"
            style="width: 100%"
          >
            <el-option 
              v-for="candidate in candidateOptions" 
              :key="candidate.id" 
              :label="`${candidate.name} - ${candidate.position}`" 
              :value="candidate.id" 
             />
          </el-select>
        </el-form-item>
        
        <el-form-item label="面试时间" prop="timeRange">
          <el-date-picker
            v-model="scheduleForm.timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
            :disabled-date="disabledDate"
            style="width: 100%"
           />
        </el-form-item>
        
        <el-form-item label="面试轮次" prop="round">
          <el-radio-group v-model="scheduleForm.round">
            <el-radio label="初试">初试</el-radio>
            <el-radio label="复试">复试</el-radio>
            <el-radio label="终试">终试</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="面试形式" prop="type">
          <el-radio-group v-model="scheduleForm.type">
            <el-radio label="现场面试">现场面试</el-radio>
            <el-radio label="视频面试">视频面试</el-radio>
            <el-radio label="电话面试">电话面试</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="面试官" prop="interviewerIds">
          <el-select 
            v-model="scheduleForm.interviewerIds" 
            multiple
            placeholder="请选择面试官"
            style="width: 100%"
          >
            <el-option 
              v-for="interviewer in interviewerList" 
              :key="interviewer.id" 
              :label="interviewer.name" 
              :value="interviewer.id"
              :disabled="checkInterviewerConflict(interviewer.id)"
            >
              <span>{{ interviewer.name }}</span>
              <span v-if="checkInterviewerConflict(interviewer.id)" style="color: #f56c6c; font-size: 12px">
                (时间冲突)
              </span>
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="面试地点" v-if="scheduleForm.type === '现场面试'">
          <el-input v-model="scheduleForm.location" placeholder="请输入面试地点"   />
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input 
            v-model="scheduleForm.remark" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入备注信息"
            />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="scheduleDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleScheduleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, 
  ArrowLeft, 
  ArrowRight, 
  User, 
  Location 
} from '@element-plus/icons-vue'
import { interviewApi, interviewArrangementApi, resumeApi } from '@/api/recruitment'
import type { InterviewSchedule, Resume } from '@/types/recruitment'
import type { FormInstance, FormRules } from 'element-plus'

// 日历数据
const currentDate = ref(new Date())
const viewType = ref<'month' | 'week' | 'day'>('month')
const selectedDate = ref(new Date())
const loading = ref(false)

// 面试数据
const interviews = ref<InterviewSchedule[]>([])
const filterInterviewer = ref('')
const interviewerList = ref([
  { id: 'user1', name: 'HrHr张经理' },
  { id: 'user2', name: '李主管' },
  { id: 'user3', name: '王总监' },
  { id: 'user4', name: 'HR小陈' }
])

// 星期显示
const weekDays = ['日', '一', '二', '三', '四', '五', '六']

// 详情对话框
const detailDialogVisible = ref(false)
const currentInterview = ref<InterviewSchedule>()

// 安排面试对话框
const scheduleDialogVisible = ref(false)
const scheduleFormRef = ref<FormInstance>()
const scheduleForm = reactive({
  candidateId: '',
  timeRange: [] as string[],
  round: '初试',
  type: '现场面试',
  interviewerIds: [] as string[],
  location: '',
  remark: ''
})

// 候选人选项
const candidateOptions = ref<any[]>([])

// 编辑模式标识
const isEditMode = ref(false)
const editingInterviewId = ref<string>('')

// 表单验证规则
const scheduleRules = reactive<FormRules>({
  candidateId: [
    { required: true, message: '请选择候选人', trigger: 'change' }
  ],
  timeRange: [
    { required: true, message: '请选择面试时间', trigger: 'change' }
  ],
  round: [
    { required: true, message: '请选择面试轮次', trigger: 'change' }
  ],
  type: [
    { required: true, message: '请选择面试形式', trigger: 'change' }
  ],
  interviewerIds: [
    { required: true, message: '请选择面试官', trigger: 'change' }
  ]
})

// 当前日期显示
const currentDateDisplay = computed(() => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth() + 1
  
  if (viewType.value === 'month') {
    return `${year}年${month}月`
  } else if (viewType.value === 'week') {
    const weekStart = getWeekStart(currentDate.value)
    const weekEnd = getWeekEnd(currentDate.value)
    return `${year}年${month}月 第${getWeekOfMonth(currentDate.value)}周`
  } else {
    return formatDate(currentDate.value)
  }
})

// 选中日期显示
const selectedDateDisplay = computed(() => {
  return formatDate(selectedDate.value)
})

// 月视图天数
const monthDays = computed(() => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)
  const days = []
  
  // 补充上月天数
  const firstDayWeek = firstDay.getDay()
  for (let i = firstDayWeek - 1; i >= 0; i--) {
    const date = new Date(year, month, -i)
    days.push(createDayObject(date, false))
  }
  
  // 当月天数
  for (let i = 1; i <= lastDay.getDate(); i++) {
    const date = new Date(year, month, i)
    days.push(createDayObject(date, true))
  }
  
  // 补充下月天数
  const remainingDays = 42 - days.length
  for (let i = 1; i <= remainingDays; i++) {
    const date = new Date(year, month + 1, i)
    days.push(createDayObject(date, false))
  }
  
  return days
})

// 周视图天数
const weekViewDays = computed(() => {
  const weekStart = getWeekStart(currentDate.value)
  const days = []
  
  for (let i = 0; i < 7; i++) {
    const date = new Date(weekStart)
    date.setDate(date.getDate() + i)
    
    const dayInterviews = getInterviewsForDate(date)
    days.push({
      date: formatDateISO(date),
      weekday: weekDays[date.getDay()],
      dayOfMonth: date.getDate(),
      isToday: isToday(date),
      interviews: dayInterviews
    })
  }
  
  return days
})

// 创建天对象
const createDayObject = (date: Date, isCurrentMonth: boolean) => {
  const dayInterviews = getInterviewsForDate(date)
  
  return {
    date: date.getDate(),
    fullDate: formatDateISO(date),
    isCurrentMonth,
    isToday: isToday(date),
    isWeekend: date.getDay() === 0 || date.getDay() === 6,
    interviews: dayInterviews
  }
}

// 获取指定日期的面试
const getInterviewsForDate = (date: Date) => {
  const dateStr = formatDateISO(date)
  return interviews.value.filter(interview => {
    const interviewDate = interview.startTime.split(' ')[0]
    return interviewDate === dateStr && 
           (!filterInterviewer.value || interview.interviewerIds.includes(filterInterviewer.value))
  })
}

// 获取指定小时的面试
const getInterviewsForHour = (hour: number) => {
  const dateStr = formatDateISO(selectedDate.value)
  return interviews.value.filter(interview => {
    const interviewDate = interview.startTime.split(' ')[0]
    const interviewHour = parseInt(interview.startTime.split(' ')[1].split(':')[0])
    return interviewDate === dateStr && 
           interviewHour === hour &&
           (!filterInterviewer.value || interview.interviewerIds.includes(filterInterviewer.value))
  })
}

// 获取面试样式（周视图）
const getInterviewStyle = (interview: InterviewSchedule) => {
  const startTime = new Date(interview.startTime)
  const endTime = new Date(interview.endTime)
  const startMinutes = startTime.getHours() * 60 + startTime.getMinutes()
  const duration = (endTime.getTime() - startTime.getTime()) / (1000 * 60)
  
  return {
    top: `${startMinutes * 1.667}px`, // 每分钟1.667px (100px/60min)
    height: `${duration * 1.667}px`,
    backgroundColor: getInterviewColor(interview)
  }
}

// 获取面试颜色
const getInterviewColor = (interview: InterviewSchedule) => {
  const colors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399']
  const index = interview.interviewerIds[0]?.charCodeAt(0) % colors.length || 0
  return colors[index]
}

// 获取状态标签类型
const getStatusTag = (status: string) => {
  const map: Record<string, string> = {
    '待面试': '',
    '已完成': 'success',
    '已取消': 'info',
    '未到场': 'danger'
  }
  return map[status] || 'info'
}

// 格式化时间
const formatTime = (datetime: string) => {
  const time = datetime.split(' ')[1]
  return time.substring(0, 5)
}

// 格式化日期时间
const formatDateTime = (datetime: string) => {
  const [date, time] = datetime.split(' ')
  return `${date} ${time.substring(0, 5)}`
}

// 格式化日期
const formatDate = (date: Date) => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  return `${year}年${month}月${day}日`
}

// 格式化日期ISO
const formatDateISO = (date: Date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 判断是否今天
const isToday = (date: Date) => {
  const today = new Date()
  return date.getFullYear() === today.getFullYear() &&
         date.getMonth() === today.getMonth() &&
         date.getDate() === today.getDate()
}

// 获取周开始日期
const getWeekStart = (date: Date) => {
  const d = new Date(date)
  const day = d.getDay()
  const diff = d.getDate() - day
  return new Date(d.setDate(diff))
}

// 获取周结束日期
const getWeekEnd = (date: Date) => {
  const d = new Date(date)
  const day = d.getDay()
  const diff = d.getDate() - day + 6
  return new Date(d.setDate(diff))
}

// 获取月第几周
const getWeekOfMonth = (date: Date) => {
  const firstDay = new Date(date.getFullYear(), date.getMonth(), 1)
  const firstDayWeek = firstDay.getDay()
  const offsetDate = date.getDate() + firstDayWeek - 1
  return Math.ceil(offsetDate / 7)
}

// 禁用日期
const disabledDate = (date: Date) => {
  return date < new Date(new Date().setHours(0, 0, 0, 0))
}

// 检查面试官时间冲突
const checkInterviewerConflict = (interviewerId: string) => {
  if (!scheduleForm.timeRange || scheduleForm.timeRange.length !== 2) {
    return false
  }
  
  const [startTime, endTime] = scheduleForm.timeRange
  return interviews.value.some(interview => {
    if (!interview.interviewerIds.includes(interviewerId)) {
      return false
    }
    
    const existingStart = new Date(interview.startTime)
    const existingEnd = new Date(interview.endTime)
    const newStart = new Date(startTime)
    const newEnd = new Date(endTime)
    
    return (newStart < existingEnd && newEnd > existingStart)
  })
}

// 获取面试数据
const fetchInterviews = async () => {
  loading.value = true
  try {
    // 模拟数据
    interviews.value = [
      {
        id: '1',
        candidateId: 'c1',
        candidateName: '张三',
        candidatePhone: '13800138001',
        positionId: 'p1',
        positionName: '前端工程师',
        startTime: '2025-01-21 10:00:00',
        endTime: '2025-01-21 11:00:00',
        round: '初试',
        type: '现场面试',
        status: '待面试',
        interviewerIds: ['user1', 'user2'],
        interviewerNames: ['张经理', '李主管'],
        location: '会议室A',
        remark: '请准备作品展示',
        createTime: '2025-01-20 15:00:00',
        updateTime: '2025-01-20 15:00:00'
      },
      {
        id: '2',
        candidateId: 'c2',
        candidateName: '李四',
        candidatePhone: '13800138002',
        positionId: 'p2',
        positionName: '产品经理',
        startTime: '2025-01-21 14:00:00',
        endTime: '2025-01-21 15:30:00',
        round: '复试',
        type: '视频面试',
        status: '待面试',
        interviewerIds: ['user3'],
        interviewerNames: ['王总监'],
        location: '',
        remark: '腾讯会议',
        createTime: '2025-01-20 16:00:00',
        updateTime: '2025-01-20 16:00:00'
      }
    ]
  } catch (__error) {
    console.error('获取面试数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索候选人
const searchCandidates = async (query: string) => {
  if (query) {
    try {
      // 模拟搜索
      candidateOptions.value = [
        { id: 'c3', name: '王五', position: 'Java工程师' },
        { id: 'c4', name: '赵六', position: 'UI设计师' }
      ]
    } catch (__error) {
      console.error('搜索候选人失败:', error)
    }
  }
}

// 上一个周期
const handlePrev = () => {
  if (viewType.value === 'month') {
    currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() - 1)
  } else if (viewType.value === 'week') {
    currentDate.value = new Date(currentDate.value.setDate(currentDate.value.getDate() - 7))
  } else {
    currentDate.value = new Date(currentDate.value.setDate(currentDate.value.getDate() - 1))
    selectedDate.value = new Date(currentDate.value)
  }
  fetchInterviews()
}

// 下一个周期
const handleNext = () => {
  if (viewType.value === 'month') {
    currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() + 1)
  } else if (viewType.value === 'week') {
    currentDate.value = new Date(currentDate.value.setDate(currentDate.value.getDate() + 7))
  } else {
    currentDate.value = new Date(currentDate.value.setDate(currentDate.value.getDate() + 1))
    selectedDate.value = new Date(currentDate.value)
  }
  fetchInterviews()
}

// 今天
const handleToday = () => {
  currentDate.value = new Date()
  selectedDate.value = new Date()
  fetchInterviews()
}

// 点击日期
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleDayClick = (day: unknown) => {
  if (viewType.value === 'month' && day.isCurrentMonth) {
    selectedDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth(), day.date)
    viewType.value = 'day'
  }
}

// 点击面试
const handleInterviewClick = (interview: InterviewSchedule) => {
  currentInterview.value = interview
  detailDialogVisible.value = true
}

// 安排面试
const handleAddInterview = () => {
  scheduleForm.candidateId = ''
  scheduleForm.timeRange = []
  scheduleForm.round = '初试'
  scheduleForm.type = '现场面试'
  scheduleForm.interviewerIds = []
  scheduleForm.location = ''
  scheduleForm.remark = ''
  isEditMode.value = false
  editingInterviewId.value = ''
  scheduleDialogVisible.value = true
}

// 编辑面试
const handleEditInterview = () => {
  if (!currentInterview.value) return
  
  // 填充表单数据
  scheduleForm.candidateId = currentInterview.value.candidateId
  scheduleForm.timeRange = [
    currentInterview.value.startTime,
    currentInterview.value.endTime
  ]
  scheduleForm.round = currentInterview.value.round
  scheduleForm.type = currentInterview.value.type
  scheduleForm.interviewerIds = currentInterview.value.interviewerIds
  scheduleForm.location = currentInterview.value.location || ''
  scheduleForm.remark = currentInterview.value.remark || ''
  
  // 添加候选人选项
  candidateOptions.value = [{
    id: currentInterview.value.candidateId,
    name: currentInterview.value.candidateName,
    position: currentInterview.value.positionName
  }]
  
  // 关闭详情弹窗，打开编辑弹窗
  detailDialogVisible.value = false
  scheduleDialogVisible.value = true
  isEditMode.value = true
  editingInterviewId.value = currentInterview.value.id
}

// 取消面试
const handleCancelInterview = async () => {
  try {
    const {value: _value} =  await ElMessageBox.prompt(
      '请输入取消原因',
      '取消面试',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /\S+/,
        inputErrorMessage: '取消原因不能为空',
        type: 'warning'
      }
    )
    
    if (!currentInterview.value) return
    
    // 调用取消API
    await interviewArrangementApi.cancel(Number(currentInterview.value.id), reason)
    
    ElMessage.success('面试已取消')
    detailDialogVisible.value 
  display: flex;
  flex-direction: column;
  
  .calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: #fff;
    border-bottom: 1px solid #ebeef5;
    
    .header-left {
      display: flex;
      align-items: center;
      gap: 20px;
      
      .current-date {
        margin: 0;
        font-size: 20px;
        color: #303133;
      }
    }
    
    .header-right {
      display: flex;
      align-items: center;
    }
  }
  
  .calendar-body {
    flex: 1;
    overflow: auto;
    background: #f5f7fa;
    padding: 20px;
  }
  
  // 月视图
  .month-view {
    background: #fff;
    border-radius: 4px;
    padding: 20px;
    
    .weekday-header {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      margin-bottom: 10px;
      
      .weekday {
        text-align: center;
        font-weight: 500;
        color: #606266;
        padding: 10px 0;
      }
    }
    
    .month-grid {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      grid-gap: 1px;
      background: #ebeef5;
      border: 1px solid #ebeef5;
      
      .day-cell {
        background: #fff;
        min-height: 100px;
        padding: 8px;
        cursor: pointer;
        transition: all 0.3s;
        
        &:hover {
          background: #f5f7fa;
        }
        
        &.other-month {
          color: #c0c4cc;
          background: #fafafa;
        }
        
        &.today {
          background: #ecf5ff;
          
          .day-number {
            color: #409eff;
            font-weight: 500;
          }
        }
        
        &.weekend {
          background: #fef0f0;
        }
        
        .day-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 5px;
          
          .day-number {
            font-size: 14px;
          }
          
          .interview-count {
            font-size: 12px;
            color: #909399;
          }
        }
        
        .day-content {
          .interview-item {
            padding: 2px 5px;
            margin-bottom: 2px;
            border-radius: 2px;
            font-size: 12px;
            color: #fff;
            cursor: pointer;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            
            .time {
              margin-right: 5px;
            }
          }
          
          .more-interviews {
            font-size: 12px;
            color: #909399;
            text-align: center;
            margin-top: 5px;
          }
        }
      }
    }
  }
  
  // 周视图
  .week-view {
    background: #fff;
    border-radius: 4px;
    height: 100%;
    
    .time-grid {
      display: flex;
      height: 100%;
      
      .time-column {
        width: 60px;
        border-right: 1px solid #ebeef5;
        
        .time-header {
          height: 60px;
          border-bottom: 1px solid #ebeef5;
        }
        
        .time-slot {
          height: 100px;
          padding: 5px;
          border-bottom: 1px solid #f5f7fa;
          font-size: 12px;
          color: #909399;
        }
      }
      
      .days-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        
        .days-header {
          display: grid;
          grid-template-columns: repeat(7, 1fr);
          height: 60px;
          border-bottom: 1px solid #ebeef5;
          
          .day-header {
            text-align: center;
            padding: 10px;
            border-right: 1px solid #f5f7fa;
            
            &:last-child {
              border-right: none;
            }
            
            &.today {
              background: #ecf5ff;
              
              .date {
                color: #409eff;
                font-weight: 500;
              }
            }
            
            .weekday {
              font-size: 12px;
              color: #909399;
            }
            
            .date {
              font-size: 16px;
              color: #303133;
            }
          }
        }
        
        .days-content {
          flex: 1;
          display: grid;
          grid-template-columns: repeat(7, 1fr);
          position: relative;
          
          .day-column {
            border-right: 1px solid #f5f7fa;
            position: relative;
            
            &:last-child {
              border-right: none;
            }
            
            .interview-block {
              position: absolute;
              left: 2px;
              right: 2px;
              padding: 5px;
              border-radius: 4px;
              color: #fff;
              font-size: 12px;
              cursor: pointer;
              overflow: hidden;
              
              .interview-time {
                font-weight: 500;
                margin-bottom: 2px;
              }
              
              .interview-info {
                .candidate {
                  font-weight: 500;
                }
                
                .position,
                .interviewer {
                  opacity: 0.9;
                  font-size: 11px;
                }
              }
            }
          }
        }
      }
    }
  }
  
  // 日视图
  .day-view {
    background: #fff;
    border-radius: 4px;
    height: 100%;
    
    .day-timeline {
      height: 100%;
      display: flex;
      flex-direction: column;
      
      .timeline-header {
        padding: 20px;
        border-bottom: 1px solid #ebeef5;
        
        h4 {
          margin: 0;
          color: #303133;
        }
      }
      
      .timeline-content {
        flex: 1;
        overflow: auto;
        
        .hour-row {
          display: flex;
          min-height: 80px;
          border-bottom: 1px solid #f5f7fa;
          
          .hour-label {
            width: 80px;
            padding: 10px;
            color: #909399;
            font-size: 14px;
            border-right: 1px solid #f5f7fa;
          }
          
          .hour-content {
            flex: 1;
            padding: 10px;
            
            .timeline-interview {
              padding: 15px;
              border-radius: 4px;
              margin-bottom: 10px;
              cursor: pointer;
              color: #fff;
              
              .interview-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;
                
                .time {
                  font-weight: 500;
                }
              }
              
              .interview-body {
                display: flex;
                gap: 20px;
                
                .main-info {
                  flex: 1;
                  
                  h5 {
                    margin: 0 0 5px;
                    font-size: 16px;
                  }
                  
                  p {
                    margin: 0;
                    opacity: 0.9;
                  }
                }
                
                .extra-info {
                  p {
                    margin: 0 0 5px;
                    display: flex;
                    align-items: center;
                    gap: 5px;
                    opacity: 0.9;
                    font-size: 14px;
                    
                    .el-icon {
                      font-size: 16px;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>