<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑招聘计划' : '新建招聘计划'"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      label-position="right"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="计划编码" prop="planCode">
            <el-input
              v-model="form.planCode"
              placeholder="请输入计划编码"
              :disabled="isEdit"
              />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="计划名称" prop="planName">
            <el-input
              v-model="form.planName"
              placeholder="请输入计划名称"
              />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="计划类型" prop="planType">
            <el-select
              v-model="form.planType"
              placeholder="请选择计划类型"
              style="width: 100%"
            >
              <el-option label="年度招聘" value="ANNUAL"  />
              <el-option label="专项招聘" value="SPECIAL"  />
              <el-option label="紧急招聘" value="URGENT"  />
              <el-option label="补充招聘" value="SUPPLEMENT"  />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="优先级" prop="priorityLevel">
            <el-select
              v-model="form.priorityLevel"
              placeholder="请选择优先级"
              style="width: 100%"
            >
              <el-option label="高" value="HIGH"  />
              <el-option label="中" value="MEDIUM"  />
              <el-option label="低" value="LOW"  />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="部门" prop="departmentId">
            <el-select
              v-model="form.departmentId"
              placeholder="请选择部门"
              style="width: 100%"
              @change="handleDepartmentChange"
            >
              <el-option
                v-for="dept in departments"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
               />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="岗位类别" prop="positionCategory">
            <el-select
              v-model="form.positionCategory"
              placeholder="请选择岗位类别"
              style="width: 100%"
            >
              <el-option label="教学岗位" value="TEACHING"  />
              <el-option label="科研岗位" value="RESEARCH"  />
              <el-option label="管理岗位" value="MANAGEMENT"  />
              <el-option label="工勤岗位" value="SERVICE"  />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="招聘人数" prop="totalPositions">
            <el-input-number
              v-model="form.totalPositions"
              :min="1"
              :max="999"
              style="width: 100%"
              />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="预算金额" prop="budgetAmount">
            <el-input-number
              v-model="form.budgetAmount"
              :min="0"
              :precision="2"
              style="width: 100%"
              />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="开始日期" prop="startDate">
            <el-date-picker
              v-model="form.startDate"
              type="date"
              placeholder="请选择开始日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
             />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结束日期" prop="endDate">
            <el-date-picker
              v-model="form.endDate"
              type="date"
              placeholder="请选择结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
             />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="招聘原因" prop="recruitmentReason">
        <el-input
          v-model="form.recruitmentReason"
          type="textarea"
          :rows="3"
          placeholder="请输入招聘原因"
          />
      </el-form-item>

      <el-form-item label="岗位要求" prop="requirements">
        <el-input
          v-model="form.requirements"
          type="textarea"
          :rows="4"
          placeholder="请输入岗位要求"
          />
      </el-form-item>

      <el-form-item label="计划描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入计划描述"
          />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { recruitmentPlanApi, type RecruitmentPlan } from '@/api/recruitment'

// Props
interface Props {
  visible: boolean
  formData?: RecruitmentPlan | null
  isEdit: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  formData: null,
  isEdit: false
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const form = reactive<RecruitmentPlan>({
  planCode: '',
  planName: '',
  planType: '',
  departmentId: '',
  departmentName: '',
  positionCategory: '',
  recruitmentReason: '',
  totalPositions: 1,
  priorityLevel: 'MEDIUM',
  startDate: '',
  endDate: '',
  budgetAmount: 0,
  description: '',
  requirements: '',
  planStatus: 'DRAFT' as unknown,
  approvalStatus: 'PENDING'
})

// 部门列表
const departments = ref([
  { id: '1', name: 'HrHr计算机学院' },
  { id: '2', name: '机械工程学院' },
  { id: '3', name: '经济管理学院' },
  { id: '4', name: '人文社科学院' },
  { id: '5', name: '艺术设计学院' }
])

// 表单验证规则
const rules: FormRules = {
  planCode: [
    { required: true, message: '请输入计划编码', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  planName: [
    { required: true, message: '请输入计划名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  planType: [
    { required: true, message: '请选择计划类型', trigger: 'change' }
  ],
  departmentId: [
    { required: true, message: '请选择部门', trigger: 'change' }
  ],
  positionCategory: [
    { required: true, message: '请选择岗位类别', trigger: 'change' }
  ],
  totalPositions: [
    { required: true, message: '请输入招聘人数', trigger: 'blur' },
    { type: 'number', min: 1, message: '招聘人数不能小于1', trigger: 'blur' }
  ],
  priorityLevel: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ],
  startDate: [
    { required: true, message: '请选择开始日期', trigger: 'change' }
  ],
  endDate: [
    { required: true, message: '请选择结束日期', trigger: 'change' }
  ],
  recruitmentReason: [
    { required: true, message: '请输入招聘原因', trigger: 'blur' },
    { min: 10, max: 500, message: '长度在 10 到 500 个字符', trigger: 'blur' }
  ]
}

// 监听visible变化
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal
    if (newVal) {
      nextTick(() => {
        resetForm()
        if (props.formData) {
          Object.assign(form, props.formData)
        }
      })
    }
  },
  { immediate: true }
)

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(form, {
    planCode: '',
    planName: '',
    planType: '',
    departmentId: '',
    departmentName: '',
    positionCategory: '',
    recruitmentReason: '',
    totalPositions: 1,
    priorityLevel: 'MEDIUM',
    startDate: '',
    endDate: '',
    budgetAmount: 0,
    description: '',
    requirements: '',
    planStatus: 'DRAFT',
    approvalStatus: 'PENDING'
  })
}

// 部门变化处理
const handleDepartmentChange = (departmentId: string) => {
  const department = departments.value.find(d => d.id === departmentId)
  if (department) {
    form.departmentName = department.name
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    // 验证日期范围
    if (form.startDate >= form.endDate) {
      ElMessage.error('结束日期必须大于开始日期')
      return
    }

    loading.value = true

    if (props.isEdit && form.id) {
      // 更新
      await recruitmentPlanApi.update(form.id, form)
      ElMessage.success('更新成功')
    } else {
      // 创建
      await recruitmentPlanApi.create(form)
      ElMessage.success('创建成功')
    }

    emit('success')
  } catch (__error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-textarea__inner) {
  resize: vertical;
}
</style>
