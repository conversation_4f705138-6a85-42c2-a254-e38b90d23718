<template>
  <el-dialog
    v-model="dialogVisible"
    title="招聘计划审批"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <!-- 计划信息 -->
    <div class="plan-info">
      <h3>计划信息</h3>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="计划编码">
          {{ plan?.planCode }}
        </el-descriptions-item>
        <el-descriptions-item label="计划名称">
          {{ plan?.planName }}
        </el-descriptions-item>
        <el-descriptions-item label="部门">
          {{ plan?.departmentName }}
        </el-descriptions-item>
        <el-descriptions-item label="招聘人数">
          {{ plan?.totalPositions }}
        </el-descriptions-item>
        <el-descriptions-item label="预算金额">
          ¥{{ plan?.budgetAmount?.toLocaleString() }}
        </el-descriptions-item>
        <el-descriptions-item label="优先级">
          <el-tag
            :type="getPriorityType(plan?.priorityLevel)"
            size="small"
          >
            {{ getPriorityText(plan?.priorityLevel) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="招聘日期" :span="2">
          {{ plan?.startDate }} 至 {{ plan?.endDate }}
        </el-descriptions-item>
        <el-descriptions-item label="招聘原因" :span="2">
          {{ plan?.recruitmentReason }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 审批表单 -->
    <div class="approval-form">
      <h3>审批意见</h3>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="审批结果" prop="approved">
          <el-radio-group v-model="form.approved">
            <el-radio :label="true">
              <el-icon color="#67c23a"><Check /></el-icon>
              通过
            </el-radio>
            <el-radio :label="false">
              <el-icon color="#f56c6c"><Close /></el-icon>
              拒绝
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="审批意见" prop="comment">
          <el-input
            v-model="form.comment"
            type="textarea"
            :rows="4"
            placeholder="请输入审批意见"
            maxlength="500"
            show-word-limit
            />
        </el-form-item>

        <el-form-item v-if="form.approved === false" label="拒绝原因" prop="rejectionReason">
          <el-select
            v-model="form.rejectionReason"
            placeholder="请选择拒绝原因"
            style="width: 100%"
          >
            <el-option label="预算不足" value="BUDGET_INSUFFICIENT"  />
            <el-option label="岗位设置不合理" value="POSITION_UNREASONABLE"  />
            <el-option label="招聘时间不当" value="TIMING_INAPPROPRIATE"  />
            <el-option label="需求不明确" value="REQUIREMENT_UNCLEAR"  />
            <el-option label="其他原因" value="OTHER"  />
          </el-select>
        </el-form-item>

        <el-form-item v-if="form.approved === true" label="特殊要求">
          <el-input
            v-model="form.specialRequirements"
            type="textarea"
            :rows="3"
            placeholder="如有特殊要求请填写（可选）"
            maxlength="300"
            show-word-limit
            />
        </el-form-item>
      </el-form>
    </div>

    <!-- 审批历史 -->
    <div v-if="approvalHistory.length > 0" class="approval-history">
      <h3>审批历史</h3>
      <el-timeline>
        <el-timeline-item
          v-for="item in approvalHistory"
          :key="item.id"
          :timestamp="item.timestamp"
          :type="item.type"
        >
          <div class="history-item">
            <div class="history-header">
              <span class="approver">{{ item.approver }}</span>
              <el-tag :type="item.result === 'APPROVED' ? 'success' : 'danger'" size="small">
                {{ item.result === 'APPROVED' ? '通过' : '拒绝' }}
              </el-tag>
            </div>
            <div class="history-comment">{{ item.comment }}</div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          提交审批
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'ApprovalDialog'
})
 
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Check, Close } from '@element-plus/icons-vue'
import { recruitmentPlanApi, offerApi, type RecruitmentPlan } from '@/api/recruitment'

// Props
interface Props {
  visible: boolean
  plan?: RecruitmentPlan | null
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  plan: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const form = reactive({
  approved: true,
  comment: '',
  rejectionReason: '',
  specialRequirements: ''
})

// 审批历史
const approvalHistory = ref([
  {
    id: 1,
    approver: '张三',
    result: 'APPROVED',
    comment: '计划合理，同意执行',
    timestamp: '2024-06-18 14:30',
    type: 'success'
  }
])

// 表单验证规则
const rules: FormRules = {
  approved: [
    { required: true, message: '请选择审批结果', trigger: 'change' }
  ],
  comment: [
    { required: true, message: '请输入审批意见', trigger: 'blur' },
    { min: 5, max: 500, message: '长度在 5 到 500 个字符', trigger: 'blur' }
  ],
  rejectionReason: [
    {
      required: true,
      message: '请选择拒绝原因',
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (form.approved === false && !value) {
          callback(new Error('请选择拒绝原因'))
        } else {
          callback()
        }
      }
    }
  ]
}

// 监听visible变化
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal
    if (newVal) {
      nextTick(() => {
        resetForm()
        loadApprovalHistory()
      })
    }
  },
  { immediate: true }
)

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(form, {
    approved: true,
    comment: '',
    rejectionReason: '',
    specialRequirements: ''
  })
}

// 加载审批历史
const loadApprovalHistory = async () => {
  try {
    if (!props.plan?.id) return
    
    // 调用API获取审批历史
    const history = await offerApi.getApprovalHistory(props.plan.id)
    approvalHistory.value = history || []
  } catch (__error) {
    console.error('加载审批历史失败:', error)
    ElMessage.error('加载审批历史失败')
  }
}

// 获取优先级类型
const getPriorityType = (priority?: string) => {
  const typeMap: Record<string, string> = {
    HIGH: 'danger',
    MEDIUM: 'warning',
    LOW: 'info'
  }
  return typeMap[priority || ''] || 'info'
}

// 获取优先级文本
const getPriorityText = (priority?: string) => {
  const textMap: Record<string, string> = {
    HIGH: '高',
    MEDIUM: '中',
    LOW: '低'
  }
  return textMap[priority || ''] || priority
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}

// 提交审批
const handleSubmit = async () => {
  if (!formRef.value || !props.plan?.id) return

  try {
    await formRef.value.validate()

    loading.value = true

    // 获取当前用户ID（从localStorage或状态管理获取）
    const currentUserId = localStorage.getItem('userId') || 'default-user'
    
    await recruitmentPlanApi.approve(
      props.plan.id,
      currentUserId,
      form.approved,
      form.comment
    )

    ElMessage.success('审批提交成功')
    emit('success')
  } catch (__error) {
    console.error('审批提交失败:', error)
    ElMessage.error('审批提交失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.plan-info,
.approval-form,
.approval-history {
  margin-bottom: 24px;
}

.plan-info h3,
.approval-form h3,
.approval-history h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.dialog-footer {
  text-align: right;
}

.history-item {
  padding: 8px 0;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.approver {
  font-weight: 500;
  color: #303133;
}

.history-comment {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

:deep(.el-descriptions__label) {
  font-weight: 500;
}

:deep(.el-radio) {
  margin-right: 24px;
}

:deep(.el-radio__label) {
  display: flex;
  align-items: center;
  gap: 4px;
}
</style>
