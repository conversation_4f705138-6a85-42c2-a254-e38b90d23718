<template>
  <div class="resume-management">
    <!-- 搜索栏 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="应聘职位">
          <el-select 
            v-model="searchForm.jobPostId" 
            clearable 
            placeholder="请选择职位"
            style="width: 200px"
          >
            <el-option 
              v-for="post in jobPosts" 
              :key="post.id" 
              :label="post.title" 
              :value="post.id" 
             />
          </el-select>
        </el-form-item>
        <el-form-item label="姓名/电话">
          <el-input 
            v-model="searchForm.keyword" 
            clearable 
            placeholder="姓名或电话"
            style="width: 200px"
            />
        </el-form-item>
        <el-form-item label="学历">
          <el-select 
            v-model="searchForm.education" 
            clearable 
            placeholder="请选择"
            style="width: 120px"
          >
            <el-option label="大专" value="大专"  />
            <el-option label="本科" value="本科"  />
            <el-option label="硕士" value="硕士"  />
            <el-option label="博士" value="博士"  />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select 
            v-model="searchForm.status" 
            clearable 
            placeholder="请选择"
            style="width: 120px"
          >
            <el-option label="待筛选" value="待筛选"  />
            <el-option label="初筛通过" value="初筛通过"  />
            <el-option label="复筛通过" value="复筛通过"  />
            <el-option label="面试中" value="面试中"  />
            <el-option label="已录用" value="已录用"  />
            <el-option label="已拒绝" value="已拒绝"  />
          </el-select>
        </el-form-item>
        <el-form-item label="来源">
          <el-select 
            v-model="searchForm.source" 
            clearable 
            placeholder="请选择"
            style="width: 120px"
          >
            <el-option label="网申" value="网申"  />
            <el-option label="内推" value="内推"  />
            <el-option label="猎头" value="猎头"  />
            <el-option label="校招" value="校招"  />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作栏 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>简历列表</span>
          <div>
            <el-button @click="handleBatchScreen('初筛通过')">
              批量通过
            </el-button>
            <el-button @click="handleBatchScreen('已拒绝')">
              批量拒绝
            </el-button>
            <el-button @click="handleAddToTalentPool">
              加入人才库
            </el-button>
            <el-button @click="handleImport">
              <el-icon><Upload /></el-icon>
              导入简历
            </el-button>
            <el-button type="primary" @click="handleExport">
              <el-icon><Download /></el-icon>
              导出简历
            </el-button>
          </div>
        </div>
      </template>

      <!-- 数据表格 -->
      <el-table 
        v-loading="loading" 
        :data="tableData" 
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column label="基本信息" min-width="200">
          <template #default="{ row }">
            <div class="applicant-info">
              <el-avatar :src="row.photo" :size="40">
                {{ row.name.charAt(0) }}
              </el-avatar>
              <div class="info-text">
                <div class="name">
                  {{ row.name }}
                  <el-tag size="small" style="margin-left: 5px">{{ row.gender }}</el-tag>
                </div>
                <div class="contact">
                  {{ row.phone }} | {{ row.email }}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="jobPostTitle" label="应聘职位" width="150"  />
        <el-table-column prop="education" label="学历" width="80">
          <template #default="{ row }">
            {{ row.education[0]?.degree || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="工作经验" width="100">
          <template #default="{ row }">
            {{ calculateWorkYears(row.workExperience) }}年
          </template>
        </el-table-column>
        <el-table-column prop="currentLocation" label="现居地" width="100"  />
        <el-table-column label="期望薪资" width="120">
          <template #default="{ row }">
            {{ row.expectedSalaryMin }}-{{ row.expectedSalaryMax }}K
          </template>
        </el-table-column>
        <el-table-column prop="source" label="来源" width="80">
          <template #default="{ row }">
            <el-tag size="small" :type="getSourceTag(row.source)">
              {{ row.source }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusTag(row.status)">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="AI分析" width="100" align="center">
          <template #default="{ row }">
            <div v-if="row.aiParsed" class="ai-info">
              <el-progress 
                :percentage="row.aiMatchScore || 0" 
                :width="60"
                type="circle"
                :color="getScoreColor(row.aiMatchScore)"
               />
            </div>
            <el-button 
              v-else 
              link 
              type="primary" 
              size="small"
              @click="handleAIParse(row)"
            >
              分析
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="投递时间" width="160"  />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleView(row)">查看</el-button>
            <el-button link type="primary" @click="handleScore(row)">评分</el-button>
            <el-dropdown style="margin-left: 10px">
              <el-button link type="primary">
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleScheduleInterview(row)">
                    安排面试
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleAIMatch(row)">
                    AI匹配岗位
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleAddToTalentPool([row])">
                    加入人才库
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleUpdateStatus(row)">
                    更新状态
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 简历详情抽屉 -->
    <el-drawer 
      v-model="detailDrawerVisible" 
      :title="`${detailData.name}的简历`" 
      size="800px"
    >
      <resume-detail :resume="detailData" />
    </el-drawer>

    <!-- 评分对话框 -->
    <el-dialog 
      v-model="scoreDialogVisible" 
      title="简历评分" 
      width="600px"
    >
      <el-form :model="scoreForm" label-width="100px">
        <el-form-item 
          v-for="dimension in scoreDimensions" 
          :key="dimension.key"
          :label="dimension.label"
        >
          <el-rate 
            v-model="scoreForm.dimensions[dimension.key]" 
            :max="10"
            show-score
            score-template="{value}分"
           />
        </el-form-item>
        <el-form-item label="综合评价">
          <el-input 
            v-model="scoreForm.comment" 
            type="textarea" 
            :rows="4" 
            placeholder="请输入综合评价意见"
            />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="scoreDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleScoreSubmit">提交</el-button>
      </template>
    </el-dialog>

    <!-- 状态更新对话框 -->
    <el-dialog 
      v-model="statusDialogVisible" 
      title="更新状态" 
      width="500px"
    >
      <el-form :model="statusForm" label-width="100px">
        <el-form-item label="新状态">
          <el-select v-model="statusForm.status" placeholder="请选择">
            <el-option label="待筛选" value="待筛选"  />
            <el-option label="初筛通过" value="初筛通过"  />
            <el-option label="复筛通过" value="复筛通过"  />
            <el-option label="面试中" value="面试中"  />
            <el-option label="已录用" value="已录用"  />
            <el-option label="已拒绝" value="已拒绝"  />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input 
            v-model="statusForm.comment" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入备注"
            />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="statusDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleStatusSubmit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog 
      v-model="importDialogVisible" 
      title="导入简历" 
      width="500px"
    >
      <el-upload
        ref="uploadRef"
        class="upload-demo"
        drag
        :action="uploadAction"
        :before-upload="beforeUpload"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        accept=".xlsx,.xls,.pdf,.doc,.docx"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            支持 xlsx、xls、pdf、doc、docx 格式，文件大小不超过10MB
          </div>
        </template>
      </el-upload>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'ResumeManagement'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload, Download, ArrowDown, UploadFilled } from '@element-plus/icons-vue'
import { resumeApi, jobPostApi } from '@/api/recruitment'
import type { Resume, JobPost } from '@/types/recruitment'
import { useRouter, useRoute } from 'vue-router'
import ResumeDetail from './components/ResumeDetail.vue'

const router = useRouter()
const route = useRoute()

// 搜索表单
const searchForm = reactive({
  jobPostId: route.query.postId || '',
  keyword: '',
  education: '',
  status: '',
  source: ''
})

// 表格数据
const loading = ref(false)
const tableData = ref<Resume[]>([])
const selectedRows = ref<Resume[]>([])

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 职位列表
const jobPosts = ref<JobPost[]>([])

// 详情抽屉
const detailDrawerVisible = ref(false)
const detailData = ref<Resume>({} as Resume)

// 评分对话框
const scoreDialogVisible = ref(false)
const currentScoreRow = ref<Resume>()
const scoreForm = reactive({
  dimensions: {
    education: 0,
    experience: 0,
    skills: 0,
    match: 0,
    potential: 0
  },
  comment: ''
})

const scoreDimensions = [
  { key: 'education', label: '教育背景' },
  { key: 'experience', label: '工作经验' },
  { key: 'skills', label: '技能匹配' },
  { key: 'match', label: '岗位契合度' },
  { key: 'potential', label: '发展潜力' }
]

// 状态更新对话框
const statusDialogVisible = ref(false)
const currentStatusRow = ref<Resume>()
const statusForm = reactive({
  status: '',
  comment: ''
})

// 导入对话框
const importDialogVisible = ref(false)
const uploadRef = ref()
const uploadAction = computed(() => '/api/recruitment/resumes/import')

// 标签类型映射
const getSourceTag = (source: string) => {
  const map: Record<string, string> = {
    '网申': '',
    '内推': 'success',
    '猎头': 'warning',
    '校招': 'info'
  }
  return map[source] || 'info'
}

const getStatusTag = (status: string) => {
  const map: Record<string, string> = {
    '待筛选': 'info',
    '初筛通过': '',
    '复筛通过': 'success',
    '面试中': 'warning',
    '已录用': 'success',
    '已拒绝': 'danger'
  }
  return map[status] || 'info'
}

const getScoreColor = (score?: number) => {
  if (!score) return '#909399'
  if (score >= 80) return '#67C23A'
  if (score >= 60) return '#E6A23C'
  return '#F56C6C'
}

// 计算工作年限
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const calculateWorkYears = (experiences?: unknown[]) => {
  if (!experiences || experiences.length === 0) return 0
  
  let totalMonths = 0
  experiences.forEach(exp => {
    const start = new Date(exp.startDate)
    const end = exp.isCurrent ? new Date() : new Date(exp.endDate)
    const months = (end.getFullYear() - start.getFullYear()) * 12 + 
                  (end.getMonth() - start.getMonth())
    totalMonths += months
  })
  
  return Math.round(totalMonths / 12)
}

// 获取列表数据
const fetchData = async () => {
  loading.value = true
  try {
    const {data: _data} =  await resumeApi.getList({
      ...searchForm,
      page: pagination.page,
      size: pagination.size
    })
    tableData.value 
  }
  
  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
  
  .applicant-info {
    display: flex;
    align-items: center;
    gap: 10px;
    
    .info-text {
      .name {
        font-weight: 500;
        margin-bottom: 4px;
      }
      
      .contact {
        font-size: 12px;
        color: #909399;
      }
    }
  }
  
  .ai-info {
    display: flex;
    justify-content: center;
  }
}

.upload-demo {
  :deep(.el-upload-dragger) {
    padding: 40px;
  }
}
</style>