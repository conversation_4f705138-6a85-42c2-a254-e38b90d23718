<template>
  <div class="recruitment-plan-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><Document /></el-icon>
        招聘计划管理
      </h1>
      <div class="page-actions">
        <el-button type="primary" @click="handleCreate">
          <el-icon><Plus /></el-icon>
          新建计划
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="计划名称">
          <el-input
            v-model="searchForm.planName"
            placeholder="请输入计划名称"
            clearable
            style="width: 200px"
            />
        </el-form-item>
        <el-form-item label="部门">
          <el-select
            v-model="searchForm.departmentId"
            placeholder="请选择部门"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="dept in departments"
              :key="dept.id"
              :label="dept.name"
              :value="dept.id"
             />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="草稿" value="DRAFT"  />
            <el-option label="已提交" value="SUBMITTED"  />
            <el-option label="已审批" value="APPROVED"  />
            <el-option label="进行中" value="ACTIVE"  />
            <el-option label="已完成" value="COMPLETED"  />
            <el-option label="已取消" value="CANCELLED"  />
          </el-select>
        </el-form-item>
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
           />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="planCode" label="计划编码" width="120"  />
        <el-table-column prop="planName" label="计划名称" min-width="200" show-overflow-tooltip  />
        <el-table-column prop="departmentName" label="部门" width="150"  />
        <el-table-column prop="totalPositions" label="招聘人数" width="100" align="center"  />
        <el-table-column prop="priorityLevel" label="优先级" width="100" align="center">
          <template #default="{ row }">
            <el-tag
              :type="getPriorityType(row.priorityLevel)"
              size="small"
            >
              {{ getPriorityText(row.priorityLevel) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="planStatus" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.planStatus)"
              size="small"
            >
              {{ getStatusText(row.planStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="startDate" label="开始日期" width="120"  />
        <el-table-column prop="endDate" label="结束日期" width="120"  />
        <el-table-column prop="createTime" label="创建时间" width="160"  />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button
              v-if="canEdit(row)"
              type="text"
              size="small"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="canSubmit(row)"
              type="text"
              size="small"
              @click="handleSubmit(row)"
            >
              提交
            </el-button>
            <el-button
              v-if="canApprove(row)"
              type="text"
              size="small"
              @click="handleApprove(row)"
            >
              审批
            </el-button>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
            <el-dropdown @command="(command: unknown) => handleCommand(command, row)">
              <el-button type="text" size="small">
                更多<el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-if="canActivate(row)"
                    command="activate"
                  >
                    激活
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-if="canComplete(row)"
                    command="complete"
                  >
                    完成
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-if="canCancel(row)"
                    command="cancel"
                  >
                    取消
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-if="canDelete(row)"
                    command="delete"
                    divided
                  >
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 批量操作 -->
    <el-card v-if="selectedRows.length > 0" class="batch-actions">
      <div class="batch-info">
        已选择 {{ selectedRows.length }} 项
      </div>
      <div class="batch-buttons">
        <el-button @click="handleBatchCancel">批量取消</el-button>
        <el-button type="danger" @click="handleBatchDelete">批量删除</el-button>
      </div>
    </el-card>

    <!-- 创建/编辑对话框 -->
    <RecruitmentPlanDialog
      v-model:visible="dialogVisible"
      :form-data="currentPlan"
      :is-edit="isEdit"
      @success="handleDialogSuccess"
    />

    <!-- 审批对话框 -->
    <ApprovalDialog
      v-model:visible="approvalDialogVisible"
      :plan="currentPlan"
      @success="handleApprovalSuccess"
    />
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Document,
  Plus,
  Search,
  Refresh,
  ArrowDown
} from '@element-plus/icons-vue'
import { recruitmentPlanApi, type RecruitmentPlan, PlanStatus } from '@/api/recruitment'
import RecruitmentPlanDialog from './components/RecruitmentPlanDialog.vue'
import HrApprovalDialog from './components/HrApprovalDialog.vue'

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const approvalDialogVisible = ref(false)
const isEdit = ref(false)
const tableData = ref<RecruitmentPlan[]>([])
const selectedRows = ref<RecruitmentPlan[]>([])
const currentPlan = ref<RecruitmentPlan | null>(null)

// 搜索表单
const searchForm = reactive({
  planName: '',
  departmentId: '',
  status: '',
  dateRange: []
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 部门列表
const departments = ref([
  { id: '1', name: 'HrHr计算机学院' },
  { id: '2', name: '机械工程学院' },
  { id: '3', name: '经济管理学院' }
])

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page - 1,
      size: pagination.size,
      ...searchForm
    }
    
    const response = await recruitmentPlanApi.getList(params)
    tableData.value = response.data.list
    pagination.total = response.data.total
  } catch (__error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    planName: '',
    departmentId: '',
    status: '',
    dateRange: []
  })
  handleSearch()
}

// 创建
const handleCreate = () => {
  currentPlan.value = null
  isEdit.value = false
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row: RecruitmentPlan) => {
  currentPlan.value = { ...row }
  isEdit.value = true
  dialogVisible.value = true
}

// 查看
const handleView = (row: RecruitmentPlan) => {
  // 使用路由跳转到详情页
  const router = useRouter()
  router.push({
    name: 'RecruitmentPlanDetail',
    params: { id: row.id },
    query: { mode: 'view' }
  })
}

// 提交审批
const handleSubmit = async (row: RecruitmentPlan) => {
  try {
    await ElMessageBox.confirm('确认提交该招聘计划进行审批？', '提示', {
      type: 'warning'
    })
    
    await recruitmentPlanApi.submitForApproval(row.id!)
    ElMessage.success('提交成功')
    loadData()
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('提交失败')
    }
  }
}

// 审批
const handleApprove = (row: RecruitmentPlan) => {
  currentPlan.value = row
  approvalDialogVisible.value = true
}

// 状态判断方法
const canEdit = (row: RecruitmentPlan) => row.planStatus === PlanStatus.DRAFT
const canSubmit = (row: RecruitmentPlan) => row.planStatus === PlanStatus.DRAFT
const canApprove = (row: RecruitmentPlan) => row.planStatus === PlanStatus.SUBMITTED
const canActivate = (row: RecruitmentPlan) => row.planStatus === PlanStatus.APPROVED
const canComplete = (row: RecruitmentPlan) => row.planStatus === PlanStatus.ACTIVE
const canCancel = (row: RecruitmentPlan) => 
  [PlanStatus.DRAFT, PlanStatus.SUBMITTED, PlanStatus.APPROVED, PlanStatus.ACTIVE].includes(row.planStatus)
const canDelete = (row: RecruitmentPlan) => 
  [PlanStatus.DRAFT, PlanStatus.CANCELLED].includes(row.planStatus)

// 获取状态类型
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    DRAFT: 'info',
    SUBMITTED: 'warning',
    APPROVED: 'success',
    ACTIVE: 'primary',
    COMPLETED: 'success',
    CANCELLED: 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    DRAFT: '草稿',
    SUBMITTED: '已提交',
    APPROVED: '已审批',
    ACTIVE: '进行中',
    COMPLETED: '已完成',
    CANCELLED: '已取消'
  }
  return textMap[status] || status
}

// 获取优先级类型
const getPriorityType = (priority: string) => {
  const typeMap: Record<string, string> = {
    HIGH: 'danger',
    MEDIUM: 'warning',
    LOW: 'info'
  }
  return typeMap[priority] || 'info'
}

// 获取优先级文本
const getPriorityText = (priority: string) => {
  const textMap: Record<string, string> = {
    HIGH: '高',
    MEDIUM: '中',
    LOW: '低'
  }
  return textMap[priority] || priority
}

// 下拉菜单命令处理
const handleCommand = async (command: string, row: RecruitmentPlan) => {
  switch (command) {
    case 'activate':
      await handleActivate(row)
      break
    case 'complete':
      await handleComplete(row)
      break
    case 'cancel':
      await handleCancel(row)
      break
    case 'delete':
      await handleDelete(row)
      break
  }
}

// 激活
const handleActivate = async (row: RecruitmentPlan) => {
  try {
    await ElMessageBox.confirm('确认激活该招聘计划？', '提示', {
      type: 'warning'
    })
    
    await recruitmentPlanApi.batchUpdateStatus([row.id!], 'ACTIVE')
    ElMessage.success('激活成功')
    loadData()
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('激活失败')
    }
  }
}

// 完成
const handleComplete = async (row: RecruitmentPlan) => {
  try {
    await ElMessageBox.confirm('确认完成该招聘计划？', '提示', {
      type: 'warning'
    })
    
    await recruitmentPlanApi.batchUpdateStatus([row.id!], 'COMPLETED')
    ElMessage.success('完成成功')
    loadData()
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('完成失败')
    }
  }
}

// 取消
const handleCancel = async (row: RecruitmentPlan) => {
  try {
    const {value: _value} =  await ElMessageBox.prompt('请输入取消原因', '取消招聘计划', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputValidator: (value) 
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.search-card,
.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.batch-info {
  color: #606266;
  font-size: 14px;
}

.batch-buttons {
  display: flex;
  gap: 12px;
}
</style>
