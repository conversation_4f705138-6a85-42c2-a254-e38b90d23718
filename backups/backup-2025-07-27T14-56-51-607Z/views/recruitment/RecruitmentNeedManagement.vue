<template>
  <div class="recruitment-need-management">
    <!-- 搜索栏 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="部门">
          <el-select 
            v-model="searchForm.departmentId" 
            clearable 
            placeholder="请选择部门"
            style="width: 200px"
          >
            <el-option 
              v-for="dept in departments" 
              :key="dept.id" 
              :label="dept.name" 
              :value="dept.id" 
             />
          </el-select>
        </el-form-item>
        <el-form-item label="岗位">
          <el-input 
            v-model="searchForm.positionName" 
            clearable 
            placeholder="请输入岗位名称"
            style="width: 200px"
            />
        </el-form-item>
        <el-form-item label="紧急程度">
          <el-select 
            v-model="searchForm.urgencyLevel" 
            clearable 
            placeholder="请选择"
            style="width: 120px"
          >
            <el-option label="高" value="高"  />
            <el-option label="中" value="中"  />
            <el-option label="低" value="低"  />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select 
            v-model="searchForm.status" 
            clearable 
            placeholder="请选择"
            style="width: 120px"
          >
            <el-option label="草稿" value="草稿"  />
            <el-option label="待审批" value="待审批"  />
            <el-option label="已通过" value="已通过"  />
            <el-option label="已拒绝" value="已拒绝"  />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作栏 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>招聘需求列表</span>
          <el-button type="primary" @click="handleCreate">
            <el-icon><Plus /></el-icon>
            新增需求
          </el-button>
        </div>
      </template>

      <!-- 数据表格 -->
      <el-table 
        v-loading="loading" 
        :data="tableData" 
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="departmentName" label="申请部门" min-width="120"  />
        <el-table-column prop="positionName" label="岗位名称" min-width="120"  />
        <el-table-column prop="needCount" label="需求人数" width="90" align="center"  />
        <el-table-column prop="recruitType" label="招聘类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getRecruitTypeTag(row.recruitType)">
              {{ row.recruitType }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="urgencyLevel" label="紧急程度" width="90" align="center">
          <template #default="{ row }">
            <el-tag :type="getUrgencyTag(row.urgencyLevel)">
              {{ row.urgencyLevel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="expectedDate" label="期望到岗日期" width="120"  />
        <el-table-column prop="status" label="状态" width="90" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusTag(row.status)">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="applyUserName" label="申请人" width="100"  />
        <el-table-column prop="applyTime" label="申请时间" width="160"  />
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleView(row)">查看</el-button>
            <el-button 
              v-if="row.status === '草稿'" 
              link 
              type="primary" 
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button 
              v-if="row.status === '草稿'" 
              link 
              type="primary" 
              @click="handleSubmit(row)"
            >
              提交
            </el-button>
            <el-button 
              v-if="row.status === '待审批' && hasApprovePermission" 
              link 
              type="primary" 
              @click="handleApprove(row)"
            >
              审批
            </el-button>
            <el-button 
              v-if="row.status === '已通过'" 
              link 
              type="primary" 
              @click="handlePublish(row)"
            >
              发布职位
            </el-button>
            <el-button 
              v-if="row.status === '草稿'" 
              link 
              type="danger" 
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="dialogTitle" 
      width="800px"
      @close="handleDialogClose"
    >
      <el-form 
        ref="formRef" 
        :model="formData" 
        :rules="formRules" 
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="申请部门" prop="departmentId">
              <el-select 
                v-model="formData.departmentId" 
                placeholder="请选择部门"
                style="width: 100%"
                @change="handleDepartmentChange"
              >
                <el-option 
                  v-for="dept in departments" 
                  :key="dept.id" 
                  :label="dept.name" 
                  :value="dept.id" 
                 />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="岗位名称" prop="positionId">
              <el-select 
                v-model="formData.positionId" 
                placeholder="请选择岗位"
                style="width: 100%"
                :disabled="!formData.departmentId"
              >
                <el-option 
                  v-for="pos in positions" 
                  :key="pos.id" 
                  :label="pos.name" 
                  :value="pos.id" 
                 />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="需求人数" prop="needCount">
              <el-input-number 
                v-model="formData.needCount" 
                :min="1" 
                :max="99" 
                style="width: 100%"
                />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="招聘类型" prop="recruitType">
              <el-select 
                v-model="formData.recruitType" 
                placeholder="请选择"
                style="width: 100%"
              >
                <el-option label="社会招聘" value="社会招聘"  />
                <el-option label="校园招聘" value="校园招聘"  />
                <el-option label="内部招聘" value="内部招聘"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="紧急程度" prop="urgencyLevel">
              <el-select 
                v-model="formData.urgencyLevel" 
                placeholder="请选择"
                style="width: 100%"
              >
                <el-option label="高" value="高"  />
                <el-option label="中" value="中"  />
                <el-option label="低" value="低"  />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="薪资范围" prop="salaryRange">
              <el-input 
                v-model="formData.salaryRange" 
                placeholder="如：8-12K/月"
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="期望到岗日期" prop="expectedDate">
              <el-date-picker
                v-model="formData.expectedDate"
                type="date"
                placeholder="选择日期"
                style="width: 100%"
                value-format="YYYY-MM-DD"
               />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="招聘原因" prop="reason">
          <el-input 
            v-model="formData.reason" 
            type="textarea" 
            :rows="2" 
            placeholder="请说明招聘原因"
            />
        </el-form-item>

        <el-form-item label="岗位职责" prop="jobDescription">
          <el-input 
            v-model="formData.jobDescription" 
            type="textarea" 
            :rows="4" 
            placeholder="请描述岗位职责"
            />
        </el-form-item>

        <el-form-item label="任职要求" prop="requirements">
          <el-input 
            v-model="formData.requirements" 
            type="textarea" 
            :rows="4" 
            placeholder="请描述任职要求"
            />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </template>
    </el-dialog>

    <!-- 审批对话框 -->
    <el-dialog 
      v-model="approveDialogVisible" 
      title="审批招聘需求" 
      width="500px"
    >
      <el-form :model="approveForm" label-width="100px">
        <el-form-item label="审批结果">
          <el-radio-group v-model="approveForm.status">
            <el-radio label="已通过">通过</el-radio>
            <el-radio label="已拒绝">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审批意见">
          <el-input 
            v-model="approveForm.comment" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入审批意见"
            />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="approveDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleApproveSubmit">提交</el-button>
      </template>
    </el-dialog>

    <!-- 详情抽屉 -->
    <el-drawer 
      v-model="detailDrawerVisible" 
      title="招聘需求详情" 
      size="600px"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="申请部门">
          {{ detailData.departmentName }}
        </el-descriptions-item>
        <el-descriptions-item label="岗位名称">
          {{ detailData.positionName }}
        </el-descriptions-item>
        <el-descriptions-item label="需求人数">
          {{ detailData.needCount }}
        </el-descriptions-item>
        <el-descriptions-item label="现有人数">
          {{ detailData.currentCount || 0 }}
        </el-descriptions-item>
        <el-descriptions-item label="招聘类型">
          {{ detailData.recruitType }}
        </el-descriptions-item>
        <el-descriptions-item label="紧急程度">
          <el-tag :type="getUrgencyTag(detailData.urgencyLevel)">
            {{ detailData.urgencyLevel }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="薪资范围">
          {{ detailData.salaryRange }}
        </el-descriptions-item>
        <el-descriptions-item label="期望到岗日期">
          {{ detailData.expectedDate }}
        </el-descriptions-item>
        <el-descriptions-item label="状态" :span="2">
          <el-tag :type="getStatusTag(detailData.status)">
            {{ detailData.status }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="招聘原因" :span="2">
          {{ detailData.reason }}
        </el-descriptions-item>
        <el-descriptions-item label="岗位职责" :span="2">
          <div style="white-space: pre-wrap;">{{ detailData.jobDescription }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="任职要求" :span="2">
          <div style="white-space: pre-wrap;">{{ detailData.requirements }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="申请人">
          {{ detailData.applyUserName }}
        </el-descriptions-item>
        <el-descriptions-item label="申请时间">
          {{ detailData.applyTime }}
        </el-descriptions-item>
        <el-descriptions-item 
          v-if="detailData.approveUserName" 
          label="审批人"
        >
          {{ detailData.approveUserName }}
        </el-descriptions-item>
        <el-descriptions-item 
          v-if="detailData.approveTime" 
          label="审批时间"
        >
          {{ detailData.approveTime }}
        </el-descriptions-item>
        <el-descriptions-item 
          v-if="detailData.approveComment" 
          label="审批意见" 
          :span="2"
        >
          {{ detailData.approveComment }}
        </el-descriptions-item>
      </el-descriptions>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { recruitmentNeedApi } from '@/api/recruitment'
import type { RecruitmentNeed } from '@/types/recruitment'
import type { FormInstance, FormRules } from 'element-plus'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

// 搜索表单
const searchForm = reactive({
  departmentId: '',
  positionName: '',
  urgencyLevel: '',
  status: ''
})

// 表格数据
const loading = ref(false)
const tableData = ref<RecruitmentNeed[]>([])
const selectedRows = ref<RecruitmentNeed[]>([])

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 对话框
const dialogVisible = ref(false)
const dialogTitle = ref('新增招聘需求')
const formRef = ref<FormInstance>()
const formData = ref<Partial<RecruitmentNeed>>({
  needCount: 1,
  urgencyLevel: '中',
  recruitType: '社会招聘'
})

// 表单验证规则
const formRules = reactive<FormRules>({
  departmentId: [
    { required: true, message: '请选择申请部门', trigger: 'change' }
  ],
  positionId: [
    { required: true, message: '请选择岗位', trigger: 'change' }
  ],
  needCount: [
    { required: true, message: '请输入需求人数', trigger: 'blur' }
  ],
  recruitType: [
    { required: true, message: '请选择招聘类型', trigger: 'change' }
  ],
  urgencyLevel: [
    { required: true, message: '请选择紧急程度', trigger: 'change' }
  ],
  reason: [
    { required: true, message: '请说明招聘原因', trigger: 'blur' }
  ],
  jobDescription: [
    { required: true, message: '请描述岗位职责', trigger: 'blur' }
  ],
  requirements: [
    { required: true, message: '请描述任职要求', trigger: 'blur' }
  ],
  expectedDate: [
    { required: true, message: '请选择期望到岗日期', trigger: 'change' }
  ]
})

// 审批对话框
const approveDialogVisible = ref(false)
const approveForm = reactive({
  status: '已通过',
  comment: ''
})
const currentApproveRow = ref<RecruitmentNeed>()

// 详情抽屉
const detailDrawerVisible = ref(false)
const detailData = ref<RecruitmentNeed>({} as RecruitmentNeed)

// 部门和岗位数据（模拟）
const departments = ref([
  { id: 1, name: 'HrHr技术部' },
  { id: 2, name: '市场部' },
  { id: 3, name: '人事部' },
  { id: 4, name: '财务部' }
])

const positions = ref<Array<{ id: number; name: string; departmentId: number }>>([])
const allPositions = [
  { id: 1, name: '高级前端工程师', departmentId: 1 },
  { id: 2, name: '后端工程师', departmentId: 1 },
  { id: 3, name: '测试工程师', departmentId: 1 },
  { id: 4, name: '市场经理', departmentId: 2 },
  { id: 5, name: '销售专员', departmentId: 2 },
  { id: 6, name: '人事专员', departmentId: 3 },
  { id: 7, name: '招聘主管', departmentId: 3 },
  { id: 8, name: '会计', departmentId: 4 },
  { id: 9, name: '出纳', departmentId: 4 }
]

// 权限判断
const hasApprovePermission = computed(() => {
  // 根据用户角色判断是否有审批权限
  return userStore.roles?.includes('hr_manager') || userStore.roles?.includes('admin')
})

// 标签类型映射
const getRecruitTypeTag = (type: string) => {
  const map: Record<string, string> = {
    '社会招聘': '',
    '校园招聘': 'success',
    '内部招聘': 'warning'
  }
  return map[type] || 'info'
}

const getUrgencyTag = (level: string) => {
  const map: Record<string, string> = {
    '高': 'danger',
    '中': 'warning',
    '低': 'info'
  }
  return map[level] || 'info'
}

const getStatusTag = (status: string) => {
  const map: Record<string, string> = {
    '草稿': 'info',
    '待审批': 'warning',
    '已通过': 'success',
    '已拒绝': 'danger'
  }
  return map[status] || 'info'
}

// 获取列表数据
const fetchData = async () => {
  loading.value = true
  try {
    const {data: _data} =  await recruitmentNeedApi.getList({
      ...searchForm,
      page: pagination.page,
      size: pagination.size
    })
    tableData.value 
  }
  
  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>