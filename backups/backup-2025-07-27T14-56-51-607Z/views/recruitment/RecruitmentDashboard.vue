<template>
  <div class="recruitment-dashboard">
    <!-- 顶部统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card>
          <div class="stat-item">
            <div class="stat-icon" style="background-color: #409eff">
              <el-icon><document /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats.totalNeeds }}</div>
              <div class="stat-label">招聘需求</div>
              <div class="stat-trend">
                <span :class="stats.needsTrend > 0 ? 'up' : 'down'">
                  {{ stats.needsTrend > 0 ? '+' : '' }}{{ stats.needsTrend }}%
                </span>
                <span class="trend-text">较上月</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card>
          <div class="stat-item">
            <div class="stat-icon" style="background-color: #67c23a">
              <el-icon><user /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats.totalResumes }}</div>
              <div class="stat-label">收到简历</div>
              <div class="stat-trend">
                <span class="up">+{{ stats.resumesTrend }}%</span>
                <span class="trend-text">较上月</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card>
          <div class="stat-item">
            <div class="stat-icon" style="background-color: #e6a23c">
              <el-icon><calendar /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats.totalInterviews }}</div>
              <div class="stat-label">面试安排</div>
              <div class="stat-trend">
                <span class="up">+{{ stats.interviewsTrend }}%</span>
                <span class="trend-text">较上月</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card>
          <div class="stat-item">
            <div class="stat-icon" style="background-color: #f56c6c">
              <el-icon><success-filled /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats.totalHired }}</div>
              <div class="stat-label">成功入职</div>
              <div class="stat-trend">
                <span :class="stats.hiredTrend > 0 ? 'up' : 'down'">
                  {{ stats.hiredTrend > 0 ? '+' : '' }}{{ stats.hiredTrend }}%
                </span>
                <span class="trend-text">较上月</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 招聘漏斗和转化率 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :xs="24" :md="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>招聘漏斗分析</span>
              <el-date-picker
                v-model="funnelDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                style="width: 240px"
                @change="updateFunnelChart"
               />
            </div>
          </template>
          <div id="funnel-chart" style="height: 400px"></div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :md="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>各环节转化率</span>
              <el-button-group size="small">
                <el-button 
                  :type="conversionPeriod === 'week' ? 'primary' : ''"
                  @click="conversionPeriod = 'week'"
                >
                  本周
                </el-button>
                <el-button 
                  :type="conversionPeriod === 'month' ? 'primary' : ''"
                  @click="conversionPeriod = 'month'"
                >
                  本月
                </el-button>
                <el-button 
                  :type="conversionPeriod === 'quarter' ? 'primary' : ''"
                  @click="conversionPeriod = 'quarter'"
                >
                  本季
                </el-button>
              </el-button-group>
            </div>
          </template>
          <div id="conversion-chart" style="height: 400px"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 部门需求和招聘进度 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :xs="24" :md="16">
        <el-card>
          <template #header>
            <span>部门招聘进度</span>
          </template>
          <el-table :data="departmentProgress" style="width: 100%">
            <el-table-column prop="department" label="部门" width="150"  />
            <el-table-column prop="position" label="职位" width="150"  />
            <el-table-column prop="needCount" label="需求人数" width="100" align="center"  />
            <el-table-column prop="resumeCount" label="收到简历" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="row.resumeCount >= row.needCount * 3 ? 'success' : 'warning'">
                  {{ row.resumeCount }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="interviewCount" label="面试人数" width="100" align="center"  />
            <el-table-column prop="hiredCount" label="已入职" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="row.hiredCount >= row.needCount ? 'success' : ''">
                  {{ row.hiredCount }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="完成进度" width="200">
              <template #default="{ row }">
                <el-progress 
                  :percentage="Math.round((row.hiredCount / row.needCount) * 100)"
                  :status="row.hiredCount >= row.needCount ? 'success' : ''"
                />
              </template>
            </el-table-column>
            <el-table-column label="预计完成" width="120">
              <template #default="{ row }">
                {{ row.estimatedDate }}
              </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" width="100">
              <template #default="{ row }">
                <el-button link type="primary" @click="viewDetail(row)">查看详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :md="8">
        <el-card>
          <template #header>
            <span>招聘渠道效果</span>
          </template>
          <div id="channel-chart" style="height: 360px"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 实时动态和待办事项 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :xs="24" :md="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>实时动态</span>
              <el-button link type="primary" @click="viewAllActivities">
                查看全部
                <el-icon><arrow-right /></el-icon>
              </el-button>
            </div>
          </template>
          <el-timeline>
            <el-timeline-item
              v-for="activity in recentActivities"
              :key="activity.id"
              :timestamp="activity.time"
              :type="getActivityType(activity.type)"
              placement="top"
            >
              <div class="activity-content">
                <p>{{ activity.content }}</p>
                <el-button 
                  v-if="activity.actionable" 
                  link 
                  type="primary" 
                  size="small"
                  @click="handleAction(activity)"
                >
                  {{ activity.actionText }}
                </el-button>
              </div>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :md="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>待办事项</span>
              <el-tag type="danger">{{ todoList.length }}</el-tag>
            </div>
          </template>
          <div class="todo-list">
            <div v-for="todo in todoList" :key="todo.id" class="todo-item">
              <div class="todo-icon" :style="{ backgroundColor: getTodoColor(todo.type) }">
                <el-icon><component :is="getTodoIcon(todo.type)" /></el-icon>
              </div>
              <div class="todo-content">
                <div class="todo-title">{{ todo.title }}</div>
                <div class="todo-desc">{{ todo.description }}</div>
                <div class="todo-time">
                  <el-icon><clock /></el-icon>
                  {{ todo.deadline }}
                </div>
              </div>
              <div class="todo-action">
                <el-button type="primary" size="small" @click="handleTodo(todo)">
                  处理
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 招聘周期分析 -->
    <el-card class="charts-row">
      <template #header>
        <div class="card-header">
          <span>平均招聘周期分析</span>
          <el-select v-model="cycleAnalysisType" size="small" style="width: 120px">
            <el-option label="按部门" value="department"  />
            <el-option label="按职位" value="position"  />
            <el-option label="按级别" value="level"  />
          </el-select>
        </div>
      </template>
      <div id="cycle-chart" style="height: 350px"></div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import { 
  Document, 
  User, 
  Calendar, 
  SuccessFilled,
  ArrowRight,
  Clock,
  Warning,
  Bell,
  Message
} from '@element-plus/icons-vue'
import { recruitmentStatisticsApi, recruitmentNeedApi } from '@/api/recruitment'
import dayjs from 'dayjs'

const router = useRouter()

// 统计数据
const stats = reactive({
  totalNeeds: 0,
  needsTrend: 0,
  totalResumes: 0,
  resumesTrend: 0,
  totalInterviews: 0,
  interviewsTrend: 0,
  totalHired: 0,
  hiredTrend: 0
})

// 图表实例
let funnelChart: echarts.ECharts | null = null
let conversionChart: echarts.ECharts | null = null
let channelChart: echarts.ECharts | null = null
let cycleChart: echarts.ECharts | null = null

// 数据筛选
const funnelDateRange = ref<[Date, Date]>([
  new Date(new Date().setMonth(new Date().getMonth() - 1)),
  new Date()
])
const conversionPeriod = ref('month')
const cycleAnalysisType = ref('department')

// 部门招聘进度
const departmentProgress = ref([
  {
    department: '技术部',
    position: '前端工程师',
    needCount: 5,
    resumeCount: 23,
    interviewCount: 12,
    hiredCount: 3,
    estimatedDate: '2025-02-15'
  },
  {
    department: '技术部',
    position: 'Java工程师',
    needCount: 8,
    resumeCount: 45,
    interviewCount: 18,
    hiredCount: 5,
    estimatedDate: '2025-02-20'
  },
  {
    department: '产品部',
    position: '产品经理',
    needCount: 3,
    resumeCount: 15,
    interviewCount: 8,
    hiredCount: 2,
    estimatedDate: '2025-01-30'
  },
  {
    department: '市场部',
    position: '市场专员',
    needCount: 4,
    resumeCount: 28,
    interviewCount: 10,
    hiredCount: 4,
    estimatedDate: '2025-01-25'
  },
  {
    department: '人事部',
    position: 'HR专员',
    needCount: 2,
    resumeCount: 12,
    interviewCount: 5,
    hiredCount: 1,
    estimatedDate: '2025-02-10'
  }
])

// 实时动态
const recentActivities = ref([
  {
    id: 1,
    type: 'resume',
    content: '收到新简历：张三 应聘 前端工程师',
    time: '10分钟前',
    actionable: true,
    actionText: '查看简历'
  },
  {
    id: 2,
    type: 'interview',
    content: '李四 的面试即将开始（14:00）',
    time: '30分钟前',
    actionable: true,
    actionText: '查看详情'
  },
  {
    id: 3,
    type: 'offer',
    content: '王五 已接受offer，准备入职',
    time: '2小时前',
    actionable: false
  },
  {
    id: 4,
    type: 'need',
    content: '技术部发布新需求：测试工程师 2名',
    time: '3小时前',
    actionable: true,
    actionText: '发布职位'
  },
  {
    id: 5,
    type: 'system',
    content: '本周面试通过率：68%，环比上升5%',
    time: '1天前',
    actionable: false
  }
])

// 待办事项
const todoList = ref([
  {
    id: 1,
    type: 'interview',
    title: '安排面试',
    description: '有3位候选人等待安排面试时间',
    deadline: '今天 18:00前'
  },
  {
    id: 2,
    type: 'feedback',
    title: '面试反馈',
    description: '昨天的2场面试需要填写反馈',
    deadline: '今天 12:00前'
  },
  {
    id: 3,
    type: 'offer',
    title: '发送Offer',
    description: '张三的offer审批已通过，需要发送',
    deadline: '明天 18:00前'
  },
  {
    id: 4,
    type: 'report',
    title: '周报提交',
    description: '本周招聘数据汇总报告',
    deadline: '本周五 17:00前'
  }
])

// 获取活动类型
const getActivityType = (type: string) => {
  const typeMap: Record<string, string> = {
    resume: 'success',
    interview: 'warning',
    offer: 'primary',
    need: 'info',
    system: ''
  }
  return typeMap[type] || ''
}

// 获取待办图标
const getTodoIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    interview: 'Calendar',
    feedback: 'Message',
    offer: 'SuccessFilled',
    report: 'Document'
  }
  return iconMap[type] || 'Bell'
}

// 获取待办颜色
const getTodoColor = (type: string) => {
  const colorMap: Record<string, string> = {
    interview: '#e6a23c',
    feedback: '#409eff',
    offer: '#67c23a',
    report: '#909399'
  }
  return colorMap[type] || '#409eff'
}

// 初始化漏斗图
const initFunnelChart = () => {
  const chartDom = document.getElementById('funnel-chart')
  if (!chartDom) return
  
  funnelChart = echarts.init(chartDom)
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c} ({d}%)'
    },
    series: [
      {
        name: 'HrHr招聘漏斗',
        type: 'funnel',
        left: '10%',
        top: 60,
        bottom: 60,
        width: '80%',
        min: 0,
        max: 100,
        minSize: '0%',
        maxSize: '100%',
        sort: 'descending',
        gap: 2,
        label: {
          show: true,
          position: 'inside'
        },
        labelLine: {
          length: 10,
          lineStyle: {
            width: 1,
            type: 'solid'
          }
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1
        },
        emphasis: {
          label: {
            fontSize: 20
          }
        },
        data: [
          { value: 100, name: '收到简历 (326)' },
          { value: 80, name: '通过筛选 (261)' },
          { value: 60, name: '面试邀约 (196)' },
          { value: 40, name: '参加面试 (130)' },
          { value: 20, name: '面试通过 (65)' },
          { value: 10, name: '发送Offer (33)' },
          { value: 7, name: '成功入职 (23)' }
        ]
      }
    ]
  }
  
  funnelChart.setOption(option)
}

// 初始化转化率图
const initConversionChart = () => {
  const chartDom = document.getElementById('conversion-chart')
  if (!chartDom) return
  
  conversionChart = echarts.init(chartDom)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: '{b}: {c}%'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      max: 100,
      axisLabel: {
        formatter: '{value}%'
      }
    },
    yAxis: {
      type: 'category',
      data: [
        '入职/Offer',
        'Offer/面试通过',
        '面试通过/面试',
        '面试/邀约',
        '邀约/筛选',
        '筛选/简历'
      ]
    },
    series: [
      {
        name: '转化率',
        type: 'bar',
        data: [70, 51, 50, 66, 75, 80],
        itemStyle: {
          normal: {
   
            color: function(params: unknown) {
              const colorList = [
                '#67C23A', '#67C23A', '#E6A23C', 
                '#E6A23C', '#409EFF', '#409EFF'
              ]
              return colorList[params.dataIndex]
            }
          }
        },
        label: {
          show: true,
          position: 'right',
          formatter: '{c}%'
        }
      }
    ]
  }
  
  conversionChart.setOption(option)
}

// 初始化渠道图
const initChannelChart = () => {
  const chartDom = document.getElementById('channel-chart')
  if (!chartDom) return
  
  channelChart = echarts.init(chartDom)
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      bottom: '5%',
      left: 'center'
    },
    series: [
      {
        name: '招聘渠道',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '20',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 128, name: '智联招聘' },
          { value: 89, name: '前程无忧' },
          { value: 67, name: 'BOSS直聘' },
          { value: 34, name: '内部推荐' },
          { value: 8, name: '校园招聘' }
        ]
      }
    ]
  }
  
  channelChart.setOption(option)
}

// 初始化周期图
const initCycleChart = () => {
  const chartDom = document.getElementById('cycle-chart')
  if (!chartDom) return
  
  cycleChart = echarts.init(chartDom)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: '{b}: {c}天'
    },
    legend: {
      data: ['发布到简历', '简历到面试', '面试到Offer', 'Offer到入职']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['技术部', '产品部', '市场部', '人事部', '财务部']
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}天'
      }
    },
    series: [
      {
        name: '发布到简历',
        type: 'bar',
        stack: '总量',
        data: [3, 4, 2, 3, 5]
      },
      {
        name: '简历到面试',
        type: 'bar',
        stack: '总量',
        data: [5, 6, 4, 5, 7]
      },
      {
        name: '面试到Offer',
        type: 'bar',
        stack: '总量',
        data: [7, 8, 6, 7, 9]
      },
      {
        name: 'Offer到入职',
        type: 'bar',
        stack: '总量',
        data: [10, 12, 8, 10, 14]
      }
    ]
  }
  
  cycleChart.setOption(option)
}

// 更新漏斗图
const updateFunnelChart = async () => {
  try {
    const params = {
      startDate: funnelDateRange.value[0] ? dayjs(funnelDateRange.value[0]).format('YYYY-MM-DD') : undefined,
      endDate: funnelDateRange.value[1] ? dayjs(funnelDateRange.value[1]).format('YYYY-MM-DD') : undefined
    }
    
    const data = await recruitmentStatisticsApi.getFunnel(params)
    
    if (funnelChart && data) {
      funnelChart.setOption({
        series: [{
          data: [
            { value: 100, name: `收到简历 (${data.resumeCount || 0})` },
            { value: data.screenedRate || 0, name: `通过筛选 (${data.screenedCount || 0})` },
            { value: data.invitedRate || 0, name: `面试邀约 (${data.invitedCount || 0})` },
            { value: data.interviewedRate || 0, name: `参加面试 (${data.interviewedCount || 0})` },
            { value: data.passedRate || 0, name: `面试通过 (${data.passedCount || 0})` },
            { value: data.offeredRate || 0, name: `发送Offer (${data.offeredCount || 0})` },
            { value: data.hiredRate || 0, name: `成功入职 (${data.hiredCount || 0})` }
          ]
        }]
      })
    }
  } catch (__error) {
    console.error('获取漏斗数据失败:', error)
  }
}

// 查看详情
   
const viewDetail = (row: unknown) => {
  router.push({
    name: 'RecruitmentNeedDetail',
    params: { id: row.id }
  })
}

// 查看所有动态
const viewAllActivities = () => {
  router.push({ name: 'RecruitmentActivities' })
}

// 处理动态操作
   
const handleAction = (activity: unknown) => {
  switch (activity.type) {
    case 'resume':
      router.push({
        name: 'ResumeDetail',
        params: { id: activity.resumeId || activity.id }
      })
      break
    case 'interview':
      router.push({
        name: 'InterviewSchedule',
        query: { candidateId: activity.candidateId }
      })
      break
    case 'offer':
      router.push({
        name: 'OfferManagement',
        query: { candidateId: activity.candidateId }
      })
      break
    case 'need':
      router.push({
        name: 'JobPostManagement',
        query: { needId: activity.needId }
      })
      break
    default:
      ElMessage.info('暂无相关页面')
  }
}

// 处理待办
   
const handleTodo = (todo: unknown) => {
  switch (todo.type) {
    case 'interview':
      router.push({
        name: 'InterviewSchedule',
        query: { status: 'pending' }
      })
      break
    case 'feedback':
      router.push({
        name: 'InterviewEvaluation',
        query: { status: 'pending' }
      })
      break
    case 'offer':
      router.push({
        name: 'OfferApproval',
        query: { status: 'pending' }
      })
      break
    case 'report':
      router.push({
        name: 'RecruitmentReport',
        query: { type: 'weekly' }
      })
      break
    default:
      ElMessage.info('正在开发中')
  }
}

// 监听周期分析类型变化
watch(cycleAnalysisType, () => {
  loadCycleData()
})

// 加载统计概览数据
const loadSummaryData = async () => {
  try {
    const data = await recruitmentStatisticsApi.getSummary()
    
    if (data) {
      stats.totalNeeds = data.totalNeeds || 0
      stats.needsTrend = data.needsTrend || 0
      stats.totalResumes = data.totalResumes || 0
      stats.resumesTrend = data.resumesTrend || 0
      stats.totalInterviews = data.totalInterviews || 0
      stats.interviewsTrend = data.interviewsTrend || 0
      stats.totalHired = data.totalHired || 0
      stats.hiredTrend = data.hiredTrend || 0
    }
  } catch (__error) {
    console.error('获取统计概览数据失败:', error)
  }
}

// 加载部门招聘进度数据
const loadDepartmentProgress = async () => {
  try {
    const params = {
      page: 1,
      pageSize: 10,
      status: 'active'
    }
    
    const response = await recruitmentNeedApi.getList(params)
    
    if (response && response.list) {
      departmentProgress.value = response.list.map(item => ({
        department: item.departmentName || '',
        position: item.positionName || '',
        needCount: item.needCount || 0,
        resumeCount: item.resumeCount || 0,
        interviewCount: item.interviewCount || 0,
        hiredCount: item.hiredCount || 0,
        estimatedDate: item.estimatedDate || ''
      }))
    }
  } catch (__error) {
    console.error('获取部门招聘进度失败:', error)
  }
}

// 加载周期分析数据
const loadCycleData = async () => {
  try {
    const params = {
      type: cycleAnalysisType.value
    }
    
    const data = await recruitmentStatisticsApi.getCycle(params)
    
    if (cycleChart && data) {
      cycleChart.setOption({
        xAxis: {
          data: data.categories || []
        },
        series: [
          { data: data.postToResume || [] },
          { data: data.resumeToInterview || [] },
          { data: data.interviewToOffer || [] },
          { data: data.offerToHire || [] }
        ]
      })
    }
  } catch (__error) {
    console.error('获取周期分析数据失败:', error)
  }
}

// 加载渠道效果数据
const loadChannelData = async () => {
  try {
    const data = await recruitmentStatisticsApi.getChannel()
    
    if (channelChart && data) {
      channelChart.setOption({
        series: [{
          data: data.channels?.map(item => ({
            value: item.count || 0,
            name: item.name || ''
          })) || []
        }]
      })
    }
  } catch (__error) {
    console.error('获取渠道效果数据失败:', error)
  }
}

// 监听转化率周期变化
watch(conversionPeriod, () => {
  // 更新转化率图表数据
  updateConversionChart()
})

// 更新转化率图表
const updateConversionChart = async () => {
  try {
    const params = {
      period: conversionPeriod.value
    }
    
    const data = await recruitmentStatisticsApi.getSummary(params)
    
    if (conversionChart && data && data.conversion) {
      conversionChart.setOption({
        series: [{
          data: [
            data.conversion.hireToOffer || 0,
            data.conversion.offerToPass || 0,
            data.conversion.passToInterview || 0,
            data.conversion.interviewToInvite || 0,
            data.conversion.inviteToScreen || 0,
            data.conversion.screenToResume || 0
          ]
        }]
      })
    }
  } catch (__error) {
    console.error('获取转化率数据失败:', error)
  }
}

// 窗口大小变化时重新调整图表
const handleResize = () => {
  funnelChart?.resize()
  conversionChart?.resize()
  channelChart?.resize()
  cycleChart?.resize()
}

onMounted(async () => {
  // 初始化图表
  initFunnelChart()
  initConversionChart()
  initChannelChart()
  initCycleChart()
  
  // 加载所有数据
  await Promise.all([
    loadSummaryData(),
    loadDepartmentProgress(),
    updateFunnelChart(),
    updateConversionChart(),
    loadChannelData(),
    loadCycleData()
  ])
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  // 销毁图表实例
  funnelChart?.dispose()
  conversionChart?.dispose()
  channelChart?.dispose()
  cycleChart?.dispose()
  
  // 移除事件监听
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.recruitment-dashboard {
  padding: 20px;
  
  .stats-cards {
    margin-bottom: 20px;
    
    .el-card {
      border-radius: 8px;
      
      :deep(.el-card__body) {
        padding: 20px;
      }
    }
    
    .stat-item {
      display: flex;
      align-items: center;
      
      .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        
        .el-icon {
          font-size: 28px;
          color: #fff;
        }
      }
      
      .stat-content {
        flex: 1;
        
        .stat-value {
          font-size: 28px;
          font-weight: 600;
          color: #303133;
          line-height: 1;
          margin-bottom: 8px;
        }
        
        .stat-label {
          color: #909399;
          font-size: 14px;
          margin-bottom: 5px;
        }
        
        .stat-trend {
          font-size: 12px;
          
          .up {
            color: #67c23a;
          }
          
          .down {
            color: #f56c6c;
          }
          
          .trend-text {
            color: #909399;
            margin-left: 5px;
          }
        }
      }
    }
  }
  
  .charts-row {
    margin-bottom: 20px;
    
    .el-card {
      height: 100%;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
  }
  
  .activity-content {
    p {
      margin: 0 0 5px;
      color: #606266;
    }
  }
  
  .todo-list {
    .todo-item {
      display: flex;
      align-items: center;
      padding: 15px;
      border-radius: 8px;
      background-color: #f5f7fa;
      margin-bottom: 10px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .todo-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        
        .el-icon {
          font-size: 20px;
          color: #fff;
        }
      }
      
      .todo-content {
        flex: 1;
        
        .todo-title {
          font-weight: 500;
          color: #303133;
          margin-bottom: 4px;
        }
        
        .todo-desc {
          font-size: 13px;
          color: #909399;
          margin-bottom: 4px;
        }
        
        .todo-time {
          font-size: 12px;
          color: #c0c4cc;
          display: flex;
          align-items: center;
          gap: 4px;
          
          .el-icon {
            font-size: 14px;
          }
        }
      }
      
      .todo-action {
        margin-left: 12px;
      }
    }
  }
}
</style>