<template>
  <div class="talent-pool-management">
    <!-- 搜索栏 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="关键词">
          <el-input 
            v-model="searchForm.keyword" 
            clearable 
            placeholder="姓名/电话/技能"
            style="width: 200px"
            />
        </el-form-item>
        <el-form-item label="人才分类">
          <el-select 
            v-model="searchForm.category" 
            clearable 
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option label="技术类" value="技术类"  />
            <el-option label="管理类" value="管理类"  />
            <el-option label="营销类" value="营销类"  />
            <el-option label="运营类" value="运营类"  />
            <el-option label="设计类" value="设计类"  />
          </el-select>
        </el-form-item>
        <el-form-item label="人才级别">
          <el-select 
            v-model="searchForm.level" 
            clearable 
            placeholder="请选择"
            style="width: 120px"
          >
            <el-option label="高级" value="高级"  />
            <el-option label="中级" value="中级"  />
            <el-option label="初级" value="初级"  />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select 
            v-model="searchForm.status" 
            clearable 
            placeholder="请选择"
            style="width: 120px"
          >
            <el-option label="活跃" value="活跃"  />
            <el-option label="储备" value="储备"  />
            <el-option label="已入职" value="已入职"  />
          </el-select>
        </el-form-item>
        <el-form-item label="标签">
          <el-select 
            v-model="searchForm.tags" 
            multiple
            clearable 
            placeholder="请选择标签"
            style="width: 200px"
          >
            <el-option 
              v-for="tag in availableTags" 
              :key="tag" 
              :label="tag" 
              :value="tag" 
             />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作栏 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>人才库列表</span>
          <div>
            <el-button @click="handleBatchActivate">
              批量激活
            </el-button>
            <el-button @click="handleBatchSetTags">
              批量设置标签
            </el-button>
            <el-button type="primary" @click="handleAdd">
              <el-icon><Plus /></el-icon>
              添加人才
            </el-button>
          </div>
        </div>
      </template>

      <!-- 数据表格 -->
      <el-table 
        v-loading="loading" 
        :data="tableData" 
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column label="人才信息" min-width="220">
          <template #default="{ row }">
            <div class="talent-info">
              <el-avatar :src="row.resume?.photo" :size="50">
                {{ row.resume?.name.charAt(0) }}
              </el-avatar>
              <div class="info-text">
                <div class="name">
                  {{ row.resume?.name }}
                  <el-tag 
                    :type="getLevelTag(row.level)" 
                    size="small" 
                    style="margin-left: 5px"
                  >
                    {{ row.level }}
                  </el-tag>
                </div>
                <div class="contact">
                  {{ row.resume?.phone }} | {{ row.resume?.email }}
                </div>
                <div class="current">
                  {{ row.resume?.currentCompany }} - {{ row.resume?.currentPosition }}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="category" label="人才分类" width="100">
          <template #default="{ row }">
            <el-tag>{{ row.category }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="核心技能" width="200">
          <template #default="{ row }">
            <el-tag 
              v-for="(skill, index) in row.resume?.skills?.slice(0, 3)" 
              :key="skill"
              size="small"
              style="margin-right: 5px; margin-bottom: 5px"
            >
              {{ skill }}
            </el-tag>
            <span v-if="row.resume?.skills?.length > 3" class="more-text">
              +{{ row.resume.skills.length - 3 }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="标签" width="180">
          <template #default="{ row }">
            <el-tag 
              v-for="tag in row.tags" 
              :key="tag"
              type="info"
              size="small"
              style="margin-right: 5px; margin-bottom: 5px"
            >
              {{ tag }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="source" label="来源" width="80"  />
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusTag(row.status)">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="联系记录" width="120" align="center">
          <template #default="{ row }">
            <div>
              <div>{{ row.contactRecords?.length || 0 }}次</div>
              <div v-if="row.lastContactTime" class="last-contact">
                上次: {{ formatDate(row.lastContactTime) }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="入库时间" width="100">
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleView(row)">查看</el-button>
            <el-button link type="primary" @click="handleContact(row)">联系</el-button>
            <el-button 
              v-if="row.status !== '已入职'" 
              link 
              type="primary" 
              @click="handleActivate(row)"
            >
              激活
            </el-button>
            <el-dropdown style="margin-left: 10px">
              <el-button link type="primary">
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleEdit(row)">
                    编辑信息
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleSetTags(row)">
                    设置标签
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleViewContactHistory(row)">
                    联系历史
                  </el-dropdown-item>
                  <el-dropdown-item 
                    divided 
                    @click="handleRemove(row)"
                    style="color: #f56c6c"
                  >
                    移出人才库
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="dialogTitle" 
      width="700px"
    >
      <el-form 
        ref="formRef" 
        :model="formData" 
        :rules="formRules" 
        label-width="100px"
      >
        <el-form-item label="选择简历" prop="resumeId" v-if="!formData.id">
          <el-select 
            v-model="formData.resumeId" 
            placeholder="请选择简历"
            filterable
            remote
            :remote-method="searchResumes"
            style="width: 100%"
          >
            <el-option 
              v-for="resume in resumeOptions" 
              :key="resume.id" 
              :label="`${resume.name} - ${resume.phone}`" 
              :value="resume.id" 
             />
          </el-select>
        </el-form-item>
        
        <el-form-item label="人才分类" prop="category">
          <el-select v-model="formData.category" placeholder="请选择">
            <el-option label="技术类" value="技术类"  />
            <el-option label="管理类" value="管理类"  />
            <el-option label="营销类" value="营销类"  />
            <el-option label="运营类" value="运营类"  />
            <el-option label="设计类" value="设计类"  />
          </el-select>
        </el-form-item>
        
        <el-form-item label="人才级别" prop="level">
          <el-radio-group v-model="formData.level">
            <el-radio label="高级">高级</el-radio>
            <el-radio label="中级">中级</el-radio>
            <el-radio label="初级">初级</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio label="活跃">活跃</el-radio>
            <el-radio label="储备">储备</el-radio>
            <el-radio label="已入职">已入职</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="标签">
          <el-select 
            v-model="formData.tags" 
            multiple
            filterable
            allow-create
            placeholder="请选择或输入标签"
            style="width: 100%"
          >
            <el-option 
              v-for="tag in availableTags" 
              :key="tag" 
              :label="tag" 
              :value="tag" 
             />
          </el-select>
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input 
            v-model="formData.notes" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入备注信息"
            />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </template>
    </el-dialog>

    <!-- 联系记录对话框 -->
    <el-dialog 
      v-model="contactDialogVisible" 
      title="添加联系记录" 
      width="600px"
    >
      <el-form :model="contactForm" label-width="100px">
        <el-form-item label="联系方式">
          <el-radio-group v-model="contactForm.type">
            <el-radio label="电话">电话</el-radio>
            <el-radio label="邮件">邮件</el-radio>
            <el-radio label="面谈">面谈</el-radio>
            <el-radio label="微信">微信</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="联系内容">
          <el-input 
            v-model="contactForm.content" 
            type="textarea" 
            :rows="4" 
            placeholder="请记录联系内容"
            />
        </el-form-item>
        
        <el-form-item label="联系结果">
          <el-input 
            v-model="contactForm.result" 
            placeholder="如：有意向/考虑中/暂无意向"
            />
        </el-form-item>
        
        <el-form-item label="下次行动">
          <el-input 
            v-model="contactForm.nextAction" 
            placeholder="如：一周后再次联系"
            />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="contactDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleContactSubmit">保存</el-button>
      </template>
    </el-dialog>

    <!-- 标签设置对话框 -->
    <el-dialog 
      v-model="tagDialogVisible" 
      title="设置标签" 
      width="500px"
    >
      <el-select 
        v-model="tagForm.tags" 
        multiple
        filterable
        allow-create
        placeholder="请选择或输入标签"
        style="width: 100%"
      >
        <el-option 
          v-for="tag in availableTags" 
          :key="tag" 
          :label="tag" 
          :value="tag" 
         />
      </el-select>
      
      <template #footer>
        <el-button @click="tagDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleTagSubmit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 联系历史抽屉 -->
    <el-drawer 
      v-model="historyDrawerVisible" 
      title="联系历史" 
      size="600px"
    >
      <el-timeline>
        <el-timeline-item
          v-for="record in contactHistory"
          :key="record.time"
          :timestamp="record.time"
          placement="top"
        >
          <el-card>
            <div class="contact-record">
              <div class="record-header">
                <el-tag size="small">{{ record.type }}</el-tag>
                <span class="operator">{{ record.userName }}</span>
              </div>
              <div class="record-content">
                <p><strong>联系内容：</strong>{{ record.content }}</p>
                <p v-if="record.result"><strong>联系结果：</strong>{{ record.result }}</p>
                <p v-if="record.nextAction"><strong>下次行动：</strong>{{ record.nextAction }}</p>
              </div>
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'TalentPoolManagement'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, ArrowDown } from '@element-plus/icons-vue'
import { talentPoolApi, resumeApi, jobPostApi, talentTrackingApi } from '@/api/recruitment'
import type { TalentPool, Resume, ContactRecord } from '@/types/recruitment'
import type { FormInstance, FormRules } from 'element-plus'
import { useRoute } from 'vue-router'

const route = useRoute()

// 搜索表单
const searchForm = reactive({
  keyword: '',
  category: '',
  level: '',
  status: '',
  tags: [] as string[]
})

// 表格数据
const loading = ref(false)
const tableData = ref<TalentPool[]>([])
const selectedRows = ref<TalentPool[]>([])

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 对话框
const dialogVisible = ref(false)
const dialogTitle = ref('添加人才')
const formRef = ref<FormInstance>()
const formData = ref<Partial<TalentPool>>({
  category: '技术类',
  level: '中级',
  status: '储备',
  tags: []
})

// 表单验证规则
const formRules = reactive<FormRules>({
  resumeId: [
    { required: true, message: '请选择简历', trigger: 'change' }
  ],
  category: [
    { required: true, message: '请选择人才分类', trigger: 'change' }
  ],
  level: [
    { required: true, message: '请选择人才级别', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
})

// 简历选项
const resumeOptions = ref<Resume[]>([])

// 联系记录对话框
const contactDialogVisible = ref(false)
const currentContactRow = ref<TalentPool>()
const contactForm = reactive({
  type: '电话',
  content: '',
  result: '',
  nextAction: ''
})

// 标签设置对话框
const tagDialogVisible = ref(false)
const currentTagRows = ref<TalentPool[]>([])
const tagForm = reactive({
  tags: [] as string[]
})

// 联系历史抽屉
const historyDrawerVisible = ref(false)
const contactHistory = ref<ContactRecord[]>([])

// 可用标签
const availableTags = ref([
  '技术大牛', '架构师', '全栈开发', '前端专家', '后端专家',
  '项目经理', '产品经理', '运营总监', '市场总监',
  '潜力股', '经验丰富', '创业经历', '大厂背景',
  '985院校', '海归', '博士', '行业专家'
])

// 标签类型映射
const getLevelTag = (level: string) => {
  const map: Record<string, string> = {
    '高级': 'danger',
    '中级': 'warning',
    '初级': ''
  }
  return map[level] || 'info'
}

const getStatusTag = (status: string) => {
  const map: Record<string, string> = {
    '活跃': 'success',
    '储备': '',
    '已入职': 'info'
  }
  return map[status] || 'info'
}

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}

// 获取列表数据
const fetchData = async () => {
  loading.value = true
  try {
    const {data: _data} =  await talentPoolApi.getList({
      ...searchForm,
      page: pagination.page,
      size: pagination.size
    })
    tableData.value 
  }
  
  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
  
  .talent-info {
    display: flex;
    align-items: center;
    gap: 10px;
    
    .info-text {
      .name {
        font-weight: 500;
        margin-bottom: 4px;
      }
      
      .contact,
      .current {
        font-size: 12px;
        color: #909399;
        line-height: 1.4;
      }
    }
  }
  
  .more-text {
    font-size: 12px;
    color: #909399;
  }
  
  .last-contact {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
  }
}

.contact-record {
  .record-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    
    .operator {
      color: #606266;
    }
  }
  
  .record-content {
    p {
      margin: 5px 0;
      color: #606266;
      line-height: 1.6;
    }
  }
}
</style>