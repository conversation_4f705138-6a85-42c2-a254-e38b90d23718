 
 

/**
 * JobPortal 组件测试
 * @description 自动生成的组件测试文件
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { mount, flushPromises } from '@vue/test-utils'
import JobPortal from '../JobPortal.vue'
describe('JobPortal', () => {
  let wrapper

  beforeEach(() => {
    wrapper = null
  })

  afterEach(() => {
    wrapper?.unmount()
  })

  it('应该正确渲染', async () => {
    const wrapper = mount(JobPortal)
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.hr-job-portal').exists()).toBe(true)
  })

  it('应该处理异步操作', async () => {
    const wrapper = mount(JobPortal)

    // 等待异步操作完成
    await wrapper.vm.$nextTick()
    await flushPromises()

    // 验证异步操作结果
    expect(wrapper.find('[data-loaded="true"]').exists()).toBe(true)
  })

  it('应该匹配快照', () => {
    const wrapper = mount(JobPortal)
    expect(wrapper.html()).toMatchSnapshot()
  })
})
