<template>
  <div class="applicant-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon><User /></el-icon>
        应聘人员管理
      </h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleBatchInterview">
          <el-icon><ChatDotRound /></el-icon>
          批量安排面试
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出简历
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="姓名">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入姓名"
            clearable
            style="width: 150px"
            />
        </el-form-item>
        <el-form-item label="应聘岗位">
          <el-select
            v-model="searchForm.position"
            placeholder="请选择岗位"
            clearable
            style="width: 200px"
          >
            <el-option label="软件工程师" value="software"  />
            <el-option label="产品经理" value="product"  />
            <el-option label="UI设计师" value="ui"  />
          </el-select>
        </el-form-item>
        <el-form-item label="申请状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="待筛选" value="pending"  />
            <el-option label="初试通过" value="interview1_pass"  />
            <el-option label="复试通过" value="interview2_pass"  />
            <el-option label="已录用" value="hired"  />
            <el-option label="已拒绝" value="rejected"  />
          </el-select>
        </el-form-item>
        <el-form-item label="学历">
          <el-select
            v-model="searchForm.education"
            placeholder="请选择学历"
            clearable
            style="width: 120px"
          >
            <el-option label="本科" value="bachelor"  />
            <el-option label="硕士" value="master"  />
            <el-option label="博士" value="doctor"  />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.total }}</div>
              <div class="stat-label">总申请人数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon pending">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.pending }}</div>
              <div class="stat-label">待筛选</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon interview">
              <el-icon><ChatDotRound /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.interview }}</div>
              <div class="stat-label">面试中</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon hired">
              <el-icon><Select /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.hired }}</div>
              <div class="stat-label">已录用</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 应聘人员列表 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="name" label="姓名" width="100"  />
        <el-table-column prop="gender" label="性别" width="80">
          <template #default="{ row }">
            {{ row.gender === 'male' ? '男' : '女' }}
          </template>
        </el-table-column>
        <el-table-column prop="age" label="年龄" width="80"  />
        <el-table-column prop="education" label="学历" width="100">
          <template #default="{ row }">
            <el-tag :type="getEducationType(row.education)">
              {{ getEducationLabel(row.education) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="major" label="专业" min-width="120"  />
        <el-table-column prop="position" label="应聘岗位" min-width="150"  />
        <el-table-column prop="experience" label="工作经验" width="100"  />
        <el-table-column prop="phone" label="联系电话" width="120"  />
        <el-table-column prop="status" label="申请状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="applyDate" label="申请时间" width="120"  />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handleViewResume(row)">
              查看简历
            </el-button>
            <el-button type="text" size="small" @click="handleInterview(row)">
              安排面试
            </el-button>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
            <el-dropdown @command="(command: unknown) => handleCommand(command, row)">
              <el-button type="text" size="small">
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="pass">通过筛选</el-dropdown-item>
                  <el-dropdown-item command="hire">录用</el-dropdown-item>
                  <el-dropdown-item command="reject" divided>拒绝</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 简历查看对话框 -->
    <el-dialog
      v-model="resumeDialogVisible"
      title="简历详情"
      width="800px"
    >
      <div class="resume-content" v-if="currentResume">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="姓名">{{ currentResume.name }}</el-descriptions-item>
          <el-descriptions-item label="性别">{{ currentResume.gender === 'male' ? '男' : '女' }}</el-descriptions-item>
          <el-descriptions-item label="年龄">{{ currentResume.age }}</el-descriptions-item>
          <el-descriptions-item label="学历">{{ getEducationLabel(currentResume.education) }}</el-descriptions-item>
          <el-descriptions-item label="专业">{{ currentResume.major }}</el-descriptions-item>
          <el-descriptions-item label="工作经验">{{ currentResume.experience }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ currentResume.phone }}</el-descriptions-item>
          <el-descriptions-item label="邮箱">{{ currentResume.email }}</el-descriptions-item>
          <el-descriptions-item label="应聘岗位" :span="2">{{ currentResume.position }}</el-descriptions-item>
          <el-descriptions-item label="自我介绍" :span="2">
            {{ currentResume.introduction }}
          </el-descriptions-item>
          <el-descriptions-item label="工作经历" :span="2">
            {{ currentResume.workExperience }}
          </el-descriptions-item>
          <el-descriptions-item label="项目经验" :span="2">
            {{ currentResume.projectExperience }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button @click="resumeDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleDownloadResume">下载简历</el-button>
      </template>
    </el-dialog>

    <!-- 面试安排对话框 -->
    <el-dialog
      v-model="interviewDialogVisible"
      title="安排面试"
      width="600px"
    >
      <el-form
        ref="interviewFormRef"
        :model="interviewForm"
        :rules="interviewRules"
        label-width="100px"
      >
        <el-form-item label="面试时间" prop="interviewTime">
          <el-date-picker
            v-model="interviewForm.interviewTime"
            type="datetime"
            placeholder="选择面试时间"
            style="width: 100%"
           />
        </el-form-item>
        <el-form-item label="面试地点" prop="location">
          <el-input v-model="interviewForm.location" placeholder="请输入面试地点"   />
        </el-form-item>
        <el-form-item label="面试官" prop="interviewer">
          <el-select v-model="interviewForm.interviewer" placeholder="请选择面试官" style="width: 100%">
            <el-option label="张三" value="zhangsan"  />
            <el-option label="李四" value="lisi"  />
            <el-option label="王五" value="wangwu"  />
          </el-select>
        </el-form-item>
        <el-form-item label="面试类型" prop="type">
          <el-radio-group v-model="interviewForm.type">
            <el-radio label="initial">初试</el-radio>
            <el-radio label="final">复试</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="interviewForm.notes"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
            />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="interviewDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmitInterview">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  User,
  ChatDotRound,
  Download,
  Search,
  Refresh,
  Clock,
  Select,
  ArrowDown
} from '@element-plus/icons-vue'
import { candidateApi } from '@/api/recruitment/candidate'

// 响应式数据
const loading = ref(false)
const resumeDialogVisible = ref(false)
const interviewDialogVisible = ref(false)
const currentResume = ref<unknown>(null)
const selectedApplicants = ref<any[]>([])

const searchForm = reactive({
  name: '',
  position: '',
  status: '',
  education: ''
})

const stats = reactive({
  total: 128,
  pending: 45,
  interview: 32,
  hired: 8
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

const interviewForm = reactive({
  interviewTime: '',
  location: '',
  interviewer: '',
  type: 'initial',
  notes: ''
})

const interviewRules = {
  interviewTime: [{ required: true, message: '请选择面试时间', trigger: 'change' }],
  location: [{ required: true, message: '请输入面试地点', trigger: 'blur' }],
  interviewer: [{ required: true, message: '请选择面试官', trigger: 'change' }]
}

const tableData = ref([
  {
    id: '1',
    name: 'HrHr张三',
    gender: 'male',
    age: 28,
    education: 'master',
    major: '计算机科学与技术',
    position: '软件工程师',
    experience: '3年',
    phone: '13800138001',
    email: '<EMAIL>',
    status: 'pending',
    applyDate: '2024-06-15',
    introduction: '具有3年软件开发经验，熟悉Java、Python等编程语言...',
    workExperience: '2021-2024 ABC公司 软件工程师...',
    projectExperience: '参与开发多个企业级应用系统...'
  }
])

// 方法
const handleSearch = () => {
  console.log('搜索:', searchForm)
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    position: '',
    status: '',
    education: ''
  })
  loadData()
}

   
const handleSelectionChange = (selection: unknown[]) => {
  selectedApplicants.value = selection
}

const handleBatchInterview = () => {
  if (selectedApplicants.value.length === 0) {
    ElMessage.warning('请先选择要安排面试的应聘者')
    return
  }
  ElMessage.info('批量安排面试功能开发中')
}

const handleExport = () => {
  ElMessage.info('导出简历功能开发中')
}

   
const handleViewResume = (row: unknown) => {
  currentResume.value = row
  resumeDialogVisible.value = true
}

   
const handleInterview = (row: unknown) => {
  currentResume.value = row
  resetInterviewForm()
  interviewDialogVisible.value = true
}

   
const handleCommand = (command: string, row: unknown) => {
  switch (command) {
    case 'pass':
      ElMessage.success(`${row.name} 已通过筛选`)
      break
    case 'hire':
      ElMessageBox.confirm(`确定要录用 ${row.name} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ElMessage.success('录用成功')
      })
      break
    case 'reject':
      ElMessageBox.confirm(`确定要拒绝 ${row.name} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ElMessage.success('已拒绝')
      })
      break
  }
}

const handleDownloadResume = () => {
  ElMessage.info('下载简历功能开发中')
}

const handleSubmitInterview = () => {
  ElMessage.success('面试安排成功')
  interviewDialogVisible.value = false
}

const resetInterviewForm = () => {
  Object.assign(interviewForm, {
    interviewTime: '',
    location: '',
    interviewer: '',
    type: 'initial',
    notes: ''
  })
}

const loadData = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params = {
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      keyword: searchForm.name,
      tags: searchForm.position ? [searchForm.position] : undefined,
      status: searchForm.status,
      educationLevel: searchForm.education
    }
    
    // 过滤掉undefined的参数
    const filteredParams = Object.fromEntries(
      Object.entries(params).filter(([_, v]) => v !== undefined && v !== '')
    )
    
    // 获取候选人列表
    const response = await candidateApi.getList(filteredParams)
    
    // 转换数据格式
    tableData.value = response.list.map(item => ({
      id: item.id,
      name: item.personalInfo.name,
      gender: item.personalInfo.gender || 'male',
      age: item.personalInfo.age || calculateAge(item.personalInfo.birthDate),
      education: mapEducationLevel(item.education?.degree),
      major: item.education?.major || '计算机科学与技术',
      position: item.appliedPosition || '软件工程师',
      experience: item.workExperience?.length ? 
        `${calculateTotalExperience(item.workExperience)}年` : '应届生',
      phone: item.personalInfo.phone,
      email: item.personalInfo.email,
      status: mapCandidateStatus(item.status),
      applyDate: item.createdAt || '2024-06-15',
      introduction: item.selfIntroduction || item.education?.description || 
        '具有丰富的工作经验，熟悉多种编程语言...',
      skills: item.skills || [],
      workExperience: item.workExperience || [],
      educationInfo: item.education || {}
    }))
    
    pagination.total = response.total
    
    // 更新统计数据
    stats.total = response.total
    stats.pending = response.list.filter(item => item.status === 'new' || item.status === 'screening').length
    stats.interview = response.list.filter(item => 
      item.stage === 'first_interview' || item.stage === 'second_interview'
    ).length
    stats.hired = response.list.filter(item => item.status === 'hired').length
  } catch (__error) {
    console.error('加载候选人列表失败:', error)
    ElMessage.error('加载候选人列表失败')
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 辅助函数：计算年龄
const calculateAge = (birthDate?: string): number => {
  if (!birthDate) return 25
  const birth = new Date(birthDate)
  const now = new Date()
  let age = now.getFullYear() - birth.getFullYear()
  const monthDiff = now.getMonth() - birth.getMonth()
  if (monthDiff < 0 || (monthDiff === 0 && now.getDate() < birth.getDate())) {
    age--
  }
  return age
}

// 辅助函数：映射学历级别
const mapEducationLevel = (degree?: string): string => {
  const degreeMap: Record<string, string> = {
    '本科': 'bachelor',
    '硕士': 'master',
    '博士': 'doctor',
    '专科': 'bachelor',
    'Bachelor': 'bachelor',
    'Master': 'master',
    'PhD': 'doctor',
    'Doctor': 'doctor'
  }
  return degreeMap[degree || ''] || 'bachelor'
}

// 辅助函数：计算总工作经验
   
const calculateTotalExperience = (workExperience: unknown[]): number => {
  if (!workExperience || workExperience.length === 0) return 0
  
  let totalMonths = 0
  workExperience.forEach(exp => {
    if (exp.startDate) {
      const start = new Date(exp.startDate)
      const end = exp.endDate ? new Date(exp.endDate) : new Date()
      const months = (end.getFullYear() - start.getFullYear()) * 12 + 
                     (end.getMonth() - start.getMonth())
      totalMonths += months
    }
  })
  
  return Math.floor(totalMonths / 12)
}

// 辅助函数：映射候选人状态
const mapCandidateStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    'new': 'pending',
    'screening': 'pending',
    'interview': 'interview',
    'first_interview': 'interview',
    'second_interview': 'interview',
    'hired': 'hired',
    'rejected': 'rejected',
    'withdrawn': 'rejected'
  }
  return statusMap[status] || 'pending'
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadData()
}

const getEducationType = (education: string) => {
  const types: Record<string, string> = {
    bachelor: 'info',
    master: 'success',
    doctor: 'warning'
  }
  return types[education] || ''
}

const getEducationLabel = (education: string) => {
  const labels: Record<string, string> = {
    bachelor: '本科',
    master: '硕士',
    doctor: '博士'
  }
  return labels[education] || education
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'info',
    interview1_pass: 'warning',
    interview2_pass: 'primary',
    hired: 'success',
    rejected: 'danger'
  }
  return types[status] || ''
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    pending: '待筛选',
    interview1_pass: '初试通过',
    interview2_pass: '复试通过',
    hired: '已录用',
    rejected: '已拒绝'
  }
  return labels[status] || status
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.applicant-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.pending {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.interview {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.hired {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.resume-content {
  max-height: 500px;
  overflow-y: auto;
}
</style>
