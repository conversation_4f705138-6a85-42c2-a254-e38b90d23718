 
/* eslint-disable @typescript-eslint/no-unused-vars */
/**
 * JobApplicationManagement 组件测试
 * @description 自动生成的组件测试文件
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { mount, flushPromises } from '@vue/test-utils'
import JobApplicationManagement from '../JobApplicationManagement.vue'
describe('JobApplicationManagement', () => {
  let wrapper

  beforeEach(() => {
    wrapper = null
  })

  afterEach(() => {
    wrapper?.unmount()
  })

  it('应该正确渲染', async () => {
    const wrapper = mount(JobApplicationManagement)
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.hr-job-application-management').exists()).toBe(true)
  })

  it('应该匹配快照', () => {
    const wrapper = mount(JobApplicationManagement)
    expect(wrapper.html()).toMatchSnapshot()
  })
})
