/**
 * 组件测试配置
 */

import { config } from '@vue/test-utils'
import ElementPlus from 'element-plus'

// 配置全局组件
config.global.plugins = [ElementPlus]

// 配置全局属性
config.global.mocks = {
  $t: _key => key,
  $route: {
    path: '/',
    params: {},
    query: {}
  },
  $router: {
    push: vi.fn(),
    replace: vi.fn(),
    go: vi.fn()
  }
}

// 配置全局stubs
config.global.stubs = {
  teleport: true,
  transition: false
}
