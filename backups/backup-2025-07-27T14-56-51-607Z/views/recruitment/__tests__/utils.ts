/**
 * 测试工具函数
 */

import { VueWrapper } from '@vue/test-utils'

/**
 * 等待所有异步更新完成
 */
export async function waitForAsync(wrapper: VueWrapper) {
  await wrapper.vm.$nextTick()
  await flushPromises()
}

/**
 * 触发输入事件
 */
export async function triggerInput(wrapper: VueWrapper, selector: string, value: string) {
  const input = wrapper.find(selector)
  await input.setValue(value)
  await input.trigger('input')
  await input.trigger('change')
}

/**
 * 触发点击事件
 */
export async function triggerClick(wrapper: VueWrapper, selector: string) {
  const element = wrapper.find(selector)
  await element.trigger('click')
}

/**
 * 模拟文件上传
 */
export function mockFileUpload(files: File[]) {
  const fileList = {
    length: files.length,
    item: (index: number) => files[index],
    ...files
  }

  return {
    target: {
      files: fileList
    }
  }
}
