<template>
  <div class="job-position-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon><Briefcase /></el-icon>
        岗位信息管理
      </h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增岗位
        </el-button>
        <el-button @click="handleImport">
          <el-icon><Upload /></el-icon>
          批量导入
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="岗位名称">
          <el-input
            v-model="searchForm.positionName"
            placeholder="请输入岗位名称"
            clearable
            style="width: 200px"
            />
        </el-form-item>
        <el-form-item label="岗位类别">
          <el-select
            v-model="searchForm.category"
            placeholder="请选择岗位类别"
            clearable
            style="width: 150px"
          >
            <el-option label="教学岗位" value="teaching"  />
            <el-option label="管理岗位" value="management"  />
            <el-option label="技术岗位" value="technical"  />
            <el-option label="服务岗位" value="service"  />
          </el-select>
        </el-form-item>
        <el-form-item label="招聘状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择招聘状态"
            clearable
            style="width: 150px"
          >
            <el-option label="招聘中" value="recruiting"  />
            <el-option label="已暂停" value="paused"  />
            <el-option label="已结束" value="ended"  />
          </el-select>
        </el-form-item>
        <el-form-item label="所属部门">
          <el-select
            v-model="searchForm.department"
            placeholder="请选择部门"
            clearable
            style="width: 200px"
          >
            <el-option label="计算机学院" value="cs"  />
            <el-option label="机械工程学院" value="me"  />
            <el-option label="经济管理学院" value="em"  />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon recruiting">
              <el-icon><Briefcase /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.recruiting }}</div>
              <div class="stat-label">招聘中岗位</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon applications">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.applications }}</div>
              <div class="stat-label">总申请人数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon interviews">
              <el-icon><ChatDotRound /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.interviews }}</div>
              <div class="stat-label">待面试人数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon hired">
              <el-icon><Select /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.hired }}</div>
              <div class="stat-label">已录用人数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 岗位列表 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="positionCode" label="岗位编码" width="120"  />
        <el-table-column prop="positionName" label="岗位名称" min-width="150"  />
        <el-table-column prop="department" label="所属部门" width="150"  />
        <el-table-column prop="category" label="岗位类别" width="120">
          <template #default="{ row }">
            <el-tag :type="getCategoryType(row.category)">
              {{ getCategoryLabel(row.category) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="recruitCount" label="招聘人数" width="100"  />
        <el-table-column prop="applicationCount" label="申请人数" width="100"  />
        <el-table-column prop="status" label="招聘状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="publishDate" label="发布时间" width="120"  />
        <el-table-column prop="deadline" label="截止时间" width="120"  />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button type="text" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="text" size="small" @click="handleApplications(row)">
              申请管理
            </el-button>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
            <el-dropdown @command="(command: unknown) => handleCommand(command, row)">
              <el-button type="text" size="small">
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="pause" v-if="row.status === 'recruiting'">
                    暂停招聘
                  </el-dropdown-item>
                  <el-dropdown-item command="resume" v-if="row.status === 'paused'">
                    恢复招聘
                  </el-dropdown-item>
                  <el-dropdown-item command="end">结束招聘</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 岗位详情/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="岗位编码" prop="positionCode">
              <el-input v-model="formData.positionCode" :disabled="isView"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="岗位名称" prop="positionName">
              <el-input v-model="formData.positionName" :disabled="isView"   />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所属部门" prop="department">
              <el-select v-model="formData.department" :disabled="isView" style="width: 100%">
                <el-option label="计算机学院" value="cs"  />
                <el-option label="机械工程学院" value="me"  />
                <el-option label="经济管理学院" value="em"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="岗位类别" prop="category">
              <el-select v-model="formData.category" :disabled="isView" style="width: 100%">
                <el-option label="教学岗位" value="teaching"  />
                <el-option label="管理岗位" value="management"  />
                <el-option label="技术岗位" value="technical"  />
                <el-option label="服务岗位" value="service"  />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="招聘人数" prop="recruitCount">
              <el-input-number
                v-model="formData.recruitCount"
                :min="1"
                :disabled="isView"
                style="width: 100%"
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="薪资范围" prop="salaryRange">
              <el-input v-model="formData.salaryRange" :disabled="isView" placeholder="如：8000-12000"   />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="岗位职责" prop="responsibilities">
          <el-input
            v-model="formData.responsibilities"
            type="textarea"
            :rows="4"
            :disabled="isView"
            placeholder="请输入岗位职责"
            />
        </el-form-item>
        <el-form-item label="任职要求" prop="requirements">
          <el-input
            v-model="formData.requirements"
            type="textarea"
            :rows="4"
            :disabled="isView"
            placeholder="请输入任职要求"
            />
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="发布时间" prop="publishDate">
              <el-date-picker
                v-model="formData.publishDate"
                type="date"
                :disabled="isView"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="截止时间" prop="deadline">
              <el-date-picker
                v-model="formData.deadline"
                type="date"
                :disabled="isView"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer v-if="!isView">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'JobPositionManagement'
})
 
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Briefcase,
  Plus,
  Upload,
  Download,
  Search,
  Refresh,
  User,
  ChatDotRound,
  Select,
  ArrowDown
} from '@element-plus/icons-vue'
import { jobPostApi, jobPostManagementApi } from '@/api/recruitment/job-post'

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isView = ref(false)

const searchForm = reactive({
  positionName: '',
  category: '',
  status: '',
  department: ''
})

const stats = reactive({
  recruiting: 15,
  applications: 128,
  interviews: 32,
  hired: 8
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

const tableData = ref([
  {
    id: '1',
    positionCode: '********',
    positionName: '软件工程师',
    department: '计算机学院',
    category: 'technical',
    recruitCount: 3,
    applicationCount: 25,
    status: 'recruiting',
    publishDate: '2024-06-01',
    deadline: '2024-07-01',
    salaryRange: '8000-12000',
    responsibilities: '负责软件系统的设计、开发和维护工作',
    requirements: '计算机相关专业，3年以上开发经验'
  }
])

const formData = reactive({
  positionCode: '',
  positionName: '',
  department: '',
  category: '',
  recruitCount: 1,
  salaryRange: '',
  responsibilities: '',
  requirements: '',
  publishDate: '',
  deadline: ''
})

const formRules = {
  positionCode: [{ required: true, message: '请输入岗位编码', trigger: 'blur' }],
  positionName: [{ required: true, message: '请输入岗位名称', trigger: 'blur' }],
  department: [{ required: true, message: '请选择所属部门', trigger: 'change' }],
  category: [{ required: true, message: '请选择岗位类别', trigger: 'change' }],
  recruitCount: [{ required: true, message: '请输入招聘人数', trigger: 'blur' }]
}

// 方法
const handleSearch = () => {
  console.log('搜索:', searchForm)
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    positionName: '',
    category: '',
    status: '',
    department: ''
  })
  loadData()
}

const handleAdd = () => {
  dialogTitle.value = '新增岗位'
  isView.value = false
  resetForm()
  dialogVisible.value = true
}

   
const handleView = (row: unknown) => {
  dialogTitle.value = '查看岗位'
  isView.value = true
  Object.assign(formData, row)
  dialogVisible.value = true
}

   
const handleEdit = (row: unknown) => {
  dialogTitle.value = '编辑岗位'
  isView.value = false
  Object.assign(formData, row)
  dialogVisible.value = true
}

   
const handleApplications = (row: unknown) => {
  ElMessage.info(`查看岗位 ${row.positionName} 的申请管理`)
}

   
const handleCommand = (command: string, row: unknown) => {
  switch (command) {
    case 'pause':
      ElMessage.success(`已暂停岗位 ${row.positionName} 的招聘`)
      break
    case 'resume':
      ElMessage.success(`已恢复岗位 ${row.positionName} 的招聘`)
      break
    case 'end':
      ElMessage.success(`已结束岗位 ${row.positionName} 的招聘`)
      break
    case 'delete':
      ElMessageBox.confirm(`确定要删除岗位 ${row.positionName} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ElMessage.success('删除成功')
      })
      break
  }
}

const handleImport = () => {
  ElMessage.info('批量导入功能开发中')
}

const handleExport = () => {
  ElMessage.info('导出数据功能开发中')
}

const handleSubmit = () => {
  ElMessage.success('保存成功')
  dialogVisible.value = false
}

const handleDialogClose = () => {
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    positionCode: '',
    positionName: '',
    department: '',
    category: '',
    recruitCount: 1,
    salaryRange: '',
    responsibilities: '',
    requirements: '',
    publishDate: '',
    deadline: ''
  })
}

const loadData = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params = {
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      positionName: searchForm.positionName,
      category: searchForm.category,
      status: searchForm.status,
      department: searchForm.department
    }
    
    // 调用API获取职位列表
    const response = await jobPostApi.getList(params)
    
    // 更新表格数据
   
    tableData.value = response.data.list.map((item: unknown) => ({
      id: item.id || item.postId,
      positionCode: item.positionCode || item.code,
      positionName: item.positionName || item.title,
      department: item.department || item.departmentName,
      category: item.category || 'general',
      recruitCount: item.recruitCount || item.vacancies || 0,
      applicationCount: item.applicationCount || 0,
      status: item.status || 'draft',
      publishDate: item.publishDate || item.publishedAt,
      deadline: item.deadline || item.expiryDate,
      salaryRange: item.salaryRange || `${item.minSalary || 0}-${item.maxSalary || 0}`,
      responsibilities: item.responsibilities || item.description || '',
      requirements: item.requirements || ''
    }))
    
    // 更新分页信息
    pagination.total = response.data.total
    
    // 同时获取统计数据
    const statsResponse = await jobPostManagementApi.getJobPosts({
      page: 1,
      pageSize: 1
    })
    
    if (statsResponse.data?.summary) {
      stats.recruiting = statsResponse.data.summary.publishedPosts || 0
      stats.applications = statsResponse.data.summary.totalApplications || 0
      // 面试和录用数据需要其他API，暂时保持原值
      stats.interviews = stats.interviews || 0
      stats.hired = stats.hired || 0
    }
  } catch (__error) {
    console.error('加载职位数据失败:', error)
    ElMessage.error('加载职位数据失败，请稍后重试')
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadData()
}

const getCategoryType = (category: string) => {
  const types: Record<string, string> = {
    teaching: 'success',
    management: 'warning',
    technical: 'primary',
    service: 'info'
  }
  return types[category] || ''
}

const getCategoryLabel = (category: string) => {
  const labels: Record<string, string> = {
    teaching: '教学岗位',
    management: '管理岗位',
    technical: '技术岗位',
    service: '服务岗位'
  }
  return labels[category] || category
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    recruiting: 'success',
    paused: 'warning',
    ended: 'info'
  }
  return types[status] || ''
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    recruiting: '招聘中',
    paused: '已暂停',
    ended: '已结束'
  }
  return labels[status] || status
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.job-position-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.recruiting {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.applications {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.interviews {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.hired {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
