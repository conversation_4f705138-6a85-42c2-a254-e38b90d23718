<template>
  <div class="recruitment-cycle">
    <!-- 顶部统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card>
          <el-statistic title="平均招聘周期" :value="avgCycle.total" suffix="天">
            <template #prefix>
              <el-icon><clock /></el-icon>
            </template>
          </el-statistic>
          <div class="trend">
            <span>环比上月</span>
            <span class="value" :class="avgCycle.trend < 0 ? 'up' : 'down'">
              {{ avgCycle.trend > 0 ? '+' : '' }}{{ avgCycle.trend }}天
            </span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <el-statistic title="最快招聘" :value="cycleRange.min" suffix="天">
            <template #prefix>
              <el-icon><trophy /></el-icon>
            </template>
          </el-statistic>
          <div class="position-info">{{ cycleRange.minPosition }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <el-statistic title="最慢招聘" :value="cycleRange.max" suffix="天">
            <template #prefix>
              <el-icon><warning /></el-icon>
            </template>
          </el-statistic>
          <div class="position-info">{{ cycleRange.maxPosition }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <el-statistic title="超期职位" :value="overtime.count" suffix="个">
            <template #prefix>
              <el-icon><alarm-clock /></el-icon>
            </template>
          </el-statistic>
          <div class="overtime-info">
            超期率：<span class="rate">{{ overtime.rate }}%</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <el-form :inline="true">
        <el-form-item label="统计时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="fetchData"
           />
        </el-form-item>
        <el-form-item label="部门">
          <el-select v-model="filters.department" placeholder="全部" clearable @change="fetchData">
            <el-option label="技术部" value="tech"  />
            <el-option label="产品部" value="product"  />
            <el-option label="市场部" value="market"  />
            <el-option label="运营部" value="operation"  />
          </el-select>
        </el-form-item>
        <el-form-item label="职位类型">
          <el-select v-model="filters.positionType" placeholder="全部" clearable @change="fetchData">
            <el-option label="技术类" value="technical"  />
            <el-option label="产品类" value="product"  />
            <el-option label="运营类" value="operation"  />
            <el-option label="职能类" value="functional"  />
          </el-select>
        </el-form-item>
        <el-form-item label="紧急程度">
          <el-select v-model="filters.urgency" placeholder="全部" clearable @change="fetchData">
            <el-option label="紧急" value="urgent"  />
            <el-option label="正常" value="normal"  />
            <el-option label="不急" value="low"  />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="fetchData">查询</el-button>
          <el-button @click="exportReport">导出报告</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 周期分析图表 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>各阶段耗时分析</span>
          </template>
          <div id="stageChart" style="height: 400px"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>招聘周期趋势</span>
            <el-radio-group v-model="trendType" size="small" style="float: right">
              <el-radio-button label="day">按天</el-radio-button>
              <el-radio-button label="week">按周</el-radio-button>
              <el-radio-button label="month">按月</el-radio-button>
            </el-radio-group>
          </template>
          <div id="trendChart" style="height: 400px"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 职位周期对比 -->
    <el-card class="comparison-card">
      <template #header>
        <span>职位招聘周期对比</span>
      </template>
      <div id="comparisonChart" style="height: 350px"></div>
    </el-card>

    <!-- 详细数据表格 -->
    <el-card class="table-card">
      <template #header>
        <span>招聘周期明细</span>
        <div style="float: right">
          <el-checkbox v-model="showDetail">显示阶段明细</el-checkbox>
        </div>
      </template>
      
      <el-table :data="tableData" stripe v-loading="loading">
        <el-table-column prop="position" label="职位" fixed  />
        <el-table-column prop="department" label="部门" width="100"  />
        <el-table-column prop="urgency" label="紧急程度" width="100">
          <template #default="{ row }">
            <el-tag :type="getUrgencyType(row.urgency)" size="small">
              {{ row.urgency }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="publishDate" label="发布日期" width="110"  />
        <el-table-column prop="closeDate" label="关闭日期" width="110"  />
        
        <!-- 阶段明细 -->
        <el-table-column v-if="showDetail" label="需求确认" align="center" width="90">
          <template #default="{ row }">
            <span :class="getStageClass(row.stages.requirement)">
              {{ row.stages.requirement }}天
            </span>
          </template>
        </el-table-column>
        <el-table-column v-if="showDetail" label="简历收集" align="center" width="90">
          <template #default="{ row }">
            <span :class="getStageClass(row.stages.collection)">
              {{ row.stages.collection }}天
            </span>
          </template>
        </el-table-column>
        <el-table-column v-if="showDetail" label="简历筛选" align="center" width="90">
          <template #default="{ row }">
            <span :class="getStageClass(row.stages.screening)">
              {{ row.stages.screening }}天
            </span>
          </template>
        </el-table-column>
        <el-table-column v-if="showDetail" label="面试安排" align="center" width="90">
          <template #default="{ row }">
            <span :class="getStageClass(row.stages.interview)">
              {{ row.stages.interview }}天
            </span>
          </template>
        </el-table-column>
        <el-table-column v-if="showDetail" label="Offer谈判" align="center" width="90">
          <template #default="{ row }">
            <span :class="getStageClass(row.stages.offer)">
              {{ row.stages.offer }}天
            </span>
          </template>
        </el-table-column>
        
        <el-table-column prop="totalDays" label="总周期" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getCycleType(row.totalDays)">
              {{ row.totalDays }}天
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === '已完成' ? 'success' : 'warning'">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="viewDetail(row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 优化建议 -->
    <el-card class="advice-card">
      <template #header>
        <span>周期优化建议</span>
      </template>
      <el-timeline>
        <el-timeline-item
          v-for="(advice, index) in optimizationAdvice"
          :key="index"
          :color="advice.priority === 'high' ? 'red' : advice.priority === 'medium' ? 'orange' : 'green'"
        >
          <div class="advice-item">
            <div class="advice-header">
              <strong>{{ advice.stage }}</strong>
              <el-tag :type="getPriorityType(advice.priority)" size="small">
                {{ getPriorityText(advice.priority) }}
              </el-tag>
            </div>
            <p>{{ advice.problem }}</p>
            <p class="suggestion">建议：{{ advice.suggestion }}</p>
            <p class="impact">预期效果：{{ advice.expectedImpact }}</p>
          </div>
        </el-timeline-item>
      </el-timeline>
    </el-card>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Clock, Trophy, Warning, AlarmClock } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 数据状态
const loading = ref(false)
const dateRange = ref(['2025-01-01', '2025-01-31'])
const trendType = ref('week')
const showDetail = ref(true)

// 筛选条件
const filters = reactive({
  department: '',
  positionType: '',
  urgency: ''
})

// 统计数据
const avgCycle = reactive({
  total: 18.5,
  trend: -2.3
})

const cycleRange = reactive({
  min: 7,
  minPosition: '高级前端工程师',
  max: 45,
  maxPosition: '算法工程师'
})

const overtime = reactive({
  count: 8,
  rate: 15.4
})

// 表格数据
const tableData = ref([
  {
    position: '前端工程师',
    department: '技术部',
    urgency: '紧急',
    publishDate: '2025-01-05',
    closeDate: '2025-01-20',
    stages: {
      requirement: 2,
      collection: 5,
      screening: 3,
      interview: 4,
      offer: 1
    },
    totalDays: 15,
    status: '已完成'
  },
  {
    position: 'Java工程师',
    department: '技术部',
    urgency: '正常',
    publishDate: '2025-01-03',
    closeDate: '2025-01-25',
    stages: {
      requirement: 3,
      collection: 7,
      screening: 4,
      interview: 6,
      offer: 2
    },
    totalDays: 22,
    status: '已完成'
  },
  {
    position: '产品经理',
    department: '产品部',
    urgency: '紧急',
    publishDate: '2025-01-10',
    closeDate: '-',
    stages: {
      requirement: 1,
      collection: 8,
      screening: 3,
      interview: 5,
      offer: 0
    },
    totalDays: 17,
    status: '进行中'
  }
])

// 优化建议
const optimizationAdvice = ref([
  {
    stage: '简历收集阶段',
    priority: 'high',
    problem: '平均耗时6.5天，超过目标值50%',
    suggestion: '扩大招聘渠道，提高职位曝光度，优化JD描述',
    expectedImpact: '缩短2-3天'
  },
  {
    stage: '面试安排阶段',
    priority: 'medium',
    problem: '面试官时间协调困难，平均耗时4.8天',
    suggestion: '建立面试官资源池，采用视频面试，提前预约时间',
    expectedImpact: '缩短1-2天'
  },
  {
    stage: '需求确认阶段',
    priority: 'low',
    problem: '部分岗位需求不明确，导致后期返工',
    suggestion: '使用标准化需求模板，加强需求评审',
    expectedImpact: '提高招聘精准度'
  }
])

// 获取紧急程度类型
const getUrgencyType = (urgency: string) => {
  const map: Record<string, string> = {
    '紧急': 'danger',
    '正常': 'primary',
    '不急': 'info'
  }
  return map[urgency] || ''
}

// 获取阶段耗时样式
const getStageClass = (days: number) => {
  if (days <= 3) return 'stage-fast'
  if (days <= 5) return 'stage-normal'
  return 'stage-slow'
}

// 获取周期类型
const getCycleType = (days: number) => {
  if (days <= 15) return 'success'
  if (days <= 25) return 'primary'
  if (days <= 35) return 'warning'
  return 'danger'
}

// 获取优先级类型
const getPriorityType = (priority: string) => {
  const map: Record<string, string> = {
    'high': 'danger',
    'medium': 'warning',
    'low': 'success'
  }
  return map[priority] || 'info'
}

// 获取优先级文本
const getPriorityText = (priority: string) => {
  const map: Record<string, string> = {
    'high': '高优先级',
    'medium': '中优先级',
    'low': '低优先级'
  }
  return map[priority] || priority
}

// 初始化各阶段耗时图表
const initStageChart = () => {
  const chartDom = document.getElementById('stageChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['最短', '平均', '最长']
    },
    xAxis: {
      type: 'category',
      data: ['需求确认', '简历收集', '简历筛选', '面试安排', 'Offer谈判']
    },
    yAxis: {
      type: 'value',
      name: 'HrHr天数'
    },
    series: [
      {
        name: '最短',
        type: 'bar',
        data: [1, 3, 2, 2, 1],
        itemStyle: { color: '#67c23a' }
      },
      {
        name: '平均',
        type: 'bar',
        data: [2.5, 6.5, 3.5, 4.8, 1.8],
        itemStyle: { color: '#409eff' }
      },
      {
        name: '最长',
        type: 'bar',
        data: [5, 12, 7, 10, 4],
        itemStyle: { color: '#f56c6c' }
      }
    ]
  }
  
  myChart.setOption(option)
}

// 初始化趋势图表
const initTrendChart = () => {
  const chartDom = document.getElementById('trendChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  
  const xData = trendType.value === 'day' 
    ? ['1月1日', '1月2日', '1月3日', '1月4日', '1月5日', '1月6日', '1月7日']
    : trendType.value === 'week'
    ? ['第1周', '第2周', '第3周', '第4周']
    : ['10月', '11月', '12月', '1月']
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['平均周期', '技术岗', '产品岗', '运营岗']
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xData
    },
    yAxis: {
      type: 'value',
      name: '天数'
    },
    series: [
      {
        name: '平均周期',
        type: 'line',
        data: trendType.value === 'week' ? [20, 19, 18.5, 18] : [22, 21, 20, 18.5],
        itemStyle: { color: '#409eff' },
        markLine: {
          data: [{ type: 'average', name: '平均值' }]
        }
      },
      {
        name: '技术岗',
        type: 'line',
        data: trendType.value === 'week' ? [18, 17, 16, 15] : [20, 19, 18, 16],
        itemStyle: { color: '#67c23a' }
      },
      {
        name: '产品岗',
        type: 'line',
        data: trendType.value === 'week' ? [22, 21, 20, 20] : [25, 24, 22, 20],
        itemStyle: { color: '#e6a23c' }
      },
      {
        name: '运营岗',
        type: 'line',
        data: trendType.value === 'week' ? [25, 24, 23, 22] : [28, 26, 25, 23],
        itemStyle: { color: '#f56c6c' }
      }
    ]
  }
  
  myChart.setOption(option)
}

// 初始化职位对比图表
const initComparisonChart = () => {
  const chartDom = document.getElementById('comparisonChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '天数',
      max: 50
    },
    yAxis: {
      type: 'category',
      data: ['UI设计师', '测试工程师', '产品经理', 'Java工程师', '前端工程师', '算法工程师']
    },
    series: [
      {
        type: 'bar',
        data: [
          { value: 12, itemStyle: { color: '#67c23a' } },
          { value: 15, itemStyle: { color: '#67c23a' } },
          { value: 20, itemStyle: { color: '#409eff' } },
          { value: 22, itemStyle: { color: '#409eff' } },
          { value: 15, itemStyle: { color: '#67c23a' } },
          { value: 45, itemStyle: { color: '#f56c6c' } }
        ],
        label: {
          show: true,
          position: 'right',
          formatter: '{c}天'
        }
      }
    ]
  }
  
  myChart.setOption(option)
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    // 模拟数据获取
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('数据更新成功')
  } catch (__error) {
    ElMessage.error('数据获取失败')
  } finally {
    loading.value = false
  }
}

// 导出报告
const exportReport = () => {
  ElMessage.success('正在生成招聘周期分析报告...')
}

// 查看详情
   
const viewDetail = (row: unknown) => {
  console.log('查看详情', row)
  ElMessage.info(`查看${row.position}的招聘详情`)
}

// 监听趋势类型变化
watch(trendType, () => {
  initTrendChart()
})

// 初始化
onMounted(() => {
  initStageChart()
  initTrendChart()
  initComparisonChart()
  
  // 响应式处理
  window.addEventListener('resize', () => {
    const stageChart = echarts.getInstanceByDom(document.getElementById('stageChart')!)
    const trendChart = echarts.getInstanceByDom(document.getElementById('trendChart')!)
    const comparisonChart = echarts.getInstanceByDom(document.getElementById('comparisonChart')!)
    
    stageChart?.resize()
    trendChart?.resize()
    comparisonChart?.resize()
  })
})
</script>

<style lang="scss" scoped>
.recruitment-cycle {
  padding: 20px;
  
  // 统计卡片
  .stats-row {
    margin-bottom: 20px;
    
    .el-card {
      text-align: center;
      
      .trend {
        margin-top: 10px;
        font-size: 12px;
        color: #909399;
        
        .value {
          margin-left: 5px;
          font-weight: bold;
          
          &.up {
            color: #67c23a;
          }
          
          &.down {
            color: #f56c6c;
          }
        }
      }
      
      .position-info {
        margin-top: 10px;
        font-size: 12px;
        color: #606266;
      }
      
      .overtime-info {
        margin-top: 10px;
        font-size: 12px;
        color: #909399;
        
        .rate {
          color: #f56c6c;
          font-weight: bold;
        }
      }
    }
  }
  
  // 筛选卡片
  .filter-card {
    margin-bottom: 20px;
  }
  
  // 图表卡片
  .chart-card {
    margin-bottom: 20px;
  }
  
  .comparison-card {
    margin-bottom: 20px;
  }
  
  // 表格卡片
  .table-card {
    margin-bottom: 20px;
    
    .stage-fast {
      color: #67c23a;
    }
    
    .stage-normal {
      color: #409eff;
    }
    
    .stage-slow {
      color: #f56c6c;
    }
  }
  
  // 建议卡片
  .advice-card {
    .advice-item {
      .advice-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
      }
      
      p {
        margin: 5px 0;
        color: #606266;
        font-size: 14px;
        
        &.suggestion {
          color: #409eff;
        }
        
        &.impact {
          color: #67c23a;
          font-size: 12px;
        }
      }
    }
  }
}

// 响应式图标
:deep(.el-statistic) {
  .el-statistic__head {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
  }
}
</style>