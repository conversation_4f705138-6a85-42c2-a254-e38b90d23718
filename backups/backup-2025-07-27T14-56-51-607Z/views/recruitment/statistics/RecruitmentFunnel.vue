<template>
  <div class="recruitment-funnel">
    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <el-form :inline="true">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="fetchData"
           />
        </el-form-item>
        <el-form-item label="部门">
          <el-select v-model="filters.department" placeholder="全部" clearable @change="fetchData">
            <el-option label="技术部" value="tech"  />
            <el-option label="产品部" value="product"  />
            <el-option label="市场部" value="market"  />
            <el-option label="运营部" value="operation"  />
          </el-select>
        </el-form-item>
        <el-form-item label="职位">
          <el-select v-model="filters.position" placeholder="全部" clearable @change="fetchData">
            <el-option label="前端工程师" value="frontend"  />
            <el-option label="后端工程师" value="backend"  />
            <el-option label="产品经理" value="pm"  />
            <el-option label="UI设计师" value="ui"  />
          </el-select>
        </el-form-item>
        <el-form-item label="渠道">
          <el-select v-model="filters.channel" placeholder="全部" clearable @change="fetchData">
            <el-option label="招聘网站" value="website"  />
            <el-option label="内部推荐" value="referral"  />
            <el-option label="校园招聘" value="campus"  />
            <el-option label="猎头" value="headhunter"  />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="fetchData">查询</el-button>
          <el-button @click="resetFilters">重置</el-button>
          <el-button type="success" @click="exportData">导出</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 漏斗图表 -->
    <el-row :gutter="20">
      <el-col :span="14">
        <el-card class="chart-card">
          <template #header>
            <span>招聘漏斗分析</span>
          </template>
          <div id="funnelChart" style="height: 500px"></div>
        </el-card>
      </el-col>
      
      <el-col :span="10">
        <el-card class="stats-card">
          <template #header>
            <span>转化率分析</span>
          </template>
          <div class="conversion-stats">
            <div class="stat-item" v-for="item in conversionStats" :key="item.stage">
              <div class="stage-info">
                <span class="stage-name">{{ item.stage }}</span>
                <span class="stage-count">{{ item.count }}人</span>
              </div>
              <div class="conversion-info">
                <div class="rate-bar">
                  <el-progress 
                    :percentage="item.rate" 
                    :stroke-width="20"
                    :color="getProgressColor(item.rate)"
                   />
                </div>
                <div class="rate-detail">
                  <span class="rate-value">{{ item.rate }}%</span>
                  <span class="rate-trend" :class="item.trend > 0 ? 'up' : 'down'">
                    {{ item.trend > 0 ? '+' : '' }}{{ item.trend }}%
                  </span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 关键指标 -->
          <div class="key-metrics">
            <el-divider>关键指标</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="metric-item">
                  <div class="metric-value">{{ metrics.overallRate }}%</div>
                  <div class="metric-label">总体转化率</div>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="metric-item">
                  <div class="metric-value">{{ metrics.avgDays }}天</div>
                  <div class="metric-label">平均招聘周期</div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细数据表格 -->
    <el-card class="table-card">
      <template #header>
        <span>漏斗详细数据</span>
        <el-radio-group v-model="tableView" size="small" style="float: right">
          <el-radio-button label="summary">汇总</el-radio-button>
          <el-radio-button label="position">按职位</el-radio-button>
          <el-radio-button label="channel">按渠道</el-radio-button>
          <el-radio-button label="time">按时间</el-radio-button>
        </el-radio-group>
      </template>
      
      <el-table :data="tableData" stripe v-loading="loading">
        <el-table-column 
          prop="dimension" 
          :label="tableView === 'time' ? '日期' : tableView === 'position' ? '职位' : tableView === 'channel' ? '渠道' : '阶段'"
          fixed
         />
        <el-table-column prop="received" label="收到简历" align="center">
          <template #default="{ row }">
            <span class="stage-number">{{ row.received }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="screened" label="通过筛选" align="center">
          <template #default="{ row }">
            <span class="stage-number">{{ row.screened }}</span>
            <span class="stage-rate">({{ getRate(row.screened, row.received) }}%)</span>
          </template>
        </el-table-column>
        <el-table-column prop="interviewed" label="参加面试" align="center">
          <template #default="{ row }">
            <span class="stage-number">{{ row.interviewed }}</span>
            <span class="stage-rate">({{ getRate(row.interviewed, row.screened) }}%)</span>
          </template>
        </el-table-column>
        <el-table-column prop="passed" label="面试通过" align="center">
          <template #default="{ row }">
            <span class="stage-number">{{ row.passed }}</span>
            <span class="stage-rate">({{ getRate(row.passed, row.interviewed) }}%)</span>
          </template>
        </el-table-column>
        <el-table-column prop="offered" label="发送Offer" align="center">
          <template #default="{ row }">
            <span class="stage-number">{{ row.offered }}</span>
            <span class="stage-rate">({{ getRate(row.offered, row.passed) }}%)</span>
          </template>
        </el-table-column>
        <el-table-column prop="joined" label="成功入职" align="center">
          <template #default="{ row }">
            <span class="stage-number">{{ row.joined }}</span>
            <span class="stage-rate">({{ getRate(row.joined, row.offered) }}%)</span>
          </template>
        </el-table-column>
        <el-table-column label="整体转化率" align="center" width="120">
          <template #default="{ row }">
            <el-tag :type="getConversionType(row.overallRate)">
              {{ row.overallRate }}%
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 优化建议 -->
    <el-card class="suggestion-card">
      <template #header>
        <span>优化建议</span>
      </template>
      <el-alert
        v-for="(suggestion, index) in suggestions"
        :key="index"
        :title="suggestion.title"
        :type="suggestion.type"
        :description="suggestion.description"
        show-icon
        :closable="false"
        style="margin-bottom: 10px"
       />
    </el-card>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'

// 数据状态
const loading = ref(false)
const dateRange = ref(['2025-01-01', '2025-01-31'])
const tableView = ref('summary')

// 筛选条件
const filters = reactive({
  department: '',
  position: '',
  channel: ''
})

// 转化率统计
const conversionStats = ref([
  { stage: '简历筛选', count: 1280, rate: 30, trend: 5 },
  { stage: '面试邀约', count: 384, rate: 50, trend: -3 },
  { stage: '面试通过', count: 192, rate: 53, trend: 8 },
  { stage: 'Offer发放', count: 102, rate: 63, trend: 2 },
  { stage: '成功入职', count: 64, rate: 63, trend: -5 }
])

// 关键指标
const metrics = reactive({
  overallRate: 5.0,
  avgDays: 18
})

// 表格数据
const tableData = ref([
  {
    dimension: '整体',
    received: 1280,
    screened: 384,
    interviewed: 192,
    passed: 102,
    offered: 64,
    joined: 64,
    overallRate: 5.0
  }
])

// 优化建议
const suggestions = ref([
  {
    type: 'warning',
    title: '简历筛选转化率偏低',
    description: '当前简历筛选通过率仅30%，建议优化职位描述，精准定位目标候选人，减少无效简历投递。'
  },
  {
    type: 'success',
    title: 'Offer接受率表现良好',
    description: 'Offer接受率达到63%，说明薪资福利具有竞争力，建议保持当前策略。'
  },
  {
    type: 'info',
    title: '面试流程可优化',
    description: '从面试到Offer平均耗时10天，建议优化面试流程，缩短决策时间。'
  }
])

// 计算转化率
const getRate = (current: number, previous: number) => {
  if (previous === 0) return 0
  return ((current / previous) * 100).toFixed(1)
}

// 获取进度条颜色
const getProgressColor = (rate: number) => {
  if (rate >= 60) return '#67c23a'
  if (rate >= 40) return '#409eff'
  if (rate >= 20) return '#e6a23c'
  return '#f56c6c'
}

// 获取转化率标签类型
const getConversionType = (rate: number) => {
  if (rate >= 8) return 'success'
  if (rate >= 5) return 'primary'
  if (rate >= 3) return 'warning'
  return 'danger'
}

// 初始化漏斗图
const initFunnelChart = () => {
  const chartDom = document.getElementById('funnelChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  
  const option = {
    tooltip: {
      trigger: 'item',
   
      formatter: (params: unknown) => {
        return `${params.name}<br/>人数: ${params.value}<br/>占比: ${params.percent}%`
      }
    },
    toolbox: {
      feature: {
        restore: {},
        saveAsImage: {}
      }
    },
    series: [
      {
        name: 'HrHr招聘漏斗',
        type: 'funnel',
        left: '10%',
        top: 60,
        bottom: 60,
        width: '80%',
        min: 0,
        max: 1280,
        minSize: '0%',
        maxSize: '100%',
        sort: 'descending',
        gap: 2,
        label: {
          show: true,
          position: 'inside',
          formatter: '{b}\n{c}人'
        },
        labelLine: {
          length: 10,
          lineStyle: {
            width: 1,
            type: 'solid'
          }
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1
        },
        emphasis: {
          label: {
            fontSize: 20
          }
        },
        data: [
          { value: 1280, name: '收到简历' },
          { value: 384, name: '通过筛选' },
          { value: 192, name: '参加面试' },
          { value: 102, name: '面试通过' },
          { value: 64, name: '发送Offer' },
          { value: 64, name: '成功入职' }
        ]
      }
    ]
  }
  
  myChart.setOption(option)
  
  // 响应式
  window.addEventListener('resize', () => {
    myChart.resize()
  })
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    // 模拟数据获取
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 根据不同视图更新表格数据
    if (tableView.value === 'position') {
      tableData.value = [
        {
          dimension: '前端工程师',
          received: 320,
          screened: 120,
          interviewed: 60,
          passed: 30,
          offered: 20,
          joined: 18,
          overallRate: 5.6
        },
        {
          dimension: '后端工程师',
          received: 280,
          screened: 100,
          interviewed: 50,
          passed: 25,
          offered: 15,
          joined: 12,
          overallRate: 4.3
        },
        {
          dimension: '产品经理',
          received: 200,
          screened: 60,
          interviewed: 30,
          passed: 15,
          offered: 10,
          joined: 8,
          overallRate: 4.0
        }
      ]
    } else if (tableView.value === 'channel') {
      tableData.value = [
        {
          dimension: '招聘网站',
          received: 600,
          screened: 150,
          interviewed: 75,
          passed: 40,
          offered: 25,
          joined: 20,
          overallRate: 3.3
        },
        {
          dimension: '内部推荐',
          received: 300,
          screened: 150,
          interviewed: 75,
          passed: 40,
          offered: 25,
          joined: 25,
          overallRate: 8.3
        },
        {
          dimension: '校园招聘',
          received: 200,
          screened: 50,
          interviewed: 25,
          passed: 15,
          offered: 10,
          joined: 10,
          overallRate: 5.0
        }
      ]
    }
    
    ElMessage.success('数据更新成功')
  } catch (__error) {
    ElMessage.error('数据获取失败')
  } finally {
    loading.value = false
  }
}

// 重置筛选条件
const resetFilters = () => {
  filters.department = ''
  filters.position = ''
  filters.channel = ''
  dateRange.value = ['2025-01-01', '2025-01-31']
  fetchData()
}

// 导出数据
const exportData = () => {
  ElMessage.success('正在导出漏斗分析数据...')
}

// 监听视图切换
watch(tableView, () => {
  fetchData()
})

// 初始化
onMounted(() => {
  initFunnelChart()
  fetchData()
})
</script>

<style lang="scss" scoped>
.recruitment-funnel {
  padding: 20px;
  
  .filter-card {
    margin-bottom: 20px;
  }
  
  .chart-card,
  .stats-card {
    margin-bottom: 20px;
    height: 100%;
  }
  
  // 转化率统计
  .conversion-stats {
    .stat-item {
      margin-bottom: 20px;
      
      .stage-info {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        
        .stage-name {
          font-size: 14px;
          color: #303133;
        }
        
        .stage-count {
          font-size: 14px;
          color: #606266;
          font-weight: bold;
        }
      }
      
      .conversion-info {
        .rate-bar {
          margin-bottom: 5px;
        }
        
        .rate-detail {
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .rate-value {
            font-size: 16px;
            font-weight: bold;
            color: #409eff;
          }
          
          .rate-trend {
            font-size: 12px;
            
            &.up {
              color: #67c23a;
            }
            
            &.down {
              color: #f56c6c;
            }
          }
        }
      }
    }
  }
  
  // 关键指标
  .key-metrics {
    margin-top: 20px;
    
    .metric-item {
      text-align: center;
      
      .metric-value {
        font-size: 24px;
        font-weight: bold;
        color: #409eff;
        margin-bottom: 5px;
      }
      
      .metric-label {
        font-size: 12px;
        color: #909399;
      }
    }
  }
  
  // 表格样式
  .table-card {
    margin-bottom: 20px;
    
    .stage-number {
      font-weight: bold;
      color: #303133;
    }
    
    .stage-rate {
      font-size: 12px;
      color: #909399;
      margin-left: 5px;
    }
  }
  
  .suggestion-card {
    :deep(.el-alert) {
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>