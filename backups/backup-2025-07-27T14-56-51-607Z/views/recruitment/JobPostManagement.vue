<template>
  <div class="job-post-management">
    <!-- 搜索栏 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="职位标题">
          <el-input 
            v-model="searchForm.title" 
            clearable 
            placeholder="请输入职位标题"
            style="width: 200px"
            />
        </el-form-item>
        <el-form-item label="工作地点">
          <el-input 
            v-model="searchForm.workLocation" 
            clearable 
            placeholder="请输入工作地点"
            style="width: 200px"
            />
        </el-form-item>
        <el-form-item label="状态">
          <el-select 
            v-model="searchForm.status" 
            clearable 
            placeholder="请选择"
            style="width: 120px"
          >
            <el-option label="草稿" value="草稿"  />
            <el-option label="已发布" value="已发布"  />
            <el-option label="已下线" value="已下线"  />
            <el-option label="已关闭" value="已关闭"  />
          </el-select>
        </el-form-item>
        <el-form-item label="紧急招聘">
          <el-select 
            v-model="searchForm.isUrgent" 
            clearable 
            placeholder="请选择"
            style="width: 120px"
          >
            <el-option label="是" :value="true"  />
            <el-option label="否" :value="false"  />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作栏 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>职位列表</span>
          <div>
            <el-button @click="handleBatchOperation('publish')">
              批量发布
            </el-button>
            <el-button @click="handleBatchOperation('offline')">
              批量下线
            </el-button>
            <el-button type="primary" @click="handleCreate">
              <el-icon><Plus /></el-icon>
              发布新职位
            </el-button>
          </div>
        </div>
      </template>

      <!-- 数据表格 -->
      <el-table 
        v-loading="loading" 
        :data="tableData" 
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="title" label="职位标题" min-width="180" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="job-title">
              <span>{{ row.title }}</span>
              <el-tag v-if="row.isUrgent" type="danger" size="small" class="urgent-tag">
                急聘
              </el-tag>
              <el-tag v-if="row.isHot" type="warning" size="small" class="hot-tag">
                热门
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="departmentName" label="所属部门" width="120"  />
        <el-table-column prop="workLocation" label="工作地点" width="100"  />
        <el-table-column prop="recruitCount" label="招聘人数" width="90" align="center"  />
        <el-table-column label="薪资范围" width="120">
          <template #default="{ row }">
            {{ row.salaryMin }}-{{ row.salaryMax }}{{ row.salaryUnit }}
          </template>
        </el-table-column>
        <el-table-column prop="education" label="学历要求" width="90"  />
        <el-table-column prop="experience" label="经验要求" width="90"  />
        <el-table-column prop="status" label="状态" width="90" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusTag(row.status)">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="浏览/申请" width="100" align="center">
          <template #default="{ row }">
            {{ row.viewCount }}/{{ row.applyCount }}
          </template>
        </el-table-column>
        <el-table-column prop="publishTime" label="发布时间" width="160"  />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleView(row)">查看</el-button>
            <el-button 
              v-if="['草稿', '已下线'].includes(row.status)" 
              link 
              type="primary" 
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button 
              v-if="['草稿', '已下线'].includes(row.status)" 
              link 
              type="primary" 
              @click="handlePublish(row)"
            >
              发布
            </el-button>
            <el-button 
              v-if="row.status === '已发布'" 
              link 
              type="warning" 
              @click="handleOffline(row)"
            >
              下线
            </el-button>
            <el-button 
              v-if="row.status === '已发布'" 
              link 
              type="primary" 
              @click="handleViewApplications(row)"
            >
              查看申请
            </el-button>
            <el-button 
              v-if="row.status === '草稿'" 
              link 
              type="danger" 
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="dialogTitle" 
      width="900px"
      @close="handleDialogClose"
    >
      <el-form 
        ref="formRef" 
        :model="formData" 
        :rules="formRules" 
        label-width="120px"
      >
        <el-tabs v-model="activeTab">
          <el-tab-pane label="基本信息" name="basic">
            <el-form-item label="职位标题" prop="title">
              <el-input 
                v-model="formData.title" 
                placeholder="请输入职位标题"
                maxlength="50"
                show-word-limit
                />
            </el-form-item>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="所属部门" prop="departmentId">
                  <el-select 
                    v-model="formData.departmentId" 
                    placeholder="请选择部门"
                    style="width: 100%"
                  >
                    <el-option 
                      v-for="dept in departments" 
                      :key="dept.id" 
                      :label="dept.name" 
                      :value="dept.id" 
                     />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="工作地点" prop="workLocation">
                  <el-input 
                    v-model="formData.workLocation" 
                    placeholder="请输入工作地点"
                    />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="工作类型" prop="jobType">
                  <el-select 
                    v-model="formData.jobType" 
                    placeholder="请选择"
                    style="width: 100%"
                  >
                    <el-option label="全职" value="全职"  />
                    <el-option label="兼职" value="兼职"  />
                    <el-option label="实习" value="实习"  />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="招聘人数" prop="recruitCount">
                  <el-input-number 
                    v-model="formData.recruitCount" 
                    :min="1" 
                    :max="999" 
                    style="width: 100%"
                    />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="截止时间" prop="expireTime">
                  <el-date-picker
                    v-model="formData.expireTime"
                    type="date"
                    placeholder="选择截止日期"
                    style="width: 100%"
                    value-format="YYYY-MM-DD"
                    :disabled-date="disabledDate"
                   />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="薪资范围">
              <el-row :gutter="10">
                <el-col :span="7">
                  <el-input-number 
                    v-model="formData.salaryMin" 
                    :min="0" 
                    placeholder="最低"
                    style="width: 100%"
                    />
                </el-col>
                <el-col :span="1" style="text-align: center">-</el-col>
                <el-col :span="7">
                  <el-input-number 
                    v-model="formData.salaryMax" 
                    :min="0" 
                    placeholder="最高"
                    style="width: 100%"
                    />
                </el-col>
                <el-col :span="9">
                  <el-select 
                    v-model="formData.salaryUnit" 
                    placeholder="单位"
                    style="width: 100%"
                  >
                    <el-option label="千/月" value="K/月"  />
                    <el-option label="万/月" value="万/月"  />
                    <el-option label="万/年" value="万/年"  />
                  </el-select>
                </el-col>
              </el-row>
            </el-form-item>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="学历要求" prop="education">
                  <el-select 
                    v-model="formData.education" 
                    placeholder="请选择"
                    style="width: 100%"
                  >
                    <el-option label="不限" value="不限"  />
                    <el-option label="大专" value="大专"  />
                    <el-option label="本科" value="本科"  />
                    <el-option label="硕士" value="硕士"  />
                    <el-option label="博士" value="博士"  />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="经验要求" prop="experience">
                  <el-select 
                    v-model="formData.experience" 
                    placeholder="请选择"
                    style="width: 100%"
                  >
                    <el-option label="不限" value="不限"  />
                    <el-option label="应届生" value="应届生"  />
                    <el-option label="1-3年" value="1-3年"  />
                    <el-option label="3-5年" value="3-5年"  />
                    <el-option label="5-10年" value="5-10年"  />
                    <el-option label="10年以上" value="10年以上"  />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="职位标签">
              <el-checkbox v-model="formData.isUrgent">急聘</el-checkbox>
              <el-checkbox v-model="formData.isHot" style="margin-left: 20px">热门</el-checkbox>
            </el-form-item>

            <el-form-item label="标签">
              <el-tag
                v-for="tag in formData.tags"
                :key="tag"
                closable
                :disable-transitions="false"
                @close="handleTagClose(tag)"
                style="margin-right: 10px"
              >
                {{ tag }}
              </el-tag>
              <el-input
                v-if="tagInputVisible"
                ref="tagInputRef"
                v-model="tagInputValue"
                size="small"
                style="width: 100px"
                @keyup.enter="handleTagInputConfirm"
                @blur="handleTagInputConfirm"
                />
              <el-button 
                v-else 
                size="small" 
                @click="showTagInput"
              >
                + 添加标签
              </el-button>
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="职位详情" name="detail">
            <el-form-item label="职位描述" prop="jobDescription">
              <el-input 
                v-model="formData.jobDescription" 
                type="textarea" 
                :rows="8" 
                placeholder="请详细描述该职位的工作内容、职责等"
                />
            </el-form-item>

            <el-form-item label="任职要求" prop="requirements">
              <el-input 
                v-model="formData.requirements" 
                type="textarea" 
                :rows="8" 
                placeholder="请详细描述该职位的任职资格、技能要求等"
                />
            </el-form-item>

            <el-form-item label="福利待遇">
              <el-input 
                v-model="formData.benefits" 
                type="textarea" 
                :rows="4" 
                placeholder="请描述公司福利待遇，如五险一金、带薪年假等"
                />
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>

      <template #footer>
        <el-button @click="handleSaveDraft">保存草稿</el-button>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveAndPublish">保存并发布</el-button>
      </template>
    </el-dialog>

    <!-- 职位详情抽屉 -->
    <el-drawer 
      v-model="detailDrawerVisible" 
      title="职位详情" 
      size="700px"
    >
      <div class="job-detail">
        <el-descriptions :column="2" border class="mb-20">
          <el-descriptions-item label="职位标题" :span="2">
            <div class="job-title">
              {{ detailData.title }}
              <el-tag v-if="detailData.isUrgent" type="danger" size="small" class="urgent-tag">
                急聘
              </el-tag>
              <el-tag v-if="detailData.isHot" type="warning" size="small" class="hot-tag">
                热门
              </el-tag>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="所属部门">
            {{ detailData.departmentName }}
          </el-descriptions-item>
          <el-descriptions-item label="工作地点">
            {{ detailData.workLocation }}
          </el-descriptions-item>
          <el-descriptions-item label="工作类型">
            {{ detailData.jobType }}
          </el-descriptions-item>
          <el-descriptions-item label="招聘人数">
            {{ detailData.recruitCount }}
          </el-descriptions-item>
          <el-descriptions-item label="薪资范围">
            {{ detailData.salaryMin }}-{{ detailData.salaryMax }}{{ detailData.salaryUnit }}
          </el-descriptions-item>
          <el-descriptions-item label="学历要求">
            {{ detailData.education }}
          </el-descriptions-item>
          <el-descriptions-item label="经验要求">
            {{ detailData.experience }}
          </el-descriptions-item>
          <el-descriptions-item label="截止时间">
            {{ detailData.expireTime || '长期有效' }}
          </el-descriptions-item>
          <el-descriptions-item label="发布状态">
            <el-tag :type="getStatusTag(detailData.status)">
              {{ detailData.status }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="发布时间">
            {{ detailData.publishTime || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="浏览次数">
            {{ detailData.viewCount }}
          </el-descriptions-item>
          <el-descriptions-item label="申请人数">
            {{ detailData.applyCount }}
          </el-descriptions-item>
        </el-descriptions>

        <el-descriptions :column="1" border class="mb-20">
          <el-descriptions-item label="职位标签">
            <el-tag 
              v-for="tag in detailData.tags" 
              :key="tag"
              style="margin-right: 10px"
            >
              {{ tag }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <h4>职位描述</h4>
        <div class="content-section">
          {{ detailData.jobDescription }}
        </div>

        <h4>任职要求</h4>
        <div class="content-section">
          {{ detailData.requirements }}
        </div>

        <h4>福利待遇</h4>
        <div class="content-section">
          {{ detailData.benefits || '暂无' }}
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox, ElInput } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { jobPostApi } from '@/api/recruitment'
import { recruitmentNeedApi } from '@/api/recruitment/needs'
import type { JobPost } from '@/types/recruitment'
import type { FormInstance, FormRules } from 'element-plus'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

// 搜索表单
const searchForm = reactive({
  title: '',
  workLocation: '',
  status: '',
  isUrgent: undefined as boolean | undefined
})

// 表格数据
const loading = ref(false)
const tableData = ref<JobPost[]>([])
const selectedRows = ref<JobPost[]>([])

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 对话框
const dialogVisible = ref(false)
const dialogTitle = ref('发布新职位')
const activeTab = ref('basic')
const formRef = ref<FormInstance>()
const formData = ref<Partial<JobPost>>({
  jobType: '全职',
  recruitCount: 1,
  education: '本科',
  experience: '1-3年',
  salaryUnit: 'K/月',
  isUrgent: false,
  isHot: false,
  tags: []
})

// 标签输入
const tagInputVisible = ref(false)
const tagInputValue = ref('')
const tagInputRef = ref<InstanceType<typeof ElInput>>()

// 表单验证规则
const formRules = reactive<FormRules>({
  title: [
    { required: true, message: '请输入职位标题', trigger: 'blur' }
  ],
  departmentId: [
    { required: true, message: '请选择所属部门', trigger: 'change' }
  ],
  workLocation: [
    { required: true, message: '请输入工作地点', trigger: 'blur' }
  ],
  jobType: [
    { required: true, message: '请选择工作类型', trigger: 'change' }
  ],
  recruitCount: [
    { required: true, message: '请输入招聘人数', trigger: 'blur' }
  ],
  education: [
    { required: true, message: '请选择学历要求', trigger: 'change' }
  ],
  experience: [
    { required: true, message: '请选择经验要求', trigger: 'change' }
  ],
  jobDescription: [
    { required: true, message: '请输入职位描述', trigger: 'blur' }
  ],
  requirements: [
    { required: true, message: '请输入任职要求', trigger: 'blur' }
  ]
})

// 详情抽屉
const detailDrawerVisible = ref(false)
const detailData = ref<JobPost>({} as JobPost)

// 部门数据（模拟）
const departments = ref([
  { id: 1, name: 'HrHr技术部' },
  { id: 2, name: '市场部' },
  { id: 3, name: '人事部' },
  { id: 4, name: '财务部' }
])

// 标签类型映射
const getStatusTag = (status: string) => {
  const map: Record<string, string> = {
    '草稿': 'info',
    '已发布': 'success',
    '已下线': 'warning',
    '已关闭': 'danger'
  }
  return map[status] || 'info'
}

// 禁用过去的日期
const disabledDate = (time: Date) => {
  return time.getTime() < Date.now() - 8.64e7
}

// 获取列表数据
const fetchData = async () => {
  loading.value = true
  try {
    const {data: _data} =  await jobPostApi.getList({
      ...searchForm,
      page: pagination.page,
      size: pagination.size
    })
    tableData.value 
  }
  
  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
  
  .job-title {
    display: flex;
    align-items: center;
    gap: 5px;
    
    .urgent-tag,
    .hot-tag {
      margin-left: 5px;
    }
  }
}

.job-detail {
  padding: 20px;
  
  h4 {
    margin: 20px 0 10px;
    color: #303133;
  }
  
  .content-section {
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;
    white-space: pre-wrap;
    line-height: 1.6;
  }
  
  .mb-20 {
    margin-bottom: 20px;
  }
}
</style>