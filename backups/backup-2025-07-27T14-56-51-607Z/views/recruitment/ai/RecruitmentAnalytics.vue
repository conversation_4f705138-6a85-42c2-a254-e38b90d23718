<template>
  <div class="recruitment-analytics">
    <!-- 顶部统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card>
          <el-statistic title="本月发布职位" :value="stats.publishedJobs">
            <template #suffix>个</template>
          </el-statistic>
          <div class="trend">
            <span>环比上月</span>
            <span class="value up">+12.5%</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <el-statistic title="收到简历" :value="stats.receivedResumes">
            <template #suffix>份</template>
          </el-statistic>
          <div class="trend">
            <span>环比上月</span>
            <span class="value up">+23.8%</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <el-statistic title="入职人数" :value="stats.hiredCount">
            <template #suffix>人</template>
          </el-statistic>
          <div class="trend">
            <span>环比上月</span>
            <span class="value down">-5.2%</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <el-statistic title="平均招聘周期" :value="stats.avgCycle" :precision="1">
            <template #suffix>天</template>
          </el-statistic>
          <div class="trend">
            <span>环比上月</span>
            <span class="value up">-3.5天</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 分析维度选择 -->
    <el-card class="filter-card">
      <el-row :gutter="20" align="middle">
        <el-col :span="4">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="refreshData"
           />
        </el-col>
        <el-col :span="3">
          <el-select v-model="filterDepartment" placeholder="部门" clearable @change="refreshData">
            <el-option label="技术部" value="tech"  />
            <el-option label="产品部" value="product"  />
            <el-option label="市场部" value="market"  />
            <el-option label="运营部" value="operation"  />
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select v-model="filterPosition" placeholder="职位类型" clearable @change="refreshData">
            <el-option label="技术类" value="technical"  />
            <el-option label="产品类" value="product"  />
            <el-option label="管理类" value="management"  />
            <el-option label="职能类" value="functional"  />
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select v-model="filterChannel" placeholder="招聘渠道" clearable @change="refreshData">
            <el-option label="招聘网站" value="website"  />
            <el-option label="内部推荐" value="referral"  />
            <el-option label="猎头" value="headhunter"  />
            <el-option label="校园招聘" value="campus"  />
          </el-select>
        </el-col>
        <el-col :span="11" style="text-align: right">
          <el-button @click="exportReport">导出报告</el-button>
          <el-button type="primary" @click="generateAIReport" :loading="generating">
            生成AI分析报告
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 图表区域 -->
    <el-row :gutter="20">
      <!-- 招聘漏斗 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>招聘漏斗分析</span>
          </template>
          <div id="funnelChart" style="height: 400px"></div>
          <div class="funnel-stats">
            <el-row :gutter="20">
              <el-col :span="8" v-for="(item, index) in funnelStats" :key="index">
                <div class="funnel-stat-item">
                  <div class="label">{{ item.label }}</div>
                  <div class="value">{{ item.value }}%</div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>

      <!-- 渠道效果 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>渠道效果分析</span>
          </template>
          <div id="channelChart" style="height: 400px"></div>
          <div class="channel-table">
            <el-table :data="channelData" size="small">
              <el-table-column prop="channel" label="渠道"  />
              <el-table-column prop="cost" label="成本(元)" align="right">
                <template #default="{ row }">
                  {{ row.cost.toLocaleString() }}
                </template>
              </el-table-column>
              <el-table-column prop="hires" label="入职数" align="center"  />
              <el-table-column prop="roi" label="ROI" align="center">
                <template #default="{ row }">
                  <el-tag :type="row.roi > 3 ? 'success' : row.roi > 1 ? 'warning' : 'danger'">
                    {{ row.roi.toFixed(2) }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px">
      <!-- 时间趋势 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>招聘趋势分析</span>
            <el-radio-group v-model="trendType" size="small" style="float: right">
              <el-radio-button label="daily">日</el-radio-button>
              <el-radio-button label="weekly">周</el-radio-button>
              <el-radio-button label="monthly">月</el-radio-button>
            </el-radio-group>
          </template>
          <div id="trendChart" style="height: 350px"></div>
        </el-card>
      </el-col>

      <!-- 部门对比 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>部门招聘对比</span>
          </template>
          <div id="departmentChart" style="height: 350px"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- AI分析报告 -->
    <el-card v-if="aiReport" class="ai-report-card">
      <template #header>
        <div class="ai-header">
          <span>AI智能分析报告</span>
          <el-tag type="info">基于{{ dateRange[0] }}至{{ dateRange[1] }}的数据</el-tag>
        </div>
      </template>

      <div class="ai-report-content">
        <!-- 总体评价 -->
        <div class="report-section">
          <h3>
            <el-icon><data-analysis /></el-icon>
            总体评价
          </h3>
          <div class="overall-score">
            <el-progress 
              type="dashboard" 
              :percentage="aiReport.overallScore" 
              :color="getScoreColor"
              :width="120"
            >
              <template #default="{ percentage }">
                <span class="percentage-value">{{ percentage }}</span>
                <span class="percentage-label">综合得分</span>
              </template>
            </el-progress>
            <div class="score-desc">
              <p>{{ aiReport.overallComment }}</p>
            </div>
          </div>
        </div>

        <!-- 关键发现 -->
        <div class="report-section">
          <h3>
            <el-icon><search /></el-icon>
            关键发现
          </h3>
          <div class="findings-grid">
            <div 
              v-for="(finding, index) in aiReport.keyFindings" 
              :key="index"
              class="finding-item"
              :class="finding.type"
            >
              <el-icon :size="24">
                <warning-filled v-if="finding.type === 'warning'" />
                <success-filled v-else-if="finding.type === 'success'" />
                <info-filled v-else />
              </el-icon>
              <div class="finding-content">
                <h4>{{ finding.title }}</h4>
                <p>{{ finding.description }}</p>
                <div class="finding-data">
                  <span>{{ finding.metric }}</span>
                  <strong>{{ finding.value }}</strong>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 优化建议 -->
        <div class="report-section">
          <h3>
            <el-icon><promotion /></el-icon>
            优化建议
          </h3>
          <el-timeline>
            <el-timeline-item 
              v-for="(suggestion, index) in aiReport.suggestions" 
              :key="index"
              :color="suggestion.priority === 'high' ? 'red' : suggestion.priority === 'medium' ? 'orange' : 'green'"
            >
              <div class="suggestion-item">
                <div class="suggestion-header">
                  <strong>{{ suggestion.title }}</strong>
                  <el-tag :type="getPriorityType(suggestion.priority)" size="small">
                    {{ getPriorityLabel(suggestion.priority) }}
                  </el-tag>
                </div>
                <p>{{ suggestion.description }}</p>
                <div class="suggestion-impact">
                  预期效果：{{ suggestion.expectedImpact }}
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>

        <!-- 预测分析 -->
        <div class="report-section">
          <h3>
            <el-icon><trend-charts /></el-icon>
            未来预测
          </h3>
          <el-row :gutter="20">
            <el-col :span="8" v-for="(prediction, index) in aiReport.predictions" :key="index">
              <div class="prediction-card">
                <div class="prediction-title">{{ prediction.metric }}</div>
                <div class="prediction-value">
                  {{ prediction.currentValue }}
                  <el-icon :color="prediction.trend === 'up' ? '#67c23a' : '#f56c6c'">
                    <arrow-up v-if="prediction.trend === 'up'" />
                    <arrow-down v-else />
                  </el-icon>
                  {{ prediction.predictedValue }}
                </div>
                <div class="prediction-desc">{{ prediction.description }}</div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-card>

    <!-- 详细数据表格 -->
    <el-card class="data-table-card">
      <template #header>
        <span>详细数据</span>
        <el-button-group style="float: right">
          <el-button size="small" :type="tableView === 'position' ? 'primary' : ''" @click="tableView = 'position'">
            按职位
          </el-button>
          <el-button size="small" :type="tableView === 'department' ? 'primary' : ''" @click="tableView = 'department'">
            按部门
          </el-button>
          <el-button size="small" :type="tableView === 'hr' ? 'primary' : ''" @click="tableView = 'hr'">
            按HR
          </el-button>
        </el-button-group>
      </template>

      <el-table :data="tableData" stripe>
        <el-table-column prop="name" :label="tableView === 'hr' ? 'HR' : tableView === 'department' ? '部门' : '职位'" fixed  />
        <el-table-column prop="published" label="发布数" align="center"  />
        <el-table-column prop="received" label="收到简历" align="center"  />
        <el-table-column prop="screened" label="通过筛选" align="center"  />
        <el-table-column prop="interviewed" label="面试数" align="center"  />
        <el-table-column prop="offered" label="发放Offer" align="center"  />
        <el-table-column prop="hired" label="入职数" align="center"  />
        <el-table-column label="转化率" align="center">
          <template #default="{ row }">
            <el-progress 
              :percentage="row.conversionRate" 
              :stroke-width="6"
              :color="getProgressColor(row.conversionRate)"
             />
          </template>
        </el-table-column>
        <el-table-column prop="avgDays" label="平均周期(天)" align="center"  />
        <el-table-column label="效率评分" align="center">
          <template #default="{ row }">
            <el-rate 
              v-model="row.efficiency" 
              disabled 
              show-score 
              text-color="#ff9900"
              score-template="{value}"
             />
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  DataAnalysis, 
  Search, 
  Promotion, 
  TrendCharts,
  WarningFilled,
  SuccessFilled,
  InfoFilled,
  ArrowUp,
  ArrowDown
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { recruitmentAnalyticsApi } from '@/api/recruitment'

// 数据状态
const dateRange = ref(['2025-01-01', '2025-01-31'])
const filterDepartment = ref('')
const filterPosition = ref('')
const filterChannel = ref('')
const trendType = ref('daily')
const tableView = ref('position')
const generating = ref(false)
const loading = ref(false)
const aiReport = ref<unknown>(null)

// 统计数据
const stats = reactive({
  publishedJobs: 45,
  receivedResumes: 1280,
  hiredCount: 23,
  avgCycle: 18.5
})

// 图表数据
const funnelData = ref<any[]>([])
const channelDataChart = ref<any[]>([])
const departmentData = ref<any[]>([])
const trendData = ref<number[]>([180, 220, 260, 320, 300])

// 筛选器对象
const filters = reactive({
  dateRange: dateRange.value,
  department: filterDepartment.value,
  position: filterPosition.value,
  channel: filterChannel.value
})

// 概览数据
const overviewData = reactive({
  totalJobs: 45,
  avgTimeToHire: 18.5,
  totalApplications: 1280,
  avgCostPerHire: 5000
})

// 漏斗统计
const funnelStats = ref([
  { label: '简历-面试', value: 15.6 },
  { label: '面试-Offer', value: 45.2 },
  { label: 'Offer-入职', value: 85.3 }
])

// 渠道数据
const channelData = ref([
  { channel: '招聘网站', cost: 12000, hires: 8, roi: 3.2 },
  { channel: '内部推荐', cost: 5000, hires: 10, roi: 8.5 },
  { channel: '猎头', cost: 45000, hires: 4, roi: 1.8 },
  { channel: '校园招聘', cost: 8000, hires: 1, roi: 0.5 }
])

// 表格数据
const tableData = ref([
  {
    name: 'HrHr前端工程师',
    published: 5,
    received: 156,
    screened: 45,
    interviewed: 23,
    offered: 8,
    hired: 6,
    conversionRate: 75,
    avgDays: 15,
    efficiency: 4.2
  },
  {
    name: 'Java工程师',
    published: 3,
    received: 89,
    screened: 28,
    interviewed: 15,
    offered: 5,
    hired: 4,
    conversionRate: 80,
    avgDays: 18,
    efficiency: 4.0
  },
  {
    name: '产品经理',
    published: 2,
    received: 67,
    screened: 20,
    interviewed: 12,
    offered: 3,
    hired: 2,
    conversionRate: 67,
    avgDays: 22,
    efficiency: 3.5
  }
])

// 颜色配置
const getScoreColor = (percentage: number) => {
  if (percentage >= 80) return '#67c23a'
  if (percentage >= 60) return '#409eff'
  if (percentage >= 40) return '#e6a23c'
  return '#f56c6c'
}

const getProgressColor = (percentage: number) => {
  if (percentage >= 80) return '#67c23a'
  if (percentage >= 60) return '#409eff'
  if (percentage >= 40) return '#e6a23c'
  return '#f56c6c'
}

const getPriorityType = (priority: string) => {
  const map: Record<string, string> = {
    high: 'danger',
    medium: 'warning',
    low: 'success'
  }
  return map[priority] || 'info'
}

const getPriorityLabel = (priority: string) => {
  const map: Record<string, string> = {
    high: '高优先级',
    medium: '中优先级',
    low: '低优先级'
  }
  return map[priority] || priority
}

// 初始化图表
const initCharts = () => {
  // 招聘漏斗图
  const funnelChart = echarts.init(document.getElementById('funnelChart'))
  const funnelOption = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c} ({d}%)'
    },
    series: [
      {
        name: '招聘漏斗',
        type: 'funnel',
        left: '10%',
        top: 60,
        bottom: 60,
        width: '80%',
        min: 0,
        max: 100,
        minSize: '0%',
        maxSize: '100%',
        sort: 'descending',
        gap: 2,
        label: {
          show: true,
          position: 'inside'
        },
        labelLine: {
          length: 10,
          lineStyle: {
            width: 1,
            type: 'solid'
          }
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1
        },
        emphasis: {
          label: {
            fontSize: 20
          }
        },
        data: [
          { value: 100, name: '收到简历 1280' },
          { value: 30, name: '通过筛选 384' },
          { value: 15, name: '参加面试 192' },
          { value: 8, name: '发放Offer 102' },
          { value: 5, name: '成功入职 64' }
        ]
      }
    ]
  }
  funnelChart.setOption(funnelOption)

  // 渠道效果图
  const channelChart = echarts.init(document.getElementById('channelChart'))
  const channelOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['简历数', '入职数', '成本(千元)']
    },
    xAxis: {
      type: 'category',
      data: ['招聘网站', '内部推荐', '猎头', '校园招聘']
    },
    yAxis: [
      {
        type: 'value',
        name: '数量',
        min: 0,
        max: 500
      },
      {
        type: 'value',
        name: '成本(千元)',
        min: 0,
        max: 50
      }
    ],
    series: [
      {
        name: '简历数',
        type: 'bar',
        data: [450, 280, 120, 80]
      },
      {
        name: '入职数',
        type: 'bar',
        data: [8, 10, 4, 1]
      },
      {
        name: '成本(千元)',
        type: 'line',
        yAxisIndex: 1,
        data: [12, 5, 45, 8]
      }
    ]
  }
  channelChart.setOption(channelOption)

  // 趋势图
  const trendChart = echarts.init(document.getElementById('trendChart'))
  const trendOption = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['发布职位', '收到简历', '面试安排', '入职人数']
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['1月1日', '1月8日', '1月15日', '1月22日', '1月29日']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '发布职位',
        type: 'line',
        data: [8, 12, 10, 15, 10]
      },
      {
        name: '收到简历',
        type: 'line',
        data: [180, 220, 260, 320, 300]
      },
      {
        name: '面试安排',
        type: 'line',
        data: [30, 45, 52, 48, 55]
      },
      {
        name: '入职人数',
        type: 'line',
        data: [3, 5, 6, 4, 5]
      }
    ]
  }
  trendChart.setOption(trendOption)

  // 部门对比图
  const departmentChart = echarts.init(document.getElementById('departmentChart'))
  const departmentOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['需求数', '完成数', '完成率']
    },
    xAxis: {
      type: 'category',
      data: ['技术部', '产品部', '市场部', '运营部', '财务部']
    },
    yAxis: [
      {
        type: 'value',
        name: '人数',
        min: 0,
        max: 20
      },
      {
        type: 'value',
        name: '完成率(%)',
        min: 0,
        max: 100
      }
    ],
    series: [
      {
        name: '需求数',
        type: 'bar',
        data: [15, 8, 6, 5, 3]
      },
      {
        name: '完成数',
        type: 'bar',
        data: [12, 6, 5, 3, 3]
      },
      {
        name: '完成率',
        type: 'line',
        yAxisIndex: 1,
        data: [80, 75, 83, 60, 100],
        markLine: {
          data: [{ type: 'average', name: '平均值' }]
        }
      }
    ]
  }
  departmentChart.setOption(departmentOption)

  // 响应式
  window.addEventListener('resize', () => {
    funnelChart.resize()
    channelChart.resize()
    trendChart.resize()
    departmentChart.resize()
  })
}

// 刷新数据
const refreshData = async () => {
  loading.value = true
  try {
    // 根据筛选条件获取最新数据
    const params = {
      dateRange: dateRange.value,
      departmentIds: filterDepartment.value ? [getDepartmentId(filterDepartment.value)] : undefined,
      positionIds: filterPosition.value ? [filterPosition.value] : undefined,
      analysisType: 'OVERVIEW' as const,
      includeRecommendations: true
    }
    
    const {data} =  await recruitmentAnalyticsApi.getEffectivenessAnalysis(params)
    
    // 更新仪表板数据
    if (data.summary) {
      overviewData.totalJobs 
  
  // 统计卡片
  .stats-cards {
    margin-bottom: 20px;
    
    .el-card {
      text-align: center;
      
      .trend {
        margin-top: 10px;
        font-size: 12px;
        color: #909399;
        
        .value {
          margin-left: 5px;
          font-weight: bold;
          
          &.up {
            color: #67c23a;
          }
          
          &.down {
            color: #f56c6c;
          }
        }
      }
    }
  }
  
  // 筛选卡片
  .filter-card {
    margin-bottom: 20px;
  }
  
  // 图表卡片
  .chart-card {
    margin-bottom: 20px;
    
    .funnel-stats {
      margin-top: 20px;
      padding-top: 20px;
      border-top: 1px solid #ebeef5;
      
      .funnel-stat-item {
        text-align: center;
        
        .label {
          font-size: 12px;
          color: #909399;
        }
        
        .value {
          font-size: 20px;
          font-weight: bold;
          color: #409eff;
          margin-top: 5px;
        }
      }
    }
    
    .channel-table {
      margin-top: 20px;
      padding-top: 20px;
      border-top: 1px solid #ebeef5;
    }
  }
  
  // AI报告卡片
  .ai-report-card {
    margin-top: 20px;
    
    .ai-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .ai-report-content {
      .report-section {
        margin-bottom: 30px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        h3 {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 20px;
          color: #303133;
        }
      }
      
      // 总体评价
      .overall-score {
        display: flex;
        align-items: center;
        gap: 30px;
        
        :deep(.el-progress) {
          .percentage-value {
            display: block;
            font-size: 24px;
            font-weight: bold;
          }
          
          .percentage-label {
            display: block;
            font-size: 12px;
            color: #909399;
            margin-top: 5px;
          }
        }
        
        .score-desc {
          flex: 1;
          
          p {
            margin: 0;
            color: #606266;
            line-height: 1.6;
          }
        }
      }
      
      // 关键发现
      .findings-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
        
        .finding-item {
          display: flex;
          gap: 15px;
          padding: 20px;
          border: 1px solid #ebeef5;
          border-radius: 8px;
          
          &.success {
            border-color: #67c23a;
            background-color: #f0f9ff;
            
            .el-icon {
              color: #67c23a;
            }
          }
          
          &.warning {
            border-color: #e6a23c;
            background-color: #fdf6ec;
            
            .el-icon {
              color: #e6a23c;
            }
          }
          
          &.info {
            border-color: #909399;
            background-color: #f4f4f5;
            
            .el-icon {
              color: #909399;
            }
          }
          
          .finding-content {
            flex: 1;
            
            h4 {
              margin: 0 0 8px;
              font-size: 16px;
              color: #303133;
            }
            
            p {
              margin: 0 0 10px;
              font-size: 13px;
              color: #606266;
              line-height: 1.5;
            }
            
            .finding-data {
              display: flex;
              justify-content: space-between;
              align-items: center;
              font-size: 12px;
              color: #909399;
              
              strong {
                font-size: 18px;
                color: #409eff;
              }
            }
          }
        }
      }
      
      // 优化建议
      .suggestion-item {
        .suggestion-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
        }
        
        p {
          margin: 0 0 8px;
          color: #606266;
          line-height: 1.5;
        }
        
        .suggestion-impact {
          font-size: 12px;
          color: #67c23a;
          font-weight: bold;
        }
      }
      
      // 预测分析
      .prediction-card {
        padding: 20px;
        border: 1px solid #ebeef5;
        border-radius: 8px;
        text-align: center;
        
        .prediction-title {
          font-size: 14px;
          color: #909399;
          margin-bottom: 10px;
        }
        
        .prediction-value {
          font-size: 18px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 10px;
          
          .el-icon {
            font-size: 20px;
          }
        }
        
        .prediction-desc {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
  
  // 数据表格
  .data-table-card {
    margin-top: 20px;
  }
}
</style>