<template>
  <div class="resume-parser">
    <el-card class="upload-card">
      <template #header>
        <div class="card-header">
          <span>简历智能解析</span>
          <el-tag type="info">支持PDF、Word、图片格式</el-tag>
        </div>
      </template>

      <!-- 上传区域 -->
      <el-upload
        class="upload-dragger"
        drag
        :action="uploadUrl"
        :before-upload="beforeUpload"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        :on-progress="handleUploadProgress"
        :show-file-list="false"
        accept=".pdf,.doc,.docx,.png,.jpg,.jpeg"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将简历文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            支持 PDF、Word、图片格式，单个文件不超过10MB
          </div>
        </template>
      </el-upload>

      <!-- 批量上传列表 -->
      <div v-if="uploadList.length > 0" class="upload-list">
        <el-table :data="uploadList" style="width: 100%">
          <el-table-column prop="name" label="文件名" min-width="200">
            <template #default="{ row }">
              <div class="file-info">
                <el-icon :size="20">
                  <document v-if="row.type === 'pdf'" />
                  <document-copy v-else-if="row.type === 'word'" />
                  <picture v-else />
                </el-icon>
                <span>{{ row.name }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="size" label="大小" width="100">
            <template #default="{ row }">
              {{ formatFileSize(row.size) }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="150">
            <template #default="{ row }">
              <el-tag v-if="row.status === 'uploading'" type="info">
                上传中...
              </el-tag>
              <el-tag v-else-if="row.status === 'parsing'" type="warning">
                <el-icon class="is-loading"><loading /></el-icon>
                解析中...
              </el-tag>
              <el-tag v-else-if="row.status === 'success'" type="success">
                解析成功
              </el-tag>
              <el-tag v-else-if="row.status === 'error'" type="danger">
                解析失败
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button 
                v-if="row.status === 'success'" 
                link 
                type="primary" 
                @click="viewParsedResult(row)"
              >
                查看结果
              </el-button>
              <el-button 
                v-if="row.status === 'error'" 
                link 
                type="primary" 
                @click="retryParse(row)"
              >
                重试
              </el-button>
              <el-button link type="danger" @click="removeFile(row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 解析结果对话框 -->
    <el-dialog 
      v-model="resultDialogVisible" 
      :title="`解析结果 - ${currentResult?.fileName}`" 
      width="1000px"
      top="5vh"
    >
      <div v-if="currentResult" class="parse-result">
        <el-row :gutter="20">
          <!-- 左侧：基本信息 -->
          <el-col :span="12">
            <el-card class="info-section">
              <template #header>
                <span>基本信息</span>
                <el-tag type="success" size="small" style="margin-left: 10px">
                  置信度: {{ currentResult.confidence }}%
                </el-tag>
              </template>
              
              <el-form label-width="100px">
                <el-form-item label="姓名">
                  <el-input v-model="currentResult.basicInfo.name"   />
                </el-form-item>
                <el-form-item label="性别">
                  <el-radio-group v-model="currentResult.basicInfo.gender">
                    <el-radio label="男">男</el-radio>
                    <el-radio label="女">女</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="年龄">
                  <el-input-number v-model="currentResult.basicInfo.age" :min="18" :max="65"   />
                </el-form-item>
                <el-form-item label="手机">
                  <el-input v-model="currentResult.basicInfo.phone"   />
                </el-form-item>
                <el-form-item label="邮箱">
                  <el-input v-model="currentResult.basicInfo.email"   />
                </el-form-item>
                <el-form-item label="学历">
                  <el-select v-model="currentResult.basicInfo.education">
                    <el-option label="博士" value="博士"  />
                    <el-option label="硕士" value="硕士"  />
                    <el-option label="本科" value="本科"  />
                    <el-option label="大专" value="大专"  />
                    <el-option label="高中" value="高中"  />
                  </el-select>
                </el-form-item>
                <el-form-item label="工作年限">
                  <el-input-number v-model="currentResult.basicInfo.experience" :min="0" :max="50"   />
                  <span style="margin-left: 10px">年</span>
                </el-form-item>
                <el-form-item label="期望薪资">
                  <el-input v-model="currentResult.basicInfo.expectedSalary" placeholder="如：15-20k"   />
                </el-form-item>
                <el-form-item label="求职状态">
                  <el-select v-model="currentResult.basicInfo.jobStatus">
                    <el-option label="离职-随时到岗" value="离职-随时到岗"  />
                    <el-option label="在职-考虑机会" value="在职-考虑机会"  />
                    <el-option label="在职-暂不考虑" value="在职-暂不考虑"  />
                    <el-option label="在职-月内到岗" value="在职-月内到岗"  />
                  </el-select>
                </el-form-item>
              </el-form>
            </el-card>
          </el-col>

          <!-- 右侧：详细信息 -->
          <el-col :span="12">
            <el-tabs v-model="activeTab">
              <!-- 工作经历 -->
              <el-tab-pane label="工作经历" name="experience">
                <div class="experience-list">
                  <div 
                    v-for="(exp, index) in currentResult.workExperience" 
                    :key="index"
                    class="experience-item"
                  >
                    <div class="exp-header">
                      <strong>{{ exp.company }}</strong>
                      <el-tag size="small">{{ exp.duration }}</el-tag>
                    </div>
                    <div class="exp-content">
                      <p class="position">{{ exp.position }}</p>
                      <p class="description">{{ exp.description }}</p>
                    </div>
                    <el-divider v-if="index < currentResult.workExperience.length - 1"   />
                  </div>
                </div>
              </el-tab-pane>

              <!-- 教育经历 -->
              <el-tab-pane label="教育经历" name="education">
                <div class="education-list">
                  <div 
                    v-for="(edu, index) in currentResult.education" 
                    :key="index"
                    class="education-item"
                  >
                    <div class="edu-header">
                      <strong>{{ edu.school }}</strong>
                      <el-tag size="small">{{ edu.duration }}</el-tag>
                    </div>
                    <div class="edu-content">
                      <p>{{ edu.major }} | {{ edu.degree }}</p>
                    </div>
                    <el-divider v-if="index < currentResult.education.length - 1"   />
                  </div>
                </div>
              </el-tab-pane>

              <!-- 技能标签 -->
              <el-tab-pane label="技能标签" name="skills">
                <div class="skills-section">
                  <div class="skill-category" v-for="(skills, category) in currentResult.skills" :key="category">
                    <h4>{{ category }}</h4>
                    <div class="skill-tags">
                      <el-tag 
                        v-for="skill in skills" 
                        :key="skill"
                        type="primary"
                        style="margin: 5px"
                      >
                        {{ skill }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </el-tab-pane>

              <!-- 项目经验 -->
              <el-tab-pane label="项目经验" name="projects">
                <div class="project-list">
                  <div 
                    v-for="(project, index) in currentResult.projects" 
                    :key="index"
                    class="project-item"
                  >
                    <div class="project-header">
                      <strong>{{ project.name }}</strong>
                      <el-tag size="small" type="info">{{ project.role }}</el-tag>
                    </div>
                    <div class="project-content">
                      <p class="tech-stack">
                        <span class="label">技术栈：</span>
                        {{ project.technologies.join('、') }}
                      </p>
                      <p class="description">{{ project.description }}</p>
                    </div>
                    <el-divider v-if="index < currentResult.projects.length - 1"   />
                  </div>
                </div>
              </el-tab-pane>

              <!-- AI分析 -->
              <el-tab-pane label="AI分析" name="analysis">
                <div class="ai-analysis">
                  <div class="analysis-item">
                    <h4>关键词提取</h4>
                    <div class="keyword-tags">
                      <el-tag 
                        v-for="keyword in currentResult.analysis.keywords" 
                        :key="keyword"
                        style="margin: 5px"
                      >
                        {{ keyword }}
                      </el-tag>
                    </div>
                  </div>
                  
                  <div class="analysis-item">
                    <h4>能力评估</h4>
                    <el-progress 
                      v-for="ability in currentResult.analysis.abilities" 
                      :key="ability.name"
                      :text-inside="true"
                      :stroke-width="20"
                      :percentage="ability.score"
                      style="margin-bottom: 10px"
                    >
                      <span>{{ ability.name }}</span>
                    </el-progress>
                  </div>
                  
                  <div class="analysis-item">
                    <h4>匹配建议</h4>
                    <el-alert 
                      :title="currentResult.analysis.recommendation"
                      type="info"
                      :closable="false"
                     />
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-col>
        </el-row>
      </div>

      <template #footer>
        <el-button @click="resultDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="saveToDatabase">保存到简历库</el-button>
        <el-button type="success" @click="createCandidate">创建候选人</el-button>
      </template>
    </el-dialog>

    <!-- 批量解析统计 -->
    <el-card v-if="batchStats.total > 0" class="stats-card">
      <template #header>
        <span>批量解析统计</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic title="总数" :value="batchStats.total">
            <template #suffix>份</template>
          </el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="成功" :value="batchStats.success">
            <template #suffix>
              <el-tag type="success" size="small">
                {{ (batchStats.success / batchStats.total * 100).toFixed(1) }}%
              </el-tag>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="失败" :value="batchStats.failed">
            <template #suffix>
              <el-tag type="danger" size="small">
                {{ (batchStats.failed / batchStats.total * 100).toFixed(1) }}%
              </el-tag>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="平均耗时" :value="batchStats.avgTime">
            <template #suffix>秒</template>
          </el-statistic>
        </el-col>
      </el-row>

      <div class="batch-actions">
        <el-button type="primary" @click="exportResults">导出解析结果</el-button>
        <el-button type="success" @click="batchSaveToDatabase">批量保存到简历库</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  UploadFilled, 
  Document, 
  DocumentCopy, 
  Picture, 
  Loading 
} from '@element-plus/icons-vue'
import { resumeApi } from '@/api/recruitment'
import { useRouter } from 'vue-router'

// 数据状态
const uploadUrl = import.meta.env.VITE_API_URL + '/recruitment/resumes/upload-parse'
const uploadList = ref<any[]>([])
const resultDialogVisible = ref(false)
const currentResult = ref<unknown>(null)
const activeTab = ref('experience')

// 批量统计
const batchStats = reactive({
  total: 0,
  success: 0,
  failed: 0,
  avgTime: 0
})

// 格式化文件大小
const formatFileSize = (size: number) => {
  if (size < 1024) return size + 'B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + 'KB'
  return (size / 1024 / 1024).toFixed(1) + 'MB'
}

// 路由实例
const router = useRouter()

// 上传前的验证
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const beforeUpload = (file: unknown) => {
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!')
    return false
  }
  
  // 添加到上传列表
  const fileType = file.name.endsWith('.pdf') ? 'pdf' : 
                   file.name.match(/\.(doc|docx)$/) ? 'word' : 'image'
  
  uploadList.value.push({
    id: Date.now(),
    name: file.name,
    size: file.size,
    type: fileType,
    status: 'uploading',
    file: file
  })
  
  return true
}

// 上传进度
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleUploadProgress = (event: unknown, file: unknown) => {
  const item = uploadList.value.find(f => f.file === file)
  if (item) {
    item.progress = event.percent
  }
}

// 上传成功
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleUploadSuccess = async (response: unknown, file: unknown) => {
  const item = uploadList.value.find(f => f.file === file)
  if (item) {
    item.status = 'parsing'
    const startTime = Date.now()
    
    try {
      // 调用AI解析API
      const {data: _data} =  await resumeApi.uploadAndParse(file)
      
      const parseTime 
  
  .upload-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .upload-dragger {
    width: 100%;
    
    :deep(.el-upload-dragger) {
      height: 200px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
  }
  
  .upload-list {
    margin-top: 20px;
    
    .file-info {
      display: flex;
      align-items: center;
      gap: 10px;
    }
  }
  
  .stats-card {
    margin-top: 20px;
    
    .batch-actions {
      margin-top: 20px;
      text-align: center;
    }
  }
}

// 解析结果样式
.parse-result {
  .info-section {
    height: 100%;
  }
  
  .experience-item,
  .education-item,
  .project-item {
    margin-bottom: 15px;
    
    .exp-header,
    .edu-header,
    .project-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }
    
    .exp-content,
    .edu-content,
    .project-content {
      color: #606266;
      font-size: 14px;
      
      .position {
        color: #409eff;
        margin-bottom: 5px;
      }
      
      .description {
        line-height: 1.6;
      }
      
      .label {
        font-weight: bold;
        color: #303133;
      }
    }
  }
  
  .skills-section {
    .skill-category {
      margin-bottom: 20px;
      
      h4 {
        margin-bottom: 10px;
        color: #303133;
      }
    }
  }
  
  .ai-analysis {
    .analysis-item {
      margin-bottom: 25px;
      
      h4 {
        margin-bottom: 10px;
        color: #303133;
      }
    }
  }
}

:deep(.el-statistic) {
  text-align: center;
}
</style>