<template>
  <div class="job-matcher">
    <el-row :gutter="20">
      <!-- 左侧：职位选择 -->
      <el-col :span="10">
        <el-card class="position-card">
          <template #header>
            <div class="card-header">
              <span>选择职位</span>
              <el-input 
                v-model="searchKeyword" 
                placeholder="搜索职位" 
                :prefix-icon="Search"
                style="width: 200px"
                @input="handleSearch"
                />
            </div>
          </template>

          <!-- 职位列表 -->
          <el-scrollbar height="600px">
            <el-radio-group v-model="selectedPosition" @change="handlePositionChange">
              <div 
                v-for="position in filteredPositions" 
                :key="position.id"
                class="position-item"
              >
                <el-radio :label="position.id">
                  <div class="position-info">
                    <div class="position-header">
                      <strong>{{ position.title }}</strong>
                      <el-tag size="small" :type="position.urgency === '紧急' ? 'danger' : 'primary'">
                        {{ position.urgency }}
                      </el-tag>
                    </div>
                    <div class="position-meta">
                      <span>{{ position.department }}</span>
                      <span>{{ position.location }}</span>
                      <span>{{ position.headcount }}人</span>
                    </div>
                    <div class="position-requirements">
                      <el-tag 
                        v-for="req in position.keyRequirements" 
                        :key="req"
                        size="small"
                        style="margin: 2px"
                      >
                        {{ req }}
                      </el-tag>
                    </div>
                  </div>
                </el-radio>
              </div>
            </el-radio-group>
          </el-scrollbar>
        </el-card>
      </el-col>

      <!-- 中间：匹配控制 -->
      <el-col :span="4">
        <el-card class="control-card">
          <template #header>
            <span>匹配设置</span>
          </template>

          <el-form label-position="top">
            <el-form-item label="匹配算法">
              <el-select v-model="matchAlgorithm" style="width: 100%">
                <el-option label="综合匹配" value="comprehensive"  />
                <el-option label="技能优先" value="skill"  />
                <el-option label="经验优先" value="experience"  />
                <el-option label="学历优先" value="education"  />
              </el-select>
            </el-form-item>

            <el-form-item label="最低匹配度">
              <el-slider 
                v-model="minMatchScore" 
                :min="0" 
                :max="100"
                :marks="{ 0: '0%', 50: '50%', 100: '100%' }"
               />
            </el-form-item>

            <el-form-item label="候选人来源">
              <el-checkbox-group v-model="candidateSources">
                <el-checkbox label="简历库">简历库</el-checkbox>
                <el-checkbox label="人才库">人才库</el-checkbox>
                <el-checkbox label="内部推荐">内部推荐</el-checkbox>
              </el-checkbox-group>
            </el-form-item>

            <el-form-item label="筛选条件">
              <el-checkbox v-model="filters.activeOnly">仅活跃候选人</el-checkbox>
              <el-checkbox v-model="filters.availableOnly">仅可面试</el-checkbox>
              <el-checkbox v-model="filters.noBlacklist">排除黑名单</el-checkbox>
            </el-form-item>

            <el-button type="primary" :icon="Search" @click="startMatching" :loading="matching">
              开始匹配
            </el-button>
          </el-form>
        </el-card>

        <!-- 匹配统计 -->
        <el-card class="stats-card" style="margin-top: 20px">
          <template #header>
            <span>匹配统计</span>
          </template>

          <div class="stats-content">
            <div class="stat-item">
              <div class="stat-value">{{ matchStats.total }}</div>
              <div class="stat-label">候选人总数</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ matchStats.matched }}</div>
              <div class="stat-label">匹配成功</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ matchStats.avgScore }}%</div>
              <div class="stat-label">平均匹配度</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：匹配结果 -->
      <el-col :span="10">
        <el-card class="result-card">
          <template #header>
            <div class="card-header">
              <span>匹配结果</span>
              <div class="actions">
                <el-button link type="primary" @click="exportResults">
                  导出结果
                </el-button>
                <el-button link type="primary" @click="batchInvite">
                  批量邀约
                </el-button>
              </div>
            </div>
          </template>

          <!-- 结果列表 -->
          <el-scrollbar height="600px" v-loading="matching">
            <div v-if="matchResults.length === 0 && !matching" class="empty-state">
              <el-empty description="请选择职位并开始匹配"  />
            </div>

            <div v-else class="result-list">
              <div 
                v-for="candidate in matchResults" 
                :key="candidate.id"
                class="candidate-card"
                @click="viewCandidateDetail(candidate)"
              >
                <!-- 匹配度展示 -->
                <div class="match-score">
                  <el-progress 
                    type="circle" 
                    :percentage="candidate.matchScore" 
                    :width="60"
                    :color="getScoreColor(candidate.matchScore)"
                   />
                </div>

                <!-- 候选人信息 -->
                <div class="candidate-info">
                  <div class="info-header">
                    <strong>{{ candidate.name }}</strong>
                    <el-tag size="small" :type="candidate.source === '人才库' ? 'success' : 'primary'">
                      {{ candidate.source }}
                    </el-tag>
                  </div>
                  <div class="info-content">
                    <p>{{ candidate.currentPosition }} | {{ candidate.experience }}年经验</p>
                    <p>{{ candidate.education }} | {{ candidate.expectedSalary }}</p>
                  </div>
                  
                  <!-- 匹配详情 -->
                  <div class="match-details">
                    <div class="detail-item" v-for="item in candidate.matchDetails" :key="item.factor">
                      <span class="factor">{{ item.factor }}:</span>
                      <el-progress 
                        :percentage="item.score" 
                        :stroke-width="6"
                        :show-text="false"
                        :color="getScoreColor(item.score)"
                       />
                      <span class="score">{{ item.score }}%</span>
                    </div>
                  </div>
                  
                  <!-- 亮点标签 -->
                  <div class="highlights">
                    <el-tag 
                      v-for="highlight in candidate.highlights" 
                      :key="highlight"
                      size="small"
                      type="success"
                      style="margin: 2px"
                    >
                      {{ highlight }}
                    </el-tag>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div class="candidate-actions">
                  <el-button size="small" type="primary" @click.stop="inviteInterview(candidate)">
                    邀约面试
                  </el-button>
                  <el-button size="small" @click.stop="addToTalentPool(candidate)">
                    加入人才库
                  </el-button>
                </div>
              </div>
            </div>
          </el-scrollbar>
        </el-card>
      </el-col>
    </el-row>

    <!-- 候选人详情对话框 -->
    <el-dialog 
      v-model="detailDialogVisible" 
      :title="`候选人详情 - ${currentCandidate?.name}`" 
      width="900px"
    >
      <div v-if="currentCandidate" class="candidate-detail">
        <el-descriptions :column="3" border>
          <el-descriptions-item label="姓名">{{ currentCandidate.name }}</el-descriptions-item>
          <el-descriptions-item label="当前职位">{{ currentCandidate.currentPosition }}</el-descriptions-item>
          <el-descriptions-item label="工作年限">{{ currentCandidate.experience }}年</el-descriptions-item>
          <el-descriptions-item label="学历">{{ currentCandidate.education }}</el-descriptions-item>
          <el-descriptions-item label="期望薪资">{{ currentCandidate.expectedSalary }}</el-descriptions-item>
          <el-descriptions-item label="求职状态">{{ currentCandidate.jobStatus }}</el-descriptions-item>
        </el-descriptions>

        <!-- 匹配分析 -->
        <div class="match-analysis">
          <h3>匹配分析</h3>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="analysis-chart">
                <div id="radarChart" style="height: 300px"></div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="analysis-text">
                <h4>匹配优势</h4>
                <ul>
                  <li v-for="advantage in currentCandidate.advantages" :key="advantage">
                    {{ advantage }}
                  </li>
                </ul>
                <h4>潜在不足</h4>
                <ul>
                  <li v-for="disadvantage in currentCandidate.disadvantages" :key="disadvantage">
                    {{ disadvantage }}
                  </li>
                </ul>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- AI建议 -->
        <el-alert 
          :title="currentCandidate.aiRecommendation"
          type="info"
          show-icon
          :closable="false"
          style="margin-top: 20px"
         />
      </div>

      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="inviteInterview(currentCandidate)">邀约面试</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'JobMatcher'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { jobMatcherApi } from '@/api/recruitment'

// 数据状态
const searchKeyword = ref('')
const selectedPosition = ref('')
const matchAlgorithm = ref('comprehensive')
const minMatchScore = ref(60)
const candidateSources = ref(['简历库', '人才库'])
const matching = ref(false)
const matchResults = ref<any[]>([])
const detailDialogVisible = ref(false)
const currentCandidate = ref<unknown>(null)

// 筛选条件
const filters = reactive({
  activeOnly: true,
  availableOnly: false,
  noBlacklist: true
})

// 匹配统计
const matchStats = reactive({
  total: 0,
  matched: 0,
  avgScore: 0
})

// 模拟职位数据
const positions = ref([
  {
    id: '1',
    title: '高级前端工程师',
    department: '技术部',
    location: '杭州',
    headcount: 2,
    urgency: '紧急',
    keyRequirements: ['Vue.js', '5年经验', '本科']
  },
  {
    id: '2',
    title: 'Java架构师',
    department: '技术部',
    location: '杭州',
    headcount: 1,
    urgency: '正常',
    keyRequirements: ['Java', '8年经验', '分布式']
  },
  {
    id: '3',
    title: '产品经理',
    department: '产品部',
    location: '杭州',
    headcount: 1,
    urgency: '紧急',
    keyRequirements: ['B端产品', '3年经验', 'SaaS']
  }
])

// 过滤后的职位列表
const filteredPositions = computed(() => {
  if (!searchKeyword.value) return positions.value
  
  const keyword = searchKeyword.value.toLowerCase()
  return positions.value.filter(pos => 
    pos.title.toLowerCase().includes(keyword) ||
    pos.department.toLowerCase().includes(keyword) ||
    pos.keyRequirements.some(req => req.toLowerCase().includes(keyword))
  )
})

// 搜索处理
const handleSearch = () => {
  // 搜索逻辑已通过computed实现
}

// 职位选择变化
const handlePositionChange = () => {
  matchResults.value = []
  matchStats.total = 0
  matchStats.matched = 0
  matchStats.avgScore = 0
}

// 开始匹配
const startMatching = async () => {
  if (!selectedPosition.value) {
    ElMessage.warning('请先选择职位')
    return
  }
  
  matching.value = true
  matchResults.value = []
  
  try {
    // 调用AI人岗匹配API
    const {data: _data} =  await jobMatcherApi.executeMatching({
      positionId: selectedPosition.value,
      algorithm: matchAlgorithm.value as unknown,
      minScore: minMatchScore.value,
      sources: candidateSources.value,
      filters: {
        activeOnly: filters.activeOnly,
        availableOnly: filters.availableOnly,
        noBlacklist: filters.noBlacklist
      }
    })
    
    // 更新匹配结果
    matchResults.value 
  
  .position-card,
  .control-card,
  .result-card {
    height: 100%;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  // 职位列表样式
  .position-item {
    padding: 15px;
    border-bottom: 1px solid #ebeef5;
    cursor: pointer;
    
    &:hover {
      background-color: #f5f7fa;
    }
    
    .position-info {
      width: 100%;
      
      .position-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
      }
      
      .position-meta {
        font-size: 13px;
        color: #909399;
        margin-bottom: 8px;
        
        span {
          margin-right: 10px;
        }
      }
    }
  }
  
  // 控制面板样式
  .control-card {
    :deep(.el-form-item) {
      margin-bottom: 20px;
    }
  }
  
  // 统计卡片样式
  .stats-card {
    .stats-content {
      text-align: center;
      
      .stat-item {
        margin-bottom: 15px;
        
        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #409eff;
        }
        
        .stat-label {
          font-size: 12px;
          color: #909399;
          margin-top: 5px;
        }
      }
    }
  }
  
  // 候选人卡片样式
  .candidate-card {
    display: flex;
    gap: 15px;
    padding: 15px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }
    
    .match-score {
      flex-shrink: 0;
    }
    
    .candidate-info {
      flex: 1;
      
      .info-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
      }
      
      .info-content {
        font-size: 13px;
        color: #606266;
        margin-bottom: 10px;
        
        p {
          margin: 3px 0;
        }
      }
      
      .match-details {
        margin-bottom: 10px;
        
        .detail-item {
          display: flex;
          align-items: center;
          margin-bottom: 5px;
          font-size: 12px;
          
          .factor {
            width: 80px;
            color: #909399;
          }
          
          :deep(.el-progress) {
            flex: 1;
            margin: 0 10px;
          }
          
          .score {
            width: 40px;
            text-align: right;
            color: #606266;
          }
        }
      }
    }
    
    .candidate-actions {
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
  }
  
  .empty-state {
    height: 600px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// 候选人详情样式
.candidate-detail {
  .match-analysis {
    margin-top: 20px;
    
    h3 {
      margin-bottom: 15px;
      color: #303133;
    }
    
    .analysis-text {
      h4 {
        margin: 15px 0 10px;
        color: #606266;
      }
      
      ul {
        list-style: none;
        padding-left: 20px;
        
        li {
          position: relative;
          margin-bottom: 8px;
          color: #909399;
          
          &:before {
            content: '•';
            position: absolute;
            left: -15px;
            color: #409eff;
          }
        }
      }
    }
  }
}
</style>