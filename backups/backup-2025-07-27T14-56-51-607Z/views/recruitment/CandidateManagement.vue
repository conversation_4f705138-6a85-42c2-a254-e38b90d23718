<template>
  <div class="candidate-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>候选人管理</h2>
      <p>全面管理候选人信息，从简历筛选到录用决策的完整流程</p>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <el-row :gutter="20">
        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.totalCount }}</div>
                <div class="stat-label">总候选人</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon new">
                <el-icon><Plus /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.newCount }}</div>
                <div class="stat-label">新候选人</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon screening">
                <el-icon><Search /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.screeningCount }}</div>
                <div class="stat-label">筛选中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon interviewing">
                <el-icon><ChatDotRound /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.interviewingCount }}</div>
                <div class="stat-label">面试中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon offer">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.offerCount }}</div>
                <div class="stat-label">已发Offer</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon hired">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.hiredCount }}</div>
                <div class="stat-label">已录用</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 操作工具栏 -->
    <el-card class="toolbar-card" shadow="never">
      <div class="toolbar-content">
        <div class="toolbar-left">
          <el-button type="primary" @click="handleCreate">
            <el-icon><Plus /></el-icon>
            新建候选人
          </el-button>
          <el-button @click="handleBatchProcess" :disabled="!selectedItems.length">
            <el-icon><Operation /></el-icon>
            批量处理
          </el-button>
          <el-button @click="handleBatchAssign" :disabled="!selectedItems.length">
            <el-icon><User /></el-icon>
            批量分配
          </el-button>
        </div>
        <div class="toolbar-right">
          <el-button @click="handleImport">
            <el-icon><Upload /></el-icon>
            批量导入
          </el-button>
          <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
          <el-button @click="handleAnalytics">
            <el-icon><TrendCharts /></el-icon>
            数据分析
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 搜索筛选 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" inline>
        <el-form-item label="职位">
          <el-select v-model="searchForm.jobPostId" placeholder="选择职位" clearable>
            <el-option
              v-for="job in jobPosts"
              :key="job.id"
              :label="job.title"
              :value="job.id"
             />
          </el-select>
        </el-form-item>
        <el-form-item label="来源">
          <el-select v-model="searchForm.source" placeholder="选择来源" clearable>
            <el-option label="网申" value="online"  />
            <el-option label="内推" value="referral"  />
            <el-option label="猎头" value="headhunt"  />
            <el-option label="校招" value="campus"  />
            <el-option label="其他" value="other"  />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
            <el-option label="新候选人" value="new"  />
            <el-option label="筛选中" value="screening"  />
            <el-option label="面试中" value="interviewing"  />
            <el-option label="已发Offer" value="offer"  />
            <el-option label="已录用" value="hired"  />
            <el-option label="已拒绝" value="rejected"  />
          </el-select>
        </el-form-item>
        <el-form-item label="阶段">
          <el-select v-model="searchForm.stage" placeholder="选择阶段" clearable>
            <el-option label="简历筛选" value="resume_screening"  />
            <el-option label="初试" value="first_interview"  />
            <el-option label="复试" value="second_interview"  />
            <el-option label="终试" value="final_interview"  />
            <el-option label="背调" value="background_check"  />
            <el-option label="Offer审批" value="offer_approval"  />
          </el-select>
        </el-form-item>
        <el-form-item label="技能">
          <el-input
            v-model="searchForm.skillKeyword"
            placeholder="输入技能关键词"
            clearable
            />
        </el-form-item>
        <el-form-item label="地区">
          <el-input
            v-model="searchForm.location"
            placeholder="输入地区"
            clearable
            />
        </el-form-item>
        <el-form-item label="收藏">
          <el-switch
            v-model="searchForm.favoriteOnly"
            active-text="仅收藏"
            inactive-text="全部"
           />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><RefreshLeft /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card shadow="never">
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="name" label="姓名" width="120" fixed="left">
          <template #default="{ row }">
            <div class="candidate-name">
              <el-avatar :src="row.avatar" :size="32">
                {{ row.name.charAt(0) }}
              </el-avatar>
              <div class="name-info">
                <div class="name">{{ row.name }}</div>
                <div class="basic-info">{{ row.gender }} | {{ row.age }}岁</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="expectedPosition" label="期望职位" width="120"  />
        <el-table-column prop="jobPostTitle" label="申请职位" width="120">
          <template #default="{ row }">
            <el-link v-if="row.jobPostTitle" type="primary" @click="handleViewJob(row.jobPostId)">
              {{ row.jobPostTitle }}
            </el-link>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="education" label="学历" width="80"  />
        <el-table-column prop="experience" label="工作年限" width="90" align="center">
          <template #default="{ row }">
            {{ row.experience }}年
          </template>
        </el-table-column>
        <el-table-column prop="expectedSalary" label="期望薪资" width="100">
          <template #default="{ row }">
            <span v-if="row.expectedSalaryMin && row.expectedSalaryMax">
              {{ row.expectedSalaryMin }}-{{ row.expectedSalaryMax }}K
            </span>
            <span v-else-if="row.expectedSalaryMin">
              {{ row.expectedSalaryMin }}K+
            </span>
            <span v-else class="text-muted">面议</span>
          </template>
        </el-table-column>
        <el-table-column prop="location" label="所在地" width="100"  />
        <el-table-column prop="source" label="来源" width="80">
          <template #default="{ row }">
            <el-tag :type="getSourceColor(row.source)" size="small">
              {{ getSourceLabel(row.source) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="90">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)" size="small">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="stage" label="阶段" width="100">
          <template #default="{ row }">
            <el-tag type="info" size="small">
              {{ getStageLabel(row.stage) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="score" label="评分" width="70" align="center">
          <template #default="{ row }">
            <el-rate
              v-if="row.score"
              :model-value="row.score / 20"
              disabled
              size="small"
              :colors="['#ff4d4f', '#faad14', '#52c41a']"
             />
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="assignedRecruiter" label="负责人" width="90"  />
        <el-table-column prop="applyTime" label="申请时间" width="110"  />
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button link type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button link type="primary" size="small" @click="handleScheduleInterview(row)">
              面试
            </el-button>
            <el-button link type="primary" size="small" @click="handleSendOffer(row)">
              Offer
            </el-button>
            <el-button
              link
              :type="row.isFavorite ? 'warning' : 'primary'"
              size="small"
              @click="handleToggleFavorite(row)"
            >
              {{ row.isFavorite ? '取消收藏' : '收藏' }}
            </el-button>
            <el-dropdown>
              <el-button link type="primary" size="small">
                更多
                <el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleCommunicate(row)">
                    记录沟通
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleScore(row)">
                    评分
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleAddTag(row)">
                    添加标签
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleMoveToTalentPool(row)">
                    移入人才库
                  </el-dropdown-item>
                  <el-dropdown-item divided @click="handleAddToBlacklist(row)">
                    <span style="color: #ff4d4f">加入黑名单</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
    </el-card>

    <!-- 候选人详情抽屉 -->
    <CandidateDetailDrawer
      v-model="detailDrawerVisible"
      :candidate-id="currentCandidateId"
      @action="handleDetailAction"
    />

    <!-- 面试安排对话框 -->
    <InterviewScheduleDialog
      v-model="interviewDialogVisible"
      :candidate="currentCandidate"
      @confirm="handleInterviewConfirm"
    />

    <!-- Offer发送对话框 -->
    <OfferSendDialog
      v-model="offerDialogVisible"
      :candidate="currentCandidate"
      @confirm="handleOfferConfirm"
    />

    <!-- 沟通记录对话框 -->
    <CommunicationDialog
      v-model="communicationDialogVisible"
      :candidate="currentCandidate"
      @confirm="handleCommunicationConfirm"
    />

    <!-- 评分对话框 -->
    <ScoreDialog
      v-model="scoreDialogVisible"
      :candidate="currentCandidate"
      @confirm="handleScoreConfirm"
    />

    <!-- 批量操作对话框 -->
    <BatchOperationDialog
      v-model="batchDialogVisible"
      :candidates="selectedItems"
      @confirm="handleBatchConfirm"
    />

    <!-- 数据分析对话框 -->
    <CandidateAnalyticsDialog
      v-model="analyticsDialogVisible"
      :search-params="searchForm"
    />
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'CandidateManagement'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  RefreshLeft,
  Plus,
  Operation,
  User,
  Upload,
  Download,
  TrendCharts,
  ChatDotRound,
  Document,
  Check,
  ArrowDown
} from '@element-plus/icons-vue'
import { candidateApi } from '@/api/recruitment'
import CandidateDetailDrawer from './components/CandidateDetailDrawer.vue'
import InterviewScheduleDialog from './components/InterviewScheduleDialog.vue'
import OfferSendDialog from './components/OfferSendDialog.vue'
import CommunicationDialog from './components/CommunicationDialog.vue'
import ScoreDialog from './components/ScoreDialog.vue'
import BatchOperationDialog from './components/BatchOperationDialog.vue'
import CandidateAnalyticsDialog from './components/CandidateAnalyticsDialog.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const selectedItems = ref([])
const detailDrawerVisible = ref(false)
const interviewDialogVisible = ref(false)
const offerDialogVisible = ref(false)
const communicationDialogVisible = ref(false)
const scoreDialogVisible = ref(false)
const batchDialogVisible = ref(false)
const analyticsDialogVisible = ref(false)
const currentCandidate = ref(null)
const currentCandidateId = ref('')

// 搜索表单
const searchForm = reactive({
  jobPostId: '',
  source: '',
  status: '',
  stage: '',
  skillKeyword: '',
  location: '',
  favoriteOnly: false,
  keyword: ''
})

// 分页参数
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

// 统计数据
const statistics = reactive({
  totalCount: 0,
  newCount: 0,
  screeningCount: 0,
  interviewingCount: 0,
  offerCount: 0,
  hiredCount: 0,
  rejectedCount: 0,
  blacklistedCount: 0,
  avgScore: 0,
  avgAiScore: 0
})

// 基础数据
const jobPosts = ref([])

// 获取列表数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      page: pagination.current,
      pageSize: pagination.pageSize
    }
    
    const {data: _data} =  await candidateApi.getList(params)
    tableData.value 

  .page-header {
    margin-bottom: 20px;
    
    h2 {
      margin: 0 0 5px 0;
      color: #303133;
    }
    
    p {
      margin: 0;
      color: #909399;
      font-size: 14px;
    }
  }

  .statistics-cards {
    margin-bottom: 20px;

    .stat-card {
      .stat-content {
        display: flex;
        align-items: center;
        padding: 15px;
        
        .stat-icon {
          font-size: 32px;
          margin-right: 15px;
          width: 60px;
          height: 60px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          
          &.total {
            background-color: #e6f7ff;
            color: #1890ff;
          }
          
          &.new {
            background-color: #f6ffed;
            color: #52c41a;
          }
          
          &.screening {
            background-color: #fff7e6;
            color: #fa8c16;
          }
          
          &.interviewing {
            background-color: #f0f5ff;
            color: #2f54eb;
          }
          
          &.offer {
            background-color: #f9f0ff;
            color: #722ed1;
          }
          
          &.hired {
            background-color: #f6ffed;
            color: #52c41a;
          }
        }
        
        .stat-info {
          .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 5px;
          }
          
          .stat-label {
            font-size: 14px;
            color: #606266;
          }
        }
      }
    }
  }

  .toolbar-card {
    margin-bottom: 20px;

    .toolbar-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .search-card {
    margin-bottom: 20px;

    :deep(.el-form) {
      .el-form-item {
        margin-bottom: 15px;
      }
    }
  }

  .candidate-name {
    display: flex;
    align-items: center;
    gap: 10px;
    
    .name-info {
      .name {
        font-weight: 500;
        color: #303133;
      }
      
      .basic-info {
        font-size: 12px;
        color: #909399;
      }
    }
  }

  .text-muted {
    color: #909399;
  }

  :deep(.el-pagination) {
    margin-top: 20px;
    justify-content: flex-end;
  }
}
</style>