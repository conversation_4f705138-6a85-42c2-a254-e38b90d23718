<template>
  <div class="offer-approval-process">
    <!-- 待审批列表 -->
    <el-card class="pending-card">
      <template #header>
        <div class="card-header">
          <span>待审批录用申请</span>
          <el-tag type="danger">{{ pendingOffers.length }}</el-tag>
        </div>
      </template>

      <el-table 
        v-loading="loading"
        :data="pendingOffers" 
        stripe
      >
        <el-table-column label="候选人信息" min-width="200">
          <template #default="{ row }">
            <div class="candidate-info">
              <div class="main-info">
                <strong>{{ row.candidateName }}</strong>
                <el-tag size="small" style="margin-left: 10px">
                  {{ row.finalRound }}
                </el-tag>
              </div>
              <div class="sub-info">
                {{ row.position }} | {{ row.department }}
              </div>
              <div class="extra-info">
                {{ row.education }} | {{ row.experience }}年经验
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="面试评价" width="180">
          <template #default="{ row }">
            <div class="interview-summary">
              <div class="score-item">
                <span>综合评分：</span>
                <strong :class="getScoreClass(row.totalScore)">
                  {{ row.totalScore }}分
                </strong>
              </div>
              <div class="recommendation">
                <el-tag :type="getRecommendationType(row.recommendation)">
                  {{ row.recommendation }}
                </el-tag>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="薪资谈判" width="200">
          <template #default="{ row }">
            <div class="salary-info">
              <div class="salary-item">
                <span>期望：</span>
                {{ row.expectedSalary }}k
              </div>
              <div class="salary-item">
                <span>建议：</span>
                {{ row.recommendedSalary }}k
              </div>
              <div class="salary-item">
                <span>定薪：</span>
                <strong class="final-salary">{{ row.finalSalary }}k</strong>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="submittedBy" label="提交人" width="100"  />
        <el-table-column prop="submittedTime" label="提交时间" width="150">
          <template #default="{ row }">
            {{ formatDateTime(row.submittedTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleApproval(row)">
              审批
            </el-button>
            <el-button link type="primary" @click="viewDetail(row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 审批对话框 -->
    <el-dialog 
      v-model="approvalDialogVisible" 
      :title="`录用审批 - ${currentOffer?.candidateName}`" 
      width="900px"
      top="5vh"
    >
      <el-form 
        ref="approvalFormRef" 
        :model="approvalForm" 
        :rules="approvalRules" 
        label-width="120px"
      >
        <!-- 候选人信息 -->
        <el-card class="info-card">
          <template #header>
            <span>候选人信息</span>
          </template>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="姓名">{{ currentOffer?.candidateName }}</el-descriptions-item>
            <el-descriptions-item label="应聘职位">{{ currentOffer?.position }}</el-descriptions-item>
            <el-descriptions-item label="所属部门">{{ currentOffer?.department }}</el-descriptions-item>
            <el-descriptions-item label="学历">{{ currentOffer?.education }}</el-descriptions-item>
            <el-descriptions-item label="工作经验">{{ currentOffer?.experience }}年</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{ currentOffer?.phone }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 面试评价汇总 -->
        <el-card class="evaluation-card">
          <template #header>
            <span>面试评价汇总</span>
          </template>
          <el-table :data="currentOffer?.interviewRecords" size="small">
            <el-table-column prop="round" label="面试轮次" width="100"  />
            <el-table-column prop="interviewer" label="面试官" width="100"  />
            <el-table-column prop="score" label="评分" width="80" align="center">
              <template #default="{ row }">
                <span :class="getScoreClass(row.score)">{{ row.score }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="recommendation" label="建议" width="100">
              <template #default="{ row }">
                <el-tag :type="getRecommendationType(row.recommendation)" size="small">
                  {{ row.recommendation }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="comments" label="评价要点"  />
          </el-table>
          
          <div class="summary-info">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="summary-item">
                  <span>平均得分：</span>
                  <strong>{{ currentOffer?.averageScore }}分</strong>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="summary-item">
                  <span>综合建议：</span>
                  <el-tag :type="getRecommendationType(currentOffer?.recommendation)">
                    {{ currentOffer?.recommendation }}
                  </el-tag>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="summary-item">
                  <span>面试通过率：</span>
                  <strong>{{ currentOffer?.passRate }}%</strong>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 薪资福利方案 -->
        <el-card class="salary-card">
          <template #header>
            <span>薪资福利方案</span>
          </template>
          
          <el-form-item label="基本工资" prop="baseSalary">
            <el-input-number 
              v-model="approvalForm.baseSalary" 
              :min="0" 
              :step="1000"
              style="width: 200px"
              />
            <span style="margin-left: 10px">元/月</span>
          </el-form-item>
          
          <el-form-item label="绩效工资">
            <el-input-number 
              v-model="approvalForm.performanceSalary" 
              :min="0" 
              :step="500"
              style="width: 200px"
              />
            <span style="margin-left: 10px">元/月</span>
          </el-form-item>
          
          <el-form-item label="岗位津贴">
            <el-input-number 
              v-model="approvalForm.positionAllowance" 
              :min="0" 
              :step="100"
              style="width: 200px"
              />
            <span style="margin-left: 10px">元/月</span>
          </el-form-item>
          
          <el-form-item label="其他福利">
            <el-checkbox-group v-model="approvalForm.benefits">
              <el-checkbox label="餐补">餐补</el-checkbox>
              <el-checkbox label="交通补贴">交通补贴</el-checkbox>
              <el-checkbox label="通讯补贴">通讯补贴</el-checkbox>
              <el-checkbox label="住房补贴">住房补贴</el-checkbox>
              <el-checkbox label="年终奖">年终奖</el-checkbox>
              <el-checkbox label="项目奖金">项目奖金</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          
          <el-form-item label="试用期">
            <el-radio-group v-model="approvalForm.probationPeriod">
              <el-radio :label="1">1个月</el-radio>
              <el-radio :label="3">3个月</el-radio>
              <el-radio :label="6">6个月</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="试用期工资">
            <el-input-number 
              v-model="approvalForm.probationDiscount" 
              :min="70" 
              :max="100"
              style="width: 120px"
              />
            <span style="margin-left: 10px">%</span>
          </el-form-item>
        </el-card>

        <!-- 入职安排 -->
        <el-card class="onboarding-card">
          <template #header>
            <span>入职安排</span>
          </template>
          
          <el-form-item label="预计入职日期" prop="expectedJoinDate">
            <el-date-picker
              v-model="approvalForm.expectedJoinDate"
              type="date"
              placeholder="选择日期"
              :disabled-date="disabledDate"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
             />
          </el-form-item>
          
          <el-form-item label="工作地点" prop="workLocation">
            <el-select v-model="approvalForm.workLocation" placeholder="请选择">
              <el-option label="总部" value="总部"  />
              <el-option label="分公司A" value="分公司A"  />
              <el-option label="分公司B" value="分公司B"  />
              <el-option label="远程办公" value="远程办公"  />
            </el-select>
          </el-form-item>
          
          <el-form-item label="直属上级" prop="directSupervisor">
            <el-input v-model="approvalForm.directSupervisor" placeholder="请输入直属上级姓名"   />
          </el-form-item>
          
          <el-form-item label="入职材料">
            <el-checkbox-group v-model="approvalForm.requiredDocuments">
              <el-checkbox label="身份证复印件">身份证复印件</el-checkbox>
              <el-checkbox label="学历证明">学历证明</el-checkbox>
              <el-checkbox label="离职证明">离职证明</el-checkbox>
              <el-checkbox label="体检报告">体检报告</el-checkbox>
              <el-checkbox label="银行卡信息">银行卡信息</el-checkbox>
              <el-checkbox label="照片">照片</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-card>

        <!-- 审批意见 -->
        <el-form-item label="审批决定" prop="decision">
          <el-radio-group v-model="approvalForm.decision">
            <el-radio label="approve">同意录用</el-radio>
            <el-radio label="reject">不予录用</el-radio>
            <el-radio label="pending">暂缓决定</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="审批意见" prop="comments">
          <el-input 
            v-model="approvalForm.comments" 
            type="textarea" 
            :rows="3"
            placeholder="请输入审批意见"
            />
        </el-form-item>
        
        <el-form-item label="特殊说明" v-if="approvalForm.decision === 'approve'">
          <el-input 
            v-model="approvalForm.specialNotes" 
            type="textarea" 
            :rows="2"
            placeholder="如有特殊入职要求或说明，请在此填写"
            />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="approvalDialogVisible = false">取消</el-button>
        <el-button 
          v-if="approvalForm.decision === 'approve'"
          type="primary" 
          @click="handleGenerateOffer"
        >
          生成Offer
        </el-button>
        <el-button type="primary" @click="handleSubmitApproval">
          提交审批
        </el-button>
      </template>
    </el-dialog>

    <!-- Offer预览对话框 -->
    <el-dialog 
      v-model="offerPreviewVisible" 
      title="Offer预览" 
      width="800px"
      top="5vh"
    >
      <div class="offer-preview">
        <div class="offer-header">
          <h2>录用通知书</h2>
          <p class="offer-no">编号：{{ generateOfferNo() }}</p>
        </div>
        
        <div class="offer-content">
          <p>尊敬的 {{ currentOffer?.candidateName }} 先生/女士：</p>
          
          <p>
            经过我公司的严格评审，您已通过我司 <strong>{{ currentOffer?.position }}</strong> 职位的面试，
            现正式向您发出工作邀请，诚邀您加入我们的团队。
          </p>
          
          <h4>一、职位信息</h4>
          <ul>
            <li>职位名称：{{ currentOffer?.position }}</li>
            <li>所属部门：{{ currentOffer?.department }}</li>
            <li>工作地点：{{ approvalForm.workLocation }}</li>
            <li>直属上级：{{ approvalForm.directSupervisor }}</li>
          </ul>
          
          <h4>二、薪资待遇</h4>
          <ul>
            <li>基本工资：{{ approvalForm.baseSalary }} 元/月</li>
            <li>绩效工资：{{ approvalForm.performanceSalary }} 元/月</li>
            <li>岗位津贴：{{ approvalForm.positionAllowance }} 元/月</li>
            <li>
              综合月薪：
              <strong>
                {{ approvalForm.baseSalary + approvalForm.performanceSalary + approvalForm.positionAllowance }} 元
              </strong>
            </li>
            <li>其他福利：{{ approvalForm.benefits.join('、') }}</li>
          </ul>
          
          <h4>三、试用期条款</h4>
          <ul>
            <li>试用期：{{ approvalForm.probationPeriod }}个月</li>
            <li>试用期工资：正式工资的{{ approvalForm.probationDiscount }}%</li>
          </ul>
          
          <h4>四、入职安排</h4>
          <ul>
            <li>报到日期：{{ approvalForm.expectedJoinDate }}</li>
            <li>报到地点：{{ approvalForm.workLocation }}人力资源部</li>
            <li>需携带材料：{{ approvalForm.requiredDocuments.join('、') }}</li>
          </ul>
          
          <p>
            请您在收到本通知书后3个工作日内予以确认。如有任何疑问，请随时与我们联系。
          </p>
          
          <p>期待您的加入！</p>
          
          <div class="offer-footer">
            <div class="signature">
              <p>杭州科技职业技术学院</p>
              <p>人力资源部</p>
              <p>{{ formatDate(new Date()) }}</p>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="offerPreviewVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleSendOffer">
          发送Offer
        </el-button>
      </template>
    </el-dialog>

    <!-- 已审批列表 -->
    <el-card class="approved-card">
      <template #header>
        <div class="card-header">
          <span>审批记录</span>
          <el-button link type="primary" @click="viewAllRecords">
            查看全部
            <el-icon><arrow-right /></el-icon>
          </el-button>
        </div>
      </template>

      <el-table :data="approvedRecords" stripe>
        <el-table-column prop="candidateName" label="候选人" width="120"  />
        <el-table-column prop="position" label="职位" width="150"  />
        <el-table-column prop="decision" label="审批结果" width="100">
          <template #default="{ row }">
            <el-tag :type="getDecisionType(row.decision)">
              {{ getDecisionText(row.decision) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="finalSalary" label="定薪" width="100">
          <template #default="{ row }">
            <span v-if="row.decision === 'approve'">{{ row.finalSalary }}k</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="approver" label="审批人" width="100"  />
        <el-table-column prop="approvalTime" label="审批时间" width="150">
          <template #default="{ row }">
            {{ formatDateTime(row.approvalTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="offerStatus" label="Offer状态" width="120">
          <template #default="{ row }">
            <el-tag v-if="row.decision === 'approve'" :type="getOfferStatusType(row.offerStatus)">
              {{ row.offerStatus }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="100">
          <template #default="{ row }">
            <el-button link type="primary" @click="viewApprovalDetail(row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowRight } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import type { FormInstance, FormRules } from 'element-plus'
import { offerApi } from '@/api/recruitment'

// 路由
const router = useRouter()

// 数据状态
const loading = ref(false)
const pendingOffers = ref<any[]>([])
const approvedRecords = ref<any[]>([])

// 对话框
const approvalDialogVisible = ref(false)
const offerPreviewVisible = ref(false)
const currentOffer = ref<unknown>(null)

// 审批表单
const approvalFormRef = ref<FormInstance>()
const approvalForm = reactive({
  decision: 'approve',
  comments: '',
  specialNotes: '',
  baseSalary: 15000,
  performanceSalary: 3000,
  positionAllowance: 2000,
  benefits: ['餐补', '交通补贴', '年终奖'],
  probationPeriod: 3,
  probationDiscount: 80,
  expectedJoinDate: '',
  workLocation: '总部',
  directSupervisor: '',
  requiredDocuments: ['身份证复印件', '学历证明', '离职证明', '体检报告']
})

// 表单验证规则
const approvalRules = reactive<FormRules>({
  decision: [
    { required: true, message: '请选择审批决定', trigger: 'change' }
  ],
  comments: [
    { required: true, message: '请输入审批意见', trigger: 'blur' }
  ],
  baseSalary: [
    { required: true, message: '请输入基本工资', trigger: 'blur' }
  ],
  expectedJoinDate: [
    { required: true, message: '请选择预计入职日期', trigger: 'change' }
  ],
  workLocation: [
    { required: true, message: '请选择工作地点', trigger: 'change' }
  ],
  directSupervisor: [
    { required: true, message: '请输入直属上级', trigger: 'blur' }
  ]
})

// 获取分数样式
const getScoreClass = (score: number) => {
  if (score >= 80) return 'score-high'
  if (score >= 60) return 'score-medium'
  return 'score-low'
}

// 获取推荐类型
const getRecommendationType = (recommendation: string) => {
  const map: Record<string, string> = {
    '强烈推荐': 'success',
    '推荐': 'primary',
    '勉强通过': 'warning',
    '不推荐': 'danger'
  }
  return map[recommendation] || 'info'
}

// 获取决定类型
const getDecisionType = (decision: string) => {
  const map: Record<string, string> = {
    'approve': 'success',
    'reject': 'danger',
    'pending': 'warning'
  }
  return map[decision] || 'info'
}

// 获取决定文本
const getDecisionText = (decision: string) => {
  const map: Record<string, string> = {
    'approve': '同意录用',
    'reject': '不予录用',
    'pending': '暂缓决定'
  }
  return map[decision] || decision
}

// 获取Offer状态类型
const getOfferStatusType = (status: string) => {
  const map: Record<string, string> = {
    '已发送': 'primary',
    '已接受': 'success',
    '已拒绝': 'danger',
    '已过期': 'info'
  }
  return map[status] || ''
}

// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return `${date.getMonth() + 1}-${date.getDate()} ${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}`
}

// 格式化日期
const formatDate = (date: Date) => {
  return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`
}

// 生成Offer编号
const generateOfferNo = () => {
  const date = new Date()
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  return `OFFER-${year}${month}-${random}`
}

// 禁用日期
const disabledDate = (date: Date) => {
  // 不能选择过去的日期
  return date.getTime() < Date.now() - 24 * 60 * 60 * 1000
}

// 获取待审批列表
const fetchPendingOffers = async () => {
  loading.value = true
  try {
    // 模拟数据
    pendingOffers.value = [
      {
        id: '1',
        candidateName: '张三',
        position: '前端工程师',
        department: '技术部',
        education: '本科',
        experience: '5',
        phone: '13800138001',
        finalRound: '终试',
        totalScore: 85,
        averageScore: 85,
        passRate: 100,
        recommendation: '强烈推荐',
        expectedSalary: '18-22',
        recommendedSalary: '18-20',
        finalSalary: 20,
        submittedBy: '技术总监',
        submittedTime: '2025-01-21 10:00:00',
        interviewRecords: [
          {
            round: '初试',
            interviewer: '张经理',
            score: 82,
            recommendation: '推荐',
            comments: '技术基础扎实，沟通能力良好'
          },
          {
            round: '复试',
            interviewer: '李总监',
            score: 88,
            recommendation: '强烈推荐',
            comments: '项目经验丰富，团队协作能力强'
          }
        ]
      }
    ]
  } catch (__error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 获取审批记录
const fetchApprovedRecords = async () => {
  try {
    // 模拟数据
    approvedRecords.value = [
      {
        id: '1',
        candidateName: '李四',
        position: 'Java工程师',
        decision: 'approve',
        finalSalary: 25,
        approver: 'HR总监',
        approvalTime: '2025-01-20 15:00:00',
        offerStatus: '已发送'
      },
      {
        id: '2',
        candidateName: '王五',
        position: '产品经理',
        decision: 'reject',
        approver: '副总裁',
        approvalTime: '2025-01-19 14:00:00'
      }
    ]
  } catch (__error) {
    console.error('获取审批记录失败:', error)
  }
}

// 开始审批
   
const handleApproval = (offer: unknown) => {
  currentOffer.value = offer
  
  // 初始化表单数据
  approvalForm.decision = 'approve'
  approvalForm.comments = ''
  approvalForm.specialNotes = ''
  approvalForm.baseSalary = offer.finalSalary * 1000 * 0.7
  approvalForm.performanceSalary = offer.finalSalary * 1000 * 0.2
  approvalForm.positionAllowance = offer.finalSalary * 1000 * 0.1
  approvalForm.expectedJoinDate = ''
  approvalForm.directSupervisor = ''
  
  approvalDialogVisible.value = true
}

// 查看详情
   
const viewDetail = (offer: unknown) => {
  // 跳转到Offer详情页
  router.push({
    name: 'OfferDetail',
    params: { id: offer.id },
    query: { candidateName: offer.candidateName }
  })
}

// 生成Offer
const handleGenerateOffer = () => {
  offerPreviewVisible.value = true
}

// 提交审批
const handleSubmitApproval = async () => {
  if (!approvalFormRef.value) return
  
  await approvalFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await ElMessageBox.confirm(
          `确定${getDecisionText(approvalForm.decision)}吗？`,
          '确认提交',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        // 调用API提交审批
        await offerApi.submitOfferApproval(currentOffer.value.id, {
          decision: approvalForm.decision,
          remark: approvalForm.remark,
          urgencyLevel: approvalForm.urgencyLevel,
          nextApproverId: approvalForm.nextApprover
        })
        
        ElMessage.success('审批提交成功')
        approvalDialogVisible.value = false
        
        // 刷新列表
        fetchPendingOffers()
        fetchApprovedRecords()
      } catch (__error) {
        if (error !== 'cancel') {
          console.error('提交失败:', error)
          ElMessage.error('提交失败')
        }
      }
    }
  })
}

// 发送Offer
const handleSendOffer = async () => {
  try {
    await ElMessageBox.confirm(
      '确定向候选人发送Offer吗？',
      '确认发送',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    // 调用API发送Offer
    await offerApi.sendOffer(currentOffer.value.id, {
      sendMethod: 'email',
      customMessage: approvalForm.remark
    })
    
    ElMessage.success('Offer已发送')
    offerPreviewVisible.value = false
    approvalDialogVisible.value = false
    
    // 刷新列表
    fetchPendingOffers()
    fetchApprovedRecords()
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('发送失败:', error)
      ElMessage.error('发送失败')
    }
  }
}

// 查看全部记录
const viewAllRecords = () => {
  // 跳转到Offer审批记录页面
  router.push({
    name: 'OfferApprovalHistory',
    query: { status: 'all' }
  })
}

// 查看审批详情
   
const viewApprovalDetail = (record: unknown) => {
  // 跳转到审批详情页
  router.push({
    name: 'OfferDetail',
    params: { id: record.offerId },
    query: { 
      candidateName: record.candidateName,
      tab: 'approval' // 直接显示审批历史标签页
    }
  })
}

// 初始化
onMounted(() => {
  fetchPendingOffers()
  fetchApprovedRecords()
})
</script>

<style lang="scss" scoped>
.offer-approval-process {
  padding: 20px;
  
  .pending-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      align-items: center;
      gap: 10px;
    }
  }
  
  .candidate-info {
    .main-info {
      margin-bottom: 4px;
    }
    
    .sub-info,
    .extra-info {
      font-size: 12px;
      color: #909399;
      margin-top: 2px;
    }
  }
  
  .interview-summary {
    .score-item {
      margin-bottom: 8px;
      
      .score-high {
        color: #67c23a;
      }
      
      .score-medium {
        color: #e6a23c;
      }
      
      .score-low {
        color: #f56c6c;
      }
    }
  }
  
  .salary-info {
    .salary-item {
      font-size: 13px;
      margin-bottom: 4px;
      
      .final-salary {
        color: #409eff;
      }
    }
  }
  
  .approved-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}

// 审批对话框样式
.info-card,
.evaluation-card,
.salary-card,
.onboarding-card {
  margin-bottom: 20px;
}

.evaluation-card {
  .summary-info {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #ebeef5;
    
    .summary-item {
      text-align: center;
      
      strong {
        color: #303133;
        font-size: 16px;
      }
    }
  }
}

// Offer预览样式
.offer-preview {
  .offer-header {
    text-align: center;
    margin-bottom: 30px;
    
    h2 {
      margin: 0 0 10px;
      color: #303133;
    }
    
    .offer-no {
      color: #909399;
      font-size: 14px;
    }
  }
  
  .offer-content {
    line-height: 1.8;
    color: #606266;
    
    p {
      margin: 15px 0;
    }
    
    h4 {
      margin: 20px 0 10px;
      color: #303133;
    }
    
    ul {
      list-style: none;
      padding-left: 20px;
      
      li {
        position: relative;
        margin-bottom: 8px;
        
        &:before {
          content: '•';
          position: absolute;
          left: -15px;
          color: #409eff;
        }
      }
    }
    
    strong {
      color: #303133;
    }
  }
  
  .offer-footer {
    margin-top: 40px;
    
    .signature {
      text-align: right;
      
      p {
        margin: 5px 0;
      }
    }
  }
}
</style>