<template>
  <div class="interview-evaluation-process">
    <!-- 待评价列表 -->
    <el-card class="pending-card">
      <template #header>
        <div class="card-header">
          <span>待评价面试</span>
          <el-tag type="danger">{{ pendingEvaluations.length }}</el-tag>
        </div>
      </template>

      <el-alert 
        v-if="overdueCount > 0"
        :title="`您有 ${overdueCount} 场面试评价已超时，请尽快完成`" 
        type="warning" 
        show-icon
        :closable="false"
        style="margin-bottom: 20px"
      />

      <el-table 
        v-loading="loading"
        :data="pendingEvaluations" 
        stripe
      >
        <el-table-column label="候选人" width="150">
          <template #default="{ row }">
            <div class="candidate-info">
              <div class="name">{{ row.candidateName }}</div>
              <div class="position">{{ row.position }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="interviewTime" label="面试时间" width="150">
          <template #default="{ row }">
            {{ formatDateTime(row.interviewTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="round" label="面试轮次" width="100" align="center">
          <template #default="{ row }">
            <el-tag>{{ row.round }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="面试官" width="180">
          <template #default="{ row }">
            <div class="interviewer-list">
              <el-tag 
                v-for="interviewer in row.interviewers" 
                :key="interviewer.id"
                size="small"
                :type="interviewer.evaluated ? 'success' : ''"
              >
                {{ interviewer.name }}
                <el-icon v-if="interviewer.evaluated"><check /></el-icon>
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="评价进度" width="120" align="center">
          <template #default="{ row }">
            {{ row.evaluatedCount }}/{{ row.totalCount }}
          </template>
        </el-table-column>
        <el-table-column label="剩余时间" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="getTimeType(row.remainingHours)">
              {{ formatRemainingTime(row.remainingHours) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="100">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleEvaluate(row)">
              评价
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 评价表单对话框 -->
    <el-dialog 
      v-model="evaluationDialogVisible" 
      :title="`面试评价 - ${currentInterview?.candidateName}`" 
      width="900px"
      top="5vh"
    >
      <el-form 
        ref="evaluationFormRef" 
        :model="evaluationForm" 
        :rules="evaluationRules" 
        label-width="120px"
      >
        <!-- 基本信息 -->
        <el-card class="info-card">
          <template #header>
            <span>面试信息</span>
          </template>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="候选人">{{ currentInterview?.candidateName }}</el-descriptions-item>
            <el-descriptions-item label="应聘职位">{{ currentInterview?.position }}</el-descriptions-item>
            <el-descriptions-item label="面试轮次">{{ currentInterview?.round }}</el-descriptions-item>
            <el-descriptions-item label="面试时间">{{ formatDateTime(currentInterview?.interviewTime) }}</el-descriptions-item>
            <el-descriptions-item label="面试时长">{{ currentInterview?.duration }}分钟</el-descriptions-item>
            <el-descriptions-item label="面试形式">{{ currentInterview?.type }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 评分项 -->
        <el-card class="score-card">
          <template #header>
            <span>评分项</span>
            <span class="total-score">
              总分：<strong>{{ totalScore }}</strong>/100
            </span>
          </template>
          
          <el-row :gutter="20">
            <el-col 
              v-for="item in scoreItems" 
              :key="item.key"
              :span="12"
            >
              <el-form-item 
                :label="item.label" 
                :prop="`scores.${item.key}`"
                class="score-item"
              >
                <div class="score-content">
                  <el-slider 
                    v-model="evaluationForm.scores[item.key]" 
                    :max="item.maxScore"
                    :marks="getScoreMarks(item.maxScore)"
                    show-input
                   />
                  <div class="score-desc">{{ item.description }}</div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 能力评估 -->
        <el-card class="ability-card">
          <template #header>
            <span>能力评估</span>
          </template>
          
          <el-form-item label="专业技能" prop="abilities.professional">
            <el-rate 
              v-model="evaluationForm.abilities.professional" 
              :texts="['较差', '一般', '良好', '优秀', '卓越']"
              show-text
             />
          </el-form-item>
          
          <el-form-item label="沟通表达" prop="abilities.communication">
            <el-rate 
              v-model="evaluationForm.abilities.communication" 
              :texts="['较差', '一般', '良好', '优秀', '卓越']"
              show-text
             />
          </el-form-item>
          
          <el-form-item label="逻辑思维" prop="abilities.logical">
            <el-rate 
              v-model="evaluationForm.abilities.logical" 
              :texts="['较差', '一般', '良好', '优秀', '卓越']"
              show-text
             />
          </el-form-item>
          
          <el-form-item label="团队协作" prop="abilities.teamwork">
            <el-rate 
              v-model="evaluationForm.abilities.teamwork" 
              :texts="['较差', '一般', '良好', '优秀', '卓越']"
              show-text
             />
          </el-form-item>
          
          <el-form-item label="学习能力" prop="abilities.learning">
            <el-rate 
              v-model="evaluationForm.abilities.learning" 
              :texts="['较差', '一般', '良好', '优秀', '卓越']"
              show-text
             />
          </el-form-item>
        </el-card>

        <!-- 详细评价 -->
        <el-card class="detail-card">
          <template #header>
            <span>详细评价</span>
          </template>
          
          <el-form-item label="面试表现" prop="performance">
            <el-input 
              v-model="evaluationForm.performance" 
              type="textarea" 
              :rows="3"
              placeholder="请描述候选人在面试中的整体表现"
              />
          </el-form-item>
          
          <el-form-item label="优势亮点" prop="advantages">
            <el-input 
              v-model="evaluationForm.advantages" 
              type="textarea" 
              :rows="3"
              placeholder="请列举候选人的优势和亮点"
              />
          </el-form-item>
          
          <el-form-item label="不足之处" prop="disadvantages">
            <el-input 
              v-model="evaluationForm.disadvantages" 
              type="textarea" 
              :rows="3"
              placeholder="请指出候选人的不足和需要改进的地方"
              />
          </el-form-item>
          
          <el-form-item label="补充说明">
            <el-input 
              v-model="evaluationForm.remarks" 
              type="textarea" 
              :rows="2"
              placeholder="其他需要说明的情况"
              />
          </el-form-item>
        </el-card>

        <!-- 综合建议 -->
        <el-card>
          <template #header>
            <span>综合建议</span>
          </template>
          
          <el-form-item label="录用建议" prop="recommendation">
            <el-radio-group v-model="evaluationForm.recommendation">
              <el-radio-button label="强烈推荐">强烈推荐</el-radio-button>
              <el-radio-button label="推荐">推荐</el-radio-button>
              <el-radio-button label="勉强通过">勉强通过</el-radio-button>
              <el-radio-button label="不推荐">不推荐</el-radio-button>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="薪资建议" v-if="evaluationForm.recommendation !== '不推荐'">
            <el-input-number 
              v-model="evaluationForm.salaryMin" 
              :min="0" 
              :step="1000"
              style="width: 120px"
              />
            <span style="margin: 0 10px">-</span>
            <el-input-number 
              v-model="evaluationForm.salaryMax" 
              :min="0" 
              :step="1000"
              style="width: 120px"
              />
            <span style="margin-left: 10px">元/月</span>
          </el-form-item>
          
          <el-form-item label="后续安排" v-if="evaluationForm.recommendation !== '不推荐'">
            <el-checkbox-group v-model="evaluationForm.nextSteps">
              <el-checkbox label="安排下一轮面试">安排下一轮面试</el-checkbox>
              <el-checkbox label="需要技术测试">需要技术测试</el-checkbox>
              <el-checkbox label="背景调查">背景调查</el-checkbox>
              <el-checkbox label="直接发送Offer">直接发送Offer</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-card>
      </el-form>
      
      <template #footer>
        <el-button @click="handleSaveDraft">保存草稿</el-button>
        <el-button @click="evaluationDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmitEvaluation">提交评价</el-button>
      </template>
    </el-dialog>

    <!-- 历史评价记录 -->
    <el-card class="history-card">
      <template #header>
        <div class="card-header">
          <span>历史评价记录</span>
          <el-button link type="primary" @click="viewAllHistory">
            查看全部
            <el-icon><arrow-right /></el-icon>
          </el-button>
        </div>
      </template>

      <el-table :data="historyList" stripe>
        <el-table-column label="候选人" width="150">
          <template #default="{ row }">
            <div class="candidate-info">
              <div class="name">{{ row.candidateName }}</div>
              <div class="position">{{ row.position }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="interviewTime" label="面试时间" width="150">
          <template #default="{ row }">
            {{ formatDateTime(row.interviewTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="round" label="轮次" width="80" align="center"  />
        <el-table-column prop="totalScore" label="总分" width="80" align="center">
          <template #default="{ row }">
            <span :class="getScoreClass(row.totalScore)">{{ row.totalScore }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="recommendation" label="建议" width="100">
          <template #default="{ row }">
            <el-tag :type="getRecommendationType(row.recommendation)">
              {{ row.recommendation }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="evaluator" label="评价人" width="100"  />
        <el-table-column prop="evaluateTime" label="评价时间" width="150">
          <template #default="{ row }">
            {{ formatDateTime(row.evaluateTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="120">
          <template #default="{ row }">
            <el-button link type="primary" @click="viewDetail(row)">查看</el-button>
            <el-button link type="primary" @click="exportPDF(row)">导出</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'InterviewEvaluationProcess'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Check, ArrowRight } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { interviewApi } from '@/api/recruitment'
import { useRouter } from 'vue-router'

// 数据状态
const loading = ref(false)
const pendingEvaluations = ref<any[]>([])
const historyList = ref<any[]>([])
const overdueCount = ref(3)

// 对话框
const evaluationDialogVisible = ref(false)
const currentInterview = ref<unknown>(null)

// 评价表单
const evaluationFormRef = ref<FormInstance>()
const evaluationForm = reactive({
  scores: {
    professional: 0,
    experience: 0,
    potential: 0,
    matching: 0
  },
  abilities: {
    professional: 3,
    communication: 3,
    logical: 3,
    teamwork: 3,
    learning: 3
  },
  performance: '',
  advantages: '',
  disadvantages: '',
  remarks: '',
  recommendation: '推荐',
  salaryMin: 15000,
  salaryMax: 20000,
  nextSteps: [] as string[]
})

// 评分项配置
const scoreItems = [
  {
    key: 'professional',
    label: '专业能力',
    maxScore: 30,
    description: '技术水平、专业知识、实践经验'
  },
  {
    key: 'experience',
    label: '项目经验',
    maxScore: 25,
    description: '项目复杂度、解决方案、成果展示'
  },
  {
    key: 'potential',
    label: '发展潜力',
    maxScore: 25,
    description: '学习能力、成长空间、职业规划'
  },
  {
    key: 'matching',
    label: '岗位匹配',
    maxScore: 20,
    description: '技能匹配度、文化适应、稳定性'
  }
]

// 表单验证规则
const evaluationRules = reactive<FormRules>({
  'scores.professional': [
    { required: true, message: '请评分', trigger: 'change' }
  ],
  'scores.experience': [
    { required: true, message: '请评分', trigger: 'change' }
  ],
  'scores.potential': [
    { required: true, message: '请评分', trigger: 'change' }
  ],
  'scores.matching': [
    { required: true, message: '请评分', trigger: 'change' }
  ],
  performance: [
    { required: true, message: '请填写面试表现', trigger: 'blur' }
  ],
  advantages: [
    { required: true, message: '请填写优势亮点', trigger: 'blur' }
  ],
  disadvantages: [
    { required: true, message: '请填写不足之处', trigger: 'blur' }
  ],
  recommendation: [
    { required: true, message: '请选择录用建议', trigger: 'change' }
  ]
})

// 计算总分
const totalScore = computed(() => {
  return Object.values(evaluationForm.scores).reduce((sum, score) => sum + score, 0)
})

// 获取分数标记
const getScoreMarks = (maxScore: number) => {
  const marks: Record<number, string> = {
    0: '0'
  }
  marks[maxScore] = String(maxScore)
  marks[Math.floor(maxScore / 2)] = String(Math.floor(maxScore / 2))
  return marks
}

// 获取时间类型
const getTimeType = (hours: number) => {
  if (hours < 0) return 'danger'
  if (hours < 24) return 'warning'
  return 'success'
}

// 获取推荐类型
const getRecommendationType = (recommendation: string) => {
  const map: Record<string, string> = {
    '强烈推荐': 'success',
    '推荐': 'primary',
    '勉强通过': 'warning',
    '不推荐': 'danger'
  }
  return map[recommendation] || 'info'
}

// 获取分数样式
const getScoreClass = (score: number) => {
  if (score >= 80) return 'score-high'
  if (score >= 60) return 'score-medium'
  return 'score-low'
}

// 格式化日期时间
const formatDateTime = (dateStr?: string) => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return `${date.getMonth() + 1}-${date.getDate()} ${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}`
}

// 格式化剩余时间
const formatRemainingTime = (hours: number) => {
  if (hours < 0) return '已超时'
  if (hours < 24) return `${hours}小时`
  return `${Math.floor(hours / 24)}天`
}

// 获取待评价列表
const fetchPendingEvaluations = async () => {
  loading.value = true
  try {
    const {data: _data} =  await interviewApi.getPendingEvaluations({
      evaluatorId: 'current', // 当前用户
      status: 'pending'
    })
    
    pendingEvaluations.value 
  
  .pending-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      align-items: center;
      gap: 10px;
    }
  }
  
  .candidate-info {
    .name {
      font-weight: 500;
      margin-bottom: 4px;
    }
    
    .position {
      font-size: 12px;
      color: #909399;
    }
  }
  
  .interviewer-list {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
  }
  
  .history-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .score-high {
    color: #67c23a;
    font-weight: 500;
  }
  
  .score-medium {
    color: #e6a23c;
  }
  
  .score-low {
    color: #f56c6c;
  }
}

// 评价表单样式
.info-card,
.score-card,
.ability-card,
.detail-card {
  margin-bottom: 20px;
}

.score-card {
  :deep(.el-card__header) {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .total-score {
      font-size: 16px;
      
      strong {
        color: #409eff;
        font-size: 20px;
      }
    }
  }
  
  .score-item {
    margin-bottom: 30px;
    
    .score-content {
      width: 100%;
      
      .score-desc {
        font-size: 12px;
        color: #909399;
        margin-top: 5px;
      }
    }
  }
}

.ability-card {
  :deep(.el-rate) {
    height: 32px;
    
    .el-rate__text {
      margin-left: 10px;
    }
  }
}
</style>