<template>
  <div class="interview-scheduling-process">
    <!-- 顶部统计 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="6">
        <el-card>
          <el-statistic title="待安排面试" :value="stats.pending"  />
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="6">
        <el-card>
          <el-statistic title="今日面试" :value="stats.todayInterview"  />
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="6">
        <el-card>
          <el-statistic title="本周面试" :value="stats.weekInterview"  />
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="6">
        <el-card>
          <el-statistic title="面试官忙碌率" :value="stats.interviewerBusyRate" suffix="%"  />
        </el-card>
      </el-col>
    </el-row>

    <!-- 待安排列表 -->
    <el-card class="schedule-card">
      <template #header>
        <div class="card-header">
          <span>待安排面试候选人</span>
          <div class="header-actions">
            <el-button @click="handleBatchSchedule" :disabled="selectedCandidates.length === 0">
              批量安排
            </el-button>
            <el-button type="primary" @click="handleAutoSchedule">
              <el-icon><magic-stick /></el-icon>
              智能排期
            </el-button>
          </div>
        </div>
      </template>

      <el-table 
        v-loading="loading"
        :data="candidateList" 
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column label="候选人信息" min-width="200">
          <template #default="{ row }">
            <div class="candidate-info">
              <div>
                <strong>{{ row.name }}</strong>
                <el-tag size="small" style="margin-left: 10px">{{ row.currentRound }}</el-tag>
              </div>
              <div class="sub-info">
                {{ row.position }} | {{ row.phone }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="通过时间" width="150">
          <template #default="{ row }">
            {{ formatDate(row.passTime) }}
          </template>
        </el-table-column>
        <el-table-column label="等待天数" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getWaitingType(row.waitingDays)">
              {{ row.waitingDays }}天
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="推荐面试官" width="200">
          <template #default="{ row }">
            <div class="interviewer-recommendation">
              <el-tag 
                v-for="interviewer in row.recommendedInterviewers" 
                :key="interviewer.id"
                size="small"
                style="margin-right: 5px"
              >
                {{ interviewer.name }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="可选时间" width="150">
          <template #default="{ row }">
            <el-button link type="primary" @click="viewAvailableSlots(row)">
              查看 {{ row.availableSlots }} 个时段
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleSchedule(row)">安排面试</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 面试日程表 -->
    <el-card class="calendar-card">
      <template #header>
        <div class="card-header">
          <span>面试日程安排</span>
          <div class="header-actions">
            <el-select v-model="calendarFilter.interviewer" clearable placeholder="筛选面试官" size="small">
              <el-option 
                v-for="interviewer in interviewerList" 
                :key="interviewer.id" 
                :label="interviewer.name" 
                :value="interviewer.id" 
               />
            </el-select>
            <el-button link type="primary" @click="viewFullCalendar">
              查看完整日历
              <el-icon><arrow-right /></el-icon>
            </el-button>
          </div>
        </div>
      </template>

      <!-- 简化版日历视图 -->
      <div class="schedule-timeline">
        <div v-for="day in weekDays" :key="day.date" class="day-column">
          <div class="day-header">
            <div class="weekday">{{ day.weekday }}</div>
            <div class="date">{{ day.dayOfMonth }}</div>
          </div>
          <div class="time-slots">
            <div 
              v-for="slot in day.slots" 
              :key="slot.time"
              class="time-slot"
              :class="{
                occupied: slot.occupied,
                available: !slot.occupied,
                selected: isSlotSelected(day.date, slot.time)
              }"
              @click="handleSlotClick(day.date, slot)"
            >
              <div class="slot-time">{{ slot.time }}</div>
              <div v-if="slot.occupied" class="slot-info">
                <div class="candidate-name">{{ slot.candidateName }}</div>
                <div class="interviewers">{{ slot.interviewers.join(', ') }}</div>
              </div>
              <div v-else class="slot-available">
                可安排
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 安排面试对话框 -->
    <el-dialog 
      v-model="scheduleDialogVisible" 
      :title="`安排面试 - ${currentCandidate?.name}`" 
      width="700px"
    >
      <el-form 
        ref="scheduleFormRef" 
        :model="scheduleForm" 
        :rules="scheduleRules" 
        label-width="100px"
      >
        <el-form-item label="面试轮次">
          <el-tag>{{ currentCandidate?.currentRound }}</el-tag>
        </el-form-item>
        
        <el-form-item label="面试时间" prop="datetime">
          <el-date-picker
            v-model="scheduleForm.datetime"
            type="datetime"
            placeholder="选择日期时间"
            :disabled-date="disabledDate"
            :disabled-hours="disabledHours"
            :disabled-minutes="disabledMinutes"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
            @change="() => checkTimeConflict()"
          />
          <div v-if="timeConflict" class="conflict-warning">
            <el-alert :title="timeConflict" type="warning" :closable="false"  />
          </div>
        </el-form-item>
        
        <el-form-item label="面试时长" prop="duration">
          <el-radio-group v-model="scheduleForm.duration">
            <el-radio :label="30">30分钟</el-radio>
            <el-radio :label="60">60分钟</el-radio>
            <el-radio :label="90">90分钟</el-radio>
            <el-radio :label="120">120分钟</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="面试形式" prop="type">
          <el-radio-group v-model="scheduleForm.type">
            <el-radio label="现场面试">现场面试</el-radio>
            <el-radio label="视频面试">视频面试</el-radio>
            <el-radio label="电话面试">电话面试</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="面试官" prop="interviewerIds">
          <el-select 
            v-model="scheduleForm.interviewerIds" 
            multiple
            placeholder="请选择面试官"
          >
            <el-option-group 
              v-for="group in groupedInterviewers" 
              :key="group.label"
              :label="group.label"
            >
              <el-option 
                v-for="interviewer in group.options" 
                :key="interviewer.id" 
                :label="interviewer.name" 
                :value="interviewer.id"
                :disabled="interviewer.conflict"
              >
                <div class="interviewer-option">
                  <span>{{ interviewer.name }}</span>
                  <span class="interviewer-info">
                    {{ interviewer.department }} | 
                    <span :class="{ 'busy': interviewer.busyRate > 70 }">
                      忙碌度: {{ interviewer.busyRate }}%
                    </span>
                  </span>
                  <span v-if="interviewer.conflict" class="conflict-tag">时间冲突</span>
                </div>
              </el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
        
        <el-form-item label="面试地点" v-if="scheduleForm.type === '现场面试'">
          <el-select v-model="scheduleForm.location" placeholder="请选择面试地点">
            <el-option 
              v-for="room in meetingRooms" 
              :key="room.id" 
              :label="room.name" 
              :value="room.id"
              :disabled="room.occupied"
            >
              <span>{{ room.name }}</span>
              <span v-if="room.occupied" style="color: #f56c6c; margin-left: 10px">
                (已占用)
              </span>
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="面试议程">
          <el-checkbox-group v-model="scheduleForm.agenda">
            <el-checkbox label="自我介绍">自我介绍</el-checkbox>
            <el-checkbox label="项目经历">项目经历</el-checkbox>
            <el-checkbox label="技术考察">技术考察</el-checkbox>
            <el-checkbox label="综合素质">综合素质</el-checkbox>
            <el-checkbox label="答疑环节">答疑环节</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item label="通知设置">
          <el-checkbox v-model="scheduleForm.sendCandidateNotice">发送候选人通知</el-checkbox>
          <el-checkbox v-model="scheduleForm.sendInterviewerNotice">发送面试官通知</el-checkbox>
          <el-checkbox v-model="scheduleForm.sendCalendarInvite">发送日历邀请</el-checkbox>
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input 
            v-model="scheduleForm.remark" 
            type="textarea" 
            :rows="3"
            placeholder="请输入面试相关备注"
            />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="scheduleDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleScheduleSubmit">确定安排</el-button>
      </template>
    </el-dialog>

    <!-- 批量安排对话框 -->
    <el-dialog 
      v-model="batchScheduleVisible" 
      title="批量安排面试" 
      width="800px"
    >
      <div class="batch-schedule">
        <el-alert 
          title="系统将根据面试官空闲时间和候选人偏好，自动安排面试时间" 
          type="info" 
          :closable="false"
          style="margin-bottom: 20px"
         />
        
        <el-table :data="batchScheduleList" max-height="400">
          <el-table-column prop="candidateName" label="候选人" width="120"  />
          <el-table-column prop="position" label="职位" width="150"  />
          <el-table-column label="建议时间" width="180">
            <template #default="{ row }">
              <el-select v-model="row.selectedTime" size="small">
                <el-option 
                  v-for="time in row.suggestedTimes" 
                  :key="time" 
                  :label="time" 
                  :value="time" 
                 />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="面试官" width="200">
            <template #default="{ row }">
              <el-select v-model="row.interviewers" multiple size="small">
                <el-option 
                  v-for="interviewer in interviewerList" 
                  :key="interviewer.id" 
                  :label="interviewer.name" 
                  :value="interviewer.id" 
                 />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="地点">
            <template #default="{ row }">
              <el-input v-model="row.location" size="small"   />
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <template #footer>
        <el-button @click="batchScheduleVisible = false">取消</el-button>
        <el-button type="primary" @click="handleBatchScheduleSubmit">
          确定安排({{ batchScheduleList.length }}场)
        </el-button>
      </template>
    </el-dialog>

    <!-- 时间段查看对话框 -->
    <el-dialog 
      v-model="slotsDialogVisible" 
      title="可选时间段" 
      width="600px"
    >
      <div class="available-slots">
        <el-timeline>
          <el-timeline-item 
            v-for="slot in availableSlots" 
            :key="slot.datetime"
            :timestamp="slot.date"
          >
            <div class="slot-item">
              <div class="slot-time">{{ slot.time }}</div>
              <div class="slot-interviewers">
                可用面试官：
                <el-tag 
                  v-for="interviewer in slot.availableInterviewers" 
                  :key="interviewer"
                  size="small"
                  style="margin-left: 5px"
                >
                  {{ interviewer }}
                </el-tag>
              </div>
              <el-button 
                size="small" 
                type="primary" 
                @click="quickSchedule(slot)"
              >
                选择此时间
              </el-button>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { MagicStick, ArrowRight } from '@element-plus/icons-vue'
import { interviewApi } from '@/api/recruitment'
import type { FormInstance, FormRules } from 'element-plus'
import { useRouter } from 'vue-router'

const router = useRouter()

// 统计数据
const stats = reactive({
  pending: 23,
  todayInterview: 5,
  weekInterview: 18,
  interviewerBusyRate: 65
})

// 数据状态
const loading = ref(false)
const candidateList = ref<any[]>([])
const selectedCandidates = ref<any[]>([])
const calendarFilter = reactive({
  interviewer: ''
})

// 面试官列表
const interviewerList = ref([
  { id: '1', name: 'HrHr张经理', department: '技术部', busyRate: 70 },
  { id: '2', name: '李主管', department: '技术部', busyRate: 45 },
  { id: '3', name: '王总监', department: '人事部', busyRate: 80 },
  { id: '4', name: '陈经理', department: '产品部', busyRate: 30 }
])

// 会议室列表
const meetingRooms = ref([
  { id: '1', name: '会议室A', occupied: false },
  { id: '2', name: '会议室B', occupied: true },
  { id: '3', name: '会议室C', occupied: false },
  { id: '4', name: '视频会议室', occupied: false }
])

// 周视图数据
const weekDays = ref<any[]>([])

// 对话框
const scheduleDialogVisible = ref(false)
const batchScheduleVisible = ref(false)
const slotsDialogVisible = ref(false)
const currentCandidate = ref<unknown>(null)
const timeConflict = ref('')

// 表单数据
const scheduleFormRef = ref<FormInstance>()
const scheduleForm = reactive({
  datetime: '',
  duration: 60,
  type: '现场面试',
  interviewerIds: [] as string[],
  location: '',
  agenda: ['自我介绍', '项目经历', '技术考察'],
  sendCandidateNotice: true,
  sendInterviewerNotice: true,
  sendCalendarInvite: true,
  remark: ''
})

// 表单验证规则
const scheduleRules = reactive<FormRules>({
  datetime: [
    { required: true, message: '请选择面试时间', trigger: 'change' }
  ],
  duration: [
    { required: true, message: '请选择面试时长', trigger: 'change' }
  ],
  type: [
    { required: true, message: '请选择面试形式', trigger: 'change' }
  ],
  interviewerIds: [
    { required: true, message: '请选择面试官', trigger: 'change' }
  ]
})

// 批量安排数据
const batchScheduleList = ref<any[]>([])

// 可用时间段
const availableSlots = ref<any[]>([])

// 分组的面试官
const groupedInterviewers = computed(() => {
  const recommended = currentCandidate.value?.recommendedInterviewers || []
  const others = interviewerList.value.filter(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
    i => !recommended.find((r: unknown) => r.id === i.id)
  )
  
  return [
    {
      label: '推荐面试官',
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
      options: recommended.map((r: unknown) => ({
        ...r,
        conflict: false // 将在需要时动态检查
      }))
    },
    {
      label: '其他面试官',
      options: others.map(i => ({
        ...i,
        conflict: false // 将在需要时动态检查
      }))
    }
  ]
})

// 获取等待类型
const getWaitingType = (days: number) => {
  if (days <= 3) return 'success'
  if (days <= 7) return 'warning'
  return 'danger'
}

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return `${date.getMonth() + 1}-${date.getDate()} ${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}`
}

// 判断时间段是否选中
const isSlotSelected = (date: string, time: string) => {
  // 判断当前选中的时间段
  const selectedDateTime = `${date} ${time}`
  return scheduleForm.datetime && scheduleForm.datetime.startsWith(selectedDateTime)
}

// 禁用日期
const disabledDate = (date: Date) => {
  return date < new Date()
}

// 禁用小时
const disabledHours = () => {
  const hours = []
  for (let i = 0; i < 9; i++) {
    hours.push(i)
  }
  for (let i = 18; i < 24; i++) {
    hours.push(i)
  }
  return hours
}

// 禁用分钟
const disabledMinutes = (hour: number) => {
  // 只允许整点和半点
  const minutes = []
  for (let i = 0; i < 60; i++) {
    if (i !== 0 && i !== 30) {
      minutes.push(i)
    }
  }
  return minutes
}

// 检查面试官冲突
const checkInterviewerConflict = async (interviewerId: string) => {
  if (!scheduleForm.datetime) return false
  
  // 检查面试官在指定时间是否有冲突
  try {
    const endTime = new Date(scheduleForm.datetime)
    endTime.setMinutes(endTime.getMinutes() + (scheduleForm.duration || 60))
    
    const {data} =  await interviewApi.checkConflict({
      interviewerId: parseInt(interviewerId),
      startTime: scheduleForm.datetime,
      endTime: endTime.toISOString()
    })
    
    return data.hasConflict
  } catch (__error) {
    console.error('检查面试官冲突失败:', error)
    return false
  }
}

// 检查时间冲突
const checkTimeConflict  i < 7; i++) {
    const date = new Date(today)
    date.setDate(today.getDate() + i)
    
    const slots = []
    // 生成时间段 (9:00-18:00，每小时一个段)
    for (let hour = 9; hour < 18; hour++) {
      const occupied = Math.random() > 0.6
      slots.push({
        time: `${hour}:00`,
        occupied,
        candidateName: occupied ? `候选人${Math.floor(Math.random() * 10)}` : '',
        interviewers: occupied ? ['张经理', '李主管'] : []
      })
    }
    
    days.push({
      date: date.toISOString().split('T')[0],
      weekday: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][date.getDay()],
      dayOfMonth: date.getDate(),
      slots
    })
  }
  
  weekDays.value = days
}

// 安排面试
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleSchedule = (candidate: unknown) => {
  currentCandidate.value = candidate
  scheduleForm.datetime = ''
  scheduleForm.duration = 60
  scheduleForm.type = '现场面试'
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  scheduleForm.interviewerIds = candidate.recommendedInterviewers.map((i: unknown) => i.id)
  scheduleForm.location = ''
  scheduleForm.remark = ''
  timeConflict.value = ''
  
  scheduleDialogVisible.value = true
}

// 批量安排
const handleBatchSchedule = () => {
  if (selectedCandidates.value.length === 0) {
    ElMessage.warning('请先选择候选人')
    return
  }
  
  // 生成批量安排列表
  batchScheduleList.value = selectedCandidates.value.map(candidate => ({
    candidateId: candidate.id,
    candidateName: candidate.name,
    position: candidate.position,
    suggestedTimes: [
      '2025-01-22 10:00',
      '2025-01-22 14:00',
      '2025-01-23 10:00'
    ],
    selectedTime: '2025-01-22 10:00',
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
    interviewers: candidate.recommendedInterviewers.map((i: unknown) => i.id),
    location: '会议室A'
  }))
  
  batchScheduleVisible.value = true
}

// 智能排期
const handleAutoSchedule = async () => {
  try {
    await ElMessageBox.confirm(
      'AI将根据面试官日程和候选人情况，自动安排所有待面试候选人的面试时间，是否继续？',
      '智能排期',
      {
        confirmButtonText: '开始排期',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    loading.value = true
    
    // 模拟智能排期
    setTimeout(() => {
      loading.value = false
      ElNotification({
        title: '智能排期完成',
        message: '成功为23位候选人安排了面试，请查看日程表',
        type: 'success',
        duration: 5000
      })
      
      fetchCandidateList()
      initWeekView()
    }, 3000)
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('智能排期失败:', error)
    }
  }
}

// 查看可用时间段
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const viewAvailableSlots = (candidate: unknown) => {
  currentCandidate.value = candidate
  
  // 模拟可用时间段
  availableSlots.value = [
    {
      date: '2025-01-22',
      time: '10:00-11:00',
      datetime: '2025-01-22 10:00:00',
      availableInterviewers: ['张经理', '李主管']
    },
    {
      date: '2025-01-22',
      time: '14:00-15:00',
      datetime: '2025-01-22 14:00:00',
      availableInterviewers: ['王总监']
    },
    {
      date: '2025-01-23',
      time: '10:00-11:00',
      datetime: '2025-01-23 10:00:00',
      availableInterviewers: ['张经理', '陈经理']
    }
  ]
  
  slotsDialogVisible.value = true
}

// 快速安排
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const quickSchedule = (slot: unknown) => {
  scheduleForm.datetime = slot.datetime
  scheduleForm.interviewerIds = interviewerList.value
    .filter(i => slot.availableInterviewers.includes(i.name))
    .map(i => i.id)
  
  slotsDialogVisible.value = false
  scheduleDialogVisible.value = true
}

// 时间段点击
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleSlotClick = (date: string, slot: unknown) => {
  if (!slot.occupied) {
    // 快速安排到该时间段
    console.log('选择时间段', date, slot.time)
  }
}

// 查看完整日历
const viewFullCalendar = () => {
  router.push({ name: 'InterviewCalendar' })
}

// 提交安排
const handleScheduleSubmit = async () => {
  if (!scheduleFormRef.value) return
  
  await scheduleFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 调用面试安排API
        const scheduleData = {
          candidateId: parseInt(currentCandidate.value.id),
          candidateName: currentCandidate.value.name,
          position: currentCandidate.value.position,
          round: currentCandidate.value.currentRound,
          interviewDate: scheduleForm.datetime.split(' ')[0],
          interviewTime: scheduleForm.datetime.split(' ')[1],
          duration: scheduleForm.duration,
          type: scheduleForm.type,
          interviewerIds: scheduleForm.interviewerIds.map(id => parseInt(id)),
          location: scheduleForm.location,
          agenda: scheduleForm.agenda,
          remark: scheduleForm.remark,
          notificationSettings: {
            sendCandidateNotice: scheduleForm.sendCandidateNotice,
            sendInterviewerNotice: scheduleForm.sendInterviewerNotice,
            sendCalendarInvite: scheduleForm.sendCalendarInvite
          }
        }
        
        const {data: _data} =  await interviewApi.create(scheduleData)
        
        ElMessage.success('面试安排成功')
        scheduleDialogVisible.value 
  
  .stats-row {
    margin-bottom: 20px;
  }
  
  .schedule-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .candidate-info {
    .sub-info {
      font-size: 12px;
      color: #909399;
      margin-top: 4px;
    }
  }
  
  .calendar-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .schedule-timeline {
    display: flex;
    gap: 10px;
    overflow-x: auto;
    padding-bottom: 10px;
    
    .day-column {
      flex: 0 0 140px;
      
      .day-header {
        text-align: center;
        padding: 10px;
        background: #f5f7fa;
        border-radius: 4px 4px 0 0;
        
        .weekday {
          font-size: 12px;
          color: #909399;
        }
        
        .date {
          font-size: 16px;
          font-weight: 500;
          color: #303133;
        }
      }
      
      .time-slots {
        border: 1px solid #ebeef5;
        border-top: none;
        border-radius: 0 0 4px 4px;
        
        .time-slot {
          padding: 8px;
          border-bottom: 1px solid #ebeef5;
          cursor: pointer;
          transition: all 0.3s;
          
          &:last-child {
            border-bottom: none;
          }
          
          &:hover {
            background: #f5f7fa;
          }
          
          &.occupied {
            background: #fef0f0;
            cursor: default;
          }
          
          &.available {
            &:hover {
              background: #f0f9ff;
            }
          }
          
          &.selected {
            background: #ecf5ff;
            border-color: #409eff;
          }
          
          .slot-time {
            font-size: 12px;
            font-weight: 500;
            color: #303133;
            margin-bottom: 4px;
          }
          
          .slot-info {
            .candidate-name {
              font-size: 12px;
              color: #303133;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            
            .interviewers {
              font-size: 11px;
              color: #909399;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
          
          .slot-available {
            font-size: 12px;
            color: #67c23a;
            text-align: center;
          }
        }
      }
    }
  }
  
  .conflict-warning {
    margin-top: 10px;
  }
  
  .interviewer-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    
    .interviewer-info {
      font-size: 12px;
      color: #909399;
      
      .busy {
        color: #e6a23c;
      }
    }
    
    .conflict-tag {
      font-size: 12px;
      color: #f56c6c;
    }
  }
  
  .batch-schedule {
    .el-table {
      margin-top: 10px;
    }
  }
  
  .available-slots {
    .slot-item {
      display: flex;
      align-items: center;
      gap: 15px;
      
      .slot-time {
        font-weight: 500;
        color: #303133;
        width: 100px;
      }
      
      .slot-interviewers {
        flex: 1;
        display: flex;
        align-items: center;
      }
    }
  }
}
</style>