<template>
  <div class="resume-screening-process">
    <!-- 顶部操作栏 -->
    <el-card class="header-card">
      <div class="header-content">
        <div class="left-section">
          <h3>简历筛选流程</h3>
          <el-tag type="info">待筛选: {{ pendingCount }}</el-tag>
          <el-tag type="success">今日已处理: {{ todayProcessed }}</el-tag>
        </div>
        <div class="right-section">
          <el-button @click="handleBatchScreen" :disabled="selectedResumes.length === 0">
            <el-icon><filter /></el-icon>
            批量筛选
          </el-button>
          <el-button @click="handleAIScreen" type="primary">
            <el-icon><magic-stick /></el-icon>
            AI智能筛选
          </el-button>
          <el-button @click="handleExport">
            <el-icon><download /></el-icon>
            导出结果
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 筛选条件配置 -->
    <el-card class="filter-card">
      <template #header>
        <div class="card-header">
          <span>筛选条件设置</span>
          <el-button link type="primary" @click="saveAsTemplate">保存为模板</el-button>
        </div>
      </template>
      
      <el-form :model="screeningCriteria" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="学历要求">
              <el-select v-model="screeningCriteria.education" placeholder="请选择">
                <el-option label="不限" value=""  />
                <el-option label="专科及以上" value="专科"  />
                <el-option label="本科及以上" value="本科"  />
                <el-option label="硕士及以上" value="硕士"  />
                <el-option label="博士" value="博士"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="工作经验">
              <el-input-number 
                v-model="screeningCriteria.experience" 
                :min="0" 
                :max="20"
                placeholder="年"
                />
              <span style="margin-left: 10px">年以上</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="年龄范围">
              <el-input-number 
                v-model="screeningCriteria.ageMin" 
                :min="18" 
                :max="60"
                style="width: 80px"
                />
              <span style="margin: 0 5px">-</span>
              <el-input-number 
                v-model="screeningCriteria.ageMax" 
                :min="18" 
                :max="60"
                style="width: 80px"
                />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="必备技能">
              <el-select 
                v-model="screeningCriteria.requiredSkills" 
                multiple
                filterable
                allow-create
                placeholder="请输入或选择技能"
                style="width: 100%"
              >
                <el-option 
                  v-for="skill in skillOptions" 
                  :key="skill" 
                  :label="skill" 
                  :value="skill" 
                 />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="关键词匹配">
              <el-input 
                v-model="screeningCriteria.keywords" 
                placeholder="请输入关键词，用逗号分隔"
                />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="AI匹配度">
          <el-slider 
            v-model="screeningCriteria.aiMatchScore" 
            :min="0" 
            :max="100"
            :marks="{ 0: '0%', 50: '50%', 80: '80%', 100: '100%' }"
            show-input
           />
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 简历列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>简历列表</span>
          <div class="header-actions">
            <el-radio-group v-model="viewMode" size="small">
              <el-radio-button label="list">列表模式</el-radio-button>
              <el-radio-button label="card">卡片模式</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </template>

      <!-- 列表模式 -->
      <div v-if="viewMode === 'list'">
        <el-table 
          v-loading="loading"
          :data="resumeList" 
          @selection-change="handleSelectionChange"
          row-key="id"
        >
          <el-table-column type="selection" width="55"  />
          <el-table-column label="候选人" min-width="200">
            <template #default="{ row }">
              <div class="candidate-info">
                <el-avatar :src="row.photo" :size="40">
                  {{ row.name.charAt(0) }}
                </el-avatar>
                <div class="info-text">
                  <div class="name">{{ row.name }}</div>
                  <div class="details">{{ row.education }} | {{ row.experience }}年经验</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="expectedPosition" label="应聘职位" width="150"  />
          <el-table-column label="技能匹配" width="200">
            <template #default="{ row }">
              <div class="skill-match">
                <el-tag 
                  v-for="skill in getMatchedSkills(row.skills)" 
                  :key="skill"
                  size="small"
                  type="success"
                  style="margin-right: 5px"
                >
                  {{ skill }}
                </el-tag>
                <span v-if="getUnmatchedSkillsCount(row.skills) > 0" class="unmatched">
                  +{{ getUnmatchedSkillsCount(row.skills) }}项
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="AI评分" width="150" align="center">
            <template #default="{ row }">
              <div class="ai-score">
                <el-progress 
                  type="circle" 
                  :percentage="row.aiScore || 0" 
                  :width="50"
                  :color="getScoreColor(row.aiScore)"
                 />
                <el-button 
                  link 
                  type="primary" 
                  size="small"
                  @click="viewAIAnalysis(row)"
                >
                  查看分析
                </el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="筛选结果" width="120" align="center">
            <template #default="{ row }">
              <el-tag 
                v-if="row.screeningResult"
                :type="getResultTag(row.screeningResult)"
              >
                {{ row.screeningResult }}
              </el-tag>
              <span v-else class="pending">待筛选</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="{ row }">
              <el-button link type="primary" @click="handleView(row)">查看</el-button>
              <el-button link type="success" @click="handlePass(row)">通过</el-button>
              <el-button link type="danger" @click="handleReject(row)">拒绝</el-button>
              <el-button link type="warning" @click="handlePending(row)">待定</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 卡片模式 -->
      <div v-else class="card-view">
        <el-row :gutter="20">
          <el-col 
            v-for="resume in resumeList" 
            :key="resume.id"
            :xs="24" 
            :sm="12" 
            :md="8" 
            :lg="6"
          >
            <div 
              class="resume-card"
              :class="{ selected: isSelected(resume.id) }"
              @click="toggleSelection(resume)"
            >
              <div class="card-header">
                <el-checkbox 
                  :model-value="isSelected(resume.id)"
                  @click.stop
                  @change="toggleSelection(resume)"
                 />
                <el-tag 
                  v-if="resume.screeningResult"
                  :type="getResultTag(resume.screeningResult)"
                  size="small"
                >
                  {{ resume.screeningResult }}
                </el-tag>
              </div>
              
              <div class="card-body">
                <el-avatar :src="resume.photo" :size="60">
                  {{ resume.name.charAt(0) }}
                </el-avatar>
                <h4>{{ resume.name }}</h4>
                <p class="position">{{ resume.expectedPosition }}</p>
                <p class="info">{{ resume.education }} | {{ resume.experience }}年</p>
                
                <div class="ai-score-card">
                  <span>AI匹配度：</span>
                  <el-progress 
                    :percentage="resume.aiScore || 0" 
                    :color="getScoreColor(resume.aiScore)"
                   />
                </div>
                
                <div class="skills">
                  <el-tag 
                    v-for="skill in resume.skills.slice(0, 3)" 
                    :key="skill"
                    size="small"
                    style="margin: 2px"
                  >
                    {{ skill }}
                  </el-tag>
                  <span v-if="resume.skills.length > 3" class="more">
                    +{{ resume.skills.length - 3 }}
                  </span>
                </div>
              </div>
              
              <div class="card-footer">
                <el-button size="small" @click.stop="handleView(resume)">查看</el-button>
                <el-button-group size="small">
                  <el-button type="success" @click.stop="handlePass(resume)">通过</el-button>
                  <el-button type="danger" @click.stop="handleReject(resume)">拒绝</el-button>
                </el-button-group>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- AI分析对话框 -->
    <el-dialog 
      v-model="aiAnalysisVisible" 
      title="AI智能分析报告" 
      width="800px"
    >
      <div v-if="currentAnalysis" class="ai-analysis">
        <el-row :gutter="20">
          <el-col :span="12">
            <h4>匹配度分析</h4>
            <el-progress 
              :percentage="currentAnalysis.overallScore" 
              :color="getScoreColor(currentAnalysis.overallScore)"
              style="margin-bottom: 20px"
             />
            
            <div class="score-breakdown">
              <div class="score-item">
                <span>教育背景：</span>
                <el-progress :percentage="currentAnalysis.educationScore" :show-text="false"  />
                <span class="score-value">{{ currentAnalysis.educationScore }}%</span>
              </div>
              <div class="score-item">
                <span>工作经验：</span>
                <el-progress :percentage="currentAnalysis.experienceScore" :show-text="false"  />
                <span class="score-value">{{ currentAnalysis.experienceScore }}%</span>
              </div>
              <div class="score-item">
                <span>技能匹配：</span>
                <el-progress :percentage="currentAnalysis.skillScore" :show-text="false"  />
                <span class="score-value">{{ currentAnalysis.skillScore }}%</span>
              </div>
              <div class="score-item">
                <span>关键词匹配：</span>
                <el-progress :percentage="currentAnalysis.keywordScore" :show-text="false"  />
                <span class="score-value">{{ currentAnalysis.keywordScore }}%</span>
              </div>
            </div>
          </el-col>
          
          <el-col :span="12">
            <h4>亮点与不足</h4>
            <div class="analysis-content">
              <div class="highlights">
                <h5><el-icon><success-filled /></el-icon> 亮点</h5>
                <ul>
                  <li v-for="(item, index) in currentAnalysis.highlights" :key="index">
                    {{ item }}
                  </li>
                </ul>
              </div>
              
              <div class="concerns">
                <h5><el-icon><warning-filled /></el-icon> 不足</h5>
                <ul>
                  <li v-for="(item, index) in currentAnalysis.concerns" :key="index">
                    {{ item }}
                  </li>
                </ul>
              </div>
            </div>
          </el-col>
        </el-row>
        
        <div class="ai-suggestion">
          <h4>AI建议</h4>
          <el-alert 
            :title="currentAnalysis.suggestion" 
            :type="currentAnalysis.recommendAction === '建议通过' ? 'success' : 
                   currentAnalysis.recommendAction === '建议拒绝' ? 'error' : 'warning'"
            :closable="false"
           />
        </div>
      </div>
    </el-dialog>

    <!-- 批量操作对话框 -->
    <el-dialog 
      v-model="batchDialogVisible" 
      title="批量筛选操作" 
      width="500px"
    >
      <el-form :model="batchForm" label-width="100px">
        <el-form-item label="选中简历">
          <el-tag>已选择 {{ selectedResumes.length }} 份简历</el-tag>
        </el-form-item>
        
        <el-form-item label="筛选结果">
          <el-radio-group v-model="batchForm.result">
            <el-radio label="通过">通过</el-radio>
            <el-radio label="拒绝">拒绝</el-radio>
            <el-radio label="待定">待定</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="操作原因">
          <el-input 
            v-model="batchForm.reason" 
            type="textarea" 
            :rows="3"
            placeholder="请输入批量操作的原因"
            />
        </el-form-item>
        
        <el-form-item label="发送通知">
          <el-switch v-model="batchForm.sendNotification"  />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="batchDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleBatchSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { 
  Filter, 
  MagicStick, 
  Download,
  SuccessFilled,
  WarningFilled
} from '@element-plus/icons-vue'
import { resumeApi } from '@/api/recruitment'
import type { Resume } from '@/types/recruitment'
import { useRouter } from 'vue-router'

const router = useRouter()

// 数据状态
const loading = ref(false)
const pendingCount = ref(156)
const todayProcessed = ref(23)
const viewMode = ref<'list' | 'card'>('list')

// 筛选条件
const screeningCriteria = reactive({
  education: '',
  experience: 0,
  ageMin: 22,
  ageMax: 45,
  requiredSkills: [] as string[],
  keywords: '',
  aiMatchScore: 60
})

// 技能选项
const skillOptions = ref([
  'JavaScript', 'Vue.js', 'React', 'Node.js', 'Python',
  'Java', 'Spring', 'MySQL', 'MongoDB', 'Docker',
  '项目管理', '团队协作', '沟通能力', '英语'
])

// 简历列表
const resumeList = ref<Resume[]>([])
const selectedResumes = ref<Resume[]>([])

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// AI分析对话框
const aiAnalysisVisible = ref(false)
const currentAnalysis = ref<unknown>(null)

// 批量操作对话框
const batchDialogVisible = ref(false)
const batchForm = reactive({
  result: '通过',
  reason: '',
  sendNotification: true
})

// 获取匹配的技能
const getMatchedSkills = (skills: string[]) => {
  if (!screeningCriteria.requiredSkills.length) return skills.slice(0, 3)
  return skills.filter(skill => 
    screeningCriteria.requiredSkills.includes(skill)
  ).slice(0, 3)
}

// 获取未匹配技能数量
const getUnmatchedSkillsCount = (skills: string[]) => {
  const matched = getMatchedSkills(skills)
  return skills.length - matched.length
}

// 获取分数颜色
const getScoreColor = (score?: number) => {
  if (!score) return '#909399'
  if (score >= 80) return '#67C23A'
  if (score >= 60) return '#E6A23C'
  return '#F56C6C'
}

// 获取结果标签类型
const getResultTag = (result: string) => {
  const map: Record<string, string> = {
    '通过': 'success',
    '拒绝': 'danger',
    '待定': 'warning'
  }
  return map[result] || 'info'
}

// 判断是否选中
const isSelected = (id: string) => {
  return selectedResumes.value.some(r => r.id === id)
}

// 切换选择状态
const toggleSelection = (resume: Resume) => {
  const index = selectedResumes.value.findIndex(r => r.id === resume.id)
  if (index > -1) {
    selectedResumes.value.splice(index, 1)
  } else {
    selectedResumes.value.push(resume)
  }
}

// 获取简历列表
const fetchResumeList = async () => {
  loading.value = true
  try {
    // 模拟数据
    const mockData: Resume[] = [
      {
        id: '1',
        name: 'HrHr张三',
        gender: '男',
        birthDate: '1995-05-15',
        phone: '13800138001',
        email: '<EMAIL>',
        photo: '',
        education: '硕士',
        school: '浙江大学',
        major: '计算机科学与技术',
        experience: '5',
        expectedPosition: '前端工程师',
        expectedSalaryMin: 15,
        expectedSalaryMax: 20,
        skills: ['Vue.js', 'React', 'Node.js', 'TypeScript', 'Webpack'],
        aiScore: 85,
        aiParsed: true,
        status: '待筛选',
        createTime: '2025-01-20 10:00:00',
        updateTime: '2025-01-20 10:00:00'
      },
      {
        id: '2',
        name: '李四',
        gender: '女',
        birthDate: '1998-03-20',
        phone: '13800138002',
        email: '<EMAIL>',
        photo: '',
        education: '本科',
        school: '杭州电子科技大学',
        major: '软件工程',
        experience: '2',
        expectedPosition: '前端工程师',
        expectedSalaryMin: 10,
        expectedSalaryMax: 15,
        skills: ['Vue.js', 'JavaScript', 'CSS3', 'HTML5'],
        aiScore: 72,
        aiParsed: true,
        status: '待筛选',
        screeningResult: '通过',
        createTime: '2025-01-20 11:00:00',
        updateTime: '2025-01-20 11:00:00'
      }
    ]
    
    resumeList.value = mockData
    pagination.total = 2
  } catch (__error) {
    console.error('获取简历列表失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 查看简历
const handleView = (resume: Resume) => {
  router.push({
    name: 'ResumeDetail',
    params: { id: resume.id }
  })
}

// 通过简历
const handlePass = async (resume: Resume) => {
  try {
    await ElMessageBox.confirm(
      `确定通过 ${resume.name} 的简历吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'success'
      }
    )
    
    // 调用API更新状态
    await resumeApi.updateStatus(Number(resume.id), {
      status: '通过',
      comment: '简历筛选通过'
    })
    
    resume.screeningResult = '通过'
    ElMessage.success('操作成功')
    
    // 更新统计
    todayProcessed.value++
    pendingCount.value--
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('操作失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

// 拒绝简历
const handleReject = async (resume: Resume) => {
  try {
    await ElMessageBox.confirm(
      `确定拒绝 ${resume.name} 的简历吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 调用API更新状态
    await resumeApi.updateStatus(Number(resume.id), {
      status: '拒绝',
      comment: '简历筛选未通过'
    })
    
    resume.screeningResult = '拒绝'
    ElMessage.success('操作成功')
    
    // 更新统计
    todayProcessed.value++
    pendingCount.value--
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('操作失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

// 待定简历
const handlePending = (resume: Resume) => {
  resume.screeningResult = '待定'
  ElMessage.info('已标记为待定')
}

// 查看AI分析
const viewAIAnalysis = (resume: Resume) => {
  // 模拟AI分析数据
  currentAnalysis.value = {
    overallScore: resume.aiScore || 0,
    educationScore: 90,
    experienceScore: 75,
    skillScore: 85,
    keywordScore: 70,
    highlights: [
      '教育背景优秀，毕业于重点院校',
      '技能栈与岗位需求高度匹配',
      '有相关项目经验',
      '年龄适中，有发展潜力'
    ],
    concerns: [
      '工作经验略低于岗位要求',
      '缺少大型项目经验',
      '期望薪资略高'
    ],
    suggestion: '该候选人整体素质较好，建议安排面试进一步了解',
    recommendAction: '建议通过'
  }
  
  aiAnalysisVisible.value = true
}

// 批量筛选
const handleBatchScreen = () => {
  if (selectedResumes.value.length === 0) {
    ElMessage.warning('请先选择要处理的简历')
    return
  }
  
  batchDialogVisible.value = true
}

// AI智能筛选
const handleAIScreen = async () => {
  try {
    await ElMessageBox.confirm(
      'AI将根据设定的筛选条件自动处理所有待筛选简历，是否继续？',
      'AI智能筛选',
      {
        confirmButtonText: '开始筛选',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    loading.value = true
    
    // 模拟AI筛选过程
    setTimeout(() => {
      loading.value = false
      ElNotification({
        title: 'AI筛选完成',
        message: '共处理156份简历，通过89份，拒绝45份，待定22份',
        type: 'success',
        duration: 5000
      })
      
      fetchResumeList()
    }, 3000)
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('AI筛选失败:', error)
    }
  }
}

// 导出结果
const handleExport = () => {
  ElMessage.info('导出功能开发中')
}

// 保存为模板
const saveAsTemplate = () => {
  ElMessage.success('筛选条件已保存为模板')
}

// 表格选择变化
const handleSelectionChange = (selection: Resume[]) => {
  selectedResumes.value = selection
}

// 批量操作提交
const handleBatchSubmit = async () => {
  try {
    // 调用批量API
    const ids = selectedResumes.value.map(r => Number(r.id))
    await resumeApi.batchScreen(ids, batchForm.result)
    
    // 如果需要发送通知
    if (batchForm.sendNotification) {
      // 可以在这里调用通知API
      console.log('发送通知给候选人')
    }
    
    ElMessage.success(`成功将${selectedResumes.value.length}份简历标记为${batchForm.result}`)
    
    // 更新统计
    todayProcessed.value += selectedResumes.value.length
    pendingCount.value -= selectedResumes.value.length
    
    batchDialogVisible.value = false
    selectedResumes.value = []
    fetchResumeList()
  } catch (__error) {
    console.error('批量操作失败:', error)
    ElMessage.error('操作失败')
  }
}

// 分页变化
const handleSizeChange = () => {
  fetchResumeList()
}

const handleCurrentChange = () => {
  fetchResumeList()
}

// 初始化
onMounted(() => {
  fetchResumeList()
})
</script>

<style lang="scss" scoped>
.resume-screening-process {
  padding: 20px;
  
  .header-card {
    margin-bottom: 20px;
    
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .left-section {
        display: flex;
        align-items: center;
        gap: 15px;
        
        h3 {
          margin: 0;
        }
      }
    }
  }
  
  .filter-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .candidate-info {
    display: flex;
    align-items: center;
    gap: 10px;
    
    .info-text {
      .name {
        font-weight: 500;
        margin-bottom: 4px;
      }
      
      .details {
        font-size: 12px;
        color: #909399;
      }
    }
  }
  
  .skill-match {
    .unmatched {
      font-size: 12px;
      color: #909399;
      margin-left: 5px;
    }
  }
  
  .ai-score {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
  }
  
  .pending {
    color: #909399;
  }
  
  // 卡片视图
  .card-view {
    .resume-card {
      border: 1px solid #ebeef5;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 20px;
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover {
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      }
      
      &.selected {
        border-color: #409eff;
        background-color: #ecf5ff;
      }
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
      }
      
      .card-body {
        text-align: center;
        
        h4 {
          margin: 10px 0 5px;
        }
        
        .position {
          color: #303133;
          margin-bottom: 5px;
        }
        
        .info {
          font-size: 12px;
          color: #909399;
          margin-bottom: 10px;
        }
        
        .ai-score-card {
          margin: 10px 0;
          font-size: 13px;
        }
        
        .skills {
          margin-top: 10px;
          
          .more {
            font-size: 12px;
            color: #909399;
          }
        }
      }
      
      .card-footer {
        display: flex;
        justify-content: space-between;
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #ebeef5;
      }
    }
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}

// AI分析对话框
.ai-analysis {
  h4 {
    margin-bottom: 15px;
    color: #303133;
  }
  
  .score-breakdown {
    .score-item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      
      > span:first-child {
        width: 80px;
        color: #606266;
      }
      
      .el-progress {
        flex: 1;
        margin: 0 10px;
      }
      
      .score-value {
        width: 40px;
        text-align: right;
        color: #303133;
        font-weight: 500;
      }
    }
  }
  
  .analysis-content {
    h5 {
      display: flex;
      align-items: center;
      gap: 5px;
      margin-bottom: 10px;
      color: #303133;
      
      .el-icon {
        font-size: 18px;
      }
    }
    
    .highlights {
      margin-bottom: 20px;
      
      h5 .el-icon {
        color: #67c23a;
      }
    }
    
    .concerns {
      h5 .el-icon {
        color: #e6a23c;
      }
    }
    
    ul {
      list-style: none;
      padding-left: 24px;
      
      li {
        position: relative;
        margin-bottom: 8px;
        color: #606266;
        line-height: 1.6;
        
        &:before {
          content: '•';
          position: absolute;
          left: -16px;
          color: #909399;
        }
      }
    }
  }
  
  .ai-suggestion {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #ebeef5;
  }
}
</style>