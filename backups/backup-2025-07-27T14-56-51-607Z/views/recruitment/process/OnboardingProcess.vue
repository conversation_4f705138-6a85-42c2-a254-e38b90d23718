<template>
  <div class="onboarding-process">
    <!-- 待入职人员列表 -->
    <el-card class="pending-card">
      <template #header>
        <div class="card-header">
          <span>待入职人员</span>
          <el-tag type="primary">{{ pendingOnboardings.length }}</el-tag>
        </div>
      </template>

      <el-table 
        v-loading="loading"
        :data="pendingOnboardings" 
        stripe
      >
        <el-table-column label="人员信息" min-width="200">
          <template #default="{ row }">
            <div class="employee-info">
              <div class="main-info">
                <strong>{{ row.name }}</strong>
                <el-tag size="small" :type="getStatusType(row.offerStatus)">
                  {{ row.offerStatus }}
                </el-tag>
              </div>
              <div class="sub-info">
                {{ row.position }} | {{ row.department }}
              </div>
              <div class="extra-info">
                预计入职：{{ row.expectedJoinDate }}
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="入职准备" width="200">
          <template #default="{ row }">
            <div class="preparation-info">
              <div class="progress-item">
                <span>材料收集：</span>
                <el-progress 
                  :percentage="row.documentProgress" 
                  :stroke-width="6"
                  :color="getProgressColor(row.documentProgress)"
                 />
              </div>
              <div class="progress-item">
                <span>系统准备：</span>
                <el-progress 
                  :percentage="row.systemProgress" 
                  :stroke-width="6"
                  :color="getProgressColor(row.systemProgress)"
                 />
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="hr" label="对接HR" width="100"  />
        <el-table-column prop="mentor" label="指定导师" width="100">
          <template #default="{ row }">
            <span v-if="row.mentor">{{ row.mentor }}</span>
            <el-tag v-else type="warning" size="small">待指定</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="入职进度" width="120" align="center">
          <template #default="{ row }">
            <div class="overall-progress">
              <el-progress 
                type="circle" 
                :percentage="row.overallProgress" 
                :width="60"
                :color="getProgressColor(row.overallProgress)"
               />
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleOnboarding(row)">
              办理入职
            </el-button>
            <el-button link type="primary" @click="viewDetail(row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 入职办理对话框 -->
    <el-dialog 
      v-model="onboardingDialogVisible" 
      :title="`入职办理 - ${currentEmployee?.name}`" 
      width="1000px"
      top="5vh"
    >
      <el-tabs v-model="activeTab">
        <!-- 材料收集 -->
        <el-tab-pane label="材料收集" name="documents">
          <el-form label-width="120px">
            <el-form-item label="必需材料">
              <el-checkbox-group v-model="collectedDocuments">
                <div class="document-list">
                  <el-checkbox 
                    v-for="doc in requiredDocuments" 
                    :key="doc.id"
                    :label="doc.name"
                  >
                    <span>{{ doc.name }}</span>
                    <el-tag v-if="doc.required" type="danger" size="small" style="margin-left: 10px">
                      必需
                    </el-tag>
                  </el-checkbox>
                </div>
              </el-checkbox-group>
            </el-form-item>
            
            <el-form-item label="材料上传">
              <el-upload
                class="upload-demo"
                :action="uploadUrl"
                :on-preview="handlePreview"
                :on-remove="handleRemove"
                :file-list="fileList"
                :before-upload="beforeUpload"
                multiple
              >
                <el-button type="primary">上传文件</el-button>
                <template #tip>
                  <div class="el-upload__tip">
                    支持jpg/png/pdf格式，单个文件不超过10MB
                  </div>
                </template>
              </el-upload>
            </el-form-item>
            
            <el-form-item label="材料审核">
              <el-table :data="documentReviewList" size="small">
                <el-table-column prop="name" label="材料名称"  />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getDocStatusType(row.status)">
                      {{ row.status }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="reviewer" label="审核人" width="100"  />
                <el-table-column prop="remark" label="备注"  />
              </el-table>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 系统准备 -->
        <el-tab-pane label="系统准备" name="system">
          <el-form label-width="120px">
            <el-form-item label="员工编号">
              <el-input v-model="systemForm.employeeId" style="width: 300px">
                <template #append>
                  <el-button @click="generateEmployeeId">自动生成</el-button>
                </template>
              </el-input>
            </el-form-item>
            
            <el-form-item label="邮箱账号">
              <el-input v-model="systemForm.email" placeholder="@company.com" style="width: 300px"   />
              <el-button type="primary" link @click="createEmail" style="margin-left: 10px">
                创建邮箱
              </el-button>
            </el-form-item>
            
            <el-form-item label="系统权限">
              <el-transfer
                v-model="systemForm.permissions"
                :data="allPermissions"
                :titles="['可选权限', '已选权限']"
                filterable
                filter-placeholder="搜索权限"
               />
            </el-form-item>
            
            <el-form-item label="办公设备">
              <el-checkbox-group v-model="systemForm.equipment">
                <el-checkbox label="笔记本电脑">笔记本电脑</el-checkbox>
                <el-checkbox label="显示器">显示器</el-checkbox>
                <el-checkbox label="键鼠套装">键鼠套装</el-checkbox>
                <el-checkbox label="手机">手机</el-checkbox>
                <el-checkbox label="门禁卡">门禁卡</el-checkbox>
                <el-checkbox label="工位">工位</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            
            <el-form-item label="系统账号">
              <el-table :data="systemAccounts" size="small">
                <el-table-column prop="system" label="系统名称"  />
                <el-table-column prop="account" label="账号"  />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.status === '已创建' ? 'success' : 'info'">
                      {{ row.status }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="100">
                  <template #default="{ row }">
                    <el-button 
                      v-if="row.status === '待创建'" 
                      link 
                      type="primary" 
                      @click="createAccount(row)"
                    >
                      创建
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 入职安排 -->
        <el-tab-pane label="入职安排" name="arrangement">
          <el-form :model="arrangementForm" label-width="120px">
            <el-form-item label="报到时间">
              <el-date-picker
                v-model="arrangementForm.reportTime"
                type="datetime"
                placeholder="选择日期时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm"
               />
            </el-form-item>
            
            <el-form-item label="报到地点">
              <el-input v-model="arrangementForm.reportLocation" placeholder="如：总部大厦3楼人力资源部"   />
            </el-form-item>
            
            <el-form-item label="接待人员">
              <el-select v-model="arrangementForm.receptionist" placeholder="请选择">
                <el-option 
                  v-for="hr in hrList" 
                  :key="hr.id"
                  :label="hr.name"
                  :value="hr.name"
                 />
              </el-select>
            </el-form-item>
            
            <el-form-item label="入职导师">
              <el-select v-model="arrangementForm.mentor" placeholder="请选择">
                <el-option 
                  v-for="mentor in mentorList" 
                  :key="mentor.id"
                  :label="`${mentor.name} (${mentor.position})`"
                  :value="mentor.name"
                 />
              </el-select>
            </el-form-item>
            
            <el-form-item label="首日安排">
              <div class="schedule-timeline">
                <el-timeline>
                  <el-timeline-item 
                    v-for="(activity, index) in firstDaySchedule" 
                    :key="index"
                    :timestamp="activity.time"
                  >
                    <div class="activity-content">
                      <strong>{{ activity.title }}</strong>
                      <p>{{ activity.description }}</p>
                      <el-tag v-if="activity.responsible" size="small">
                        负责人：{{ activity.responsible }}
                      </el-tag>
                    </div>
                  </el-timeline-item>
                </el-timeline>
              </div>
            </el-form-item>
            
            <el-form-item label="培训计划">
              <el-table :data="trainingPlan" size="small">
                <el-table-column prop="course" label="培训课程"  />
                <el-table-column prop="type" label="类型" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.type === '必修' ? 'danger' : 'primary'" size="small">
                      {{ row.type }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="duration" label="时长" width="80"  />
                <el-table-column prop="deadline" label="完成期限" width="120"  />
                <el-table-column prop="trainer" label="培训讲师" width="100"  />
              </el-table>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 欢迎准备 -->
        <el-tab-pane label="欢迎准备" name="welcome">
          <el-form :model="welcomeForm" label-width="120px">
            <el-form-item label="欢迎邮件">
              <el-switch v-model="welcomeForm.sendWelcomeEmail"  />
              <el-button 
                v-if="welcomeForm.sendWelcomeEmail" 
                type="primary" 
                link 
                @click="previewWelcomeEmail"
                style="margin-left: 20px"
              >
                预览邮件
              </el-button>
            </el-form-item>
            
            <el-form-item label="工位准备">
              <el-checkbox-group v-model="welcomeForm.deskItems">
                <el-checkbox label="欢迎卡片">欢迎卡片</el-checkbox>
                <el-checkbox label="公司手册">公司手册</el-checkbox>
                <el-checkbox label="办公用品">办公用品</el-checkbox>
                <el-checkbox label="企业文化礼品">企业文化礼品</el-checkbox>
                <el-checkbox label="茶水零食">茶水零食</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            
            <el-form-item label="团队通知">
              <el-input 
                v-model="welcomeForm.teamNotification" 
                type="textarea" 
                :rows="4"
                placeholder="向团队介绍新同事的邮件内容..."
                />
            </el-form-item>
            
            <el-form-item label="欢迎活动">
              <el-checkbox-group v-model="welcomeForm.activities">
                <el-checkbox label="部门欢迎会">部门欢迎会</el-checkbox>
                <el-checkbox label="团队午餐">团队午餐</el-checkbox>
                <el-checkbox label="公司参观">公司参观</el-checkbox>
                <el-checkbox label="新人见面会">新人见面会</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            
            <el-form-item label="入职礼包">
              <el-checkbox-group v-model="welcomeForm.giftPackage">
                <el-checkbox label="公司T恤">公司T恤</el-checkbox>
                <el-checkbox label="背包">背包</el-checkbox>
                <el-checkbox label="水杯">水杯</el-checkbox>
                <el-checkbox label="笔记本">笔记本</el-checkbox>
                <el-checkbox label="员工手册">员工手册</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
      
      <template #footer>
        <el-button @click="onboardingDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveProgress">保存进度</el-button>
        <el-button type="success" @click="completeOnboarding">完成入职</el-button>
      </template>
    </el-dialog>

    <!-- 欢迎邮件预览 -->
    <el-dialog 
      v-model="emailPreviewVisible" 
      title="欢迎邮件预览" 
      width="700px"
    >
      <div class="email-preview">
        <div class="email-header">
          <p><strong>收件人：</strong>{{ currentEmployee?.name }} &lt;{{ systemForm.email }}&gt;</p>
          <p><strong>主题：</strong>欢迎加入杭州科技职业技术学院！</p>
        </div>
        <div class="email-content">
          <p>亲爱的{{ currentEmployee?.name }}：</p>
          
          <p>欢迎加入杭州科技职业技术学院大家庭！</p>
          
          <p>
            我们非常高兴您能够加入{{ currentEmployee?.department }}，
            担任{{ currentEmployee?.position }}一职。相信您的加入将为我们的团队带来新的活力和创新。
          </p>
          
          <p>您的入职信息如下：</p>
          <ul>
            <li>员工编号：{{ systemForm.employeeId }}</li>
            <li>邮箱账号：{{ systemForm.email }}</li>
            <li>报到时间：{{ arrangementForm.reportTime }}</li>
            <li>报到地点：{{ arrangementForm.reportLocation }}</li>
            <li>接待人员：{{ arrangementForm.receptionist }}</li>
            <li>入职导师：{{ arrangementForm.mentor }}</li>
          </ul>
          
          <p>
            在您入职的第一天，我们为您准备了详细的入职安排，
            您的导师{{ arrangementForm.mentor }}将全程协助您熟悉工作环境和流程。
          </p>
          
          <p>如有任何问题，请随时联系人力资源部。</p>
          
          <p>再次欢迎您的加入，期待与您共创美好未来！</p>
          
          <p>
            此致<br>
            敬礼！<br><br>
            杭州科技职业技术学院<br>
            人力资源部
          </p>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="emailPreviewVisible = false">关闭</el-button>
        <el-button type="primary" @click="sendWelcomeEmail">发送邮件</el-button>
      </template>
    </el-dialog>

    <!-- 已入职人员列表 -->
    <el-card class="completed-card" style="margin-top: 20px">
      <template #header>
        <div class="card-header">
          <span>近期入职人员</span>
          <el-button link type="primary" @click="viewAllOnboarded">
            查看全部
            <el-icon><arrow-right /></el-icon>
          </el-button>
        </div>
      </template>

      <el-table :data="recentOnboarded" stripe>
        <el-table-column prop="name" label="姓名" width="100"  />
        <el-table-column prop="employeeId" label="工号" width="120"  />
        <el-table-column prop="position" label="职位" width="150"  />
        <el-table-column prop="department" label="部门" width="150"  />
        <el-table-column prop="joinDate" label="入职日期" width="120"  />
        <el-table-column prop="mentor" label="导师" width="100"  />
        <el-table-column label="试用期进度" width="150">
          <template #default="{ row }">
            <el-progress 
              :percentage="row.probationProgress" 
              :stroke-width="6"
              :color="getProgressColor(row.probationProgress)"
             />
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="100">
          <template #default="{ row }">
            <el-button link type="primary" @click="viewOnboardingRecord(row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowRight } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { onboardingApi } from '@/api/recruitment'

// 路由
const router = useRouter()

// 数据状态
const loading = ref(false)
const pendingOnboardings = ref<any[]>([])
const recentOnboarded = ref<any[]>([])

// 对话框
const onboardingDialogVisible = ref(false)
const emailPreviewVisible = ref(false)
const currentEmployee = ref<unknown>(null)
const activeTab = ref('documents')

// 表单数据
const collectedDocuments = ref<string[]>([])
const fileList = ref<any[]>([])
const uploadUrl = '/api/recruitment/upload'

// 系统准备表单
const systemForm = reactive({
  employeeId: '',
  email: '',
  permissions: [],
  equipment: []
})

// 入职安排表单
const arrangementForm = reactive({
  reportTime: '',
  reportLocation: '',
  receptionist: '',
  mentor: ''
})

// 欢迎准备表单
const welcomeForm = reactive({
  sendWelcomeEmail: true,
  deskItems: ['欢迎卡片', '公司手册', '办公用品'],
  teamNotification: '',
  activities: ['部门欢迎会'],
  giftPackage: ['公司T恤', '水杯', '员工手册']
})

// 必需材料列表
const requiredDocuments = ref([
  { id: 1, name: 'HrHr身份证复印件', required: true },
  { id: 2, name: '学历证明', required: true },
  { id: 3, name: '离职证明', required: true },
  { id: 4, name: '体检报告', required: true },
  { id: 5, name: '银行卡信息', required: true },
  { id: 6, name: '一寸照片', required: true },
  { id: 7, name: '社保转移单', required: false },
  { id: 8, name: '公积金转移单', required: false }
])

// 材料审核列表
const documentReviewList = ref([
  { name: '身份证复印件', status: '已审核', reviewer: '张三', remark: '信息核实无误' },
  { name: '学历证明', status: '审核中', reviewer: '李四', remark: '' },
  { name: '离职证明', status: '待审核', reviewer: '', remark: '' }
])

// 所有权限列表
const allPermissions = ref([
  { key: 'hr.view', label: '人事查看' },
  { key: 'hr.edit', label: '人事编辑' },
  { key: 'salary.view', label: '薪资查看' },
  { key: 'attendance.view', label: '考勤查看' },
  { key: 'recruitment.view', label: '招聘查看' }
])

// 系统账号列表
const systemAccounts = ref([
  { system: 'OA系统', account: 'zhangsan', status: '已创建' },
  { system: '邮箱系统', account: '<EMAIL>', status: '已创建' },
  { system: '考勤系统', account: '', status: '待创建' },
  { system: 'ERP系统', account: '', status: '待创建' }
])

// HR列表
const hrList = ref([
  { id: 1, name: '王小明' },
  { id: 2, name: '李小红' },
  { id: 3, name: '张小华' }
])

// 导师列表
const mentorList = ref([
  { id: 1, name: '陈经理', position: '技术经理' },
  { id: 2, name: '刘主管', position: '项目主管' },
  { id: 3, name: '赵工', position: '高级工程师' }
])

// 首日安排
const firstDaySchedule = ref([
  {
    time: '09:00',
    title: '报到签到',
    description: '到人力资源部报到，领取入职材料',
    responsible: '王小明'
  },
  {
    time: '09:30',
    title: '公司介绍',
    description: '了解公司历史、文化、组织架构',
    responsible: '李小红'
  },
  {
    time: '10:30',
    title: '部门见面',
    description: '与部门同事见面，了解部门情况',
    responsible: '陈经理'
  },
  {
    time: '14:00',
    title: '系统培训',
    description: '学习使用公司内部系统',
    responsible: '赵工'
  },
  {
    time: '16:00',
    title: '工作交接',
    description: '与导师对接具体工作内容',
    responsible: '陈经理'
  }
])

// 培训计划
const trainingPlan = ref([
  { course: '公司文化与制度', type: '必修', duration: '2小时', deadline: '入职7天内', trainer: '人力资源部' },
  { course: '信息安全培训', type: '必修', duration: '1小时', deadline: '入职7天内', trainer: 'IT部' },
  { course: '产品知识培训', type: '必修', duration: '4小时', deadline: '入职14天内', trainer: '产品部' },
  { course: '技能提升培训', type: '选修', duration: '8小时', deadline: '入职30天内', trainer: '技术部' }
])

// 获取状态类型
const getStatusType = (status: string) => {
  const map: Record<string, string> = {
    '已接受': 'success',
    '待确认': 'warning',
    '已拒绝': 'danger',
    '已过期': 'info'
  }
  return map[status] || ''
}

// 获取进度颜色
const getProgressColor = (percentage: number) => {
  if (percentage < 30) return '#f56c6c'
  if (percentage < 70) return '#e6a23c'
  return '#67c23a'
}

// 获取文档状态类型
const getDocStatusType = (status: string) => {
  const map: Record<string, string> = {
    '已审核': 'success',
    '审核中': 'warning',
    '待审核': 'info',
    '审核不通过': 'danger'
  }
  return map[status] || ''
}

// 获取待入职人员列表
const fetchPendingOnboardings = async () => {
  loading.value = true
  try {
    // 模拟数据
    pendingOnboardings.value = [
      {
        id: '1',
        name: '张三',
        position: '前端工程师',
        department: '技术部',
        offerStatus: '已接受',
        expectedJoinDate: '2025-01-25',
        documentProgress: 60,
        systemProgress: 30,
        overallProgress: 45,
        hr: '王小明',
        mentor: '陈经理'
      },
      {
        id: '2',
        name: '李四',
        position: 'Java工程师',
        department: '技术部',
        offerStatus: '已接受',
        expectedJoinDate: '2025-01-28',
        documentProgress: 100,
        systemProgress: 80,
        overallProgress: 90,
        hr: '李小红',
        mentor: null
      }
    ]
  } catch (__error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 获取近期入职人员
const fetchRecentOnboarded = async () => {
  try {
    // 模拟数据
    recentOnboarded.value = [
      {
        id: '1',
        name: '王五',
        employeeId: 'EMP202501001',
        position: '产品经理',
        department: '产品部',
        joinDate: '2025-01-15',
        mentor: '刘主管',
        probationProgress: 20
      },
      {
        id: '2',
        name: '赵六',
        employeeId: 'EMP202501002',
        position: '测试工程师',
        department: '质量部',
        joinDate: '2025-01-10',
        mentor: '赵工',
        probationProgress: 40
      }
    ]
  } catch (__error) {
    console.error('获取近期入职人员失败:', error)
  }
}

// 办理入职
   
const handleOnboarding = (employee: unknown) => {
  currentEmployee.value = employee
  
  // 初始化表单数据
  systemForm.employeeId = ''
  systemForm.email = ''
  systemForm.permissions = ['hr.view', 'attendance.view']
  systemForm.equipment = ['笔记本电脑', '门禁卡', '工位']
  
  arrangementForm.reportTime = employee.expectedJoinDate + ' 09:00'
  arrangementForm.reportLocation = '总部大厦3楼人力资源部'
  arrangementForm.receptionist = employee.hr
  arrangementForm.mentor = employee.mentor || ''
  
  onboardingDialogVisible.value = true
}

// 查看详情
   
const viewDetail = (employee: unknown) => {
  // 跳转到员工详情页
  router.push({
    name: 'EmployeeDetail',
    params: { id: employee.candidateId || employee.id },
    query: { 
      name: employee.name,
      tab: 'onboarding' // 直接显示入职信息标签页
    }
  })
}

// 生成员工编号
const generateEmployeeId = () => {
  const date = new Date()
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  systemForm.employeeId = `EMP${year}${month}${random}`
}

// 创建邮箱
const createEmail = () => {
  if (!systemForm.employeeId) {
    ElMessage.warning('请先生成员工编号')
    return
  }
  systemForm.email = `${systemForm.employeeId.toLowerCase()}@company.com`
  ElMessage.success('邮箱创建成功')
}

// 创建系统账号
   
const createAccount = (account: unknown) => {
  account.status = '已创建'
  account.account = systemForm.employeeId.toLowerCase()
  ElMessage.success(`${account.system}账号创建成功`)
}

// 预览欢迎邮件
const previewWelcomeEmail = () => {
  emailPreviewVisible.value = true
}

// 发送欢迎邮件
const sendWelcomeEmail = async () => {
  try {
    if (!currentEmployee.value) return
    
    const loading = ElMessage.info({
      message: '正在发送邮件...',
      duration: 0
    })
    
    // 调用API发送邮件
    await onboardingApi.sendWelcomeEmail(currentEmployee.value.employeeId || currentEmployee.value.id, {
      recipientEmail: currentEmployee.value.email || `${systemForm.employeeId}@company.com`,
      subject: '欢迎加入我们的团队！',
      content: '欢迎邮件内容...' // 可以从模板生成
    })
    
    loading.close()
    ElMessage.success('欢迎邮件已发送')
    emailPreviewVisible.value = false
  } catch (__error) {
    console.error('发送邮件失败:', error)
    ElMessage.error('邮件发送失败，请稍后重试')
  }
}

// 文件上传前的钩子
   
const beforeUpload = (file: unknown) => {
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过 10MB!')
  }
  return isLt10M
}

// 处理文件预览
   
const handlePreview = (file: unknown) => {
  console.log('预览文件', file)
}

// 处理文件删除
   
const handleRemove = (file: unknown, fileList: unknown[]) => {
  console.log('删除文件', file, fileList)
}

// 保存进度
const saveProgress = async () => {
  try {
    if (!currentEmployee.value) return
    
    // 计算完成率
    const totalSteps = 4
    let completedSteps = 0
    if (collectedDocuments.value.length > 0) completedSteps++
    if (systemForm.employeeId && systemForm.email) completedSteps++
    if (systemForm.permissions.length > 0) completedSteps++
    if (arrangementForm.workLocation) completedSteps++
    
    const completionRate = Math.round((completedSteps / totalSteps) * 100)
    
    // 调用API保存进度
    await onboardingApi.saveOnboardingProgress(currentEmployee.value.employeeId || currentEmployee.value.id, {
      documentsCollected: collectedDocuments.value,
      systemAccounts: systemAccounts.value,
      permissions: systemForm.permissions,
      equipmentIssued: systemForm.equipment,
      trainingCompleted: arrangementForm.trainingItems || [],
      arrangementInfo: arrangementForm,
      welcomeInfo: welcomeForm,
      currentStep: activeTab.value,
      completionRate
    })
    
    ElMessage.success('进度已保存')
  } catch (__error) {
    console.error('保存进度失败:', error)
    ElMessage.error('保存失败，请稍后重试')
  }
}

// 完成入职
const completeOnboarding = async () => {
  try {
    await ElMessageBox.confirm(
      '确定已完成所有入职手续吗？',
      '确认完成',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 调用API完成入职
    await onboardingApi.completeOnboarding(currentEmployee.value.employeeId || currentEmployee.value.id, {
      completionDate: new Date().toISOString(),
      hrComments: '入职手续办理完成',
      nextSteps: ['试用期跟进', '培训安排', '绩效目标设定']
    })
    
    ElMessage.success('入职办理完成')
    onboardingDialogVisible.value = false
    
    // 刷新列表
    fetchPendingOnboardings()
    fetchRecentOnboarded()
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('完成入职失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

// 查看全部已入职人员
const viewAllOnboarded = () => {
  // 跳转到全部已入职人员列表页面
  router.push({
    name: 'OnboardingHistory',
    query: { 
      status: 'completed',
      dateRange: 'all'
    }
  })
}

// 查看入职记录
   
const viewOnboardingRecord = (employee: unknown) => {
  // 跳转到入职记录详情页
  router.push({
    name: 'OnboardingDetail',
    params: { id: employee.employeeId || employee.id },
    query: { 
      name: employee.name,
      viewMode: 'readonly' // 只读模式查看
    }
  })
}

// 初始化
onMounted(() => {
  fetchPendingOnboardings()
  fetchRecentOnboarded()
})
</script>

<style lang="scss" scoped>
.onboarding-process {
  padding: 20px;
  
  .pending-card,
  .completed-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .employee-info {
    .main-info {
      margin-bottom: 4px;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .sub-info,
    .extra-info {
      font-size: 12px;
      color: #909399;
      margin-top: 2px;
    }
  }
  
  .preparation-info {
    .progress-item {
      margin-bottom: 8px;
      
      span {
        display: inline-block;
        width: 80px;
        font-size: 12px;
        color: #606266;
      }
      
      :deep(.el-progress) {
        display: inline-block;
        width: 100px;
      }
    }
  }
  
  .overall-progress {
    display: flex;
    justify-content: center;
  }
}

// 文档列表
.document-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  
  .el-checkbox {
    margin-right: 0;
  }
}

// 时间轴样式
.schedule-timeline {
  .activity-content {
    p {
      margin: 5px 0;
      color: #909399;
      font-size: 13px;
    }
  }
}

// 邮件预览
.email-preview {
  .email-header {
    background-color: #f5f7fa;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
    
    p {
      margin: 5px 0;
      color: #606266;
    }
  }
  
  .email-content {
    line-height: 1.8;
    color: #606266;
    
    p {
      margin: 15px 0;
    }
    
    ul {
      list-style: none;
      padding-left: 20px;
      
      li {
        position: relative;
        margin-bottom: 8px;
        
        &:before {
          content: '•';
          position: absolute;
          left: -15px;
          color: #409eff;
        }
      }
    }
  }
}

// 穿梭框样式调整
:deep(.el-transfer) {
  .el-transfer-panel {
    width: 250px;
  }
}
</style>