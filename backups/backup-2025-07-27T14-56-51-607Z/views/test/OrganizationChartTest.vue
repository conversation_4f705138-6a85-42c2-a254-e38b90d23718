<template>
  <div class="chart-test">
    <h2>组织架构图测试页面</h2>
    
    <el-card>
      <template #header>
        <span>组织架构可视化测试</span>
      </template>
      
      <OrganizationChart
        :height="600"
        :editable="true"
        @node-click="handleNodeClick"
        @node-double-click="handleNodeDoubleClick"
        @layout-change="handleLayoutChange"
      />
    </el-card>

    <el-card style="margin-top: 20px;">
      <template #header>
        <span>事件日志</span>
      </template>
      
      <div class="event-log">
        <div 
          v-for="(event, index) in eventLog" 
          :key="index"
          class="log-item"
        >
          <span class="log-time">{{ event.time }}</span>
          <span class="log-type">{{ event.type }}</span>
          <span class="log-message">{{ event.message }}</span>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'OrganizationChartTest'
})
 
import { ref } from 'vue'
import HrOrganizationChart from '@/components/organization/HrOrganizationChart.vue'
import type { OrganizationNode } from '@/types/organization'

// 事件日志
const eventLog = ref<Array<{
  time: string
  type: string
  message: string
}>>([])

// 添加日志
const addLog = (type: string, message: string) => {
  eventLog.value.unshift({
    time: new Date().toLocaleTimeString(),
    type,
    message
  })
  
  // 只保留最近20条日志
  if (eventLog.value.length > 20) {
    eventLog.value = eventLog.value.slice(0, 20)
  }
}

// 处理节点点击
const handleNodeClick = (node: OrganizationNode) => {
  addLog('节点点击', `点击了组织: ${node.orgName} (ID: ${node.orgId})`)
}

// 处理节点双击
const handleNodeDoubleClick = (node: OrganizationNode) => {
  addLog('节点双击', `双击了组织: ${node.orgName} (ID: ${node.orgId})`)
}

// 处理布局变化
   
const handleLayoutChange = (layout: unknown) => {
  addLog('布局变化', `布局类型: ${layout.type}, 方向: ${layout.direction || 'N/A'}`)
}

// 初始化日志
addLog('系统', '组织架构图测试页面已加载')
</script>

<style scoped>
.chart-test {
  padding: 20px;
}

.event-log {
  max-height: 300px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.log-time {
  width: 100px;
  color: #909399;
  font-size: 12px;
}

.log-type {
  width: 80px;
  font-weight: bold;
  color: #409eff;
}

.log-message {
  flex: 1;
  color: #606266;
}
</style>
