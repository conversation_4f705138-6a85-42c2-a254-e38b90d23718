<template>
  <div class="travel-record-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon><MapLocation /></el-icon>
        出境记录管理
      </h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增记录
        </el-button>
        <el-button @click="handleImport">
          <el-icon><Upload /></el-icon>
          批量导入
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出记录
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="出境人员">
          <el-input
            v-model="searchForm.travelerName"
            placeholder="请输入出境人员姓名"
            clearable
            style="width: 150px"
            />
        </el-form-item>
        <el-form-item label="目的地">
          <el-input
            v-model="searchForm.destination"
            placeholder="请输入目的地"
            clearable
            style="width: 150px"
            />
        </el-form-item>
        <el-form-item label="出境类型">
          <el-select
            v-model="searchForm.travelType"
            placeholder="请选择出境类型"
            clearable
            style="width: 150px"
          >
            <el-option label="因公出境" value="official"  />
            <el-option label="因私出境" value="personal"  />
            <el-option label="学术交流" value="academic"  />
            <el-option label="培训学习" value="training"  />
          </el-select>
        </el-form-item>
        <el-form-item label="记录状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="已出境" value="departed"  />
            <el-option label="已返回" value="returned"  />
            <el-option label="逾期未归" value="overdue"  />
            <el-option label="延期申请" value="extension"  />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><MapLocation /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.total }}</div>
              <div class="stat-label">总记录数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon departed">
              <el-icon><Position /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.departed }}</div>
              <div class="stat-label">已出境</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon returned">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.returned }}</div>
              <div class="stat-label">已返回</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon overdue">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.overdue }}</div>
              <div class="stat-label">逾期未归</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 出境记录列表 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="recordId" label="记录编号" width="120"  />
        <el-table-column prop="travelerName" label="出境人员" width="100"  />
        <el-table-column prop="employeeId" label="员工工号" width="120"  />
        <el-table-column prop="department" label="所属部门" width="120"  />
        <el-table-column prop="travelType" label="出境类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.travelType)">
              {{ getTypeLabel(row.travelType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="destination" label="目的地" width="120"  />
        <el-table-column prop="departureDate" label="出境日期" width="120"  />
        <el-table-column prop="expectedReturnDate" label="预计返回" width="120"  />
        <el-table-column prop="actualReturnDate" label="实际返回" width="120">
          <template #default="{ row }">
            {{ row.actualReturnDate || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="duration" label="出境天数" width="100">
          <template #default="{ row }">
            {{ row.duration }}天
          </template>
        </el-table-column>
        <el-table-column prop="passportNumber" label="护照号码" width="120"  />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button type="text" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="text" size="small" @click="handleReturn(row)" v-if="row.status === 'departed'">
              登记返回
            </el-button>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
            <el-dropdown @command="(command: unknown) => handleCommand(command, row)">
              <el-button type="text" size="small">
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="extend" v-if="row.status === 'departed'">
                    申请延期
                  </el-dropdown-item>
                  <el-dropdown-item command="timeline">出境时间线</el-dropdown-item>
                  <el-dropdown-item command="documents">相关文件</el-dropdown-item>
                  <el-dropdown-item command="report" v-if="row.status === 'returned'">
                    出境报告
                  </el-dropdown-item>
                  <el-dropdown-item command="print">打印记录</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 出境记录详情/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="900px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="记录编号" prop="recordId">
              <el-input v-model="formData.recordId" :disabled="isView"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出境人员" prop="travelerName">
              <el-select v-model="formData.travelerName" :disabled="isView" style="width: 100%">
                <el-option label="张三" value="张三"  />
                <el-option label="李四" value="李四"  />
                <el-option label="王五" value="王五"  />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="出境类型" prop="travelType">
              <el-select v-model="formData.travelType" :disabled="isView" style="width: 100%">
                <el-option label="因公出境" value="official"  />
                <el-option label="因私出境" value="personal"  />
                <el-option label="学术交流" value="academic"  />
                <el-option label="培训学习" value="training"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="目的地" prop="destination">
              <el-input v-model="formData.destination" :disabled="isView"   />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="出境日期" prop="departureDate">
              <el-date-picker
                v-model="formData.departureDate"
                type="date"
                :disabled="isView"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预计返回日期" prop="expectedReturnDate">
              <el-date-picker
                v-model="formData.expectedReturnDate"
                type="date"
                :disabled="isView"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="出境天数" prop="duration">
              <el-input-number
                v-model="formData.duration"
                :min="1"
                :disabled="isView"
                style="width: 100%"
                />
              <span style="margin-left: 8px;">天</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="护照号码" prop="passportNumber">
              <el-input v-model="formData.passportNumber" :disabled="isView"   />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="签证类型" prop="visaType">
              <el-input v-model="formData.visaType" :disabled="isView"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="签证有效期" prop="visaExpiry">
              <el-date-picker
                v-model="formData.visaExpiry"
                type="date"
                :disabled="isView"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="出境口岸" prop="exitPort">
              <el-input v-model="formData.exitPort" :disabled="isView"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="入境口岸" prop="entryPort">
              <el-input v-model="formData.entryPort" :disabled="isView"   />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="出境目的" prop="purpose">
          <el-input
            v-model="formData.purpose"
            type="textarea"
            :rows="3"
            :disabled="isView"
            placeholder="请输入出境目的"
            />
        </el-form-item>
        <el-form-item label="行程安排" prop="itinerary">
          <el-input
            v-model="formData.itinerary"
            type="textarea"
            :rows="4"
            :disabled="isView"
            placeholder="请输入详细行程安排"
            />
        </el-form-item>
        <el-form-item label="联系方式" prop="contactInfo">
          <el-input
            v-model="formData.contactInfo"
            type="textarea"
            :rows="2"
            :disabled="isView"
            placeholder="请输入境外联系方式"
            />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="formData.notes"
            type="textarea"
            :rows="2"
            :disabled="isView"
            placeholder="请输入备注信息"
            />
        </el-form-item>
      </el-form>
      <template #footer v-if="!isView">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'TravelRecordManagement'
})
 
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  MapLocation,
  Plus,
  Upload,
  Download,
  Search,
  Refresh,
  Position,
  CircleCheck,
  Warning,
  ArrowDown
} from '@element-plus/icons-vue'
import { travelRecordApi } from '@/api/comprehensiveService'

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isView = ref(false)

const searchForm = reactive({
  travelerName: '',
  destination: '',
  travelType: '',
  status: ''
})

const stats = reactive({
  total: 234,
  departed: 45,
  returned: 178,
  overdue: 11
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

const tableData = ref([
  {
    id: '1',
    recordId: '*********',
    travelerName: '张三',
    employeeId: 'EMP001',
    department: '计算机学院',
    travelType: 'academic',
    destination: '美国',
    departureDate: '2024-06-01',
    expectedReturnDate: '2024-06-15',
    actualReturnDate: '2024-06-14',
    duration: 14,
    passportNumber: 'E12345678',
    status: 'returned',
    visaType: 'B1/B2',
    visaExpiry: '2025-06-01',
    exitPort: '上海浦东国际机场',
    entryPort: '洛杉矶国际机场',
    purpose: '参加国际人工智能学术会议',
    itinerary: '第1-3天：参加会议，第4-10天：访问实验室，第11-14天：学术交流',
    contactInfo: '电话：+1-555-123-4567，邮箱：<EMAIL>',
    notes: '学术交流活动，按时返回'
  }
])

const formData = reactive({
  recordId: '',
  travelerName: '',
  travelType: '',
  destination: '',
  departureDate: '',
  expectedReturnDate: '',
  duration: 1,
  passportNumber: '',
  visaType: '',
  visaExpiry: '',
  exitPort: '',
  entryPort: '',
  purpose: '',
  itinerary: '',
  contactInfo: '',
  notes: ''
})

const formRules = {
  recordId: [{ required: true, message: '请输入记录编号', trigger: 'blur' }],
  travelerName: [{ required: true, message: '请选择出境人员', trigger: 'change' }],
  travelType: [{ required: true, message: '请选择出境类型', trigger: 'change' }],
  destination: [{ required: true, message: '请输入目的地', trigger: 'blur' }],
  departureDate: [{ required: true, message: '请选择出境日期', trigger: 'change' }],
  expectedReturnDate: [{ required: true, message: '请选择预计返回日期', trigger: 'change' }]
}

// 方法
const handleSearch = () => {
  console.log('搜索:', searchForm)
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    travelerName: '',
    destination: '',
    travelType: '',
    status: ''
  })
  loadData()
}

const handleAdd = () => {
  dialogTitle.value = '新增出境记录'
  isView.value = false
  resetForm()
  dialogVisible.value = true
}

   
const handleView = (row: unknown) => {
  dialogTitle.value = '查看出境记录'
  isView.value = true
  Object.assign(formData, row)
  dialogVisible.value = true
}

   
const handleEdit = (row: unknown) => {
  dialogTitle.value = '编辑出境记录'
  isView.value = false
  Object.assign(formData, row)
  dialogVisible.value = true
}

   
const handleReturn = (row: unknown) => {
  ElMessageBox.confirm(`确定要登记 ${row.travelerName} 的返回记录吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('返回记录登记成功')
  })
}

   
const handleCommand = (command: string, row: unknown) => {
  switch (command) {
    case 'extend':
      ElMessage.info(`申请延期 ${row.travelerName} 的出境期限`)
      break
    case 'timeline':
      ElMessage.info(`查看 ${row.travelerName} 的出境时间线`)
      break
    case 'documents':
      ElMessage.info(`查看 ${row.travelerName} 的相关文件`)
      break
    case 'report':
      ElMessage.info(`查看 ${row.travelerName} 的出境报告`)
      break
    case 'print':
      ElMessage.info(`打印 ${row.travelerName} 的出境记录`)
      break
    case 'delete':
      ElMessageBox.confirm(`确定要删除 ${row.travelerName} 的出境记录吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ElMessage.success('删除成功')
      })
      break
  }
}

const handleImport = () => {
  ElMessage.info('批量导入功能开发中')
}

const handleExport = () => {
  ElMessage.info('导出记录功能开发中')
}

const handleSubmit = () => {
  ElMessage.success('保存成功')
  dialogVisible.value = false
}

const handleDialogClose = () => {
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    recordId: '',
    travelerName: '',
    travelType: '',
    destination: '',
    departureDate: '',
    expectedReturnDate: '',
    duration: 1,
    passportNumber: '',
    visaType: '',
    visaExpiry: '',
    exitPort: '',
    entryPort: '',
    purpose: '',
    itinerary: '',
    contactInfo: '',
    notes: ''
  })
}

const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.currentPage - 1,
      size: pagination.pageSize,
      employeeId: searchForm.employeeName, // 使用员工名称作为搜索条件
      destination: searchForm.destination,
      status: searchForm.status,
      year: searchForm.year ? parseInt(searchForm.year) : undefined
    }
    
    const response = await travelRecordApi.getPage(params)
    if (response.data.success) {
      tableData.value = response.data.data.content.map(item => ({
        id: item.id,
        recordId: `TR${item.id}`,
        employeeName: item.employeeName,
        employeeId: item.employeeId,
        destination: item.destination,
        travelType: 'official', // 默认因公出境
        purpose: item.purpose,
        departureDate: item.departureDate,
        returnDate: item.returnDate,
        actualReturnDate: item.actualReturnDate,
        duration: item.travelDays,
        status: item.status.toLowerCase(),
        notes: item.notes || '',
        passportNumber: item.passportNumber,
        visaType: '商务签证', // 默认值
        emergencyContact: '紧急联系人'
      }))
      pagination.total = response.data.data.totalElements
      
      // 更新统计数据
      stats.total = response.data.data.totalElements
   
      stats.departed = response.data.data.content.filter((item: unknown) => item.status === 'DEPARTED').length
   
      stats.returned = response.data.data.content.filter((item: unknown) => item.actualReturnDate).length
   
      stats.overdue = response.data.data.content.filter((item: unknown) => 
        !item.actualReturnDate && new Date(item.returnDate) < new Date()
      ).length
    } else {
      throw new Error(response.data.message)
    }
  } catch (__error) {
    console.error('加载出境记录数据失败:', error)
    // 使用模拟数据
    tableData.value = [
      {
        id: '1',
        recordId: '*********',
        employeeName: '张三',
        employeeId: 'EMP001',
        destination: '美国',
        travelType: 'official',
        purpose: '参加国际学术会议',
        departureDate: '2024-06-01',
        returnDate: '2024-06-10',
        actualReturnDate: '2024-06-09',
        duration: 10,
        status: 'returned',
        notes: '按时返回',
        passportNumber: '*********',
        visaType: '商务签证',
        emergencyContact: '李四 13800138001'
      }
    ]
    pagination.total = 234
    ElMessage.warning('数据加载失败，显示默认数据')
  } finally {
    loading.value = false
  }
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadData()
}

const getTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    official: 'primary',
    personal: 'success',
    academic: 'warning',
    training: 'info'
  }
  return colors[type] || ''
}

const getTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    official: '因公出境',
    personal: '因私出境',
    academic: '学术交流',
    training: '培训学习'
  }
  return labels[type] || type
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    departed: 'warning',
    returned: 'success',
    overdue: 'danger',
    extension: 'info'
  }
  return types[status] || ''
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    departed: '已出境',
    returned: '已返回',
    overdue: '逾期未归',
    extension: '延期申请'
  }
  return labels[status] || status
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.travel-record-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.departed {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.returned {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-icon.overdue {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
