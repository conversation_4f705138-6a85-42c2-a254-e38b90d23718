<template>
  <div class="vote-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon><Select /></el-icon>
        投票管理
      </h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增投票
        </el-button>
        <el-button @click="handleBatchPublish">
          <el-icon><Promotion /></el-icon>
          批量发布
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出结果
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="投票标题">
          <el-input
            v-model="searchForm.title"
            placeholder="请输入投票标题"
            clearable
            style="width: 200px"
            />
        </el-form-item>
        <el-form-item label="投票类型">
          <el-select
            v-model="searchForm.voteType"
            placeholder="请选择投票类型"
            clearable
            style="width: 150px"
          >
            <el-option label="单选投票" value="single"  />
            <el-option label="多选投票" value="multiple"  />
            <el-option label="评分投票" value="rating"  />
            <el-option label="排序投票" value="ranking"  />
          </el-select>
        </el-form-item>
        <el-form-item label="投票状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="草稿" value="draft"  />
            <el-option label="进行中" value="active"  />
            <el-option label="已结束" value="ended"  />
            <el-option label="已取消" value="cancelled"  />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
           />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><Select /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.total }}</div>
              <div class="stat-label">总投票数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon active">
              <el-icon><VideoPlay /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.active }}</div>
              <div class="stat-label">进行中</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon participants">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.participants }}</div>
              <div class="stat-label">参与人数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon rate">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.rate }}%</div>
              <div class="stat-label">参与率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 投票列表 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="voteId" label="投票编号" width="120"  />
        <el-table-column prop="title" label="投票标题" min-width="200"  />
        <el-table-column prop="voteType" label="投票类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.voteType)">
              {{ getTypeLabel(row.voteType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="startTime" label="开始时间" width="120"  />
        <el-table-column prop="endTime" label="结束时间" width="120"  />
        <el-table-column prop="participantCount" label="参与人数" width="100"  />
        <el-table-column prop="totalVotes" label="总票数" width="100"  />
        <el-table-column prop="participationRate" label="参与率" width="100">
          <template #default="{ row }">
            {{ row.participationRate }}%
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="creator" label="创建人" width="100"  />
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button type="text" size="small" @click="handleEdit(row)" v-if="row.status === 'draft'">
              编辑
            </el-button>
            <el-button type="text" size="small" @click="handlePublish(row)" v-if="row.status === 'draft'">
              发布
            </el-button>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
            <el-dropdown @command="(command: unknown) => handleCommand(command, row)">
              <el-button type="text" size="small">
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="end" v-if="row.status === 'active'">
                    结束投票
                  </el-dropdown-item>
                  <el-dropdown-item command="results">查看结果</el-dropdown-item>
                  <el-dropdown-item command="statistics">统计分析</el-dropdown-item>
                  <el-dropdown-item command="participants">参与人员</el-dropdown-item>
                  <el-dropdown-item command="copy">复制投票</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 投票详情/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="900px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="投票编号" prop="voteId">
              <el-input v-model="formData.voteId" :disabled="isView"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="投票标题" prop="title">
              <el-input v-model="formData.title" :disabled="isView"   />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="投票类型" prop="voteType">
              <el-select v-model="formData.voteType" :disabled="isView" style="width: 100%">
                <el-option label="单选投票" value="single"  />
                <el-option label="多选投票" value="multiple"  />
                <el-option label="评分投票" value="rating"  />
                <el-option label="排序投票" value="ranking"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="投票范围" prop="scope">
              <el-select v-model="formData.scope" :disabled="isView" style="width: 100%">
                <el-option label="全校" value="all"  />
                <el-option label="指定部门" value="department"  />
                <el-option label="指定人员" value="specific"  />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                v-model="formData.startTime"
                type="datetime"
                :disabled="isView"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                v-model="formData.endTime"
                type="datetime"
                :disabled="isView"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="是否匿名" prop="anonymous">
              <el-switch v-model="formData.anonymous" :disabled="isView"  />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否公开结果" prop="publicResult">
              <el-switch v-model="formData.publicResult" :disabled="isView"  />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="投票描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="4"
            :disabled="isView"
            placeholder="请输入投票描述"
            />
        </el-form-item>
        <el-form-item label="投票选项" prop="options">
          <el-input
            v-model="formData.options"
            type="textarea"
            :rows="4"
            :disabled="isView"
            placeholder="请输入投票选项，每行一个选项"
            />
        </el-form-item>
        <el-form-item label="投票规则" prop="rules">
          <el-input
            v-model="formData.rules"
            type="textarea"
            :rows="3"
            :disabled="isView"
            placeholder="请输入投票规则"
            />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="formData.notes"
            type="textarea"
            :rows="2"
            :disabled="isView"
            placeholder="请输入备注信息"
            />
        </el-form-item>
      </el-form>
      <template #footer v-if="!isView">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'VoteManagement'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Select,
  Plus,
  Promotion,
  Download,
  Search,
  Refresh,
  VideoPlay,
  User,
  TrendCharts,
  ArrowDown
} from '@element-plus/icons-vue'
import { voteActivityApi } from '@/api/comprehensiveService'

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isView = ref(false)
const selectedVotes = ref([])

const searchForm = reactive({
  title: '',
  voteType: '',
  status: '',
  dateRange: []
})

const stats = reactive({
  total: 45,
  active: 12,
  participants: 1256,
  rate: 78.5
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

const tableData = ref([
  {
    id: '1',
    voteId: 'V2024001',
    title: '2024年度优秀教师评选',
    voteType: 'single',
    startTime: '2024-06-01 09:00',
    endTime: '2024-06-15 18:00',
    participantCount: 156,
    totalVotes: 142,
    participationRate: 91.0,
    status: 'active',
    creator: '张主任',
    scope: 'all',
    anonymous: true,
    publicResult: false,
    description: '评选2024年度优秀教师，请投票选择您心目中的优秀教师',
    options: '张三教授\n李四副教授\n王五讲师\n赵六教授',
    rules: '每人只能投一票，投票截止后公布结果',
    notes: '重要评选活动，请认真投票'
  }
])

const formData = reactive({
  voteId: '',
  title: '',
  voteType: '',
  scope: '',
  startTime: '',
  endTime: '',
  anonymous: false,
  publicResult: false,
  description: '',
  options: '',
  rules: '',
  notes: ''
})

const formRules = {
  voteId: [{ required: true, message: '请输入投票编号', trigger: 'blur' }],
  title: [{ required: true, message: '请输入投票标题', trigger: 'blur' }],
  voteType: [{ required: true, message: '请选择投票类型', trigger: 'change' }],
  scope: [{ required: true, message: '请选择投票范围', trigger: 'change' }],
  startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
  endTime: [{ required: true, message: '请选择结束时间', trigger: 'change' }]
}

// 方法
const handleSearch = () => {
  console.log('搜索:', searchForm)
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    title: '',
    voteType: '',
    status: '',
    dateRange: []
  })
  loadData()
}

   
const handleSelectionChange = (selection: unknown[]) => {
  selectedVotes.value = selection as unknown // 临时修复类型不匹配
}

const handleAdd = () => {
  dialogTitle.value = '新增投票'
  isView.value = false
  resetForm()
  dialogVisible.value = true
}

   
const handleView = (row: unknown) => {
  dialogTitle.value = '查看投票'
  isView.value = true
  Object.assign(formData, row)
  dialogVisible.value = true
}

   
const handleEdit = (row: unknown) => {
  dialogTitle.value = '编辑投票'
  isView.value = false
  Object.assign(formData, row)
  dialogVisible.value = true
}

   
const handlePublish = (row: unknown) => {
  ElMessageBox.confirm(`确定要发布投票 ${row.title} 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('投票发布成功')
  })
}

const handleBatchPublish = () => {
  if (selectedVotes.value.length === 0) {
    ElMessage.warning('请先选择要批量发布的投票')
    return
  }
  ElMessage.info('批量发布功能开发中')
}

   
const handleCommand = (command: string, row: unknown) => {
  switch (command) {
    case 'end':
      ElMessageBox.confirm(`确定要结束投票 ${row.title} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ElMessage.success('投票已结束')
      })
      break
    case 'results':
      ElMessage.info(`查看投票 ${row.title} 的结果`)
      break
    case 'statistics':
      ElMessage.info(`查看投票 ${row.title} 的统计分析`)
      break
    case 'participants':
      ElMessage.info(`查看投票 ${row.title} 的参与人员`)
      break
    case 'copy':
      ElMessage.success(`已复制投票 ${row.title}`)
      break
    case 'delete':
      ElMessageBox.confirm(`确定要删除投票 ${row.title} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ElMessage.success('删除成功')
      })
      break
  }
}

const handleExport = () => {
  ElMessage.info('导出结果功能开发中')
}

const handleSubmit = () => {
  ElMessage.success('保存成功')
  dialogVisible.value = false
}

const handleDialogClose = () => {
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    voteId: '',
    title: '',
    voteType: '',
    scope: '',
    startTime: '',
    endTime: '',
    anonymous: false,
    publicResult: false,
    description: '',
    options: '',
    rules: '',
    notes: ''
  })
}

const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.currentPage - 1,
      size: pagination.pageSize,
      status: searchForm.status,
      voteType: searchForm.voteType,
      keyword: searchForm.title
    }
    
    const response = await voteActivityApi.getPage(params)
    if (response.data.success) {
      tableData.value = response.data.data.content.map(item => ({
        id: item.id,
        voteId: item.id,
        title: item.title,
        voteType: item.voteType,
        startTime: item.startTime,
        endTime: item.endTime,
        participantCount: item.participantCount,
        totalVotes: item.totalVotes,
        status: item.status.toLowerCase(),
        createdBy: item.createdBy || '系统管理员',
        description: item.description,
        isAnonymous: item.isAnonymous,
        allowMultiple: item.allowMultiple,
        maxChoices: item.maxChoices,
        targetAudience: item.targetAudience
      }))
      pagination.total = response.data.data.totalElements
      
      // 更新统计数据
      stats.total = response.data.data.totalElements
   
      stats.active = response.data.data.content.filter((item: unknown) => item.status === 'VOTING').length
   
      stats.participants = response.data.data.content.reduce((sum: number, item: unknown) => sum + item.participantCount, 0)
      stats.rate = stats.total > 0 ? ((stats.active / stats.total) * 100).toFixed(1) : '0.0'
    } else {
      throw new Error(response.data.message)
    }
  } catch (__error) {
    console.error('加载投票数据失败:', error)
    // 使用模拟数据
    tableData.value = [
      {
        id: '1',
        voteId: 'V2024001',
        title: '2024年度优秀教师评选',
        voteType: 'single',
        startTime: '2024-06-01 09:00',
        endTime: '2024-06-15 18:00',
        participantCount: 156,
        totalVotes: 156,
        status: 'active',
        createdBy: '张管理员',
        description: '评选本年度优秀教师',
        isAnonymous: true,
        allowMultiple: false,
        maxChoices: 1,
        targetAudience: ['全体员工']
      }
    ]
    pagination.total = 45
    ElMessage.warning('数据加载失败，显示默认数据')
  } finally {
    loading.value = false
  }
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadData()
}

const getTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    single: 'primary',
    multiple: 'success',
    rating: 'warning',
    ranking: 'info'
  }
  return colors[type] || ''
}

const getTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    single: '单选投票',
    multiple: '多选投票',
    rating: '评分投票',
    ranking: '排序投票'
  }
  return labels[type] || type
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    draft: 'info',
    active: 'success',
    ended: 'warning',
    cancelled: 'danger'
  }
  return types[status] || ''
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    draft: '草稿',
    active: '进行中',
    ended: '已结束',
    cancelled: '已取消'
  }
  return labels[status] || status
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.vote-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.active {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-icon.participants {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.rate {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
