<template>
  <div class="comprehensive-service-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><Grid /></el-icon>
        综合服务管理
      </h1>
      <p class="page-description">管理在线投票、出国申请、证照借还和出境记录</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon votes">
              <el-icon><ChatDotSquare /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.activeVotes }}</div>
              <div class="stats-label">进行中投票</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon travel">
              <el-icon><Promotion /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.pendingTravel }}</div>
              <div class="stats-label">待审批出国申请</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon documents">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.borrowedDocuments }}</div>
              <div class="stats-label">借出证照</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon overdue">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.overdueDocuments }}</div>
              <div class="stats-label">逾期未还</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 功能导航 -->
    <el-row :gutter="20" class="function-nav">
      <el-col :span="6">
        <el-card class="function-card" @click="navigateTo('/comprehensive-service/votes')">
          <div class="function-content">
            <div class="function-icon">
              <el-icon><ChatDotSquare /></el-icon>
            </div>
            <div class="function-info">
              <h3>投票管理</h3>
              <p>创建和管理在线投票活动</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="function-card" @click="navigateTo('/comprehensive-service/travel-applications')">
          <div class="function-content">
            <div class="function-icon">
              <el-icon><Promotion /></el-icon>
            </div>
            <div class="function-info">
              <h3>出国申请管理</h3>
              <p>管理出国申请和审批流程</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="function-card" @click="navigateTo('/comprehensive-service/document-loans')">
          <div class="function-content">
            <div class="function-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="function-info">
              <h3>证照借还管理</h3>
              <p>管理证照借用和归还</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="function-card" @click="navigateTo('/comprehensive-service/travel-records')">
          <div class="function-content">
            <div class="function-icon">
              <el-icon><Files /></el-icon>
            </div>
            <div class="function-info">
              <h3>出境记录管理</h3>
              <p>管理出境记录和统计</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作 -->
    <el-card class="quick-actions">
      <template #header>
        <div class="card-header">
          <span>快速操作</span>
        </div>
      </template>
      <el-row :gutter="16">
        <el-col :span="6">
          <el-button type="primary" size="large" @click="createVote" style="width: 100%">
            <el-icon><Plus /></el-icon>
            创建投票
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="success" size="large" @click="applyTravel" style="width: 100%">
            <el-icon><Promotion /></el-icon>
            申请出国
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="warning" size="large" @click="borrowDocument" style="width: 100%">
            <el-icon><Document /></el-icon>
            借用证照
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="info" size="large" @click="viewStatistics" style="width: 100%">
            <el-icon><DataAnalysis /></el-icon>
            查看统计
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 当前投票 -->
    <el-card class="current-votes">
      <template #header>
        <div class="card-header">
          <span>当前投票</span>
          <el-button type="text" @click="viewAllVotes">查看全部</el-button>
        </div>
      </template>
      <el-table :data="currentVotes" style="width: 100%">
        <el-table-column prop="title" label="投票标题" min-width="200"  />
        <el-table-column prop="voteType" label="投票类型" width="120"  />
        <el-table-column prop="startTime" label="开始时间" width="160"  />
        <el-table-column prop="endTime" label="结束时间" width="160"  />
        <el-table-column prop="participantCount" label="参与人数" width="100" align="center"  />
        <el-table-column prop="totalVotes" label="投票数" width="100" align="center"  />
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag
              :type="getVoteStatusType(row.status)"
              size="small"
            >
              {{ getVoteStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="viewVote(row)">
              查看
            </el-button>
            <el-button
              v-if="canVote(row)"
              type="text"
              size="small"
              @click="participateVote(row)"
            >
              投票
            </el-button>
            <el-button
              v-if="canManageVote(row)"
              type="text"
              size="small"
              @click="manageVote(row)"
            >
              管理
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 待办事项 -->
    <el-card class="pending-tasks">
      <template #header>
        <div class="card-header">
          <span>待办事项</span>
          <el-button type="text" @click="viewAllTasks">查看全部</el-button>
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="task-item">
            <div class="item-header">
              <h4>待审批出国申请</h4>
              <span class="count">{{ pendingTasks.travelApprovals }}</span>
            </div>
            <p>需要审批的出国申请</p>
            <el-button type="primary" size="small" @click="viewPendingTravelApprovals">
              立即处理
            </el-button>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="task-item">
            <div class="item-header">
              <h4>逾期证照提醒</h4>
              <span class="count">{{ pendingTasks.overdueDocuments }}</span>
            </div>
            <p>逾期未归还的证照</p>
            <el-button type="warning" size="small" @click="viewOverdueDocuments">
              发送提醒
            </el-button>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="task-item">
            <div class="item-header">
              <h4>投票结果统计</h4>
              <span class="count">{{ pendingTasks.voteResults }}</span>
            </div>
            <p>需要统计结果的投票</p>
            <el-button type="success" size="small" @click="viewVoteResults">
              查看结果
            </el-button>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 最近活动 -->
    <el-card class="recent-activities">
      <template #header>
        <div class="card-header">
          <span>最近活动</span>
          <el-button type="text" @click="viewAllActivities">查看全部</el-button>
        </div>
      </template>
      <el-timeline>
        <el-timeline-item
          v-for="activity in recentActivities"
          :key="activity.id"
          :timestamp="activity.timestamp"
          :type="activity.type"
        >
          <div class="activity-content">
            <h4>{{ activity.title }}</h4>
            <p>{{ activity.description }}</p>
          </div>
        </el-timeline-item>
      </el-timeline>
    </el-card>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'ComprehensiveServiceManagement'
})
 
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Grid,
  ChatDotSquare,
  Promotion,
  Document,
  Warning,
  Files,
  Plus,
  DataAnalysis
} from '@element-plus/icons-vue'
import { VoteStatus, voteActivityApi, travelApplicationApi, documentLoanApi } from '@/api/comprehensiveService'

const router = useRouter()

// 统计数据
const statistics = ref({
  activeVotes: 0,
  pendingTravel: 0,
  borrowedDocuments: 0,
  overdueDocuments: 0
})

// 当前投票
const currentVotes = ref([
  {
    id: '1',
    title: '2024年度优秀教师评选',
    voteType: '单选',
    startTime: '2024-06-20 09:00',
    endTime: '2024-06-30 18:00',
    participantCount: 156,
    totalVotes: 142,
    status: VoteStatus.VOTING
  },
  {
    id: '2',
    title: '食堂菜品满意度调查',
    voteType: '多选',
    startTime: '2024-06-15 08:00',
    endTime: '2024-06-25 20:00',
    participantCount: 89,
    totalVotes: 76,
    status: VoteStatus.VOTING
  }
])

// 待办事项
const pendingTasks = ref({
  travelApprovals: 5,
  overdueDocuments: 3,
  voteResults: 2
})

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    title: '新投票活动发布',
    description: '2024年度优秀教师评选投票已开始',
    timestamp: '2024-06-19 14:30',
    type: 'primary'
  },
  {
    id: 2,
    title: '出国申请审批',
    description: '张三的学术交流申请已通过审批',
    timestamp: '2024-06-19 11:20',
    type: 'success'
  },
  {
    id: 3,
    title: '证照归还提醒',
    description: '李四的护照借用即将到期',
    timestamp: '2024-06-19 09:15',
    type: 'warning'
  }
])

// 页面导航
const navigateTo = (path: string) => {
  router.push(path)
}

// 创建投票
const createVote = () => {
  router.push('/comprehensive-service/votes/create')
}

// 申请出国
const applyTravel = () => {
  router.push('/comprehensive-service/travel-applications/create')
}

// 借用证照
const borrowDocument = () => {
  router.push('/comprehensive-service/document-loans/create')
}

// 查看统计
const viewStatistics = () => {
  router.push('/comprehensive-service/statistics')
}

// 查看所有投票
const viewAllVotes = () => {
  router.push('/comprehensive-service/votes')
}

// 查看投票详情
   
const viewVote = (vote: unknown) => {
  router.push(`/comprehensive-service/votes/${vote.id}`)
}

// 参与投票
   
const participateVote = (vote: unknown) => {
  router.push(`/comprehensive-service/votes/${vote.id}/participate`)
}

// 管理投票
   
const manageVote = (vote: unknown) => {
  router.push(`/comprehensive-service/votes/${vote.id}/manage`)
}

// 查看所有任务
const viewAllTasks = () => {
  router.push('/comprehensive-service/tasks')
}

// 查看待审批出国申请
const viewPendingTravelApprovals = () => {
  router.push('/comprehensive-service/travel-applications?status=SUBMITTED')
}

// 查看逾期证照
const viewOverdueDocuments = () => {
  router.push('/comprehensive-service/document-loans?overdue=true')
}

// 查看投票结果
const viewVoteResults = () => {
  router.push('/comprehensive-service/votes?status=ENDED')
}

// 查看所有活动
const viewAllActivities = () => {
  router.push('/comprehensive-service/activities')
}

// 判断是否可以投票
   
const canVote = (vote: unknown) => {
  return vote.status === VoteStatus.VOTING
}

// 判断是否可以管理投票
   
const canManageVote = (vote: unknown) => {
  return [VoteStatus.DRAFT, VoteStatus.PUBLISHED, VoteStatus.VOTING].includes(vote.status)
}

// 获取投票状态类型
const getVoteStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    DRAFT: 'info',
    PUBLISHED: 'warning',
    VOTING: 'primary',
    ENDED: 'success',
    CANCELLED: 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取投票状态文本
const getVoteStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    DRAFT: '草稿',
    PUBLISHED: '已发布',
    VOTING: '投票中',
    ENDED: '已结束',
    CANCELLED: '已取消'
  }
  return textMap[status] || status
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    // 调用API获取统计数据
    const [votesResponse, travelResponse, overdueResponse] = await Promise.all([
      voteActivityApi.getPage({ page: 0, size: 10, status: 'VOTING' }),
      travelApplicationApi.getPage({ page: 0, size: 10, status: 'SUBMITTED' }),
      documentLoanApi.getOverdue()
    ])
    
    statistics.value = {
      activeVotes: votesResponse.data.success ? votesResponse.data.data.totalElements : 0,
      pendingTravel: travelResponse.data.success ? travelResponse.data.data.totalElements : 0,
      borrowedDocuments: 12, // 需要单独的API来获取所有借用中的文档数量
      overdueDocuments: overdueResponse.data.success ? overdueResponse.data.data.length : 0
    }
    
    // 更新当前投票数据
    if (votesResponse.data.success && votesResponse.data.data.content.length > 0) {
      currentVotes.value = votesResponse.data.data.content.slice(0, 2).map(vote => ({
        id: vote.id,
        title: vote.title,
        voteType: vote.allowMultiple ? '多选' : '单选',
        startTime: vote.startTime,
        endTime: vote.endTime,
        participantCount: vote.participantCount,
        totalVotes: vote.totalVotes,
        status: vote.status
      }))
    }
    
    // 更新待办事项
    pendingTasks.value = {
      travelApprovals: statistics.value.pendingTravel,
      overdueDocuments: statistics.value.overdueDocuments,
      voteResults: 2 // 默认值，需要根据实际业务逻辑调整
    }
  } catch (__error) {
    console.error('加载统计数据失败:', error)
    // 使用默认数据
    statistics.value = {
      activeVotes: 3,
      pendingTravel: 5,
      borrowedDocuments: 12,
      overdueDocuments: 3
    }
    ElMessage.warning('统计数据加载失败，显示默认数据')
  }
}

onMounted(() => {
  loadStatistics()
})
</script>

<style scoped>
.comprehensive-service-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 24px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stats-icon.votes {
  background: linear-gradient(135deg, #409eff, #67c23a);
}

.stats-icon.travel {
  background: linear-gradient(135deg, #e6a23c, #f56c6c);
}

.stats-icon.documents {
  background: linear-gradient(135deg, #67c23a, #409eff);
}

.stats-icon.overdue {
  background: linear-gradient(135deg, #f56c6c, #e6a23c);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.function-nav {
  margin-bottom: 24px;
}

.function-card {
  cursor: pointer;
  transition: all 0.3s ease;
  height: 120px;
}

.function-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.function-content {
  display: flex;
  align-items: center;
  gap: 16px;
  height: 100%;
}

.function-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background: linear-gradient(135deg, #409eff, #67c23a);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.function-info h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.function-info p {
  margin: 0;
  font-size: 14px;
  color: #606266;
}

.quick-actions,
.current-votes,
.pending-tasks,
.recent-activities {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-item {
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  text-align: center;
  transition: all 0.3s ease;
}

.task-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.item-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.count {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
}

.task-item p {
  margin: 0 0 16px 0;
  color: #606266;
  font-size: 14px;
}

.activity-content h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.activity-content p {
  margin: 0;
  color: #606266;
  font-size: 13px;
}
</style>
