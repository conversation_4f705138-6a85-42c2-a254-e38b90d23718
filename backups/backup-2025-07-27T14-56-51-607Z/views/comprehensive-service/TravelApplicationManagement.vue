<template>
  <div class="travel-application-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon><Promotion /></el-icon>
        出国申请管理
      </h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增申请
        </el-button>
        <el-button @click="handleBatchApprove">
          <el-icon><CircleCheck /></el-icon>
          批量审批
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="申请人">
          <el-input
            v-model="searchForm.applicantName"
            placeholder="请输入申请人姓名"
            clearable
            style="width: 150px"
            />
        </el-form-item>
        <el-form-item label="出国类型">
          <el-select
            v-model="searchForm.travelType"
            placeholder="请选择出国类型"
            clearable
            style="width: 150px"
          >
            <el-option label="学术交流" value="academic"  />
            <el-option label="会议参加" value="conference"  />
            <el-option label="培训学习" value="training"  />
            <el-option label="合作研究" value="research"  />
            <el-option label="其他" value="other"  />
          </el-select>
        </el-form-item>
        <el-form-item label="申请状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="待提交" value="draft"  />
            <el-option label="待审批" value="pending"  />
            <el-option label="已批准" value="approved"  />
            <el-option label="已拒绝" value="rejected"  />
            <el-option label="已完成" value="completed"  />
          </el-select>
        </el-form-item>
        <el-form-item label="目的地国家">
          <el-input
            v-model="searchForm.destination"
            placeholder="请输入目的地"
            clearable
            style="width: 150px"
            />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><Promotion /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.total }}</div>
              <div class="stat-label">总申请数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon pending">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.pending }}</div>
              <div class="stat-label">待审批</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon approved">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.approved }}</div>
              <div class="stat-label">已批准</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon rate">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.approvalRate }}%</div>
              <div class="stat-label">批准率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 申请列表 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="applicationId" label="申请编号" width="120"  />
        <el-table-column prop="applicantName" label="申请人" width="100"  />
        <el-table-column prop="employeeId" label="员工工号" width="120"  />
        <el-table-column prop="department" label="所属部门" width="120"  />
        <el-table-column prop="travelType" label="出国类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.travelType)">
              {{ getTypeLabel(row.travelType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="destination" label="目的地" width="120"  />
        <el-table-column prop="startDate" label="出国日期" width="120"  />
        <el-table-column prop="endDate" label="返回日期" width="120"  />
        <el-table-column prop="duration" label="出国天数" width="100">
          <template #default="{ row }">
            {{ row.duration }}天
          </template>
        </el-table-column>
        <el-table-column prop="estimatedCost" label="预计费用" width="120">
          <template #default="{ row }">
            ¥{{ row.estimatedCost?.toLocaleString() || 0 }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button type="text" size="small" @click="handleEdit(row)" v-if="row.status === 'draft'">
              编辑
            </el-button>
            <el-button type="text" size="small" @click="handleApprove(row)" v-if="row.status === 'pending'">
              审批
            </el-button>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
            <el-dropdown @command="(command: unknown) => handleCommand(command, row)">
              <el-button type="text" size="small">
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="submit" v-if="row.status === 'draft'">
                    提交申请
                  </el-dropdown-item>
                  <el-dropdown-item command="documents">申请材料</el-dropdown-item>
                  <el-dropdown-item command="progress">审批进度</el-dropdown-item>
                  <el-dropdown-item command="visa">签证信息</el-dropdown-item>
                  <el-dropdown-item command="report" v-if="row.status === 'completed'">
                    出国报告
                  </el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 申请详情/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="900px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="申请编号" prop="applicationId">
              <el-input v-model="formData.applicationId" :disabled="isView"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请人" prop="applicantName">
              <el-select v-model="formData.applicantName" :disabled="isView" style="width: 100%">
                <el-option label="张三" value="张三"  />
                <el-option label="李四" value="李四"  />
                <el-option label="王五" value="王五"  />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="出国类型" prop="travelType">
              <el-select v-model="formData.travelType" :disabled="isView" style="width: 100%">
                <el-option label="学术交流" value="academic"  />
                <el-option label="会议参加" value="conference"  />
                <el-option label="培训学习" value="training"  />
                <el-option label="合作研究" value="research"  />
                <el-option label="其他" value="other"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="目的地国家" prop="destination">
              <el-input v-model="formData.destination" :disabled="isView"   />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="出国日期" prop="startDate">
              <el-date-picker
                v-model="formData.startDate"
                type="date"
                :disabled="isView"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="返回日期" prop="endDate">
              <el-date-picker
                v-model="formData.endDate"
                type="date"
                :disabled="isView"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="出国天数" prop="duration">
              <el-input-number
                v-model="formData.duration"
                :min="1"
                :disabled="isView"
                style="width: 100%"
                />
              <span style="margin-left: 8px;">天</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预计费用" prop="estimatedCost">
              <el-input-number
                v-model="formData.estimatedCost"
                :min="0"
                :disabled="isView"
                style="width: 100%"
                />
              <span style="margin-left: 8px;">元</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="邀请单位" prop="invitingOrganization">
              <el-input v-model="formData.invitingOrganization" :disabled="isView"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人" prop="contactPerson">
              <el-input v-model="formData.contactPerson" :disabled="isView"   />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="出国目的" prop="purpose">
          <el-input
            v-model="formData.purpose"
            type="textarea"
            :rows="4"
            :disabled="isView"
            placeholder="请输入出国目的"
            />
        </el-form-item>
        <el-form-item label="活动安排" prop="itinerary">
          <el-input
            v-model="formData.itinerary"
            type="textarea"
            :rows="4"
            :disabled="isView"
            placeholder="请输入详细的活动安排"
            />
        </el-form-item>
        <el-form-item label="预期成果" prop="expectedResults">
          <el-input
            v-model="formData.expectedResults"
            type="textarea"
            :rows="3"
            :disabled="isView"
            placeholder="请输入预期成果"
            />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="formData.notes"
            type="textarea"
            :rows="2"
            :disabled="isView"
            placeholder="请输入备注信息"
            />
        </el-form-item>
      </el-form>
      <template #footer v-if="!isView">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'TravelApplicationManagement'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Promotion,
  Plus,
  CircleCheck,
  Download,
  Search,
  Refresh,
  Clock,
  TrendCharts,
  ArrowDown
} from '@element-plus/icons-vue'
import { travelApplicationApi } from '@/api/comprehensiveService'

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isView = ref(false)
const selectedApplications = ref([])

const searchForm = reactive({
  applicantName: '',
  travelType: '',
  status: '',
  destination: ''
})

const stats = reactive({
  total: 89,
  pending: 23,
  approved: 56,
  approvalRate: 78.5
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

const tableData = ref([
  {
    id: '1',
    applicationId: 'TA2024001',
    applicantName: '张三',
    employeeId: 'EMP001',
    department: '计算机学院',
    travelType: 'academic',
    destination: '美国',
    startDate: '2024-08-01',
    endDate: '2024-08-15',
    duration: 14,
    estimatedCost: 25000,
    status: 'pending',
    invitingOrganization: '斯坦福大学',
    contactPerson: 'Prof. Smith',
    purpose: '参加国际人工智能学术会议，进行学术交流',
    itinerary: '第1-3天：参加会议，第4-10天：访问实验室，第11-14天：学术交流',
    expectedResults: '了解最新研究动态，建立合作关系，发表学术论文',
    notes: '重要学术交流活动'
  }
])

const formData = reactive({
  applicationId: '',
  applicantName: '',
  travelType: '',
  destination: '',
  startDate: '',
  endDate: '',
  duration: 1,
  estimatedCost: 0,
  invitingOrganization: '',
  contactPerson: '',
  purpose: '',
  itinerary: '',
  expectedResults: '',
  notes: ''
})

const formRules = {
  applicationId: [{ required: true, message: '请输入申请编号', trigger: 'blur' }],
  applicantName: [{ required: true, message: '请选择申请人', trigger: 'change' }],
  travelType: [{ required: true, message: '请选择出国类型', trigger: 'change' }],
  destination: [{ required: true, message: '请输入目的地国家', trigger: 'blur' }],
  startDate: [{ required: true, message: '请选择出国日期', trigger: 'change' }],
  endDate: [{ required: true, message: '请选择返回日期', trigger: 'change' }]
}

// 方法
const handleSearch = () => {
  console.log('搜索:', searchForm)
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    applicantName: '',
    travelType: '',
    status: '',
    destination: ''
  })
  loadData()
}

   
const handleSelectionChange = (selection: unknown[]) => {
  selectedApplications.value = selection as unknown // 临时修复类型不匹配
}

const handleAdd = () => {
  dialogTitle.value = '新增出国申请'
  isView.value = false
  resetForm()
  dialogVisible.value = true
}

   
const handleView = (row: unknown) => {
  dialogTitle.value = '查看出国申请'
  isView.value = true
  Object.assign(formData, row)
  dialogVisible.value = true
}

   
const handleEdit = (row: unknown) => {
  dialogTitle.value = '编辑出国申请'
  isView.value = false
  Object.assign(formData, row)
  dialogVisible.value = true
}

   
const handleApprove = (row: unknown) => {
  ElMessage.info(`审批 ${row.applicantName} 的出国申请`)
}

const handleBatchApprove = () => {
  if (selectedApplications.value.length === 0) {
    ElMessage.warning('请先选择要批量审批的申请')
    return
  }
  ElMessage.info('批量审批功能开发中')
}

   
const handleCommand = (command: string, row: unknown) => {
  switch (command) {
    case 'submit':
      ElMessage.success(`${row.applicantName} 的出国申请已提交`)
      break
    case 'documents':
      ElMessage.info(`查看 ${row.applicantName} 的申请材料`)
      break
    case 'progress':
      ElMessage.info(`查看 ${row.applicantName} 的审批进度`)
      break
    case 'visa':
      ElMessage.info(`查看 ${row.applicantName} 的签证信息`)
      break
    case 'report':
      ElMessage.info(`查看 ${row.applicantName} 的出国报告`)
      break
    case 'delete':
      ElMessageBox.confirm(`确定要删除 ${row.applicantName} 的出国申请吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ElMessage.success('删除成功')
      })
      break
  }
}

const handleExport = () => {
  ElMessage.info('导出数据功能开发中')
}

const handleSubmit = () => {
  ElMessage.success('保存成功')
  dialogVisible.value = false
}

const handleDialogClose = () => {
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    applicationId: '',
    applicantName: '',
    travelType: '',
    destination: '',
    startDate: '',
    endDate: '',
    duration: 1,
    estimatedCost: 0,
    invitingOrganization: '',
    contactPerson: '',
    purpose: '',
    itinerary: '',
    expectedResults: '',
    notes: ''
  })
}

const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.currentPage - 1,
      size: pagination.pageSize,
      status: searchForm.status,
      travelType: searchForm.travelType,
      applicantName: searchForm.applicantName,
      year: new Date().getFullYear()
    }
    
    const response = await travelApplicationApi.getPage(params)
    if (response.data.success) {
      tableData.value = response.data.data.content.map(item => ({
        id: item.id,
        applicationId: item.applicationCode,
        applicantName: item.applicantName,
        department: item.departmentName,
        travelType: item.travelType,
        destination: item.destination,
        startDate: item.startDate,
        endDate: item.endDate,
        duration: item.duration,
        purpose: item.purpose,
        status: item.status.toLowerCase(),
        cost: item.estimatedCost,
        itinerary: item.itinerary,
        emergencyContact: item.emergencyContact,
        submitTime: item.createTime,
        notes: ''
      }))
      pagination.total = response.data.data.totalElements
      
      // 更新统计数据
      stats.total = response.data.data.totalElements
   
      stats.pending = response.data.data.content.filter((item: unknown) => item.status === 'SUBMITTED').length
   
      stats.approved = response.data.data.content.filter((item: unknown) => item.status === 'APPROVED').length
   
      stats.cost = response.data.data.content.reduce((sum: number, item: unknown) => sum + (item.estimatedCost || 0), 0)
    } else {
      throw new Error(response.data.message)
    }
  } catch (__error) {
    console.error('加载出国申请数据失败:', error)
    // 使用模拟数据
    tableData.value = [
      {
        id: '1',
        applicationId: 'TA2024001',
        applicantName: '张三',
        department: '国际合作处',
        travelType: 'academic',
        destination: '美国',
        startDate: '2024-07-01',
        endDate: '2024-07-10',
        duration: 10,
        purpose: '参加国际学术会议',
        status: 'pending',
        cost: 25000,
        itinerary: '北京-纽约-波士顿-纽约-北京',
        emergencyContact: '李四 13800138001',
        submitTime: '2024-06-01',
        notes: ''
      }
    ]
    pagination.total = 89
    ElMessage.warning('数据加载失败，显示默认数据')
  } finally {
    loading.value = false
  }
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadData()
}

const getTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    academic: 'primary',
    conference: 'success',
    training: 'warning',
    research: 'info',
    other: 'danger'
  }
  return colors[type] || ''
}

const getTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    academic: '学术交流',
    conference: '会议参加',
    training: '培训学习',
    research: '合作研究',
    other: '其他'
  }
  return labels[type] || type
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    draft: 'info',
    pending: 'warning',
    approved: 'success',
    rejected: 'danger',
    completed: 'primary'
  }
  return types[status] || ''
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    draft: '待提交',
    pending: '待审批',
    approved: '已批准',
    rejected: '已拒绝',
    completed: '已完成'
  }
  return labels[status] || status
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.travel-application-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.pending {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.approved {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-icon.rate {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
