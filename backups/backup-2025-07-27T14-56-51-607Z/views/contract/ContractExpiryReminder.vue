<template>
  <div class="contract-expiry-reminder">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>合同到期提醒</h2>
      <p>管理合同到期预警规则和提醒通知，确保合同及时续签</p>
    </div>

    <!-- 提醒规则配置 -->
    <el-card class="config-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>提醒规则配置</h3>
          <el-button type="primary" @click="handleAddRule">
            <el-icon><Plus /></el-icon>
            新增规则
          </el-button>
        </div>
      </template>
      
      <el-table :data="reminderRules" style="width: 100%">
        <el-table-column prop="name" label="规则名称" width="200"  />
        <el-table-column prop="contractType" label="合同类型" width="150"  />
        <el-table-column prop="department" label="适用部门" width="150"  />
        <el-table-column prop="reminderDays" label="提前天数" width="120">
          <template #default="scope">
            <el-tag type="primary">{{ scope.row.reminderDays }}天</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="notificationMethods" label="通知方式" width="200">
          <template #default="scope">
            <el-tag 
              v-for="method in scope.row.notificationMethods" 
              :key="method"
              size="small"
              style="margin-right: 5px;"
            >
              {{ getMethodLabel(method) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="isEnabled" label="状态" width="100">
          <template #default="scope">
            <el-switch 
              v-model="scope.row.isEnabled" 
              @change="handleToggleRule(scope.row)"
             />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="handleEditRule(scope.row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button size="small" type="danger" @click="handleDeleteRule(scope.row)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 功能标签页 -->
    <el-tabs v-model="activeTab" class="function-tabs">
      <!-- 到期提醒 -->
      <el-tab-pane label="到期提醒" name="reminders">
        <el-card shadow="never">
          <div class="tab-header">
            <span class="tab-title">即将到期合同提醒</span>
            <div class="tab-actions">
              <el-button type="primary" @click="handleRefreshReminders">
                <el-icon><Refresh /></el-icon>
                刷新提醒
              </el-button>
              <el-button type="success" @click="handleSendBatchReminders">
                <el-icon><Message /></el-icon>
                批量发送
              </el-button>
            </div>
          </div>

          <!-- 提醒统计 -->
          <el-row :gutter="20" class="reminder-stats">
            <el-col :span="6">
              <el-card class="stats-card">
                <div class="stats-content">
                  <div class="stats-icon urgent">
                    <el-icon><Warning /></el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ reminderStats.urgent }}</div>
                    <div class="stats-label">30天内到期</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stats-card">
                <div class="stats-content">
                  <div class="stats-icon warning">
                    <el-icon><Clock /></el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ reminderStats.warning }}</div>
                    <div class="stats-label">90天内到期</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stats-card">
                <div class="stats-content">
                  <div class="stats-icon info">
                    <el-icon><InfoFilled /></el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ reminderStats.info }}</div>
                    <div class="stats-label">180天内到期</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stats-card">
                <div class="stats-content">
                  <div class="stats-icon total">
                    <el-icon><Document /></el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ reminderStats.total }}</div>
                    <div class="stats-label">提醒总数</div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 筛选条件 -->
          <div class="filter-form">
            <el-row :gutter="20">
              <el-col :span="4">
                <el-select v-model="reminderFilter.urgencyLevel" placeholder="紧急程度" clearable>
                  <el-option label="紧急（30天内）" value="URGENT"  />
                  <el-option label="警告（90天内）" value="WARNING"  />
                  <el-option label="提醒（180天内）" value="INFO"  />
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-select v-model="reminderFilter.contractType" placeholder="合同类型" clearable>
                  <el-option label="事业编制聘用合同" value="CAREER_ESTABLISHMENT"  />
                  <el-option label="人事代理合同" value="PERSONNEL_AGENCY"  />
                  <el-option label="劳务协议" value="LABOR_AGREEMENT"  />
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-select v-model="reminderFilter.department" placeholder="部门" clearable>
                  <el-option label="全部部门" value=""  />
                  <el-option
                    v-for="dept in departmentOptions"
                    :key="dept.id"
                    :label="dept.name"
                    :value="dept.id"
                   />
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-select v-model="reminderFilter.reminderStatus" placeholder="提醒状态" clearable>
                  <el-option label="未发送" value="NOT_SENT"  />
                  <el-option label="已发送" value="SENT"  />
                  <el-option label="已确认" value="CONFIRMED"  />
                  <el-option label="已忽略" value="IGNORED"  />
                </el-select>
              </el-col>
              <el-col :span="8">
                <el-button type="primary" @click="handleFilterReminders">
                  <el-icon><Search /></el-icon>
                  筛选
                </el-button>
                <el-button @click="handleResetFilter">
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
              </el-col>
            </el-row>
          </div>

          <!-- 提醒列表 -->
          <el-table :data="reminderList" style="width: 100%" @selection-change="handleReminderSelectionChange">
            <el-table-column type="selection" width="55"  />
            <el-table-column prop="employeeName" label="教职工" width="100"  />
            <el-table-column prop="employeeNumber" label="工号" width="100"  />
            <el-table-column prop="department" label="部门" width="150" show-overflow-tooltip  />
            <el-table-column prop="contractNumber" label="合同编号" width="120"  />
            <el-table-column prop="contractType" label="合同类型" width="150" show-overflow-tooltip  />
            <el-table-column prop="endDate" label="到期日期" width="100"  />
            <el-table-column prop="remainingDays" label="剩余天数" width="100">
              <template #default="scope">
                <el-tag :type="getRemainingDaysTag(scope.row.remainingDays)" size="small">
                  {{ scope.row.remainingDays }}天
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="urgencyLevel" label="紧急程度" width="100">
              <template #default="scope">
                <el-tag :type="getUrgencyLevelTag(scope.row.urgencyLevel)" size="small">
                  {{ scope.row.urgencyLevelName }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="reminderStatus" label="提醒状态" width="100">
              <template #default="scope">
                <el-tag :type="getReminderStatusTag(scope.row.reminderStatus)" size="small">
                  {{ scope.row.reminderStatusName }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="lastReminderTime" label="最后提醒时间" width="150"  />
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button size="small" type="primary" link @click="handleSendReminder(scope.row)">
                  发送提醒
                </el-button>
                <el-button size="small" type="success" link @click="handleRenewalProcess(scope.row)">
                  办理续签
                </el-button>
                <el-button size="small" type="info" link @click="handleIgnoreReminder(scope.row)">
                  忽略提醒
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <!-- 提醒规则配置 -->
      <el-tab-pane label="提醒规则配置" name="rules">
        <el-card shadow="never">
          <div class="tab-header">
            <span class="tab-title">提醒规则配置</span>
            <el-button type="success" @click="handleAddRule">
              <el-icon><Plus /></el-icon>
              新增规则
            </el-button>
          </div>

          <!-- 规则列表 -->
          <el-table :data="rulesList" style="width: 100%">
            <el-table-column prop="ruleName" label="规则名称" width="200"  />
            <el-table-column prop="contractType" label="适用合同类型" width="180">
              <template #default="scope">
                <el-tag size="small">{{ getContractTypeText(scope.row.contractType) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="reminderDaysBefore" label="提前提醒天数" width="120"  />
            <el-table-column prop="reminderFrequency" label="提醒频率" width="120">
              <template #default="scope">
                <el-tag :type="getFrequencyTag(scope.row.reminderFrequency)" size="small">
                  {{ getFrequencyText(scope.row.reminderFrequency) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="reminderChannels" label="提醒渠道" width="150">
              <template #default="scope">
                <el-tag 
                  v-for="channel in scope.row.reminderChannels" 
                  :key="channel" 
                  size="small" 
                  style="margin-right: 4px;"
                >
                  {{ getChannelText(channel) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="isEnabled" label="状态" width="100">
              <template #default="scope">
                <el-switch
                  v-model="scope.row.isEnabled"
                  @change="handleToggleRule(scope.row)"
                 />
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="150"  />
            <el-table-column label="操作" width="150">
              <template #default="scope">
                <el-button size="small" type="primary" link @click="handleEditRule(scope.row)">
                  编辑
                </el-button>
                <el-button size="small" type="danger" link @click="handleDeleteRule(scope.row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <!-- 提醒记录 -->
      <el-tab-pane label="提醒记录" name="history">
        <el-card shadow="never">
          <div class="tab-header">
            <span class="tab-title">提醒发送记录</span>
            <div class="tab-actions">
              <el-button @click="handleExportHistory">
                <el-icon><Download /></el-icon>
                导出记录
              </el-button>
            </div>
          </div>

          <!-- 记录筛选 -->
          <div class="filter-form">
            <el-row :gutter="20">
              <el-col :span="5">
                <el-input
                  v-model="historyFilter.keyword"
                  placeholder="搜索教职工姓名、合同编号"
                  clearable
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </el-col>
              <el-col :span="4">
                <el-select v-model="historyFilter.reminderType" placeholder="提醒类型" clearable>
                  <el-option label="自动提醒" value="AUTO"  />
                  <el-option label="手动提醒" value="MANUAL"  />
                  <el-option label="批量提醒" value="BATCH"  />
                </el-select>
              </el-col>
              <el-col :span="6">
                <el-date-picker
                  v-model="historyFilter.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                 />
              </el-col>
              <el-col :span="9">
                <el-button type="primary" @click="handleFilterHistory">
                  <el-icon><Search /></el-icon>
                  搜索
                </el-button>
                <el-button @click="handleResetHistoryFilter">
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
              </el-col>
            </el-row>
          </div>

          <!-- 记录列表 -->
          <el-table :data="historyList" style="width: 100%">
            <el-table-column prop="employeeName" label="教职工" width="100"  />
            <el-table-column prop="contractNumber" label="合同编号" width="120"  />
            <el-table-column prop="reminderType" label="提醒类型" width="100">
              <template #default="scope">
                <el-tag :type="getReminderTypeTag(scope.row.reminderType)" size="small">
                  {{ getReminderTypeText(scope.row.reminderType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="reminderChannel" label="提醒渠道" width="100"  />
            <el-table-column prop="reminderContent" label="提醒内容" show-overflow-tooltip  />
            <el-table-column prop="sendTime" label="发送时间" width="150"  />
            <el-table-column prop="sendStatus" label="发送状态" width="100">
              <template #default="scope">
                <el-tag :type="getSendStatusTag(scope.row.sendStatus)" size="small">
                  {{ scope.row.sendStatusName }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="readTime" label="阅读时间" width="150"  />
            <el-table-column label="操作" width="120">
              <template #default="scope">
                <el-button size="small" type="primary" link @click="handleViewHistoryDetail(scope.row)">
                  查看详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <!-- 提醒规则配置对话框 -->
    <el-dialog 
      v-model="ruleDialogVisible" 
      :title="currentRule.id ? '编辑提醒规则' : '新增提醒规则'"
      width="600px"
    >
      <el-form ref="ruleFormRef" :model="currentRule" label-width="100px">
        <el-form-item label="规则名称" prop="name">
          <el-input v-model="currentRule.name" placeholder="请输入规则名称"   />
        </el-form-item>
        <el-form-item label="合同类型" prop="contractType">
          <el-select v-model="currentRule.contractType" placeholder="请选择合同类型">
            <el-option label="全部类型" value=""  />
            <el-option label="事业编制聘用合同" value="career"  />
            <el-option label="劳务协议" value="labor"  />
            <el-option label="劳务派遣合同" value="dispatch"  />
            <el-option label="兼职协议" value="parttime"  />
          </el-select>
        </el-form-item>
        <el-form-item label="适用部门" prop="department">
          <el-select v-model="currentRule.department" placeholder="请选择部门">
            <el-option label="全部部门" value=""  />
            <el-option
              v-for="dept in departmentOptions"
              :key="dept.id"
              :label="dept.name"
              :value="dept.id"
             />
          </el-select>
        </el-form-item>
        <el-form-item label="提前天数" prop="reminderDays">
          <el-input-number 
            v-model="currentRule.reminderDays" 
            :min="1" 
            :max="365"
            controls-position="right"
            style="width: 150px;"
            />
          <span style="margin-left: 10px; color: #909399;">天</span>
        </el-form-item>
        <el-form-item label="通知方式" prop="notificationMethods">
          <el-checkbox-group v-model="currentRule.notificationMethods">
            <el-checkbox value="system">系统通知</el-checkbox>
            <el-checkbox value="email">邮件通知</el-checkbox>
            <el-checkbox value="sms">短信通知</el-checkbox>
            <el-checkbox value="wechat">企业微信</el-checkbox>
            <el-checkbox value="dingtalk">钉钉</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="通知频率" prop="frequency">
          <el-radio-group v-model="currentRule.frequency">
            <el-radio value="once">仅一次</el-radio>
            <el-radio value="daily">每日</el-radio>
            <el-radio value="weekly">每周</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="启用状态" prop="isEnabled">
          <el-switch v-model="currentRule.isEnabled"  />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="ruleDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveRule">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  Message,
  Warning,
  Clock,
  InfoFilled,
  Document,
  Search,
  Plus,
  Download,
  Edit,
  Delete
} from '@element-plus/icons-vue'
import { organizationApi } from '@/api/organization'

// 组件引入（需要创建）
// import ReminderRuleDialog from './components/ReminderRuleDialog.vue'
// import BatchReminderDialog from './components/BatchReminderDialog.vue'

// 响应式数据
const activeTab = ref('reminders')
const selectedReminders = ref([])
const ruleDialogVisible = ref(false)
const ruleFormRef = ref()

// 提醒统计数据
const reminderStats = reactive({
  urgent: 12,
  warning: 28,
  info: 45,
  total: 85
})

// 提醒规则数据
const reminderRules = ref([
  {
    id: '1',
    name: 'HrHr事业编制合同提醒',
    contractType: 'career',
    department: '',
    reminderDays: 30,
    notificationMethods: ['system', 'email'],
    frequency: 'once',
    isEnabled: true
  },
  {
    id: '2',
    name: '劳务协议紧急提醒',
    contractType: 'labor',
    department: '',
    reminderDays: 7,
    notificationMethods: ['system', 'email', 'sms'],
    frequency: 'daily',
    isEnabled: true
  },
  {
    id: '3',
    name: '兼职协议提前通知',
    contractType: 'parttime',
    department: '',
    reminderDays: 15,
    notificationMethods: ['system'],
    frequency: 'weekly',
    isEnabled: false
  }
])

// 当前编辑的规则
const currentRule = ref({
  id: '',
  name: '',
  contractType: '',
  department: '',
  reminderDays: 30,
  notificationMethods: ['system'],
  frequency: 'once',
  isEnabled: true
})

// 筛选条件
const reminderFilter = reactive({
  urgencyLevel: '',
  contractType: '',
  department: '',
  reminderStatus: ''
})

const historyFilter = reactive({
  keyword: '',
  reminderType: '',
  dateRange: []
})

// 提醒列表
const reminderList = ref([
  {
    id: '1',
    employeeName: '张三',
    employeeNumber: 'EMP001',
    department: '计算机学院',
    contractNumber: 'HKY2025001',
    contractType: 'CAREER_ESTABLISHMENT',
    endDate: '2025-07-19',
    remainingDays: 30,
    urgencyLevel: 'URGENT',
    urgencyLevelName: '紧急',
    reminderStatus: 'NOT_SENT',
    reminderStatusName: '未发送',
    lastReminderTime: ''
  },
  {
    id: '2',
    employeeName: '李四',
    employeeNumber: 'EMP002',
    department: '机械工程学院',
    contractNumber: 'HKY2025002',
    contractType: 'PERSONNEL_AGENCY',
    endDate: '2025-09-15',
    remainingDays: 88,
    urgencyLevel: 'WARNING',
    urgencyLevelName: '警告',
    reminderStatus: 'SENT',
    reminderStatusName: '已发送',
    lastReminderTime: '2025-06-15 10:30:00'
  }
])

// 规则列表
const rulesList = ref([
  {
    id: '1',
    ruleName: '事业编制合同到期提醒',
    contractType: 'CAREER_ESTABLISHMENT',
    reminderDaysBefore: 90,
    reminderFrequency: 'WEEKLY',
    reminderChannels: ['EMAIL', 'SMS', 'SYSTEM'],
    isEnabled: true,
    createTime: '2025-01-01 10:00:00'
  },
  {
    id: '2',
    ruleName: '劳务协议到期提醒',
    contractType: 'LABOR_AGREEMENT',
    reminderDaysBefore: 30,
    reminderFrequency: 'DAILY',
    reminderChannels: ['EMAIL', 'SYSTEM'],
    isEnabled: true,
    createTime: '2025-01-01 10:00:00'
  }
])

// 历史记录列表
const historyList = ref([
  {
    id: '1',
    employeeName: '张三',
    contractNumber: 'HKY2025001',
    reminderType: 'AUTO',
    reminderChannel: '邮件',
    reminderContent: '您的合同将于30天后到期，请及时办理续签手续',
    sendTime: '2025-06-19 09:00:00',
    sendStatus: 'SUCCESS',
    sendStatusName: '发送成功',
    readTime: '2025-06-19 09:15:00'
  }
])

// 对话框相关
const batchReminderDialogVisible = ref(false)
const ruleDialogMode = ref<'view' | 'add' | 'edit'>('add')

// 部门列表
const departmentOptions = ref<any[]>([])

// 刷新提醒
const handleRefreshReminders = () => {
  ElMessage.success('提醒数据已刷新')
}

// 批量发送提醒
const handleSendBatchReminders = () => {
  if (selectedReminders.value.length === 0) {
    ElMessage.warning('请先选择要发送提醒的记录')
    return
  }
  batchReminderDialogVisible.value = true
}

// 筛选提醒
const handleFilterReminders = () => {
  ElMessage.info('筛选提醒功能开发中...')
}

// 重置筛选
const handleResetFilter = () => {
  Object.assign(reminderFilter, {
    urgencyLevel: '',
    contractType: '',
    department: '',
    reminderStatus: ''
  })
}

// 提醒选择变化
   
const handleReminderSelectionChange = (selection: unknown[]) => {
  selectedReminders.value = selection as unknown // 修复类型：never[] → any[]
}

// 发送单个提醒
   
const handleSendReminder = (reminder: unknown) => {
  ElMessage.success(`已向 ${reminder.employeeName} 发送合同到期提醒`)
}

// 办理续签
   
const handleRenewalProcess = (reminder: unknown) => {
  ElMessage.info(`开始为 ${reminder.employeeName} 办理合同续签`)
}

// 忽略提醒
   
const handleIgnoreReminder = (reminder: unknown) => {
  ElMessage.info(`已忽略 ${reminder.employeeName} 的合同到期提醒`)
}

// 获取通知方式标签
const getMethodLabel = (method: string) => {
  const labels = {
    system: '系统',
    email: '邮件',
    sms: '短信',
    wechat: '企业微信',
    dingtalk: '钉钉'
  }
  return labels[method] || method
}

// 新增规则
const handleAddRule = () => {
  currentRule.value = {
    id: '',
    name: '',
    contractType: '',
    department: '',
    reminderDays: 30,
    notificationMethods: ['system'],
    frequency: 'once',
    isEnabled: true
  }
  ruleDialogVisible.value = true
}

// 编辑规则
   
const handleEditRule = (rule: unknown) => {
  currentRule.value = { ...rule }
  ruleDialogVisible.value = true
}

// 保存规则
const handleSaveRule = () => {
  if (currentRule.value.id) {
    // 编辑
    const index = reminderRules.value.findIndex(r => r.id === currentRule.value.id)
    if (index > -1) {
      reminderRules.value[index] = { ...currentRule.value }
      ElMessage.success('更新成功')
    }
  } else {
    // 新增
    currentRule.value.id = Date.now().toString()
    reminderRules.value.push({ ...currentRule.value })
    ElMessage.success('添加成功')
  }
  ruleDialogVisible.value = false
}

// 删除规则
   
const handleDeleteRule = async (rule: unknown) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除规则 "${rule.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = reminderRules.value.findIndex(r => r.id === rule.id)
    if (index > -1) {
      reminderRules.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 切换规则状态
   
const handleToggleRule = (rule: unknown) => {
  const status = rule.isEnabled ? '启用' : '禁用'
  ElMessage.success(`规则 "${rule.ruleName}" 已${status}`)
}

// 导出历史记录
const handleExportHistory = () => {
  ElMessage.info('导出历史记录功能开发中...')
}

// 筛选历史记录
const handleFilterHistory = () => {
  ElMessage.info('筛选历史记录功能开发中...')
}

// 重置历史筛选
const handleResetHistoryFilter = () => {
  Object.assign(historyFilter, {
    keyword: '',
    reminderType: '',
    dateRange: []
  })
}

// 查看历史详情
   
const handleViewHistoryDetail = (history: unknown) => {
  ElMessage.info(`查看提醒记录详情：${history.reminderContent}`)
}

// 对话框成功回调
const handleRuleSuccess = () => {
  ElMessage.success('操作成功')
}

const handleBatchReminderSuccess = () => {
  ElMessage.success('批量发送提醒成功')
}

// 获取剩余天数标签
const getRemainingDaysTag = (days: number) => {
  if (days <= 30) return 'danger'
  if (days <= 90) return 'warning'
  return 'info'
}

// 获取紧急程度标签
const getUrgencyLevelTag = (level: string) => {
  switch (level) {
    case 'URGENT': return 'danger'
    case 'WARNING': return 'warning'
    case 'INFO': return 'info'
    default: return ''
  }
}

// 获取提醒状态标签
const getReminderStatusTag = (status: string) => {
  switch (status) {
    case 'NOT_SENT': return 'info'
    case 'SENT': return 'success'
    case 'CONFIRMED': return 'primary'
    case 'IGNORED': return 'warning'
    default: return ''
  }
}

// 获取合同类型文本
const getContractTypeText = (type: string) => {
  switch (type) {
    case 'CAREER_ESTABLISHMENT': return '事业编制聘用合同'
    case 'PERSONNEL_AGENCY': return '人事代理合同'
    case 'LABOR_AGREEMENT': return '劳务协议'
    default: return type
  }
}

// 获取频率标签
const getFrequencyTag = (frequency: string) => {
  switch (frequency) {
    case 'DAILY': return 'danger'
    case 'WEEKLY': return 'warning'
    case 'MONTHLY': return 'primary'
    default: return ''
  }
}

// 获取频率文本
const getFrequencyText = (frequency: string) => {
  switch (frequency) {
    case 'DAILY': return '每日'
    case 'WEEKLY': return '每周'
    case 'MONTHLY': return '每月'
    default: return frequency
  }
}

// 获取渠道文本
const getChannelText = (channel: string) => {
  switch (channel) {
    case 'EMAIL': return '邮件'
    case 'SMS': return '短信'
    case 'SYSTEM': return '系统'
    case 'WECHAT': return '微信'
    default: return channel
  }
}

// 获取提醒类型标签
const getReminderTypeTag = (type: string) => {
  switch (type) {
    case 'AUTO': return 'primary'
    case 'MANUAL': return 'success'
    case 'BATCH': return 'warning'
    default: return ''
  }
}

// 获取提醒类型文本
const getReminderTypeText = (type: string) => {
  switch (type) {
    case 'AUTO': return '自动提醒'
    case 'MANUAL': return '手动提醒'
    case 'BATCH': return '批量提醒'
    default: return type
  }
}

// 获取发送状态标签
const getSendStatusTag = (status: string) => {
  switch (status) {
    case 'SUCCESS': return 'success'
    case 'FAILED': return 'danger'
    case 'PENDING': return 'warning'
    default: return ''
  }
}

// 获取部门列表
const fetchDepartments = async () => {
  try {
    const response = await organizationApi.getTree()
    if (response && Array.isArray(response)) {
      departmentOptions.value = response.map(dept => ({
        id: dept.id,
        name: dept.name,
        orgCode: dept.orgCode
      }))
    }
  } catch (__error) {
    console.error('获取部门列表失败:', error)
    ElMessage.error('获取部门列表失败')
  }
}

// 初始化
onMounted(() => {
  // 初始化数据
  fetchDepartments()
})
</script>

<style scoped>
.contract-expiry-reminder {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.function-tabs {
  margin-top: 20px;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.tab-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.tab-actions {
  display: flex;
  gap: 8px;
}

.reminder-stats {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stats-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 20px;
  color: white;
}

.stats-icon.urgent {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.stats-icon.warning {
  background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
}

.stats-icon.info {
  background: linear-gradient(135deg, #48dbfb 0%, #0abde3 100%);
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 12px;
  color: #909399;
}

.filter-form {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.config-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.dialog-footer {
  text-align: right;
}
</style>
