<template>
  <div class="contract-renewal-flow">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>合同续签流程</h2>
      <p>管理合同续签全流程，包括续签申请、审批、签署和归档</p>
    </div>

    <!-- 续签申请列表 -->
    <el-card class="renewal-list" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>续签申请列表</h3>
          <div class="header-actions">
            <el-button @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="primary" @click="handleStartRenewal">
              <el-icon><Plus /></el-icon>
              发起续签
            </el-button>
          </div>
        </div>
      </template>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-form :inline="true" :model="filterForm" size="small">
          <el-form-item label="员工姓名">
            <el-input 
              v-model="filterForm.employeeName" 
              placeholder="请输入员工姓名"
              clearable
              />
          </el-form-item>
          <el-form-item label="续签状态">
            <el-select v-model="filterForm.renewalStatus" placeholder="请选择状态" clearable>
              <el-option label="待申请" value="pending"  />
              <el-option label="审批中" value="approving"  />
              <el-option label="已通过" value="approved"  />
              <el-option label="已拒绝" value="rejected"  />
              <el-option label="已签署" value="signed"  />
            </el-select>
          </el-form-item>
          <el-form-item label="到期时间">
            <el-date-picker
              v-model="filterForm.expiryDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
             />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleFilter">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="handleResetFilter">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 续签统计 -->
      <div class="renewal-stats">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-card urgent">
              <div class="stat-icon">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ renewalStats.urgent }}</div>
                <div class="stat-label">紧急续签</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card pending">
              <div class="stat-icon">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ renewalStats.pending }}</div>
                <div class="stat-label">待处理</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card processing">
              <div class="stat-icon">
                <el-icon><Loading /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ renewalStats.processing }}</div>
                <div class="stat-label">处理中</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card completed">
              <div class="stat-icon">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ renewalStats.completed }}</div>
                <div class="stat-label">已完成</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 续签表格 -->
      <el-table :data="renewalList" style="width: 100%">
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="employeeName" label="员工姓名" width="100"  />
        <el-table-column prop="contractNumber" label="合同编号" width="150"  />
        <el-table-column prop="currentExpiryDate" label="当前到期日期" width="120"  />
        <el-table-column prop="renewalTerm" label="续签期限" width="100"  />
        <el-table-column prop="renewalStatus" label="续签状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.renewalStatus)" size="small">
              {{ getStatusText(scope.row.renewalStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="applicant" label="申请人" width="100"  />
        <el-table-column prop="applicationDate" label="申请时间" width="150"  />
        <el-table-column prop="urgency" label="紧急程度" width="100">
          <template #default="scope">
            <el-tag 
              :type="scope.row.urgency === 'urgent' ? 'danger' : scope.row.urgency === 'important' ? 'warning' : 'info'"
              size="small"
            >
              {{ scope.row.urgency === 'urgent' ? '紧急' : scope.row.urgency === 'important' ? '重要' : '一般' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <el-button size="small" @click="handleViewRenewal(scope.row)">
              查看
            </el-button>
            <el-button 
              size="small" 
              type="primary" 
              @click="handleProcessRenewal(scope.row)"
              v-if="scope.row.renewalStatus === 'pending'"
            >
              处理
            </el-button>
            <el-button 
              size="small" 
              type="success" 
              @click="handleApproveRenewal(scope.row)"
              v-if="scope.row.renewalStatus === 'approving'"
            >
              审批
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
    </el-card>

    <!-- 续签申请对话框 -->
    <el-dialog v-model="renewalDialogVisible" title="合同续签申请" width="800px">
      <div class="renewal-form">
        <el-form ref="renewalFormRef" :model="renewalForm" :rules="renewalRules" label-width="120px">
          <el-tabs v-model="activeRenewalTab" type="card">
            <!-- 基本信息 -->
            <el-tab-pane label="基本信息" name="basic">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="员工姓名" prop="employeeName">
                    <el-input v-model="renewalForm.employeeName" placeholder="请输入员工姓名"   />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="原合同编号" prop="originalContractNumber">
                    <el-input v-model="renewalForm.originalContractNumber" placeholder="请输入原合同编号"   />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="当前到期日期" prop="currentExpiryDate">
                    <el-date-picker
                      v-model="renewalForm.currentExpiryDate"
                      type="date"
                      placeholder="选择到期日期"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                     />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="续签期限" prop="renewalTerm">
                    <el-input-number
                      v-model="renewalForm.renewalTerm"
                      :min="1"
                      :max="10"
                      controls-position="right"
                      />
                    <span style="margin-left: 10px;">年</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="新合同生效日期" prop="newEffectiveDate">
                    <el-date-picker
                      v-model="renewalForm.newEffectiveDate"
                      type="date"
                      placeholder="选择生效日期"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                     />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="紧急程度" prop="urgency">
                    <el-select v-model="renewalForm.urgency" placeholder="请选择紧急程度">
                      <el-option label="一般" value="normal"  />
                      <el-option label="重要" value="important"  />
                      <el-option label="紧急" value="urgent"  />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-tab-pane>

            <!-- 续签条件 -->
            <el-tab-pane label="续签条件" name="conditions">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="薪资调整" prop="salaryAdjustment">
                    <el-radio-group v-model="renewalForm.salaryAdjustment">
                      <el-radio value="maintain">维持原薪资</el-radio>
                      <el-radio value="increase">薪资上调</el-radio>
                      <el-radio value="decrease">薪资下调</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="12" v-if="renewalForm.salaryAdjustment !== 'maintain'">
                  <el-form-item label="调整幅度" prop="salaryAdjustmentPercent">
                    <el-input-number
                      v-model="renewalForm.salaryAdjustmentPercent"
                      :min="0"
                      :max="100"
                      controls-position="right"
                      />
                    <span style="margin-left: 10px;">%</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="岗位变更" prop="positionChange">
                    <el-radio-group v-model="renewalForm.positionChange">
                      <el-radio value="no">不变更</el-radio>
                      <el-radio value="yes">变更</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="12" v-if="renewalForm.positionChange === 'yes'">
                  <el-form-item label="新岗位" prop="newPosition">
                    <el-input v-model="renewalForm.newPosition" placeholder="请输入新岗位"   />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="工作地点" prop="workLocationChange">
                    <el-radio-group v-model="renewalForm.workLocationChange">
                      <el-radio value="no">不变更</el-radio>
                      <el-radio value="yes">变更</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="12" v-if="renewalForm.workLocationChange === 'yes'">
                  <el-form-item label="新工作地点" prop="newWorkLocation">
                    <el-input v-model="renewalForm.newWorkLocation" placeholder="请输入新工作地点"   />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-tab-pane>

            <!-- 续签原因 -->
            <el-tab-pane label="续签原因" name="reasons">
              <el-form-item label="续签原因" prop="renewalReason">
                <el-input
                  v-model="renewalForm.renewalReason"
                  type="textarea"
                  :rows="4"
                  placeholder="请详细说明续签原因"
                  />
              </el-form-item>
              <el-form-item label="员工评价" prop="employeeEvaluation">
                <el-input
                  v-model="renewalForm.employeeEvaluation"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入对员工的评价"
                  />
              </el-form-item>
              <el-form-item label="续签建议" prop="renewalSuggestion">
                <el-input
                  v-model="renewalForm.renewalSuggestion"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入续签建议"
                  />
              </el-form-item>
            </el-tab-pane>

            <!-- 附件材料 -->
            <el-tab-pane label="附件材料" name="attachments">
              <el-form-item label="附件上传">
                <el-upload
                  ref="uploadRef"
                  :file-list="renewalForm.attachments"
                  :on-change="handleFileChange"
                  :on-remove="handleFileRemove"
                  :before-upload="() => false"
                  multiple
                  drag
                >
                  <el-icon class="el-icon--upload">
                    <UploadFilled />
                  </el-icon>
                  <div class="el-upload__text">
                    将文件拖到此处，或<em>点击上传</em>
                  </div>
                  <template #tip>
                    <div class="el-upload__tip">
                      支持上传jpg/png/pdf等格式文件，单个文件大小不超过10MB
                    </div>
                  </template>
                </el-upload>
              </el-form-item>
              
              <div class="attachment-list" v-if="renewalForm.attachments.length > 0">
                <h4>已上传文件</h4>
                <el-table :data="renewalForm.attachments" style="width: 100%">
                  <el-table-column prop="name" label="文件名"  />
                  <el-table-column prop="size" label="大小" width="100">
                    <template #default="scope">
                      {{ formatFileSize(scope.row.size) }}
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="120">
                    <template #default="scope">
                      <el-button size="small" @click="handlePreviewFile(scope.row)">
                        预览
                      </el-button>
                      <el-button size="small" type="danger" @click="handleRemoveFile(scope.row)">
                        删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-form>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="renewalDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmitRenewal">提交申请</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 续签详情对话框 -->
    <el-dialog v-model="renewalDetailDialogVisible" title="续签详情" width="800px">
      <div class="renewal-detail" v-if="currentRenewal">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="员工姓名">{{ currentRenewal.employeeName }}</el-descriptions-item>
          <el-descriptions-item label="原合同编号">{{ currentRenewal.contractNumber }}</el-descriptions-item>
          <el-descriptions-item label="当前到期日期">{{ currentRenewal.currentExpiryDate }}</el-descriptions-item>
          <el-descriptions-item label="续签期限">{{ currentRenewal.renewalTerm }}年</el-descriptions-item>
          <el-descriptions-item label="续签状态">
            <el-tag :type="getStatusType(currentRenewal.renewalStatus)" size="small">
              {{ getStatusText(currentRenewal.renewalStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="申请人">{{ currentRenewal.applicant }}</el-descriptions-item>
          <el-descriptions-item label="申请时间">{{ currentRenewal.applicationDate }}</el-descriptions-item>
          <el-descriptions-item label="紧急程度">
            <el-tag 
              :type="currentRenewal.urgency === 'urgent' ? 'danger' : currentRenewal.urgency === 'important' ? 'warning' : 'info'"
              size="small"
            >
              {{ currentRenewal.urgency === 'urgent' ? '紧急' : currentRenewal.urgency === 'important' ? '重要' : '一般' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="renewal-timeline">
          <h4>续签进度</h4>
          <el-timeline>
            <el-timeline-item
              v-for="milestone in renewalMilestones"
              :key="milestone.id"
              :timestamp="milestone.timestamp"
              :type="milestone.type"
            >
              <div class="milestone-content">
                <div class="milestone-title">{{ milestone.title }}</div>
                <div class="milestone-description">{{ milestone.description }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-dialog>

    <!-- 审批对话框 -->
    <el-dialog v-model="approvalDialogVisible" title="续签审批" width="600px">
      <div class="approval-form">
        <el-form ref="approvalFormRef" :model="approvalForm" :rules="approvalRules" label-width="100px">
          <el-form-item label="审批结果" prop="result">
            <el-radio-group v-model="approvalForm.result">
              <el-radio value="approved">通过</el-radio>
              <el-radio value="rejected">拒绝</el-radio>
              <el-radio value="pending">待定</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="审批意见" prop="comment">
            <el-input
              v-model="approvalForm.comment"
              type="textarea"
              :rows="4"
              placeholder="请输入审批意见"
              />
          </el-form-item>
          <el-form-item label="审批人" prop="approver">
            <el-input v-model="approvalForm.approver" placeholder="请输入审批人姓名"   />
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="approvalDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmitApproval">提交审批</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'ContractRenewalFlow'
})
 
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  Plus,
  Search,
  Warning,
  Clock,
  Loading,
  Check,
  UploadFilled
} from '@element-plus/icons-vue'

// 响应式数据
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const renewalDialogVisible = ref(false)
const renewalDetailDialogVisible = ref(false)
const approvalDialogVisible = ref(false)
const activeRenewalTab = ref('basic')
const renewalFormRef = ref()
const approvalFormRef = ref()
const uploadRef = ref()
const currentRenewal = ref(null)

// 筛选表单
const filterForm = reactive({
  employeeName: '',
  renewalStatus: '',
  expiryDateRange: []
})

// 续签统计
const renewalStats = reactive({
  urgent: 5,
  pending: 12,
  processing: 8,
  completed: 25
})

// 续签申请表单
const renewalForm = reactive({
  employeeName: '',
  originalContractNumber: '',
  currentExpiryDate: '',
  renewalTerm: 3,
  newEffectiveDate: '',
  urgency: 'normal',
  salaryAdjustment: 'maintain',
  salaryAdjustmentPercent: 0,
  positionChange: 'no',
  newPosition: '',
  workLocationChange: 'no',
  newWorkLocation: '',
  renewalReason: '',
  employeeEvaluation: '',
  renewalSuggestion: '',
  attachments: []
})

// 续签申请验证规则
const renewalRules = {
  employeeName: [
    { required: true, message: '请输入员工姓名', trigger: 'blur' }
  ],
  originalContractNumber: [
    { required: true, message: '请输入原合同编号', trigger: 'blur' }
  ],
  currentExpiryDate: [
    { required: true, message: '请选择当前到期日期', trigger: 'change' }
  ],
  newEffectiveDate: [
    { required: true, message: '请选择新合同生效日期', trigger: 'change' }
  ],
  renewalReason: [
    { required: true, message: '请输入续签原因', trigger: 'blur' }
  ]
}

// 审批表单
const approvalForm = reactive({
  result: 'approved',
  comment: '',
  approver: ''
})

// 审批验证规则
const approvalRules = {
  result: [
    { required: true, message: '请选择审批结果', trigger: 'change' }
  ],
  comment: [
    { required: true, message: '请输入审批意见', trigger: 'blur' }
  ],
  approver: [
    { required: true, message: '请输入审批人姓名', trigger: 'blur' }
  ]
}

// 续签列表数据
const renewalList = ref([
  {
    id: '1',
    employeeName: '张三',
    contractNumber: 'HKY2025001',
    currentExpiryDate: '2025-03-31',
    renewalTerm: 3,
    renewalStatus: 'pending',
    applicant: '人事专员',
    applicationDate: '2025-01-20 09:00:00',
    urgency: 'urgent'
  },
  {
    id: '2',
    employeeName: '李四',
    contractNumber: 'HKY2025002',
    currentExpiryDate: '2025-04-30',
    renewalTerm: 2,
    renewalStatus: 'approving',
    applicant: '人事经理',
    applicationDate: '2025-01-21 10:30:00',
    urgency: 'important'
  },
  {
    id: '3',
    employeeName: '王五',
    contractNumber: 'HKY2025003',
    currentExpiryDate: '2025-05-31',
    renewalTerm: 1,
    renewalStatus: 'approved',
    applicant: '部门经理',
    applicationDate: '2025-01-22 14:00:00',
    urgency: 'normal'
  }
])

// 续签进度里程碑
const renewalMilestones = ref([
  {
    id: '1',
    timestamp: '2025-01-20 09:00:00',
    type: 'primary',
    title: '申请提交',
    description: '续签申请已提交，等待审批'
  },
  {
    id: '2',
    timestamp: '2025-01-21 10:30:00',
    type: 'success',
    title: '初审通过',
    description: '人事部门初审通过，提交部门经理审批'
  },
  {
    id: '3',
    timestamp: '2025-01-22 14:00:00',
    type: 'success',
    title: '终审通过',
    description: '部门经理审批通过，准备签署新合同'
  },
  {
    id: '4',
    timestamp: '2025-01-23 16:00:00',
    type: 'warning',
    title: '合同签署',
    description: '正在进行合同签署，待双方确认'
  }
])

// 方法
const handleRefresh = () => {
  ElMessage.success('刷新成功')
}

const handleStartRenewal = () => {
  renewalDialogVisible.value = true
  // 重置表单
  Object.assign(renewalForm, {
    employeeName: '',
    originalContractNumber: '',
    currentExpiryDate: '',
    renewalTerm: 3,
    newEffectiveDate: '',
    urgency: 'normal',
    salaryAdjustment: 'maintain',
    salaryAdjustmentPercent: 0,
    positionChange: 'no',
    newPosition: '',
    workLocationChange: 'no',
    newWorkLocation: '',
    renewalReason: '',
    employeeEvaluation: '',
    renewalSuggestion: '',
    attachments: []
  })
}

const handleFilter = () => {
  ElMessage.success('筛选查询')
}

const handleResetFilter = () => {
  Object.assign(filterForm, {
    employeeName: '',
    renewalStatus: '',
    expiryDateRange: []
  })
  ElMessage.success('筛选条件已重置')
}

   
const handleViewRenewal = (row: unknown) => {
  currentRenewal.value = row
  renewalDetailDialogVisible.value = true
}

   
const handleProcessRenewal = (row: unknown) => {
  ElMessage.success(`开始处理续签申请: ${row.employeeName}`)
}

   
const handleApproveRenewal = (row: unknown) => {
  currentRenewal.value = row
  approvalDialogVisible.value = true
  // 重置审批表单
  Object.assign(approvalForm, {
    result: 'approved',
    comment: '',
    approver: ''
  })
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  ElMessage.success(`每页显示 ${size} 条`)
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  ElMessage.success(`切换到第 ${page} 页`)
}

const handleSubmitRenewal = () => {
  renewalFormRef.value.validate((valid: boolean) => {
    if (valid) {
      renewalDialogVisible.value = false
      ElMessage.success('续签申请提交成功')
      // 添加到续签列表
      renewalList.value.unshift({
        id: Date.now().toString(),
        employeeName: renewalForm.employeeName,
        contractNumber: renewalForm.originalContractNumber,
        currentExpiryDate: renewalForm.currentExpiryDate,
        renewalTerm: renewalForm.renewalTerm,
        renewalStatus: 'pending',
        applicant: '当前用户',
        applicationDate: new Date().toLocaleString(),
        urgency: renewalForm.urgency
      })
    }
  })
}

const handleSubmitApproval = () => {
  approvalFormRef.value.validate((valid: boolean) => {
    if (valid) {
      approvalDialogVisible.value = false
      ElMessage.success('审批提交成功')
      // 更新续签状态
      if (currentRenewal.value) {
        const index = renewalList.value.findIndex(item => item.id === currentRenewal.value.id)
        if (index !== -1) {
          renewalList.value[index].renewalStatus = approvalForm.result
        }
      }
    }
  })
}

   
const handleFileChange = (file: unknown) => {
  renewalForm.attachments.push(file)
  ElMessage.success(`文件 ${file.name} 上传成功`)
}

   
const handleFileRemove = (file: unknown) => {
  const index = renewalForm.attachments.findIndex(item => item.uid === file.uid)
  if (index !== -1) {
    renewalForm.attachments.splice(index, 1)
  }
}

   
const handlePreviewFile = (file: unknown) => {
  ElMessage.info(`预览文件: ${file.name}`)
}

   
const handleRemoveFile = (file: unknown) => {
  const index = renewalForm.attachments.findIndex(item => item.uid === file.uid)
  if (index !== -1) {
    renewalForm.attachments.splice(index, 1)
    ElMessage.success('文件删除成功')
  }
}

const getStatusType = (status: string) => {
  const statusMap = {
    'pending': 'info',
    'approving': 'warning',
    'approved': 'success',
    'rejected': 'danger',
    'signed': 'success'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap = {
    'pending': '待申请',
    'approving': '审批中',
    'approved': '已通过',
    'rejected': '已拒绝',
    'signed': '已签署'
  }
  return statusMap[status] || '未知'
}

const formatFileSize = (size: number) => {
  if (size < 1024) {
    return size + 'B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(1) + 'KB'
  } else {
    return (size / (1024 * 1024)).toFixed(1) + 'MB'
  }
}

// 生命周期
onMounted(() => {
  total.value = renewalList.value.length
})
</script>

<style scoped>
.contract-renewal-flow {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.renewal-list {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.renewal-stats {
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.stat-card.urgent .stat-icon {
  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
}

.stat-card.pending .stat-icon {
  background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
}

.stat-card.processing .stat-icon {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
}

.stat-card.completed .stat-icon {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.renewal-form {
  margin-top: 20px;
}

.attachment-list {
  margin-top: 20px;
}

.attachment-list h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.renewal-detail {
  margin-top: 20px;
}

.renewal-timeline {
  margin-top: 30px;
}

.renewal-timeline h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.milestone-content {
  padding: 8px 0;
}

.milestone-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.milestone-description {
  color: #606266;
  font-size: 14px;
}

.approval-form {
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}

.el-pagination {
  margin-top: 20px;
  text-align: center;
}
</style>