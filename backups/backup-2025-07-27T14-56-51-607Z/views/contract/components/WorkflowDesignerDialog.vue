<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="90%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="workflow-designer">
      <!-- 设计器工具栏 -->
      <div class="designer-toolbar">
        <div class="toolbar-left">
          <el-button size="small" @click="handleAddStep">
            <el-icon><Plus /></el-icon>
            添加步骤
          </el-button>
          <el-button size="small" @click="handleAutoLayout">
            <el-icon><Grid /></el-icon>
            自动布局
          </el-button>
          <el-button size="small" @click="handleValidateWorkflow">
            <el-icon><Check /></el-icon>
            验证流程
          </el-button>
        </div>
        <div class="toolbar-right">
          <el-button size="small" @click="handlePreviewWorkflow">
            <el-icon><View /></el-icon>
            预览
          </el-button>
          <el-button size="small" @click="handleImportWorkflow">
            <el-icon><Upload /></el-icon>
            导入
          </el-button>
          <el-button size="small" @click="handleExportWorkflow">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="designer-content">
        <!-- 左侧组件面板 -->
        <div class="component-panel">
          <h4>组件库</h4>
          <div class="component-list">
            <div
              v-for="component in componentLibrary"
              :key="component.type"
              class="component-item"
              draggable="true"
              @dragstart="handleDragStart(component)"
            >
              <el-icon class="component-icon">
                <component :is="component.icon" />
              </el-icon>
              <span class="component-name">{{ component.name }}</span>
            </div>
          </div>
        </div>

        <!-- 中间设计画布 -->
        <div class="design-canvas">
          <div class="canvas-header">
            <span>工作流设计画布</span>
            <div class="canvas-tools">
              <el-button-group size="small">
                <el-button @click="handleZoomIn">
                  <el-icon><ZoomIn /></el-icon>
                </el-button>
                <el-button @click="handleZoomOut">
                  <el-icon><ZoomOut /></el-icon>
                </el-button>
                <el-button @click="handleResetZoom">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </el-button-group>
            </div>
          </div>
          
          <div
            class="canvas-area"
            @drop="handleDrop"
            @dragover="handleDragOver"
            @click="handleCanvasClick"
          >
            <!-- 工作流步骤节点 -->
            <div
              v-for="(step, index) in workflowSteps"
              :key="step.id"
              class="workflow-step"
              :class="{ active: selectedStep?.id === step.id }"
              :style="{ left: step.x + 'px', top: step.y + 'px' }"
              @click.stop="handleSelectStep(step)"
              @mousedown="handleStepMouseDown(step, $event)"
            >
              <div class="step-header">
                <el-icon class="step-icon">
                  <component :is="getStepIcon(step.type)" />
                </el-icon>
                <span class="step-order">{{ index + 1 }}</span>
              </div>
              <div class="step-content">
                <div class="step-name">{{ step.stepName || '未命名步骤' }}</div>
                <div class="step-approver">{{ step.approverName || '未设置审批人' }}</div>
              </div>
              <div class="step-actions">
                <el-button size="small" type="primary" link @click.stop="handleEditStep(step)">
                  <el-icon><Edit /></el-icon>
                </el-button>
                <el-button size="small" type="danger" link @click.stop="handleDeleteStep(step)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>

            <!-- 连接线 -->
            <svg class="connection-lines" :style="{ width: '100%', height: '100%' }">
              <defs>
                <marker
                  id="arrowhead"
                  markerWidth="10"
                  markerHeight="7"
                  refX="9"
                  refY="3.5"
                  orient="auto"
                >
                  <polygon points="0 0, 10 3.5, 0 7" fill="#409eff" />
                </marker>
              </defs>
              <line
                v-for="connection in connections"
                :key="`${connection.from}-${connection.to}`"
                :x1="connection.x1"
                :y1="connection.y1"
                :x2="connection.x2"
                :y2="connection.y2"
                stroke="#409eff"
                stroke-width="2"
                marker-end="url(#arrowhead)"
              />
            </svg>

            <!-- 空状态提示 -->
            <div v-if="workflowSteps.length === 0" class="empty-canvas">
              <el-icon class="empty-icon"><Box /></el-icon>
              <p>拖拽左侧组件到此处开始设计工作流</p>
            </div>
          </div>
        </div>

        <!-- 右侧属性面板 -->
        <div class="property-panel">
          <h4>属性配置</h4>
          <div v-if="selectedStep" class="step-properties">
            <el-form :model="selectedStep" label-width="80px" size="small">
              <el-form-item label="步骤名称">
                <el-input v-model="selectedStep.stepName" placeholder="请输入步骤名称"   />
              </el-form-item>
              <el-form-item label="步骤类型">
                <el-select v-model="selectedStep.type" style="width: 100%">
                  <el-option label="审批" value="approval"  />
                  <el-option label="通知" value="notification"  />
                  <el-option label="条件" value="condition"  />
                  <el-option label="并行" value="parallel"  />
                </el-select>
              </el-form-item>
              <el-form-item label="审批人">
                <el-select v-model="selectedStep.approverType" style="width: 100%" @change="handleApproverTypeChange">
                  <el-option label="指定用户" value="user"  />
                  <el-option label="角色" value="role"  />
                  <el-option label="部门" value="department"  />
                </el-select>
              </el-form-item>
              <el-form-item label="审批人员">
                <el-select
                  v-model="selectedStep.approverIds"
                  multiple
                  placeholder="选择审批人员"
                  style="width: 100%"
                >
                  <el-option
                    v-for="approver in availableApprovers"
                    :key="approver.id"
                    :label="approver.name"
                    :value="approver.id"
                   />
                </el-select>
              </el-form-item>
              <el-form-item label="是否必须">
                <el-switch v-model="selectedStep.isRequired"  />
              </el-form-item>
              <el-form-item label="超时时间">
                <el-input-number
                  v-model="selectedStep.timeoutHours"
                  :min="1"
                  :max="168"
                  placeholder="小时"
                  style="width: 100%"
                  />
              </el-form-item>
              <el-form-item label="自动审批">
                <el-switch v-model="selectedStep.autoApprove"  />
              </el-form-item>
              <el-form-item label="审批条件">
                <el-input
                  v-model="selectedStep.conditions"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入审批条件表达式"
                  />
              </el-form-item>
            </el-form>
          </div>
          <div v-else class="no-selection">
            <el-icon class="no-selection-icon"><InfoFilled /></el-icon>
            <p>请选择一个步骤来配置属性</p>
          </div>
        </div>
      </div>

      <!-- 工作流基本信息 -->
      <el-card class="workflow-info-card" shadow="never">
        <template #header>
          <span>工作流基本信息</span>
        </template>
        <el-form ref="workflowFormRef" :model="workflowInfo" :rules="workflowRules" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="工作流名称" prop="workflowName">
                <el-input v-model="workflowInfo.workflowName" placeholder="请输入工作流名称"   />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="适用合同类型" prop="contractType">
                <el-select v-model="workflowInfo.contractType" placeholder="请选择合同类型" style="width: 100%">
                  <el-option
                    v-for="item in contractTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                   />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="工作流描述" prop="description">
            <el-input
              v-model="workflowInfo.description"
              type="textarea"
              :rows="3"
              placeholder="请输入工作流描述"
              />
          </el-form-item>
          <el-form-item label="启用状态">
            <el-switch
              v-model="workflowInfo.isActive"
              active-text="启用"
              inactive-text="禁用"
             />
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 步骤编辑对话框 -->
    <StepEditorDialog
      v-model:visible="stepEditorVisible"
      :step="editingStep"
      :mode="stepEditorMode"
      @success="handleStepEditorSuccess"
    />

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="handleSaveWorkflow">保存草稿</el-button>
        <el-button type="primary" @click="handleSubmitWorkflow" :loading="loading">
          {{ mode === 'add' ? '创建工作流' : '保存工作流' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Grid,
  Check,
  View,
  Upload,
  Download,
  ZoomIn,
  ZoomOut,
  Refresh,
  Edit,
  Delete,
  Box,
  InfoFilled,
  User,
  Bell,
  Share,
  Operation
} from '@element-plus/icons-vue'
import StepEditorDialog from './StepEditorDialog.vue'

// Props
interface Props {
  visible: boolean
   
  workflow?: unknown
  mode: 'view' | 'add' | 'edit'
}

const props = withDefaults(defineProps<Props>(), {
  workflow: null,
  mode: 'add'
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const workflowFormRef = ref()
const loading = ref(false)
const stepEditorVisible = ref(false)
const stepEditorMode = ref<'add' | 'edit'>('add')
const editingStep = ref(null)
const selectedStep = ref<unknown>(null) // 修复类型：null → any
const draggedComponent = ref<unknown>(null) // 修复类型：null → any

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 对话框标题
const dialogTitle = computed(() => {
  switch (props.mode) {
    case 'add': return '创建工作流'
    case 'edit': return '编辑工作流'
    case 'view': return '查看工作流'
    default: return '工作流设计器'
  }
})

// 工作流基本信息
const workflowInfo = reactive({
  workflowName: '',
  contractType: '',
  description: '',
  isActive: true
})

// 工作流验证规则
const workflowRules = {
  workflowName: [
    { required: true, message: '请输入工作流名称', trigger: 'blur' }
  ],
  contractType: [
    { required: true, message: '请选择合同类型', trigger: 'change' }
  ]
}

// 工作流步骤
const workflowSteps = ref([
  {
    id: '1',
    stepName: '部门初审',
    type: 'approval',
    approverType: 'role',
    approverIds: ['dept_manager'],
    approverName: '部门主管',
    isRequired: true,
    timeoutHours: 24,
    autoApprove: false,
    conditions: '',
    x: 100,
    y: 100
  },
  {
    id: '2',
    stepName: '人事处审批',
    type: 'approval',
    approverType: 'role',
    approverIds: ['hr_manager'],
    approverName: '人事处长',
    isRequired: true,
    timeoutHours: 48,
    autoApprove: false,
    conditions: '',
    x: 300,
    y: 100
  }
])

// 连接线
const connections = computed(() => {
  const lines = []
  for (let i = 0; i < workflowSteps.value.length - 1; i++) {
    const current = workflowSteps.value[i]
    const next = workflowSteps.value[i + 1]
    lines.push({
      from: current.id,
      to: next.id,
      x1: current.x + 100,
      y1: current.y + 40,
      x2: next.x,
      y2: next.y + 40
    })
  }
  return lines
})

// 组件库
const componentLibrary = [
  { type: 'approval', name: 'HrHr审批节点', icon: User },
  { type: 'notification', name: '通知节点', icon: Bell },
  { type: 'condition', name: '条件节点', icon: Share },
  { type: 'parallel', name: '并行节点', icon: Operation }
]

// 合同类型选项
const contractTypeOptions = [
  { label: '事业编制聘用合同', value: 'CAREER_ESTABLISHMENT' },
  { label: '人事代理合同', value: 'PERSONNEL_AGENCY' },
  { label: '劳务协议', value: 'LABOR_AGREEMENT' }
]

// 可用审批人
const availableApprovers = ref([
  { id: 'dept_manager', name: '部门主管' },
  { id: 'hr_manager', name: '人事处长' },
  { id: 'dean', name: '院长' }
])

// 获取步骤图标
const getStepIcon = (type: string) => {
  switch (type) {
    case 'approval': return User
    case 'notification': return Bell
    case 'condition': return Share
    case 'parallel': return Operation
    default: return User
  }
}

// 拖拽开始
   
const handleDragStart = (component: unknown) => {
  draggedComponent.value = component
}

// 拖拽结束
const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  if (!draggedComponent.value) return

  const rect = (event.target as HTMLElement).getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  const newStep = {
    id: Date.now().toString(),
    stepName: `新${(draggedComponent.value as unknown)?.name}`, // 修复属性访问类型
    type: (draggedComponent.value as unknown)?.type, // 修复属性访问类型
    approverType: 'user',
    approverIds: [],
    approverName: '',
    isRequired: true,
    timeoutHours: 24,
    autoApprove: false,
    conditions: '',
    x: x - 50,
    y: y - 40
  }

  workflowSteps.value.push(newStep)
  draggedComponent.value = null
}

// 拖拽悬停
const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
}

// 画布点击
const handleCanvasClick = () => {
  selectedStep.value = null
}

// 选择步骤
   
const handleSelectStep = (step: unknown) => {
  selectedStep.value = step
}

// 步骤鼠标按下（用于拖拽移动）
   
const handleStepMouseDown = (step: unknown, event: MouseEvent) => {
  // 实现步骤拖拽移动逻辑
}

// 添加步骤
const handleAddStep = () => {
  editingStep.value = null
  stepEditorMode.value = 'add'
  stepEditorVisible.value = true
}

// 编辑步骤
   
const handleEditStep = (step: unknown) => {
  editingStep.value = step
  stepEditorMode.value = 'edit'
  stepEditorVisible.value = true
}

// 删除步骤
   
const handleDeleteStep = async (step: unknown) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除步骤 "${step.stepName}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = workflowSteps.value.findIndex(s => s.id === step.id)
    if (index > -1) {
      workflowSteps.value.splice(index, 1)
    }
    
    if ((selectedStep.value as unknown)?.id === (step as unknown)?.id) { // 修复属性访问类型
      selectedStep.value = null
    }
    
    ElMessage.success('删除成功')
  } catch (__error) {
    // 用户取消
  }
}

// 审批人类型变化
const handleApproverTypeChange = () => {
  if (selectedStep.value) {
    (selectedStep.value as unknown).approverIds = [] // 修复属性访问类型
  }
}

// 步骤编辑器成功
   
const handleStepEditorSuccess = (step: unknown) => {
  if (stepEditorMode.value === 'add') {
    workflowSteps.value.push({
      ...step,
      id: Date.now().toString(),
      x: 100 + workflowSteps.value.length * 200,
      y: 100
    })
  } else {
   
    const index = workflowSteps.value.findIndex((s: unknown) => (s as unknown)?.id === (editingStep.value as unknown)?.id) // 修复类型断言
    if (index > -1) {
      workflowSteps.value[index] = { ...workflowSteps.value[index], ...step }
    }
  }
  ElMessage.success('步骤保存成功')
}

// 自动布局
const handleAutoLayout = () => {
  workflowSteps.value.forEach((step, index) => {
    step.x = 100 + index * 200
    step.y = 100
  })
  ElMessage.success('自动布局完成')
}

// 验证工作流
const handleValidateWorkflow = () => {
  if (workflowSteps.value.length === 0) {
    ElMessage.warning('工作流至少需要一个步骤')
    return
  }
  
  const invalidSteps = workflowSteps.value.filter(step => !step.stepName || !step.approverIds.length)
  if (invalidSteps.length > 0) {
    ElMessage.warning('存在未配置完整的步骤')
    return
  }
  
  ElMessage.success('工作流验证通过')
}

// 预览工作流
const handlePreviewWorkflow = () => {
  ElMessage.info('预览功能开发中...')
}

// 导入工作流
const handleImportWorkflow = () => {
  ElMessage.info('导入功能开发中...')
}

// 导出工作流
const handleExportWorkflow = () => {
  ElMessage.info('导出功能开发中...')
}

// 缩放操作
const handleZoomIn = () => {
  ElMessage.info('放大功能开发中...')
}

const handleZoomOut = () => {
  ElMessage.info('缩小功能开发中...')
}

const handleResetZoom = () => {
  ElMessage.info('重置缩放功能开发中...')
}

// 保存工作流
const handleSaveWorkflow = async () => {
  try {
    loading.value = true
    
    // 模拟保存
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('工作流草稿保存成功')
  } catch (__error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

// 提交工作流
const handleSubmitWorkflow = async () => {
  try {
    await workflowFormRef.value.validate()
    
    if (workflowSteps.value.length === 0) {
      ElMessage.warning('工作流至少需要一个步骤')
      return
    }
    
    loading.value = true
    
    // 模拟提交
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success(props.mode === 'add' ? '工作流创建成功' : '工作流保存成功')
    emit('success')
    handleClose()
  } catch (__error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.workflow-designer {
  height: 80vh;
  display: flex;
  flex-direction: column;
}

.designer-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #ebeef5;
  background: #f5f7fa;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  gap: 8px;
}

.designer-content {
  flex: 1;
  display: flex;
  min-height: 0;
}

.component-panel {
  width: 200px;
  border-right: 1px solid #ebeef5;
  padding: 16px;
  background: #fafafa;
}

.component-panel h4 {
  margin: 0 0 16px 0;
  color: #303133;
}

.component-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.component-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: white;
  cursor: grab;
  transition: all 0.3s;
}

.component-item:hover {
  border-color: #409eff;
  background: #ecf5ff;
}

.component-item:active {
  cursor: grabbing;
}

.component-icon {
  font-size: 16px;
  color: #409eff;
}

.component-name {
  font-size: 12px;
  color: #606266;
}

.design-canvas {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.canvas-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
  background: #f5f7fa;
}

.canvas-area {
  flex: 1;
  position: relative;
  background: white;
  overflow: hidden;
}

.workflow-step {
  position: absolute;
  width: 200px;
  height: 80px;
  border: 2px solid #dcdfe6;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.3s;
}

.workflow-step:hover,
.workflow-step.active {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.step-icon {
  color: #409eff;
}

.step-order {
  background: #409eff;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.step-content {
  padding: 8px 12px;
  flex: 1;
}

.step-name {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
  margin-bottom: 4px;
}

.step-approver {
  font-size: 12px;
  color: #909399;
}

.step-actions {
  position: absolute;
  top: -8px;
  right: -8px;
  display: none;
  gap: 4px;
}

.workflow-step:hover .step-actions {
  display: flex;
}

.connection-lines {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 1;
}

.empty-canvas {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #909399;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.property-panel {
  width: 300px;
  border-left: 1px solid #ebeef5;
  padding: 16px;
  background: #fafafa;
}

.property-panel h4 {
  margin: 0 0 16px 0;
  color: #303133;
}

.no-selection {
  text-align: center;
  color: #909399;
  margin-top: 50px;
}

.no-selection-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.workflow-info-card {
  margin-top: 16px;
}

.dialog-footer {
  text-align: right;
}
</style>
