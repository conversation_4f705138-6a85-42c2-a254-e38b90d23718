<template>
  <el-dialog
    v-model="dialogVisible"
    title="模板导入"
    width="60%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="import-container">
      <!-- 导入步骤 -->
      <el-steps :active="currentStep" finish-status="success" class="import-steps">
        <el-step title="选择文件" description="上传模板文件"  />
        <el-step title="配置信息" description="设置模板信息"  />
        <el-step title="预览确认" description="预览导入内容"  />
        <el-step title="导入完成" description="完成导入"  />
      </el-steps>

      <!-- 步骤1: 文件上传 -->
      <div v-if="currentStep === 0" class="step-content">
        <div class="upload-section">
          <el-upload
            ref="uploadRef"
            class="upload-dragger"
            drag
            :auto-upload="false"
            :limit="1"
            :accept="acceptedFileTypes"
            :on-change="handleFileChange"
            :on-exceed="handleExceed"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持 .docx, .txt, .json 格式的模板文件，文件大小不超过 10MB
              </div>
            </template>
          </el-upload>

          <div v-if="uploadedFile" class="file-info">
            <el-card shadow="never">
              <div class="file-details">
                <el-icon class="file-icon"><Document /></el-icon>
                <div class="file-meta">
                  <div class="file-name">{{ uploadedFile.name }}</div>
                  <div class="file-size">{{ formatFileSize(uploadedFile.size) }}</div>
                </div>
                <el-button type="danger" size="small" @click="handleRemoveFile">
                  <el-icon><Delete /></el-icon>
                  移除
                </el-button>
              </div>
            </el-card>
          </div>
        </div>

        <div class="import-options">
          <h4>导入选项</h4>
          <el-form :model="importOptions" label-width="120px">
            <el-form-item label="导入模式">
              <el-radio-group v-model="importOptions.mode">
                <el-radio value="new">创建新模板</el-radio>
                <el-radio value="update">更新现有模板</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="importOptions.mode === 'update'" label="目标模板">
              <el-select v-model="importOptions.targetTemplate" placeholder="选择要更新的模板" style="width: 100%">
                <el-option
                  v-for="template in existingTemplates"
                  :key="template.id"
                  :label="template.templateName"
                  :value="template.id"
                 />
              </el-select>
            </el-form-item>
            <el-form-item label="冲突处理">
              <el-radio-group v-model="importOptions.conflictResolution">
                <el-radio value="skip">跳过冲突项</el-radio>
                <el-radio value="overwrite">覆盖现有项</el-radio>
                <el-radio value="rename">自动重命名</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 步骤2: 配置信息 -->
      <div v-if="currentStep === 1" class="step-content">
        <el-form ref="configFormRef" :model="templateConfig" :rules="configRules" label-width="120px">
          <el-form-item label="模板名称" prop="templateName">
            <el-input v-model="templateConfig.templateName" placeholder="请输入模板名称"   />
          </el-form-item>
          <el-form-item label="模板编码" prop="templateCode">
            <el-input v-model="templateConfig.templateCode" placeholder="请输入模板编码"   />
          </el-form-item>
          <el-form-item label="合同类型" prop="contractType">
            <el-select v-model="templateConfig.contractType" placeholder="请选择合同类型" style="width: 100%">
              <el-option
                v-for="item in contractTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-select>
          </el-form-item>
          <el-form-item label="版本号" prop="version">
            <el-input v-model="templateConfig.version" placeholder="请输入版本号"   />
          </el-form-item>
          <el-form-item label="模板描述" prop="description">
            <el-input
              v-model="templateConfig.description"
              type="textarea"
              :rows="3"
              placeholder="请输入模板描述"
              />
          </el-form-item>
          <el-form-item label="启用状态">
            <el-switch
              v-model="templateConfig.isActive"
              active-text="启用"
              inactive-text="禁用"
             />
          </el-form-item>
        </el-form>
      </div>

      <!-- 步骤3: 预览确认 -->
      <div v-if="currentStep === 2" class="step-content">
        <div class="preview-section">
          <h4>导入预览</h4>
          
          <!-- 模板信息预览 -->
          <el-card class="preview-card" shadow="never">
            <template #header>
              <span>模板信息</span>
            </template>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="模板名称">
                {{ templateConfig.templateName }}
              </el-descriptions-item>
              <el-descriptions-item label="模板编码">
                {{ templateConfig.templateCode }}
              </el-descriptions-item>
              <el-descriptions-item label="合同类型">
                {{ getContractTypeText(templateConfig.contractType) }}
              </el-descriptions-item>
              <el-descriptions-item label="版本号">
                v{{ templateConfig.version }}
              </el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="templateConfig.isActive ? 'success' : 'info'" size="small">
                  {{ templateConfig.isActive ? '启用' : '禁用' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="变量数量">
                {{ parsedVariables.length }}
              </el-descriptions-item>
              <el-descriptions-item label="描述" :span="2">
                {{ templateConfig.description }}
              </el-descriptions-item>
            </el-descriptions>
          </el-card>

          <!-- 变量预览 -->
          <el-card class="preview-card" shadow="never" style="margin-top: 16px;">
            <template #header>
              <span>变量列表 ({{ parsedVariables.length }}个)</span>
            </template>
            <el-table :data="parsedVariables" style="width: 100%" max-height="200">
              <el-table-column prop="variableName" label="变量名" width="150"  />
              <el-table-column prop="variableLabel" label="显示名称" width="150"  />
              <el-table-column prop="variableType" label="类型" width="100">
                <template #default="scope">
                  <el-tag size="small">{{ scope.row.variableType }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="isRequired" label="必填" width="80">
                <template #default="scope">
                  <el-tag :type="scope.row.isRequired ? 'danger' : 'info'" size="small">
                    {{ scope.row.isRequired ? '是' : '否' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="defaultValue" label="默认值" show-overflow-tooltip  />
            </el-table>
          </el-card>

          <!-- 内容预览 -->
          <el-card class="preview-card" shadow="never" style="margin-top: 16px;">
            <template #header>
              <span>模板内容预览</span>
            </template>
            <div class="content-preview">
              {{ parsedContent }}
            </div>
          </el-card>
        </div>
      </div>

      <!-- 步骤4: 导入完成 -->
      <div v-if="currentStep === 3" class="step-content">
        <div class="result-section">
          <div class="result-icon">
            <el-icon v-if="importResult.success" class="success-icon"><CircleCheck /></el-icon>
            <el-icon v-else class="error-icon"><CircleClose /></el-icon>
          </div>
          <div class="result-message">
            <h3>{{ importResult.success ? '导入成功' : '导入失败' }}</h3>
            <p>{{ importResult.message }}</p>
          </div>
          
          <div v-if="importResult.success" class="result-details">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="导入模板">
                {{ templateConfig.templateName }}
              </el-descriptions-item>
              <el-descriptions-item label="模板编码">
                {{ templateConfig.templateCode }}
              </el-descriptions-item>
              <el-descriptions-item label="导入时间">
                {{ importResult.importTime }}
              </el-descriptions-item>
              <el-descriptions-item label="变量数量">
                {{ parsedVariables.length }}
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <div v-if="!importResult.success && importResult.errors" class="error-details">
            <h4>错误详情</h4>
            <ul>
              <li v-for="error in importResult.errors" :key="error">{{ error }}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button v-if="currentStep > 0 && currentStep < 3" @click="handlePrevStep">
          上一步
        </el-button>
        <el-button @click="handleClose">
          {{ currentStep === 3 ? '关闭' : '取消' }}
        </el-button>
        <el-button
          v-if="currentStep < 3"
          type="primary"
          @click="handleNextStep"
          :loading="loading"
          :disabled="!canProceed"
        >
          {{ currentStep === 2 ? '开始导入' : '下一步' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'TemplateImportDialog'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  UploadFilled,
  Document,
  Delete,
  CircleCheck,
  CircleClose
} from '@element-plus/icons-vue'

// Props
interface Props {
  visible: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const uploadRef = ref()
const configFormRef = ref()
const currentStep = ref(0)
const loading = ref(false)
const uploadedFile = ref<unknown>(null) // 修复类型：null → any

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 导入选项
const importOptions = reactive({
  mode: 'new',
  targetTemplate: '',
  conflictResolution: 'skip'
})

// 模板配置
const templateConfig = reactive({
  templateName: '',
  templateCode: '',
  contractType: '',
  version: '1.0',
  description: '',
  isActive: true
})

// 导入结果
const importResult = reactive({
  success: false,
  message: '',
  importTime: '',
  errors: [] as unknown[] // 修复类型：never[] → any[]
})

// 解析的变量和内容
const parsedVariables = ref<any[]>([]) // 修复类型：never[] → any[]
const parsedContent = ref('')

// 配置表单验证规则
const configRules = {
  templateName: [
    { required: true, message: '请输入模板名称', trigger: 'blur' }
  ],
  templateCode: [
    { required: true, message: '请输入模板编码', trigger: 'blur' }
  ],
  contractType: [
    { required: true, message: '请选择合同类型', trigger: 'change' }
  ],
  version: [
    { required: true, message: '请输入版本号', trigger: 'blur' }
  ]
}

// 支持的文件类型
const acceptedFileTypes = '.docx,.txt,.json'

// 合同类型选项
const contractTypeOptions = [
  { label: '事业编制聘用合同', value: 'CAREER_ESTABLISHMENT' },
  { label: '人事代理合同', value: 'PERSONNEL_AGENCY' },
  { label: '劳务协议', value: 'LABOR_AGREEMENT' },
  { label: '劳务派遣合同', value: 'LABOR_DISPATCH' },
  { label: '兼职协议', value: 'PART_TIME_AGREEMENT' },
  { label: '项目合作协议', value: 'PROJECT_COOPERATION' }
]

// 现有模板列表
const existingTemplates = ref([
  { id: '1', templateName: '事业编制聘用合同模板' },
  { id: '2', templateName: '劳务协议模板' }
])

// 是否可以继续下一步
const canProceed = computed(() => {
  switch (currentStep.value) {
    case 0:
      return uploadedFile.value !== null
    case 1:
      return templateConfig.templateName && templateConfig.templateCode && templateConfig.contractType
    case 2:
      return true
    default:
      return false
  }
})

// 文件变化处理
   
const handleFileChange = (file: unknown) => {
  uploadedFile.value = file
  
  // 解析文件内容
  parseFileContent(file)
}

// 文件超出限制
const handleExceed = () => {
  ElMessage.warning('只能上传一个文件')
}

// 移除文件
const handleRemoveFile = () => {
  uploadedFile.value = null
  uploadRef.value.clearFiles()
  parsedVariables.value = []
  parsedContent.value = ''
}

// 解析文件内容
   
const parseFileContent = async (file: unknown) => {
  try {
    loading.value = true
    
    // 模拟文件解析
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 根据文件类型解析内容
    if (file.name.endsWith('.json')) {
      // JSON格式模板
      const reader = new FileReader()
      reader.onload = (_e) => {
        try {
          const data = JSON.parse(e.target?.result as string)
          templateConfig.templateName = data.templateName || ''
          templateConfig.templateCode = data.templateCode || ''
          templateConfig.contractType = data.contractType || ''
          templateConfig.description = data.description || ''
          parsedVariables.value = data.variables || []
          parsedContent.value = data.templateContent || ''
        } catch (__error) {
          ElMessage.error('JSON文件格式错误')
        }
      }
      reader.readAsText(file.raw)
    } else {
      // 其他格式，模拟解析结果
      templateConfig.templateName = file.name.replace(/\.[^/.]+$/, '')
      templateConfig.templateCode = `TEMPLATE_${Date.now()}`
      parsedVariables.value = [
        {
          variableName: 'employeeName',
          variableLabel: '员工姓名',
          variableType: 'TEXT',
          isRequired: true,
          defaultValue: ''
        },
        {
          variableName: 'startDate',
          variableLabel: '开始日期',
          variableType: 'DATE',
          isRequired: true,
          defaultValue: ''
        }
      ]
      parsedContent.value = '这是从文件中解析出的模板内容...'
    }
    
    ElMessage.success('文件解析成功')
  } catch (__error) {
    console.error('文件解析失败:', error)
    ElMessage.error('文件解析失败')
  } finally {
    loading.value = false
  }
}

// 下一步
const handleNextStep = async () => {
  if (currentStep.value === 1) {
    // 验证配置表单
    try {
      await configFormRef.value.validate()
    } catch (__error) {
      return
    }
  }
  
  if (currentStep.value === 2) {
    // 开始导入
    await performImport()
  } else {
    currentStep.value++
  }
}

// 上一步
const handlePrevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

// 执行导入
const performImport = async () => {
  try {
    loading.value = true
    
    // 模拟导入过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 模拟导入结果
    const success = Math.random() > 0.2 // 80% 成功率
    
    if (success) {
      importResult.success = true
      importResult.message = '模板导入成功，已添加到模板库中'
      importResult.importTime = new Date().toLocaleString()
      importResult.errors = []
    } else {
      importResult.success = false
      importResult.message = '模板导入失败，请检查文件格式和内容'
      importResult.errors = [
        '模板编码已存在',
        '变量定义格式错误',
        '模板内容包含不支持的标签'
      ]
    }
    
    currentStep.value = 3
    
    if (success) {
      ElMessage.success('导入成功')
      emit('success')
    } else {
      ElMessage.error('导入失败')
    }
  } catch (__error) {
    console.error('导入失败:', error)
    ElMessage.error('导入过程中发生错误')
  } finally {
    loading.value = false
  }
}

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 获取合同类型文本
const getContractTypeText = (type: string) => {
  const option = contractTypeOptions.find(item => item.value === type)
  return option ? option.label : type
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  // 重置状态
  currentStep.value = 0
  uploadedFile.value = null
  uploadRef.value?.clearFiles()
  Object.assign(templateConfig, {
    templateName: '',
    templateCode: '',
    contractType: '',
    version: '1.0',
    description: '',
    isActive: true
  })
  parsedVariables.value = []
  parsedContent.value = ''
}
</script>

<style scoped>
.import-container {
  max-height: 70vh;
  overflow-y: auto;
}

.import-steps {
  margin-bottom: 30px;
}

.step-content {
  min-height: 300px;
}

.upload-section {
  margin-bottom: 20px;
}

.upload-dragger {
  width: 100%;
}

.file-info {
  margin-top: 16px;
}

.file-details {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-icon {
  font-size: 24px;
  color: #409eff;
}

.file-meta {
  flex: 1;
}

.file-name {
  font-weight: 600;
  color: #303133;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

.import-options h4 {
  margin: 0 0 16px 0;
  color: #303133;
}

.preview-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
}

.preview-card {
  margin-bottom: 16px;
}

.content-preview {
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background: #fafafa;
  max-height: 200px;
  overflow-y: auto;
  font-family: monospace;
  font-size: 13px;
  line-height: 1.6;
  white-space: pre-wrap;
}

.result-section {
  text-align: center;
  padding: 20px;
}

.result-icon {
  margin-bottom: 16px;
}

.success-icon {
  font-size: 48px;
  color: #67c23a;
}

.error-icon {
  font-size: 48px;
  color: #f56c6c;
}

.result-message h3 {
  margin: 0 0 8px 0;
  color: #303133;
}

.result-message p {
  margin: 0 0 20px 0;
  color: #606266;
}

.result-details {
  margin-top: 20px;
  text-align: left;
}

.error-details {
  margin-top: 20px;
  text-align: left;
}

.error-details h4 {
  margin: 0 0 12px 0;
  color: #f56c6c;
}

.error-details ul {
  margin: 0;
  padding-left: 20px;
}

.error-details li {
  color: #f56c6c;
  margin-bottom: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>
