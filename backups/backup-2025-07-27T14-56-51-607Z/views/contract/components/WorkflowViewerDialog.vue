<template>
  <el-dialog
    v-model="dialogVisible"
    title="工作流查看器"
    width="80%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="workflow-viewer">
      <!-- 合同信息 -->
      <el-card v-if="contract" class="contract-info-card" shadow="never">
        <template #header>
          <span>合同信息</span>
        </template>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="合同编号">
            {{ contract.contractNumber }}
          </el-descriptions-item>
          <el-descriptions-item label="员工姓名">
            {{ contract.employeeName }}
          </el-descriptions-item>
          <el-descriptions-item label="合同类型">
            {{ contract.contractType }}
          </el-descriptions-item>
          <el-descriptions-item label="申请部门">
            {{ contract.department }}
          </el-descriptions-item>
          <el-descriptions-item label="当前状态">
            <el-tag :type="getStatusTag(contract.status)" size="small">
              {{ getStatusText(contract.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="提交时间">
            {{ contract.submitTime }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 工作流进度 -->
      <el-card class="workflow-progress-card" shadow="never">
        <template #header>
          <div class="progress-header">
            <span>审批进度</span>
            <div class="progress-stats">
              <el-tag type="info" size="small">
                {{ completedSteps }}/{{ totalSteps }} 已完成
              </el-tag>
              <el-tag :type="getProgressTag(progressPercentage)" size="small">
                {{ progressPercentage }}%
              </el-tag>
            </div>
          </div>
        </template>

        <!-- 进度条 -->
        <el-progress
          :percentage="progressPercentage"
          :status="getProgressStatus()"
          :stroke-width="8"
          class="workflow-progress"
         />

        <!-- 步骤流程图 -->
        <div class="workflow-diagram">
          <div class="steps-container">
            <div
              v-for="(step, index) in workflowSteps"
              :key="step.id"
              class="step-node"
              :class="getStepClass(step)"
            >
              <!-- 步骤图标 -->
              <div class="step-icon-wrapper">
                <el-icon class="step-icon">
                  <component :is="getStepIcon(step.status)" />
                </el-icon>
                <span class="step-number">{{ index + 1 }}</span>
              </div>

              <!-- 步骤信息 -->
              <div class="step-info">
                <div class="step-name">{{ step.stepName }}</div>
                <div class="step-approver">{{ step.approverName }}</div>
                <div v-if="step.approvalTime" class="step-time">
                  {{ step.approvalTime }}
                </div>
                <div v-if="step.status === 'CURRENT'" class="step-remaining">
                  剩余：{{ step.remainingHours }}小时
                </div>
              </div>

              <!-- 步骤状态 -->
              <div class="step-status">
                <el-tag :type="getStepStatusTag(step.status)" size="small">
                  {{ getStepStatusText(step.status) }}
                </el-tag>
              </div>

              <!-- 连接线 -->
              <div
                v-if="index < workflowSteps.length - 1"
                class="step-connector"
                :class="{ active: step.status === 'COMPLETED' }"
              ></div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 审批详情 -->
      <el-card class="approval-details-card" shadow="never">
        <template #header>
          <span>审批详情</span>
        </template>
        
        <el-collapse v-model="activeCollapse" accordion>
          <el-collapse-item
            v-for="(step, index) in workflowSteps"
            :key="step.id"
            :title="step.stepName"
            :name="step.id"
          >
            <template #title>
              <div class="collapse-title">
                <el-icon class="title-icon">
                  <component :is="getStepIcon(step.status)" />
                </el-icon>
                <span class="title-text">{{ step.stepName }}</span>
                <el-tag :type="getStepStatusTag(step.status)" size="small">
                  {{ getStepStatusText(step.status) }}
                </el-tag>
              </div>
            </template>

            <div class="step-detail">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="审批人">
                  {{ step.approverName }}
                </el-descriptions-item>
                <el-descriptions-item label="步骤状态">
                  <el-tag :type="getStepStatusTag(step.status)" size="small">
                    {{ getStepStatusText(step.status) }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item v-if="step.approvalTime" label="审批时间">
                  {{ step.approvalTime }}
                </el-descriptions-item>
                <el-descriptions-item v-if="step.processingTime" label="处理时长">
                  {{ step.processingTime }}
                </el-descriptions-item>
                <el-descriptions-item v-if="step.timeoutHours" label="超时时间">
                  {{ step.timeoutHours }}小时
                </el-descriptions-item>
                <el-descriptions-item v-if="step.remainingHours" label="剩余时间">
                  {{ step.remainingHours }}小时
                </el-descriptions-item>
              </el-descriptions>

              <div v-if="step.comment" class="step-comment">
                <h4>审批意见</h4>
                <div class="comment-content">{{ step.comment }}</div>
              </div>

              <div v-if="step.attachments && step.attachments.length > 0" class="step-attachments">
                <h4>相关附件</h4>
                <div class="attachment-list">
                  <div
                    v-for="attachment in step.attachments"
                    :key="attachment.id"
                    class="attachment-item"
                  >
                    <el-icon class="attachment-icon"><Document /></el-icon>
                    <span class="attachment-name">{{ attachment.name }}</span>
                    <el-button size="small" type="primary" link @click="handleDownloadAttachment(attachment)">
                      下载
                    </el-button>
                  </div>
                </div>
              </div>

              <div v-if="step.conditions" class="step-conditions">
                <h4>审批条件</h4>
                <div class="conditions-content">{{ step.conditions }}</div>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </el-card>

      <!-- 操作记录 -->
      <el-card class="operation-log-card" shadow="never">
        <template #header>
          <span>操作记录</span>
        </template>
        
        <el-timeline class="operation-timeline">
          <el-timeline-item
            v-for="(log, index) in operationLogs"
            :key="index"
            :timestamp="log.timestamp"
            :type="getLogType(log.action)"
          >
            <div class="log-content">
              <div class="log-header">
                <span class="log-action">{{ log.action }}</span>
                <span class="log-operator">{{ log.operator }}</span>
              </div>
              <div v-if="log.description" class="log-description">
                {{ log.description }}
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button @click="handleExportWorkflow">
          <el-icon><Download /></el-icon>
          导出流程图
        </el-button>
        <el-button @click="handlePrintWorkflow">
          <el-icon><Printer /></el-icon>
          打印
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
 
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Check,
  Clock,
  Close,
  Warning,
  User,
  Document,
  Download,
  Printer
} from '@element-plus/icons-vue'

// Props
interface Props {
  visible: boolean
   
  contract?: unknown
}

const props = withDefaults(defineProps<Props>(), {
  contract: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 响应式数据
const activeCollapse = ref('')

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 模拟工作流步骤数据
const workflowSteps = ref([
  {
    id: '1',
    stepName: '部门初审',
    approverName: '张主任',
    status: 'COMPLETED',
    approvalTime: '2025-06-18 10:30:00',
    processingTime: '1小时30分钟',
    comment: '材料齐全，符合部门要求，同意提交上级审批',
    timeoutHours: 24,
    attachments: [
      { id: '1', name: 'HrHr部门审核意见.pdf' }
    ],
    conditions: '申请人必须是本部门正式员工'
  },
  {
    id: '2',
    stepName: '人事处审批',
    approverName: '李处长',
    status: 'CURRENT',
    approvalTime: '',
    processingTime: '',
    comment: '',
    timeoutHours: 48,
    remainingHours: 36,
    conditions: '合同条款符合学校规定'
  },
  {
    id: '3',
    stepName: '院长审批',
    approverName: '王院长',
    status: 'PENDING',
    approvalTime: '',
    processingTime: '',
    comment: '',
    timeoutHours: 72,
    conditions: '重要合同需院长最终审批'
  }
])

// 操作记录
const operationLogs = ref([
  {
    action: '提交申请',
    operator: '申请人',
    timestamp: '2025-06-18 09:00:00',
    description: '合同申请已提交，等待部门审批'
  },
  {
    action: '部门审批通过',
    operator: '张主任',
    timestamp: '2025-06-18 10:30:00',
    description: '部门初审通过，转入人事处审批环节'
  },
  {
    action: '分配审批人',
    operator: '系统',
    timestamp: '2025-06-18 10:31:00',
    description: '已分配给李处长进行审批'
  }
])

// 计算属性
const totalSteps = computed(() => workflowSteps.value.length)
const completedSteps = computed(() => workflowSteps.value.filter(step => step.status === 'COMPLETED').length)
const progressPercentage = computed(() => Math.round((completedSteps.value / totalSteps.value) * 100))

// 获取合同状态标签
const getStatusTag = (status: string) => {
  switch (status) {
    case 'PENDING': return 'warning'
    case 'APPROVED': return 'success'
    case 'REJECTED': return 'danger'
    default: return 'info'
  }
}

// 获取合同状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'PENDING': return '审批中'
    case 'APPROVED': return '已通过'
    case 'REJECTED': return '已驳回'
    default: return status
  }
}

// 获取进度标签
const getProgressTag = (percentage: number) => {
  if (percentage === 100) return 'success'
  if (percentage >= 50) return 'primary'
  return 'warning'
}

// 获取进度状态
const getProgressStatus = () => {
  if (progressPercentage.value === 100) return 'success'
  if (workflowSteps.value.some(step => step.status === 'REJECTED')) return 'exception'
  return undefined
}

// 获取步骤类名
   
const getStepClass = (step: unknown) => {
  return {
    'step-completed': step.status === 'COMPLETED',
    'step-current': step.status === 'CURRENT',
    'step-rejected': step.status === 'REJECTED',
    'step-pending': step.status === 'PENDING'
  }
}

// 获取步骤图标
const getStepIcon = (status: string) => {
  switch (status) {
    case 'COMPLETED': return Check
    case 'CURRENT': return Clock
    case 'REJECTED': return Close
    case 'WARNING': return Warning
    default: return User
  }
}

// 获取步骤状态标签
const getStepStatusTag = (status: string) => {
  switch (status) {
    case 'COMPLETED': return 'success'
    case 'CURRENT': return 'primary'
    case 'REJECTED': return 'danger'
    case 'WARNING': return 'warning'
    default: return 'info'
  }
}

// 获取步骤状态文本
const getStepStatusText = (status: string) => {
  switch (status) {
    case 'COMPLETED': return '已完成'
    case 'CURRENT': return '进行中'
    case 'REJECTED': return '已驳回'
    case 'WARNING': return '超时警告'
    case 'PENDING': return '等待中'
    default: return status
  }
}

// 获取日志类型
const getLogType = (action: string) => {
  if (action.includes('通过') || action.includes('完成')) return 'success'
  if (action.includes('驳回') || action.includes('拒绝')) return 'danger'
  if (action.includes('警告') || action.includes('超时')) return 'warning'
  return 'primary'
}

// 下载附件
   
const handleDownloadAttachment = (attachment: unknown) => {
  ElMessage.info(`下载附件：${attachment.name}`)
}

// 导出工作流
const handleExportWorkflow = () => {
  ElMessage.info('导出流程图功能开发中...')
}

// 打印工作流
const handlePrintWorkflow = () => {
  ElMessage.info('打印功能开发中...')
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.workflow-viewer {
  max-height: 80vh;
  overflow-y: auto;
}

.contract-info-card,
.workflow-progress-card,
.approval-details-card,
.operation-log-card {
  margin-bottom: 16px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-stats {
  display: flex;
  gap: 8px;
}

.workflow-progress {
  margin-bottom: 20px;
}

.workflow-diagram {
  margin-top: 20px;
}

.steps-container {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  overflow-x: auto;
}

.step-node {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 150px;
  padding: 16px;
  border: 2px solid #dcdfe6;
  border-radius: 8px;
  background: white;
  transition: all 0.3s;
}

.step-node.step-completed {
  border-color: #67c23a;
  background: #f0f9ff;
}

.step-node.step-current {
  border-color: #409eff;
  background: #ecf5ff;
  box-shadow: 0 0 10px rgba(64, 158, 255, 0.3);
}

.step-node.step-rejected {
  border-color: #f56c6c;
  background: #fef0f0;
}

.step-icon-wrapper {
  position: relative;
  margin-bottom: 8px;
}

.step-icon {
  font-size: 24px;
  color: #409eff;
}

.step-completed .step-icon {
  color: #67c23a;
}

.step-rejected .step-icon {
  color: #f56c6c;
}

.step-number {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #409eff;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.step-completed .step-number {
  background: #67c23a;
}

.step-rejected .step-number {
  background: #f56c6c;
}

.step-info {
  text-align: center;
  margin-bottom: 8px;
}

.step-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.step-approver {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
}

.step-time {
  font-size: 12px;
  color: #909399;
}

.step-remaining {
  font-size: 12px;
  color: #e6a23c;
  font-weight: 600;
}

.step-connector {
  position: absolute;
  top: 50%;
  right: -22px;
  width: 20px;
  height: 2px;
  background: #dcdfe6;
  transform: translateY(-50%);
}

.step-connector.active {
  background: #67c23a;
}

.step-connector::after {
  content: '';
  position: absolute;
  right: -4px;
  top: -3px;
  width: 0;
  height: 0;
  border-left: 6px solid #dcdfe6;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
}

.step-connector.active::after {
  border-left-color: #67c23a;
}

.collapse-title {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.title-icon {
  font-size: 16px;
}

.title-text {
  flex: 1;
}

.step-detail {
  margin-top: 16px;
}

.step-comment,
.step-attachments,
.step-conditions {
  margin-top: 16px;
}

.step-comment h4,
.step-attachments h4,
.step-conditions h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 14px;
}

.comment-content,
.conditions-content {
  padding: 8px 12px;
  background: #f5f7fa;
  border-radius: 4px;
  color: #606266;
  font-size: 14px;
  line-height: 1.6;
}

.attachment-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f5f7fa;
  border-radius: 4px;
}

.attachment-icon {
  color: #409eff;
}

.attachment-name {
  flex: 1;
  color: #606266;
  font-size: 14px;
}

.operation-timeline {
  margin-top: 16px;
}

.log-content {
  padding-left: 8px;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.log-action {
  font-weight: 600;
  color: #303133;
}

.log-operator {
  font-size: 12px;
  color: #909399;
}

.log-description {
  font-size: 14px;
  color: #606266;
  line-height: 1.6;
}

.dialog-footer {
  text-align: right;
}
</style>
