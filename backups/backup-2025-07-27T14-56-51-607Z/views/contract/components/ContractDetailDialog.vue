<template>
  <el-dialog
    v-model="dialogVisible"
    title="合同详情"
    width="70%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="contract" class="contract-detail">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="合同编号">
          {{ contract.contractNumber }}
        </el-descriptions-item>
        <el-descriptions-item label="员工姓名">
          {{ contract.employeeName }}
        </el-descriptions-item>
        <el-descriptions-item label="合同类型">
          {{ contract.contractType }}
        </el-descriptions-item>
        <el-descriptions-item label="申请部门">
          {{ contract.department }}
        </el-descriptions-item>
        <el-descriptions-item label="当前步骤">
          {{ contract.currentStep }}
        </el-descriptions-item>
        <el-descriptions-item label="优先级">
          <el-tag :type="getPriorityTag(contract.priority)" size="small">
            {{ getPriorityText(contract.priority) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="提交时间">
          {{ contract.submitTime }}
        </el-descriptions-item>
        <el-descriptions-item label="剩余时间">
          <el-tag :type="getRemainingHoursTag(contract.remainingHours)" size="small">
            {{ contract.remainingHours }}小时
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>

      <div class="contract-content">
        <h4>合同内容</h4>
        <div class="content-box">
          {{ contract.contractContent || '合同内容加载中...' }}
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'ContractDetailDialog'
})
 
import { computed } from 'vue'

// Props
interface Props {
  visible: boolean
   
  contract?: unknown
}

const props = withDefaults(defineProps<Props>(), {
  contract: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 获取优先级标签
const getPriorityTag = (priority: string) => {
  switch (priority) {
    case 'URGENT': return 'danger'
    case 'HIGH': return 'warning'
    case 'MEDIUM': return 'primary'
    case 'LOW': return 'info'
    default: return ''
  }
}

// 获取优先级文本
const getPriorityText = (priority: string) => {
  switch (priority) {
    case 'URGENT': return '紧急'
    case 'HIGH': return '高'
    case 'MEDIUM': return '中'
    case 'LOW': return '低'
    default: return priority
  }
}

// 获取剩余时间标签
const getRemainingHoursTag = (hours: number) => {
  if (hours <= 8) return 'danger'
  if (hours <= 24) return 'warning'
  return 'success'
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.contract-detail {
  margin-top: 16px;
}

.contract-content {
  margin-top: 20px;
}

.contract-content h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.content-box {
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background: #fafafa;
  max-height: 200px;
  overflow-y: auto;
  font-size: 14px;
  line-height: 1.6;
  white-space: pre-wrap;
}

.dialog-footer {
  text-align: right;
}
</style>
