<template>
  <el-dialog
    v-model="dialogVisible"
    title="使用记录"
    width="70%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="usage-container">
      <!-- 使用统计概览 -->
      <el-row :gutter="20" class="usage-stats">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon total">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ usageStats.total }}</div>
                <div class="stats-label">总使用次数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon thisMonth">
                <el-icon><Calendar /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ usageStats.thisMonth }}</div>
                <div class="stats-label">本月使用</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon active">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ usageStats.active }}</div>
                <div class="stats-label">有效合同</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon users">
                <el-icon><User /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ usageStats.users }}</div>
                <div class="stats-label">使用人数</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 筛选条件 -->
      <div class="filter-form">
        <el-row :gutter="20">
          <el-col :span="5">
            <el-input
              v-model="filterForm.keyword"
              placeholder="搜索合同编号、使用人"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select v-model="filterForm.status" placeholder="合同状态" clearable>
              <el-option label="有效" value="ACTIVE"  />
              <el-option label="已到期" value="EXPIRED"  />
              <el-option label="已终止" value="TERMINATED"  />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
             />
          </el-col>
          <el-col :span="9">
            <el-button type="primary" @click="handleFilter">
              <el-icon><Search /></el-icon>
              筛选
            </el-button>
            <el-button @click="handleResetFilter">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
            <el-button @click="handleExportUsage">
              <el-icon><Download /></el-icon>
              导出记录
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 使用记录列表 -->
      <el-table :data="filteredUsageList" style="width: 100%">
        <el-table-column prop="contractNumber" label="合同编号" width="120"  />
        <el-table-column prop="employeeName" label="员工姓名" width="100"  />
        <el-table-column prop="department" label="部门" width="150" show-overflow-tooltip  />
        <el-table-column prop="contractType" label="合同类型" width="150">
          <template #default="scope">
            <el-tag size="small">{{ scope.row.contractType }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="templateVersion" label="模板版本" width="100">
          <template #default="scope">
            <el-tag type="primary" size="small">v{{ scope.row.templateVersion }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="150"  />
        <el-table-column prop="createBy" label="创建人" width="100"  />
        <el-table-column prop="contractStatus" label="合同状态" width="100">
          <template #default="scope">
            <el-tag :type="getContractStatusTag(scope.row.contractStatus)" size="small">
              {{ getContractStatusText(scope.row.contractStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button size="small" type="primary" link @click="handleViewContract(scope.row)">
              查看合同
            </el-button>
            <el-button size="small" type="info" link @click="handleViewUsageDetail(scope.row)">
              使用详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </div>

    <!-- 使用详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="使用详情"
      width="60%"
      append-to-body
    >
      <div v-if="currentUsage" class="usage-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="合同编号">
            {{ currentUsage.contractNumber }}
          </el-descriptions-item>
          <el-descriptions-item label="员工姓名">
            {{ currentUsage.employeeName }}
          </el-descriptions-item>
          <el-descriptions-item label="部门">
            {{ currentUsage.department }}
          </el-descriptions-item>
          <el-descriptions-item label="合同类型">
            {{ currentUsage.contractType }}
          </el-descriptions-item>
          <el-descriptions-item label="模板版本">
            v{{ currentUsage.templateVersion }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ currentUsage.createTime }}
          </el-descriptions-item>
          <el-descriptions-item label="创建人">
            {{ currentUsage.createBy }}
          </el-descriptions-item>
          <el-descriptions-item label="合同状态">
            <el-tag :type="getContractStatusTag(currentUsage.contractStatus)" size="small">
              {{ getContractStatusText(currentUsage.contractStatus) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <div class="variable-values">
          <h4>变量值</h4>
          <el-table :data="currentUsage.variableValues" style="width: 100%">
            <el-table-column prop="variableName" label="变量名" width="150">
              <template #default="scope">
                <code>{{ scope.row.variableName }}</code>
              </template>
            </el-table-column>
            <el-table-column prop="variableLabel" label="显示名称" width="150"  />
            <el-table-column prop="value" label="使用值" show-overflow-tooltip  />
          </el-table>
        </div>

        <div class="contract-preview">
          <h4>合同预览</h4>
          <div class="preview-content">
            {{ currentUsage.contractContent }}
          </div>
        </div>
      </div>
    </el-dialog>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'TemplateUsageDialog'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Document,
  Calendar,
  Check,
  User,
  Search,
  Refresh,
  Download
} from '@element-plus/icons-vue'

// Props
interface Props {
  visible: boolean
   
  template?: unknown
}

const props = withDefaults(defineProps<Props>(), {
  template: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 响应式数据
const detailDialogVisible = ref(false)
const currentUsage = ref<unknown>(null) // 修复类型：null → any

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 筛选表单
const filterForm = reactive({
  keyword: '',
  status: '',
  dateRange: [] as string[] // 修复类型：never[] → string[]
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 使用统计
const usageStats = reactive({
  total: 156,
  thisMonth: 23,
  active: 128,
  users: 45
})

// 模拟使用记录数据
const usageList = ref([
  {
    id: '1',
    contractNumber: 'HKY2025001',
    employeeName: '张三',
    department: '计算机学院',
    contractType: '事业编制聘用合同',
    templateVersion: '2.1',
    createTime: '2025-06-19 10:30:00',
    createBy: '人事专员',
    contractStatus: 'ACTIVE',
    variableValues: [
      { variableName: 'employeeName', variableLabel: '员工姓名', value: '张三' },
      { variableName: 'department', variableLabel: '部门', value: '计算机学院' },
      { variableName: 'startDate', variableLabel: '开始日期', value: '2025-01-01' }
    ],
    contractContent: '这是张三的事业编制聘用合同内容...'
  },
  {
    id: '2',
    contractNumber: 'HKY2025002',
    employeeName: '李四',
    department: '机械工程学院',
    contractType: '人事代理合同',
    templateVersion: '2.0',
    createTime: '2025-06-18 14:20:00',
    createBy: '部门主管',
    contractStatus: 'ACTIVE',
    variableValues: [
      { variableName: 'employeeName', variableLabel: '员工姓名', value: '李四' },
      { variableName: 'department', variableLabel: '部门', value: '机械工程学院' },
      { variableName: 'startDate', variableLabel: '开始日期', value: '2025-02-01' }
    ],
    contractContent: '这是李四的人事代理合同内容...'
  },
  {
    id: '3',
    contractNumber: 'HKY2025003',
    employeeName: '王五',
    department: '管理学院',
    contractType: '劳务协议',
    templateVersion: '1.0',
    createTime: '2025-06-17 09:15:00',
    createBy: '人事专员',
    contractStatus: 'EXPIRED',
    variableValues: [
      { variableName: 'employeeName', variableLabel: '员工姓名', value: '王五' },
      { variableName: 'department', variableLabel: '部门', value: '管理学院' },
      { variableName: 'startDate', variableLabel: '开始日期', value: '2024-06-01' }
    ],
    contractContent: '这是王五的劳务协议内容...'
  }
])

// 筛选后的使用记录列表
const filteredUsageList = computed(() => {
  let result = usageList.value
  
  if (filterForm.keyword) {
    result = result.filter(item => 
      item.contractNumber.toLowerCase().includes(filterForm.keyword.toLowerCase()) ||
      item.employeeName.includes(filterForm.keyword)
    )
  }
  
  if (filterForm.status) {
    result = result.filter(item => item.contractStatus === filterForm.status)
  }
  
  if (filterForm.dateRange && filterForm.dateRange.length === 2) {
    const [startDate, endDate] = filterForm.dateRange
    result = result.filter(item => {
      const createDate = item.createTime.split(' ')[0]
      return createDate >= startDate && createDate <= endDate
    })
  }
  
  pagination.total = result.length
  return result
})

// 筛选
const handleFilter = () => {
  pagination.page = 1
}

// 重置筛选
const handleResetFilter = () => {
  Object.assign(filterForm, {
    keyword: '',
    status: '',
    dateRange: []
  })
  pagination.page = 1
}

// 导出使用记录
const handleExportUsage = () => {
  ElMessage.info('导出使用记录功能开发中...')
}

// 查看合同
   
const handleViewContract = (usage: unknown) => {
  ElMessage.info(`查看合同 ${usage.contractNumber}`)
}

// 查看使用详情
   
const handleViewUsageDetail = (usage: unknown) => {
  currentUsage.value = usage
  detailDialogVisible.value = true
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
}

// 获取合同状态标签
const getContractStatusTag = (status: string) => {
  switch (status) {
    case 'ACTIVE': return 'success'
    case 'EXPIRED': return 'danger'
    case 'TERMINATED': return 'warning'
    default: return 'info'
  }
}

// 获取合同状态文本
const getContractStatusText = (status: string) => {
  switch (status) {
    case 'ACTIVE': return '有效'
    case 'EXPIRED': return '已到期'
    case 'TERMINATED': return '已终止'
    default: return status
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.usage-container {
  max-height: 70vh;
  overflow-y: auto;
}

.usage-stats {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stats-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 18px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.thisMonth {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.active {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-icon.users {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 12px;
  color: #909399;
}

.filter-form {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.usage-detail {
  margin-top: 16px;
}

.variable-values {
  margin-top: 20px;
}

.variable-values h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.contract-preview {
  margin-top: 20px;
}

.contract-preview h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.preview-content {
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background: #fafafa;
  max-height: 200px;
  overflow-y: auto;
  font-size: 14px;
  line-height: 1.6;
  white-space: pre-wrap;
}

.dialog-footer {
  text-align: right;
}
</style>
