<template>
  <div class="agreement-management">
    <!-- 页面标题和操作 -->
    <div class="page-header">
      <div class="header-title">
        <h2>协议管理</h2>
        <p>管理与合同相关的各类协议文件</p>
      </div>
      <div class="header-actions">
        <el-button @click="showBatchDialog = true" :disabled="selectedAgreements.length === 0">
          <el-icon><Operation /></el-icon>
          批量操作
        </el-button>
        <el-button @click="exportAgreements" :icon="Download">
          导出
        </el-button>
        <el-button @click="openAgreementDialog()" type="primary" :icon="Plus">
          新增协议
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-filters">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索协议编号、标题..."
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.type" placeholder="协议类型" clearable>
            <el-option label="全部类型" value=""  />
            <el-option label="保密协议" value="CONFIDENTIALITY"  />
            <el-option label="培训协议" value="TRAINING"  />
            <el-option label="竞业限制" value="NON_COMPETE"  />
            <el-option label="项目合作" value="PROJECT_COOPERATION"  />
            <el-option label="补充协议" value="SUPPLEMENT"  />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.status" placeholder="协议状态" clearable>
            <el-option label="全部状态" value=""  />
            <el-option label="草稿" value="DRAFT"  />
            <el-option label="待审批" value="PENDING_APPROVAL"  />
            <el-option label="已签署" value="SIGNED"  />
            <el-option label="执行中" value="ACTIVE"  />
            <el-option label="即将到期" value="EXPIRING"  />
            <el-option label="已到期" value="EXPIRED"  />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
           />
        </el-col>
        <el-col :span="4">
          <el-button @click="resetSearch" :icon="Refresh">重置</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 协议列表 -->
    <div class="agreement-table">
      <el-table
        v-loading="loading"
        :data="agreementList"
        @selection-change="handleSelectionChange"
        stripe
        height="calc(100vh - 300px)"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="agreementNumber" label="协议编号" width="150"  />
        <el-table-column prop="agreementTitle" label="协议标题" min-width="200" show-overflow-tooltip  />
        <el-table-column prop="agreementType" label="协议类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.agreementType)">
              {{ getTypeLabel(row.agreementType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="relatedContractName" label="关联合同" width="180" show-overflow-tooltip  />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="signDate" label="签署日期" width="120"  />
        <el-table-column prop="endDate" label="到期日期" width="120">
          <template #default="{ row }">
            <span :class="{ 'text-warning': isExpiringSoon(row.endDate) }">
              {{ row.endDate }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="signingParties" label="签署方" width="150">
          <template #default="{ row }">
            <div class="signing-parties">
              <el-tag
                v-for="party in row.signingParties?.slice(0, 2)"
                :key="party.partyId"
                size="small"
                :type="party.signStatus === 'SIGNED' ? 'success' : 'info'"
              >
                {{ party.partyName }}
              </el-tag>
              <span v-if="row.signingParties?.length > 2" class="more-parties">
                +{{ row.signingParties.length - 2 }}
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewAgreement(row)" :icon="View">
              查看
            </el-button>
            <el-button size="small" @click="openAgreementDialog(row)" :icon="Edit">
              编辑
            </el-button>
            <el-dropdown @command="handleRowAction">
              <el-button size="small" :icon="MoreFilled"   />
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="{ action: 'duplicate', row }">
                    <el-icon><CopyDocument /></el-icon>
                    复制协议
                  </el-dropdown-item>
                  <el-dropdown-item :command="{ action: 'history', row }">
                    <el-icon><Clock /></el-icon>
                    变更历史
                  </el-dropdown-item>
                  <el-dropdown-item :command="{ action: 'print', row }">
                    <el-icon><Printer /></el-icon>
                    打印协议
                  </el-dropdown-item>
                  <el-dropdown-item :command="{ action: 'delete', row }" divided>
                    <el-icon><Delete /></el-icon>
                    删除协议
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
    </div>

    <!-- 协议编辑对话框 -->
    <el-dialog
      v-model="agreementDialog.visible"
      :title="agreementDialog.title"
      width="80%"
      :close-on-click-modal="false"
      class="agreement-dialog"
    >
      <el-form
        ref="agreementFormRef"
        :model="agreementForm"
        :rules="agreementRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="协议编号" prop="agreementNumber">
              <el-input v-model="agreementForm.agreementNumber" placeholder="自动生成" readonly   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="协议类型" prop="agreementType">
              <el-select v-model="agreementForm.agreementType" placeholder="选择协议类型">
                <el-option label="保密协议" value="CONFIDENTIALITY"  />
                <el-option label="培训协议" value="TRAINING"  />
                <el-option label="竞业限制协议" value="NON_COMPETE"  />
                <el-option label="项目合作协议" value="PROJECT_COOPERATION"  />
                <el-option label="补充协议" value="SUPPLEMENT"  />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="关联合同" prop="relatedContractId">
              <el-select
                v-model="agreementForm.relatedContractId"
                placeholder="选择关联合同"
                filterable
                remote
                :remote-method="searchContracts"
                :loading="contractSearchLoading"
              >
                <el-option
                  v-for="contract in contractOptions"
                  :key="contract.id"
                  :label="`${contract.contractNumber} - ${contract.contractTitle}`"
                  :value="contract.id"
                 />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="协议标题" prop="agreementTitle">
              <el-input v-model="agreementForm.agreementTitle" placeholder="输入协议标题"   />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="协议内容" prop="agreementContent">
          <el-input
            v-model="agreementForm.agreementContent"
            type="textarea"
            :rows="6"
            placeholder="输入协议具体内容..."
            />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="生效日期" prop="startDate">
              <el-date-picker
                v-model="agreementForm.startDate"
                type="date"
                placeholder="选择生效日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
               />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="到期日期" prop="endDate">
              <el-date-picker
                v-model="agreementForm.endDate"
                type="date"
                placeholder="选择到期日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
               />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="签署日期" prop="signDate">
              <el-date-picker
                v-model="agreementForm.signDate"
                type="date"
                placeholder="选择签署日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
               />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 签署方管理 -->
        <el-form-item label="签署方">
          <div class="signing-parties-manager">
            <div
              v-for="(party, index) in agreementForm.signingParties"
              :key="index"
              class="party-item"
            >
              <el-select v-model="party.partyType" placeholder="选择签署方类型" style="width: 120px;">
                <el-option label="员工" value="EMPLOYEE"  />
                <el-option label="公司" value="COMPANY"  />
                <el-option label="第三方" value="THIRD_PARTY"  />
              </el-select>
              <el-input v-model="party.partyName" placeholder="签署方名称" style="margin-left: 10px; width: 200px;"   />
              <el-select v-model="party.signStatus" placeholder="签署状态" style="margin-left: 10px; width: 120px;">
                <el-option label="待签署" value="PENDING"  />
                <el-option label="已签署" value="SIGNED"  />
                <el-option label="拒绝签署" value="REJECTED"  />
              </el-select>
              <el-button
                @click="removeSigningParty(index)"
                :icon="Delete"
                size="small"
                style="margin-left: 10px;"
                />
            </div>
            <el-button @click="addSigningParty" :icon="Plus" size="small">
              添加签署方
            </el-button>
          </div>
        </el-form-item>

        <!-- 特殊条款 -->
        <el-form-item label="特殊条款">
          <div class="special-terms-manager">
            <div
              v-for="(term, index) in agreementForm.specialTerms"
              :key="index"
              class="term-item"
            >
              <el-input v-model="term.termType" placeholder="条款类型" style="width: 150px;"   />
              <el-input
                v-model="term.termContent"
                placeholder="条款内容"
                style="margin-left: 10px; flex: 1;"
                />
              <el-button
                @click="removeSpecialTerm(index)"
                :icon="Delete"
                size="small"
                style="margin-left: 10px;"
                />
            </div>
            <el-button @click="addSpecialTerm" :icon="Plus" size="small">
              添加特殊条款
            </el-button>
          </div>
        </el-form-item>

        <el-form-item label="附件">
          <el-upload
            v-model:file-list="agreementForm.attachments"
            :action="uploadUrl"
            :headers="uploadHeaders"
            multiple
            :limit="5"
            :on-exceed="handleExceed"
          >
            <el-button :icon="Upload">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持pdf、doc、docx格式，单个文件不超过10MB，最多上传5个文件
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="agreementDialog.visible = false">取消</el-button>
        <el-button @click="saveAgreementDraft" :loading="saving">保存草稿</el-button>
        <el-button @click="submitAgreement" type="primary" :loading="saving">
          提交审批
        </el-button>
      </template>
    </el-dialog>

    <!-- 批量操作对话框 -->
    <el-dialog v-model="showBatchDialog" title="批量操作" width="500px">
      <el-form :model="batchForm" label-width="100px">
        <el-form-item label="操作类型">
          <el-radio-group v-model="batchForm.action">
            <el-radio label="updateStatus">批量更新状态</el-radio>
            <el-radio label="export">批量导出</el-radio>
            <el-radio label="delete">批量删除</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="batchForm.action === 'updateStatus'" label="目标状态">
          <el-select v-model="batchForm.targetStatus" placeholder="选择状态">
            <el-option label="草稿" value="DRAFT"  />
            <el-option label="待审批" value="PENDING_APPROVAL"  />
            <el-option label="已签署" value="SIGNED"  />
            <el-option label="执行中" value="ACTIVE"  />
            <el-option label="已终止" value="TERMINATED"  />
          </el-select>
        </el-form-item>

        <el-form-item label="选中数量">
          <el-text>{{ selectedAgreements.length }} 个协议</el-text>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showBatchDialog = false">取消</el-button>
        <el-button @click="executeBatchOperation" type="primary" :loading="batchLoading">
          执行操作
        </el-button>
      </template>
    </el-dialog>

    <!-- 协议详情对话框 -->
    <el-dialog v-model="viewDialog.visible" title="协议详情" width="70%">
      <div v-if="viewDialog.agreement" class="agreement-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="协议编号">
            {{ viewDialog.agreement.agreementNumber }}
          </el-descriptions-item>
          <el-descriptions-item label="协议类型">
            <el-tag :type="getTypeTagType(viewDialog.agreement.agreementType)">
              {{ getTypeLabel(viewDialog.agreement.agreementType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="协议标题" :span="2">
            {{ viewDialog.agreement.agreementTitle }}
          </el-descriptions-item>
          <el-descriptions-item label="关联合同">
            {{ viewDialog.agreement.relatedContractName }}
          </el-descriptions-item>
          <el-descriptions-item label="协议状态">
            <el-tag :type="getStatusTagType(viewDialog.agreement.status)">
              {{ getStatusLabel(viewDialog.agreement.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="生效日期">
            {{ viewDialog.agreement.startDate }}
          </el-descriptions-item>
          <el-descriptions-item label="到期日期">
            {{ viewDialog.agreement.endDate }}
          </el-descriptions-item>
          <el-descriptions-item label="协议内容" :span="2">
            <div class="agreement-content">{{ viewDialog.agreement.agreementContent }}</div>
          </el-descriptions-item>
        </el-descriptions>

        <!-- 签署方信息 -->
        <div v-if="viewDialog.agreement.signingParties?.length" class="signing-parties-detail">
          <h4>签署方信息</h4>
          <el-table :data="viewDialog.agreement.signingParties" border>
            <el-table-column prop="partyName" label="签署方名称"  />
            <el-table-column prop="partyType" label="类型">
              <template #default="{ row }">
                {{ getPartyTypeLabel(row.partyType) }}
              </template>
            </el-table-column>
            <el-table-column prop="signStatus" label="签署状态">
              <template #default="{ row }">
                <el-tag :type="row.signStatus === 'SIGNED' ? 'success' : 'info'">
                  {{ getSignStatusLabel(row.signStatus) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="signDate" label="签署时间"  />
          </el-table>
        </div>

        <!-- 特殊条款 -->
        <div v-if="viewDialog.agreement.specialTerms?.length" class="special-terms-detail">
          <h4>特殊条款</h4>
          <div
            v-for="(term, index) in viewDialog.agreement.specialTerms"
            :key="index"
            class="term-detail"
          >
            <strong>{{ term.termType }}:</strong>
            <p>{{ term.termContent }}</p>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="viewDialog.visible = false">关闭</el-button>
        <el-button @click="printAgreement(viewDialog.agreement)" :icon="Printer">
          打印协议
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'AgreementManagement'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  Edit,
  View,
  Delete,
  Download,
  Upload,
  Operation,
  MoreFilled,
  CopyDocument,
  Clock,
  Printer
} from '@element-plus/icons-vue'

// 协议接口定义
interface Agreement {
  id?: string
  agreementNumber: string
  agreementType: string
  relatedContractId: string
  relatedContractName?: string
  agreementTitle: string
  agreementContent: string
  startDate: string
  endDate?: string
  signDate?: string
  status: string
  signingParties?: SigningParty[]
  specialTerms?: SpecialTerm[]
   
  attachments?: unknown[]
  createTime?: string
  updateTime?: string
}

interface SigningParty {
  partyType: string
  partyId: string
  partyName: string
  signDate?: string
  signStatus: string
}

interface SpecialTerm {
  termType: string
  termContent: string
  effectiveDate?: string
  expiryDate?: string
}

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const batchLoading = ref(false)
const contractSearchLoading = ref(false)

const agreementList = ref<Agreement[]>([])
const selectedAgreements = ref<Agreement[]>([])
const contractOptions = ref<any[]>([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  type: '',
  status: '',
  dateRange: []
})

// 分页
const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

// 协议表单
const agreementFormRef = ref()
const agreementForm = reactive<Agreement>({
  agreementNumber: '',
  agreementType: '',
  relatedContractId: '',
  agreementTitle: '',
  agreementContent: '',
  startDate: '',
  endDate: '',
  signDate: '',
  status: 'DRAFT',
  signingParties: [],
  specialTerms: [],
  attachments: []
})

// 表单验证规则
const agreementRules = {
  agreementType: [{ required: true, message: '请选择协议类型', trigger: 'change' }],
  relatedContractId: [{ required: true, message: '请选择关联合同', trigger: 'change' }],
  agreementTitle: [{ required: true, message: '请输入协议标题', trigger: 'blur' }],
  agreementContent: [{ required: true, message: '请输入协议内容', trigger: 'blur' }],
  startDate: [{ required: true, message: '请选择生效日期', trigger: 'change' }]
}

// 对话框状态
const agreementDialog = reactive({
  visible: false,
  title: '新增协议',
  isEdit: false
})

const viewDialog = reactive({
  visible: false,
  agreement: null as Agreement | null
})

const showBatchDialog = ref(false)

// 批量操作表单
const batchForm = reactive({
  action: 'updateStatus',
  targetStatus: ''
})

// 文件上传配置
const uploadUrl = ref('/api/upload')
const uploadHeaders = ref({
  'Authorization': `Bearer ${localStorage.getItem('token')}`
})

// 获取协议列表
const fetchAgreementList = async () => {
  loading.value = true
  try {
    // 模拟API调用
    const response = await mockFetchAgreements()
    agreementList.value = response.data
    pagination.total = response.total
  } catch (__error) {
    ElMessage.error('获取协议列表失败')
  } finally {
    loading.value = false
  }
}

// 模拟获取协议数据
const mockFetchAgreements = async () => {
  await new Promise(resolve => setTimeout(resolve, 500))
  
  const mockData = [
    {
      id: '1',
      agreementNumber: 'AGR-2025-001',
      agreementType: 'CONFIDENTIALITY',
      relatedContractId: 'CON-001',
      relatedContractName: '张三-软件工程师劳动合同',
      agreementTitle: '员工保密协议',
      agreementContent: '甲方与乙方就保密事项达成如下协议...',
      startDate: '2025-01-01',
      endDate: '2027-12-31',
      signDate: '2025-01-01',
      status: 'SIGNED',
      signingParties: [
        { partyType: 'EMPLOYEE', partyId: 'EMP001', partyName: '张三', signStatus: 'SIGNED', signDate: '2025-01-01' },
        { partyType: 'COMPANY', partyId: 'COMP001', partyName: '杭州科技学院', signStatus: 'SIGNED', signDate: '2025-01-01' }
      ],
      specialTerms: [
        { termType: '违约责任', termContent: '违反保密义务的，承担相应法律责任' }
      ]
    },
    {
      id: '2',
      agreementNumber: 'AGR-2025-002',
      agreementType: 'TRAINING',
      relatedContractId: 'CON-002',
      relatedContractName: '李四-高级开发工程师劳动合同',
      agreementTitle: '技术培训协议',
      agreementContent: '公司为员工提供专业技术培训，双方约定...',
      startDate: '2025-01-15',
      endDate: '2025-12-31',
      status: 'ACTIVE',
      signingParties: [
        { partyType: 'EMPLOYEE', partyId: 'EMP002', partyName: '李四', signStatus: 'SIGNED', signDate: '2025-01-15' },
        { partyType: 'COMPANY', partyId: 'COMP001', partyName: '杭州科技学院', signStatus: 'SIGNED', signDate: '2025-01-15' }
      ]
    }
  ]
  
  return {
    data: mockData,
    total: mockData.length
  }
}

// 搜索合同
const searchContracts = async (query: string) => {
  if (!query) return
  
  contractSearchLoading.value = true
  try {
    // 模拟搜索合同
    const mockContracts = [
      { id: 'CON-001', contractNumber: 'CON-2025-001', contractTitle: '张三-软件工程师劳动合同' },
      { id: 'CON-002', contractNumber: 'CON-2025-002', contractTitle: '李四-高级开发工程师劳动合同' },
      { id: 'CON-003', contractNumber: 'CON-2025-003', contractTitle: '王五-项目经理劳动合同' }
    ]
    
    contractOptions.value = mockContracts.filter(contract => 
      contract.contractNumber.includes(query) || contract.contractTitle.includes(query)
    )
  } finally {
    contractSearchLoading.value = false
  }
}

// 处理搜索
const handleSearch = () => {
  pagination.current = 1
  fetchAgreementList()
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    type: '',
    status: '',
    dateRange: []
  })
  handleSearch()
}

// 分页事件
const handleSizeChange = (size: number) => {
  pagination.size = size
  fetchAgreementList()
}

const handleCurrentChange = (current: number) => {
  pagination.current = current
  fetchAgreementList()
}

// 选择变更
const handleSelectionChange = (selection: Agreement[]) => {
  selectedAgreements.value = selection
}

// 打开协议对话框
const openAgreementDialog = (agreement?: Agreement) => {
  if (agreement) {
    agreementDialog.isEdit = true
    agreementDialog.title = '编辑协议'
    Object.assign(agreementForm, agreement)
  } else {
    agreementDialog.isEdit = false
    agreementDialog.title = '新增协议'
    resetAgreementForm()
    generateAgreementNumber()
  }
  agreementDialog.visible = true
}

// 重置协议表单
const resetAgreementForm = () => {
  Object.assign(agreementForm, {
    agreementNumber: '',
    agreementType: '',
    relatedContractId: '',
    agreementTitle: '',
    agreementContent: '',
    startDate: '',
    endDate: '',
    signDate: '',
    status: 'DRAFT',
    signingParties: [],
    specialTerms: [],
    attachments: []
  })
}

// 生成协议编号
const generateAgreementNumber = () => {
  const year = new Date().getFullYear()
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  agreementForm.agreementNumber = `AGR-${year}-${random}`
}

// 添加签署方
const addSigningParty = () => {
  agreementForm.signingParties?.push({
    partyType: 'EMPLOYEE',
    partyId: '',
    partyName: '',
    signStatus: 'PENDING'
  })
}

// 移除签署方
const removeSigningParty = (index: number) => {
  agreementForm.signingParties?.splice(index, 1)
}

// 添加特殊条款
const addSpecialTerm = () => {
  agreementForm.specialTerms?.push({
    termType: '',
    termContent: ''
  })
}

// 移除特殊条款
const removeSpecialTerm = (index: number) => {
  agreementForm.specialTerms?.splice(index, 1)
}

// 保存草稿
const saveAgreementDraft = async () => {
  saving.value = true
  try {
    agreementForm.status = 'DRAFT'
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('草稿保存成功')
    agreementDialog.visible = false
    fetchAgreementList()
  } catch (__error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 提交审批
const submitAgreement = async () => {
  const valid = await agreementFormRef.value?.validate()
  if (!valid) return
  
  saving.value = true
  try {
    agreementForm.status = 'PENDING_APPROVAL'
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('提交审批成功')
    agreementDialog.visible = false
    fetchAgreementList()
  } catch (__error) {
    ElMessage.error('提交失败')
  } finally {
    saving.value = false
  }
}

// 查看协议详情
const viewAgreement = (agreement: Agreement) => {
  viewDialog.agreement = agreement
  viewDialog.visible = true
}

// 行操作处理
const handleRowAction = async ({ action, row }: { action: string; row: Agreement }) => {
  switch (action) {
    case 'duplicate':
      await duplicateAgreement(row)
      break
    case 'history':
      await viewChangeHistory(row)
      break
    case 'print':
      await printAgreement(row)
      break
    case 'delete':
      await deleteAgreement(row)
      break
  }
}

// 复制协议
const duplicateAgreement = async (agreement: Agreement) => {
  const newAgreement = { ...agreement }
  delete newAgreement.id
  generateAgreementNumber()
  newAgreement.agreementNumber = agreementForm.agreementNumber
  newAgreement.status = 'DRAFT'
  newAgreement.agreementTitle = `${agreement.agreementTitle} - 副本`
  
  openAgreementDialog(newAgreement)
}

// 查看变更历史
const viewChangeHistory = async (agreement: Agreement) => {
  ElMessage.info('查看变更历史功能开发中...')
}

// 打印协议
const printAgreement = async (agreement: Agreement) => {
  ElMessage.info('协议打印功能开发中...')
}

// 删除协议
const deleteAgreement = async (agreement: Agreement) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除协议"${agreement.agreementTitle}"吗？`,
      '确认删除',
      { type: 'warning' }
    )
    
    ElMessage.success('删除成功')
    fetchAgreementList()
  } catch {
    // 用户取消
  }
}

// 执行批量操作
const executeBatchOperation = async () => {
  if (selectedAgreements.value.length === 0) {
    ElMessage.warning('请先选择协议')
    return
  }
  
  batchLoading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    switch (batchForm.action) {
      case 'updateStatus':
        ElMessage.success(`已批量更新${selectedAgreements.value.length}个协议状态`)
        break
      case 'export':
        ElMessage.success(`已导出${selectedAgreements.value.length}个协议`)
        break
      case 'delete':
        ElMessage.success(`已删除${selectedAgreements.value.length}个协议`)
        break
    }
    
    showBatchDialog.value = false
    fetchAgreementList()
  } catch (__error) {
    ElMessage.error('批量操作失败')
  } finally {
    batchLoading.value = false
  }
}

// 导出协议
const exportAgreements = () => {
  ElMessage.info('协议导出功能开发中...')
}

// 文件上传限制
const handleExceed = () => {
  ElMessage.warning('最多只能上传5个文件')
}

// 工具函数
const getTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    'CONFIDENTIALITY': '保密协议',
    'TRAINING': '培训协议',
    'NON_COMPETE': '竞业限制',
    'PROJECT_COOPERATION': '项目合作',
    'SUPPLEMENT': '补充协议'
  }
  return labels[type] || type
}

const getTypeTagType = (type: string) => {
  const types: Record<string, string> = {
    'CONFIDENTIALITY': 'warning',
    'TRAINING': 'success',
    'NON_COMPETE': 'danger',
    'PROJECT_COOPERATION': 'primary',
    'SUPPLEMENT': 'info'
  }
  return types[type] || ''
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    'DRAFT': '草稿',
    'PENDING_APPROVAL': '待审批',
    'APPROVED': '已审批',
    'SIGNED': '已签署',
    'ACTIVE': '执行中',
    'EXPIRING': '即将到期',
    'EXPIRED': '已到期',
    'TERMINATED': '已终止'
  }
  return labels[status] || status
}

const getStatusTagType = (status: string) => {
  const types: Record<string, string> = {
    'DRAFT': 'info',
    'PENDING_APPROVAL': 'warning',
    'APPROVED': 'primary',
    'SIGNED': 'success',
    'ACTIVE': 'success',
    'EXPIRING': 'warning',
    'EXPIRED': 'danger',
    'TERMINATED': 'danger'
  }
  return types[status] || ''
}

const getPartyTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    'EMPLOYEE': '员工',
    'COMPANY': '公司',
    'THIRD_PARTY': '第三方'
  }
  return labels[type] || type
}

const getSignStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    'PENDING': '待签署',
    'SIGNED': '已签署',
    'REJECTED': '拒绝签署'
  }
  return labels[status] || status
}

const isExpiringSoon = (endDate: string) => {
  if (!endDate) return false
  const end = new Date(endDate).getTime()
  const now = new Date().getTime()
  const thirtyDays = 30 * 24 * 60 * 60 * 1000
  return end - now <= thirtyDays && end > now
}

// 生命周期
onMounted(() => {
  fetchAgreementList()
})
</script>

<style scoped>
.agreement-management {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-title h2 {
  margin: 0 0 8px 0;
  color: #333;
}

.header-title p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.search-filters {
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.agreement-table {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.signing-parties {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.more-parties {
  font-size: 12px;
  color: #999;
}

.text-warning {
  color: #f56c6c;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.agreement-dialog {
  --el-dialog-content-font-size: 14px;
}

.signing-parties-manager,
.special-terms-manager {
  width: 100%;
}

.party-item,
.term-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.agreement-detail {
  max-height: 60vh;
  overflow-y: auto;
}

.agreement-content {
  max-height: 200px;
  overflow-y: auto;
  padding: 10px;
  background: #f5f5f5;
  border-radius: 4px;
  white-space: pre-wrap;
}

.signing-parties-detail,
.special-terms-detail {
  margin-top: 20px;
}

.signing-parties-detail h4,
.special-terms-detail h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.term-detail {
  margin-bottom: 15px;
  padding: 10px;
  background: #f9f9f9;
  border-radius: 4px;
}

.term-detail strong {
  color: #333;
}

.term-detail p {
  margin: 5px 0 0 0;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .agreement-management {
    padding: 10px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .search-filters .el-row {
    flex-direction: column;
  }
  
  .search-filters .el-col {
    width: 100% !important;
    margin-bottom: 10px;
  }
}
</style>