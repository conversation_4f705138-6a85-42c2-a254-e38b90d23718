<template>
  <div class="contract-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>合同管理</h2>
      <p>管理教职工聘用合同的全生命周期，包括新签、续签、变更、终止和到期提醒</p>
    </div>

    <!-- 功能导航卡片 -->
    <el-row :gutter="20" class="function-cards">
      <el-col :span="8">
        <el-card class="function-card" shadow="hover" @click="navigateTo('contract-information')">
          <div class="card-content">
            <div class="card-icon contract-info">
              <el-icon><Document /></el-icon>
            </div>
            <div class="card-info">
              <h3>合同信息管理</h3>
              <p>管理合同新签、续签、变更、终止等全流程操作</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card class="function-card" shadow="hover" @click="navigateTo('expiry-reminder')">
          <div class="card-content">
            <div class="card-icon reminder">
              <el-icon><Bell /></el-icon>
            </div>
            <div class="card-info">
              <h3>到期提醒</h3>
              <p>配置合同到期提醒规则，管理提醒记录</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card class="function-card" shadow="hover" @click="navigateTo('external-staff-contract')">
          <div class="card-content">
            <div class="card-icon external-staff">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="card-info">
              <h3>编外人员合同</h3>
              <p>管理劳务协议、劳务派遣、兼职等编外人员合同</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="function-cards">
      <el-col :span="8">
        <el-card class="function-card" shadow="hover" @click="navigateTo('template-management')">
          <div class="card-content">
            <div class="card-icon template">
              <el-icon><Files /></el-icon>
            </div>
            <div class="card-info">
              <h3>合同模板管理</h3>
              <p>创建和管理合同模板，支持版本控制</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card class="function-card" shadow="hover" @click="navigateTo('approval-workflow')">
          <div class="card-content">
            <div class="card-icon workflow">
              <el-icon><Operation /></el-icon>
            </div>
            <div class="card-info">
              <h3>审批工作流</h3>
              <p>管理合同审批流程，跟踪审批状态</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card class="function-card" shadow="hover" @click="navigateTo('statistics')">
          <div class="card-content">
            <div class="card-icon statistics">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="card-info">
              <h3>统计分析</h3>
              <p>查看合同统计数据和履行情况分析</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 合同概览统计 -->
    <el-row :gutter="20" class="overview-stats">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ overviewStats.total }}</div>
              <div class="stats-label">合同总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon active">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ overviewStats.active }}</div>
              <div class="stats-label">有效合同</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon expiring">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ overviewStats.expiring }}</div>
              <div class="stats-label">即将到期</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon thisMonth">
              <el-icon><Star /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ overviewStats.thisMonth }}</div>
              <div class="stats-label">本月新签</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近合同动态 -->
    <el-card class="recent-activities" shadow="never">
      <template #header>
        <div class="card-header">
          <span>最近合同动态</span>
          <el-button type="primary" size="small" @click="viewAllActivities">查看全部</el-button>
        </div>
      </template>
      
      <el-table :data="recentActivities" style="width: 100%">
        <el-table-column prop="employeeName" label="教职工" width="100"  />
        <el-table-column prop="contractNumber" label="合同编号" width="120"  />
        <el-table-column prop="activityType" label="操作类型" width="120">
          <template #default="scope">
            <el-tag :type="getActivityTypeTag(scope.row.activityType)" size="small">
              {{ scope.row.activityTypeName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="contractType" label="合同类型" width="120"  />
        <el-table-column prop="startDate" label="合同开始日期" width="120"  />
        <el-table-column prop="endDate" label="合同结束日期" width="120"  />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusTag(scope.row.status)" size="small">
              {{ scope.row.statusName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="operationTime" label="操作时间" width="150"  />
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button size="small" type="primary" link @click="viewDetail(scope.row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 待处理事项 -->
    <el-card class="pending-tasks" shadow="never" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>待处理事项</span>
          <el-badge :value="pendingTasks.length" class="badge">
            <el-button type="warning" size="small" @click="viewAllPendingTasks">处理全部</el-button>
          </el-badge>
        </div>
      </template>
      
      <el-table :data="pendingTasks" style="width: 100%">
        <el-table-column prop="taskType" label="任务类型" width="120">
          <template #default="scope">
            <el-tag :type="getTaskTypeTag(scope.row.taskType)" size="small">
              {{ scope.row.taskTypeName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="employeeName" label="相关教职工" width="120"  />
        <el-table-column prop="contractNumber" label="合同编号" width="120"  />
        <el-table-column prop="description" label="任务描述" show-overflow-tooltip  />
        <el-table-column prop="priority" label="优先级" width="100">
          <template #default="scope">
            <el-tag :type="getPriorityTag(scope.row.priority)" size="small">
              {{ scope.row.priorityName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="dueDate" label="截止日期" width="120"  />
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button size="small" type="primary" link @click="handleTask(scope.row)">
              立即处理
            </el-button>
            <el-button size="small" type="info" link @click="viewTaskDetail(scope.row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Document,
  Bell,
  UserFilled,
  Files,
  Operation,
  DataAnalysis,
  Check,
  Warning,
  Star
} from '@element-plus/icons-vue'

const router = useRouter()

// 概览统计数据
const overviewStats = reactive({
  total: 1256,
  active: 1089,
  expiring: 45,
  thisMonth: 23
})

// 最近合同动态
const recentActivities = ref([
  {
    id: '1',
    employeeName: '张三',
    contractNumber: 'HKY2025001',
    activityType: 'NEW_CONTRACT',
    activityTypeName: '新签合同',
    contractType: '事业编制聘用合同',
    startDate: '2025-06-01',
    endDate: '2028-05-31',
    status: 'ACTIVE',
    statusName: '有效',
    operationTime: '2025-06-19 10:30:00'
  },
  {
    id: '2',
    employeeName: '李四',
    contractNumber: 'HKY2025002',
    activityType: 'RENEWAL',
    activityTypeName: '续签合同',
    contractType: '事业编制聘用合同',
    startDate: '2025-07-01',
    endDate: '2028-06-30',
    status: 'PENDING_APPROVAL',
    statusName: '待审批',
    operationTime: '2025-06-19 09:15:00'
  },
  {
    id: '3',
    employeeName: '王五',
    contractNumber: 'HKY2025003',
    activityType: 'TERMINATION',
    activityTypeName: '终止合同',
    contractType: '劳务协议',
    startDate: '2024-01-01',
    endDate: '2025-06-15',
    status: 'TERMINATED',
    statusName: '已终止',
    operationTime: '2025-06-19 08:45:00'
  }
])

// 待处理事项
const pendingTasks = ref([
  {
    id: '1',
    taskType: 'EXPIRY_REMINDER',
    taskTypeName: '到期提醒',
    employeeName: '赵六',
    contractNumber: 'HKY2024156',
    description: '合同将于30天后到期，需要办理续签手续',
    priority: 'HIGH',
    priorityName: '高',
    dueDate: '2025-07-19'
  },
  {
    id: '2',
    taskType: 'APPROVAL_PENDING',
    taskTypeName: '待审批',
    employeeName: '孙七',
    contractNumber: 'HKY2025004',
    description: '新签合同待人事处审批',
    priority: 'MEDIUM',
    priorityName: '中',
    dueDate: '2025-06-25'
  },
  {
    id: '3',
    taskType: 'DOCUMENT_UPLOAD',
    taskTypeName: '文档上传',
    employeeName: '周八',
    contractNumber: 'HKY2025005',
    description: '需要上传已签署的合同文档',
    priority: 'LOW',
    priorityName: '低',
    dueDate: '2025-06-30'
  }
])

// 导航到具体功能页面
const navigateTo = (routeName: string) => {
  router.push({ name: `contract-${routeName}` })
}

// 查看全部活动
const viewAllActivities = () => {
  router.push({ name: 'contract-contract-information' })
}

// 查看全部待处理事项
const viewAllPendingTasks = () => {
  ElMessage.info('查看全部待处理事项功能开发中...')
}

// 查看详情
   
const viewDetail = (record: unknown) => {
  ElMessage.info(`查看合同 ${record.contractNumber} 的详细信息`)
}

// 处理任务
   
const handleTask = (task: unknown) => {
  ElMessage.info(`处理任务：${task.description}`)
}

// 查看任务详情
   
const viewTaskDetail = (task: unknown) => {
  ElMessage.info(`查看任务详情：${task.description}`)
}

// 获取活动类型标签
const getActivityTypeTag = (type: string) => {
  switch (type) {
    case 'NEW_CONTRACT': return 'success'
    case 'RENEWAL': return 'primary'
    case 'MODIFICATION': return 'warning'
    case 'TERMINATION': return 'danger'
    default: return ''
  }
}

// 获取状态标签
const getStatusTag = (status: string) => {
  switch (status) {
    case 'ACTIVE': return 'success'
    case 'PENDING_APPROVAL': return 'warning'
    case 'TERMINATED': return 'info'
    case 'EXPIRED': return 'danger'
    default: return ''
  }
}

// 获取任务类型标签
const getTaskTypeTag = (type: string) => {
  switch (type) {
    case 'EXPIRY_REMINDER': return 'warning'
    case 'APPROVAL_PENDING': return 'primary'
    case 'DOCUMENT_UPLOAD': return 'info'
    default: return ''
  }
}

// 获取优先级标签
const getPriorityTag = (priority: string) => {
  switch (priority) {
    case 'HIGH': return 'danger'
    case 'MEDIUM': return 'warning'
    case 'LOW': return 'info'
    default: return ''
  }
}

// 初始化
onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.contract-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.function-cards {
  margin-bottom: 30px;
}

.function-card {
  cursor: pointer;
  transition: all 0.3s;
  height: 120px;
}

.function-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 10px;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 28px;
  color: white;
}

.card-icon.contract-info {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-icon.reminder {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.card-icon.external-staff {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card-icon.template {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-icon.workflow {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.card-icon.statistics {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.card-info h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.card-info p {
  margin: 0;
  color: #909399;
  font-size: 12px;
  line-height: 1.4;
}

.overview-stats {
  margin-bottom: 30px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stats-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 20px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.active {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.expiring {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.thisMonth {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 12px;
  color: #909399;
}

.recent-activities,
.pending-tasks {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.badge {
  margin-left: 8px;
}
</style>
