<template>
  <div class="contract-term-calculator">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>合同期限计算</h2>
      <p>计算合同期限、工龄累计和相关时间参数</p>
    </div>

    <!-- 计算输入卡片 -->
    <el-card class="calculation-input" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>期限计算</h3>
          <el-button type="primary" @click="handleCalculate">
            <el-icon><Calculator /></el-icon>
            开始计算
          </el-button>
        </div>
      </template>
      
      <el-form ref="formRef" :model="calculationForm" :rules="formRules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="合同类型" prop="contractType">
              <el-select v-model="calculationForm.contractType" placeholder="请选择合同类型">
                <el-option
                  v-for="item in contractTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                 />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="员工姓名" prop="employeeName">
              <el-input v-model="calculationForm.employeeName" placeholder="请输入员工姓名"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同开始日期" prop="startDate">
              <el-date-picker
                v-model="calculationForm.startDate"
                type="date"
                placeholder="选择开始日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
               />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同结束日期" prop="endDate">
              <el-date-picker
                v-model="calculationForm.endDate"
                type="date"
                placeholder="选择结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
               />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="试用期（月）" prop="probationMonths">
              <el-input-number
                v-model="calculationForm.probationMonths"
                :min="0"
                :max="12"
                placeholder="试用期月数"
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工作经验（年）" prop="workExperience">
              <el-input-number
                v-model="calculationForm.workExperience"
                :min="0"
                :max="50"
                placeholder="工作经验年数"
                />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 计算结果展示 -->
    <el-card class="calculation-results" shadow="never" v-if="calculationResults.isCalculated">
      <template #header>
        <div class="card-header">
          <h3>计算结果</h3>
          <div class="header-actions">
            <el-button @click="handleExportResults">
              <el-icon><Download /></el-icon>
              导出结果
            </el-button>
            <el-button type="primary" @click="handleSaveResults">
              <el-icon><DocumentCopy /></el-icon>
              保存结果
            </el-button>
          </div>
        </div>
      </template>
      
      <!-- 基本信息 -->
      <div class="result-section">
        <h4>基本信息</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>合同期限</label>
              <span class="value">{{ calculationResults.contractDuration }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>试用期</label>
              <span class="value">{{ calculationResults.probationPeriod }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>正式期限</label>
              <span class="value">{{ calculationResults.formalPeriod }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 时间统计 -->
      <div class="result-section">
        <h4>时间统计</h4>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon days">
                <el-icon><Calendar /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ calculationResults.totalDays }}</div>
                <div class="stat-label">总天数</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon months">
                <el-icon><Calendar /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ calculationResults.totalMonths }}</div>
                <div class="stat-label">总月数</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon years">
                <el-icon><Calendar /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ calculationResults.totalYears }}</div>
                <div class="stat-label">总年数</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon working-days">
                <el-icon><Calendar /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ calculationResults.workingDays }}</div>
                <div class="stat-label">工作日</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 工龄计算 -->
      <div class="result-section">
        <h4>工龄计算</h4>
        <el-table :data="workingAgeData" style="width: 100%">
          <el-table-column prop="type" label="工龄类型" width="150"  />
          <el-table-column prop="startDate" label="开始日期" width="120"  />
          <el-table-column prop="endDate" label="结束日期" width="120"  />
          <el-table-column prop="duration" label="期限" width="120"  />
          <el-table-column prop="totalDays" label="总天数" width="100"  />
          <el-table-column prop="totalMonths" label="总月数" width="100"  />
          <el-table-column prop="totalYears" label="总年数" width="100"  />
          <el-table-column prop="description" label="说明" show-overflow-tooltip  />
        </el-table>
      </div>

      <!-- 重要时间节点 -->
      <div class="result-section">
        <h4>重要时间节点</h4>
        <el-timeline>
          <el-timeline-item
            v-for="milestone in timeMilestones"
            :key="milestone.id"
            :timestamp="milestone.date"
            :type="milestone.type"
          >
            <div class="milestone-item">
              <div class="milestone-title">{{ milestone.title }}</div>
              <div class="milestone-description">{{ milestone.description }}</div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>

      <!-- 假期统计 -->
      <div class="result-section">
        <h4>假期统计</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="holiday-stat">
              <h5>年假</h5>
              <div class="holiday-info">
                <span class="holiday-days">{{ calculationResults.annualLeave }}</span>
                <span class="holiday-unit">天</span>
              </div>
              <div class="holiday-description">
                根据工作年限计算的年假天数
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="holiday-stat">
              <h5>法定假日</h5>
              <div class="holiday-info">
                <span class="holiday-days">{{ calculationResults.legalHolidays }}</span>
                <span class="holiday-unit">天</span>
              </div>
              <div class="holiday-description">
                合同期内的法定假日天数
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="holiday-stat">
              <h5>周末</h5>
              <div class="holiday-info">
                <span class="holiday-days">{{ calculationResults.weekends }}</span>
                <span class="holiday-unit">天</span>
              </div>
              <div class="holiday-description">
                合同期内的周末天数
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 批量计算 -->
    <el-card class="batch-calculation" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>批量计算</h3>
          <div class="header-actions">
            <el-upload
              ref="uploadRef"
              :show-file-list="false"
              :on-change="handleFileUpload"
              accept=".xlsx,.xls"
            >
              <el-button>
                <el-icon><Upload /></el-icon>
                上传文件
              </el-button>
            </el-upload>
            <el-button type="primary" @click="handleBatchCalculate">
              <el-icon><Operation /></el-icon>
              批量计算
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="batch-content">
        <div class="upload-tips">
          <h4>使用说明</h4>
          <ul>
            <li>支持Excel格式文件（.xlsx, .xls）</li>
            <li>需要包含：员工姓名、合同类型、开始日期、结束日期等字段</li>
            <li>可以下载标准模板进行填写</li>
          </ul>
          <el-button @click="handleDownloadTemplate">
            <el-icon><Download /></el-icon>
            下载模板
          </el-button>
        </div>
        
        <el-table :data="batchData" style="width: 100%">
          <el-table-column prop="employeeName" label="员工姓名" width="100"  />
          <el-table-column prop="contractType" label="合同类型" width="150"  />
          <el-table-column prop="startDate" label="开始日期" width="120"  />
          <el-table-column prop="endDate" label="结束日期" width="120"  />
          <el-table-column prop="duration" label="合同期限" width="120"  />
          <el-table-column prop="workingDays" label="工作日" width="100"  />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag 
                :type="scope.row.status === 'calculated' ? 'success' : 'info'"
                size="small"
              >
                {{ scope.row.status === 'calculated' ? '已计算' : '待计算' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button size="small" @click="handleCalculateSingle(scope.row)">
                计算
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 计算历史 -->
    <el-card class="calculation-history" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>计算历史</h3>
          <el-button @click="handleClearHistory">
            <el-icon><Delete /></el-icon>
            清空历史
          </el-button>
        </div>
      </template>
      
      <el-table :data="calculationHistory" style="width: 100%">
        <el-table-column prop="employeeName" label="员工姓名" width="100"  />
        <el-table-column prop="contractType" label="合同类型" width="150"  />
        <el-table-column prop="calculationDate" label="计算日期" width="150"  />
        <el-table-column prop="duration" label="合同期限" width="120"  />
        <el-table-column prop="workingDays" label="工作日" width="100"  />
        <el-table-column prop="operator" label="操作人" width="100"  />
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button size="small" @click="handleViewHistory(scope.row)">
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'ContractTermCalculator'
})
 
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Calculator,
  Download,
  DocumentCopy,
  Calendar,
  Upload,
  Operation,
  Delete
} from '@element-plus/icons-vue'

// 响应式数据
const formRef = ref()
const uploadRef = ref()

// 表单数据
const calculationForm = reactive({
  contractType: '',
  employeeName: '',
  startDate: '',
  endDate: '',
  probationMonths: 0,
  workExperience: 0
})

// 表单验证规则
const formRules = {
  contractType: [
    { required: true, message: '请选择合同类型', trigger: 'change' }
  ],
  employeeName: [
    { required: true, message: '请输入员工姓名', trigger: 'blur' }
  ],
  startDate: [
    { required: true, message: '请选择开始日期', trigger: 'change' }
  ],
  endDate: [
    { required: true, message: '请选择结束日期', trigger: 'change' }
  ]
}

// 合同类型选项
const contractTypeOptions = [
  { label: '事业编制聘用合同', value: 'career' },
  { label: '劳务协议', value: 'labor' },
  { label: '劳务派遣合同', value: 'dispatch' },
  { label: '兼职协议', value: 'parttime' },
  { label: '临时用工协议', value: 'temporary' }
]

// 计算结果
const calculationResults = reactive({
  isCalculated: false,
  contractDuration: '',
  probationPeriod: '',
  formalPeriod: '',
  totalDays: 0,
  totalMonths: 0,
  totalYears: 0,
  workingDays: 0,
  annualLeave: 0,
  legalHolidays: 0,
  weekends: 0
})

// 工龄数据
const workingAgeData = ref([
  {
    type: '合同工龄',
    startDate: '2025-01-01',
    endDate: '2028-12-31',
    duration: '4年',
    totalDays: 1461,
    totalMonths: 48,
    totalYears: 4,
    description: '本合同期内的工作时间'
  },
  {
    type: '累计工龄',
    startDate: '2020-01-01',
    endDate: '2028-12-31',
    duration: '9年',
    totalDays: 3287,
    totalMonths: 108,
    totalYears: 9,
    description: '包含之前工作经验的累计工龄'
  }
])

// 时间节点
const timeMilestones = ref([
  {
    id: '1',
    date: '2025-01-01',
    type: 'primary',
    title: '合同生效',
    description: '合同正式生效，开始履行合同义务'
  },
  {
    id: '2',
    date: '2025-04-01',
    type: 'success',
    title: '试用期结束',
    description: '3个月试用期结束，转为正式员工'
  },
  {
    id: '3',
    date: '2026-01-01',
    type: 'info',
    title: '工作满一年',
    description: '开始享受年假等福利待遇'
  },
  {
    id: '4',
    date: '2028-10-01',
    type: 'warning',
    title: '合同到期提醒',
    description: '合同即将到期，需要考虑续签事宜'
  },
  {
    id: '5',
    date: '2028-12-31',
    type: 'danger',
    title: '合同到期',
    description: '合同到期，需要办理续签或离职手续'
  }
])

// 批量数据
const batchData = ref([
  {
    employeeName: '张三',
    contractType: '事业编制聘用合同',
    startDate: '2025-01-01',
    endDate: '2028-12-31',
    duration: '4年',
    workingDays: 1043,
    status: 'calculated'
  },
  {
    employeeName: '李四',
    contractType: '劳务协议',
    startDate: '2025-02-01',
    endDate: '2026-01-31',
    duration: '1年',
    workingDays: 261,
    status: 'pending'
  }
])

// 计算历史
const calculationHistory = ref([
  {
    employeeName: '张三',
    contractType: '事业编制聘用合同',
    calculationDate: '2025-01-22 10:30:00',
    duration: '4年',
    workingDays: 1043,
    operator: '人事专员'
  },
  {
    employeeName: '李四',
    contractType: '劳务协议',
    calculationDate: '2025-01-22 09:15:00',
    duration: '1年',
    workingDays: 261,
    operator: '人事专员'
  }
])

// 方法
const handleCalculate = () => {
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      // 执行计算逻辑
      const startDate = new Date(calculationForm.startDate)
      const endDate = new Date(calculationForm.endDate)
      const timeDiff = endDate.getTime() - startDate.getTime()
      const totalDays = Math.ceil(timeDiff / (1000 * 3600 * 24))
      const totalMonths = Math.round(totalDays / 30.44)
      const totalYears = Math.round(totalMonths / 12)
      const workingDays = Math.round(totalDays * 5 / 7) // 假设5天工作制
      
      // 更新计算结果
      Object.assign(calculationResults, {
        isCalculated: true,
        contractDuration: `${totalYears}年${totalMonths % 12}个月`,
        probationPeriod: `${calculationForm.probationMonths}个月`,
        formalPeriod: `${totalMonths - calculationForm.probationMonths}个月`,
        totalDays,
        totalMonths,
        totalYears,
        workingDays,
        annualLeave: Math.min(Math.max(calculationForm.workExperience, 1) * 5, 15),
        legalHolidays: Math.round(totalDays * 11 / 365), // 假设每年11天法定假日
        weekends: Math.round(totalDays * 2 / 7)
      })
      
      ElMessage.success('计算完成')
    }
  })
}

const handleExportResults = () => {
  ElMessage.success('导出计算结果')
}

const handleSaveResults = () => {
  ElMessage.success('保存计算结果')
}

   
const handleFileUpload = (file: unknown) => {
  ElMessage.success(`上传文件: ${file.name}`)
}

const handleBatchCalculate = () => {
  ElMessage.success('批量计算完成')
}

const handleDownloadTemplate = () => {
  ElMessage.success('下载模板文件')
}

   
const handleCalculateSingle = (row: unknown) => {
  row.status = 'calculated'
  ElMessage.success(`计算完成: ${row.employeeName}`)
}

const handleClearHistory = () => {
  calculationHistory.value = []
  ElMessage.success('历史记录已清空')
}

   
const handleViewHistory = (row: unknown) => {
  ElMessage.info(`查看历史记录: ${row.employeeName}`)
}

// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.contract-term-calculator {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.calculation-input,
.calculation-results,
.batch-calculation,
.calculation-history {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.result-section {
  margin-bottom: 30px;
}

.result-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.info-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.info-item label {
  display: block;
  margin-bottom: 8px;
  color: #909399;
  font-size: 12px;
}

.info-item .value {
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 20px;
  color: white;
}

.stat-icon.days {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.months {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.years {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-icon.working-days {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.milestone-item {
  padding: 8px 0;
}

.milestone-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.milestone-description {
  color: #606266;
  font-size: 14px;
}

.holiday-stat {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.holiday-stat h5 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.holiday-info {
  margin-bottom: 8px;
}

.holiday-days {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
}

.holiday-unit {
  font-size: 14px;
  color: #909399;
  margin-left: 4px;
}

.holiday-description {
  color: #606266;
  font-size: 12px;
}

.batch-content {
  margin-top: 20px;
}

.upload-tips {
  margin-bottom: 20px;
  padding: 16px;
  background: #f0f7ff;
  border-radius: 8px;
}

.upload-tips h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.upload-tips ul {
  margin: 0 0 12px 0;
  padding-left: 20px;
}

.upload-tips li {
  margin-bottom: 4px;
  color: #606266;
  font-size: 14px;
}
</style>