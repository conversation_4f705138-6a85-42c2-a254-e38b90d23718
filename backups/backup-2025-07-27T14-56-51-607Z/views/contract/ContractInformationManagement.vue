<template>
  <div class="contract-information-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>合同信息管理</h2>
      <p>管理教职工聘用合同的新签、续签、变更、终止等全流程操作</p>
    </div>

    <!-- 搜索和操作区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-form">
        <el-row :gutter="20">
          <el-col :span="5">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索姓名、工号、合同编号"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.contractType" placeholder="合同类型" clearable>
              <el-option
                v-for="item in contractTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.status" placeholder="合同状态" clearable>
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.department" placeholder="所属部门" clearable>
              <el-option label="全部部门" value=""  />
              <el-option
                v-for="dept in departmentOptions"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
               />
            </el-select>
          </el-col>
          <el-col :span="7">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
            <el-button type="success" @click="handleAdd">
              <el-icon><Plus /></el-icon>
              新签合同
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.total }}</div>
              <div class="stats-label">合同总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon active">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.active }}</div>
              <div class="stats-label">有效合同</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon expiring">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.expiring }}</div>
              <div class="stats-label">即将到期</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon pending">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.pending }}</div>
              <div class="stats-label">待审批</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 合同列表 -->
    <el-card class="table-card" shadow="never">
      <div class="table-header">
        <span class="table-title">合同信息列表</span>
        <div class="table-actions">
          <el-button size="small" @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
          <el-button size="small" @click="handleBatchOperation" :disabled="selectedRows.length === 0">
            <el-icon><Operation /></el-icon>
            批量操作
          </el-button>
        </div>
      </div>

      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="contractNumber" label="合同编号" width="120"  />
        <el-table-column prop="employeeName" label="教职工" width="100"  />
        <el-table-column prop="employeeNumber" label="工号" width="100"  />
        <el-table-column prop="department" label="部门" width="150" show-overflow-tooltip  />
        <el-table-column prop="contractType" label="合同类型" width="150">
          <template #default="scope">
            <el-tag :type="getContractTypeTag(scope.row.contractType)" size="small">
              {{ getContractTypeText(scope.row.contractType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="startDate" label="开始日期" width="100"  />
        <el-table-column prop="endDate" label="结束日期" width="100"  />
        <el-table-column prop="remainingDays" label="剩余天数" width="100">
          <template #default="scope">
            <el-tag :type="getRemainingDaysTag(scope.row.remainingDays)" size="small">
              {{ scope.row.remainingDays }}天
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="120">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="signDate" label="签署日期" width="100"  />
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" link @click="handleView(scope.row)">
              查看
            </el-button>
            <el-button 
              v-if="canEdit(scope.row.status)"
              size="small" 
              type="success" 
              link 
              @click="handleEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button 
              v-if="canRenew(scope.row)"
              size="small" 
              type="warning" 
              link 
              @click="handleRenew(scope.row)"
            >
              续签
            </el-button>
            <el-button 
              v-if="canTerminate(scope.row.status)"
              size="small" 
              type="danger" 
              link 
              @click="handleTerminate(scope.row)"
            >
              终止
            </el-button>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
            <el-dropdown trigger="click" @command="(command: unknown) => handleMoreAction(command, scope.row)"> <!-- 修复command参数类型 -->
              <el-button size="small" type="info" link>
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="modify">变更</el-dropdown-item>
                  <el-dropdown-item command="history">历史记录</el-dropdown-item>
                  <el-dropdown-item command="download">下载合同</el-dropdown-item>
                  <el-dropdown-item command="print">打印合同</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 合同详情/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="80%"
      :close-on-click-modal="false"
      :before-close="handleDialogClose"
    >
      <div class="contract-form">
        <el-form
          v-if="dialogMode !== 'view'"
          :model="contractForm"
          :rules="contractRules"
          ref="contractFormRef"
          label-width="120px"
        >
          <el-tabs v-model="activeTab" type="border-card">
            <!-- 基本信息 -->
            <el-tab-pane label="基本信息" name="basic">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="合同编号" prop="contractNumber">
                    <el-input
                      v-model="contractForm.contractNumber"
                      placeholder="系统自动生成"
                      readonly
                      />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="员工姓名" prop="employeeName">
                    <el-select
                      v-model="contractForm.employeeName"
                      placeholder="请选择员工"
                      filterable
                      @change="handleEmployeeChange"
                    >
                      <el-option
                        v-for="employee in mockEmployees"
                        :key="employee.id"
                        :label="employee.name"
                        :value="employee.name"
                       />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="合同类型" prop="contractType">
                    <el-select
                      v-model="contractForm.contractType"
                      placeholder="请选择合同类型"
                    >
                      <el-option
                        v-for="type in contractTypeOptions"
                        :key="type.value"
                        :label="type.label"
                        :value="type.value"
                       />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="合同状态" prop="status">
                    <el-select
                      v-model="contractForm.status"
                      placeholder="请选择状态"
                    >
                      <el-option
                        v-for="status in statusOptions"
                        :key="status.value"
                        :label="status.label"
                        :value="status.value"
                       />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="开始日期" prop="startDate">
                    <el-date-picker
                      v-model="contractForm.startDate"
                      type="date"
                      placeholder="请选择开始日期"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                     />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="结束日期" prop="endDate">
                    <el-date-picker
                      v-model="contractForm.endDate"
                      type="date"
                      placeholder="请选择结束日期"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                     />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="签署日期" prop="signDate">
                    <el-date-picker
                      v-model="contractForm.signDate"
                      type="date"
                      placeholder="请选择签署日期"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                     />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="所属部门" prop="department">
                    <el-input
                      v-model="contractForm.department"
                      placeholder="请输入所属部门"
                      />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="备注">
                <el-input
                  v-model="contractForm.remark"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入备注信息"
                  />
              </el-form-item>
            </el-tab-pane>

            <!-- 薪资信息 -->
            <el-tab-pane label="薪资信息" name="salary">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="基本薪资" prop="baseSalary">
                    <el-input-number
                      v-model="contractForm.baseSalary"
                      :min="0"
                      :precision="2"
                      placeholder="请输入基本薪资"
                      />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="绩效薪资" prop="performanceSalary">
                    <el-input-number
                      v-model="contractForm.performanceSalary"
                      :min="0"
                      :precision="2"
                      placeholder="请输入绩效薪资"
                      />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="社会保险">
                    <el-switch v-model="contractForm.socialInsurance"  />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="住房公积金">
                    <el-switch v-model="contractForm.housingFund"  />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="其他福利">
                <el-input
                  v-model="contractForm.otherBenefits"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入其他福利信息"
                  />
              </el-form-item>
            </el-tab-pane>

            <!-- 合同条款 -->
            <el-tab-pane label="合同条款" name="terms">
              <el-form-item label="工作内容">
                <el-input
                  v-model="contractForm.workContent"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入工作内容"
                  />
              </el-form-item>
              <el-form-item label="工作地点">
                <el-input
                  v-model="contractForm.workLocation"
                  placeholder="请输入工作地点"
                  />
              </el-form-item>
              <el-form-item label="续签条件">
                <el-input
                  v-model="contractForm.renewalConditions"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入续签条件"
                  />
              </el-form-item>
              <el-form-item label="终止条件">
                <el-input
                  v-model="contractForm.terminationConditions"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入终止条件"
                  />
              </el-form-item>
            </el-tab-pane>
          </el-tabs>
        </el-form>

        <!-- 查看模式 -->
        <div v-else class="contract-view">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="合同编号">{{ currentContract?.contractNumber }}</el-descriptions-item>
            <el-descriptions-item label="员工姓名">{{ currentContract?.employeeName }}</el-descriptions-item>
            <el-descriptions-item label="员工编号">{{ currentContract?.employeeNumber }}</el-descriptions-item>
            <el-descriptions-item label="所属部门">{{ currentContract?.department }}</el-descriptions-item>
            <el-descriptions-item label="合同类型">
              <el-tag :type="getContractTypeTag(currentContract?.contractType)" size="small">
                {{ getContractTypeText(currentContract?.contractType) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="合同状态">
              <el-tag :type="getStatusTagType(currentContract?.status)" size="small">
                {{ getStatusText(currentContract?.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="开始日期">{{ currentContract?.startDate }}</el-descriptions-item>
            <el-descriptions-item label="结束日期">{{ currentContract?.endDate }}</el-descriptions-item>
            <el-descriptions-item label="签署日期">{{ currentContract?.signDate }}</el-descriptions-item>
            <el-descriptions-item label="剩余天数">
              <el-tag :type="getRemainingDaysTag(currentContract?.remainingDays)" size="small">
                {{ currentContract?.remainingDays }}天
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">
            {{ dialogMode === 'view' ? '关闭' : '取消' }}
          </el-button>
          <el-button
            v-if="dialogMode !== 'view'"
            type="primary"
            @click="handleSaveContract"
            :loading="saving"
          >
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 合同续签对话框 -->
    <el-dialog
      v-model="renewalDialogVisible"
      title="合同续签"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="renewalForm"
        :rules="renewalRules"
        ref="renewalFormRef"
        label-width="120px"
      >
        <el-form-item label="原合同信息">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="合同编号">{{ currentContract?.contractNumber }}</el-descriptions-item>
            <el-descriptions-item label="员工姓名">{{ currentContract?.employeeName }}</el-descriptions-item>
            <el-descriptions-item label="原结束日期">{{ currentContract?.endDate }}</el-descriptions-item>
            <el-descriptions-item label="合同类型">{{ getContractTypeText(currentContract?.contractType) }}</el-descriptions-item>
          </el-descriptions>
        </el-form-item>
        <el-form-item label="新结束日期" prop="newEndDate">
          <el-date-picker
            v-model="renewalForm.newEndDate"
            type="date"
            placeholder="请选择新的结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
           />
        </el-form-item>
        <el-form-item label="续签原因" prop="renewalReason">
          <el-input
            v-model="renewalForm.renewalReason"
            type="textarea"
            :rows="3"
            placeholder="请输入续签原因"
            />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="renewalForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
            />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="renewalDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveRenewal" :loading="saving">
            确认续签
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 合同终止对话框 -->
    <el-dialog
      v-model="terminationDialogVisible"
      title="合同终止"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="terminationForm"
        :rules="terminationRules"
        ref="terminationFormRef"
        label-width="120px"
      >
        <el-form-item label="合同信息">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="合同编号">{{ currentContract?.contractNumber }}</el-descriptions-item>
            <el-descriptions-item label="员工姓名">{{ currentContract?.employeeName }}</el-descriptions-item>
            <el-descriptions-item label="合同类型">{{ getContractTypeText(currentContract?.contractType) }}</el-descriptions-item>
            <el-descriptions-item label="原结束日期">{{ currentContract?.endDate }}</el-descriptions-item>
          </el-descriptions>
        </el-form-item>
        <el-form-item label="终止日期" prop="terminationDate">
          <el-date-picker
            v-model="terminationForm.terminationDate"
            type="date"
            placeholder="请选择终止日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
           />
        </el-form-item>
        <el-form-item label="终止类型" prop="terminationType">
          <el-select v-model="terminationForm.terminationType" placeholder="请选择终止类型">
            <el-option label="协商终止" value="NEGOTIATION"  />
            <el-option label="到期终止" value="EXPIRATION"  />
            <el-option label="违约终止" value="BREACH"  />
            <el-option label="其他" value="OTHER"  />
          </el-select>
        </el-form-item>
        <el-form-item label="终止原因" prop="terminationReason">
          <el-input
            v-model="terminationForm.terminationReason"
            type="textarea"
            :rows="4"
            placeholder="请输入终止原因"
            />
        </el-form-item>
        <el-form-item label="补偿情况">
          <el-input
            v-model="terminationForm.compensation"
            type="textarea"
            :rows="3"
            placeholder="请输入补偿情况"
            />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="terminationDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="handleSaveTermination" :loading="saving">
            确认终止
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 合同变更对话框 -->
    <el-dialog
      v-model="modificationDialogVisible"
      title="合同变更"
      width="700px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="modificationForm"
        :rules="modificationRules"
        ref="modificationFormRef"
        label-width="120px"
      >
        <el-form-item label="变更类型" prop="modificationType">
          <el-select v-model="modificationForm.modificationType" placeholder="请选择变更类型">
            <el-option label="岗位变更" value="POSITION_CHANGE"  />
            <el-option label="薪资变更" value="SALARY_CHANGE"  />
            <el-option label="工作地点变更" value="LOCATION_CHANGE"  />
            <el-option label="其他变更" value="OTHER"  />
          </el-select>
        </el-form-item>
        <el-form-item label="变更原因" prop="modificationReason">
          <el-input
            v-model="modificationForm.modificationReason"
            type="textarea"
            :rows="3"
            placeholder="请输入变更原因"
            />
        </el-form-item>
        <el-form-item label="变更内容" prop="modificationContent">
          <el-input
            v-model="modificationForm.modificationContent"
            type="textarea"
            :rows="4"
            placeholder="请输入变更内容"
            />
        </el-form-item>
        <el-form-item label="生效日期" prop="effectiveDate">
          <el-date-picker
            v-model="modificationForm.effectiveDate"
            type="date"
            placeholder="请选择生效日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
           />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="modificationForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
            />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="modificationDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveModification" :loading="saving">
            确认变更
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  Document,
  Check,
  Warning,
  Clock,
  Download,
  Operation,
  ArrowDown
} from '@element-plus/icons-vue'
import { contractInformationApi } from '@/api/contract'
import type { ContractInformation, ContractQueryRequest } from '@/api/contract'
import { organizationApi } from '@/api/organization'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const tableData = ref([])
const selectedRows = ref<any[]>([]) // 修复类型：never[] → any[]
const contractFormRef = ref()
const renewalFormRef = ref()
const terminationFormRef = ref()
const modificationFormRef = ref()

// 活动标签页
const activeTab = ref('basic')

// 部门列表
const departmentOptions = ref<any[]>([])

// 表单数据
const contractForm = reactive({
  id: '',
  contractNumber: '',
  employeeName: '',
  employeeNumber: '',
  department: '',
  contractType: '',
  status: '',
  startDate: '',
  endDate: '',
  signDate: '',
  baseSalary: 0,
  performanceSalary: 0,
  socialInsurance: true,
  housingFund: true,
  otherBenefits: '',
  workContent: '',
  workLocation: '',
  renewalConditions: '',
  terminationConditions: '',
  remark: ''
})

const renewalForm = reactive({
  newEndDate: '',
  renewalReason: '',
  remark: ''
})

const terminationForm = reactive({
  terminationDate: '',
  terminationType: '',
  terminationReason: '',
  compensation: ''
})

const modificationForm = reactive({
  modificationType: '',
  modificationReason: '',
  modificationContent: '',
  effectiveDate: '',
  remark: ''
})

// 模拟员工数据
const mockEmployees = [
  { id: 1, name: 'HrHr张三', number: 'EMP001', department: '计算机学院' },
  { id: 2, name: '李四', number: 'EMP002', department: '机械工程学院' },
  { id: 3, name: '王五', number: 'EMP003', department: '经济管理学院' }
]

// 搜索表单
const searchForm = reactive({
  keyword: '',
  contractType: '',
  status: '',
  department: ''
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 统计信息
const stats = reactive({
  total: 0,
  active: 0,
  expiring: 0,
  pending: 0
})

// 对话框相关
const dialogVisible = ref(false)
const renewalDialogVisible = ref(false)
const terminationDialogVisible = ref(false)
const modificationDialogVisible = ref(false)
const dialogMode = ref<'view' | 'add' | 'edit'>('view')
const currentContract = ref(null)

// 计算属性
const dialogTitle = computed(() => {
  const titles = {
    view: '查看合同',
    add: '新建合同',
    edit: '编辑合同'
  }
  return titles[dialogMode.value] || '合同信息'
})

// 表单验证规则
const contractRules = {
  employeeName: [{ required: true, message: '请选择员工', trigger: 'change' }],
  contractType: [{ required: true, message: '请选择合同类型', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
  startDate: [{ required: true, message: '请选择开始日期', trigger: 'change' }],
  endDate: [{ required: true, message: '请选择结束日期', trigger: 'change' }],
  signDate: [{ required: true, message: '请选择签署日期', trigger: 'change' }],
  department: [{ required: true, message: '请输入所属部门', trigger: 'blur' }]
}

const renewalRules = {
  newEndDate: [{ required: true, message: '请选择新的结束日期', trigger: 'change' }],
  renewalReason: [{ required: true, message: '请输入续签原因', trigger: 'blur' }]
}

const terminationRules = {
  terminationDate: [{ required: true, message: '请选择终止日期', trigger: 'change' }],
  terminationType: [{ required: true, message: '请选择终止类型', trigger: 'change' }],
  terminationReason: [{ required: true, message: '请输入终止原因', trigger: 'blur' }]
}

const modificationRules = {
  modificationType: [{ required: true, message: '请选择变更类型', trigger: 'change' }],
  modificationReason: [{ required: true, message: '请输入变更原因', trigger: 'blur' }],
  modificationContent: [{ required: true, message: '请输入变更内容', trigger: 'blur' }],
  effectiveDate: [{ required: true, message: '请选择生效日期', trigger: 'change' }]
}

// 合同类型选项
const contractTypeOptions = [
  { label: '事业编制聘用合同', value: 'CAREER_ESTABLISHMENT' },
  { label: '人事代理合同', value: 'PERSONNEL_AGENCY' },
  { label: '劳务协议', value: 'LABOR_AGREEMENT' },
  { label: '劳务派遣合同', value: 'LABOR_DISPATCH' },
  { label: '兼职协议', value: 'PART_TIME_AGREEMENT' },
  { label: '项目合作协议', value: 'PROJECT_COOPERATION' }
]

// 状态选项
const statusOptions = [
  { label: '草稿', value: 'DRAFT' },
  { label: '待审批', value: 'PENDING_APPROVAL' },
  { label: '已审批', value: 'APPROVED' },
  { label: '已签署', value: 'SIGNED' },
  { label: '有效', value: 'ACTIVE' },
  { label: '即将到期', value: 'EXPIRING' },
  { label: '已到期', value: 'EXPIRED' },
  { label: '已终止', value: 'TERMINATED' },
  { label: '已作废', value: 'CANCELLED' }
]

// 模拟数据
const mockData = [
  {
    id: '1',
    contractNumber: 'HKY2025001',
    employeeName: '张三',
    employeeNumber: 'EMP001',
    department: '计算机学院',
    contractType: 'CAREER_ESTABLISHMENT',
    startDate: '2025-01-01',
    endDate: '2027-12-31',
    remainingDays: 925,
    status: 'ACTIVE',
    signDate: '2024-12-15'
  },
  {
    id: '2',
    contractNumber: 'HKY2025002',
    employeeName: '李四',
    employeeNumber: 'EMP002',
    department: '机械工程学院',
    contractType: 'PERSONNEL_AGENCY',
    startDate: '2025-07-01',
    endDate: '2025-12-31',
    remainingDays: 195,
    status: 'EXPIRING',
    signDate: '2025-06-15'
  }
]

// 获取合同列表
const fetchContracts = async () => {
  try {
    loading.value = true
    const params: ContractQueryRequest = {
      page: pagination.page - 1,
      size: pagination.size,
      keyword: searchForm.keyword,
      contractType: searchForm.contractType,
      status: searchForm.status,
      department: searchForm.department,
      dateStart: searchForm.dateRange?.[0],
      dateEnd: searchForm.dateRange?.[1]
    }
    
    const response = await contractInformationApi.queryContracts(params)
    tableData.value = response.content || []
    pagination.total = response.totalElements || 0
  } catch (__error) {
    console.error('获取合同列表失败:', error)
    ElMessage.error('获取合同列表失败')
  } finally {
    loading.value = false
  }
}

// 获取统计信息
const fetchStats = async () => {
  try {
    const response = await contractInformationApi.getStats()
    stats.total = response.total || 0
    stats.active = response.active || 0
    stats.expiring = response.expiring || 0
    stats.pending = response.pending || 0
  } catch (__error) {
    console.error('获取统计信息失败:', error)
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchContracts()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    contractType: '',
    status: '',
    department: ''
  })
  pagination.page = 1
  fetchContracts()
}

// 新增合同
const handleAdd = () => {
  resetContractForm()
  currentContract.value = null
  dialogMode.value = 'add'
  dialogVisible.value = true
  activeTab.value = 'basic'
}

// 查看合同
   
const handleView = (contract: unknown) => {
  currentContract.value = contract
  dialogMode.value = 'view'
  dialogVisible.value = true
}

// 编辑合同
   
const handleEdit = (contract: unknown) => {
  currentContract.value = contract
  dialogMode.value = 'edit'
  dialogVisible.value = true
}

// 续签合同
   
const handleRenew = (contract: unknown) => {
  currentContract.value = contract
  renewalDialogVisible.value = true
}

// 终止合同
   
const handleTerminate = (contract: unknown) => {
  currentContract.value = contract
  terminationDialogVisible.value = true
}

// 更多操作
   
const handleMoreAction = (command: string, contract: unknown) => {
  currentContract.value = contract
  switch (command) {
    case 'modify':
      modificationDialogVisible.value = true
      break
    case 'history':
      ElMessage.info('查看历史记录功能开发中...')
      break
    case 'download':
      ElMessage.info('下载合同功能开发中...')
      break
    case 'print':
      ElMessage.info('打印合同功能开发中...')
      break
  }
}

// 表格选择变化
   
const handleSelectionChange = (selection: unknown[]) => {
  selectedRows.value = selection
}

// 批量操作
const handleBatchOperation = () => {
  ElMessage.info('批量操作功能开发中...')
}

// 导出
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchContracts()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchContracts()
}

// 对话框成功回调
const handleDialogSuccess = () => {
  fetchContracts()
  fetchStats()
}

const handleRenewalSuccess = () => {
  fetchContracts()
  fetchStats()
}

const handleTerminationSuccess = () => {
  fetchContracts()
  fetchStats()
}

const handleModificationSuccess = () => {
  fetchContracts()
  fetchStats()
}

// 重置表单
const resetContractForm = () => {
  Object.assign(contractForm, {
    id: '',
    contractNumber: '',
    employeeName: '',
    employeeNumber: '',
    department: '',
    contractType: '',
    status: '',
    startDate: '',
    endDate: '',
    signDate: '',
    baseSalary: 0,
    performanceSalary: 0,
    socialInsurance: true,
    housingFund: true,
    otherBenefits: '',
    workContent: '',
    workLocation: '',
    renewalConditions: '',
    terminationConditions: '',
    remark: ''
  })
}

// 员工选择变化
const handleEmployeeChange = (employeeName: string) => {
  const employee = mockEmployees.find(emp => emp.name === employeeName)
  if (employee) {
    contractForm.employeeNumber = employee.number
    contractForm.department = employee.department
  }
}

// 保存合同
const handleSaveContract = async () => {
  try {
    await contractFormRef.value.validate()
    saving.value = true
    
    // 生成合同编号
    if (dialogMode.value === 'add') {
      contractForm.contractNumber = `HKY${new Date().getFullYear()}${String(Math.floor(Math.random() * 9999)).padStart(4, '0')}`
    }
    
    // 准备合同数据
    const contractData: ContractInformation = {
      ...contractForm,
      salaryStandard: contractForm.baseSalary + contractForm.performanceSalary,
      benefits: contractForm.otherBenefits ? [contractForm.otherBenefits] : []
    }
    
    // 根据模式调用不同的API
    if (dialogMode.value === 'add') {
      await contractInformationApi.createContract(contractData)
    } else if (dialogMode.value === 'edit' && contractForm.id) {
      await contractInformationApi.updateContract(contractForm.id, contractData)
    }
    
    ElMessage.success('保存成功')
    dialogVisible.value = false
    fetchContracts()
    fetchStats()
  } catch (__error) {
    console.error('保存合同失败:', error)
    ElMessage.error('保存合同失败')
  } finally {
    saving.value = false
  }
}

// 保存续签
const handleSaveRenewal = async () => {
  try {
    await renewalFormRef.value.validate()
    saving.value = true
    
    // 调用续签API
    await contractInformationApi.renewContract(renewalForm.contractId, {
      endDate: renewalForm.newEndDate,
      remark: renewalForm.reason
    })
    
    ElMessage.success('续签成功')
    renewalDialogVisible.value = false
    fetchContracts()
    fetchStats()
  } catch (__error) {
    console.error('续签失败:', error)
    ElMessage.error('续签失败')
  } finally {
    saving.value = false
  }
}

// 保存终止
const handleSaveTermination = async () => {
  try {
    await terminationFormRef.value.validate()
    saving.value = true
    
    // 调用终止API
    await contractInformationApi.terminateContract(terminationForm.contractId, {
      terminationDate: terminationForm.terminationDate,
      reason: `${terminationForm.terminationType}: ${terminationForm.terminationReason}${terminationForm.compensation ? `, 补偿: ${terminationForm.compensation}` : ''}`
    })
    
    ElMessage.success('终止成功')
    terminationDialogVisible.value = false
    fetchContracts()
    fetchStats()
  } catch (__error) {
    console.error('终止失败:', error)
    ElMessage.error('终止失败')
  } finally {
    saving.value = false
  }
}

// 保存变更
const handleSaveModification = async () => {
  try {
    await modificationFormRef.value.validate()
    saving.value = true
    
    // 调用变更API
    await contractInformationApi.modifyContract(modificationForm.contractId, {
      modificationType: modificationForm.modificationType,
      modificationContent: modificationForm.modificationContent,
      reason: modificationForm.modificationReason,
      effectiveDate: modificationForm.effectiveDate
    })
    
    ElMessage.success('变更成功')
    modificationDialogVisible.value = false
    fetchContracts()
    fetchStats()
  } catch (__error) {
    console.error('变更失败:', error)
    ElMessage.error('变更失败')
  } finally {
    saving.value = false
  }
}

// 对话框关闭前处理
const handleDialogClose = (done: () => void) => {
  if (dialogMode.value !== 'view') {
    ElMessageBox.confirm('确定要关闭吗？未保存的数据将丢失。')
      .then(() => {
        done()
      })
      .catch(() => {})
  } else {
    done()
  }
}

// 获取合同类型标签
const getContractTypeTag = (type: string) => {
  switch (type) {
    case 'CAREER_ESTABLISHMENT': return 'primary'
    case 'PERSONNEL_AGENCY': return 'success'
    case 'LABOR_AGREEMENT': return 'warning'
    case 'LABOR_DISPATCH': return 'info'
    case 'PART_TIME_AGREEMENT': return 'warning'
    case 'PROJECT_COOPERATION': return 'info'
    default: return ''
  }
}

// 获取合同类型文本
const getContractTypeText = (type: string) => {
  const option = contractTypeOptions.find(item => item.value === type)
  return option ? option.label : type
}

// 获取剩余天数标签
const getRemainingDaysTag = (days: number) => {
  if (days <= 30) return 'danger'
  if (days <= 90) return 'warning'
  return 'success'
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  switch (status) {
    case 'DRAFT': return 'info'
    case 'PENDING_APPROVAL': return 'warning'
    case 'APPROVED': return 'primary'
    case 'SIGNED': return 'success'
    case 'ACTIVE': return 'success'
    case 'EXPIRING': return 'warning'
    case 'EXPIRED': return 'danger'
    case 'TERMINATED': return 'info'
    case 'CANCELLED': return 'danger'
    default: return ''
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  const option = statusOptions.find(item => item.value === status)
  return option ? option.label : status
}

// 判断是否可以编辑
const canEdit = (status: string) => {
  return ['DRAFT', 'PENDING_APPROVAL'].includes(status)
}

// 判断是否可以续签
   
const canRenew = (contract: unknown) => {
  return ['ACTIVE', 'EXPIRING'].includes(contract.status) && contract.remainingDays <= 180
}

// 判断是否可以终止
const canTerminate = (status: string) => {
  return ['ACTIVE', 'EXPIRING'].includes(status)
}

// 获取部门列表
const fetchDepartments = async () => {
  try {
    const response = await organizationApi.getTree()
    if (response && Array.isArray(response)) {
      departmentOptions.value = response.map(dept => ({
        id: dept.id,
        name: dept.name,
        orgCode: dept.orgCode
      }))
    }
  } catch (__error) {
    console.error('获取部门列表失败:', error)
    ElMessage.error('获取部门列表失败')
  }
}

// 初始化
onMounted(() => {
  fetchContracts()
  fetchStats()
  fetchDepartments()
})
</script>

<style scoped>
.contract-information-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  padding: 10px 0;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stats-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 20px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.active {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.expiring {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.pending {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 12px;
  color: #909399;
}

.table-card {
  margin-top: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 对话框样式 */
.contract-form {
  max-height: 600px;
  overflow-y: auto;
}

.contract-view {
  padding: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.el-tabs {
  margin-top: 10px;
}

.el-descriptions {
  margin-bottom: 20px;
}

.el-form-item {
  margin-bottom: 18px;
}

.el-input-number {
  width: 100%;
}

.el-select {
  width: 100%;
}

.el-date-picker {
  width: 100%;
}
</style>
