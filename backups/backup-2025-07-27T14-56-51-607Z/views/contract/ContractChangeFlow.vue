<template>
  <div class="contract-change-flow">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>合同变更流程</h2>
      <p>管理合同变更全流程，包括变更申请、审批、执行和归档</p>
    </div>

    <!-- 变更申请列表 -->
    <el-card class="change-list" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>变更申请列表</h3>
          <div class="header-actions">
            <el-button @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="primary" @click="handleStartChange">
              <el-icon><Plus /></el-icon>
              发起变更
            </el-button>
          </div>
        </div>
      </template>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-form :inline="true" :model="filterForm" size="small">
          <el-form-item label="员工姓名">
            <el-input 
              v-model="filterForm.employeeName" 
              placeholder="请输入员工姓名"
              clearable
              />
          </el-form-item>
          <el-form-item label="变更类型">
            <el-select v-model="filterForm.changeType" placeholder="请选择变更类型" clearable>
              <el-option label="薪资变更" value="salary"  />
              <el-option label="岗位变更" value="position"  />
              <el-option label="部门变更" value="department"  />
              <el-option label="工作地点变更" value="location"  />
              <el-option label="合同期限变更" value="term"  />
            </el-select>
          </el-form-item>
          <el-form-item label="变更状态">
            <el-select v-model="filterForm.changeStatus" placeholder="请选择状态" clearable>
              <el-option label="待审批" value="pending"  />
              <el-option label="审批中" value="approving"  />
              <el-option label="已通过" value="approved"  />
              <el-option label="已拒绝" value="rejected"  />
              <el-option label="已执行" value="executed"  />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleFilter">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="handleResetFilter">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 变更统计 -->
      <div class="change-stats">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-card pending">
              <div class="stat-icon">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ changeStats.pending }}</div>
                <div class="stat-label">待审批</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card processing">
              <div class="stat-icon">
                <el-icon><Loading /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ changeStats.processing }}</div>
                <div class="stat-label">审批中</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card approved">
              <div class="stat-icon">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ changeStats.approved }}</div>
                <div class="stat-label">已通过</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card executed">
              <div class="stat-icon">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ changeStats.executed }}</div>
                <div class="stat-label">已执行</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 变更表格 -->
      <el-table :data="changeList" style="width: 100%">
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="employeeName" label="员工姓名" width="100"  />
        <el-table-column prop="contractNumber" label="合同编号" width="150"  />
        <el-table-column prop="changeType" label="变更类型" width="120">
          <template #default="scope">
            <el-tag :type="getChangeTypeColor(scope.row.changeType)" size="small">
              {{ getChangeTypeText(scope.row.changeType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="changeStatus" label="变更状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.changeStatus)" size="small">
              {{ getStatusText(scope.row.changeStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="applicant" label="申请人" width="100"  />
        <el-table-column prop="applicationDate" label="申请时间" width="150"  />
        <el-table-column prop="effectiveDate" label="生效日期" width="120"  />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="handleViewChange(scope.row)">
              查看
            </el-button>
            <el-button 
              size="small" 
              type="primary" 
              @click="handleProcessChange(scope.row)"
              v-if="scope.row.changeStatus === 'pending'"
            >
              处理
            </el-button>
            <el-button 
              size="small" 
              type="success" 
              @click="handleApproveChange(scope.row)"
              v-if="scope.row.changeStatus === 'approving'"
            >
              审批
            </el-button>
            <el-button 
              size="small" 
              type="warning" 
              @click="handleExecuteChange(scope.row)"
              v-if="scope.row.changeStatus === 'approved'"
            >
              执行
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
    </el-card>

    <!-- 变更申请对话框 -->
    <el-dialog v-model="changeDialogVisible" title="合同变更申请" width="900px">
      <div class="change-form">
        <el-form ref="changeFormRef" :model="changeForm" :rules="changeRules" label-width="120px">
          <el-tabs v-model="activeChangeTab" type="card">
            <!-- 基本信息 -->
            <el-tab-pane label="基本信息" name="basic">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="员工姓名" prop="employeeName">
                    <el-input v-model="changeForm.employeeName" placeholder="请输入员工姓名"   />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="合同编号" prop="contractNumber">
                    <el-input v-model="changeForm.contractNumber" placeholder="请输入合同编号"   />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="变更类型" prop="changeType">
                    <el-select v-model="changeForm.changeType" placeholder="请选择变更类型">
                      <el-option label="薪资变更" value="salary"  />
                      <el-option label="岗位变更" value="position"  />
                      <el-option label="部门变更" value="department"  />
                      <el-option label="工作地点变更" value="location"  />
                      <el-option label="合同期限变更" value="term"  />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="生效日期" prop="effectiveDate">
                    <el-date-picker
                      v-model="changeForm.effectiveDate"
                      type="date"
                      placeholder="选择生效日期"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                     />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="变更原因" prop="changeReason">
                    <el-select v-model="changeForm.changeReason" placeholder="请选择变更原因">
                      <el-option label="工作需要" value="work_need"  />
                      <el-option label="岗位调整" value="position_adjustment"  />
                      <el-option label="组织架构调整" value="organization_change"  />
                      <el-option label="员工申请" value="employee_request"  />
                      <el-option label="绩效考核" value="performance_review"  />
                      <el-option label="其他" value="other"  />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="紧急程度" prop="urgency">
                    <el-select v-model="changeForm.urgency" placeholder="请选择紧急程度">
                      <el-option label="一般" value="normal"  />
                      <el-option label="重要" value="important"  />
                      <el-option label="紧急" value="urgent"  />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-tab-pane>

            <!-- 变更内容 -->
            <el-tab-pane label="变更内容" name="content">
              <div class="change-content">
                <h4>变更前后对比</h4>
                <el-table :data="changeDetails" style="width: 100%">
                  <el-table-column prop="field" label="变更项目" width="150"  />
                  <el-table-column prop="originalValue" label="变更前">
                    <template #default="scope">
                      <span class="original-value">{{ scope.row.originalValue }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="newValue" label="变更后">
                    <template #default="scope">
                      <span class="new-value">{{ scope.row.newValue }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="changeReason" label="变更说明" show-overflow-tooltip  />
                  <el-table-column label="操作" width="120">
                    <template #default="scope">
                      <el-button size="small" @click="handleEditChangeDetail(scope.row)">
                        编辑
                      </el-button>
                      <el-button size="small" type="danger" @click="handleDeleteChangeDetail(scope.row)">
                        删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
                
                <div class="add-change-item">
                  <el-button type="primary" @click="handleAddChangeItem">
                    <el-icon><Plus /></el-icon>
                    添加变更项
                  </el-button>
                </div>
              </div>
            </el-tab-pane>

            <!-- 审批信息 -->
            <el-tab-pane label="审批信息" name="approval">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="审批人" prop="approver">
                    <el-select v-model="changeForm.approver" placeholder="请选择审批人">
                      <el-option label="部门经理" value="dept_manager"  />
                      <el-option label="人事经理" value="hr_manager"  />
                      <el-option label="总经理" value="general_manager"  />
                      <el-option label="董事长" value="chairman"  />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="抄送人" prop="ccRecipients">
                    <el-select v-model="changeForm.ccRecipients" multiple placeholder="请选择抄送人">
                      <el-option label="直接上级" value="direct_supervisor"  />
                      <el-option label="人事专员" value="hr_specialist"  />
                      <el-option label="财务经理" value="finance_manager"  />
                      <el-option label="法务专员" value="legal_specialist"  />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="申请说明" prop="applicationDescription">
                    <el-input
                      v-model="changeForm.applicationDescription"
                      type="textarea"
                      :rows="4"
                      placeholder="请详细说明变更申请的背景和理由"
                      />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-tab-pane>

            <!-- 附件材料 -->
            <el-tab-pane label="附件材料" name="attachments">
              <el-form-item label="附件上传">
                <el-upload
                  ref="uploadRef"
                  :file-list="changeForm.attachments"
                  :on-change="handleFileChange"
                  :on-remove="handleFileRemove"
                  :before-upload="() => false"
                  multiple
                  drag
                >
                  <el-icon class="el-icon--upload">
                    <UploadFilled />
                  </el-icon>
                  <div class="el-upload__text">
                    将文件拖到此处，或<em>点击上传</em>
                  </div>
                  <template #tip>
                    <div class="el-upload__tip">
                      支持上传相关证明文件、审批表单等，单个文件大小不超过10MB
                    </div>
                  </template>
                </el-upload>
              </el-form-item>
            </el-tab-pane>
          </el-tabs>
        </el-form>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="changeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmitChange">提交申请</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 变更详情对话框 -->
    <el-dialog v-model="changeDetailDialogVisible" title="变更详情" width="800px">
      <div class="change-detail" v-if="currentChange">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="员工姓名">{{ currentChange.employeeName }}</el-descriptions-item>
          <el-descriptions-item label="合同编号">{{ currentChange.contractNumber }}</el-descriptions-item>
          <el-descriptions-item label="变更类型">
            <el-tag :type="getChangeTypeColor(currentChange.changeType)" size="small">
              {{ getChangeTypeText(currentChange.changeType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="变更状态">
            <el-tag :type="getStatusType(currentChange.changeStatus)" size="small">
              {{ getStatusText(currentChange.changeStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="申请人">{{ currentChange.applicant }}</el-descriptions-item>
          <el-descriptions-item label="申请时间">{{ currentChange.applicationDate }}</el-descriptions-item>
          <el-descriptions-item label="生效日期">{{ currentChange.effectiveDate }}</el-descriptions-item>
          <el-descriptions-item label="变更原因">{{ currentChange.changeReason }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="change-timeline">
          <h4>变更进度</h4>
          <el-timeline>
            <el-timeline-item
              v-for="milestone in changeMilestones"
              :key="milestone.id"
              :timestamp="milestone.timestamp"
              :type="milestone.type"
            >
              <div class="milestone-content">
                <div class="milestone-title">{{ milestone.title }}</div>
                <div class="milestone-description">{{ milestone.description }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-dialog>

    <!-- 审批对话框 -->
    <el-dialog v-model="approvalDialogVisible" title="变更审批" width="600px">
      <div class="approval-form">
        <el-form ref="approvalFormRef" :model="approvalForm" :rules="approvalRules" label-width="100px">
          <el-form-item label="审批结果" prop="result">
            <el-radio-group v-model="approvalForm.result">
              <el-radio value="approved">通过</el-radio>
              <el-radio value="rejected">拒绝</el-radio>
              <el-radio value="pending">待定</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="审批意见" prop="comment">
            <el-input
              v-model="approvalForm.comment"
              type="textarea"
              :rows="4"
              placeholder="请输入审批意见"
              />
          </el-form-item>
          <el-form-item label="审批人" prop="approver">
            <el-input v-model="approvalForm.approver" placeholder="请输入审批人姓名"   />
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="approvalDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmitApproval">提交审批</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 变更项编辑对话框 -->
    <el-dialog v-model="changeItemDialogVisible" title="编辑变更项" width="500px">
      <div class="change-item-form">
        <el-form ref="changeItemFormRef" :model="changeItemForm" :rules="changeItemRules" label-width="100px">
          <el-form-item label="变更项目" prop="field">
            <el-input v-model="changeItemForm.field" placeholder="请输入变更项目"   />
          </el-form-item>
          <el-form-item label="变更前" prop="originalValue">
            <el-input v-model="changeItemForm.originalValue" placeholder="请输入变更前的值"   />
          </el-form-item>
          <el-form-item label="变更后" prop="newValue">
            <el-input v-model="changeItemForm.newValue" placeholder="请输入变更后的值"   />
          </el-form-item>
          <el-form-item label="变更说明" prop="changeReason">
            <el-input
              v-model="changeItemForm.changeReason"
              type="textarea"
              :rows="3"
              placeholder="请输入变更说明"
              />
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="changeItemDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveChangeItem">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'ContractChangeFlow'
})
 
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  Plus,
  Search,
  Clock,
  Loading,
  Check,
  CircleCheck,
  UploadFilled
} from '@element-plus/icons-vue'

// 响应式数据
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const changeDialogVisible = ref(false)
const changeDetailDialogVisible = ref(false)
const approvalDialogVisible = ref(false)
const changeItemDialogVisible = ref(false)
const activeChangeTab = ref('basic')
const changeFormRef = ref()
const approvalFormRef = ref()
const changeItemFormRef = ref()
const uploadRef = ref()
const currentChange = ref(null)
const editingChangeItemIndex = ref(-1)

// 筛选表单
const filterForm = reactive({
  employeeName: '',
  changeType: '',
  changeStatus: ''
})

// 变更统计
const changeStats = reactive({
  pending: 8,
  processing: 12,
  approved: 6,
  executed: 24
})

// 变更申请表单
const changeForm = reactive({
  employeeName: '',
  contractNumber: '',
  changeType: '',
  effectiveDate: '',
  changeReason: '',
  urgency: 'normal',
  approver: '',
  ccRecipients: [],
  applicationDescription: '',
  attachments: []
})

// 变更申请验证规则
const changeRules = {
  employeeName: [
    { required: true, message: '请输入员工姓名', trigger: 'blur' }
  ],
  contractNumber: [
    { required: true, message: '请输入合同编号', trigger: 'blur' }
  ],
  changeType: [
    { required: true, message: '请选择变更类型', trigger: 'change' }
  ],
  effectiveDate: [
    { required: true, message: '请选择生效日期', trigger: 'change' }
  ],
  changeReason: [
    { required: true, message: '请选择变更原因', trigger: 'change' }
  ],
  applicationDescription: [
    { required: true, message: '请输入申请说明', trigger: 'blur' }
  ]
}

// 变更详情
const changeDetails = ref([
  {
    field: '基本工资',
    originalValue: '8000元/月',
    newValue: '10000元/月',
    changeReason: '工作表现优秀，予以调薪'
  },
  {
    field: '岗位津贴',
    originalValue: '1000元/月',
    newValue: '1500元/月',
    changeReason: '岗位职责增加，调整津贴标准'
  }
])

// 变更项表单
const changeItemForm = reactive({
  field: '',
  originalValue: '',
  newValue: '',
  changeReason: ''
})

// 变更项验证规则
const changeItemRules = {
  field: [
    { required: true, message: '请输入变更项目', trigger: 'blur' }
  ],
  originalValue: [
    { required: true, message: '请输入变更前的值', trigger: 'blur' }
  ],
  newValue: [
    { required: true, message: '请输入变更后的值', trigger: 'blur' }
  ]
}

// 审批表单
const approvalForm = reactive({
  result: 'approved',
  comment: '',
  approver: ''
})

// 审批验证规则
const approvalRules = {
  result: [
    { required: true, message: '请选择审批结果', trigger: 'change' }
  ],
  comment: [
    { required: true, message: '请输入审批意见', trigger: 'blur' }
  ],
  approver: [
    { required: true, message: '请输入审批人姓名', trigger: 'blur' }
  ]
}

// 变更列表数据
const changeList = ref([
  {
    id: '1',
    employeeName: '张三',
    contractNumber: 'HKY2025001',
    changeType: 'salary',
    changeStatus: 'pending',
    applicant: '人事专员',
    applicationDate: '2025-01-23 09:00:00',
    effectiveDate: '2025-02-01',
    changeReason: 'work_need'
  },
  {
    id: '2',
    employeeName: '李四',
    contractNumber: 'HKY2025002',
    changeType: 'position',
    changeStatus: 'approving',
    applicant: '部门经理',
    applicationDate: '2025-01-22 14:30:00',
    effectiveDate: '2025-02-15',
    changeReason: 'position_adjustment'
  },
  {
    id: '3',
    employeeName: '王五',
    contractNumber: 'HKY2025003',
    changeType: 'department',
    changeStatus: 'approved',
    applicant: '人事经理',
    applicationDate: '2025-01-21 10:00:00',
    effectiveDate: '2025-02-10',
    changeReason: 'organization_change'
  }
])

// 变更进度里程碑
const changeMilestones = ref([
  {
    id: '1',
    timestamp: '2025-01-23 09:00:00',
    type: 'primary',
    title: '变更申请提交',
    description: '申请人提交变更申请，等待审批'
  },
  {
    id: '2',
    timestamp: '2025-01-23 10:30:00',
    type: 'success',
    title: '部门经理审批',
    description: '部门经理审批通过，提交人事审批'
  },
  {
    id: '3',
    timestamp: '2025-01-23 14:00:00',
    type: 'success',
    title: '人事审批通过',
    description: '人事部门审批通过，准备执行变更'
  },
  {
    id: '4',
    timestamp: '2025-01-24 09:00:00',
    type: 'warning',
    title: '变更执行中',
    description: '正在执行变更，更新合同信息'
  }
])

// 方法
const handleRefresh = () => {
  ElMessage.success('刷新成功')
}

const handleStartChange = () => {
  changeDialogVisible.value = true
  // 重置表单
  Object.assign(changeForm, {
    employeeName: '',
    contractNumber: '',
    changeType: '',
    effectiveDate: '',
    changeReason: '',
    urgency: 'normal',
    approver: '',
    ccRecipients: [],
    applicationDescription: '',
    attachments: []
  })
  changeDetails.value = []
}

const handleFilter = () => {
  ElMessage.success('筛选查询')
}

const handleResetFilter = () => {
  Object.assign(filterForm, {
    employeeName: '',
    changeType: '',
    changeStatus: ''
  })
  ElMessage.success('筛选条件已重置')
}

   
const handleViewChange = (row: unknown) => {
  currentChange.value = row
  changeDetailDialogVisible.value = true
}

   
const handleProcessChange = (row: unknown) => {
  ElMessage.success(`开始处理变更申请: ${row.employeeName}`)
}

   
const handleApproveChange = (row: unknown) => {
  currentChange.value = row
  approvalDialogVisible.value = true
  // 重置审批表单
  Object.assign(approvalForm, {
    result: 'approved',
    comment: '',
    approver: ''
  })
}

   
const handleExecuteChange = (row: unknown) => {
  ElMessageBox.confirm('确定要执行此变更吗？', '确认执行', {
    type: 'warning'
  }).then(() => {
    row.changeStatus = 'executed'
    ElMessage.success('变更执行成功')
  }).catch(() => {
    ElMessage.info('已取消执行')
  })
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  ElMessage.success(`每页显示 ${size} 条`)
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  ElMessage.success(`切换到第 ${page} 页`)
}

const handleSubmitChange = () => {
  changeFormRef.value.validate((valid: boolean) => {
    if (valid) {
      changeDialogVisible.value = false
      ElMessage.success('变更申请提交成功')
      // 添加到变更列表
      changeList.value.unshift({
        id: Date.now().toString(),
        employeeName: changeForm.employeeName,
        contractNumber: changeForm.contractNumber,
        changeType: changeForm.changeType,
        changeStatus: 'pending',
        applicant: '当前用户',
        applicationDate: new Date().toLocaleString(),
        effectiveDate: changeForm.effectiveDate,
        changeReason: changeForm.changeReason
      })
    }
  })
}

const handleSubmitApproval = () => {
  approvalFormRef.value.validate((valid: boolean) => {
    if (valid) {
      approvalDialogVisible.value = false
      ElMessage.success('审批提交成功')
      // 更新变更状态
      if (currentChange.value) {
        const index = changeList.value.findIndex(item => item.id === currentChange.value.id)
        if (index !== -1) {
          changeList.value[index].changeStatus = approvalForm.result
        }
      }
    }
  })
}

const handleAddChangeItem = () => {
  editingChangeItemIndex.value = -1
  Object.assign(changeItemForm, {
    field: '',
    originalValue: '',
    newValue: '',
    changeReason: ''
  })
  changeItemDialogVisible.value = true
}

   
const handleEditChangeDetail = (row: unknown) => {
  editingChangeItemIndex.value = changeDetails.value.findIndex(item => item === row)
  Object.assign(changeItemForm, row)
  changeItemDialogVisible.value = true
}

   
const handleDeleteChangeDetail = (row: unknown) => {
  const index = changeDetails.value.findIndex(item => item === row)
  if (index !== -1) {
    changeDetails.value.splice(index, 1)
    ElMessage.success('删除成功')
  }
}

const handleSaveChangeItem = () => {
  changeItemFormRef.value.validate((valid: boolean) => {
    if (valid) {
      if (editingChangeItemIndex.value === -1) {
        // 添加新项
        changeDetails.value.push({ ...changeItemForm })
      } else {
        // 编辑现有项
        Object.assign(changeDetails.value[editingChangeItemIndex.value], changeItemForm)
      }
      changeItemDialogVisible.value = false
      ElMessage.success('保存成功')
    }
  })
}

   
const handleFileChange = (file: unknown) => {
  changeForm.attachments.push(file)
  ElMessage.success(`文件 ${file.name} 上传成功`)
}

   
const handleFileRemove = (file: unknown) => {
  const index = changeForm.attachments.findIndex(item => item.uid === file.uid)
  if (index !== -1) {
    changeForm.attachments.splice(index, 1)
  }
}

const getChangeTypeColor = (type: string) => {
  const colorMap = {
    'salary': 'success',
    'position': 'warning',
    'department': 'info',
    'location': 'primary',
    'term': 'danger'
  }
  return colorMap[type] || 'info'
}

const getChangeTypeText = (type: string) => {
  const textMap = {
    'salary': '薪资变更',
    'position': '岗位变更',
    'department': '部门变更',
    'location': '工作地点变更',
    'term': '合同期限变更'
  }
  return textMap[type] || '未知类型'
}

const getStatusType = (status: string) => {
  const statusMap = {
    'pending': 'info',
    'approving': 'warning',
    'approved': 'success',
    'rejected': 'danger',
    'executed': 'success'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap = {
    'pending': '待审批',
    'approving': '审批中',
    'approved': '已通过',
    'rejected': '已拒绝',
    'executed': '已执行'
  }
  return statusMap[status] || '未知状态'
}

// 生命周期
onMounted(() => {
  total.value = changeList.value.length
})
</script>

<style scoped>
.contract-change-flow {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.change-list {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.change-stats {
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.stat-card.pending .stat-icon {
  background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
}

.stat-card.processing .stat-icon {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
}

.stat-card.approved .stat-icon {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.stat-card.executed .stat-icon {
  background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.change-form {
  margin-top: 20px;
}

.change-content {
  margin-top: 20px;
}

.change-content h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.original-value {
  color: #f56565;
  text-decoration: line-through;
}

.new-value {
  color: #38a169;
  font-weight: 600;
}

.add-change-item {
  margin-top: 20px;
  text-align: center;
}

.change-detail {
  margin-top: 20px;
}

.change-timeline {
  margin-top: 30px;
}

.change-timeline h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.milestone-content {
  padding: 8px 0;
}

.milestone-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.milestone-description {
  color: #606266;
  font-size: 14px;
}

.approval-form,
.change-item-form {
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}

.el-pagination {
  margin-top: 20px;
  text-align: center;
}
</style>