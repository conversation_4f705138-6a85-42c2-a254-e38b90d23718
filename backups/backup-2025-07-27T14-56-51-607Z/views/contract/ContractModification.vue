<template>
  <div class="contract-modification">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>合同变更管理</h2>
      <p>管理教职工合同变更申请、审批和执行流程</p>
    </div>

    <!-- 搜索和操作区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-form">
        <el-row :gutter="20">
          <el-col :span="5">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索姓名、工号、申请编号"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.modificationType" placeholder="变更类型" clearable>
              <el-option
                v-for="item in modificationTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.status" placeholder="申请状态" clearable>
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.department" placeholder="所属部门" clearable>
              <el-option label="全部部门" value=""  />
              <el-option
                v-for="dept in departmentOptions"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
               />
            </el-select>
          </el-col>
          <el-col :span="7">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
            <el-button type="success" @click="handleAdd">
              <el-icon><Plus /></el-icon>
              新增变更
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.total }}</div>
              <div class="stats-label">总申请数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon pending">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.pending }}</div>
              <div class="stats-label">待审批</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon approved">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.approved }}</div>
              <div class="stats-label">已通过</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon rejected">
              <el-icon><Close /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.rejected }}</div>
              <div class="stats-label">已拒绝</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 变更申请列表 -->
    <el-card class="list-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>变更申请列表</h3>
          <div class="header-actions">
            <el-button @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="primary" @click="handleBatchApprove">
              <el-icon><Check /></el-icon>
              批量审批
            </el-button>
          </div>
        </div>
      </template>

      <el-table 
        :data="modificationList" 
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50"  />
        <el-table-column prop="applicationNumber" label="申请编号" width="140"  />
        <el-table-column prop="employeeName" label="申请人" width="100"  />
        <el-table-column prop="department" label="部门" width="150" show-overflow-tooltip  />
        <el-table-column prop="contractNumber" label="合同编号" width="140"  />
        <el-table-column prop="modificationType" label="变更类型" width="120">
          <template #default="scope">
            <el-tag :type="getModificationTypeTag(scope.row.modificationType)" size="small">
              {{ getModificationTypeLabel(scope.row.modificationType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="reason" label="变更原因" show-overflow-tooltip  />
        <el-table-column prop="applicationDate" label="申请日期" width="120"  />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusTag(scope.row.status)" size="small">
              {{ getStatusLabel(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="approver" label="审批人" width="100"  />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="handleView(scope.row)">
              <el-icon><View /></el-icon>
              查看
            </el-button>
            <el-button 
              v-if="scope.row.status === 'PENDING'"
              size="small" 
              type="primary" 
              @click="handleApprove(scope.row)"
            >
              <el-icon><Check /></el-icon>
              审批
            </el-button>
            <el-button 
              v-if="scope.row.status === 'PENDING'"
              size="small" 
              @click="handleEdit(scope.row)"
            >
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        style="margin-top: 20px; text-align: right;"
       />
    </el-card>

    <!-- 新增/编辑变更申请对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="dialogTitle"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form ref="formRef" :model="currentForm" :rules="formRules" label-width="120px">
        <el-tabs v-model="activeTab" type="card">
          <!-- 基本信息 -->
          <el-tab-pane label="基本信息" name="basic">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="申请编号" prop="applicationNumber">
                  <el-input v-model="currentForm.applicationNumber" disabled   />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="申请人" prop="employeeName">
                  <el-select 
                    v-model="currentForm.employeeId" 
                    placeholder="请选择申请人"
                    filterable
                    @change="handleEmployeeChange"
                  >
                    <el-option
                      v-for="employee in employeeOptions"
                      :key="employee.id"
                      :label="employee.name"
                      :value="employee.id"
                     />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="合同编号" prop="contractNumber">
                  <el-input v-model="currentForm.contractNumber" disabled   />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="变更类型" prop="modificationType">
                  <el-select v-model="currentForm.modificationType" placeholder="请选择变更类型">
                    <el-option
                      v-for="item in modificationTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                     />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="生效日期" prop="effectiveDate">
                  <el-date-picker
                    v-model="currentForm.effectiveDate"
                    type="date"
                    placeholder="选择生效日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                   />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="申请日期" prop="applicationDate">
                  <el-date-picker
                    v-model="currentForm.applicationDate"
                    type="date"
                    placeholder="选择申请日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                   />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="变更原因" prop="reason">
                  <el-input
                    v-model="currentForm.reason"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入变更原因"
                    />
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>

          <!-- 变更内容 -->
          <el-tab-pane label="变更内容" name="content">
            <div class="modification-content">
              <h4>变更前</h4>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="职位" prop="originalPosition">
                    <el-input v-model="currentForm.originalPosition"   />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="部门" prop="originalDepartment">
                    <el-input v-model="currentForm.originalDepartment"   />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="薪资" prop="originalSalary">
                    <el-input-number 
                      v-model="currentForm.originalSalary"
                      :min="0"
                      :precision="2"
                      placeholder="原薪资"
                      />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="合同期限" prop="originalContractTerm">
                    <el-input v-model="currentForm.originalContractTerm"   />
                  </el-form-item>
                </el-col>
              </el-row>

              <h4>变更后</h4>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="职位" prop="newPosition">
                    <el-input v-model="currentForm.newPosition"   />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="部门" prop="newDepartment">
                    <el-input v-model="currentForm.newDepartment"   />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="薪资" prop="newSalary">
                    <el-input-number 
                      v-model="currentForm.newSalary"
                      :min="0"
                      :precision="2"
                      placeholder="新薪资"
                      />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="合同期限" prop="newContractTerm">
                    <el-input v-model="currentForm.newContractTerm"   />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>

          <!-- 附件材料 -->
          <el-tab-pane label="附件材料" name="attachment">
            <el-upload
              ref="uploadRef"
              :file-list="currentForm.attachments"
              :on-change="handleFileChange"
              :on-remove="handleFileRemove"
              :auto-upload="false"
              multiple
              drag
            >
              <el-icon class="el-icon--upload"><upload-filled /></el-icon>
              <div class="el-upload__text">
                将文件拖拽到此处或<em>点击选择文件</em>
              </div>
              <template #tip>
                <div class="el-upload__tip">
                  支持上传相关证明材料，支持jpg/png/pdf/doc/docx格式，单个文件不超过10MB
                </div>
              </template>
            </el-upload>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSave">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 审批对话框 -->
    <el-dialog v-model="approvalDialogVisible" title="审批变更申请" width="600px">
      <el-form ref="approvalFormRef" :model="approvalForm" label-width="100px">
        <el-form-item label="申请编号">
          <span>{{ approvalForm.applicationNumber }}</span>
        </el-form-item>
        <el-form-item label="申请人">
          <span>{{ approvalForm.employeeName }}</span>
        </el-form-item>
        <el-form-item label="变更类型">
          <span>{{ getModificationTypeLabel(approvalForm.modificationType) }}</span>
        </el-form-item>
        <el-form-item label="审批结果" prop="approvalResult">
          <el-radio-group v-model="approvalForm.approvalResult">
            <el-radio value="APPROVED">通过</el-radio>
            <el-radio value="REJECTED">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审批意见" prop="approvalComment">
          <el-input
            v-model="approvalForm.approvalComment"
            type="textarea"
            :rows="4"
            placeholder="请输入审批意见"
            />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="approvalDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmApproval">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog v-model="viewDialogVisible" title="变更申请详情" width="800px">
      <div v-if="viewData" class="view-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="申请编号">{{ viewData.applicationNumber }}</el-descriptions-item>
          <el-descriptions-item label="申请人">{{ viewData.employeeName }}</el-descriptions-item>
          <el-descriptions-item label="部门">{{ viewData.department }}</el-descriptions-item>
          <el-descriptions-item label="合同编号">{{ viewData.contractNumber }}</el-descriptions-item>
          <el-descriptions-item label="变更类型">
            <el-tag :type="getModificationTypeTag(viewData.modificationType)" size="small">
              {{ getModificationTypeLabel(viewData.modificationType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="申请日期">{{ viewData.applicationDate }}</el-descriptions-item>
          <el-descriptions-item label="生效日期">{{ viewData.effectiveDate }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTag(viewData.status)" size="small">
              {{ getStatusLabel(viewData.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="变更原因" :span="2">{{ viewData.reason }}</el-descriptions-item>
        </el-descriptions>

        <div class="comparison-section">
          <h4>变更对比</h4>
          <el-table :data="comparisonData" style="width: 100%">
            <el-table-column prop="field" label="字段" width="120"  />
            <el-table-column prop="original" label="变更前"  />
            <el-table-column prop="new" label="变更后"  />
            <el-table-column label="变化" width="100">
              <template #default="scope">
                <el-tag 
                  v-if="scope.row.original !== scope.row.new"
                  type="warning" 
                  size="small"
                >
                  已变更
                </el-tag>
                <el-tag 
                  v-else
                  type="info" 
                  size="small"
                >
                  无变化
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div v-if="viewData.approvalComment" class="approval-section">
          <h4>审批意见</h4>
          <p>{{ viewData.approvalComment }}</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  Document,
  Clock,
  Check,
  Close,
  View,
  Edit,
  UploadFilled
} from '@element-plus/icons-vue'
import { organizationApi } from '@/api/organization'

// 响应式数据
const searchForm = reactive({
  keyword: '',
  modificationType: '',
  status: '',
  department: ''
})

const stats = reactive({
  total: 86,
  pending: 12,
  approved: 58,
  rejected: 16
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

const dialogVisible = ref(false)
const approvalDialogVisible = ref(false)
const viewDialogVisible = ref(false)
const activeTab = ref('basic')
const formRef = ref()
const approvalFormRef = ref()
const uploadRef = ref()

// 部门选项数据
const departmentOptions = ref<any[]>([])

// 选项数据
const modificationTypeOptions = [
  { label: '职位变更', value: 'POSITION_CHANGE' },
  { label: '部门调动', value: 'DEPARTMENT_TRANSFER' },
  { label: '薪资调整', value: 'SALARY_ADJUSTMENT' },
  { label: '合同期限变更', value: 'CONTRACT_TERM_CHANGE' },
  { label: '工作地点变更', value: 'WORKPLACE_CHANGE' }
]

const statusOptions = [
  { label: '待审批', value: 'PENDING' },
  { label: '已通过', value: 'APPROVED' },
  { label: '已拒绝', value: 'REJECTED' },
  { label: '已撤销', value: 'WITHDRAWN' }
]

const employeeOptions = ref([
  { id: '1', name: 'HrHr张三', contractNumber: 'HKY2025001' },
  { id: '2', name: '李四', contractNumber: 'HKY2025002' },
  { id: '3', name: '王五', contractNumber: 'HKY2025003' }
])

// 列表数据
const modificationList = ref([
  {
    id: '1',
    applicationNumber: 'MOD2025001',
    employeeName: '张三',
    department: '计算机学院',
    contractNumber: 'HKY2025001',
    modificationType: 'POSITION_CHANGE',
    reason: '工作调整需要',
    applicationDate: '2025-01-15',
    effectiveDate: '2025-02-01',
    status: 'PENDING',
    approver: '李主任',
    originalPosition: '助教',
    newPosition: '讲师',
    originalDepartment: '计算机学院',
    newDepartment: '计算机学院',
    originalSalary: 6000,
    newSalary: 8000,
    originalContractTerm: '1年',
    newContractTerm: '3年'
  },
  {
    id: '2',
    applicationNumber: 'MOD2025002',
    employeeName: '李四',
    department: '机械工程学院',
    contractNumber: 'HKY2025002',
    modificationType: 'DEPARTMENT_TRANSFER',
    reason: '部门重组调整',
    applicationDate: '2025-01-10',
    effectiveDate: '2025-01-20',
    status: 'APPROVED',
    approver: '王处长',
    approvalComment: '同意调动，请按时办理交接手续',
    originalPosition: '工程师',
    newPosition: '高级工程师',
    originalDepartment: '机械工程学院',
    newDepartment: '自动化学院',
    originalSalary: 9000,
    newSalary: 11000,
    originalContractTerm: '2年',
    newContractTerm: '2年'
  }
])

const selectedList = ref([])

// 表单数据
const currentForm = ref({
  id: '',
  applicationNumber: '',
  employeeId: '',
  employeeName: '',
  contractNumber: '',
  modificationType: '',
  reason: '',
  applicationDate: '',
  effectiveDate: '',
  originalPosition: '',
  newPosition: '',
  originalDepartment: '',
  newDepartment: '',
  originalSalary: 0,
  newSalary: 0,
  originalContractTerm: '',
  newContractTerm: '',
  attachments: []
})

// 审批表单数据
const approvalForm = ref({
  id: '',
  applicationNumber: '',
  employeeName: '',
  modificationType: '',
  approvalResult: '',
  approvalComment: ''
})

// 查看数据
const viewData = ref(null)
const comparisonData = ref([])

// 表单验证规则
const formRules = {
  employeeId: [
    { required: true, message: '请选择申请人', trigger: 'change' }
  ],
  modificationType: [
    { required: true, message: '请选择变更类型', trigger: 'change' }
  ],
  reason: [
    { required: true, message: '请输入变更原因', trigger: 'blur' }
  ],
  applicationDate: [
    { required: true, message: '请选择申请日期', trigger: 'change' }
  ],
  effectiveDate: [
    { required: true, message: '请选择生效日期', trigger: 'change' }
  ]
}

// 计算属性
const dialogTitle = computed(() => {
  return currentForm.value.id ? '编辑变更申请' : '新增变更申请'
})

// 方法
const handleSearch = () => {
  ElMessage.success('搜索功能执行中...')
}

const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    modificationType: '',
    status: '',
    department: ''
  })
}

const handleRefresh = () => {
  ElMessage.success('数据已刷新')
}

const handleAdd = () => {
  currentForm.value = {
    id: '',
    applicationNumber: generateApplicationNumber(),
    employeeId: '',
    employeeName: '',
    contractNumber: '',
    modificationType: '',
    reason: '',
    applicationDate: new Date().toISOString().slice(0, 10),
    effectiveDate: '',
    originalPosition: '',
    newPosition: '',
    originalDepartment: '',
    newDepartment: '',
    originalSalary: 0,
    newSalary: 0,
    originalContractTerm: '',
    newContractTerm: '',
    attachments: []
  }
  activeTab.value = 'basic'
  dialogVisible.value = true
}

   
const handleEdit = (row: unknown) => {
  currentForm.value = { ...row }
  activeTab.value = 'basic'
  dialogVisible.value = true
}

   
const handleView = (row: unknown) => {
  viewData.value = row
  comparisonData.value = [
    {
      field: '职位',
      original: row.originalPosition,
      new: row.newPosition
    },
    {
      field: '部门',
      original: row.originalDepartment,
      new: row.newDepartment
    },
    {
      field: '薪资',
      original: `¥${row.originalSalary}`,
      new: `¥${row.newSalary}`
    },
    {
      field: '合同期限',
      original: row.originalContractTerm,
      new: row.newContractTerm
    }
  ]
  viewDialogVisible.value = true
}

   
const handleApprove = (row: unknown) => {
  approvalForm.value = {
    id: row.id,
    applicationNumber: row.applicationNumber,
    employeeName: row.employeeName,
    modificationType: row.modificationType,
    approvalResult: '',
    approvalComment: ''
  }
  approvalDialogVisible.value = true
}

const handleSave = () => {
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      if (currentForm.value.id) {
        // 编辑
        const index = modificationList.value.findIndex(item => item.id === currentForm.value.id)
        if (index > -1) {
          modificationList.value[index] = { ...currentForm.value }
          ElMessage.success('更新成功')
        }
      } else {
        // 新增
        currentForm.value.id = Date.now().toString()
        modificationList.value.unshift({ ...currentForm.value })
        ElMessage.success('添加成功')
      }
      dialogVisible.value = false
    }
  })
}

const handleConfirmApproval = () => {
  if (!approvalForm.value.approvalResult) {
    ElMessage.warning('请选择审批结果')
    return
  }
  if (!approvalForm.value.approvalComment) {
    ElMessage.warning('请输入审批意见')
    return
  }
  
  const index = modificationList.value.findIndex(item => item.id === approvalForm.value.id)
  if (index > -1) {
    modificationList.value[index].status = approvalForm.value.approvalResult
    modificationList.value[index].approvalComment = approvalForm.value.approvalComment
    ElMessage.success('审批成功')
  }
  approvalDialogVisible.value = false
}

   
const handleSelectionChange = (selection: unknown[]) => {
  selectedList.value = selection
}

const handleBatchApprove = () => {
  if (selectedList.value.length === 0) {
    ElMessage.warning('请先选择要审批的申请')
    return
  }
  ElMessage.info('批量审批功能开发中...')
}

const handleEmployeeChange = (employeeId: string) => {
  const employee = employeeOptions.value.find(emp => emp.id === employeeId)
  if (employee) {
    currentForm.value.employeeName = employee.name
    currentForm.value.contractNumber = employee.contractNumber
  }
}

   
const handleFileChange = (file: unknown) => {
  console.log('文件变化:', file)
}

   
const handleFileRemove = (file: unknown) => {
  console.log('文件删除:', file)
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
}

// 辅助方法
const generateApplicationNumber = () => {
  const date = new Date()
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  return `MOD${year}${month}${day}${random}`
}

const getModificationTypeTag = (type: string) => {
  switch (type) {
    case 'POSITION_CHANGE': return 'primary'
    case 'DEPARTMENT_TRANSFER': return 'success'
    case 'SALARY_ADJUSTMENT': return 'warning'
    case 'CONTRACT_TERM_CHANGE': return 'info'
    case 'WORKPLACE_CHANGE': return 'danger'
    default: return ''
  }
}

const getModificationTypeLabel = (type: string) => {
  const option = modificationTypeOptions.find(opt => opt.value === type)
  return option ? option.label : type
}

const getStatusTag = (status: string) => {
  switch (status) {
    case 'PENDING': return 'warning'
    case 'APPROVED': return 'success'
    case 'REJECTED': return 'danger'
    case 'WITHDRAWN': return 'info'
    default: return ''
  }
}

const getStatusLabel = (status: string) => {
  const option = statusOptions.find(opt => opt.value === status)
  return option ? option.label : status
}

// 获取部门列表
const fetchDepartments = async () => {
  try {
    const response = await organizationApi.getTree()
    if (response && Array.isArray(response)) {
      departmentOptions.value = response.map(dept => ({
        id: dept.id,
        name: dept.name,
        orgCode: dept.orgCode
      }))
    }
  } catch (__error) {
    console.error('获取部门列表失败:', error)
    ElMessage.error('获取部门列表失败')
  }
}

// 生命周期
onMounted(() => {
  pagination.total = modificationList.value.length
  fetchDepartments()
})
</script>

<style scoped>
.contract-modification {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  padding: 10px;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stats-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 20px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.pending {
  background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
}

.stats-icon.approved {
  background: linear-gradient(135deg, #1dd1a1 0%, #10ac84 100%);
}

.stats-icon.rejected {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 12px;
  color: #909399;
}

.list-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.modification-content h4 {
  margin: 20px 0 10px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.comparison-section,
.approval-section {
  margin-top: 20px;
}

.comparison-section h4,
.approval-section h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.view-content {
  max-height: 500px;
  overflow-y: auto;
}

.dialog-footer {
  text-align: right;
}
</style>