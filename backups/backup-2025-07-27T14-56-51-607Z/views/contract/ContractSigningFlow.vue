<template>
  <div class="contract-signing-flow">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>合同签订流程</h2>
      <p>管理合同签订全流程，包括模板选择、信息填充、审批和签章</p>
    </div>

    <!-- 流程步骤 -->
    <el-card class="flow-steps" shadow="never">
      <el-steps :active="currentStep" :process-status="stepStatus" finish-status="success">
        <el-step title="选择模板" description="选择合同模板类型"  />
        <el-step title="填写信息" description="填写合同基本信息"  />
        <el-step title="审核确认" description="审核合同内容"  />
        <el-step title="签章确认" description="双方签章确认"  />
        <el-step title="归档完成" description="合同归档生效"  />
      </el-steps>
    </el-card>

    <!-- 步骤内容 -->
    <el-card class="step-content" shadow="never">
      <!-- 第一步：选择模板 -->
      <div v-if="currentStep === 0" class="step-section">
        <h3>选择合同模板</h3>
        <div class="template-selection">
          <el-row :gutter="20">
            <el-col :span="8" v-for="template in contractTemplates" :key="template.id">
              <el-card 
                class="template-card" 
                :class="{ 'selected': selectedTemplate?.id === template.id }"
                @click="selectTemplate(template)"
                shadow="hover"
              >
                <div class="template-content">
                  <div class="template-icon">
                    <el-icon><Document /></el-icon>
                  </div>
                  <div class="template-info">
                    <h4>{{ template.name }}</h4>
                    <p>{{ template.description }}</p>
                    <div class="template-meta">
                      <span class="template-type">{{ template.type }}</span>
                      <span class="template-version">v{{ template.version }}</span>
                    </div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 第二步：填写信息 -->
      <div v-if="currentStep === 1" class="step-section">
        <h3>填写合同信息</h3>
        <el-form ref="contractFormRef" :model="contractForm" :rules="contractRules" label-width="120px">
          <el-tabs v-model="activeFormTab" type="card">
            <!-- 基本信息 -->
            <el-tab-pane label="基本信息" name="basic">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="合同编号" prop="contractNumber">
                    <el-input v-model="contractForm.contractNumber" disabled   />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="合同类型" prop="contractType">
                    <el-input v-model="contractForm.contractType" disabled   />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="签订日期" prop="signDate">
                    <el-date-picker
                      v-model="contractForm.signDate"
                      type="date"
                      placeholder="选择签订日期"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                     />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="生效日期" prop="effectiveDate">
                    <el-date-picker
                      v-model="contractForm.effectiveDate"
                      type="date"
                      placeholder="选择生效日期"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                     />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="合同期限" prop="contractTerm">
                    <el-input-number
                      v-model="contractForm.contractTerm"
                      :min="1"
                      :max="10"
                      controls-position="right"
                      />
                    <span style="margin-left: 10px;">年</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="试用期" prop="probationPeriod">
                    <el-input-number
                      v-model="contractForm.probationPeriod"
                      :min="0"
                      :max="12"
                      controls-position="right"
                      />
                    <span style="margin-left: 10px;">个月</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-tab-pane>

            <!-- 员工信息 -->
            <el-tab-pane label="员工信息" name="employee">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="员工姓名" prop="employee.name">
                    <el-input v-model="contractForm.employee.name"   />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="身份证号" prop="employee.idNumber">
                    <el-input v-model="contractForm.employee.idNumber"   />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="联系电话" prop="employee.phone">
                    <el-input v-model="contractForm.employee.phone"   />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="邮箱地址" prop="employee.email">
                    <el-input v-model="contractForm.employee.email"   />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="所属部门" prop="employee.department">
                    <el-select v-model="contractForm.employee.department" placeholder="请选择部门">
                      <el-option
                        v-for="dept in departments"
                        :key="dept.id"
                        :label="dept.name"
                        :value="dept.id"
                       />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="职位" prop="employee.position">
                    <el-input v-model="contractForm.employee.position"   />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-tab-pane>

            <!-- 薪资信息 -->
            <el-tab-pane label="薪资信息" name="salary">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="基本工资" prop="salary.basicSalary">
                    <el-input-number
                      v-model="contractForm.salary.basicSalary"
                      :min="0"
                      :precision="2"
                      controls-position="right"
                      />
                    <span style="margin-left: 10px;">元/月</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="岗位津贴" prop="salary.positionAllowance">
                    <el-input-number
                      v-model="contractForm.salary.positionAllowance"
                      :min="0"
                      :precision="2"
                      controls-position="right"
                      />
                    <span style="margin-left: 10px;">元/月</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="绩效工资" prop="salary.performanceSalary">
                    <el-input-number
                      v-model="contractForm.salary.performanceSalary"
                      :min="0"
                      :precision="2"
                      controls-position="right"
                      />
                    <span style="margin-left: 10px;">元/月</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="其他补贴" prop="salary.otherAllowance">
                    <el-input-number
                      v-model="contractForm.salary.otherAllowance"
                      :min="0"
                      :precision="2"
                      controls-position="right"
                      />
                    <span style="margin-left: 10px;">元/月</span>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="薪资总额">
                    <el-input :value="totalSalary" disabled>
                      <template #append>元/月</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-tab-pane>

            <!-- 合同条款 -->
            <el-tab-pane label="合同条款" name="terms">
              <el-form-item label="工作内容" prop="terms.workContent">
                <el-input
                  v-model="contractForm.terms.workContent"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入工作内容和要求"
                  />
              </el-form-item>
              <el-form-item label="工作时间" prop="terms.workTime">
                <el-input
                  v-model="contractForm.terms.workTime"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入工作时间安排"
                  />
              </el-form-item>
              <el-form-item label="福利待遇" prop="terms.benefits">
                <el-input
                  v-model="contractForm.terms.benefits"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入福利待遇说明"
                  />
              </el-form-item>
              <el-form-item label="其他条款" prop="terms.otherTerms">
                <el-input
                  v-model="contractForm.terms.otherTerms"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入其他特殊条款"
                  />
              </el-form-item>
            </el-tab-pane>
          </el-tabs>
        </el-form>
      </div>

      <!-- 第三步：审核确认 -->
      <div v-if="currentStep === 2" class="step-section">
        <h3>审核确认</h3>
        <div class="contract-preview">
          <div class="preview-header">
            <h4>合同预览</h4>
            <el-button @click="handlePreviewContract">
              <el-icon><View /></el-icon>
              预览合同
            </el-button>
          </div>
          
          <div class="contract-summary">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="合同编号">{{ contractForm.contractNumber }}</el-descriptions-item>
              <el-descriptions-item label="合同类型">{{ contractForm.contractType }}</el-descriptions-item>
              <el-descriptions-item label="员工姓名">{{ contractForm.employee.name }}</el-descriptions-item>
              <el-descriptions-item label="身份证号">{{ contractForm.employee.idNumber }}</el-descriptions-item>
              <el-descriptions-item label="所属部门">{{ getDepartmentName(contractForm.employee.department) }}</el-descriptions-item>
              <el-descriptions-item label="职位">{{ contractForm.employee.position }}</el-descriptions-item>
              <el-descriptions-item label="合同期限">{{ contractForm.contractTerm }}年</el-descriptions-item>
              <el-descriptions-item label="试用期">{{ contractForm.probationPeriod }}个月</el-descriptions-item>
              <el-descriptions-item label="薪资总额">{{ totalSalary }}元/月</el-descriptions-item>
              <el-descriptions-item label="生效日期">{{ contractForm.effectiveDate }}</el-descriptions-item>
            </el-descriptions>
          </div>

          <div class="approval-section">
            <h4>审批意见</h4>
            <el-form :model="approvalForm" label-width="100px">
              <el-form-item label="审批结果">
                <el-radio-group v-model="approvalForm.result">
                  <el-radio value="approved">通过</el-radio>
                  <el-radio value="rejected">驳回</el-radio>
                  <el-radio value="pending">待定</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="审批意见">
                <el-input
                  v-model="approvalForm.comment"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入审批意见"
                  />
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>

      <!-- 第四步：签章确认 -->
      <div v-if="currentStep === 3" class="step-section">
        <h3>签章确认</h3>
        <div class="signature-section">
          <el-alert
            title="签章说明"
            type="info"
            description="合同需要双方签章确认后生效，请按照顺序完成签章操作"
            :closable="false"
            show-icon
           />
          
          <div class="signature-parties">
            <div class="party-section">
              <h4>甲方签章（用人单位）</h4>
              <div class="signature-box" :class="{ 'signed': signatures.partyA }">
                <div v-if="signatures.partyA" class="signature-content">
                  <img :src="signatures.partyA.image" alt="甲方签章" />
                  <p>{{ signatures.partyA.signerName }}</p>
                  <p>{{ signatures.partyA.signTime }}</p>
                </div>
                <div v-else class="signature-placeholder">
                  <el-button type="primary" @click="handleSignature('partyA')">
                    <el-icon><EditPen /></el-icon>
                    甲方签章
                  </el-button>
                </div>
              </div>
            </div>

            <div class="party-section">
              <h4>乙方签章（员工）</h4>
              <div class="signature-box" :class="{ 'signed': signatures.partyB }">
                <div v-if="signatures.partyB" class="signature-content">
                  <img :src="signatures.partyB.image" alt="乙方签章" />
                  <p>{{ signatures.partyB.signerName }}</p>
                  <p>{{ signatures.partyB.signTime }}</p>
                </div>
                <div v-else class="signature-placeholder">
                  <el-button type="primary" @click="handleSignature('partyB')">
                    <el-icon><EditPen /></el-icon>
                    乙方签章
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 第五步：归档完成 -->
      <div v-if="currentStep === 4" class="step-section">
        <h3>归档完成</h3>
        <div class="completion-section">
          <el-result
            icon="success"
            title="合同签订成功"
            sub-title="合同已完成签订并归档，可以开始履行合同义务"
          >
            <template #extra>
              <el-button type="primary" @click="handleViewContract">查看合同</el-button>
              <el-button @click="handleDownloadContract">下载合同</el-button>
              <el-button @click="handlePrintContract">打印合同</el-button>
            </template>
          </el-result>
          
          <div class="contract-info">
            <h4>合同信息</h4>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="合同编号">{{ contractForm.contractNumber }}</el-descriptions-item>
              <el-descriptions-item label="签订状态">已签订</el-descriptions-item>
              <el-descriptions-item label="生效日期">{{ contractForm.effectiveDate }}</el-descriptions-item>
              <el-descriptions-item label="到期日期">{{ contractEndDate }}</el-descriptions-item>
              <el-descriptions-item label="甲方签章人">{{ signatures.partyA?.signerName }}</el-descriptions-item>
              <el-descriptions-item label="乙方签章人">{{ signatures.partyB?.signerName }}</el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 操作按钮 -->
    <div class="flow-actions">
      <el-button v-if="currentStep > 0" @click="handlePrevStep">上一步</el-button>
      <el-button v-if="currentStep < 4" type="primary" @click="handleNextStep">下一步</el-button>
      <el-button v-if="currentStep === 4" type="success" @click="handleComplete">完成</el-button>
      <el-button @click="handleCancel">取消</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Document,
  View,
  EditPen
} from '@element-plus/icons-vue'

// 响应式数据
const currentStep = ref(0)
const stepStatus = ref('')
const activeFormTab = ref('basic')
const contractFormRef = ref()
const selectedTemplate = ref(null)

// 合同模板数据
const contractTemplates = ref([
  {
    id: '1',
    name: 'HrHr事业编制聘用合同',
    type: '正式员工',
    version: '2.1',
    description: '适用于事业单位正式员工聘用'
  },
  {
    id: '2',
    name: '劳务协议合同',
    type: '临时员工',
    version: '1.8',
    description: '适用于临时工作人员聘用'
  },
  {
    id: '3',
    name: '劳务派遣合同',
    type: '派遣员工',
    version: '1.5',
    description: '适用于劳务派遣人员聘用'
  },
  {
    id: '4',
    name: '兼职协议合同',
    type: '兼职员工',
    version: '1.2',
    description: '适用于兼职工作人员聘用'
  }
])

// 部门数据
const departments = ref([
  { id: '1', name: '计算机学院' },
  { id: '2', name: '机械工程学院' },
  { id: '3', name: '经济管理学院' },
  { id: '4', name: '外语学院' },
  { id: '5', name: '人文学院' }
])

// 合同表单数据
const contractForm = reactive({
  contractNumber: 'HKY' + new Date().getFullYear() + String(Date.now()).slice(-6),
  contractType: '',
  signDate: new Date().toISOString().slice(0, 10),
  effectiveDate: new Date().toISOString().slice(0, 10),
  contractTerm: 3,
  probationPeriod: 3,
  employee: {
    name: '',
    idNumber: '',
    phone: '',
    email: '',
    department: '',
    position: ''
  },
  salary: {
    basicSalary: 0,
    positionAllowance: 0,
    performanceSalary: 0,
    otherAllowance: 0
  },
  terms: {
    workContent: '',
    workTime: '',
    benefits: '',
    otherTerms: ''
  }
})

// 表单验证规则
const contractRules = {
  signDate: [
    { required: true, message: '请选择签订日期', trigger: 'change' }
  ],
  effectiveDate: [
    { required: true, message: '请选择生效日期', trigger: 'change' }
  ],
  'employee.name': [
    { required: true, message: '请输入员工姓名', trigger: 'blur' }
  ],
  'employee.idNumber': [
    { required: true, message: '请输入身份证号', trigger: 'blur' },
    { pattern: /^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/, message: '身份证号格式不正确', trigger: 'blur' }
  ],
  'employee.phone': [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
  ],
  'employee.email': [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '邮箱格式不正确', trigger: 'blur' }
  ],
  'employee.department': [
    { required: true, message: '请选择所属部门', trigger: 'change' }
  ],
  'employee.position': [
    { required: true, message: '请输入职位', trigger: 'blur' }
  ]
}

// 审批表单数据
const approvalForm = reactive({
  result: 'approved',
  comment: ''
})

// 签章数据
const signatures = reactive({
  partyA: null,
  partyB: null
})

// 计算属性
const totalSalary = computed(() => {
  const {basicSalary: _basicSalary, positionAllowance: _positionAllowance, performanceSalary: _performanceSalary, otherAllowance: _otherAllowance} =  contractForm.salary
  return (basicSalary + positionAllowance + performanceSalary + otherAllowance).toFixed(2)
})

const contractEndDate 
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.flow-steps,
.step-content {
  margin-bottom: 20px;
}

.step-section {
  padding: 20px;
}

.step-section h3 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.template-selection {
  margin-top: 20px;
}

.template-card {
  cursor: pointer;
  transition: all 0.3s;
  height: 120px;
}

.template-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.template-card.selected {
  border-color: #409eff;
  background: #f0f7ff;
}

.template-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.template-icon {
  width: 50px;
  height: 50px;
  background: #409eff;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
  font-size: 20px;
}

.template-info {
  flex: 1;
}

.template-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.template-info p {
  margin: 0 0 8px 0;
  color: #606266;
  font-size: 14px;
}

.template-meta {
  display: flex;
  gap: 8px;
}

.template-type,
.template-version {
  padding: 2px 8px;
  background: #e4e7ed;
  border-radius: 4px;
  color: #606266;
  font-size: 12px;
}

.contract-preview {
  margin-top: 20px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.preview-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.contract-summary {
  margin-bottom: 30px;
}

.approval-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.signature-section {
  margin-top: 20px;
}

.signature-parties {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
}

.party-section {
  width: 48%;
}

.party-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.signature-box {
  width: 100%;
  height: 150px;
  border: 2px dashed #d4d7de;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.signature-box.signed {
  border-color: #67c23a;
  border-style: solid;
}

.signature-content {
  text-align: center;
}

.signature-content img {
  width: 100px;
  height: 60px;
  margin-bottom: 8px;
}

.signature-content p {
  margin: 4px 0;
  color: #606266;
  font-size: 12px;
}

.signature-placeholder {
  text-align: center;
}

.completion-section {
  margin-top: 20px;
}

.contract-info {
  margin-top: 30px;
}

.contract-info h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.flow-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 30px;
}
</style>