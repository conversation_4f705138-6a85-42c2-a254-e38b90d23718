<template>
  <div class="dashboard-test">
    <el-card>
      <template #header>
        <span>统计仪表板功能测试</span>
      </template>

      <el-space direction="vertical" size="large" style="width: 100%">
        <!-- API连接测试 -->
        <el-card>
          <template #header>
            <span>API连接测试</span>
          </template>

          <el-space>
            <el-button @click="testDashboardData" :loading="apiLoading.dashboard">
              仪表板数据
            </el-button>
            <el-button @click="testOverviewData" :loading="apiLoading.overview">概览数据</el-button>
            <el-button @click="testChartData" :loading="apiLoading.chart">图表数据</el-button>
            <el-button @click="testRealTimeData" :loading="apiLoading.realtime">实时数据</el-button>
            <el-button @click="testAlerts" :loading="apiLoading.alerts">预警数据</el-button>
          </el-space>

          <div v-if="testResults.length > 0" style="margin-top: 20px">
            <h4>测试结果：</h4>
            <el-timeline>
              <el-timeline-item
                v-for="(result, index) in testResults"
                :key="index"
                :timestamp="result.timestamp"
                :type="result.success ? 'success' : 'danger'"
              >
                <div>
                  <strong>{{ result.test }}</strong>
                  : {{ result.message }}
                  <pre v-if="result.data" style="margin-top: 8px; font-size: 12px">{{
                    JSON.stringify(result.data, null, 2)
                  }}</pre>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-card>

        <!-- 组件测试 -->
        <el-card>
          <template #header>
            <span>组件功能测试</span>
          </template>

          <el-space>
            <el-button @click="showStatisticsCard">测试统计卡片</el-button>
            <el-button @click="showSettingsDialog">测试设置对话框</el-button>
            <el-button @click="showAlertsDialog">测试预警对话框</el-button>
            <el-button @click="showDrillDownDialog">测试钻取对话框</el-button>
            <el-button @click="testChartRendering">测试图表渲染</el-button>
          </el-space>
        </el-card>

        <!-- 类型定义测试 -->
        <el-card>
          <template #header>
            <span>类型定义测试</span>
          </template>

          <el-descriptions :column="2" border>
            <el-descriptions-item label="时间范围枚举">
              {{ Object.values(TimeRangeOption).join(', ') }}
            </el-descriptions-item>
            <el-descriptions-item label="图表类型枚举">
              {{ Object.values(ChartType).join(', ') }}
            </el-descriptions-item>
            <el-descriptions-item label="数据类型枚举">
              {{ Object.values(DataType).join(', ') }}
            </el-descriptions-item>
            <el-descriptions-item label="统计维度枚举">
              {{ Object.values(StatisticsDimension).join(', ') }}
            </el-descriptions-item>
          </el-descriptions>

          <div style="margin-top: 20px">
            <h4>选项配置测试：</h4>
            <el-row :gutter="20">
              <el-col :span="6">
                <h5>时间范围选项</h5>
                <el-tag v-for="option in timeRangeOptions" :key="option.value" style="margin: 2px">
                  {{ option.label }}
                </el-tag>
              </el-col>
              <el-col :span="6">
                <h5>图表类型选项</h5>
                <el-tag v-for="option in chartTypeOptions" :key="option.value" style="margin: 2px">
                  {{ option.label }}
                </el-tag>
              </el-col>
              <el-col :span="6">
                <h5>数据类型选项</h5>
                <el-tag v-for="option in dataTypeOptions" :key="option.value" style="margin: 2px">
                  {{ option.label }}
                </el-tag>
              </el-col>
              <el-col :span="6">
                <h5>统计维度选项</h5>
                <el-tag
                  v-for="option in statisticsDimensionOptions"
                  :key="option.value"
                  style="margin: 2px"
                >
                  {{ option.label }}
                </el-tag>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 模拟数据展示 -->
        <el-card>
          <template #header>
            <span>模拟数据展示</span>
          </template>

          <el-row :gutter="20">
            <el-col :span="6">
              <StatisticsCard :card="mockCards[0]" @click="handleCardClick" />
            </el-col>
            <el-col :span="6">
              <StatisticsCard :card="mockCards[1]" @click="handleCardClick" />
            </el-col>
            <el-col :span="6">
              <StatisticsCard :card="mockCards[2]" @click="handleCardClick" />
            </el-col>
            <el-col :span="6">
              <StatisticsCard :card="mockCards[3]" @click="handleCardClick" />
            </el-col>
          </el-row>
        </el-card>

        <!-- 图表测试区域 -->
        <el-card>
          <template #header>
            <span>图表渲染测试</span>
          </template>

          <el-row :gutter="20">
            <el-col :span="12">
              <div class="chart-test">
                <h4>折线图测试</h4>
                <div ref="lineChartRef" class="test-chart"></div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="chart-test">
                <h4>饼图测试</h4>
                <div ref="pieChartRef" class="test-chart"></div>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 功能状态检查 -->
        <el-card>
          <template #header>
            <span>功能状态检查</span>
          </template>

          <el-table :data="featureStatus" border>
            <el-table-column prop="feature" label="功能模块" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag
                  :type="
                    row.status === '完成' ? 'success' : row.status === '开发中' ? 'warning' : 'info'
                  "
                >
                  {{ row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="说明" />
          </el-table>
        </el-card>

        <!-- 性能测试 -->
        <el-card>
          <template #header>
            <span>性能测试</span>
          </template>

          <el-space>
            <el-button @click="testLoadingPerformance" :loading="performanceLoading">
              加载性能测试
            </el-button>
            <el-button @click="testChartPerformance" :loading="chartPerformanceLoading">
              图表渲染性能
            </el-button>
            <el-button @click="testMemoryUsage">内存使用测试</el-button>
          </el-space>

          <div v-if="performanceResults.length > 0" style="margin-top: 20px">
            <h4>性能测试结果：</h4>
            <el-table :data="performanceResults" border size="small">
              <el-table-column prop="test" label="测试项目" />
              <el-table-column prop="duration" label="耗时(ms)" width="100" />
              <el-table-column prop="result" label="结果" width="100">
                <template #default="{ row }">
                  <el-tag :type="row.result === '通过' ? 'success' : 'warning'">
                    {{ row.result }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="note" label="备注" />
            </el-table>
          </div>
        </el-card>
      </el-space>
    </el-card>

    <!-- 测试对话框 -->
    <DashboardSettingsDialog v-model="settingsDialogVisible" @save="handleSettingsSave" />

    <AlertsDialog
      v-model="alertsDialogVisible"
      :alerts="mockAlerts"
      @acknowledge="handleAlertAcknowledge"
    />

    <ChartDrillDownDialog
      v-model="drillDownDialogVisible"
      chart-id="changes-trend"
      :params="mockQueryParams"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import { dashboardApi } from '@/api/dashboard'
import type {
  StatisticsCard as StatisticsCardType,
  AlertData,
  DashboardQueryParams
} from '@/types/dashboard'
import {
  TimeRangeOption,
  ChartType,
  DataType,
  StatisticsDimension,
  timeRangeOptions,
  chartTypeOptions,
  dataTypeOptions,
  statisticsDimensionOptions
} from '@/types/dashboard'
import HrStatisticsCard from '@/components/dashboard/HrStatisticsCard.vue'
import HrDashboardSettingsDialog from '@/components/dashboard/HrDashboardSettingsDialog.vue'
import HrAlertsDialog from '@/components/dashboard/HrAlertsDialog.vue'
import HrChartDrillDownDialog from '@/components/dashboard/HrChartDrillDownDialog.vue'

// 响应式数据
const testResults = ref<any[]>([])
const performanceResults = ref<any[]>([])
const performanceLoading = ref(false)
const chartPerformanceLoading = ref(false)

const apiLoading = reactive({
  dashboard: false,
  overview: false,
  chart: false,
  realtime: false,
  alerts: false
})

const settingsDialogVisible = ref(false)
const alertsDialogVisible = ref(false)
const drillDownDialogVisible = ref(false)

// 图表引用
const lineChartRef = ref<HTMLDivElement>()
const pieChartRef = ref<HTMLDivElement>()

// 图表实例
let lineChart: echarts.ECharts | null = null
let pieChart: echarts.ECharts | null = null

// 模拟数据
const mockCards: StatisticsCardType[] = [
  {
    id: 'total-institutions',
    title: '总机构数',
    value: 156,
    icon: 'office-building',
    color: '#409eff',
    trend: 5.2,
    trendLabel: '较上月',
    clickable: true
  },
  {
    id: 'active-changes',
    title: '活跃变更',
    value: 23,
    icon: 'edit',
    color: '#67c23a',
    trend: -2.1,
    trendLabel: '较上周',
    clickable: true
  },
  {
    id: 'pending-approvals',
    title: '待审批',
    value: 8,
    icon: 'clock',
    color: '#e6a23c',
    trend: 12.5,
    trendLabel: '较昨日',
    clickable: true
  },
  {
    id: 'expiring-institutions',
    title: '即将到期',
    value: 5,
    icon: 'warning',
    color: '#f56c6c',
    trend: -8.3,
    trendLabel: '较上周',
    clickable: true
  }
]

const mockAlerts: AlertData[] = [
  {
    id: '1',
    type: 'warning',
    title: '临时机构即将到期',
    message: '数字化转型项目组将于3天后到期，请及时处理',
    severity: 'medium',
    source: '临时机构管理',
    createTime: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
    acknowledged: false,
    actionRequired: true
  },
  {
    id: '2',
    type: 'error',
    title: '系统异常',
    message: '数据同步服务出现异常，请检查网络连接',
    severity: 'high',
    source: '系统监控',
    createTime: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
    acknowledged: false,
    actionRequired: true
  }
]

const mockQueryParams: DashboardQueryParams = {
  timeRange: TimeRangeOption.LAST_30_DAYS,
  dataTypes: [DataType.INSTITUTION_CHANGE, DataType.AD_HOC_INSTITUTION],
  departments: [],
  includeSubDepartments: true
}

// 功能状态
const featureStatus = ref([
  { feature: 'TypeScript类型定义', status: '完成', description: '完整的枚举和接口定义' },
  { feature: 'API接口层', status: '完成', description: '统合统计API和现有模块API' },
  { feature: '统计仪表板主页面', status: '完成', description: '概览卡片、图表展示、筛选控制' },
  { feature: '统计卡片组件', status: '完成', description: '可点击的统计卡片，支持趋势显示' },
  { feature: '仪表板设置对话框', status: '完成', description: '多标签页设置，支持个性化配置' },
  { feature: '预警对话框组件', status: '完成', description: '预警列表、批量操作、详情查看' },
  { feature: '图表钻取对话框', status: '完成', description: '交互式钻取、多维度分析' },
  { feature: '路由和导航', status: '完成', description: '完整的页面路由配置' },
  { feature: '功能测试页面', status: '完成', description: '组件和API功能验证' },
  { feature: '实时数据更新', status: '完成', description: 'WebSocket实时数据推送' }
])

// API测试函数
const testDashboardData = async () => {
  apiLoading.dashboard = true
  try {
    const result = await dashboardApi.getDashboardData(mockQueryParams)
    addTestResult('仪表板数据', true, '获取成功', result)
  } catch (__error) {
    addTestResult('仪表板数据', false, '获取失败', error)
  } finally {
    apiLoading.dashboard = false
  }
}

const testOverviewData = async () => {
  apiLoading.overview = true
  try {
    const result = await dashboardApi.getOverviewData(mockQueryParams)
    addTestResult('概览数据', true, '获取成功', result)
  } catch (__error) {
    addTestResult('概览数据', false, '获取失败', error)
  } finally {
    apiLoading.overview = false
  }
}

const testChartData = async () => {
  apiLoading.chart = true
  try {
    const result = await dashboardApi.getChangesTrendChart(mockQueryParams)
    addTestResult('图表数据', true, '获取成功', result)
  } catch (__error) {
    addTestResult('图表数据', false, '获取失败', error)
  } finally {
    apiLoading.chart = false
  }
}

const testRealTimeData = async () => {
  apiLoading.realtime = true
  try {
    const result = await dashboardApi.getRealTimeData()
    addTestResult('实时数据', true, '获取成功', result)
  } catch (__error) {
    addTestResult('实时数据', false, '获取失败', error)
  } finally {
    apiLoading.realtime = false
  }
}

const testAlerts = async () => {
  apiLoading.alerts = true
  try {
    const result = await dashboardApi.getAlerts()
    addTestResult('预警数据', true, '获取成功', result)
  } catch (__error) {
    addTestResult('预警数据', false, '获取失败', error)
  } finally {
    apiLoading.alerts = false
  }
}

// 添加测试结果

const addTestResult = (test: string, success: boolean, message: string, data?: unknown) => {
  testResults.value.unshift({
    test,
    success,
    message,
    data,
    timestamp: new Date().toLocaleString()
  })

  if (success) {
    ElMessage.success(`${test}: ${message}`)
  } else {
    ElMessage.error(`${test}: ${message}`)
  }
}

// 组件测试函数
const showStatisticsCard = () => {
  ElMessage.info('统计卡片组件已在模拟数据展示区域显示')
}

const showSettingsDialog = () => {
  settingsDialogVisible.value = true
}

const showAlertsDialog = () => {
  alertsDialogVisible.value = true
}

const showDrillDownDialog = () => {
  drillDownDialogVisible.value = true
}

const testChartRendering = () => {
  initTestCharts()
  ElMessage.success('图表渲染测试完成，请查看图表测试区域')
}

// 性能测试函数
const testLoadingPerformance = async () => {
  performanceLoading.value = true

  const startTime = performance.now()

  try {
    // 模拟数据加载
    await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 500))

    const endTime = performance.now()
    const duration = Math.round(endTime - startTime)

    performanceResults.value.push({
      test: '数据加载性能',
      duration,
      result: duration < 3000 ? '通过' : '需优化',
      note: duration < 3000 ? '加载时间符合要求' : '加载时间超过3秒'
    })

    ElMessage.success('加载性能测试完成')
  } catch (__error) {
    ElMessage.error('加载性能测试失败')
  } finally {
    performanceLoading.value = false
  }
}

const testChartPerformance = async () => {
  chartPerformanceLoading.value = true

  const startTime = performance.now()

  try {
    // 重新渲染图表
    initTestCharts()

    const endTime = performance.now()
    const duration = Math.round(endTime - startTime)

    performanceResults.value.push({
      test: '图表渲染性能',
      duration,
      result: duration < 1000 ? '通过' : '需优化',
      note: duration < 1000 ? '渲染时间符合要求' : '渲染时间超过1秒'
    })

    ElMessage.success('图表渲染性能测试完成')
  } catch (__error) {
    ElMessage.error('图表渲染性能测试失败')
  } finally {
    chartPerformanceLoading.value = false
  }
}

const testMemoryUsage = () => {
  if ('memory' in performance) {
    const memory = (performance as unknown).memory
    const usedMemory = Math.round(memory.usedJSHeapSize / 1024 / 1024)

    performanceResults.value.push({
      test: '内存使用情况',
      duration: usedMemory,
      result: usedMemory < 100 ? '通过' : '需优化',
      note: `当前使用 ${usedMemory}MB 内存`
    })

    ElMessage.success('内存使用测试完成')
  } else {
    ElMessage.warning('当前浏览器不支持内存监控')
  }
}

// 初始化测试图表
const initTestCharts = () => {
  // 折线图
  if (lineChartRef.value) {
    if (lineChart) {
      lineChart.dispose()
    }

    lineChart = echarts.init(lineChartRef.value)

    const lineOption = {
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: 'HrHr变更数量',
          type: 'line',
          data: [12, 19, 15, 27, 22, 18],
          smooth: true,
          itemStyle: {
            color: '#409eff'
          }
        }
      ]
    }

    lineChart.setOption(lineOption)
  }

  // 饼图
  if (pieChartRef.value) {
    if (pieChart) {
      pieChart.dispose()
    }

    pieChart = echarts.init(pieChartRef.value)

    const pieOption = {
      tooltip: {
        trigger: 'item'
      },
      series: [
        {
          name: '机构类型',
          type: 'pie',
          radius: '50%',
          data: [
            { value: 35, name: '项目组' },
            { value: 25, name: '工作组' },
            { value: 20, name: '委员会' },
            { value: 15, name: '专项小组' },
            { value: 5, name: '其他' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }

    pieChart.setOption(pieOption)
  }
}

// 事件处理
const handleCardClick = (cardId: string) => {
  ElMessage.info(`点击了卡片: ${cardId}`)
}

const handleSettingsSave = (settings: unknown) => {
  ElMessage.success('设置保存成功')
  console.log('Settings saved:', settings)
}

const handleAlertAcknowledge = (alertId: string) => {
  ElMessage.success(`预警 ${alertId} 已确认`)
}

// 组件挂载
onMounted(() => {
  // 延迟初始化图表，确保DOM已渲染
  setTimeout(() => {
    initTestCharts()
  }, 100)
})

// 组件卸载
onUnmounted(() => {
  if (lineChart) {
    lineChart.dispose()
  }
  if (pieChart) {
    pieChart.dispose()
  }
})
</script>

<style scoped>
.dashboard-test {
  padding: 20px;
}

.test-chart {
  height: 300px;
  width: 100%;
}

.chart-test h4 {
  margin-bottom: 16px;
  color: #303133;
}

pre {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  max-height: 200px;
}
</style>
