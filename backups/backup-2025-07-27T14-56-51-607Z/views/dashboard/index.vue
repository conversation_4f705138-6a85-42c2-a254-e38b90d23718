<template>
  <div class="dashboard-container">
    <div class="page-header">
      <h1>数据概览</h1>
      <p>欢迎使用杭科院人事综合管理系统</p>
    </div>
    
    <el-row :gutter="20" class="statistics-row">
      <el-col :span="6">
        <el-card>
          <div class="statistic-item">
            <div class="statistic-value">1,234</div>
            <div class="statistic-label">员工总数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="statistic-item">
            <div class="statistic-value">56</div>
            <div class="statistic-label">本月新增</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="statistic-item">
            <div class="statistic-value">89%</div>
            <div class="statistic-label">出勤率</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="statistic-item">
            <div class="statistic-value">23</div>
            <div class="statistic-label">待处理事项</div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <el-card>
          <template #header>
            <h3>部门人员分布</h3>
          </template>
          <div class="chart-placeholder">
            <el-empty description="图表开发中"  />
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <h3>月度考勤趋势</h3>
          </template>
          <div class="chart-placeholder">
            <el-empty description="图表开发中"  />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
 
defineOptions({
  name: 'DashboardPage'
})

// 未来可以在这里添加数据获取和图表渲染逻辑
</script>

<style scoped>
.dashboard-container {
  width: 100%;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  font-size: 24px;
  font-weight: 500;
  margin: 0 0 8px 0;
}

.page-header p {
  color: #909399;
  margin: 0;
}

.statistics-row {
  margin-bottom: 20px;
}

.statistic-item {
  text-align: center;
  padding: 20px 0;
}

.statistic-value {
  font-size: 32px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.statistic-label {
  font-size: 14px;
  color: #909399;
}

.charts-row {
  margin-bottom: 20px;
}

.chart-placeholder {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>