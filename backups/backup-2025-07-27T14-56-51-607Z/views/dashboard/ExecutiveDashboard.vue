<template>
  <div class="executive-dashboard">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon><DataBoard /></el-icon>
        领导驾驶舱
      </h2>
      <div class="header-actions">
        <el-button @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button @click="handleFullscreen">
          <el-icon><FullScreen /></el-icon>
          全屏显示
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出报告
        </el-button>
      </div>
    </div>

    <!-- 核心指标卡片 -->
    <el-row :gutter="20" class="core-metrics">
      <el-col :span="6">
        <el-card class="metric-card total-employees">
          <div class="metric-content">
            <div class="metric-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ metrics.totalEmployees }}</div>
              <div class="metric-label">在职员工总数</div>
              <div class="metric-trend">
                <span class="trend-up">+{{ metrics.employeeGrowth }}%</span>
                <span class="trend-label">较上月</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="metric-card recruitment">
          <div class="metric-content">
            <div class="metric-icon">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ metrics.newHires }}</div>
              <div class="metric-label">本月新入职</div>
              <div class="metric-trend">
                <span class="trend-up">+{{ metrics.hiringGrowth }}%</span>
                <span class="trend-label">较上月</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="metric-card turnover">
          <div class="metric-content">
            <div class="metric-icon">
              <el-icon><SwitchButton /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ metrics.turnoverRate }}%</div>
              <div class="metric-label">离职率</div>
              <div class="metric-trend">
                <span class="trend-down">-{{ metrics.turnoverChange }}%</span>
                <span class="trend-label">较上月</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="metric-card satisfaction">
          <div class="metric-content">
            <div class="metric-icon">
              <el-icon><Star /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ metrics.satisfaction }}</div>
              <div class="metric-label">员工满意度</div>
              <div class="metric-trend">
                <span class="trend-up">+{{ metrics.satisfactionChange }}</span>
                <span class="trend-label">较上月</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-section">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>人员结构分析</span>
              <el-select v-model="structureTimeRange" size="small" style="width: 120px">
                <el-option label="本月" value="month"  />
                <el-option label="本季度" value="quarter"  />
                <el-option label="本年度" value="year"  />
              </el-select>
            </div>
          </template>
          <div ref="structureChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>招聘趋势分析</span>
              <el-select v-model="recruitmentTimeRange" size="small" style="width: 120px">
                <el-option label="近6个月" value="6months"  />
                <el-option label="近12个月" value="12months"  />
                <el-option label="近2年" value="2years"  />
              </el-select>
            </div>
          </template>
          <div ref="recruitmentChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-section">
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <span>部门人员分布</span>
          </template>
          <div ref="departmentChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <span>薪酬成本分析</span>
          </template>
          <div ref="salaryChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <span>绩效评估分布</span>
          </template>
          <div ref="performanceChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 实时数据表格 -->
    <el-row :gutter="20" class="data-tables">
      <el-col :span="12">
        <el-card class="table-card">
          <template #header>
            <div class="table-header">
              <span>待办事项</span>
              <el-badge :value="pendingTasks.length" class="badge">
                <el-button type="text" @click="handleViewAllTasks">查看全部</el-button>
              </el-badge>
            </div>
          </template>
          <el-table :data="pendingTasks" style="width: 100%" max-height="300">
            <el-table-column prop="type" label="类型" width="100">
              <template #default="{ row }">
                <el-tag :type="getTaskTypeColor(row.type)" size="small">
                  {{ getTaskTypeLabel(row.type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="title" label="事项" min-width="200"  />
            <el-table-column prop="priority" label="优先级" width="80">
              <template #default="{ row }">
                <el-tag :type="getPriorityColor(row.priority)" size="small">
                  {{ row.priority }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="deadline" label="截止时间" width="120"  />
            <el-table-column label="操作" width="80">
              <template #default="{ row }">
                <el-button type="text" size="small" @click="handleProcessTask(row)">
                  处理
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="table-card">
          <template #header>
            <div class="table-header">
              <span>关键指标预警</span>
              <el-badge :value="alerts.length" class="badge" type="danger">
                <el-button type="text" @click="handleViewAllAlerts">查看全部</el-button>
              </el-badge>
            </div>
          </template>
          <el-table :data="alerts" style="width: 100%" max-height="300">
            <el-table-column prop="level" label="级别" width="80">
              <template #default="{ row }">
                <el-tag :type="getAlertLevelColor(row.level)" size="small">
                  {{ row.level }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="metric" label="指标" width="120"  />
            <el-table-column prop="description" label="描述" min-width="200"  />
            <el-table-column prop="value" label="当前值" width="100"  />
            <el-table-column label="操作" width="80">
              <template #default="{ row }">
                <el-button type="text" size="small" @click="handleViewAlert(row)">
                  查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作区域 -->
    <el-card class="quick-actions">
      <template #header>
        <span>快速操作</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="4">
          <div class="action-item" @click="handleQuickAction('recruitment')">
            <el-icon><UserFilled /></el-icon>
            <span>招聘管理</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="action-item" @click="handleQuickAction('performance')">
            <el-icon><TrendCharts /></el-icon>
            <span>绩效考核</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="action-item" @click="handleQuickAction('salary')">
            <el-icon><Money /></el-icon>
            <span>薪酬管理</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="action-item" @click="handleQuickAction('training')">
            <el-icon><Reading /></el-icon>
            <span>培训发展</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="action-item" @click="handleQuickAction('attendance')">
            <el-icon><Clock /></el-icon>
            <span>考勤管理</span>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="action-item" @click="handleQuickAction('reports')">
            <el-icon><Document /></el-icon>
            <span>报表中心</span>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import {
  DataBoard,
  Refresh,
  FullScreen,
  Download,
  User,
  UserFilled,
  SwitchButton,
  Star,
  TrendCharts,
  Money,
  Reading,
  Clock,
  Document
} from '@element-plus/icons-vue'

// 响应式数据
const structureTimeRange = ref('month')
const recruitmentTimeRange = ref('6months')

// 图表引用
const structureChartRef = ref<HTMLDivElement>()
const recruitmentChartRef = ref<HTMLDivElement>()
const departmentChartRef = ref<HTMLDivElement>()
const salaryChartRef = ref<HTMLDivElement>()
const performanceChartRef = ref<HTMLDivElement>()

// 图表实例
let structureChart: echarts.ECharts | null = null
let recruitmentChart: echarts.ECharts | null = null
let departmentChart: echarts.ECharts | null = null
let salaryChart: echarts.ECharts | null = null
let performanceChart: echarts.ECharts | null = null

// 核心指标数据
const metrics = reactive({
  totalEmployees: 1256,
  employeeGrowth: 3.2,
  newHires: 45,
  hiringGrowth: 12.5,
  turnoverRate: 2.8,
  turnoverChange: 0.5,
  satisfaction: 4.6,
  satisfactionChange: 0.2
})

// 待办事项数据
const pendingTasks = ref([
  {
    type: 'approval',
    title: '张三的职称申报审批',
    priority: '高',
    deadline: '2024-06-25'
  },
  {
    type: 'recruitment',
    title: '计算机学院教师招聘',
    priority: '中',
    deadline: '2024-06-30'
  },
  {
    type: 'performance',
    title: '2024年度绩效考核启动',
    priority: '高',
    deadline: '2024-07-01'
  }
])

// 预警数据
const alerts = ref([
  {
    level: '高',
    metric: '离职率',
    description: '本月离职率超过预警线',
    value: '3.2%'
  },
  {
    level: '中',
    metric: '招聘进度',
    description: '计算机学院招聘进度滞后',
    value: '60%'
  }
])

// 方法
const handleRefresh = () => {
  // 刷新核心指标数据（模拟数据更新）
  metrics.totalEmployees = Math.floor(1200 + Math.random() * 100)
  metrics.employeeGrowth = Number((Math.random() * 5).toFixed(1))
  metrics.newHires = Math.floor(40 + Math.random() * 20)
  metrics.turnoverRate = Number((2 + Math.random() * 2).toFixed(1))
  metrics.averageSalary = Math.floor(12000 + Math.random() * 3000)
  metrics.salaryGrowth = Number((Math.random() * 10).toFixed(1))
  metrics.recruitmentRate = Number((80 + Math.random() * 15).toFixed(1))
  metrics.recruitmentCycle = Math.floor(25 + Math.random() * 10)
  
  // 销毁现有图表
  destroyCharts()
  
  // 重新初始化图表
  setTimeout(() => {
    initCharts()
    ElMessage.success('数据已刷新')
  }, 100)
}

const handleFullscreen = () => {
  if (document.fullscreenElement) {
    document.exitFullscreen()
  } else {
    document.documentElement.requestFullscreen()
  }
}

const handleExport = () => {
  ElMessage.info('导出报告功能开发中')
}

const handleViewAllTasks = () => {
  ElMessage.info('跳转到任务管理页面')
}

const handleViewAllAlerts = () => {
  ElMessage.info('跳转到预警管理页面')
}

   
const handleProcessTask = (task: unknown) => {
  ElMessage.info(`处理任务：${task.title}`)
}

   
const handleViewAlert = (alert: unknown) => {
  ElMessage.info(`查看预警：${alert.description}`)
}

const handleQuickAction = (action: string) => {
  ElMessage.info(`快速操作：${action}`)
}

const getTaskTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    approval: 'warning',
    recruitment: 'primary',
    performance: 'success'
  }
  return colors[type] || ''
}

const getTaskTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    approval: '审批',
    recruitment: '招聘',
    performance: '绩效'
  }
  return labels[type] || type
}

const getPriorityColor = (priority: string) => {
  const colors: Record<string, string> = {
    高: 'danger',
    中: 'warning',
    低: 'info'
  }
  return colors[priority] || ''
}

const getAlertLevelColor = (level: string) => {
  const colors: Record<string, string> = {
    高: 'danger',
    中: 'warning',
    低: 'info'
  }
  return colors[level] || ''
}

// 初始化图表
const initCharts = () => {
  // 人员结构图表
  if (structureChartRef.value) {
    structureChart = echarts.init(structureChartRef.value)
    structureChart.setOption({
      tooltip: { trigger: 'item' },
      series: [{
        type: 'pie',
        radius: ['40%', '70%'],
        data: [
          { value: 456, name: 'HrHr教学人员' },
          { value: 234, name: '管理人员' },
          { value: 345, name: '技术人员' },
          { value: 221, name: '工勤人员' }
        ]
      }]
    })
  }

  // 招聘趋势图表
  if (recruitmentChartRef.value) {
    recruitmentChart = echarts.init(recruitmentChartRef.value)
    recruitmentChart.setOption({
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月']
      },
      yAxis: { type: 'value' },
      series: [{
        type: 'line',
        data: [32, 28, 45, 38, 52, 45],
        smooth: true
      }]
    })
  }

  // 部门分布图表
  if (departmentChartRef.value) {
    departmentChart = echarts.init(departmentChartRef.value)
    departmentChart.setOption({
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: ['计算机', '机械', '经管', '外语', '艺术']
      },
      yAxis: { type: 'value' },
      series: [{
        type: 'bar',
        data: [234, 189, 156, 123, 98]
      }]
    })
  }

  // 薪酬成本图表
  if (salaryChartRef.value) {
    salaryChart = echarts.init(salaryChartRef.value)
    salaryChart.setOption({
      tooltip: { trigger: 'item' },
      series: [{
        type: 'pie',
        radius: '60%',
        data: [
          { value: 45, name: '基本工资' },
          { value: 25, name: '绩效奖金' },
          { value: 20, name: '津贴补贴' },
          { value: 10, name: '其他' }
        ]
      }]
    })
  }

  // 绩效分布图表
  if (performanceChartRef.value) {
    performanceChart = echarts.init(performanceChartRef.value)
    performanceChart.setOption({
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: ['优秀', '良好', '合格', '待改进']
      },
      yAxis: { type: 'value' },
      series: [{
        type: 'bar',
        data: [156, 234, 189, 23],
        itemStyle: {
   
          color: function(params: unknown) {
            const colors = ['#67C23A', '#409EFF', '#E6A23C', '#F56C6C']
            return colors[params.dataIndex]
          }
        }
      }]
    })
  }
}

// 销毁图表
const destroyCharts = () => {
  structureChart?.dispose()
  recruitmentChart?.dispose()
  departmentChart?.dispose()
  salaryChart?.dispose()
  performanceChart?.dispose()
}

onMounted(() => {
  initCharts()
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    structureChart?.resize()
    recruitmentChart?.resize()
    departmentChart?.resize()
    salaryChart?.resize()
    performanceChart?.resize()
  })
})

onUnmounted(() => {
  destroyCharts()
  window.removeEventListener('resize', () => {})
})
</script>

<style scoped>
.executive-dashboard {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.core-metrics {
  margin-bottom: 20px;
}

.metric-card {
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.metric-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.metric-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  color: white;
}

.total-employees .metric-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.recruitment .metric-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.turnover .metric-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.satisfaction .metric-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.metric-info {
  flex: 1;
}

.metric-value {
  font-size: 32px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 4px;
}

.trend-up {
  color: #67c23a;
  font-weight: 600;
}

.trend-down {
  color: #f56c6c;
  font-weight: 600;
}

.trend-label {
  font-size: 12px;
  color: #909399;
}

.charts-section {
  margin-bottom: 20px;
}

.chart-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 300px;
}

.data-tables {
  margin-bottom: 20px;
}

.table-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.badge {
  margin-left: 8px;
}

.quick-actions {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px;
  border-radius: 8px;
  background: #f8f9fa;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-item:hover {
  background: #e9ecef;
  transform: translateY(-2px);
}

.action-item .el-icon {
  font-size: 32px;
  color: #409eff;
}

.action-item span {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}
</style>
