<template>
  <el-card class="realtime-metrics" shadow="hover">
    <template #header>
      <div class="card-header">
        <h3 class="card-title">
          <el-icon><DataLine /></el-icon>
          实时性能指标
        </h3>
        <el-tag :type="performanceLevel.type" size="small">
          {{ performanceLevel.text }}
        </el-tag>
      </div>
    </template>

    <div class="metrics-container">
      <!-- 请求速率 -->
      <div class="metric-card">
        <div class="metric-header">
          <span class="metric-name">请求速率</span>
          <el-tooltip content="每分钟API请求数量" placement="top">
            <el-icon class="info-icon"><InfoFilled /></el-icon>
          </el-tooltip>
        </div>
        <div class="metric-body">
          <div class="metric-main">
            <AnimatedNumber 
              :value="data.requestsPerMinute" 
              format="0,0"
              class="metric-value"
            />
            <span class="metric-unit">req/min</span>
          </div>
          <div class="metric-chart">
            <MiniLineChart 
              :data="requestTrend"
              :height="40"
              color="#409eff"
            />
          </div>
        </div>
        <div class="metric-footer">
          <span class="metric-trend" :class="requestTrendClass">
            <el-icon>
              <CaretTop v-if="requestChange > 0" />
              <CaretBottom v-else />
            </el-icon>
            {{ Math.abs(requestChange) }}%
          </span>
          <span class="metric-baseline">较5分钟前</span>
        </div>
      </div>

      <!-- 响应时间 -->
      <div class="metric-card">
        <div class="metric-header">
          <span class="metric-name">平均响应时间</span>
          <el-tooltip content="API平均响应时间" placement="top">
            <el-icon class="info-icon"><InfoFilled /></el-icon>
          </el-tooltip>
        </div>
        <div class="metric-body">
          <div class="metric-main">
            <AnimatedNumber 
              :value="data.avgResponseTime" 
              format="0,0"
              class="metric-value"
            />
            <span class="metric-unit">ms</span>
          </div>
          <div class="metric-chart">
            <MiniLineChart 
              :data="responseTrend"
              :height="40"
              :color="responseTimeColor"
            />
          </div>
        </div>
        <div class="metric-footer">
          <el-progress 
            :percentage="responseTimePercent" 
            :color="responseTimeColor"
            :show-text="false"
            :stroke-width="4"
           />
          <span class="metric-baseline">目标: &lt;500ms</span>
        </div>
      </div>

      <!-- 活跃连接 -->
      <div class="metric-card">
        <div class="metric-header">
          <span class="metric-name">活跃连接数</span>
          <el-tooltip content="当前WebSocket连接数" placement="top">
            <el-icon class="info-icon"><InfoFilled /></el-icon>
          </el-tooltip>
        </div>
        <div class="metric-body">
          <div class="metric-main">
            <AnimatedNumber 
              :value="data.activeConnections" 
              format="0,0"
              class="metric-value"
            />
            <span class="metric-unit">连接</span>
          </div>
          <div class="metric-distribution">
            <div 
              v-for="(dist, index) in connectionDistribution"
              :key="index"
              class="dist-item"
            >
              <span class="dist-label">{{ dist.label }}</span>
              <el-progress 
                :percentage="dist.percentage" 
                :color="dist.color"
                :stroke-width="10"
               />
            </div>
          </div>
        </div>
        <div class="metric-footer">
          <span class="metric-status">
            <el-icon class="status-icon"><Connection /></el-icon>
            连接稳定
          </span>
          <span class="metric-baseline">容量: {{ maxConnections }}</span>
        </div>
      </div>

      <!-- 错误率 -->
      <div class="metric-card">
        <div class="metric-header">
          <span class="metric-name">错误率</span>
          <el-tooltip content="5xx错误占比" placement="top">
            <el-icon class="info-icon"><InfoFilled /></el-icon>
          </el-tooltip>
        </div>
        <div class="metric-body">
          <div class="metric-main">
            <AnimatedNumber 
              :value="data.errorRate" 
              format="0.00"
              class="metric-value"
              :class="errorRateClass"
            />
            <span class="metric-unit">%</span>
          </div>
          <div class="error-breakdown">
            <div 
              v-for="error in errorBreakdown"
              :key="error.code"
              class="error-item"
            >
              <span class="error-code">{{ error.code }}</span>
              <span class="error-count">{{ error.count }}</span>
            </div>
          </div>
        </div>
        <div class="metric-footer">
          <el-tag 
            :type="errorLevel.type" 
            size="small"
            effect="plain"
          >
            {{ errorLevel.text }}
          </el-tag>
          <span class="metric-baseline">阈值: &lt;1%</span>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="metric-actions">
      <el-button 
        size="small" 
        text
        @click="$emit('view-details')"
      >
        查看详细报告
      </el-button>
      <el-button 
        size="small" 
        text
        @click="$emit('export-metrics')"
      >
        导出指标数据
      </el-button>
    </div>
  </el-card>
</template>

<script setup lang="ts">

defineOptions({
  name: 'RealTimeMetrics'
})
 
import { 
  DataLine, 
  InfoFilled, 
  CaretTop, 
  CaretBottom,
  Connection
} from '@element-plus/icons-vue'
import HrAnimatedNumber from '@/components/common/HrAnimatedNumber.vue'
import MiniLineChart from './MiniLineChart.vue'

interface Props {
  data: {
    requestsPerMinute: number
    avgResponseTime: number
    activeConnections: number
    errorRate: number
  }
}

interface Emits {
  (e: 'view-details'): void
  (e: 'export-metrics'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 性能等级评估
const performanceLevel = computed(() => {
  const score = calculatePerformanceScore()
  if (score >= 90) return { type: 'success', text: '优秀' }
  if (score >= 70) return { type: 'warning', text: '良好' }
  return { type: 'danger', text: '需优化' }
})

// 计算性能分数
const calculatePerformanceScore = () => {
  let score = 100
  
  // 响应时间扣分
  if (props.data.avgResponseTime > 1000) score -= 30
  else if (props.data.avgResponseTime > 500) score -= 15
  
  // 错误率扣分
  if (props.data.errorRate > 5) score -= 40
  else if (props.data.errorRate > 1) score -= 20
  
  return Math.max(0, score)
}

// 请求趋势数据（模拟）
const requestTrend = ref([
  120, 135, 128, 142, 156, 148, 165, 158, 172, 
  props.data.requestsPerMinute
])

// 响应时间趋势（模拟）
const responseTrend = ref([
  280, 320, 295, 310, 340, 325, 315, 330, 345,
  props.data.avgResponseTime
])

// 请求变化率
const requestChange = computed(() => {
  const prev = requestTrend.value[requestTrend.value.length - 2]
  const current = props.data.requestsPerMinute
  return Math.round(((current - prev) / prev) * 100)
})

const requestTrendClass = computed(() => 
  requestChange.value > 0 ? 'trend-up' : 'trend-down'
)

// 响应时间相关
const responseTimePercent = computed(() => {
  const target = 500
  return Math.min(100, (props.data.avgResponseTime / target) * 100)
})

const responseTimeColor = computed(() => {
  if (props.data.avgResponseTime <= 200) return '#67c23a'
  if (props.data.avgResponseTime <= 500) return '#409eff'
  if (props.data.avgResponseTime <= 1000) return '#e6a23c'
  return '#f56c6c'
})

// 连接分布
const maxConnections = 10000
const connectionDistribution = computed(() => [
  {
    label: 'WebSocket',
    percentage: 65,
    color: '#409eff'
  },
  {
    label: 'HTTP',
    percentage: 35,
    color: '#67c23a'
  }
])

// 错误率相关
const errorRateClass = computed(() => {
  if (props.data.errorRate < 0.5) return 'text-success'
  if (props.data.errorRate < 1) return 'text-warning'
  return 'text-danger'
})

const errorLevel = computed(() => {
  if (props.data.errorRate < 0.5) return { type: 'success', text: '正常' }
  if (props.data.errorRate < 1) return { type: 'warning', text: '警告' }
  return { type: 'danger', text: '异常' }
})

// 错误分解（模拟）
const errorBreakdown = ref([
  { code: '500', count: 12 },
  { code: '502', count: 5 },
  { code: '503', count: 3 },
  { code: '504', count: 1 }
])
</script>

<style scoped>
.realtime-metrics {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.metrics-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

/* 指标卡片 */
.metric-card {
  padding: 16px;
  background: #f5f7fa;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-name {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.info-icon {
  color: #909399;
  cursor: help;
}

.metric-body {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.metric-main {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.metric-value {
  font-size: 32px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.metric-value.text-success {
  color: #67c23a;
}

.metric-value.text-warning {
  color: #e6a23c;
}

.metric-value.text-danger {
  color: #f56c6c;
}

.metric-unit {
  font-size: 14px;
  color: #909399;
}

.metric-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.metric-trend {
  display: flex;
  align-items: center;
  font-weight: 500;
}

.metric-trend.trend-up {
  color: #67c23a;
}

.metric-trend.trend-down {
  color: #f56c6c;
}

.metric-baseline {
  color: #909399;
}

.metric-status {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #67c23a;
}

.status-icon {
  font-size: 14px;
}

/* 分布图 */
.metric-distribution {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.dist-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.dist-label {
  font-size: 12px;
  color: #909399;
  min-width: 60px;
}

/* 错误分解 */
.error-breakdown {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.error-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: white;
  border-radius: 4px;
  font-size: 12px;
}

.error-code {
  color: #f56c6c;
  font-weight: 500;
}

.error-count {
  color: #606266;
}

/* 操作区 */
.metric-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

/* 响应式 */
@media (max-width: 768px) {
  .metrics-container {
    grid-template-columns: 1fr;
  }
  
  .metric-value {
    font-size: 28px;
  }
}
</style>