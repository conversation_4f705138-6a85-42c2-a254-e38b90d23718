<template>
  <el-card class="changes-trend-chart" shadow="hover">
    <template #header>
      <div class="chart-header">
        <h3 class="chart-title">
          <el-icon><TrendCharts /></el-icon>
          变更趋势分析
        </h3>
        <el-button-group size="small">
          <el-button 
            v-for="period in timePeriods"
            :key="period.value"
            :type="selectedPeriod === period.value ? 'primary' : 'default'"
            @click="selectedPeriod = period.value"
          >
            {{ period.label }}
          </el-button>
        </el-button-group>
      </div>
    </template>

    <div class="chart-container">
      <div ref="chartRef" class="echart-instance"></div>
    </div>

    <div class="chart-footer">
      <div class="summary-item">
        <span class="summary-label">总变更数:</span>
        <span class="summary-value">{{ totalChanges }}</span>
      </div>
      <div class="summary-item">
        <span class="summary-label">平均每日:</span>
        <span class="summary-value">{{ avgDaily }}</span>
      </div>
      <div class="summary-item">
        <span class="summary-label">峰值:</span>
        <span class="summary-value">{{ peakValue }}</span>
      </div>
      <div class="summary-item">
        <span class="summary-label">增长率:</span>
        <span class="summary-value" :class="growthRateClass">
          <el-icon>
            <CaretTop v-if="growthRate > 0" />
            <CaretBottom v-else />
          </el-icon>
          {{ Math.abs(growthRate) }}%
        </span>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
 
import { 
  TrendCharts, 
  CaretTop, 
  CaretBottom 
} from '@element-plus/icons-vue'
import * as echarts from 'echarts/core'
import { HrLineChart, HrBarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  DataZoomComponent
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import type { ChartData } from '../types'

// 注册ECharts组件
echarts.use([
  LineChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  DataZoomComponent,
  CanvasRenderer
])

interface Props {
  data: ChartData
}

const props = defineProps<Props>()

const chartRef = ref<HTMLDivElement>()
let chartInstance: echarts.ECharts | null = null

// 时间周期选项
const timePeriods = [
  { label: '7天', value: '7d' },
  { label: '30天', value: '30d' },
  { label: '90天', value: '90d' },
  { label: '1年', value: '1y' }
]

const selectedPeriod = ref('30d')

// 计算统计数据
const totalChanges = computed(() => {
  if (!props.data.datasets[0]) return 0
  return props.data.datasets[0].data.reduce((sum, val) => sum + val, 0)
})

const avgDaily = computed(() => {
  if (!props.data.datasets[0]) return 0
  const data = props.data.datasets[0].data
  return Math.round(totalChanges.value / data.length)
})

const peakValue = computed(() => {
  if (!props.data.datasets[0]) return 0
  return Math.max(...props.data.datasets[0].data)
})

const growthRate = computed(() => {
  if (!props.data.datasets[0] || props.data.datasets[0].data.length < 2) return 0
  const data = props.data.datasets[0].data
  const lastWeekAvg = data.slice(-7).reduce((a, b) => a + b, 0) / 7
  const prevWeekAvg = data.slice(-14, -7).reduce((a, b) => a + b, 0) / 7
  return Math.round(((lastWeekAvg - prevWeekAvg) / prevWeekAvg) * 100)
})

const growthRateClass = computed(() => 
  growthRate.value > 0 ? 'growth-positive' : 'growth-negative'
)

// 图表配置
const chartOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      label: {
        backgroundColor: '#6a7985'
      }
    },
   
    formatter: (params: unknown) => {
      let html = `<div style="font-weight: bold; margin-bottom: 5px">${params[0].axisValue}</div>`
   
      params.forEach((param: unknown) => {
        html += `
          <div style="display: flex; align-items: center; justify-content: space-between; min-width: 150px">
            <span style="display: flex; align-items: center;">
              <span style="display: inline-block; width: 10px; height: 10px; background: ${param.color}; border-radius: 50%; margin-right: 5px;"></span>
              ${param.seriesName}
            </span>
            <span style="font-weight: bold; margin-left: 20px">${param.value}</span>
          </div>
        `
      })
      return html
    }
  },
  legend: {
    data: props.data.datasets.map(d => d.label),
    bottom: 0
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '15%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: props.data.labels,
    axisLabel: {
      formatter: (value: string) => {
        // 格式化日期显示
        const date = new Date(value)
        if (selectedPeriod.value === '7d') {
          return `${date.getMonth() + 1}/${date.getDate()}`
        } else if (selectedPeriod.value === '1y') {
          return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
        }
        return value
      }
    }
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '{value}'
    }
  },
  dataZoom: [
    {
      type: 'inside',
      start: selectedPeriod.value === '7d' ? 0 : 70,
      end: 100
    },
    {
      start: selectedPeriod.value === '7d' ? 0 : 70,
      end: 100
    }
  ],
  series: props.data.datasets.map((dataset, index) => ({
    name: dataset.label,
    type: dataset.type || 'line',
    smooth: true,
    symbol: 'circle',
    symbolSize: 8,
    sampling: 'lttb',
    itemStyle: {
      color: dataset.borderColor || getDefaultColor(index)
    },
    areaStyle: dataset.type === 'line' ? {
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color: `${dataset.borderColor || getDefaultColor(index)}33`
        },
        {
          offset: 1,
          color: `${dataset.borderColor || getDefaultColor(index)}05`
        }
      ])
    } : undefined,
    data: dataset.data
  }))
}))

// 默认颜色方案
const getDefaultColor = (index: number) => {
  const colors = ['#409eff', '#67c23a', '#e6a23c', '#f56c6c', '#909399']
  return colors[index % colors.length]
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  chartInstance.setOption(chartOption.value)
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return
  chartInstance.setOption(chartOption.value)
}

// 响应式调整
const handleResize = () => {
  chartInstance?.resize()
}

// 监听数据和选项变化
watch(() => props.data, updateChart, { deep: true })
watch(chartOption, updateChart)
watch(selectedPeriod, updateChart)

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  chartInstance?.dispose()
})
</script>

<style scoped>
.changes-trend-chart {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.chart-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-container {
  flex: 1;
  min-height: 300px;
  padding: 16px 0;
}

.echart-instance {
  width: 100%;
  height: 100%;
  min-height: 300px;
}

.chart-footer {
  display: flex;
  justify-content: space-around;
  padding: 16px 0;
  border-top: 1px solid #ebeef5;
  flex-wrap: wrap;
  gap: 16px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.summary-label {
  font-size: 12px;
  color: #909399;
}

.summary-value {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 4px;
}

.growth-positive {
  color: #67c23a;
}

.growth-negative {
  color: #f56c6c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .chart-container {
    min-height: 250px;
  }
  
  .echart-instance {
    min-height: 250px;
  }
  
  .chart-footer {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>