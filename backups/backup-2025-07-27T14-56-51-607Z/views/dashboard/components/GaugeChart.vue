<template>
  <div ref="chartRef" class="gauge-chart"></div>
</template>

<script setup lang="ts">
 
import { ref, onMounted, watch, onUnmounted } from 'vue'
import * as echarts from 'echarts'

interface Props {
  title?: string
  value: number
  max?: number
  unit?: string
  thresholds?: {
    value: number
    color: string
  }[]
}

const props = withDefaults(defineProps<Props>(), {
  max: 100,
  unit: '%'
})

const chartRef = ref<HTMLElement>()
let chartInstance: echarts.EChartsType | null = null

const getOption = () => {
  const splitNumber = props.thresholds ? props.thresholds.length : 5
  const axisLineColor = props.thresholds
    ? props.thresholds.map((t, index) => [
        index === 0 ? t.value / props.max : props.thresholds![index - 1].value / props.max,
        t.color
      ])
    : [
        [0.2, '#67C23A'],
        [0.4, '#409EFF'],
        [0.6, '#E6A23C'],
        [0.8, '#F56C6C'],
        [1, '#909399']
      ]

  return {
    tooltip: {
      formatter: '{a} <br/>{b} : {c}' + props.unit
    },
    series: [
      {
        name: props.title || '仪表盘',
        type: 'gauge',
        radius: '90%',
        min: 0,
        max: props.max,
        splitNumber: splitNumber,
        axisLine: {
          lineStyle: {
            color: axisLineColor,
            width: 20
          }
        },
        axisTick: {
          show: true,
          length: 10,
          lineStyle: {
            color: 'auto',
            width: 2
          }
        },
        splitLine: {
          length: 15,
          lineStyle: {
            color: 'auto',
            width: 2
          }
        },
        axisLabel: {
          color: '#606266',
          fontSize: 12,
          distance: -35,
          formatter: (value: number) => {
            return value.toFixed(0)
          }
        },
        pointer: {
          itemStyle: {
            color: 'auto'
          },
          width: 5
        },
        anchor: {
          show: true,
          showAbove: true,
          size: 20,
          itemStyle: {
            borderWidth: 8,
            borderColor: 'auto'
          }
        },
        title: {
          show: true,
          offsetCenter: [0, '65%'],
          fontSize: 14,
          color: '#606266'
        },
        detail: {
          valueAnimation: true,
          fontSize: 24,
          fontWeight: 600,
          offsetCenter: [0, '35%'],
          formatter: (value: number) => {
            return value.toFixed(1) + props.unit
          },
          color: 'auto'
        },
        data: [
          {
            value: props.value,
            name: props.title || ''
          }
        ]
      }
    ]
  }
}

const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  chartInstance.setOption(getOption())
}

const updateChart = () => {
  if (!chartInstance) return
  chartInstance.setOption(getOption())
}

const handleResize = () => {
  chartInstance?.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  chartInstance?.dispose()
  chartInstance = null
})

watch(() => props.value, () => {
  updateChart()
})

defineExpose({
  resize: handleResize,
  refresh: updateChart
})
</script>

<style scoped>
.gauge-chart {
  width: 100%;
  height: 100%;
  min-height: 300px;
}
</style>