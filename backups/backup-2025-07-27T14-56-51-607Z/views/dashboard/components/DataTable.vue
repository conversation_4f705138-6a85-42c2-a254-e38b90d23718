<template>
  <div class="data-table-widget">
    <div v-if="title" class="table-header">
      <h4 class="table-title">{{ title }}</h4>
      <el-button v-if="showExport" text @click="handleExport">
        <el-icon><Download /></el-icon>
        导出
      </el-button>
    </div>
    <el-table
      :data="tableData"
      :stripe="stripe"
      :border="border"
      :max-height="maxHeight"
      style="width: 100%"
      @sort-change="handleSortChange"
    >
      <el-table-column
        v-for="column in columns"
        :key="column.prop"
        :prop="column.prop"
        :label="column.label"
        :width="column.width"
        :min-width="column.minWidth"
        :sortable="column.sortable"
        :fixed="column.fixed"
        :align="column.align"
      >
        <template v-if="column.type === 'index'" #default="{ $index }">
          {{ $index + 1 }}
        </template>
        <template v-else-if="column.formatter" #default="{ row }">
          {{ column.formatter(row[column.prop], row) }}
        </template>
        <template v-else-if="column.type === 'tag'" #default="{ row }">
          <el-tag
            :type="getTagType(row[column.prop], column.tagMap)"
            size="small"
          >
            {{ row[column.prop] }}
          </el-tag>
        </template>
        <template v-else-if="column.type === 'progress'" #default="{ row }">
          <el-progress
            :percentage="row[column.prop]"
            :status="getProgressStatus(row[column.prop])"
           />
        </template>
      </el-table-column>
    </el-table>
    <div v-if="showPagination" class="table-pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
    </div>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'DataTable'
})
 
import { ref, computed, watch } from 'vue'
import type { TableColumn } from '@/types/common'
import { ElMessage } from 'element-plus'
import { Download } from '@element-plus/icons-vue'

// 使用通用TableColumn类型
interface Props {
  title?: string
  columns: TableColumn[]
   
  data: unknown[]
  stripe?: boolean
  border?: boolean
  maxHeight?: number | string
  showPagination?: boolean
  showExport?: boolean
  pageSize?: number
  total?: number
}

const props = withDefaults(defineProps<Props>(), {
  stripe: true,
  border: false,
  showPagination: true,
  showExport: true,
  pageSize: 10
})

const emit = defineEmits<{
  (e: 'page-change', page: number, size: number): void
  (e: 'sort-change', column: string, order: string): void
  (e: 'export'): void
}>()

const currentPage = ref(1)
const pageSize = ref(props.pageSize)

const tableData = computed(() => {
  if (!props.showPagination) return props.data
  
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return props.data.slice(start, end)
})

const total = computed(() => props.total || props.data.length)

   
const getTagType = (value: unknown, tagMap?: Record<string, string>) => {
  if (!tagMap) return ''
  return tagMap[value] || ''
}

const getProgressStatus = (value: number) => {
  if (value >= 80) return 'success'
  if (value >= 60) return 'warning'
  return 'exception'
}

   
const handleSortChange = ({ column, prop, order }: unknown) => {
  if (column.sortable === 'custom') {
    emit('sort-change', prop, order)
  }
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  emit('page-change', currentPage.value, size)
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  emit('page-change', page, pageSize.value)
}

const handleExport = () => {
  emit('export')
  ElMessage.success('导出功能已触发')
}

watch(() => props.pageSize, (newSize) => {
  pageSize.value = newSize
})
</script>

<style scoped>
.data-table-widget {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.table-pagination {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style>