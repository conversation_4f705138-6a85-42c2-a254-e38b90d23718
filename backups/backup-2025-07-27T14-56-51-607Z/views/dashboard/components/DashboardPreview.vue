<template>
  <div 
    class="dashboard-preview"
    :style="{
      backgroundColor: config.backgroundColor || '#f5f7fa'
    }"
  >
    <!-- 预览模式工具栏 -->
    <div class="preview-toolbar">
      <div class="toolbar-left">
        <span class="preview-title">{{ config.name || '仪表盘预览' }}</span>
      </div>
      <div class="toolbar-right">
        <el-button @click="toggleFullscreen">
          <el-icon><FullScreen /></el-icon>
          {{ isFullscreen ? '退出全屏' : '全屏' }}
        </el-button>
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button type="primary" @click="$emit('close')">
          <el-icon><Close /></el-icon>
          关闭预览
        </el-button>
      </div>
    </div>

    <!-- 仪表盘内容区域 -->
    <div class="preview-content" ref="contentRef">
      <div
        v-for="widget in widgets"
        :key="widget.id"
        class="preview-widget"
        :style="{
          left: widget.x + 'px',
          top: widget.y + 'px',
          width: widget.width + 'px',
          height: widget.height + 'px'
        }"
      >
        <!-- 组件标题栏 -->
        <div class="widget-header" v-if="widget.name">
          <span class="widget-title">{{ widget.name }}</span>
          <div class="widget-actions">
            <el-button
              v-if="widget.config.exportTypes?.includes('image')"
              size="small"
              link
              @click="exportWidget(widget, 'image')"
            >
              <el-icon><Picture /></el-icon>
            </el-button>
            <el-button
              v-if="widget.config.exportTypes?.includes('excel')"
              size="small"
              link
              @click="exportWidget(widget, 'excel')"
            >
              <el-icon><Document /></el-icon>
            </el-button>
          </div>
        </div>
        
        <!-- 组件内容 -->
        <div class="widget-body">
          <component
            :is="getComponentByType(widget.type)"
            v-bind="widget.config"
            :data="widgetData[widget.id]"
            :loading="widgetLoading[widget.id]"
            @click="handleWidgetClick(widget, $event)"
          />
        </div>
      </div>
    </div>

    <!-- 加载遮罩 -->
    <div v-if="loading" class="loading-mask">
      <el-icon class="is-loading" :size="40">
        <Loading />
      </el-icon>
      <p>正在加载数据...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  FullScreen,
  Refresh,
  Close,
  Picture,
  Document,
  Loading
} from '@element-plus/icons-vue'
import HrLineChart from '@/components/visualization/HrLineChart.vue'
import HrBarChart from '@/components/visualization/HrBarChart.vue'
import HrPieChart from '@/components/visualization/HrPieChart.vue'
import HrRadarChart from '@/components/visualization/HrRadarChart.vue'
import HrScatterChart from '@/components/visualization/HrScatterChart.vue'
import StatCard from '../components/StatCard.vue'
import GaugeChart from '../components/GaugeChart.vue'
import DataTable from '../components/DataTable.vue'
import { dashboardApi } from '@/api/dashboard'

// Props定义
interface Props {
   
  widgets: unknown[]
   
  config: unknown
}

const props = defineProps<Props>()

// Emits定义
const emit = defineEmits<{
  (e: 'close'): void
}>()
// 组件映射
const componentMap: Record<string, unknown> = {
  line: LineChart,
  bar: BarChart,
  pie: PieChart,
  radar: RadarChart,
  scatter: ScatterChart,
  card: StatCard,
  gauge: GaugeChart,
  table: DataTable
}

// 响应式数据
const contentRef = ref<HTMLElement>()
const loading = ref(false)
const isFullscreen = ref(false)
const widgetData = reactive<Record<string, any>>({})
const widgetLoading = reactive<Record<string, boolean>>({})
const refreshTimers = reactive<Record<string, number>>({})

// 获取组件
const getComponentByType = (type: string) => {
  return componentMap[type] || 'div'
}

// 加载组件数据
   
const loadWidgetData = async (widget: unknown) => {
  widgetLoading[widget.id] = true
  
  try {
    // 根据数据源类型加载数据
    if (widget.dataSource === 'static') {
      // 静态数据
      widgetData[widget.id] = widget.data || getMockData(widget.type)
    } else if (widget.dataSource === 'api') {
      // API数据
      const response = await dashboardApi.getWidgetData(widget)
      widgetData[widget.id] = response.data
    } else if (widget.dataSource === 'realtime') {
      // 实时数据
      widgetData[widget.id] = getMockData(widget.type)
      
      // 设置定时刷新
      if (widget.refreshInterval > 0) {
        refreshTimers[widget.id] = window.setInterval(() => {
          updateRealtimeData(widget)
        }, widget.refreshInterval)
      }
    }
  } catch (__error) {
    console.error(`加载组件 ${widget.id} 数据失败:`, error)
    ElMessage.error(`组件 ${widget.name} 数据加载失败`)
  } finally {
    widgetLoading[widget.id] = false
  }
}

// 获取模拟数据
const getMockData = (type: string) => {
  switch (type) {
    case 'line':
    case 'bar':
      return {
        xAxis: ['一月', '二月', '三月', '四月', '五月', '六月'],
        series: [
          {
            name: 'HrHr销售额',
            data: [120, 200, 150, 80, 70, 110]
          },
          {
            name: '利润',
            data: [80, 160, 120, 60, 50, 80]
          }
        ]
      }
    case 'pie':
      return [
        { name: '直接访问', value: 335 },
        { name: '邮件营销', value: 310 },
        { name: '联盟广告', value: 234 },
        { name: '视频广告', value: 135 },
        { name: '搜索引擎', value: 400 }
      ]
    case 'radar':
      return {
        indicators: [
          { name: '销售', max: 100 },
          { name: '管理', max: 100 },
          { name: '信息技术', max: 100 },
          { name: '客服', max: 100 },
          { name: '研发', max: 100 },
          { name: '市场', max: 100 }
        ],
        series: [
          {
            name: '预算分配',
            value: [83, 88, 65, 77, 85, 90]
          },
          {
            name: '实际开销',
            value: [75, 80, 70, 82, 78, 85]
          }
        ]
      }
    case 'scatter':
      return [
        {
          name: '散点数据',
          data: Array.from({ length: 50 }, () => [
            Math.random() * 100,
            Math.random() * 100,
            Math.random() * 50 + 10
          ])
        }
      ]
    default:
      return null
  }
}

// 更新实时数据
   
const updateRealtimeData = (widget: unknown) => {
  // 模拟数据更新
  const currentData = widgetData[widget.id]
  if (!currentData) return
  
  switch (widget.type) {
    case 'line':
    case 'bar':
   
      currentData.series.forEach((series: unknown) => {
        series.data = series.data.map((v: number) => 
          Math.max(0, v + (Math.random() - 0.5) * 20)
        )
      })
      break
    case 'pie':
   
      currentData.forEach((item: unknown) => {
        item.value = Math.max(100, item.value + (Math.random() - 0.5) * 50)
      })
      break
  }
}

// 刷新所有数据
const refreshData = async () => {
  loading.value = true
  
  try {
    await Promise.all(
      props.widgets.map(widget => loadWidgetData(widget))
    )
    ElMessage.success('数据刷新成功')
  } catch (__error) {
    ElMessage.error('部分数据刷新失败')
  } finally {
    loading.value = false
  }
}

// 切换全屏
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    contentRef.value?.requestFullscreen()
    isFullscreen.value = true
  } else {
    document.exitFullscreen()
    isFullscreen.value = false
  }
}

// 处理组件点击
   
const handleWidgetClick = (widget: unknown, event: unknown) => {
  if (!widget.config.enableClick) return
  
  switch (widget.config.clickAction) {
    case 'detail':
      // 显示详情
      ElMessage.info(`点击了 ${widget.name}`)
      break
    case 'link':
      // 跳转链接
      if (widget.config.clickLink) {
        window.open(widget.config.clickLink, '_blank')
      }
      break
    case 'drill':
      // 下钻数据
      ElMessage.info('数据下钻功能开发中')
      break
    case 'script':
      // 自定义脚本
      if (widget.config.clickScript) {
        try {
           
          new Function('widget', 'event', widget.config.clickScript)(widget, event)
        } catch (__error) {
          console.error('执行脚本失败:', error)
        }
      }
      break
  }
}

// 导出组件
   
const exportWidget = async (widget: unknown, type: string) => {
  switch (type) {
    case 'image':
      try {
        // 获取widget对应的DOM元素
        const widgetElement = document.getElementById(`widget-${widget.id}`)
        if (!widgetElement) {
          ElMessage.error('无法找到要导出的组件')
          return
        }
        
        // 使用canvas将DOM转换为图片
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        const rect = widgetElement.getBoundingClientRect()
        
        canvas.width = rect.width
        canvas.height = rect.height
        
        // 这里需要使用html2canvas等库，暂时使用简单实现
        // 创建下载链接
        const link = document.createElement('a')
        link.download = `${widget.name}-${new Date().getTime()}.png`
        
        // 使用浏览器的截图API（如果支持）
        if ('domtoimage' in window) {
          // 需要引入dom-to-image库
          ElMessage.warning('图片导出功能需要额外的库支持')
        } else {
          // 临时解决方案：打开打印预览让用户截图
          window.print()
          ElMessage.info('请使用浏览器的打印功能保存为PDF或截图')
        }
      } catch (__error) {
        console.error('导出图片失败:', error)
        ElMessage.error('导出图片失败')
      }
      break
      
    case 'excel':
      try {
        // 获取widget的数据
        const data = widgetData[widget.id]
        if (!data) {
          ElMessage.error('没有可导出的数据')
          return
        }
        
        // 根据widget类型处理数据
        let csvContent = ''
        if (widget.type === 'table' && Array.isArray(data)) {
          // 表格数据导出
          const headers = Object.keys(data[0] || {})
          csvContent = headers.join(',') + '\n'
          
          data.forEach(row => {
            const values = headers.map(header => {
              const value = row[header]
              // 处理包含逗号的值
              return typeof value === 'string' && value.includes(',') 
                ? `"${value}"` 
                : value
            })
            csvContent += values.join(',') + '\n'
          })
        } else {
          // 其他类型数据导出为JSON
          csvContent = JSON.stringify(data, null, 2)
        }
        
        // 创建Blob并下载
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
        const link = document.createElement('a')
        link.href = URL.createObjectURL(blob)
        link.download = `${widget.name}-${new Date().getTime()}.csv`
        link.click()
        
        ElMessage.success('导出成功')
      } catch (__error) {
        console.error('导出Excel失败:', error)
        ElMessage.error('导出Excel失败')
      }
      break
  }
}

// 监听全屏变化
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
}

// 生命周期
onMounted(() => {
  // 加载所有组件数据
  props.widgets.forEach(widget => {
    loadWidgetData(widget)
  })
  
  // 监听全屏事件
  document.addEventListener('fullscreenchange', handleFullscreenChange)
})

onUnmounted(() => {
  // 清理定时器
  Object.values(refreshTimers).forEach(timer => {
    clearInterval(timer)
  })
  
  // 移除事件监听
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
})
</script>

<style scoped>
.dashboard-preview {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 预览工具栏 */
.preview-toolbar {
  height: 50px;
  background: rgba(255, 255, 255, 0.95);
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  z-index: 100;
}

.preview-title {
  font-size: 18px;
  font-weight: 500;
  color: #303133;
}

.toolbar-right {
  display: flex;
  gap: 10px;
}

/* 预览内容区域 */
.preview-content {
  flex: 1;
  position: relative;
  overflow: auto;
}

/* 预览组件 */
.preview-widget {
  position: absolute;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 组件标题栏 */
.widget-header {
  height: 40px;
  padding: 0 15px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f5f7fa;
}

.widget-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.widget-actions {
  display: flex;
  gap: 5px;
}

/* 组件内容 */
.widget-body {
  flex: 1;
  padding: 10px;
  overflow: hidden;
}

/* 加载遮罩 */
.loading-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 200;
}

.loading-mask p {
  margin-top: 20px;
  color: #909399;
  font-size: 14px;
}

/* 全屏模式 */
:fullscreen .preview-toolbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
}

:fullscreen .preview-content {
  margin-top: 50px;
}

/* 响应式图表容器 */
.widget-body > div {
  width: 100% !important;
  height: 100% !important;
}
</style>