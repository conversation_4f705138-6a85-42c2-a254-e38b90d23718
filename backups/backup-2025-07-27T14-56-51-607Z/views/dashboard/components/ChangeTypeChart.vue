<template>
  <el-card class="change-type-chart" shadow="hover">
    <template #header>
      <div class="chart-header">
        <h3 class="chart-title">
          <el-icon><Histogram /></el-icon>
          变更类型分析
        </h3>
        <el-radio-group 
          v-model="viewMode" 
          size="small"
        >
          <el-radio-button label="count">数量</el-radio-button>
          <el-radio-button label="percent">占比</el-radio-button>
        </el-radio-group>
      </div>
    </template>

    <div class="chart-container">
      <div ref="chartRef" class="echart-instance"></div>
    </div>

    <!-- 数据表格 -->
    <div class="data-table">
      <table>
        <thead>
          <tr>
            <th>变更类型</th>
            <th>本月</th>
            <th>上月</th>
            <th>环比</th>
            <th>占比</th>
          </tr>
        </thead>
        <tbody>
          <tr 
            v-for="row in tableData"
            :key="row.type"
            @click="$emit('row-click', row)"
            class="clickable-row"
          >
            <td>
              <span class="type-indicator" :style="{ backgroundColor: row.color }"></span>
              {{ row.type }}
            </td>
            <td class="text-right">{{ row.current }}</td>
            <td class="text-right">{{ row.previous }}</td>
            <td class="text-right">
              <span :class="row.changeClass">
                <el-icon v-if="row.change !== 0">
                  <CaretTop v-if="row.change > 0" />
                  <CaretBottom v-else />
                </el-icon>
                {{ Math.abs(row.change) }}%
              </span>
            </td>
            <td class="text-right">{{ row.percent }}%</td>
          </tr>
        </tbody>
      </table>
    </div>
  </el-card>
</template>

<script setup lang="ts">
 
import { 
  Histogram, 
  CaretTop, 
  CaretBottom 
} from '@element-plus/icons-vue'
import * as echarts from 'echarts/core'
import { HrBarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import type { ChartData } from '../types'

// 注册ECharts组件
echarts.use([
  BarChart,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  CanvasRenderer
])

interface Props {
  data: ChartData
}

interface Emits {
   
  (e: 'row-click', row: unknown): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const chartRef = ref<HTMLDivElement>()
let chartInstance: echarts.ECharts | null = null

const viewMode = ref<'count' | 'percent'>('count')

// 颜色方案
const colorScheme = [
  '#409eff', '#67c23a', '#e6a23c', '#f56c6c', 
  '#909399', '#00c0ef', '#dd4b39', '#f39c12'
]

// 表格数据
const tableData = computed(() => {
  if (!props.data.labels.length) return []
  
  const currentData = props.data.datasets[0]?.data || []
  const previousData = props.data.datasets[1]?.data || []
  const total = currentData.reduce((sum, val) => sum + val, 0)
  
  return props.data.labels.map((label, index) => {
    const current = currentData[index] || 0
    const previous = previousData[index] || 0
    const change = previous ? Math.round(((current - previous) / previous) * 100) : 0
    
    return {
      type: label,
      current,
      previous,
      change,
      changeClass: change > 0 ? 'text-success' : change < 0 ? 'text-danger' : 'text-muted',
      percent: total ? ((current / total) * 100).toFixed(1) : '0.0',
      color: colorScheme[index % colorScheme.length]
    }
  })
})

// 图表配置
const chartOption = computed(() => {
  const isPercent = viewMode.value === 'percent'
  
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
   
      formatter: (params: unknown) => {
        let html = `<div style="font-weight: bold; margin-bottom: 5px">${params[0].axisValue}</div>`
   
        params.forEach((param: unknown) => {
          const value = isPercent ? `${param.value}%` : param.value
          html += `
            <div style="display: flex; align-items: center; justify-content: space-between; min-width: 150px">
              <span style="display: flex; align-items: center;">
                <span style="display: inline-block; width: 10px; height: 10px; background: ${param.color}; border-radius: 50%; margin-right: 5px;"></span>
                ${param.seriesName}
              </span>
              <span style="font-weight: bold; margin-left: 20px">${value}</span>
            </div>
          `
        })
        return html
      }
    },
    legend: {
      data: props.data.datasets.map(d => d.label),
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: props.data.labels,
      axisLabel: {
        interval: 0,
        rotate: 45,
        formatter: (value: string) => {
          return value.length > 6 ? value.substring(0, 6) + '...' : value
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: isPercent ? '{value}%' : '{value}'
      }
    },
    series: props.data.datasets.map((dataset, seriesIndex) => {
      const data = isPercent 
        ? dataset.data.map((val, idx) => {
            const total = dataset.data.reduce((sum, v) => sum + v, 0)
            return total ? ((val / total) * 100).toFixed(1) : 0
          })
        : dataset.data
        
      return {
        name: dataset.label,
        type: 'bar',
        barWidth: '40%',
        itemStyle: {
          borderRadius: [4, 4, 0, 0],
   
          color: (params: unknown) => colorScheme[params.dataIndex % colorScheme.length]
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        data
      }
    })
  }
})

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  chartInstance.setOption(chartOption.value)
  
  // 点击事件
   
  chartInstance.on('click', (params: unknown) => {
    const row = tableData.value.find(item => item.type === params.name)
    if (row) emit('row-click', row)
  })
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return
  chartInstance.setOption(chartOption.value)
}

// 响应式调整
const handleResize = () => {
  chartInstance?.resize()
}

// 监听变化
watch(() => props.data, updateChart, { deep: true })
watch(viewMode, updateChart)

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  chartInstance?.dispose()
})
</script>

<style scoped>
.change-type-chart {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.chart-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-container {
  flex: 1;
  min-height: 300px;
  padding: 16px 0;
}

.echart-instance {
  width: 100%;
  height: 100%;
  min-height: 300px;
}

/* 数据表格 */
.data-table {
  margin-top: 20px;
  overflow-x: auto;
}

.data-table table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.data-table th {
  padding: 12px 8px;
  text-align: left;
  font-weight: 500;
  color: #909399;
  border-bottom: 1px solid #ebeef5;
  white-space: nowrap;
}

.data-table td {
  padding: 12px 8px;
  border-bottom: 1px solid #f2f6fc;
}

.clickable-row {
  cursor: pointer;
  transition: background-color 0.3s;
}

.clickable-row:hover {
  background-color: #f5f7fa;
}

.type-indicator {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 8px;
  vertical-align: middle;
}

.text-right {
  text-align: right;
}

.text-success {
  color: #67c23a;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 2px;
}

.text-danger {
  color: #f56c6c;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 2px;
}

.text-muted {
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .chart-container {
    min-height: 250px;
  }
  
  .echart-instance {
    min-height: 250px;
  }
  
  .data-table {
    font-size: 12px;
  }
  
  .data-table th,
  .data-table td {
    padding: 8px 4px;
  }
}
</style>