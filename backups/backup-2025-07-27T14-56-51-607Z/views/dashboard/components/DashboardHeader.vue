<template>
  <div class="dashboard-header" role="banner">
    <div class="header-content">
      <div class="header-title">
        <h1>{{ title }}</h1>
        <p class="subtitle">{{ subtitle }}</p>
      </div>

      <!-- 简化的快速筛选 -->
      <div class="quick-controls" v-if="!isMobile">
        <el-button-group class="time-range-group">
          <el-button
            v-for="range in quickTimeRanges"
            :key="range.value"
            :type="modelValue.timeRange === range.value ? 'primary' : 'default'"
            size="small"
            @click="$emit('update:modelValue', { ...modelValue, timeRange: range.value })"
            :aria-pressed="modelValue.timeRange === range.value"
          >
            {{ range.label }}
          </el-button>
        </el-button-group>

        <div class="header-actions">
          <el-tooltip content="刷新数据" placement="bottom">
            <el-button
              :icon="Refresh"
              circle
              @click="$emit('refresh')"
              :loading="loading"
              aria-label="刷新数据"
              />
          </el-tooltip>

          <el-tooltip content="导出报告" placement="bottom">
            <el-button
              :icon="Download"
              circle
              @click="$emit('export')"
              aria-label="导出报告"
              />
          </el-tooltip>

          <el-tooltip content="设置" placement="bottom">
            <el-button
              :icon="Setting"
              circle
              @click="$emit('show-settings')"
              aria-label="打开设置"
              />
          </el-tooltip>

          <el-switch
            :model-value="autoRefresh"
            active-text="自动刷新"
            @change="$emit('update:autoRefresh', $event)"
            aria-label="切换自动刷新"
           />
        </div>
      </div>

      <!-- 移动端控制按钮 -->
      <div class="mobile-controls" v-if="isMobile">
        <el-button
          :icon="Filter"
          circle
          @click="$emit('show-mobile-filters')"
          aria-label="打开筛选"
          />
        <el-button
          :icon="Refresh"
          circle
          @click="$emit('refresh')"
          :loading="loading"
          aria-label="刷新数据"
          />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'DashboardHeader'
})
 
import { Refresh, Download, Setting, Filter } from '@element-plus/icons-vue'
import type { DashboardQueryParams, TimeRange } from '../types'

interface Props {
  title?: string
  subtitle?: string
  modelValue: DashboardQueryParams
  loading?: boolean
  autoRefresh?: boolean
  isMobile?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: DashboardQueryParams): void
  (e: 'update:autoRefresh', value: boolean): void
  (e: 'refresh'): void
  (e: 'export'): void
  (e: 'show-settings'): void
  (e: 'show-mobile-filters'): void
}

const props = withDefaults(defineProps<Props>(), {
  title: '数据仪表板',
  subtitle: '实时监控系统运行状态和关键指标',
  loading: false,
  autoRefresh: false,
  isMobile: false
})

const emit = defineEmits<Emits>()

// 快速时间范围选项
const quickTimeRanges = [
  { label: '今日', value: 'today' as TimeRange },
  { label: '本周', value: 'week' as TimeRange },
  { label: '本月', value: 'month' as TimeRange },
  { label: '本季', value: 'quarter' as TimeRange },
  { label: '本年', value: 'year' as TimeRange }
]
</script>

<style scoped>
.dashboard-header {
  background: linear-gradient(135deg, #f5f7fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #e4e7ed;
  padding: 20px 0;
  margin-bottom: 24px;
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.header-title h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.subtitle {
  margin: 4px 0 0;
  font-size: 14px;
  color: #909399;
}

.quick-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.time-range-group {
  display: flex;
  flex-wrap: nowrap;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.mobile-controls {
  display: flex;
  gap: 8px;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 16px;
  }

  .header-title h1 {
    font-size: 20px;
  }

  .subtitle {
    font-size: 12px;
  }
}
</style>