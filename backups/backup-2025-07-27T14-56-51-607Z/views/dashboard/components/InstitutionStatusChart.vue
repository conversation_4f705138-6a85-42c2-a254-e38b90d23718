<template>
  <el-card class="institution-status-chart" shadow="hover">
    <template #header>
      <div class="chart-header">
        <h3 class="chart-title">
          <el-icon><hr-pie-chart /></el-icon>
          机构状态分布
        </h3>
        <el-dropdown @command="handleExport">
          <el-button size="small" text>
            导出
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="png">导出为图片</el-dropdown-item>
              <el-dropdown-item command="excel">导出为Excel</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </template>

    <div class="chart-wrapper">
      <div ref="chartRef" class="echart-instance"></div>
      
      <!-- 图例详情 -->
      <div class="legend-details">
        <div 
          v-for="(item, index) in legendData"
          :key="item.name"
          class="legend-item"
          @click="handleLegendClick(item.name)"
          :class="{ 'is-disabled': !item.selected }"
        >
          <span 
            class="legend-dot" 
            :style="{ backgroundColor: item.color }"
          ></span>
          <span class="legend-name">{{ item.name }}</span>
          <span class="legend-value">{{ item.value }}</span>
          <span class="legend-percent">{{ item.percent }}%</span>
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-footer">
      <div class="stat-item">
        <el-icon><CircleCheck /></el-icon>
        <span>正常运行: {{ normalCount }}家</span>
      </div>
      <div class="stat-item">
        <el-icon><Warning /></el-icon>
        <span>需关注: {{ warningCount }}家</span>
      </div>
      <div class="stat-item">
        <el-icon><CircleClose /></el-icon>
        <span>异常: {{ errorCount }}家</span>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
 
import { 
  HrPieChart as PieChartIcon, 
  ArrowDown,
  CircleCheck,
  Warning,
  CircleClose
} from '@element-plus/icons-vue'
import * as echarts from 'echarts/core'
import { HrPieChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import type { ChartData } from '../types'

// 注册ECharts组件
echarts.use([
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  CanvasRenderer
])

interface Props {
  data: ChartData
}

interface Emits {
   
  (e: 'item-click', item: unknown): void
  (e: 'export', type: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const chartRef = ref<HTMLDivElement>()
let chartInstance: echarts.ECharts | null = null

// 颜色映射
const colorMap: Record<string, string> = {
  '正常': '#67c23a',
  '待审核': '#409eff',
  '整改中': '#e6a23c',
  '已暂停': '#f56c6c',
  '已注销': '#909399'
}

// 图例数据
const legendData = computed(() => {
  if (!props.data.datasets[0]) return []
  
  const dataset = props.data.datasets[0]
  const total = dataset.data.reduce((sum, val) => sum + val, 0)
  
  return props.data.labels.map((label, index) => ({
    name: label,
    value: dataset.data[index],
    percent: ((dataset.data[index] / total) * 100).toFixed(1),
    color: colorMap[label] || getDefaultColor(index),
    selected: true
  }))
})

// 统计数据
const normalCount = computed(() => {
  const index = props.data.labels.indexOf('正常')
  return index >= 0 ? props.data.datasets[0].data[index] : 0
})

const warningCount = computed(() => {
  const indices = ['待审核', '整改中'].map(label => props.data.labels.indexOf(label))
  return indices.reduce((sum, idx) => 
    idx >= 0 ? sum + props.data.datasets[0].data[idx] : sum, 0
  )
})

const errorCount = computed(() => {
  const indices = ['已暂停', '已注销'].map(label => props.data.labels.indexOf(label))
  return indices.reduce((sum, idx) => 
    idx >= 0 ? sum + props.data.datasets[0].data[idx] : sum, 0
  )
})

// 图表配置
const chartOption = computed(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{b}: {c} ({d}%)'
  },
  series: [
    {
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '45%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 20,
          fontWeight: 'bold',
   
          formatter: (params: unknown) => {
            return `${params.name}\n${params.value}`
          }
        },
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      labelLine: {
        show: false
      },
      data: props.data.labels.map((label, index) => ({
        name: label,
        value: props.data.datasets[0].data[index],
        itemStyle: {
          color: colorMap[label] || getDefaultColor(index)
        }
      }))
    }
  ]
}))

// 默认颜色
const getDefaultColor = (index: number) => {
  const colors = ['#409eff', '#67c23a', '#e6a23c', '#f56c6c', '#909399']
  return colors[index % colors.length]
}

// 处理图例点击
const handleLegendClick = (name: string) => {
  if (!chartInstance) return
  
  chartInstance.dispatchAction({
    type: 'legendToggleSelect',
    name: name
  })
}

// 处理导出
const handleExport = (command: string) => {
  if (command === 'png' && chartInstance) {
    const url = chartInstance.getDataURL({
      type: 'png',
      pixelRatio: 2,
      backgroundColor: '#fff'
    })
    const link = document.createElement('a')
    link.href = url
    link.download = `机构状态分布_${new Date().toLocaleDateString()}.png`
    link.click()
  } else {
    emit('export', command)
  }
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  chartInstance.setOption(chartOption.value)
  
  // 点击事件
   
  chartInstance.on('click', (params: unknown) => {
    emit('item-click', params.data)
  })
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return
  chartInstance.setOption(chartOption.value)
}

// 响应式调整
const handleResize = () => {
  chartInstance?.resize()
}

// 监听数据变化
watch(() => props.data, updateChart, { deep: true })
watch(chartOption, updateChart)

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  chartInstance?.dispose()
})
</script>

<style scoped>
.institution-status-chart {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 16px 0;
}

.echart-instance {
  width: 100%;
  height: 300px;
}

/* 图例详情 */
.legend-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 0 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.legend-item:hover {
  background-color: #f5f7fa;
}

.legend-item.is-disabled {
  opacity: 0.5;
}

.legend-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.legend-name {
  flex: 1;
  font-size: 14px;
  color: #606266;
}

.legend-value {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-right: 8px;
}

.legend-percent {
  font-size: 14px;
  color: #909399;
}

/* 统计信息 */
.stats-footer {
  display: flex;
  justify-content: space-around;
  padding: 16px 0;
  border-top: 1px solid #ebeef5;
  flex-wrap: wrap;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #606266;
}

.stat-item .el-icon {
  font-size: 16px;
}

.stat-item:nth-child(1) .el-icon {
  color: #67c23a;
}

.stat-item:nth-child(2) .el-icon {
  color: #e6a23c;
}

.stat-item:nth-child(3) .el-icon {
  color: #f56c6c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .echart-instance {
    height: 250px;
  }
  
  .legend-details {
    padding: 0 10px;
  }
  
  .stats-footer {
    flex-direction: column;
    align-items: center;
  }
}
</style>