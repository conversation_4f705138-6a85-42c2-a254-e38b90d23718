<template>
  <el-card class="department-heatmap-chart" shadow="hover">
    <template #header>
      <div class="chart-header">
        <h3 class="chart-title">
          <el-icon><Grid /></el-icon>
          部门活跃度热力图
        </h3>
        <div class="header-controls">
          <el-select 
            v-model="metricType" 
            size="small"
            style="width: 120px"
          >
            <el-option 
              v-for="metric in metricOptions"
              :key="metric.value"
              :label="metric.label"
              :value="metric.value"
             />
          </el-select>
          <el-button 
            size="small"
            :icon="fullscreen ? Minus : FullScreen"
            @click="toggleFullscreen"
            circle
            />
        </div>
      </div>
    </template>

    <div class="chart-container" :class="{ 'is-fullscreen': fullscreen }">
      <div ref="chartRef" class="echart-instance"></div>
      
      <!-- 图例说明 -->
      <div class="legend-bar">
        <span class="legend-label">低</span>
        <div class="legend-gradient"></div>
        <span class="legend-label">高</span>
      </div>
    </div>

    <!-- 部门排行 -->
    <div class="department-ranking" v-if="!fullscreen">
      <h4 class="ranking-title">活跃度排行 TOP5</h4>
      <div class="ranking-list">
        <div 
          v-for="(dept, index) in topDepartments"
          :key="dept.name"
          class="ranking-item"
          @click="$emit('department-click', dept)"
        >
          <span class="ranking-index" :class="`ranking-${index + 1}`">
            {{ index + 1 }}
          </span>
          <span class="department-name">{{ dept.name }}</span>
          <div class="department-bar">
            <div 
              class="bar-fill"
              :style="{ 
                width: `${(dept.value / maxValue) * 100}%`,
                backgroundColor: getBarColor(index)
              }"
            ></div>
          </div>
          <span class="department-value">{{ dept.value }}</span>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">

defineOptions({
  name: 'DepartmentHeatmapChart'
})
 
import { 
  Grid, 
  FullScreen, 
  Minus 
} from '@element-plus/icons-vue'
import * as echarts from 'echarts/core'
import { HeatmapChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  VisualMapComponent
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import type { HeatmapData } from '../types'

// 注册ECharts组件
echarts.use([
  HeatmapChart,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  VisualMapComponent,
  CanvasRenderer
])

interface Props {
  data: HeatmapData
}

interface Emits {
   
  (e: 'department-click', dept: unknown): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const chartRef = ref<HTMLDivElement>()
let chartInstance: echarts.ECharts | null = null

const fullscreen = ref(false)
const metricType = ref('changes')

// 指标选项
const metricOptions = [
  { label: '变更次数', value: 'changes' },
  { label: '审批效率', value: 'approval' },
  { label: '问题发生率', value: 'issues' }
]

// 计算数据范围
const dataRange = computed(() => {
  const values = props.data.data.map(item => item[2])
  return {
    min: Math.min(...values),
    max: Math.max(...values)
  }
})

// 最大值
const maxValue = computed(() => dataRange.value.max)

// 部门活跃度排行
const topDepartments = computed(() => {
  // 聚合每个部门的数据
  const deptMap = new Map<string, number>()
  
  props.data.data.forEach(([x, y, value]) => {
    const dept = props.data.categories.y[y]
    deptMap.set(dept, (deptMap.get(dept) || 0) + value)
  })
  
  // 转换为数组并排序
  return Array.from(deptMap.entries())
    .map(([name, value]) => ({ name, value }))
    .sort((a, b) => b.value - a.value)
    .slice(0, 5)
})

// 图表配置
const chartOption = computed(() => ({
  tooltip: {
    position: 'top',
   
    formatter: (params: unknown) => {
      const x = props.data.categories.x[params.data[0]]
      const y = props.data.categories.y[params.data[1]]
      const value = params.data[2]
      return `
        <div style="font-weight: bold; margin-bottom: 5px">${y}</div>
        <div>${x}: ${value}</div>
      `
    }
  },
  grid: {
    left: '15%',
    right: '5%',
    bottom: '15%',
    top: '5%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: props.data.categories.x,
    splitArea: {
      show: true
    },
    axisLabel: {
      interval: 0,
      rotate: 45,
      formatter: (value: string) => {
        return value.length > 8 ? value.substring(0, 8) + '...' : value
      }
    }
  },
  yAxis: {
    type: 'category',
    data: props.data.categories.y,
    splitArea: {
      show: true
    },
    axisLabel: {
      formatter: (value: string) => {
        return value.length > 10 ? value.substring(0, 10) + '...' : value
      }
    }
  },
  visualMap: {
    min: dataRange.value.min,
    max: dataRange.value.max,
    calculable: true,
    orient: 'horizontal',
    left: 'center',
    bottom: '0%',
    show: false,
    inRange: {
      color: ['#e3f2fd', '#90caf9', '#42a5f5', '#1e88e5', '#1565c0', '#0d47a1']
    }
  },
  series: [{
    type: 'heatmap',
    data: props.data.data,
    label: {
      show: true,
   
      formatter: (params: unknown) => {
        const value = params.data[2]
        return value > 0 ? value.toString() : ''
      }
    },
    emphasis: {
      itemStyle: {
        shadowBlur: 10,
        shadowColor: 'rgba(0, 0, 0, 0.5)'
      }
    }
  }]
}))

// 获取排行条颜色
const getBarColor = (index: number) => {
  const colors = ['#f56c6c', '#e6a23c', '#409eff', '#67c23a', '#909399']
  return colors[index] || colors[colors.length - 1]
}

// 切换全屏
const toggleFullscreen = () => {
  fullscreen.value = !fullscreen.value
  nextTick(() => {
    handleResize()
  })
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  chartInstance.setOption(chartOption.value)
  
  // 点击事件
   
  chartInstance.on('click', (params: unknown) => {
    const deptName = props.data.categories.y[params.data[1]]
    const dept = topDepartments.value.find(d => d.name === deptName)
    if (dept) emit('department-click', dept)
  })
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return
  chartInstance.setOption(chartOption.value)
}

// 响应式调整
const handleResize = () => {
  chartInstance?.resize()
}

// 监听变化
watch(() => props.data, updateChart, { deep: true })
watch(metricType, updateChart)

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  chartInstance?.dispose()
})
</script>

<style scoped>
.department-heatmap-chart {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.chart-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chart-container {
  position: relative;
  padding: 16px 0;
  transition: all 0.3s;
}

.chart-container.is-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;
  background: white;
  padding: 40px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
}

.echart-instance {
  width: 100%;
  height: 400px;
}

.is-fullscreen .echart-instance {
  height: calc(100vh - 120px);
}

/* 图例条 */
.legend-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-top: 20px;
}

.legend-label {
  font-size: 12px;
  color: #909399;
}

.legend-gradient {
  width: 200px;
  height: 10px;
  background: linear-gradient(to right, #e3f2fd, #90caf9, #42a5f5, #1e88e5, #1565c0, #0d47a1);
  border-radius: 5px;
}

/* 部门排行 */
.department-ranking {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #ebeef5;
}

.ranking-title {
  margin: 0 0 16px;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.ranking-item {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.ranking-item:hover {
  background-color: #f5f7fa;
}

.ranking-index {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.ranking-1 {
  background-color: #fef0f0;
  color: #f56c6c;
}

.ranking-2 {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.ranking-3 {
  background-color: #ecf5ff;
  color: #409eff;
}

.ranking-4,
.ranking-5 {
  background-color: #f5f7fa;
  color: #909399;
}

.department-name {
  flex: 0 0 120px;
  font-size: 14px;
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.department-bar {
  flex: 1;
  height: 16px;
  background-color: #f5f7fa;
  border-radius: 8px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.department-value {
  flex: 0 0 50px;
  text-align: right;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .echart-instance {
    height: 300px;
  }
  
  .department-name {
    flex: 0 0 80px;
  }
  
  .legend-gradient {
    width: 150px;
  }
}
</style>