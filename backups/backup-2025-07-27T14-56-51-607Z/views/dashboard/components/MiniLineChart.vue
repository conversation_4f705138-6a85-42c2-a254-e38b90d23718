<template>
  <div 
    ref="chartContainer" 
    class="mini-line-chart"
    :style="{ height: `${height}px` }"
  ></div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'MiniLine<PERSON><PERSON>'
})
 
import * as echarts from 'echarts/core'
import { Hr<PERSON>ine<PERSON>hart } from 'echarts/charts'
import { GridComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

// 注册必需的组件
echarts.use([LineChart, GridComponent, CanvasRenderer])

interface Props {
  data: number[]
  height?: number
  color?: string
  smooth?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  height: 40,
  color: '#409eff',
  smooth: true
})

const chartContainer = ref<HTMLDivElement>()
let chartInstance: echarts.ECharts | null = null

// 图表配置
const option = computed(() => ({
  grid: {
    top: 0,
    right: 0,
    bottom: 0,
    left: 0
  },
  xAxis: {
    type: 'category',
    show: false,
    data: Array.from({ length: props.data.length }, (_, i) => i)
  },
  yAxis: {
    type: 'value',
    show: false
  },
  series: [{
    type: 'line',
    data: props.data,
    smooth: props.smooth,
    symbol: 'none',
    lineStyle: {
      color: props.color,
      width: 2
    },
    areaStyle: {
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          { offset: 0, color: `${props.color}33` },
          { offset: 1, color: `${props.color}00` }
        ]
      }
    }
  }]
}))

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return
  
  chartInstance = echarts.init(chartContainer.value)
  chartInstance.setOption(option.value)
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return
  chartInstance.setOption(option.value)
}

// 响应式调整
const handleResize = () => {
  chartInstance?.resize()
}

// 监听数据变化
watch(() => props.data, updateChart, { deep: true })
watch(option, updateChart)

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  chartInstance?.dispose()
})
</script>

<style scoped>
.mini-line-chart {
  width: 100%;
}
</style>