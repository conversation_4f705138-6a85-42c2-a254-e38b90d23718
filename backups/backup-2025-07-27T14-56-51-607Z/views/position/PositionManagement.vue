<template>
  <div class="position-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>岗位管理</span>
          <el-button 
            type="primary" 
            @click="showCreateDialog"
            :icon="Plus"
          >
            新增岗位
          </el-button>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form 
        :model="queryForm" 
        :inline="true" 
        class="search-form"
        @submit.prevent="handleSearch"
      >
        <el-form-item label="关键词">
          <el-input
            v-model="queryForm.keyword"
            placeholder="岗位编码/名称"
            clearable
            style="width: 200px"
            />
        </el-form-item>
        
        <el-form-item label="所属组织">
          <OrganizationSelect
            v-model="queryForm.organizationId"
            placeholder="请选择组织"
            style="width: 200px"
            clearable
          />
        </el-form-item>
        
        <el-form-item label="岗位类型">
          <el-select
            v-model="queryForm.positionType"
            placeholder="请选择岗位类型"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="option in positionTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
             />
          </el-select>
        </el-form-item>
        
        <el-form-item label="岗位级别">
          <el-select
            v-model="queryForm.positionLevel"
            placeholder="请选择岗位级别"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="option in positionLevelOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
             />
          </el-select>
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select
            v-model="queryForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option
              v-for="option in positionStatusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
             />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-checkbox v-model="queryForm.onlyVacant">只显示空缺岗位</el-checkbox>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :icon="Search">搜索</el-button>
          <el-button @click="handleReset" :icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 岗位表格 -->
      <el-table
        v-loading="loading"
        :data="positionList"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column prop="positionCode" label="岗位编码" width="120"  />
        <el-table-column prop="positionName" label="岗位名称" width="150"  />
        <el-table-column prop="organizationName" label="所属组织" width="150"  />
        
        <el-table-column prop="positionType" label="岗位类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getPositionTypeTagType(row.positionType)">
              {{ getPositionTypeLabel(row.positionType) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="positionLevel" label="岗位级别" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.positionLevel" type="info">
              {{ getPositionLevelLabel(row.positionLevel) }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        
        <el-table-column label="人员配置" width="120">
          <template #default="{ row }">
            <div class="headcount-info">
              <span>{{ row.currentCount || 0 }}/{{ row.headcount || 0 }}</span>
              <el-tag 
                v-if="row.vacantCount > 0" 
                type="warning" 
                size="small"
                class="vacant-tag"
              >
                缺{{ row.vacantCount }}人
              </el-tag>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="薪资范围" width="150">
          <template #default="{ row }">
            <span v-if="row.minSalary || row.maxSalary">
              {{ formatSalary(row.minSalary) }} - {{ formatSalary(row.maxSalary) }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              text
              @click="showDetailDialog(row)"
              :icon="View"
            >
              详情
            </el-button>
            <el-button
              type="warning"
              size="small"
              text
              @click="showEditDialog(row)"
              :icon="Edit"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              text
              @click="handleDelete(row)"
              :icon="Delete"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        class="pagination"
        @size-change="handleSearch"
        @current-change="handleSearch"
       />
    </el-card>

    <!-- 创建/编辑岗位对话框 -->
    <PositionDialog
      v-model="dialogVisible"
      :position="currentPosition"
      :is-edit="isEdit"
      @success="handleDialogSuccess"
    />

    <!-- 岗位详情对话框 -->
    <PositionDetailDialog
      v-model="detailDialogVisible"
      :position="currentPosition"
    />
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'PositionManagement'
})
 
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh, View, Edit, Delete } from '@element-plus/icons-vue'
import { positionApi } from '@/api/position'
import type { Position, PositionQueryRequest } from '@/types/position'
import { 
  positionTypeOptions, 
  positionLevelOptions, 
  positionStatusOptions,
  PositionType,
  PositionLevel,
  PositionStatus
} from '@/types/position'
import HrOrganizationSelect from '@/components/organization/HrOrganizationSelect.vue'
import HrPositionDialog from '@/components/position/HrPositionDialog.vue'
import HrPositionDetailDialog from '@/components/position/HrPositionDetailDialog.vue'

const route = useRoute()

// 响应式数据
const loading = ref(false)
const positionList = ref<Position[]>([])
const dialogVisible = ref(false)
const detailDialogVisible = ref(false)
const currentPosition = ref<Position | null>(null)
const isEdit = ref(false)

// 查询表单
const queryForm = reactive<PositionQueryRequest>({
  keyword: '',
  organizationId: undefined,
  positionType: undefined,
  positionLevel: undefined,
  status: undefined,
  onlyVacant: false,
  page: 0,
  size: 20
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 加载岗位列表
const loadPositions = async () => {
  try {
    loading.value = true
    
    const params: PositionQueryRequest = {
      ...queryForm,
      page: pagination.page - 1, // 后端从0开始
      size: pagination.size
    }
    
    const response = await positionApi.query(params)
    positionList.value = response.content
    pagination.total = response.totalElements
  } catch (__error) {
    ElMessage.error('加载岗位列表失败')
    console.error('Load positions error:', error)
  } finally {
    loading.value = false
  }
}

// 处理搜索
const handleSearch = () => {
  pagination.page = 1
  loadPositions()
}

// 处理重置
const handleReset = () => {
  Object.assign(queryForm, {
    keyword: '',
    organizationId: undefined,
    positionType: undefined,
    positionLevel: undefined,
    status: undefined,
    onlyVacant: false
  })
  handleSearch()
}

// 显示创建对话框
const showCreateDialog = () => {
  currentPosition.value = null
  isEdit.value = false
  dialogVisible.value = true
}

// 显示编辑对话框
const showEditDialog = (position: Position) => {
  currentPosition.value = position
  isEdit.value = true
  dialogVisible.value = true
}

// 显示详情对话框
const showDetailDialog = (position: Position) => {
  currentPosition.value = position
  detailDialogVisible.value = true
}

// 处理删除
const handleDelete = async (position: Position) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除岗位 "${position.positionName}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await positionApi.delete(position.id)
    ElMessage.success('删除成功')
    loadPositions()
   
  } catch (error: unknown) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error('Delete position error:', error)
    }
  }
}

// 处理对话框成功
const handleDialogSuccess = () => {
  loadPositions()
}

// 获取岗位类型标签
const getPositionTypeLabel = (type: PositionType) => {
  return positionTypeOptions.find(option => option.value === type)?.label || type
}

// 获取岗位类型标签颜色
const getPositionTypeTagType = (type: PositionType) => {
  const typeMap: Record<PositionType, string> = {
    MANAGEMENT: 'danger',
    TECHNICAL: 'primary',
    TEACHING: 'success',
    RESEARCH: 'warning',
    ADMINISTRATIVE: 'info',
    SERVICE: '',
    OTHER: ''
  }
  return typeMap[type] || ''
}

// 获取岗位级别标签
const getPositionLevelLabel = (level: PositionLevel) => {
  return positionLevelOptions.find(option => option.value === level)?.label || level
}

// 获取状态标签
const getStatusLabel = (status: PositionStatus) => {
  return positionStatusOptions.find(option => option.value === status)?.label || status
}

// 获取状态标签颜色
const getStatusTagType = (status: PositionStatus) => {
  const statusMap: Record<PositionStatus, string> = {
    ACTIVE: 'success',
    INACTIVE: 'warning',
    CANCELLED: 'danger'
  }
  return statusMap[status] || ''
}

// 格式化薪资
const formatSalary = (salary?: number) => {
  if (!salary) return '-'
  return `¥${salary.toLocaleString()}`
}

// 组件挂载时加载数据
onMounted(() => {
  // 如果路由中有组织ID参数，设置到查询条件中
  if (route.query.organizationId) {
    queryForm.organizationId = Number(route.query.organizationId)
  }
  
  loadPositions()
})
</script>

<style scoped>
.position-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.headcount-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.vacant-tag {
  margin-top: 4px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
