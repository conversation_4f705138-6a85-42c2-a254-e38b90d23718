<template>
  <div class="ad-hoc-institution-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>临时机构管理</span>
          <div class="header-actions">
            <el-button 
              type="primary" 
              @click="showCreateDialog"
              :icon="Plus"
            >
              新增临时机构
            </el-button>
            <el-button 
              @click="showStatistics"
              :icon="DataAnalysis"
            >
              统计分析
            </el-button>
            <el-button 
              @click="checkExpiration"
              :icon="Clock"
              :loading="checkingExpiration"
            >
              检查到期
            </el-button>
            <el-button 
              @click="exportData"
              :icon="Download"
            >
              导出数据
            </el-button>
          </div>
        </div>
      </template>

      <!-- 快速统计卡片 -->
      <el-row :gutter="20" class="stats-cards">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ statistics.activeCount }}</div>
              <div class="stat-label">活跃机构</div>
            </div>
            <el-icon class="stat-icon active"><OfficeBuilding /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value expiring">{{ statistics.expiringSoonCount }}</div>
              <div class="stat-label">即将到期</div>
            </div>
            <el-icon class="stat-icon expiring"><Warning /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value expired">{{ statistics.expiredCount }}</div>
              <div class="stat-label">已到期</div>
            </div>
            <el-icon class="stat-icon expired"><Clock /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ statistics.totalCount }}</div>
              <div class="stat-label">总数</div>
            </div>
            <el-icon class="stat-icon"><DataAnalysis /></el-icon>
          </el-card>
        </el-col>
      </el-row>

      <!-- 搜索筛选区域 -->
      <el-form 
        :model="queryForm" 
        :inline="true" 
        class="search-form"
        label-width="80px"
      >
        <el-form-item label="关键词">
          <el-input
            v-model="queryForm.keyword"
            placeholder="搜索机构名称、编码、负责人"
            :prefix-icon="Search"
            clearable
            style="width: 250px"
            />
        </el-form-item>

        <el-form-item label="机构类型">
          <el-select 
            v-model="queryForm.institutionType" 
            placeholder="请选择机构类型"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="option in adHocInstitutionTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
             />
          </el-select>
        </el-form-item>

        <el-form-item label="状态">
          <el-select 
            v-model="queryForm.status" 
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option
              v-for="option in adHocInstitutionStatusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
             />
          </el-select>
        </el-form-item>

        <el-form-item label="优先级">
          <el-select 
            v-model="queryForm.priority" 
            placeholder="请选择优先级"
            clearable
            style="width: 120px"
          >
            <el-option
              v-for="option in priorityOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
             />
          </el-select>
        </el-form-item>

        <el-form-item label="建立日期">
          <el-date-picker
            v-model="establishDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
           />
        </el-form-item>

        <el-form-item label="计划结束">
          <el-date-picker
            v-model="plannedEndDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
           />
        </el-form-item>

        <el-form-item>
          <el-checkbox v-model="queryForm.isUrgent">仅显示紧急</el-checkbox>
        </el-form-item>

        <el-form-item>
          <el-checkbox v-model="queryForm.includeExpired">包含已到期</el-checkbox>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch" :icon="Search">搜索</el-button>
          <el-button @click="handleReset" :icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 批量操作栏 -->
      <div v-if="selectedRows.length > 0" class="batch-actions">
        <el-alert
          :title="`已选择 ${selectedRows.length} 个临时机构`"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <el-space>
              <el-button size="small" @click="batchExtend">批量延期</el-button>
              <el-button size="small" @click="batchDissolve" type="danger">批量撤销</el-button>
              <el-button size="small" @click="batchNotify">批量通知</el-button>
              <el-button size="small" @click="clearSelection">取消选择</el-button>
            </el-space>
          </template>
        </el-alert>
      </div>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="institutionList"
        stripe
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        
        <el-table-column prop="institutionCode" label="机构编码" width="140" fixed="left">
          <template #default="{ row }">
            <el-link type="primary" @click="showDetail(row)">
              {{ row.institutionCode }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column prop="institutionName" label="机构名称" width="200" show-overflow-tooltip  />

        <el-table-column prop="institutionType" label="类型" width="120">
          <template #default="{ row }">
            <el-tag :type="(typeColors as unknown)[row.institutionType] || ''">
              {{ getTypeLabel(row.institutionType) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="(statusColors as unknown)[row.status] || ''">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="responsiblePersonName" label="负责人" width="100"  />

        <el-table-column prop="memberCount" label="成员数" width="80" align="center"  />

        <el-table-column prop="priority" label="优先级" width="80">
          <template #default="{ row }">
            <el-tag 
              :type="(priorityColors as unknown)[row.priority] || ''"
              size="small"
              :effect="row.isUrgent ? 'dark' : 'light'"
            >
              {{ getPriorityLabel(row.priority) }}
              <el-icon v-if="row.isUrgent" style="margin-left: 4px;"><Warning /></el-icon>
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="establishDate" label="建立日期" width="110">
          <template #default="{ row }">
            {{ formatDate(row.establishDate) }}
          </template>
        </el-table-column>

        <el-table-column prop="plannedEndDate" label="计划结束" width="110">
          <template #default="{ row }">
            <span :class="getEndDateClass(row)">
              {{ formatDate(row.plannedEndDate) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="剩余天数" width="90" align="center">
          <template #default="{ row }">
            <span :class="getRemainingDaysClass(row)">
              {{ getRemainingDays(row.plannedEndDate) }}天
            </span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              text
              @click="showDetail(row)"
              :icon="View"
            >
              详情
            </el-button>
            
            <el-button
              v-if="canEdit(row)"
              type="warning"
              size="small"
              text
              @click="showEditDialog(row)"
              :icon="Edit"
            >
              编辑
            </el-button>

            <el-button
              v-if="canExtend(row)"
              type="success"
              size="small"
              text
              @click="showExtensionDialog(row)"
              :icon="Clock"
            >
              延期
            </el-button>

            <el-button
              v-if="canHandleExpiration(row)"
              type="warning"
              size="small"
              text
              @click="showExpirationDialog(row)"
              :icon="Setting"
            >
              处理
            </el-button>

            <el-button
              v-if="canDelete(row)"
              type="danger"
              size="small"
              text
              @click="handleDelete(row)"
              :icon="Delete"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        class="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
    </el-card>

    <!-- 对话框组件 -->
    <AdHocInstitutionDialog
      v-model="dialogVisible"
      :institution="currentInstitution"
      :is-edit="isEdit"
      @success="handleDialogSuccess"
    />

    <AdHocInstitutionDetail
      v-model="detailDialogVisible"
      :institution="currentInstitution"
    />

    <ExtensionDialog
      v-model="extensionDialogVisible"
      :institution="currentInstitution"
      @success="handleExtensionSuccess"
    />

    <ExpirationHandlingDialog
      v-model="expirationDialogVisible"
      :institution="currentInstitution"
      @success="handleExpirationSuccess"
    />

    <StatisticsDialog
      v-model="statisticsDialogVisible"
    />

    <BatchExtensionDialog
      v-model="batchExtensionDialogVisible"
      :institutions="selectedRows"
      @success="handleBatchExtensionSuccess"
    />

    <BatchNotificationDialog
      v-model="batchNotificationDialogVisible"
      :institutions="selectedRows"
      @success="handleBatchNotificationSuccess"
    />
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'AdHocInstitutionManagement'
})
 
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Search, Refresh, View, Edit, Delete, Clock, Warning,
  DataAnalysis, Download, OfficeBuilding, Setting
} from '@element-plus/icons-vue'
import { adHocInstitutionApi } from '@/api/adHocInstitution'
import type {
  AdHocInstitution,
  AdHocInstitutionQueryRequest,
  AdHocInstitutionType,
  Priority,
  AdHocInstitutionStatistics
} from '@/types/adHocInstitution'
import {
  AdHocInstitutionStatus
} from '@/types/adHocInstitution'
import {
  adHocInstitutionTypeOptions,
  adHocInstitutionStatusOptions,
  priorityOptions,
  statusColors,
  typeColors,
  priorityColors
} from '@/types/adHocInstitution'
import HrAdHocInstitutionDialog from '@/components/adHocInstitution/HrAdHocInstitutionDialog.vue'
import HrAdHocInstitutionDetail from '@/components/adHocInstitution/HrAdHocInstitutionDetail.vue'
import HrExtensionDialog from '@/components/adHocInstitution/HrExtensionDialog.vue'
import HrExpirationHandlingDialog from '@/components/adHocInstitution/HrExpirationHandlingDialog.vue'
import HrStatisticsDialog from '@/components/adHocInstitution/HrStatisticsDialog.vue'
import HrBatchExtensionDialog from '@/components/adHocInstitution/HrBatchExtensionDialog.vue'
import HrBatchNotificationDialog from '@/components/adHocInstitution/HrBatchNotificationDialog.vue'

// 响应式数据
const loading = ref(false)
const checkingExpiration = ref(false)
const institutionList = ref<AdHocInstitution[]>([])
const selectedRows = ref<AdHocInstitution[]>([])
const dialogVisible = ref(false)
const detailDialogVisible = ref(false)
const extensionDialogVisible = ref(false)
const expirationDialogVisible = ref(false)
const statisticsDialogVisible = ref(false)
const batchExtensionDialogVisible = ref(false)
const batchNotificationDialogVisible = ref(false)
const currentInstitution = ref<AdHocInstitution | null>(null)
const isEdit = ref(false)

// 统计数据
const statistics = reactive<AdHocInstitutionStatistics>({
  totalCount: 0,
  activeCount: 0,
  expiringSoonCount: 0,
  expiredCount: 0,
  dissolvedCount: 0,
  convertedCount: 0,
  thisMonthCreated: 0,
  thisMonthExpired: 0,
  averageDuration: 0,
  totalBudget: 0
})

// 查询表单
const queryForm = reactive<AdHocInstitutionQueryRequest>({
  keyword: '',
  institutionType: undefined,
  status: undefined,
  priority: undefined,
  isUrgent: false,
  includeExpired: false,
  page: 0,
  size: 20
})

// 日期范围
const establishDateRange = ref<[string, string] | null>(null)
const plannedEndDateRange = ref<[string, string] | null>(null)

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 加载临时机构列表
const loadInstitutionList = async () => {
  try {
    loading.value = true

    const params: AdHocInstitutionQueryRequest = {
      ...queryForm,
      page: pagination.page - 1, // 后端从0开始
      size: pagination.size
    }

    // 处理日期范围
    if (establishDateRange.value) {
      params.establishDateStart = establishDateRange.value[0]
      params.establishDateEnd = establishDateRange.value[1]
    }

    if (plannedEndDateRange.value) {
      params.plannedEndDateStart = plannedEndDateRange.value[0]
      params.plannedEndDateEnd = plannedEndDateRange.value[1]
    }

    const response = await adHocInstitutionApi.query(params)
    institutionList.value = response.content
    pagination.total = response.totalElements
  } catch (__error) {
    ElMessage.error('加载临时机构列表失败')
    console.error('Load institution list error:', error)
  } finally {
    loading.value = false
  }
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    const data = await adHocInstitutionApi.getStatistics()
    Object.assign(statistics, data)
  } catch (__error) {
    console.error('Load statistics error:', error)
  }
}

// 处理搜索
const handleSearch = () => {
  pagination.page = 1
  loadInstitutionList()
}

// 处理重置
const handleReset = () => {
  Object.assign(queryForm, {
    keyword: '',
    institutionType: undefined,
    status: undefined,
    priority: undefined,
    isUrgent: false,
    includeExpired: false
  })
  establishDateRange.value = null
  plannedEndDateRange.value = null
  handleSearch()
}

// 处理选择变化
const handleSelectionChange = (selection: AdHocInstitution[]) => {
  selectedRows.value = selection
}

// 清除选择
const clearSelection = () => {
  selectedRows.value = []
}

// 处理分页大小变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  loadInstitutionList()
}

// 处理当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadInstitutionList()
}

// 显示创建对话框
const showCreateDialog = () => {
  currentInstitution.value = null
  isEdit.value = false
  dialogVisible.value = true
}

// 显示编辑对话框
const showEditDialog = (institution: AdHocInstitution) => {
  currentInstitution.value = institution
  isEdit.value = true
  dialogVisible.value = true
}

// 显示详情对话框
const showDetail = (institution: AdHocInstitution) => {
  currentInstitution.value = institution
  detailDialogVisible.value = true
}

// 显示延期对话框
const showExtensionDialog = (institution: AdHocInstitution) => {
  currentInstitution.value = institution
  extensionDialogVisible.value = true
}

// 显示到期处理对话框
const showExpirationDialog = (institution: AdHocInstitution) => {
  currentInstitution.value = institution
  expirationDialogVisible.value = true
}

// 显示统计分析
const showStatistics = () => {
  statisticsDialogVisible.value = true
}

// 检查到期状态
const checkExpiration = async () => {
  try {
    checkingExpiration.value = true
    await adHocInstitutionApi.checkExpirationStatus()
    ElMessage.success('到期状态检查完成')
    loadInstitutionList()
    loadStatistics()
  } catch (__error) {
    ElMessage.error('检查到期状态失败')
    console.error('Check expiration error:', error)
  } finally {
    checkingExpiration.value = false
  }
}

// 处理对话框成功
const handleDialogSuccess = () => {
  loadInstitutionList()
  loadStatistics()
}

// 处理延期成功
const handleExtensionSuccess = () => {
  loadInstitutionList()
  loadStatistics()
}

// 处理到期处理成功
const handleExpirationSuccess = () => {
  loadInstitutionList()
  loadStatistics()
}

// 处理批量延期成功
const handleBatchExtensionSuccess = () => {
  clearSelection()
  loadInstitutionList()
  loadStatistics()
}

// 处理批量通知成功
const handleBatchNotificationSuccess = () => {
  clearSelection()
  loadInstitutionList()
}

// 处理删除
const handleDelete = async (institution: AdHocInstitution) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除临时机构 "${institution.institutionName}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await adHocInstitutionApi.delete(institution.id)
    ElMessage.success('删除成功')
    loadInstitutionList()
    loadStatistics()
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error('Delete institution error:', error)
    }
  }
}

// 批量延期
const batchExtend = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要延期的临时机构')
    return
  }
  batchExtensionDialogVisible.value = true
}

// 批量撤销
const batchDissolve = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要撤销选中的 ${selectedRows.value.length} 个临时机构吗？`,
      '确认批量撤销',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const institutionIds = selectedRows.value.map(item => item.id)
    await adHocInstitutionApi.batchDissolve(institutionIds, '批量撤销操作')
    ElMessage.success('批量撤销成功')
    clearSelection()
    loadInstitutionList()
    loadStatistics()
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('批量撤销失败')
      console.error('Batch dissolve error:', error)
    }
  }
}

// 批量通知
const batchNotify = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要通知的临时机构')
    return
  }
  batchNotificationDialogVisible.value = true
}

// 导出数据
const exportData = async () => {
  try {
    const params = { ...queryForm }
    if (establishDateRange.value) {
      params.establishDateStart = establishDateRange.value[0]
      params.establishDateEnd = establishDateRange.value[1]
    }
    if (plannedEndDateRange.value) {
      params.plannedEndDateStart = plannedEndDateRange.value[0]
      params.plannedEndDateEnd = plannedEndDateRange.value[1]
    }

    const blob = await adHocInstitutionApi.exportData(params)
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `临时机构数据_${new Date().toISOString().slice(0, 10)}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (__error) {
    ElMessage.error('导出失败')
    console.error('Export data error:', error)
  }
}

// 权限判断
const canEdit = (institution: AdHocInstitution) => {
  return institution.status === AdHocInstitutionStatus.DRAFT ||
         institution.status === AdHocInstitutionStatus.ACTIVE
}

const canExtend = (institution: AdHocInstitution) => {
  return institution.status === AdHocInstitutionStatus.ACTIVE ||
         institution.status === AdHocInstitutionStatus.EXPIRING_SOON
}

const canHandleExpiration = (institution: AdHocInstitution) => {
  return institution.status === AdHocInstitutionStatus.EXPIRING_SOON ||
         institution.status === AdHocInstitutionStatus.EXPIRED
}

const canDelete = (institution: AdHocInstitution) => {
  return institution.status === AdHocInstitutionStatus.DRAFT
}

// 获取标签类型和文本
const getTypeLabel = (type: AdHocInstitutionType) => {
  const option = adHocInstitutionTypeOptions.find(opt => opt.value === type)
  return option?.label || type
}

const getStatusLabel = (status: AdHocInstitutionStatus) => {
  const option = adHocInstitutionStatusOptions.find(opt => opt.value === status)
  return option?.label || status
}

const getPriorityLabel = (priority: Priority) => {
  const option = priorityOptions.find(opt => opt.value === priority)
  return option?.label || priority
}

// 获取结束日期样式
const getEndDateClass = (institution: AdHocInstitution) => {
  const remainingDays = getRemainingDays(institution.plannedEndDate)
  if (remainingDays < 0) return 'expired-date'
  if (remainingDays <= 7) return 'warning-date'
  if (remainingDays <= 30) return 'caution-date'
  return ''
}

// 获取剩余天数样式
const getRemainingDaysClass = (institution: AdHocInstitution) => {
  const remainingDays = getRemainingDays(institution.plannedEndDate)
  if (remainingDays < 0) return 'expired-days'
  if (remainingDays <= 7) return 'warning-days'
  if (remainingDays <= 30) return 'caution-days'
  return 'normal-days'
}

// 计算剩余天数
const getRemainingDays = (endDate: string) => {
  const end = new Date(endDate)
  const now = new Date()
  const diffTime = end.getTime() - now.getTime()
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return '-'
  return new Date(date).toLocaleDateString('zh-CN')
}

// 组件挂载时加载数据
onMounted(() => {
  loadInstitutionList()
  loadStatistics()
})
</script>

<style scoped>
.ad-hoc-institution-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-content {
  position: relative;
  z-index: 2;
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.stat-value.expiring {
  color: #e6a23c;
}

.stat-value.expired {
  color: #f56c6c;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.stat-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 48px;
  opacity: 0.1;
  z-index: 1;
}

.stat-icon.active {
  color: #67c23a;
}

.stat-icon.expiring {
  color: #e6a23c;
}

.stat-icon.expired {
  color: #f56c6c;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.batch-actions {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

/* 日期状态样式 */
.expired-date {
  color: #f56c6c;
  font-weight: bold;
}

.warning-date {
  color: #e6a23c;
  font-weight: bold;
}

.caution-date {
  color: #409eff;
}

/* 剩余天数样式 */
.expired-days {
  color: #f56c6c;
  font-weight: bold;
}

.warning-days {
  color: #e6a23c;
  font-weight: bold;
}

.caution-days {
  color: #409eff;
}

.normal-days {
  color: #67c23a;
}
</style>
