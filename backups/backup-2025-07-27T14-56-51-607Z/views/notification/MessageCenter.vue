<template>
  <div class="modern-message-center" :class="{ 'mobile-layout': isMobile }">
    <!-- 现代化页面头部 -->
    <div class="page-header" role="banner">
      <div class="header-content">
        <div class="header-title">
          <h1>
            <el-icon class="title-icon"><Message /></el-icon>
            消息中心
          </h1>
          <p class="subtitle">智能化消息管理与通知平台</p>
        </div>

        <!-- 快速操作按钮 -->
        <div class="header-actions" v-if="!isMobile">
          <el-button type="primary" size="large" @click="showSendDialog = true">
            <el-icon><Plus /></el-icon>
            发送消息
          </el-button>

          <el-dropdown trigger="click" placement="bottom-end">
            <el-button size="large">
              <el-icon><MoreFilled /></el-icon>
              更多操作
            </el-button>

            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="markAllAsRead" :disabled="!hasUnreadMessages">
                  <el-icon><Check /></el-icon>
                  全部已读
                </el-dropdown-item>
                <el-dropdown-item @click="deleteSelected" :disabled="!hasSelection">
                  <el-icon><Delete /></el-icon>
                  删除选中
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>

        <!-- 移动端操作按钮 -->
        <div class="mobile-actions" v-if="isMobile">
          <el-button type="primary" circle @click="showSendDialog = true">
            <el-icon><Plus /></el-icon>
          </el-button>
          <el-button circle @click="showMobileMenu = true">
            <el-icon><MoreFilled /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card total-messages">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><ChatDotRound /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ messageStats.total || 0 }}</div>
                <div class="stat-label">总消息数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card unread-messages">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Bell /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ messageStats.unread || 0 }}</div>
                <div class="stat-label">未读消息</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card today-messages">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Calendar /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ messageStats.today || 0 }}</div>
                <div class="stat-label">今日消息</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card success-rate">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ (messageStats.successRate || 0).toFixed(1) }}%</div>
                <div class="stat-label">发送成功率</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <el-card class="main-content">
      <!-- 搜索和过滤 -->
      <div class="search-section">
        <el-form :model="searchForm" inline>
          <el-form-item label="消息类型">
            <el-select v-model="searchForm.channelType" placeholder="请选择类型" clearable>
              <el-option label="站内信" value="INTERNAL"  />
              <el-option label="邮件" value="EMAIL"  />
              <el-option label="短信" value="SMS"  />
              <el-option label="微信" value="WECHAT"  />
              <el-option label="钉钉" value="DINGTALK"  />
            </el-select>
          </el-form-item>
          <el-form-item label="消息状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="待发送" value="PENDING"  />
              <el-option label="发送中" value="SENDING"  />
              <el-option label="发送成功" value="SUCCESS"  />
              <el-option label="发送失败" value="FAILED"  />
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="handleDateRangeChange"
             />
          </el-form-item>
          <el-form-item label="关键词">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索标题或内容"
              clearable
              @keyup.enter="handleSearch"
              />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 消息列表 -->
      <el-table
        v-loading="loading"
        :data="messages"
        @selection-change="handleSelectionChange"
        stripe
        border
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column label="消息" min-width="300">
          <template #default="{ row }">
            <div class="message-content" :class="{ 'unread': !row.readTime }">
              <div class="message-header">
                <span class="message-title">{{ row.title || '无标题' }}</span>
                <el-tag :type="getChannelTypeColor(row.channelType)" size="small">
                  {{ getChannelTypeName(row.channelType) }}
                </el-tag>
              </div>
              <div class="message-body">
                {{ truncateText(row.content, 100) }}
              </div>
              <div class="message-meta">
                <span class="recipient">收件人: {{ row.recipient }}</span>
                <span class="send-time">{{ formatDateTime(row.sendTime || row.createTime) }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="优先级" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getPriorityColor(row.priority)" size="small">
              {{ getPriorityName(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)" size="small">
              {{ getStatusName(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="发送时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.sendTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-button size="small" @click="viewMessage(row)">
                <el-icon><View /></el-icon>
                查看
              </el-button>
              <el-button 
                size="small" 
                @click="markAsRead(row)" 
                v-if="!row.readTime && row.channelType === 'INTERNAL'"
              >
                <el-icon><Check /></el-icon>
                已读
              </el-button>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
              <el-dropdown @command="(command: unknown) => handleMoreAction(command, row)">
                <el-button size="small">
                  更多<el-icon><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="resend" v-if="row.status === 'FAILED'">重新发送</el-dropdown-item>
                    <el-dropdown-item command="copy">复制内容</el-dropdown-item>
                    <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 发送消息对话框 -->
    <MessageSendDialog
      v-model="showSendDialog"
      @success="handleSendSuccess"
    />

    <!-- 消息详情对话框 -->
    <MessageDetailDialog
      v-model="showDetailDialog"
      :message="selectedMessage"
    />
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'MessageCenter'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Message, Plus, Check, Delete, ChatDotRound, Bell, Calendar,
  TrendCharts, Search, Refresh, View, ArrowDown, MoreFilled
} from '@element-plus/icons-vue'
import { useMobile } from '@/composables/useMobile'
import { notificationApi } from '@/api/notification'
import type { NotificationMessage, NotificationQueryRequest } from '@/types/notification'
import MessageSendDialog from './components/MessageSendDialog.vue'
import MessageDetailDialog from './components/MessageDetailDialog.vue'
import { formatDateTime, truncateText } from '@/utils/format'

// 移动端适配
const {isMobile: _isMobile} =  useMobile()

// UI状态
const showMobileMenu 
  background-color: #f5f7fa;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .header-content {
      .page-title {
        display: flex;
        align-items: center;
        font-size: 24px;
        font-weight: 600;
        color: #303133;
        margin: 0 0 8px 0;

        .el-icon {
          margin-right: 8px;
          color: #409eff;
        }
      }

      .page-description {
        color: #909399;
        margin: 0;
        font-size: 14px;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }
  }

  .stats-cards {
    margin-bottom: 20px;

    .stat-card {
      border: none;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      transition: transform 0.2s ease;

      &:hover {
        transform: translateY(-2px);
      }

      :deep(.el-card__body) {
        padding: 20px;
      }

      .stat-content {
        display: flex;
        align-items: center;

        .stat-icon {
          width: 48px;
          height: 48px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;

          .el-icon {
            font-size: 24px;
            color: white;
          }
        }

        .stat-info {
          .stat-value {
            font-size: 28px;
            font-weight: 600;
            color: #303133;
            line-height: 1;
            margin-bottom: 4px;
          }

          .stat-label {
            font-size: 14px;
            color: #909399;
          }
        }
      }

      &.total-messages .stat-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }

      &.unread-messages .stat-icon {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }

      &.today-messages .stat-icon {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }

      &.success-rate .stat-icon {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      }
    }
  }

  .main-content {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .search-section {
      padding: 20px;
      border-bottom: 1px solid #ebeef5;
      background-color: #fafafa;

      .el-form {
        margin-bottom: 0;
      }
    }

    .message-content {
      padding: 8px 0;

      &.unread {
        .message-title {
          font-weight: 600;
          color: #303133;
        }
      }

      .message-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .message-title {
          font-size: 14px;
          color: #606266;
          flex: 1;
          margin-right: 12px;
        }
      }

      .message-body {
        font-size: 13px;
        color: #909399;
        line-height: 1.4;
        margin-bottom: 8px;
      }

      .message-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        color: #c0c4cc;

        .recipient {
          flex: 1;
        }

        .send-time {
          white-space: nowrap;
        }
      }
    }

    .pagination-wrapper {
      padding: 20px;
      display: flex;
      justify-content: center;
      border-top: 1px solid #ebeef5;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .message-center {
    padding: 10px;

    .page-header {
      flex-direction: column;
      gap: 16px;

      .header-actions {
        width: 100%;
        justify-content: flex-start;
        flex-wrap: wrap;
      }
    }

    .stats-cards {
      .el-col {
        margin-bottom: 16px;
      }
    }

    .search-section {
      .el-form {
        .el-form-item {
          width: 100%;
          margin-bottom: 16px;

          .el-input,
          .el-select,
          .el-date-picker {
            width: 100%;
          }
        }
      }
    }

    .message-content {
      .message-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }

      .message-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
      }
    }
  }
}
</style>
