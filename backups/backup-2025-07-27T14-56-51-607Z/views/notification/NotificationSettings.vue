<template>
  <div class="notification-settings">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><Setting /></el-icon>
          通知设置
        </h1>
        <p class="page-description">管理通知渠道配置和用户订阅设置</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="saveAllSettings" :loading="saving">
          <el-icon><Check /></el-icon>
          保存设置
        </el-button>
        <el-button @click="resetSettings">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
      </div>
    </div>

    <el-row :gutter="20">
      <!-- 渠道配置 -->
      <el-col :span="12">
        <el-card class="settings-card">
          <template #header>
            <div class="card-header">
              <span>通知渠道配置</span>
              <el-button size="small" @click="testAllChannels">
                <el-icon><Connection /></el-icon>
                测试连接
              </el-button>
            </div>
          </template>

          <div class="channel-settings">
            <div
              v-for="channel in channelSettings"
              :key="channel.type"
              class="channel-item"
            >
              <div class="channel-header">
                <div class="channel-info">
                  <el-icon class="channel-icon" :class="channel.iconClass">
                    <component :is="channel.icon" />
                  </el-icon>
                  <span class="channel-name">{{ channel.name }}</span>
                  <el-tag
                    :type="channel.status === 'UP' ? 'success' : 'danger'"
                    size="small"
                  >
                    {{ channel.status === 'UP' ? '正常' : '异常' }}
                  </el-tag>
                </div>
                <el-switch
                  v-model="channel.enabled"
                  @change="handleChannelToggle(channel)"
                 />
              </div>

              <div v-if="channel.enabled" class="channel-config">
                <!-- 邮件配置 -->
                <div v-if="channel.type === 'EMAIL'" class="config-section">
                  <el-form :model="channel.config" label-width="100px" size="small">
                    <el-form-item label="SMTP服务器">
                      <el-input v-model="channel.config.host" placeholder="smtp.example.com"   />
                    </el-form-item>
                    <el-form-item label="端口">
                      <el-input-number v-model="channel.config.port" :min="1" :max="65535"   />
                    </el-form-item>
                    <el-form-item label="用户名">
                      <el-input v-model="channel.config.username"   />
                    </el-form-item>
                    <el-form-item label="密码">
                      <el-input v-model="channel.config.password" type="password" show-password   />
                    </el-form-item>
                    <el-form-item label="SSL加密">
                      <el-switch v-model="channel.config.ssl"  />
                    </el-form-item>
                  </el-form>
                </div>

                <!-- 短信配置 -->
                <div v-if="channel.type === 'SMS'" class="config-section">
                  <el-form :model="channel.config" label-width="100px" size="small">
                    <el-form-item label="服务商">
                      <el-select v-model="channel.config.provider">
                        <el-option label="阿里云" value="aliyun"  />
                        <el-option label="腾讯云" value="tencent"  />
                        <el-option label="华为云" value="huawei"  />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="AccessKey">
                      <el-input v-model="channel.config.accessKey"   />
                    </el-form-item>
                    <el-form-item label="SecretKey">
                      <el-input v-model="channel.config.secretKey" type="password" show-password   />
                    </el-form-item>
                    <el-form-item label="签名">
                      <el-input v-model="channel.config.signName"   />
                    </el-form-item>
                    <el-form-item label="模板ID">
                      <el-input v-model="channel.config.templateCode"   />
                    </el-form-item>
                  </el-form>
                </div>

                <!-- 钉钉配置 -->
                <div v-if="channel.type === 'DINGTALK'" class="config-section">
                  <el-form :model="channel.config" label-width="100px" size="small">
                    <el-form-item label="应用类型">
                      <el-radio-group v-model="channel.config.type">
                        <el-radio label="robot">机器人</el-radio>
                        <el-radio label="work">工作通知</el-radio>
                      </el-radio-group>
                    </el-form-item>
                    <div v-if="channel.config.type === 'robot'">
                      <el-form-item label="Webhook">
                        <el-input v-model="channel.config.webhookUrl"   />
                      </el-form-item>
                      <el-form-item label="密钥">
                        <el-input v-model="channel.config.secret" type="password" show-password   />
                      </el-form-item>
                    </div>
                    <div v-if="channel.config.type === 'work'">
                      <el-form-item label="AppKey">
                        <el-input v-model="channel.config.appKey"   />
                      </el-form-item>
                      <el-form-item label="AppSecret">
                        <el-input v-model="channel.config.appSecret" type="password" show-password   />
                      </el-form-item>
                      <el-form-item label="AgentId">
                        <el-input v-model="channel.config.agentId"   />
                      </el-form-item>
                    </div>
                  </el-form>
                </div>

                <div class="channel-actions">
                  <el-button size="small" @click="testChannel(channel)">
                    <el-icon><Connection /></el-icon>
                    测试连接
                  </el-button>
                  <el-button size="small" @click="saveChannelConfig(channel)">
                    <el-icon><Check /></el-icon>
                    保存配置
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 订阅设置 -->
      <el-col :span="12">
        <el-card class="settings-card">
          <template #header>
            <div class="card-header">
              <span>订阅设置</span>
              <el-button size="small" @click="loadUserSubscriptions">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>

          <div class="subscription-settings">
            <div class="user-selector">
              <el-select
                v-model="selectedUserId"
                placeholder="选择用户"
                filterable
                @change="handleUserChange"
                style="width: 100%"
              >
                <el-option
                  v-for="user in users"
                  :key="user.id"
                  :label="user.name"
                  :value="user.id"
                 />
              </el-select>
            </div>

            <div v-if="selectedUserId" class="subscription-list">
              <div
                v-for="category in subscriptionCategories"
                :key="category.code"
                class="subscription-item"
              >
                <div class="subscription-header">
                  <div class="category-info">
                    <span class="category-name">{{ category.name }}</span>
                    <el-text size="small" type="info">{{ category.description }}</el-text>
                  </div>
                  <el-switch
                    :model-value="userSubscriptions[category.code]?.enabled || false"
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    @update:model-value="(value: unknown) => handleSubscriptionToggle(category.code, value)"
                  />
                </div>

                <div
                  v-if="userSubscriptions[category.code]?.enabled"
                  class="subscription-channels"
                >
                  <div class="channels-label">通知渠道:</div>
                  <el-checkbox-group
                    v-model="userSubscriptions[category.code].channelTypes"
                    @change="handleChannelTypesChange(category.code)"
                  >
                    <el-checkbox
                      v-for="channel in category.defaultChannels"
                      :key="channel"
                      :label="channel"
                      :disabled="!isChannelAvailable(channel)"
                    >
                      {{ getChannelTypeName(channel) }}
                    </el-checkbox>
                  </el-checkbox-group>
                </div>

                <div
                  v-if="userSubscriptions[category.code]?.enabled"
                  class="subscription-settings-detail"
                >
                  <el-form size="small" label-width="80px">
                    <el-form-item label="频率">
                      <el-select
                        v-model="userSubscriptions[category.code].settings.frequency"
                        size="small"
                      >
                        <el-option label="立即" value="IMMEDIATE"  />
                        <el-option label="每日汇总" value="DAILY"  />
                        <el-option label="每周汇总" value="WEEKLY"  />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="免打扰">
                      <el-time-picker
                        v-model="quietHours"
                        is-range
                        range-separator="至"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        format="HH:mm"
                        value-format="HH:mm"
                        @change="handleQuietHoursChange(category.code)"
                       />
                    </el-form-item>
                  </el-form>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统设置 -->
    <el-card class="settings-card system-settings">
      <template #header>
        <span>系统设置</span>
      </template>

      <el-form :model="systemSettings" label-width="150px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="全局开关">
              <el-switch v-model="systemSettings.globalEnabled"  />
            </el-form-item>
            <el-form-item label="默认优先级">
              <el-select v-model="systemSettings.defaultPriority">
                <el-option label="低" :value="10"  />
                <el-option label="普通" :value="50"  />
                <el-option label="高" :value="80"  />
              </el-select>
            </el-form-item>
            <el-form-item label="最大重试次数">
              <el-input-number v-model="systemSettings.maxRetries" :min="0" :max="10"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="批量发送大小">
              <el-input-number v-model="systemSettings.batchSize" :min="1" :max="1000"   />
            </el-form-item>
            <el-form-item label="队列大小">
              <el-input-number v-model="systemSettings.queueSize" :min="100" :max="10000"   />
            </el-form-item>
            <el-form-item label="加密传输">
              <el-switch v-model="systemSettings.encryptionEnabled"  />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Setting, Check, Refresh, Connection, Message, ChatDotRound,
  Phone, Promotion
} from '@element-plus/icons-vue'
import { notificationApi } from '@/api/notification'

// 响应式数据
const saving = ref(false)
const selectedUserId = ref('')
const quietHours = ref<[string, string] | null>(null)

// 渠道设置
const channelSettings = ref([
  {
    type: 'EMAIL',
    name: 'HrHr邮件通知',
    icon: Message,
    iconClass: 'email-icon',
    enabled: true,
    status: 'UP',
    config: {
      host: '',
      port: 587,
      username: '',
      password: '',
      ssl: true
    }
  },
  {
    type: 'SMS',
    name: '短信通知',
    icon: Phone,
    iconClass: 'sms-icon',
    enabled: true,
    status: 'UP',
    config: {
      provider: 'aliyun',
      accessKey: '',
      secretKey: '',
      signName: '',
      templateCode: ''
    }
  },
  {
    type: 'DINGTALK',
    name: '钉钉通知',
    icon: ChatDotRound,
    iconClass: 'dingtalk-icon',
    enabled: true,
    status: 'UP',
    config: {
      type: 'robot',
      webhookUrl: '',
      secret: '',
      appKey: '',
      appSecret: '',
      agentId: ''
    }
  }
])

// 用户列表
const users = ref([
  { id: 'user1', name: '张三' },
  { id: 'user2', name: '李四' },
  { id: 'user3', name: '王五' }
])

// 订阅分类
const subscriptionCategories = ref([
  {
    code: 'WORKFLOW',
    name: '工作流通知',
    description: '流程审批、任务分配等通知',
    defaultChannels: ['INTERNAL', 'EMAIL'],
    required: true
  },
  {
    code: 'SYSTEM',
    name: '系统通知',
    description: '系统维护、安全提醒等通知',
    defaultChannels: ['INTERNAL', 'SMS'],
    required: true
  },
  {
    code: 'BUSINESS',
    name: '业务通知',
    description: '业务相关的提醒和通知',
    defaultChannels: ['INTERNAL', 'EMAIL', 'DINGTALK'],
    required: false
  }
])

// 用户订阅设置
const userSubscriptions = ref<Record<string, any>>({})

// 系统设置
const systemSettings = reactive({
  globalEnabled: true,
  defaultPriority: 50,
  maxRetries: 3,
  batchSize: 100,
  queueSize: 1000,
  encryptionEnabled: true
})

// 生命周期
onMounted(() => {
  loadChannelStatus()
  loadSystemSettings()
})

// 方法
const loadChannelStatus = async () => {
  try {
    const channels = await notificationApi.getChannelStatus()
    channelSettings.value.forEach(setting => {
      const status = channels.find(c => c.channelType === setting.type)
      if (status) {
        setting.enabled = status.enabled
        setting.status = status.status
        setting.config = { ...setting.config, ...status.configuration } as unknown
      }
    })
  } catch (__error) {
    console.error('加载渠道状态失败:', error)
  }
}

const loadSystemSettings = async () => {
  try {
    const config = await notificationApi.getSystemConfig()
    Object.assign(systemSettings, config.globalSettings)
  } catch (__error) {
    console.error('加载系统设置失败:', error)
  }
}

const loadUserSubscriptions = async () => {
  if (!selectedUserId.value) return

  try {
    const subscriptions = await notificationApi.getUserSubscriptions(selectedUserId.value)
    userSubscriptions.value = {}
    
    subscriptionCategories.value.forEach(category => {
      const subscription = subscriptions.find(s => s.category === category.code)
      userSubscriptions.value[category.code] = subscription || {
        enabled: false,
        channelTypes: [],
        settings: {
          frequency: 'IMMEDIATE',
          quietHours: { start: '22:00', end: '08:00' }
        }
      }
    })
  } catch (__error) {
    console.error('加载用户订阅设置失败:', error)
  }
}

const handleUserChange = () => {
  loadUserSubscriptions()
}

   
const handleChannelToggle = async (channel: unknown) => {
  try {
    await notificationApi.toggleChannel(channel.type, channel.enabled)
    ElMessage.success(`${channel.name}已${channel.enabled ? '启用' : '禁用'}`)
  } catch (__error) {
    ElMessage.error('操作失败')
    channel.enabled = !channel.enabled // 回滚状态
  }
}

   
const testChannel = async (channel: unknown) => {
  try {
    const result = await notificationApi.testChannelConnection(channel.type)
    if (result) {
      ElMessage.success(`${channel.name}连接测试成功`)
      channel.status = 'UP'
    } else {
      ElMessage.error(`${channel.name}连接测试失败`)
      channel.status = 'DOWN'
    }
  } catch (__error) {
    ElMessage.error(`${channel.name}连接测试失败`)
    channel.status = 'DOWN'
  }
}

const testAllChannels = async () => {
  for (const channel of channelSettings.value) {
    if (channel.enabled) {
      await testChannel(channel)
    }
  }
}

   
const saveChannelConfig = async (channel: unknown) => {
  try {
    await notificationApi.updateChannelConfig(channel.type, channel.config)
    ElMessage.success(`${channel.name}配置保存成功`)
  } catch (__error) {
    ElMessage.error('配置保存失败')
  }
}

const handleSubscriptionToggle = (categoryCode: string, enabled: boolean) => {
  if (!userSubscriptions.value[categoryCode]) {
    userSubscriptions.value[categoryCode] = {
      enabled: false,
      channelTypes: [],
      settings: {
        frequency: 'IMMEDIATE',
        quietHours: { start: '22:00', end: '08:00' }
      }
    }
  }
  userSubscriptions.value[categoryCode].enabled = enabled
}

const handleSubscriptionChange = (categoryCode: string) => {
  // 订阅状态变化处理
}

const handleChannelTypesChange = (categoryCode: string) => {
  // 渠道类型变化处理
}

const handleQuietHoursChange = (categoryCode: string) => {
  if (quietHours.value) {
    userSubscriptions.value[categoryCode].settings.quietHours = {
      start: quietHours.value[0],
      end: quietHours.value[1]
    }
  }
}

const saveAllSettings = async () => {
  try {
    saving.value = true
    
    // 保存系统设置
    await notificationApi.updateSystemConfig({ globalSettings: systemSettings })
    
    // 保存用户订阅设置
    if (selectedUserId.value) {
      const subscriptions = Object.entries(userSubscriptions.value).map(([category, setting]) => ({
        category,
        ...setting
      }))
      await notificationApi.updateUserSubscriptions(selectedUserId.value, subscriptions)
    }
    
    ElMessage.success('设置保存成功')
  } catch (__error) {
    ElMessage.error('设置保存失败')
  } finally {
    saving.value = false
  }
}

const resetSettings = () => {
  loadChannelStatus()
  loadSystemSettings()
  if (selectedUserId.value) {
    loadUserSubscriptions()
  }
  ElMessage.info('设置已重置')
}

// 辅助方法
const getChannelTypeName = (type: string) => {
  const names: Record<string, string> = {
    INTERNAL: '站内信',
    EMAIL: '邮件',
    SMS: '短信',
    WECHAT: '微信',
    DINGTALK: '钉钉'
  }
  return names[type] || type
}

const isChannelAvailable = (channelType: string) => {
  const channel = channelSettings.value.find(c => c.type === channelType)
  return channel?.enabled && channel?.status === 'UP'
}
</script>

<style scoped lang="scss">
.notification-settings {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .header-content {
      .page-title {
        display: flex;
        align-items: center;
        font-size: 24px;
        font-weight: 600;
        color: #303133;
        margin: 0 0 8px 0;

        .el-icon {
          margin-right: 8px;
          color: #409eff;
        }
      }

      .page-description {
        color: #909399;
        margin: 0;
        font-size: 14px;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }
  }

  .settings-card {
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .channel-settings {
    .channel-item {
      border: 1px solid #ebeef5;
      border-radius: 6px;
      margin-bottom: 16px;
      overflow: hidden;

      &:last-child {
        margin-bottom: 0;
      }

      .channel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        background-color: #fafafa;
        border-bottom: 1px solid #ebeef5;

        .channel-info {
          display: flex;
          align-items: center;
          gap: 12px;

          .channel-icon {
            font-size: 20px;

            &.email-icon {
              color: #409eff;
            }

            &.sms-icon {
              color: #67c23a;
            }

            &.dingtalk-icon {
              color: #1890ff;
            }
          }

          .channel-name {
            font-weight: 500;
            color: #303133;
          }
        }
      }

      .channel-config {
        padding: 16px;

        .config-section {
          margin-bottom: 16px;

          .el-form-item {
            margin-bottom: 12px;
          }
        }

        .channel-actions {
          display: flex;
          gap: 8px;
          padding-top: 12px;
          border-top: 1px solid #f0f2f5;
        }
      }
    }
  }

  .subscription-settings {
    .user-selector {
      margin-bottom: 20px;
    }

    .subscription-list {
      .subscription-item {
        border: 1px solid #ebeef5;
        border-radius: 6px;
        margin-bottom: 16px;
        overflow: hidden;

        &:last-child {
          margin-bottom: 0;
        }

        .subscription-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          padding: 16px;
          background-color: #fafafa;
          border-bottom: 1px solid #ebeef5;

          .category-info {
            flex: 1;

            .category-name {
              display: block;
              font-weight: 500;
              color: #303133;
              margin-bottom: 4px;
            }
          }
        }

        .subscription-channels {
          padding: 16px;
          border-bottom: 1px solid #f0f2f5;

          .channels-label {
            font-size: 14px;
            color: #606266;
            margin-bottom: 8px;
          }

          .el-checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
          }
        }

        .subscription-settings-detail {
          padding: 16px;

          .el-form-item {
            margin-bottom: 12px;
          }
        }
      }
    }
  }

  .system-settings {
    .el-form-item {
      margin-bottom: 20px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .notification-settings {
    padding: 10px;

    .page-header {
      flex-direction: column;
      gap: 16px;

      .header-actions {
        width: 100%;
        justify-content: flex-start;
      }
    }

    .el-row {
      .el-col {
        margin-bottom: 20px;
      }
    }

    .channel-settings {
      .channel-item {
        .channel-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 12px;
        }

        .channel-config {
          .channel-actions {
            flex-direction: column;
          }
        }
      }
    }

    .subscription-settings {
      .subscription-list {
        .subscription-item {
          .subscription-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;
          }

          .subscription-channels {
            .el-checkbox-group {
              flex-direction: column;
            }
          }
        }
      }
    }
  }
}
</style>
