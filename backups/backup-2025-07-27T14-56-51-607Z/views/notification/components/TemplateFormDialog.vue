<template>
  <el-dialog
    v-model="visible"
    :title="dialogMode === 'add' ? '新增模板' : '编辑模板'"
    width="70%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="模板名称" prop="name">
            <el-input v-model="formData.name" placeholder="请输入模板名称"   />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="模板编码" prop="code">
            <el-input
              v-model="formData.code"
              placeholder="请输入模板编码"
              :disabled="dialogMode === 'edit'"
              />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="模板类型" prop="type">
            <el-select v-model="formData.type" placeholder="请选择模板类型" style="width: 100%">
              <el-option label="邮件模板" value="EMAIL"  />
              <el-option label="短信模板" value="SMS"  />
              <el-option label="站内信模板" value="INTERNAL"  />
              <el-option label="微信模板" value="WECHAT"  />
              <el-option label="钉钉模板" value="DINGTALK"  />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="业务类型" prop="businessType">
            <el-select v-model="formData.businessType" placeholder="请选择业务类型" style="width: 100%">
              <el-option label="系统通知" value="SYSTEM"  />
              <el-option label="工作流通知" value="WORKFLOW"  />
              <el-option label="业务通知" value="BUSINESS"  />
              <el-option label="告警通知" value="ALERT"  />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="模板标题" prop="title">
        <el-input v-model="formData.title" placeholder="请输入模板标题">
          <template #append>
            <el-button @click="showVariableHelper('title')">变量</el-button>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item label="模板内容" prop="content">
        <el-input
          v-model="formData.content"
          type="textarea"
          :rows="8"
          placeholder="请输入模板内容"
          />
        <div class="template-actions">
          <el-button size="small" @click="showVariableHelper('content')">
            <el-icon><Plus /></el-icon>
            插入变量
          </el-button>
          <el-button size="small" @click="previewTemplate">
            <el-icon><View /></el-icon>
            预览
          </el-button>
        </div>
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio value="ENABLED">启用</el-radio>
              <el-radio value="DISABLED">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sort">
            <el-input-number v-model="formData.sort" :min="0" :max="999"   />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注"
          />
      </el-form-item>
    </el-form>

    <!-- 变量助手对话框 -->
    <el-dialog
      v-model="variableDialogVisible"
      title="变量助手"
      width="500px"
      append-to-body
    >
      <div class="variable-helper">
        <div class="variable-categories">
          <el-tabs v-model="activeVariableTab">
            <el-tab-pane label="系统变量" name="system">
              <div class="variable-list">
                <div
                  v-for="variable in systemVariables"
                  :key="variable.key"
                  class="variable-item"
                  @click="insertVariable(variable)"
                >
                  <div class="variable-key">{{ variable.key }}</div>
                  <div class="variable-desc">{{ variable.description }}</div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="用户变量" name="user">
              <div class="variable-list">
                <div
                  v-for="variable in userVariables"
                  :key="variable.key"
                  class="variable-item"
                  @click="insertVariable(variable)"
                >
                  <div class="variable-key">{{ variable.key }}</div>
                  <div class="variable-desc">{{ variable.description }}</div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="业务变量" name="business">
              <div class="variable-list">
                <div
                  v-for="variable in businessVariables"
                  :key="variable.key"
                  class="variable-item"
                  @click="insertVariable(variable)"
                >
                  <div class="variable-key">{{ variable.key }}</div>
                  <div class="variable-desc">{{ variable.description }}</div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </el-dialog>

    <!-- 模板预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="模板预览"
      width="600px"
      append-to-body
    >
      <div class="template-preview">
        <div class="preview-title">
          <strong>标题：</strong>{{ renderTemplate(formData.title) }}
        </div>
        <div class="preview-content">
          <strong>内容：</strong>
          <div class="content-body" v-html="renderTemplate(formData.content)"></div>
        </div>
      </div>
    </el-dialog>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
 
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, View } from '@element-plus/icons-vue'

// Props
interface Props {
  modelValue: boolean
  mode: 'add' | 'edit'
   
  template?: unknown
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  mode: 'add',
  template: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
   
  'submit': [data: unknown]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const dialogMode = computed(() => props.mode)
const submitting = ref(false)
const variableDialogVisible = ref(false)
const previewDialogVisible = ref(false)
const activeVariableTab = ref('system')
const currentField = ref('')

// 表单数据
const formData = reactive({
  id: '',
  name: '',
  code: '',
  type: '',
  businessType: '',
  title: '',
  content: '',
  status: 'ENABLED',
  sort: 0,
  remark: ''
})

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
  code: [
    { required: true, message: '请输入模板编码', trigger: 'blur' },
    { pattern: /^[A-Z_][A-Z0-9_]*$/, message: '模板编码只能包含大写字母、数字和下划线，且以字母或下划线开头', trigger: 'blur' }
  ],
  type: [{ required: true, message: '请选择模板类型', trigger: 'change' }],
  businessType: [{ required: true, message: '请选择业务类型', trigger: 'change' }],
  title: [{ required: true, message: '请输入模板标题', trigger: 'blur' }],
  content: [{ required: true, message: '请输入模板内容', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

// 变量数据
const systemVariables = ref([
  { key: '${systemName}', description: '系统名称' },
  { key: '${currentTime}', description: '当前时间' },
  { key: '${currentDate}', description: '当前日期' },
  { key: '${serverUrl}', description: '服务器地址' }
])

const userVariables = ref([
  { key: '${userName}', description: '用户姓名' },
  { key: '${userEmail}', description: '用户邮箱' },
  { key: '${userPhone}', description: '用户手机' },
  { key: '${userDepartment}', description: '用户部门' }
])

const businessVariables = ref([
  { key: '${processName}', description: '流程名称' },
  { key: '${taskName}', description: '任务名称' },
  { key: '${businessKey}', description: '业务键' },
  { key: '${approver}', description: '审批人' }
])

const formRef = ref()

// 监听props变化
watch(() => props.template, (template) => {
  if (template && dialogMode.value === 'edit') {
    Object.assign(formData, {
      id: template.id,
      name: template.name,
      code: template.code,
      type: template.type,
      businessType: template.businessType,
      title: template.title,
      content: template.content,
      status: template.status,
      sort: template.sort || 0,
      remark: template.remark || ''
    })
  }
})

watch(visible, (val) => {
  if (val && dialogMode.value === 'add') {
    resetForm()
  }
})

// 方法
const handleClose = () => {
  visible.value = false
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    id: '',
    name: '',
    code: '',
    type: '',
    businessType: '',
    title: '',
    content: '',
    status: 'ENABLED',
    sort: 0,
    remark: ''
  })
  formRef.value?.clearValidate()
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true

    emit('submit', { ...formData })
    ElMessage.success(dialogMode.value === 'add' ? '模板创建成功' : '模板更新成功')
    handleClose()
  } catch (__error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}

const showVariableHelper = (field: string) => {
  currentField.value = field
  variableDialogVisible.value = true
}

   
const insertVariable = (variable: unknown) => {
  if (currentField.value === 'title') {
    formData.title += variable.key
  } else if (currentField.value === 'content') {
    formData.content += variable.key
  }
  variableDialogVisible.value = false
}

const previewTemplate = () => {
  previewDialogVisible.value = true
}

const renderTemplate = (template: string) => {
  if (!template) return ''
  
  // 简单的变量替换示例
  return template
    .replace(/\$\{systemName\}/g, '杭科院人事管理系统')
    .replace(/\$\{currentTime\}/g, new Date().toLocaleString())
    .replace(/\$\{currentDate\}/g, new Date().toLocaleDateString())
    .replace(/\$\{userName\}/g, '张三')
    .replace(/\$\{userEmail\}/g, '<EMAIL>')
    .replace(/\$\{processName\}/g, '员工入职流程')
    .replace(/\n/g, '<br>')
}
</script>

<style scoped>
.template-actions {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

.variable-helper {
  max-height: 400px;
  overflow-y: auto;
}

.variable-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.variable-item {
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.variable-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.variable-key {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 4px;
}

.variable-desc {
  font-size: 12px;
  color: #909399;
}

.template-preview {
  padding: 16px;
}

.preview-title {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.preview-content {
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.content-body {
  margin-top: 8px;
  line-height: 1.6;
  white-space: pre-wrap;
}

.dialog-footer {
  text-align: right;
}
</style>
