<template>
  <el-dialog
    v-model="visible"
    title="导入模板"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="template-import">
      <!-- 上传区域 -->
      <el-upload
        ref="uploadRef"
        class="upload-area"
        drag
        :auto-upload="false"
        :on-change="handleFileChange"
        :on-remove="handleFileRemove"
        :before-upload="beforeUpload"
        accept=".json,.xlsx,.csv"
        multiple
      >
        <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            支持 .json、.xlsx、.csv 格式文件，单个文件不超过 5MB
          </div>
        </template>
      </el-upload>

      <!-- 文件列表 -->
      <div v-if="fileList.length > 0" class="file-list">
        <h4>待导入文件</h4>
        <el-table :data="fileList" stripe>
          <el-table-column prop="name" label="文件名"  />
          <el-table-column prop="size" label="文件大小" width="120">
            <template #default="scope">
              {{ formatFileSize(scope.row.size) }}
            </template>
          </el-table-column>
          <el-table-column prop="type" label="文件类型" width="100"  />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusTagType(scope.row.status)" size="small">
                {{ getStatusLabel(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="scope">
              <el-button
                size="small"
                type="danger"
                link
                @click="removeFile(scope.$index)"
              >
                移除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 导入选项 -->
      <div v-if="fileList.length > 0" class="import-options">
        <h4>导入选项</h4>
        <el-form :model="importForm" label-width="120px">
          <el-form-item label="导入模式">
            <el-radio-group v-model="importForm.mode">
              <el-radio value="CREATE">仅创建新模板</el-radio>
              <el-radio value="UPDATE">更新已存在模板</el-radio>
              <el-radio value="MERGE">合并模式</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="冲突处理">
            <el-radio-group v-model="importForm.conflictStrategy">
              <el-radio value="SKIP">跳过冲突项</el-radio>
              <el-radio value="OVERWRITE">覆盖已存在</el-radio>
              <el-radio value="RENAME">自动重命名</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="数据验证">
            <el-switch v-model="importForm.validate"  />
            <el-text size="small" type="info" style="margin-left: 8px">
              导入前验证模板数据的完整性
            </el-text>
          </el-form-item>

          <el-form-item label="自动启用">
            <el-switch v-model="importForm.autoEnable"  />
            <el-text size="small" type="info" style="margin-left: 8px">
              导入后自动启用模板
            </el-text>
          </el-form-item>

          <el-form-item label="导入说明">
            <el-input
              v-model="importForm.description"
              type="textarea"
              :rows="3"
              placeholder="请输入导入说明（可选）"
              />
          </el-form-item>
        </el-form>
      </div>

      <!-- 导入结果 -->
      <div v-if="importResults.length > 0" class="import-results">
        <h4>导入结果</h4>
        <el-table :data="importResults" stripe>
          <el-table-column prop="fileName" label="文件名"  />
          <el-table-column prop="templateName" label="模板名称"  />
          <el-table-column prop="status" label="导入状态" width="120">
            <template #default="scope">
              <el-tag :type="getResultTagType(scope.row.status)" size="small">
                {{ getResultLabel(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="message" label="结果信息" show-overflow-tooltip  />
        </el-table>
      </div>

      <!-- 进度条 -->
      <div v-if="importing" class="import-progress">
        <el-progress
          :percentage="importProgress"
          :status="importProgress === 100 ? 'success' : undefined"
         />
        <div class="progress-text">
          {{ importProgressText }}
        </div>
      </div>

      <!-- 模板格式说明 -->
      <el-card class="format-help" shadow="never">
        <template #header>
          <span>模板格式说明</span>
        </template>
        <el-tabs>
          <el-tab-pane label="JSON格式" name="json">
            <pre class="format-example">
{
  "templates": [
    {
      "name": "模板名称",
      "code": "TEMPLATE_CODE",
      "type": "EMAIL",
      "businessType": "SYSTEM",
      "title": "模板标题",
      "content": "模板内容",
      "status": "ENABLED",
      "remark": "备注信息"
    }
  ]
}
            </pre>
          </el-tab-pane>
          <el-tab-pane label="Excel格式" name="excel">
            <div class="excel-format">
              <p>Excel文件应包含以下列：</p>
              <ul>
                <li>name - 模板名称（必填）</li>
                <li>code - 模板编码（必填）</li>
                <li>type - 模板类型（EMAIL/SMS/INTERNAL等）</li>
                <li>businessType - 业务类型（SYSTEM/WORKFLOW等）</li>
                <li>title - 模板标题（必填）</li>
                <li>content - 模板内容（必填）</li>
                <li>status - 状态（ENABLED/DISABLED）</li>
                <li>remark - 备注信息</li>
              </ul>
            </div>
          </el-tab-pane>
          <el-tab-pane label="CSV格式" name="csv">
            <pre class="format-example">
name,code,type,businessType,title,content,status,remark
"用户注册通知","USER_REGISTER","EMAIL","SYSTEM","欢迎注册","欢迎您注册我们的系统","ENABLED","用户注册时发送"
            </pre>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">
          {{ importing ? '后台运行' : '取消' }}
        </el-button>
        <el-button
          v-if="fileList.length > 0 && !importing"
          type="primary"
          @click="startImport"
        >
          开始导入
        </el-button>
        <el-button
          v-if="importing"
          type="warning"
          @click="cancelImport"
        >
          取消导入
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'

// Props
interface Props {
  modelValue: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
   
  'imported': [results: unknown[]]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const fileList = ref<any[]>([])
const importing = ref(false)
const importProgress = ref(0)
const importProgressText = ref('')
const importResults = ref<any[]>([])

// 导入表单
const importForm = reactive({
  mode: 'CREATE',
  conflictStrategy: 'SKIP',
  validate: true,
  autoEnable: false,
  description: ''
})

const uploadRef = ref()

// 方法
const handleClose = () => {
  if (!importing.value) {
    visible.value = false
    resetData()
  }
}

const resetData = () => {
  fileList.value = []
  importResults.value = []
  importProgress.value = 0
  importProgressText.value = ''
  Object.assign(importForm, {
    mode: 'CREATE',
    conflictStrategy: 'SKIP',
    validate: true,
    autoEnable: false,
    description: ''
  })
}

   
const handleFileChange = (file: unknown, files: unknown[]) => {
  const newFile = {
    name: file.name,
    size: file.size,
    type: getFileType(file.name),
    status: 'PENDING',
    raw: file.raw
  }
  
  // 检查文件是否已存在
   
  const exists = fileList.value.some((f: unknown) => f.name === newFile.name)
  if (exists) {
    ElMessage.warning(`文件 ${newFile.name} 已存在`)
    return
  }
  
  fileList.value.push(newFile)
}

   
const handleFileRemove = (file: unknown, files: unknown[]) => {
   
  const index = fileList.value.findIndex((f: unknown) => f.name === file.name)
  if (index > -1) {
    fileList.value.splice(index, 1)
  }
}

const removeFile = (index: number) => {
  fileList.value.splice(index, 1)
}

const beforeUpload = (file: File) => {
  const isValidType = /\.(json|xlsx|csv)$/i.test(file.name)
  const isValidSize = file.size / 1024 / 1024 < 5

  if (!isValidType) {
    ElMessage.error('只支持 .json、.xlsx、.csv 格式文件')
    return false
  }
  
  if (!isValidSize) {
    ElMessage.error('文件大小不能超过 5MB')
    return false
  }
  
  return true
}

const startImport = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先选择要导入的文件')
    return
  }

  importing.value = true
  importProgress.value = 0
  importResults.value = []

  try {
    for (let i = 0; i < fileList.value.length; i++) {
      const file = fileList.value[i]
      importProgressText.value = `正在导入 ${file.name}...`
      
      // 模拟导入过程
      await simulateImport(file, i)
      
      importProgress.value = Math.round(((i + 1) / fileList.value.length) * 100)
    }

    importProgressText.value = '导入完成'
    ElMessage.success('模板导入完成')
    emit('imported', importResults.value)
  } catch (__error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败')
  } finally {
    importing.value = false
  }
}

   
const simulateImport = async (file: unknown, index: number) => {
  // 模拟导入延迟
  await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000))
  
  // 模拟导入结果
  const success = Math.random() > 0.3 // 70% 成功率
  const result = {
    fileName: file.name,
    templateName: `模板_${index + 1}`,
    status: success ? 'SUCCESS' : 'FAILED',
    message: success ? '导入成功' : '文件格式错误或数据不完整'
  }
  
  importResults.value.push(result)
  file.status = result.status
}

const cancelImport = () => {
  importing.value = false
  importProgress.value = 0
  importProgressText.value = '导入已取消'
  ElMessage.info('导入已取消')
}

// 辅助方法
const getFileType = (fileName: string) => {
  const ext = fileName.split('.').pop()?.toLowerCase()
  switch (ext) {
    case 'json': return 'JSON'
    case 'xlsx': return 'Excel'
    case 'csv': return 'CSV'
    default: return 'UNKNOWN'
  }
}

const formatFileSize = (size: number) => {
  if (size < 1024) return `${size}B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`
  return `${(size / 1024 / 1024).toFixed(1)}MB`
}

const getStatusTagType = (status: string) => {
  switch (status) {
    case 'PENDING': return 'info'
    case 'SUCCESS': return 'success'
    case 'FAILED': return 'danger'
    default: return ''
  }
}

const getStatusLabel = (status: string) => {
  switch (status) {
    case 'PENDING': return '待导入'
    case 'SUCCESS': return '成功'
    case 'FAILED': return '失败'
    default: return status
  }
}

const getResultTagType = (status: string) => {
  switch (status) {
    case 'SUCCESS': return 'success'
    case 'FAILED': return 'danger'
    case 'WARNING': return 'warning'
    default: return ''
  }
}

const getResultLabel = (status: string) => {
  switch (status) {
    case 'SUCCESS': return '成功'
    case 'FAILED': return '失败'
    case 'WARNING': return '警告'
    default: return status
  }
}
</script>

<style scoped>
.template-import {
  padding: 10px 0;
}

.upload-area {
  margin-bottom: 20px;
}

.file-list,
.import-options,
.import-results,
.format-help {
  margin-bottom: 20px;
}

.file-list h4,
.import-options h4,
.import-results h4 {
  margin-bottom: 12px;
  color: #303133;
  font-size: 16px;
}

.import-progress {
  margin: 20px 0;
}

.progress-text {
  text-align: center;
  margin-top: 8px;
  color: #606266;
  font-size: 14px;
}

.format-example {
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.5;
  overflow-x: auto;
}

.excel-format p {
  margin-bottom: 8px;
  color: #606266;
}

.excel-format ul {
  margin: 0;
  padding-left: 20px;
}

.excel-format li {
  margin-bottom: 4px;
  color: #606266;
  font-size: 12px;
}

.dialog-footer {
  text-align: right;
}
</style>
