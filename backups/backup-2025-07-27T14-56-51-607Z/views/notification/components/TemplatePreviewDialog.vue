<template>
  <el-dialog
    v-model="visible"
    :title="`模板预览 - ${template?.name || ''}`"
    width="80%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="template" class="template-preview">
      <!-- 模板信息 -->
      <el-card class="template-info" shadow="never">
        <template #header>
          <span>模板信息</span>
        </template>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="模板名称">{{ template.name }}</el-descriptions-item>
          <el-descriptions-item label="模板编码">{{ template.code }}</el-descriptions-item>
          <el-descriptions-item label="模板类型">{{ getTypeLabel(template.type) }}</el-descriptions-item>
          <el-descriptions-item label="业务类型">{{ getBusinessTypeLabel(template.businessType) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="template.status === 'ENABLED' ? 'success' : 'danger'" size="small">
              {{ template.status === 'ENABLED' ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ template.createTime }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 预览参数 -->
      <el-card class="preview-params" shadow="never">
        <template #header>
          <div class="card-header">
            <span>预览参数</span>
            <el-button size="small" @click="resetParams">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </div>
        </template>
        <el-form :model="previewParams" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="用户姓名">
                <el-input v-model="previewParams.userName" @input="updatePreview"   />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="用户邮箱">
                <el-input v-model="previewParams.userEmail" @input="updatePreview"   />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="用户手机">
                <el-input v-model="previewParams.userPhone" @input="updatePreview"   />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="用户部门">
                <el-input v-model="previewParams.userDepartment" @input="updatePreview"   />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="流程名称">
                <el-input v-model="previewParams.processName" @input="updatePreview"   />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="任务名称">
                <el-input v-model="previewParams.taskName" @input="updatePreview"   />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="业务键">
                <el-input v-model="previewParams.businessKey" @input="updatePreview"   />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="审批人">
                <el-input v-model="previewParams.approver" @input="updatePreview"   />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="自定义参数">
                <el-input v-model="previewParams.customParam" @input="updatePreview"   />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>

      <!-- 预览结果 -->
      <el-card class="preview-result" shadow="never">
        <template #header>
          <div class="card-header">
            <span>预览结果</span>
            <div class="header-actions">
              <el-button size="small" @click="copyPreview">
                <el-icon><CopyDocument /></el-icon>
                复制
              </el-button>
              <el-button size="small" @click="sendTestMessage">
                <el-icon><Message /></el-icon>
                发送测试
              </el-button>
            </div>
          </div>
        </template>
        
        <!-- 邮件预览 -->
        <div v-if="template.type === 'EMAIL'" class="email-preview">
          <div class="email-header">
            <div class="email-field">
              <strong>主题：</strong>{{ renderedTitle }}
            </div>
            <div class="email-field">
              <strong>收件人：</strong>{{ previewParams.userEmail }}
            </div>
            <div class="email-field">
              <strong>发送时间：</strong>{{ currentTime }}
            </div>
          </div>
          <div class="email-body">
            <div class="email-content" v-html="renderedContent"></div>
          </div>
        </div>

        <!-- 短信预览 -->
        <div v-else-if="template.type === 'SMS'" class="sms-preview">
          <div class="sms-header">
            <div class="sms-info">
              <strong>短信预览</strong>
              <span class="sms-length">{{ smsLength }}/70字符</span>
            </div>
          </div>
          <div class="sms-body">
            <div class="sms-content">{{ renderedContent }}</div>
          </div>
        </div>

        <!-- 站内信预览 -->
        <div v-else-if="template.type === 'INTERNAL'" class="internal-preview">
          <div class="internal-header">
            <div class="internal-title">{{ renderedTitle }}</div>
            <div class="internal-meta">
              <span>发送给：{{ previewParams.userName }}</span>
              <span>{{ currentTime }}</span>
            </div>
          </div>
          <div class="internal-body">
            <div class="internal-content" v-html="renderedContent"></div>
          </div>
        </div>

        <!-- 微信/钉钉预览 -->
        <div v-else class="message-preview">
          <div class="message-header">
            <div class="message-title">{{ renderedTitle }}</div>
            <div class="message-meta">{{ currentTime }}</div>
          </div>
          <div class="message-body">
            <div class="message-content" v-html="renderedContent"></div>
          </div>
        </div>
      </el-card>

      <!-- 变量说明 -->
      <el-card class="variable-help" shadow="never">
        <template #header>
          <span>可用变量</span>
        </template>
        <div class="variable-list">
          <el-row :gutter="20">
            <el-col :span="8">
              <h4>系统变量</h4>
              <ul>
                <li><code>${systemName}</code> - 系统名称</li>
                <li><code>${currentTime}</code> - 当前时间</li>
                <li><code>${currentDate}</code> - 当前日期</li>
                <li><code>${serverUrl}</code> - 服务器地址</li>
              </ul>
            </el-col>
            <el-col :span="8">
              <h4>用户变量</h4>
              <ul>
                <li><code>${userName}</code> - 用户姓名</li>
                <li><code>${userEmail}</code> - 用户邮箱</li>
                <li><code>${userPhone}</code> - 用户手机</li>
                <li><code>${userDepartment}</code> - 用户部门</li>
              </ul>
            </el-col>
            <el-col :span="8">
              <h4>业务变量</h4>
              <ul>
                <li><code>${processName}</code> - 流程名称</li>
                <li><code>${taskName}</code> - 任务名称</li>
                <li><code>${businessKey}</code> - 业务键</li>
                <li><code>${approver}</code> - 审批人</li>
              </ul>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="editTemplate">
          <el-icon><Edit /></el-icon>
          编辑模板
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'TemplatePreviewDialog'
})
 
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, CopyDocument, Message, Edit } from '@element-plus/icons-vue'

// Props
interface Props {
  modelValue: boolean
   
  template: unknown | null
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  template: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
   
  'edit': [template: unknown]
   
  'sendTest': [template: unknown, params: unknown]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const currentTime = ref(new Date().toLocaleString())

// 预览参数
const previewParams = reactive({
  userName: '张三',
  userEmail: '<EMAIL>',
  userPhone: '***********',
  userDepartment: '技术部',
  processName: '员工入职流程',
  taskName: '填写入职信息',
  businessKey: 'EMP_2025_001',
  approver: '李四',
  customParam: '自定义参数值'
})

// 渲染后的内容
const renderedTitle = ref('')
const renderedContent = ref('')

// 计算属性
const smsLength = computed(() => renderedContent.value.length)

// 监听props变化
watch(() => props.template, (template) => {
  if (template && visible.value) {
    updatePreview()
  }
})

watch(visible, (val) => {
  if (val && props.template) {
    updatePreview()
  }
})

// 方法
const handleClose = () => {
  visible.value = false
}

const updatePreview = () => {
  if (!props.template) return

  renderedTitle.value = renderTemplate(props.template.title)
  renderedContent.value = renderTemplate(props.template.content)
}

const renderTemplate = (template: string) => {
  if (!template) return ''

  return template
    .replace(/\$\{systemName\}/g, '杭科院人事管理系统')
    .replace(/\$\{currentTime\}/g, currentTime.value)
    .replace(/\$\{currentDate\}/g, new Date().toLocaleDateString())
    .replace(/\$\{serverUrl\}/g, 'https://hr.hky.edu.cn')
    .replace(/\$\{userName\}/g, previewParams.userName)
    .replace(/\$\{userEmail\}/g, previewParams.userEmail)
    .replace(/\$\{userPhone\}/g, previewParams.userPhone)
    .replace(/\$\{userDepartment\}/g, previewParams.userDepartment)
    .replace(/\$\{processName\}/g, previewParams.processName)
    .replace(/\$\{taskName\}/g, previewParams.taskName)
    .replace(/\$\{businessKey\}/g, previewParams.businessKey)
    .replace(/\$\{approver\}/g, previewParams.approver)
    .replace(/\$\{customParam\}/g, previewParams.customParam)
    .replace(/\n/g, '<br>')
}

const resetParams = () => {
  Object.assign(previewParams, {
    userName: '张三',
    userEmail: '<EMAIL>',
    userPhone: '***********',
    userDepartment: '技术部',
    processName: '员工入职流程',
    taskName: '填写入职信息',
    businessKey: 'EMP_2025_001',
    approver: '李四',
    customParam: '自定义参数值'
  })
  updatePreview()
}

const copyPreview = async () => {
  try {
    const content = `标题：${renderedTitle.value}\n\n内容：${renderedContent.value.replace(/<br>/g, '\n')}`
    await navigator.clipboard.writeText(content)
    ElMessage.success('预览内容已复制到剪贴板')
  } catch (__error) {
    ElMessage.error('复制失败')
  }
}

const sendTestMessage = () => {
  emit('sendTest', props.template, { ...previewParams })
}

const editTemplate = () => {
  emit('edit', props.template)
  handleClose()
}

// 辅助方法
const getTypeLabel = (type: string) => {
  switch (type) {
    case 'EMAIL': return '邮件模板'
    case 'SMS': return '短信模板'
    case 'INTERNAL': return '站内信模板'
    case 'WECHAT': return '微信模板'
    case 'DINGTALK': return '钉钉模板'
    default: return type
  }
}

const getBusinessTypeLabel = (type: string) => {
  switch (type) {
    case 'SYSTEM': return '系统通知'
    case 'WORKFLOW': return '工作流通知'
    case 'BUSINESS': return '业务通知'
    case 'ALERT': return '告警通知'
    default: return type
  }
}
</script>

<style scoped>
.template-preview {
  padding: 10px 0;
}

.template-info,
.preview-params,
.preview-result,
.variable-help {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 邮件预览样式 */
.email-preview {
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.email-header {
  padding: 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #ddd;
}

.email-field {
  margin-bottom: 8px;
}

.email-body {
  padding: 16px;
  background-color: white;
}

.email-content {
  line-height: 1.6;
}

/* 短信预览样式 */
.sms-preview {
  max-width: 300px;
  margin: 0 auto;
  border: 2px solid #409eff;
  border-radius: 8px;
  overflow: hidden;
}

.sms-header {
  padding: 12px;
  background-color: #409eff;
  color: white;
}

.sms-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sms-length {
  font-size: 12px;
}

.sms-body {
  padding: 16px;
  background-color: white;
}

.sms-content {
  line-height: 1.5;
  word-wrap: break-word;
}

/* 站内信预览样式 */
.internal-preview {
  border: 1px solid #e6a23c;
  border-radius: 4px;
  overflow: hidden;
}

.internal-header {
  padding: 16px;
  background-color: #fdf6ec;
  border-bottom: 1px solid #e6a23c;
}

.internal-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}

.internal-meta {
  font-size: 12px;
  color: #909399;
  display: flex;
  justify-content: space-between;
}

.internal-body {
  padding: 16px;
  background-color: white;
}

.internal-content {
  line-height: 1.6;
}

/* 消息预览样式 */
.message-preview {
  border: 1px solid #67c23a;
  border-radius: 4px;
  overflow: hidden;
}

.message-header {
  padding: 16px;
  background-color: #f0f9ff;
  border-bottom: 1px solid #67c23a;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.message-title {
  font-size: 16px;
  font-weight: 600;
}

.message-meta {
  font-size: 12px;
  color: #909399;
}

.message-body {
  padding: 16px;
  background-color: white;
}

.message-content {
  line-height: 1.6;
}

/* 变量说明样式 */
.variable-list h4 {
  margin-bottom: 12px;
  color: #303133;
  font-size: 14px;
}

.variable-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.variable-list li {
  margin-bottom: 8px;
  font-size: 12px;
  color: #606266;
}

.variable-list code {
  background-color: #f5f7fa;
  padding: 2px 4px;
  border-radius: 2px;
  font-family: 'Courier New', monospace;
  color: #409eff;
}

.dialog-footer {
  text-align: right;
}
</style>
