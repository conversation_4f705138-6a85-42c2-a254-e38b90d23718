<template>
  <el-dialog
    v-model="dialogVisible"
    title="发送消息"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      @submit.prevent
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="通知渠道" prop="channelType" required>
            <el-select
              v-model="form.channelType"
              placeholder="请选择通知渠道"
              style="width: 100%"
              @change="handleChannelTypeChange"
            >
              <el-option
                v-for="channel in availableChannels"
                :key="channel.value"
                :label="channel.label"
                :value="channel.value"
                :disabled="!channel.enabled"
              >
                <div class="channel-option">
                  <span>{{ channel.label }}</span>
                  <el-tag v-if="!channel.enabled" type="danger" size="small">不可用</el-tag>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="消息优先级" prop="priority">
            <el-select v-model="form.priority" placeholder="请选择优先级" style="width: 100%">
              <el-option label="低优先级" :value="10"  />
              <el-option label="普通优先级" :value="50"  />
              <el-option label="高优先级" :value="80"  />
              <el-option label="紧急" :value="100"  />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="收件人" prop="recipients" required>
        <div class="recipients-section">
          <el-select
            v-model="form.recipients"
            multiple
            filterable
            allow-create
            placeholder="请输入或选择收件人"
            style="width: 100%"
            :loading="loadingUsers"
            @focus="loadUsers"
          >
            <el-option
              v-for="user in userOptions"
              :key="user.value"
              :label="user.label"
              :value="user.value"
             />
          </el-select>
          <div class="recipients-tips">
            <el-text size="small" type="info">
              {{ getRecipientTips() }}
            </el-text>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="使用模板">
        <el-row :gutter="12">
          <el-col :span="18">
            <el-select
              v-model="selectedTemplateId"
              placeholder="选择消息模板（可选）"
              style="width: 100%"
              clearable
              filterable
              @change="handleTemplateChange"
            >
              <el-option
                v-for="template in templates"
                :key="template.id"
                :label="template.name"
                :value="template.id"
              >
                <div class="template-option">
                  <span>{{ template.name }}</span>
                  <el-tag size="small" type="info">{{ template.category }}</el-tag>
                </div>
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-button @click="previewTemplate" :disabled="!selectedTemplateId">
              <el-icon><View /></el-icon>
              预览
            </el-button>
          </el-col>
        </el-row>
      </el-form-item>

      <el-form-item label="消息标题" prop="title">
        <el-input
          v-model="form.title"
          placeholder="请输入消息标题"
          maxlength="200"
          show-word-limit
          />
      </el-form-item>

      <el-form-item label="消息内容" prop="content" required>
        <el-input
          v-model="form.content"
          type="textarea"
          :rows="6"
          placeholder="请输入消息内容"
          maxlength="2000"
          show-word-limit
          />
      </el-form-item>

      <!-- 模板变量 -->
      <el-form-item v-if="templateVariables.length > 0" label="模板变量">
        <div class="template-variables">
          <el-row :gutter="12" v-for="variable in templateVariables" :key="variable">
            <el-col :span="6">
              <el-text>{{ variable }}:</el-text>
            </el-col>
            <el-col :span="18">
              <el-input
                v-model="form.variables[variable]"
                :placeholder="`请输入${variable}的值`"
                size="small"
                />
            </el-col>
          </el-row>
        </div>
      </el-form-item>

      <el-form-item label="发送选项">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-checkbox v-model="form.async">异步发送</el-checkbox>
          </el-col>
          <el-col :span="8">
            <el-checkbox v-model="form.batch" :disabled="form.recipients.length <= 1">
              批量发送
            </el-checkbox>
          </el-col>
          <el-col :span="8">
            <el-checkbox v-model="enableSchedule">定时发送</el-checkbox>
          </el-col>
        </el-row>
      </el-form-item>

      <el-form-item v-if="enableSchedule" label="发送时间" prop="scheduledTime">
        <el-date-picker
          v-model="form.scheduledTime"
          type="datetime"
          placeholder="选择发送时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          :disabled-date="disabledDate"
          style="width: 100%"
         />
      </el-form-item>

      <el-form-item label="业务信息">
        <el-row :gutter="12">
          <el-col :span="12">
            <el-input
              v-model="form.businessType"
              placeholder="业务类型（可选）"
              />
          </el-col>
          <el-col :span="12">
            <el-input
              v-model="form.businessId"
              placeholder="业务ID（可选）"
              />
          </el-col>
        </el-row>
      </el-form-item>
    </el-form>

    <!-- 预览区域 -->
    <div v-if="previewContent" class="preview-section">
      <el-divider>消息预览</el-divider>
      <el-card class="preview-card">
        <div class="preview-header">
          <strong>{{ form.title || '无标题' }}</strong>
          <el-tag :type="getChannelTypeColor(form.channelType)" size="small">
            {{ getChannelTypeName(form.channelType) }}
          </el-tag>
        </div>
        <div class="preview-content">
          {{ previewContent }}
        </div>
        <div class="preview-footer">
          <el-text size="small" type="info">
            收件人: {{ form.recipients.join(', ') }}
          </el-text>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="handlePreview" :disabled="!canPreview">
          <el-icon><View /></el-icon>
          预览
        </el-button>
        <el-button
          type="primary"
          :loading="sending"
          :disabled="!canSend"
          @click="handleSend"
        >
          <el-icon><Promotion /></el-icon>
          {{ sending ? '发送中...' : (enableSchedule ? '定时发送' : '立即发送') }}
        </el-button>
      </div>
    </template>

    <!-- 模板预览对话框 -->
    <el-dialog
      v-model="showTemplatePreview"
      title="模板预览"
      width="600px"
      append-to-body
    >
      <div v-if="templatePreview">
        <div class="template-preview-header">
          <strong>{{ templatePreview.subject || templatePreview.title }}</strong>
        </div>
        <div class="template-preview-content">
          {{ templatePreview.content }}
        </div>
      </div>
      <template #footer>
        <el-button @click="showTemplatePreview = false">关闭</el-button>
        <el-button type="primary" @click="useTemplate">使用此模板</el-button>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'MessageSendDialog'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { View, Promotion } from '@element-plus/icons-vue'
import { notificationApi } from '@/api/notification'
import { employeeApi } from '@/api/employee'
import type { NotificationSendRequest, NotificationTemplate } from '@/types/notification'
import type { FormInstance, FormRules } from 'element-plus'

// Props
interface Props {
  modelValue: boolean
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const formRef = ref<FormInstance>()
const sending = ref(false)
const loadingUsers = ref(false)
const enableSchedule = ref(false)
const selectedTemplateId = ref('')
const showTemplatePreview = ref(false)
const templatePreview = ref<unknown>(null)
const previewContent = ref('')

// 表单数据
const form = reactive<NotificationSendRequest & { variables: Record<string, unknown> }>({
  channelType: 'INTERNAL',
  recipients: [],
  title: '',
  content: '',
  priority: 50,
  async: false,
  batch: false,
  variables: {}
})

// 选项数据
const availableChannels = ref([
  { label: '站内信', value: 'INTERNAL', enabled: true },
  { label: '邮件', value: 'EMAIL', enabled: true },
  { label: '短信', value: 'SMS', enabled: true },
  { label: '微信', value: 'WECHAT', enabled: false },
  { label: '钉钉', value: 'DINGTALK', enabled: true }
])

const userOptions = ref<Array<{ label: string; value: string }>>([])
const templates = ref<NotificationTemplate[]>([])
const templateVariables = ref<string[]>([])

// 表单验证规则
const rules: FormRules = {
  channelType: [
    { required: true, message: '请选择通知渠道', trigger: 'change' }
  ],
  recipients: [
    { required: true, message: '请选择收件人', trigger: 'change' },
    { type: 'array', min: 1, message: '至少选择一个收件人', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入消息内容', trigger: 'blur' },
    { min: 1, max: 2000, message: '内容长度在 1 到 2000 个字符', trigger: 'blur' }
  ],
  scheduledTime: [
    { required: true, message: '请选择发送时间', trigger: 'change', validator: validateScheduledTime }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const canPreview = computed(() => {
  return form.content && form.recipients.length > 0
})

const canSend = computed(() => {
  return form.channelType && form.recipients.length > 0 && form.content && !sending.value
})

// 监听对话框显示状态
watch(dialogVisible, (visible) => {
  if (visible) {
    resetForm()
    loadTemplates()
    loadChannelStatus()
  }
})

// 监听定时发送选项
watch(enableSchedule, (enabled) => {
  if (!enabled) {
    form.scheduledTime = undefined
  }
})

// 方法
const resetForm = () => {
  Object.assign(form, {
    channelType: 'INTERNAL',
    recipients: [],
    title: '',
    content: '',
    priority: 50,
    async: false,
    batch: false,
    variables: {}
  })
  enableSchedule.value = false
  selectedTemplateId.value = ''
  templateVariables.value = []
  previewContent.value = ''
  formRef.value?.clearValidate()
}

const loadUsers = async () => {
  if (userOptions.value.length > 0) return
  
  try {
    loadingUsers.value = true
    // 调用员工API获取用户列表
    const response = await employeeApi.queryEmployees({
      page: 0,
      size: 100,
      employmentStatus: 'ACTIVE' // 只获取在职员工
    })
    
    userOptions.value = response.content.map(employee => ({
      label: `${employee.name} (${employee.email || employee.phone || employee.employeeCode})`,
      value: employee.email || employee.phone || employee.employeeCode // 根据渠道类型，可以是邮箱或手机号
    }))
    
    if (userOptions.value.length === 0) {
      // 如果没有数据，使用模拟数据
      userOptions.value = [
        { label: '张三 (<EMAIL>)', value: '<EMAIL>' },
        { label: '李四 (<EMAIL>)', value: '<EMAIL>' },
        { label: '王五 (<EMAIL>)', value: '<EMAIL>' }
      ]
    }
  } catch (__error) {
    console.error('加载用户列表失败:', error)
    // 失败时使用模拟数据
    userOptions.value = [
      { label: '张三 (<EMAIL>)', value: '<EMAIL>' },
      { label: '李四 (<EMAIL>)', value: '<EMAIL>' },
      { label: '王五 (<EMAIL>)', value: '<EMAIL>' }
    ]
  } finally {
    loadingUsers.value = false
  }
}

const loadTemplates = async () => {
  try {
    const response = await notificationApi.queryTemplates({
      page: 1,
      size: 100,
      channelType: form.channelType,
      enabled: true
    })
    templates.value = response.records
  } catch (__error) {
    console.error('加载模板列表失败:', error)
  }
}

const loadChannelStatus = async () => {
  try {
    const channels = await notificationApi.getChannelStatus()
    availableChannels.value.forEach(channel => {
      const status = channels.find(c => c.channelType === channel.value)
      channel.enabled = Boolean(status?.enabled && status?.available)
    })
  } catch (__error) {
    console.error('加载渠道状态失败:', error)
  }
}

const handleChannelTypeChange = () => {
  loadTemplates()
  selectedTemplateId.value = ''
  templateVariables.value = []
}

const handleTemplateChange = async () => {
  if (!selectedTemplateId.value) {
    templateVariables.value = []
    return
  }

  try {
    const template = await notificationApi.getTemplateById(selectedTemplateId.value)
    templateVariables.value = template.variables || []
    
    // 清空之前的变量值
    form.variables = {}
    templateVariables.value.forEach(variable => {
      form.variables[variable] = ''
    })
  } catch (__error) {
    ElMessage.error('加载模板失败')
  }
}

const previewTemplate = async () => {
  if (!selectedTemplateId.value) return

  try {
    const preview = await notificationApi.previewTemplate(selectedTemplateId.value, form.variables)
    templatePreview.value = preview
    showTemplatePreview.value = true
  } catch (__error) {
    ElMessage.error('预览模板失败')
  }
}

const useTemplate = () => {
  if (templatePreview.value) {
    form.title = templatePreview.value.subject || templatePreview.value.title
    form.content = templatePreview.value.content
    showTemplatePreview.value = false
  }
}

const handlePreview = () => {
  let content = form.content
  
  // 替换模板变量
  templateVariables.value.forEach(variable => {
    const value = form.variables[variable] || `{${variable}}`
    content = content?.replace(new RegExp(`\\$\\{${variable}\\}`, 'g'), value) || ''
  })
  
  previewContent.value = content || ''
}

const handleSend = async () => {
  try {
    await formRef.value?.validate()
  } catch (__error) {
    return
  }

  try {
    sending.value = true
    
    const sendData: NotificationSendRequest = {
      ...form,
      templateId: selectedTemplateId.value || undefined
    }

    if (enableSchedule.value) {
      await notificationApi.scheduleMessage(sendData, form.scheduledTime!)
      ElMessage.success('定时消息设置成功')
    } else if (form.batch && form.recipients.length > 1) {
      await notificationApi.sendBatchMessages(sendData)
      ElMessage.success('批量消息发送成功')
    } else if (form.async) {
      await notificationApi.sendMessageAsync(sendData)
      ElMessage.success('异步消息发送成功')
    } else {
      await notificationApi.sendMessage(sendData)
      ElMessage.success('消息发送成功')
    }
    
    emit('success')
    handleClose()
   
  } catch (error: unknown) {
    ElMessage.error(error.message || '消息发送失败')
  } finally {
    sending.value = false
  }
}

const handleClose = () => {
  emit('update:modelValue', false)
}

// 辅助方法
const getRecipientTips = () => {
  const channelTips: Record<string, string> = {
    INTERNAL: '输入用户名或用户ID',
    EMAIL: '输入邮箱地址',
    SMS: '输入手机号码',
    WECHAT: '输入微信用户ID',
    DINGTALK: '输入钉钉用户ID'
  }
  return channelTips[form.channelType] || '输入收件人信息'
}

const getChannelTypeName = (type: string) => {
  const names: Record<string, string> = {
    INTERNAL: '站内信',
    EMAIL: '邮件',
    SMS: '短信',
    WECHAT: '微信',
    DINGTALK: '钉钉'
  }
  return names[type] || type
}

const getChannelTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    INTERNAL: '',
    EMAIL: 'success',
    SMS: 'warning',
    WECHAT: 'success',
    DINGTALK: 'primary'
  }
  return colors[type] || ''
}

const disabledDate = (time: Date) => {
  return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
}

   
function validateScheduledTime(rule: unknown, value: unknown, callback: unknown) {
  if (enableSchedule.value && !value) {
    callback(new Error('请选择发送时间'))
  } else if (enableSchedule.value && new Date(value).getTime() <= Date.now()) {
    callback(new Error('发送时间必须晚于当前时间'))
  } else {
    callback()
  }
}
</script>

<style scoped lang="scss">
.channel-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.template-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.recipients-section {
  .recipients-tips {
    margin-top: 8px;
  }
}

.template-variables {
  .el-row {
    margin-bottom: 12px;
    align-items: center;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.preview-section {
  margin-top: 20px;

  .preview-card {
    background-color: #f8f9fa;

    .preview-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e9ecef;
    }

    .preview-content {
      line-height: 1.6;
      color: #495057;
      margin-bottom: 12px;
      white-space: pre-wrap;
    }

    .preview-footer {
      padding-top: 8px;
      border-top: 1px solid #e9ecef;
    }
  }
}

.template-preview-header {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.template-preview-content {
  line-height: 1.6;
  color: #606266;
  white-space: pre-wrap;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 表单样式优化
:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-textarea__inner) {
  resize: vertical;
}

:deep(.el-select .el-input__inner) {
  cursor: pointer;
}

// 响应式设计
@media (max-width: 768px) {
  .el-row {
    .el-col {
      margin-bottom: 16px;
    }
  }

  .template-variables {
    .el-row {
      .el-col:first-child {
        margin-bottom: 8px;
      }
    }
  }

  .preview-section {
    .preview-card {
      .preview-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }
    }
  }
}
</style>
