<template>
  <el-dialog
    v-model="visible"
    title="发送测试消息"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="template" class="template-test">
      <!-- 模板信息 -->
      <el-card class="template-info" shadow="never">
        <template #header>
          <span>模板信息</span>
        </template>
        <el-descriptions :column="2" border size="small">
          <el-descriptions-item label="模板名称">{{ template.name }}</el-descriptions-item>
          <el-descriptions-item label="模板类型">{{ getTypeLabel(template.type) }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 测试配置 -->
      <el-form
        ref="formRef"
        :model="testForm"
        :rules="testRules"
        label-width="120px"
      >
        <el-form-item label="接收方式" prop="receiverType">
          <el-radio-group v-model="testForm.receiverType" @change="handleReceiverTypeChange">
            <el-radio value="MANUAL">手动输入</el-radio>
            <el-radio value="SELECT">选择用户</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 手动输入接收者 -->
        <div v-if="testForm.receiverType === 'MANUAL'">
          <el-form-item
            v-if="template.type === 'EMAIL'"
            label="接收邮箱"
            prop="receiverEmail"
          >
            <el-input
              v-model="testForm.receiverEmail"
              placeholder="请输入接收邮箱"
              />
          </el-form-item>
          
          <el-form-item
            v-if="template.type === 'SMS'"
            label="接收手机"
            prop="receiverPhone"
          >
            <el-input
              v-model="testForm.receiverPhone"
              placeholder="请输入接收手机号"
              />
          </el-form-item>
        </div>

        <!-- 选择用户 -->
        <el-form-item
          v-if="testForm.receiverType === 'SELECT'"
          label="选择用户"
          prop="receiverUser"
        >
          <el-select
            v-model="testForm.receiverUser"
            placeholder="请选择用户"
            filterable
            remote
            :remote-method="searchUsers"
            :loading="userLoading"
            style="width: 100%"
          >
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="`${user.name} (${user.email || user.phone})`"
              :value="user.id"
             />
          </el-select>
        </el-form-item>

        <!-- 测试参数 -->
        <el-divider content-position="left">测试参数</el-divider>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户姓名">
              <el-input v-model="testForm.params.userName"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户部门">
              <el-input v-model="testForm.params.userDepartment"   />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="流程名称">
              <el-input v-model="testForm.params.processName"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务名称">
              <el-input v-model="testForm.params.taskName"   />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="业务键">
              <el-input v-model="testForm.params.businessKey"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="审批人">
              <el-input v-model="testForm.params.approver"   />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="自定义参数">
          <el-input
            v-model="testForm.params.customParams"
            type="textarea"
            :rows="3"
            placeholder="请输入自定义参数，格式：key1=value1,key2=value2"
            />
        </el-form-item>

        <el-form-item label="发送选项">
          <el-checkbox v-model="testForm.options.saveRecord">保存发送记录</el-checkbox>
          <el-checkbox v-model="testForm.options.realSend">真实发送（取消勾选仅模拟）</el-checkbox>
        </el-form-item>
      </el-form>

      <!-- 预览内容 -->
      <el-card class="preview-content" shadow="never">
        <template #header>
          <span>预览内容</span>
        </template>
        <div class="preview-result">
          <div class="preview-title">
            <strong>标题：</strong>{{ renderedTitle }}
          </div>
          <div class="preview-body">
            <strong>内容：</strong>
            <div class="content-text" v-html="renderedContent"></div>
          </div>
        </div>
      </el-card>

      <!-- 发送历史 -->
      <el-card v-if="sendHistory.length > 0" class="send-history" shadow="never">
        <template #header>
          <span>发送历史</span>
        </template>
        <el-table :data="sendHistory" size="small" max-height="200">
          <el-table-column prop="sendTime" label="发送时间" width="180"  />
          <el-table-column prop="receiver" label="接收者" width="200"  />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === 'SUCCESS' ? 'success' : 'danger'" size="small">
                {{ scope.row.status === 'SUCCESS' ? '成功' : '失败' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="message" label="结果信息" show-overflow-tooltip  />
        </el-table>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="sending" @click="handleSend">
          <el-icon><Message /></el-icon>
          发送测试
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Message } from '@element-plus/icons-vue'
import { employeeApi } from '@/api/employee'
import { notificationApi } from '@/api/notification'

// Props
interface Props {
  modelValue: boolean
   
  template: unknown | null
   
  params?: unknown
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  template: null,
  params: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
   
  'sent': [result: unknown]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const sending = ref(false)
const userLoading = ref(false)
const users = ref<any[]>([])
const sendHistory = ref<any[]>([])

// 测试表单
const testForm = reactive({
  receiverType: 'MANUAL',
  receiverEmail: '',
  receiverPhone: '',
  receiverUser: '',
  params: {
    userName: '张三',
    userDepartment: '技术部',
    processName: '员工入职流程',
    taskName: '填写入职信息',
    businessKey: 'EMP_2025_001',
    approver: '李四',
    customParams: ''
  },
  options: {
    saveRecord: true,
    realSend: false
  }
})

// 表单验证规则
const testRules = {
  receiverType: [{ required: true, message: '请选择接收方式', trigger: 'change' }],
  receiverEmail: [
    { required: true, message: '请输入接收邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  receiverPhone: [
    { required: true, message: '请输入接收手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  receiverUser: [{ required: true, message: '请选择用户', trigger: 'change' }]
}

// 渲染后的内容
const renderedTitle = computed(() => {
  if (!props.template) return ''
  return renderTemplate(props.template.title)
})

const renderedContent = computed(() => {
  if (!props.template) return ''
  return renderTemplate(props.template.content)
})

const formRef = ref()

// 监听props变化
watch(() => props.params, (params) => {
  if (params) {
    Object.assign(testForm.params, params)
  }
})

watch(visible, (val) => {
  if (val) {
    loadSendHistory()
  }
})

// 方法
const handleClose = () => {
  visible.value = false
}

const handleReceiverTypeChange = () => {
  testForm.receiverEmail = ''
  testForm.receiverPhone = ''
  testForm.receiverUser = ''
}

const searchUsers = async (query: string) => {
  if (!query) return
  
  userLoading.value = true
  try {
    // 调用员工API搜索用户
    const response = await employeeApi.queryEmployees({
      page: 0,
      size: 20,
      keyword: query,
      employmentStatus: 'ACTIVE' // 只搜索在职员工
    })
    
    users.value = response.content.map(employee => ({
      id: employee.id?.toString() || employee.employeeCode,
      name: employee.name,
      email: employee.email || '',
      phone: employee.phone || ''
    }))
    
    if (users.value.length === 0) {
      // 如果没有搜索到，使用模拟数据
      users.value = [
        { id: 'user1', name: 'HrHr张三', email: '<EMAIL>', phone: '13800138000' },
        { id: 'user2', name: '李四', email: '<EMAIL>', phone: '13800138001' }
      ]
    }
  } catch (__error) {
    console.error('搜索用户失败:', error)
    // 失败时使用模拟数据
    users.value = [
      { id: 'user1', name: '张三', email: '<EMAIL>', phone: '13800138000' },
      { id: 'user2', name: '李四', email: '<EMAIL>', phone: '13800138001' }
    ]
  } finally {
    userLoading.value = false
  }
}

const renderTemplate = (template: string) => {
  if (!template) return ''

  let rendered = template
    .replace(/\$\{systemName\}/g, '杭科院人事管理系统')
    .replace(/\$\{currentTime\}/g, new Date().toLocaleString())
    .replace(/\$\{currentDate\}/g, new Date().toLocaleDateString())
    .replace(/\$\{serverUrl\}/g, 'https://hr.hky.edu.cn')
    .replace(/\$\{userName\}/g, testForm.params.userName)
    .replace(/\$\{userDepartment\}/g, testForm.params.userDepartment)
    .replace(/\$\{processName\}/g, testForm.params.processName)
    .replace(/\$\{taskName\}/g, testForm.params.taskName)
    .replace(/\$\{businessKey\}/g, testForm.params.businessKey)
    .replace(/\$\{approver\}/g, testForm.params.approver)

  // 处理自定义参数
  if (testForm.params.customParams) {
    const customPairs = testForm.params.customParams.split(',')
    customPairs.forEach(pair => {
      const [key, value] = pair.split('=')
      if (key && value) {
        rendered = rendered.replace(new RegExp(`\\$\\{${key.trim()}\\}`, 'g'), value.trim())
      }
    })
  }

  return rendered.replace(/\n/g, '<br>')
}

const handleSend = async () => {
  try {
    await formRef.value.validate()
    sending.value = true

    // 构建发送数据
    const sendData = {
      templateId: props.template.id,
      templateType: props.template.type,
      receiver: getReceiver(),
      params: { ...testForm.params },
      options: { ...testForm.options }
    }

    // 调用API发送测试消息
    let result
    try {
      await notificationApi.sendNotification({
        type: props.template.type || 'NOTIFICATION',
        title: props.template.subject || props.template.title || '测试消息',
        content: renderTemplate(props.template.content || ''),
        receiverIds: [getReceiver()],
        priority: 'MEDIUM',
        link: testForm.options.realSend ? '#' : undefined
      })
      
      result = {
        success: true,
        message: '发送成功',
        messageId: `MSG_${Date.now()}`
      }
    } catch (__apiError) {
      console.error('API调用失败:', apiError)
      // 失败时使用模拟结果
      result = {
        success: Math.random() > 0.2, // 80% 成功率
        message: Math.random() > 0.2 ? '发送成功' : '发送失败：网络错误',
        messageId: `MSG_${Date.now()}`
      }
    }

    // 添加到发送历史
    sendHistory.value.unshift({
      sendTime: new Date().toLocaleString(),
      receiver: getReceiver(),
      status: result.success ? 'SUCCESS' : 'FAILED',
      message: result.message
    })

    if (result.success) {
      ElMessage.success('测试消息发送成功')
      emit('sent', result)
    } else {
      ElMessage.error(`测试消息发送失败：${result.message}`)
    }
  } catch (__error) {
    console.error('发送测试消息失败:', error)
    ElMessage.error('发送测试消息失败')
  } finally {
    sending.value = false
  }
}

const getReceiver = () => {
  if (testForm.receiverType === 'MANUAL') {
    return testForm.receiverEmail || testForm.receiverPhone
  } else {
    const user = users.value.find(u => u.id === testForm.receiverUser)
    return user ? `${user.name} (${user.email || user.phone})` : ''
  }
}

const loadSendHistory = async () => {
  try {
    // 加载发送历史（通过通知历史API获取最近的测试消息）
    const response = await notificationApi.getNotificationHistory({
      page: 0,
      pageSize: 10,
      type: 'TEST' // 假设测试消息有特殊类型标记
    })
    
    sendHistory.value = response.list.map(item => ({
      sendTime: new Date(item.timestamp).toLocaleString(),
      receiver: item.receiverId || '',
      status: item.read ? 'SUCCESS' : 'PENDING',
      message: item.data.title || ''
    }))
  } catch (__error) {
    console.error('加载发送历史失败:', error)
    // 失败时清空历史
    sendHistory.value = []
  }
}

// 辅助方法
const getTypeLabel = (type: string) => {
  switch (type) {
    case 'EMAIL': return '邮件模板'
    case 'SMS': return '短信模板'
    case 'INTERNAL': return '站内信模板'
    case 'WECHAT': return '微信模板'
    case 'DINGTALK': return '钉钉模板'
    default: return type
  }
}
</script>

<style scoped>
.template-test {
  padding: 10px 0;
}

.template-info,
.preview-content,
.send-history {
  margin-bottom: 20px;
}

.preview-result {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.preview-title {
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #dee2e6;
}

.preview-body {
  margin-top: 12px;
}

.content-text {
  margin-top: 8px;
  line-height: 1.6;
  padding: 8px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.dialog-footer {
  text-align: right;
}
</style>
