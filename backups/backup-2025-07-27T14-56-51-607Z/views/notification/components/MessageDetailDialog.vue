<template>
  <el-dialog
    v-model="visible"
    :title="message?.title || '消息详情'"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="message" class="message-detail">
      <!-- 消息头部信息 -->
      <div class="message-header">
        <div class="message-meta">
          <el-tag :type="getTypeTagType(message.type)" size="small">
            {{ getTypeLabel(message.type) }}
          </el-tag>
          <el-tag :type="getPriorityTagType(message.priority)" size="small">
            {{ getPriorityLabel(message.priority) }}
          </el-tag>
          <el-tag :type="getStatusTagType(message.status)" size="small">
            {{ getStatusLabel(message.status) }}
          </el-tag>
        </div>
        <div class="message-time">
          <el-text size="small" type="info">
            发送时间：{{ message.sendTime }}
          </el-text>
        </div>
      </div>

      <!-- 发送者信息 -->
      <div class="sender-info">
        <el-descriptions :column="2" border size="small">
          <el-descriptions-item label="发送者">{{ message.sender }}</el-descriptions-item>
          <el-descriptions-item label="发送渠道">{{ getChannelLabel(message.channel) }}</el-descriptions-item>
          <el-descriptions-item label="接收者">{{ message.receiver }}</el-descriptions-item>
          <el-descriptions-item label="业务类型">{{ message.businessType }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 消息内容 -->
      <div class="message-content">
        <h4>消息内容</h4>
        <div class="content-body" v-html="message.content"></div>
      </div>

      <!-- 附件信息 -->
      <div v-if="message.attachments && message.attachments.length > 0" class="attachments">
        <h4>附件</h4>
        <div class="attachment-list">
          <div
            v-for="attachment in message.attachments"
            :key="attachment.id"
            class="attachment-item"
          >
            <el-icon><Document /></el-icon>
            <span class="attachment-name">{{ attachment.name }}</span>
            <span class="attachment-size">({{ formatFileSize(attachment.size) }})</span>
            <el-button size="small" type="primary" link @click="downloadAttachment(attachment)">
              下载
            </el-button>
          </div>
        </div>
      </div>

      <!-- 操作记录 -->
      <div v-if="message.operations && message.operations.length > 0" class="operations">
        <h4>操作记录</h4>
        <el-timeline>
          <el-timeline-item
            v-for="operation in message.operations"
            :key="operation.id"
            :timestamp="operation.time"
            placement="top"
          >
            <div class="operation-content">
              <span class="operation-user">{{ operation.user }}</span>
              <span class="operation-action">{{ operation.action }}</span>
              <span v-if="operation.remark" class="operation-remark">{{ operation.remark }}</span>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>

      <!-- 相关消息 -->
      <div v-if="message.relatedMessages && message.relatedMessages.length > 0" class="related-messages">
        <h4>相关消息</h4>
        <div class="related-list">
          <div
            v-for="related in message.relatedMessages"
            :key="related.id"
            class="related-item"
            @click="viewRelatedMessage(related)"
          >
            <div class="related-title">{{ related.title }}</div>
            <div class="related-time">{{ related.sendTime }}</div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button v-if="!message?.read" type="primary" @click="markAsRead">
          标记为已读
        </el-button>
        <el-button v-if="message?.type === 'WORKFLOW'" type="success" @click="handleWorkflow">
          处理工作流
        </el-button>
        <el-button type="info" @click="replyMessage">
          回复
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'MessageDetailDialog'
})
 
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Document } from '@element-plus/icons-vue'
import { notificationApi } from '@/api/notification'

// Props
interface Props {
  modelValue: boolean
   
  message: unknown | null
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  message: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'read': [messageId: string]
   
  'reply': [message: unknown]
   
  'workflow': [message: unknown]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 方法
const handleClose = () => {
  visible.value = false
}

const markAsRead = () => {
  if (props.message) {
    emit('read', props.message.id)
    ElMessage.success('已标记为已读')
  }
}

const replyMessage = () => {
  if (props.message) {
    emit('reply', props.message)
    handleClose()
  }
}

const handleWorkflow = () => {
  if (props.message) {
    emit('workflow', props.message)
    handleClose()
  }
}

   
const downloadAttachment = async (attachment: unknown) => {
  try {
    // 调用API下载附件
    const blob = await notificationApi.downloadAttachment(message.value.id, attachment.id)
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = attachment.name
    link.click()
    
    // 清理
    window.URL.revokeObjectURL(url)
    
    ElMessage.success(`附件 ${attachment.name} 下载成功`)
  } catch (__error) {
    console.error('下载附件失败:', error)
    ElMessage.error(`下载附件失败: ${attachment.name}`)
  }
}

   
const viewRelatedMessage = async (related: unknown) => {
  try {
    // 获取相关消息详情
    const relatedMessages = await notificationApi.getRelatedMessages(message.value.id)
    const relatedMessage = relatedMessages.find(msg => msg.id === related.id)
    
    if (relatedMessage) {
      // 更新当前显示的消息
      message.value = relatedMessage
      ElMessage.success('已切换到相关消息')
    } else {
      ElMessage.warning('未找到相关消息')
    }
  } catch (__error) {
    console.error('查看相关消息失败:', error)
    ElMessage.error('查看相关消息失败')
  }
}

// 辅助方法
const getTypeTagType = (type: string) => {
  switch (type) {
    case 'SYSTEM': return 'primary'
    case 'WORKFLOW': return 'success'
    case 'BUSINESS': return 'warning'
    case 'NOTIFICATION': return 'info'
    default: return ''
  }
}

const getTypeLabel = (type: string) => {
  switch (type) {
    case 'SYSTEM': return '系统消息'
    case 'WORKFLOW': return '工作流消息'
    case 'BUSINESS': return '业务消息'
    case 'NOTIFICATION': return '通知消息'
    default: return type
  }
}

const getPriorityTagType = (priority: string) => {
  switch (priority) {
    case 'HIGH': return 'danger'
    case 'MEDIUM': return 'warning'
    case 'LOW': return 'info'
    default: return ''
  }
}

const getPriorityLabel = (priority: string) => {
  switch (priority) {
    case 'HIGH': return '高优先级'
    case 'MEDIUM': return '中优先级'
    case 'LOW': return '低优先级'
    default: return priority
  }
}

const getStatusTagType = (status: string) => {
  switch (status) {
    case 'SENT': return 'success'
    case 'PENDING': return 'warning'
    case 'FAILED': return 'danger'
    case 'READ': return 'info'
    default: return ''
  }
}

const getStatusLabel = (status: string) => {
  switch (status) {
    case 'SENT': return '已发送'
    case 'PENDING': return '待发送'
    case 'FAILED': return '发送失败'
    case 'READ': return '已读'
    default: return status
  }
}

const getChannelLabel = (channel: string) => {
  switch (channel) {
    case 'INTERNAL': return '站内信'
    case 'EMAIL': return '邮件'
    case 'SMS': return '短信'
    case 'WECHAT': return '微信'
    case 'DINGTALK': return '钉钉'
    default: return channel
  }
}

const formatFileSize = (size: number) => {
  if (size < 1024) return `${size}B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`
  return `${(size / 1024 / 1024).toFixed(1)}MB`
}
</script>

<style scoped>
.message-detail {
  padding: 10px 0;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.message-meta {
  display: flex;
  gap: 8px;
}

.sender-info {
  margin-bottom: 20px;
}

.message-content {
  margin-bottom: 20px;
}

.message-content h4 {
  margin-bottom: 12px;
  color: #303133;
  font-size: 16px;
}

.content-body {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  line-height: 1.6;
  word-wrap: break-word;
}

.attachments {
  margin-bottom: 20px;
}

.attachments h4 {
  margin-bottom: 12px;
  color: #303133;
  font-size: 16px;
}

.attachment-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.attachment-name {
  flex: 1;
  color: #303133;
}

.attachment-size {
  color: #909399;
  font-size: 12px;
}

.operations {
  margin-bottom: 20px;
}

.operations h4 {
  margin-bottom: 12px;
  color: #303133;
  font-size: 16px;
}

.operation-content {
  display: flex;
  gap: 8px;
  align-items: center;
}

.operation-user {
  font-weight: 600;
  color: #409eff;
}

.operation-action {
  color: #303133;
}

.operation-remark {
  color: #909399;
  font-size: 12px;
}

.related-messages {
  margin-bottom: 20px;
}

.related-messages h4 {
  margin-bottom: 12px;
  color: #303133;
  font-size: 16px;
}

.related-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.related-item {
  padding: 10px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  cursor: pointer;
  transition: all 0.3s;
}

.related-item:hover {
  background-color: #e9ecef;
  border-color: #409eff;
}

.related-title {
  color: #303133;
  font-weight: 500;
  margin-bottom: 4px;
}

.related-time {
  color: #909399;
  font-size: 12px;
}

.dialog-footer {
  text-align: right;
}
</style>
