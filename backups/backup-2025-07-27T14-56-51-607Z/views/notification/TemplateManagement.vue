<template>
  <div class="template-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><Document /></el-icon>
          消息模板管理
        </h1>
        <p class="page-description">管理各种通知渠道的消息模板，支持变量配置和预览功能</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新建模板
        </el-button>
        <el-button @click="showImportDialog = true">
          <el-icon><Upload /></el-icon>
          批量导入
        </el-button>
        <el-button @click="exportSelected" :disabled="!hasSelection">
          <el-icon><Download /></el-icon>
          导出选中
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card total-templates">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ templateStats.total || 0 }}</div>
                <div class="stat-label">总模板数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card active-templates">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ templateStats.active || 0 }}</div>
                <div class="stat-label">启用模板</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card email-templates">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Message /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ templateStats.email || 0 }}</div>
                <div class="stat-label">邮件模板</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card sms-templates">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><ChatDotRound /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ templateStats.sms || 0 }}</div>
                <div class="stat-label">短信模板</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <el-card class="main-content">
      <!-- 搜索和过滤 -->
      <div class="search-section">
        <el-form :model="searchForm" inline>
          <el-form-item label="模板名称">
            <el-input
              v-model="searchForm.name"
              placeholder="请输入模板名称"
              clearable
              @keyup.enter="handleSearch"
              />
          </el-form-item>
          <el-form-item label="模板类型">
            <el-select v-model="searchForm.type" placeholder="请选择类型" clearable>
              <el-option label="邮件模板" value="EMAIL"  />
              <el-option label="短信模板" value="SMS"  />
              <el-option label="站内信模板" value="INTERNAL"  />
              <el-option label="微信模板" value="WECHAT"  />
              <el-option label="钉钉模板" value="DINGTALK"  />
            </el-select>
          </el-form-item>
          <el-form-item label="模板分类">
            <el-select v-model="searchForm.category" placeholder="请选择分类" clearable>
              <el-option label="系统通知" value="SYSTEM"  />
              <el-option label="业务通知" value="BUSINESS"  />
              <el-option label="营销推广" value="MARKETING"  />
              <el-option label="安全提醒" value="SECURITY"  />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="启用" value="ACTIVE"  />
              <el-option label="禁用" value="DISABLED"  />
              <el-option label="草稿" value="DRAFT"  />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 模板表格 -->
      <el-table
        v-loading="loading"
        :data="templates"
        @selection-change="handleSelectionChange"
        stripe
        border
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column label="模板信息" min-width="250">
          <template #default="{ row }">
            <div class="template-info">
              <div class="template-header">
                <span class="template-name">{{ row.name }}</span>
                <el-tag :type="getTypeColor(row.type)" size="small">
                  {{ getTypeName(row.type) }}
                </el-tag>
              </div>
              <div class="template-meta">
                <span class="template-code">编码: {{ row.code }}</span>
                <span class="template-category">分类: {{ getCategoryName(row.category) }}</span>
              </div>
              <div class="template-description" v-if="row.description">
                {{ truncateText(row.description, 80) }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="subject" label="主题/标题" width="200">
          <template #default="{ row }">
            <span class="template-subject">{{ row.subject || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="内容预览" width="300">
          <template #default="{ row }">
            <div class="content-preview">
              {{ truncateText(row.content, 100) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="variables" label="变量" width="150">
          <template #default="{ row }">
            <el-tag 
              v-for="variable in row.variables?.slice(0, 3)" 
              :key="variable"
              size="small"
              style="margin-right: 4px; margin-bottom: 4px;"
            >
              {{ variable }}
            </el-tag>
            <el-tag v-if="row.variables?.length > 3" size="small" type="info">
              +{{ row.variables.length - 3 }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.updateTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-button size="small" @click="previewTemplate(row)">
                <el-icon><View /></el-icon>
                预览
              </el-button>
              <el-button size="small" @click="editTemplate(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
              <el-dropdown @command="(command: unknown) => handleMoreAction(command, row)">
                <el-button size="small">
                  更多<el-icon><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="copy">复制模板</el-dropdown-item>
                    <el-dropdown-item command="test">发送测试</el-dropdown-item>
                    <el-dropdown-item command="export">导出模板</el-dropdown-item>
                    <el-dropdown-item command="enable" v-if="row.status !== 'ACTIVE'">启用</el-dropdown-item>
                    <el-dropdown-item command="disable" v-if="row.status === 'ACTIVE'">禁用</el-dropdown-item>
                    <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 模板创建/编辑对话框 -->
    <TemplateFormDialog
      v-model="showCreateDialog"
      :mode="selectedTemplate ? 'edit' : 'add'"
      :template="selectedTemplate"
      @submit="handleFormSuccess"
    />

    <!-- 模板预览对话框 -->
    <TemplatePreviewDialog
      v-model="showPreviewDialog"
      :template="selectedTemplate"
    />

    <!-- 模板测试对话框 -->
    <TemplateTestDialog
      v-model="showTestDialog"
      :template="selectedTemplate"
      @success="handleTestSuccess"
    />

    <!-- 批量导入对话框 -->
    <TemplateImportDialog
      v-model="showImportDialog"
      @success="handleImportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Document, Plus, Upload, Download, CircleCheck, Message,
  ChatDotRound, Search, Refresh, View, Edit, ArrowDown
} from '@element-plus/icons-vue'
import TemplateFormDialog from './components/TemplateFormDialog.vue'
import TemplatePreviewDialog from './components/TemplatePreviewDialog.vue'
import TemplateTestDialog from './components/TemplateTestDialog.vue'
import TemplateImportDialog from './components/TemplateImportDialog.vue'
import { notificationApi } from '@/api/notification'
import { formatDateTime, truncateText } from '@/utils/format'

// 响应式数据
const loading = ref(false)
const templates = ref<any[]>([])
const selectedRows = ref<any[]>([])
const selectedTemplate = ref<unknown>(null)

// 对话框显示状态
const showCreateDialog = ref(false)
const showPreviewDialog = ref(false)
const showTestDialog = ref(false)
const showImportDialog = ref(false)

// 搜索表单
const searchForm = reactive({
  name: '',
  type: '',
  category: '',
  status: ''
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 模板统计
const templateStats = ref({
  total: 0,
  active: 0,
  email: 0,
  sms: 0
})

// 计算属性
const hasSelection = computed(() => selectedRows.value.length > 0)

// 生命周期
onMounted(() => {
  loadTemplates()
  loadTemplateStats()
})

// 方法
const loadTemplates = async () => {
  try {
    loading.value = true
    
    // 调用模板API
    const params = {
      page: pagination.currentPage - 1,
      pageSize: pagination.pageSize,
      type: searchForm.type,
      category: searchForm.category,
      status: searchForm.status,
      keyword: searchForm.keyword
    }
    
    const response = await notificationApi.getTemplates(params)
    templates.value = response.list
    pagination.total = response.total
    
    if (templates.value.length === 0) {
      // 如果没有数据，使用模拟数据
      templates.value = [
        {
          id: '1',
          name: 'HrHr用户注册欢迎邮件',
          code: 'USER_REGISTER_WELCOME',
          type: 'EMAIL',
          category: 'SYSTEM',
          subject: '欢迎加入杭科院人事管理系统',
          content: '亲爱的${userName}，欢迎您注册杭科院人事管理系统...',
          variables: ['userName', 'loginUrl', 'supportEmail'],
          status: 'ACTIVE',
          description: '用户注册成功后发送的欢迎邮件模板',
          updateTime: '2025-06-18 10:00:00'
        },
        {
          id: '2',
          name: '任务分配通知',
          code: 'TASK_ASSIGN_NOTIFICATION',
          type: 'INTERNAL',
          category: 'BUSINESS',
          subject: '您有新的任务待处理',
          content: '您好${assigneeName}，您有一个新的任务"${taskName}"需要处理...',
          variables: ['assigneeName', 'taskName', 'taskUrl', 'dueDate'],
          status: 'ACTIVE',
          description: '任务分配时发送的通知模板',
          updateTime: '2025-06-18 11:00:00'
        }
      ]
      pagination.total = templates.value.length
    }
  } catch (__error) {
    ElMessage.error('加载模板列表失败')
    console.error('加载模板失败:', error)
    // 失败时使用模拟数据
    templates.value = [
      {
        id: '1',
        name: '用户注册欢迎邮件',
        code: 'USER_REGISTER_WELCOME',
        type: 'EMAIL',
        category: 'SYSTEM',
        subject: '欢迎加入杭科院人事管理系统',
        content: '亲爱的${userName}，欢迎您注册杭科院人事管理系统...',
        variables: ['userName', 'loginUrl', 'supportEmail'],
        status: 'ACTIVE',
        description: '用户注册成功后发送的欢迎邮件模板',
        updateTime: '2025-06-18 10:00:00'
      }
    ]
    pagination.total = 1
  } finally {
    loading.value = false
  }
}

const loadTemplateStats = async () => {
  try {
    // 调用统计API
    const stats = await notificationApi.getTemplateStats()
    templateStats.value = stats
  } catch (__error) {
    console.error('加载模板统计失败:', error)
    // 失败时使用模拟数据
    templateStats.value = {
      total: 25,
      active: 20,
      email: 12,
      sms: 8,
      internal: 6,
      wechat: 2
    }
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadTemplates()
}

const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    type: '',
    category: '',
    status: ''
  })
  pagination.page = 1
  loadTemplates()
}

   
const handleSelectionChange = (selection: unknown[]) => {
  selectedRows.value = selection
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  loadTemplates()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadTemplates()
}

   
const previewTemplate = (template: unknown) => {
  selectedTemplate.value = template
  showPreviewDialog.value = true
}

   
const editTemplate = (template: unknown) => {
  selectedTemplate.value = { ...template }
  showCreateDialog.value = true
}

   
const handleMoreAction = async (command: string, template: unknown) => {
  selectedTemplate.value = template
  
  switch (command) {
    case 'copy':
      await copyTemplate(template)
      break
    case 'test':
      showTestDialog.value = true
      break
    case 'export':
      await exportTemplate(template)
      break
    case 'enable':
      await toggleTemplateStatus(template, 'ACTIVE')
      break
    case 'disable':
      await toggleTemplateStatus(template, 'DISABLED')
      break
    case 'delete':
      await deleteTemplate(template)
      break
  }
}

   
const copyTemplate = async (template: unknown) => {
  try {
    ElMessage.success('模板复制成功')
    loadTemplates()
    loadTemplateStats()
  } catch (__error) {
    ElMessage.error('模板复制失败')
  }
}

   
const exportTemplate = async (template: unknown) => {
  try {
    ElMessage.success('导出成功')
  } catch (__error) {
    ElMessage.error('导出失败')
  }
}

   
const toggleTemplateStatus = async (template: unknown, status: string) => {
  try {
    template.status = status
    ElMessage.success('状态更新成功')
    loadTemplateStats()
  } catch (__error) {
    ElMessage.error('状态更新失败')
  }
}

   
const deleteTemplate = async (template: unknown) => {
  try {
    await ElMessageBox.confirm('确定要删除该模板吗？此操作不可恢复！', '确认删除', {
      type: 'warning'
    })
    
    ElMessage.success('模板删除成功')
    loadTemplates()
    loadTemplateStats()
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('模板删除失败')
    }
  }
}

const exportSelected = async () => {
  if (!hasSelection.value) {
    ElMessage.warning('请先选择要导出的模板')
    return
  }
  
  try {
    ElMessage.success('批量导出成功')
  } catch (__error) {
    ElMessage.error('导出失败')
  }
}

const handleFormSuccess = () => {
  loadTemplates()
  loadTemplateStats()
}

const handleTestSuccess = () => {
  ElMessage.success('测试消息发送成功')
}

const handleImportSuccess = () => {
  loadTemplates()
  loadTemplateStats()
}

// 辅助方法
const getTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    EMAIL: '邮件',
    SMS: '短信',
    INTERNAL: '站内信',
    WECHAT: '微信',
    DINGTALK: '钉钉'
  }
  return typeMap[type] || type
}

const getTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    EMAIL: 'success',
    SMS: 'warning',
    INTERNAL: 'primary',
    WECHAT: 'success',
    DINGTALK: 'primary'
  }
  return colorMap[type] || ''
}

const getCategoryName = (category: string) => {
  const categoryMap: Record<string, string> = {
    SYSTEM: '系统通知',
    BUSINESS: '业务通知',
    MARKETING: '营销推广',
    SECURITY: '安全提醒'
  }
  return categoryMap[category] || category
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    ACTIVE: '启用',
    DISABLED: '禁用',
    DRAFT: '草稿'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    ACTIVE: 'success',
    DISABLED: 'info',
    DRAFT: 'warning'
  }
  return typeMap[status] || ''
}
</script>

<style scoped lang="scss">
.template-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .header-content {
      .page-title {
        display: flex;
        align-items: center;
        font-size: 24px;
        font-weight: 600;
        color: #303133;
        margin: 0 0 8px 0;

        .el-icon {
          margin-right: 8px;
          color: #409eff;
        }
      }

      .page-description {
        color: #909399;
        margin: 0;
        font-size: 14px;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }
  }

  .stats-cards {
    margin-bottom: 20px;

    .stat-card {
      border: none;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      transition: transform 0.2s ease;

      &:hover {
        transform: translateY(-2px);
      }

      :deep(.el-card__body) {
        padding: 20px;
      }

      .stat-content {
        display: flex;
        align-items: center;

        .stat-icon {
          width: 48px;
          height: 48px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;

          .el-icon {
            font-size: 24px;
            color: white;
          }
        }

        .stat-info {
          .stat-value {
            font-size: 28px;
            font-weight: 600;
            color: #303133;
            line-height: 1;
            margin-bottom: 4px;
          }

          .stat-label {
            font-size: 14px;
            color: #909399;
          }
        }
      }

      &.total-templates .stat-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }

      &.active-templates .stat-icon {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      }

      &.email-templates .stat-icon {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }

      &.sms-templates .stat-icon {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }
    }
  }

  .main-content {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .search-section {
      padding: 20px;
      border-bottom: 1px solid #ebeef5;
      background-color: #fafafa;

      .el-form {
        margin-bottom: 0;
      }
    }

    .template-info {
      .template-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;

        .template-name {
          font-weight: 500;
          color: #303133;
          flex: 1;
          margin-right: 8px;
        }
      }

      .template-meta {
        display: flex;
        gap: 12px;
        margin-bottom: 4px;
        font-size: 12px;
        color: #909399;

        .template-code {
          color: #409eff;
        }
      }

      .template-description {
        font-size: 12px;
        color: #606266;
        line-height: 1.4;
      }
    }

    .template-subject {
      font-weight: 500;
      color: #303133;
    }

    .content-preview {
      font-size: 12px;
      color: #606266;
      line-height: 1.4;
      max-height: 60px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
    }

    .pagination-wrapper {
      padding: 20px;
      display: flex;
      justify-content: center;
      border-top: 1px solid #ebeef5;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .template-management {
    padding: 10px;

    .page-header {
      flex-direction: column;
      gap: 16px;

      .header-actions {
        width: 100%;
        justify-content: flex-start;
        flex-wrap: wrap;
      }
    }

    .stats-cards {
      .el-col {
        margin-bottom: 16px;
      }
    }

    .search-section {
      .el-form {
        .el-form-item {
          width: 100%;
          margin-bottom: 16px;

          .el-input,
          .el-select {
            width: 100%;
          }
        }
      }
    }

    .template-info {
      .template-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
      }

      .template-meta {
        flex-direction: column;
        gap: 2px;
      }
    }
  }
}
</style>
