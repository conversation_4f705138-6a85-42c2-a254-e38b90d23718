<template>
  <div class="notification-statistics">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><DataAnalysis /></el-icon>
          通知统计分析
        </h1>
        <p class="page-description">分析通知发送情况、成功率和用户行为数据</p>
      </div>
      <div class="header-actions">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          @change="handleDateRangeChange"
         />
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button @click="exportReport">
          <el-icon><Download /></el-icon>
          导出报告
        </el-button>
      </div>
    </div>

    <!-- 核心指标卡片 -->
    <div class="metrics-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="metric-card total-sent">
            <div class="metric-content">
              <div class="metric-icon">
                <el-icon><Promotion /></el-icon>
              </div>
              <div class="metric-info">
                <div class="metric-value">{{ formatNumber(metrics.totalSent) }}</div>
                <div class="metric-label">总发送量</div>
                <div class="metric-change" :class="getChangeClass(metrics.sentChange)">
                  <el-icon><ArrowUp v-if="metrics.sentChange > 0" /><ArrowDown v-else /></el-icon>
                  {{ Math.abs(metrics.sentChange) }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="metric-card success-rate">
            <div class="metric-content">
              <div class="metric-icon">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="metric-info">
                <div class="metric-value">{{ metrics.successRate }}%</div>
                <div class="metric-label">成功率</div>
                <div class="metric-change" :class="getChangeClass(metrics.rateChange)">
                  <el-icon><ArrowUp v-if="metrics.rateChange > 0" /><ArrowDown v-else /></el-icon>
                  {{ Math.abs(metrics.rateChange) }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="metric-card avg-response">
            <div class="metric-content">
              <div class="metric-icon">
                <el-icon><Timer /></el-icon>
              </div>
              <div class="metric-info">
                <div class="metric-value">{{ metrics.avgResponseTime }}ms</div>
                <div class="metric-label">平均响应时间</div>
                <div class="metric-change" :class="getChangeClass(-metrics.responseChange)">
                  <el-icon><ArrowUp v-if="metrics.responseChange < 0" /><ArrowDown v-else /></el-icon>
                  {{ Math.abs(metrics.responseChange) }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="metric-card active-users">
            <div class="metric-content">
              <div class="metric-icon">
                <el-icon><User /></el-icon>
              </div>
              <div class="metric-info">
                <div class="metric-value">{{ formatNumber(metrics.activeUsers) }}</div>
                <div class="metric-label">活跃用户</div>
                <div class="metric-change" :class="getChangeClass(metrics.userChange)">
                  <el-icon><ArrowUp v-if="metrics.userChange > 0" /><ArrowDown v-else /></el-icon>
                  {{ Math.abs(metrics.userChange) }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-section">
      <!-- 发送趋势图 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>发送趋势分析</span>
              <el-radio-group v-model="trendPeriod" size="small" @change="loadTrendData">
                <el-radio-button label="7d">7天</el-radio-button>
                <el-radio-button label="30d">30天</el-radio-button>
                <el-radio-button label="90d">90天</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div ref="trendChartRef" class="chart-container" v-loading="trendLoading"></div>
        </el-card>
      </el-col>

      <!-- 渠道分布图 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>渠道分布</span>
          </template>
          <div ref="channelChartRef" class="chart-container" v-loading="channelLoading"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-section">
      <!-- 成功率对比图 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>渠道成功率对比</span>
          </template>
          <div ref="successChartRef" class="chart-container" v-loading="successLoading"></div>
        </el-card>
      </el-col>

      <!-- 用户行为分析 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>用户行为分析</span>
          </template>
          <div ref="behaviorChartRef" class="chart-container" v-loading="behaviorLoading"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细数据表格 -->
    <el-card class="data-table-card">
      <template #header>
        <div class="table-header">
          <span>详细数据</span>
          <div class="table-controls">
            <el-select v-model="tableGroupBy" size="small" @change="loadTableData">
              <el-option label="按日期" value="date"  />
              <el-option label="按渠道" value="channel"  />
              <el-option label="按模板" value="template"  />
              <el-option label="按用户" value="user"  />
            </el-select>
            <el-button size="small" @click="exportTableData">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="tableLoading"
        :data="tableData"
        stripe
        border
        show-summary
        :summary-method="getSummaries"
      >
        <el-table-column 
          :prop="getTableColumnProp('name')" 
          :label="getTableColumnLabel()" 
          min-width="150"
         />
        <el-table-column prop="totalSent" label="发送总数" width="120" align="right">
          <template #default="{ row }">
            {{ formatNumber(row.totalSent) }}
          </template>
        </el-table-column>
        <el-table-column prop="successCount" label="成功数" width="120" align="right">
          <template #default="{ row }">
            {{ formatNumber(row.successCount) }}
          </template>
        </el-table-column>
        <el-table-column prop="failureCount" label="失败数" width="120" align="right">
          <template #default="{ row }">
            {{ formatNumber(row.failureCount) }}
          </template>
        </el-table-column>
        <el-table-column prop="successRate" label="成功率" width="120" align="right">
          <template #default="{ row }">
            <span :class="getSuccessRateClass(row.successRate)">
              {{ row.successRate }}%
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="avgResponseTime" label="平均响应时间" width="140" align="right">
          <template #default="{ row }">
            {{ row.avgResponseTime }}ms
          </template>
        </el-table-column>
        <el-table-column prop="cost" label="成本" width="120" align="right" v-if="tableGroupBy === 'channel'">
          <template #default="{ row }">
            ¥{{ (row.cost || 0).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewDetails(row)">
              <el-icon><View /></el-icon>
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="tablePagination.page"
          v-model:page-size="tablePagination.size"
          :total="tablePagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleTableSizeChange"
          @current-change="handleTableCurrentChange"
         />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="详细信息"
      width="800px"
    >
      <div v-if="selectedDetail" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="名称">
            {{ selectedDetail.name }}
          </el-descriptions-item>
          <el-descriptions-item label="类型">
            {{ getDetailType() }}
          </el-descriptions-item>
          <el-descriptions-item label="发送总数">
            {{ formatNumber(selectedDetail.totalSent) }}
          </el-descriptions-item>
          <el-descriptions-item label="成功数">
            {{ formatNumber(selectedDetail.successCount) }}
          </el-descriptions-item>
          <el-descriptions-item label="失败数">
            {{ formatNumber(selectedDetail.failureCount) }}
          </el-descriptions-item>
          <el-descriptions-item label="成功率">
            {{ selectedDetail.successRate }}%
          </el-descriptions-item>
          <el-descriptions-item label="平均响应时间">
            {{ selectedDetail.avgResponseTime }}ms
          </el-descriptions-item>
          <el-descriptions-item label="最后更新">
            {{ formatDateTime(selectedDetail.lastUpdate) }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 错误分析 -->
        <div v-if="selectedDetail.errors?.length" class="error-analysis">
          <h4>错误分析</h4>
          <el-table :data="selectedDetail.errors" size="small">
            <el-table-column prop="errorCode" label="错误码" width="120"  />
            <el-table-column prop="errorMessage" label="错误信息"  />
            <el-table-column prop="count" label="次数" width="80" align="right"  />
            <el-table-column prop="percentage" label="占比" width="80" align="right">
              <template #default="{ row }">
                {{ row.percentage }}%
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  DataAnalysis, Refresh, Download, Promotion, CircleCheck,
  Timer, User, ArrowUp, ArrowDown, View
} from '@element-plus/icons-vue'
import { notificationApi } from '@/api/notification'
import { formatDateTime, formatNumber } from '@/utils/format'

// 响应式数据
const dateRange = ref<[string, string] | null>(null)
const trendPeriod = ref('7d')
const tableGroupBy = ref('date')
const showDetailDialog = ref(false)
const selectedDetail = ref<unknown>(null)

// 加载状态
const trendLoading = ref(false)
const channelLoading = ref(false)
const successLoading = ref(false)
const behaviorLoading = ref(false)
const tableLoading = ref(false)

// 图表引用
const trendChartRef = ref<HTMLElement>()
const channelChartRef = ref<HTMLElement>()
const successChartRef = ref<HTMLElement>()
const behaviorChartRef = ref<HTMLElement>()

// 数据
const metrics = reactive({
  totalSent: 125680,
  successRate: 94.5,
  avgResponseTime: 245,
  activeUsers: 8520,
  sentChange: 12.5,
  rateChange: 2.3,
  responseChange: -8.1,
  userChange: 15.2
})

const tableData = ref<any[]>([])
const tablePagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 图表实例
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
let trendChart: unknown = null
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
let channelChart: unknown = null
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
let successChart: unknown = null
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
let behaviorChart: unknown = null

// 生命周期
onMounted(async () => {
  // 设置默认日期范围（最近7天）
  const endDate = new Date()
  const startDate = new Date()
  startDate.setDate(endDate.getDate() - 7)
  
  dateRange.value = [
    startDate.toISOString().split('T')[0],
    endDate.toISOString().split('T')[0]
  ]

  await loadAllData()
  await nextTick()
  initCharts()
})

onUnmounted(() => {
  destroyCharts()
})

// 方法
const loadAllData = async () => {
  await Promise.all([
    loadMetrics(),
    loadTrendData(),
    loadChannelData(),
    loadSuccessData(),
    loadBehaviorData(),
    loadTableData()
  ])
}

const loadMetrics = async () => {
  try {
    const [startDate, endDate] = dateRange.value || []
    if (!startDate || !endDate) return
    
    const stats = await notificationApi.getSendStatistics(startDate, endDate)
    Object.assign(metrics, {
      totalSent: stats.totalSent || 0,
      successRate: stats.successRate || 0,
      avgResponseTime: stats.avgResponseTime || 0,
      activeUsers: stats.activeUsers || 0,
      sentChange: stats.sentChange || 0,
      rateChange: stats.rateChange || 0,
      responseChange: stats.responseChange || 0,
      userChange: stats.userChange || 0
    })
  } catch (__error) {
    console.error('加载指标失败:', error)
  }
}

const loadTrendData = async () => {
  try {
    trendLoading.value = true
    const [startDate, endDate] = dateRange.value || []
    if (!startDate || !endDate) return
    
    const granularity = trendPeriod.value === '7d' ? 'day' : trendPeriod.value === '30d' ? 'day' : 'week'
    const trendData = await notificationApi.getTrendStatistics(startDate, endDate, granularity)
    
    // 更新趋势图表
    if (trendChart && trendData?.length) {
      updateTrendChart(trendData)
    }
  } catch (__error) {
    console.error('加载趋势数据失败:', error)
  } finally {
    trendLoading.value = false
  }
}

const loadChannelData = async () => {
  try {
    channelLoading.value = true
    const [startDate, endDate] = dateRange.value || []
    if (!startDate || !endDate) return
    
    const channelData = await notificationApi.getChannelStatistics(startDate, endDate)
    
    // 更新渠道分布图
    if (channelChart && channelData?.length) {
      updateChannelChart(channelData)
    }
  } catch (__error) {
    console.error('加载渠道数据失败:', error)
  } finally {
    channelLoading.value = false
  }
}

const loadSuccessData = async () => {
  try {
    successLoading.value = true
    
    // 获取各渠道成功率
    const channels = ['EMAIL', 'SMS', 'INTERNAL', 'WECHAT', 'DINGTALK']
    const [startDate, endDate] = dateRange.value || []
    
    const successRates = await Promise.all(
      channels.map(channel => 
        notificationApi.getMessageSuccessRate(channel, startDate, endDate)
      )
    )
    
    // 更新成功率对比图
    if (successChart) {
      updateSuccessChart(successRates)
    }
  } catch (__error) {
    console.error('加载成功率数据失败:', error)
  } finally {
    successLoading.value = false
  }
}

const loadBehaviorData = async () => {
  try {
    behaviorLoading.value = true
    const [startDate, endDate] = dateRange.value || []
    if (!startDate || !endDate) return
    
    const userActivity = await notificationApi.getUserActivityStatistics(startDate, endDate)
    
    // 更新用户行为图
    if (behaviorChart && userActivity?.length) {
      updateBehaviorChart(userActivity)
    }
  } catch (__error) {
    console.error('加载用户行为数据失败:', error)
  } finally {
    behaviorLoading.value = false
  }
}

const loadTableData = async () => {
  try {
    tableLoading.value = true
    const [startDate, endDate] = dateRange.value || []
    if (!startDate || !endDate) return
    
    const params = {
      startDate,
      endDate,
      groupBy: tableGroupBy.value,
      page: tablePagination.page,
      size: tablePagination.size
    }
    
    const result = await notificationApi.queryMessages(params)
    tableData.value = result.records || []
    tablePagination.total = result.total || 0
  } catch (__error) {
    console.error('加载表格数据失败:', error)
    ElMessage.error('加载表格数据失败')
  } finally {
    tableLoading.value = false
  }
}

const initCharts = async () => {
  try {
    // 动态导入ECharts
    const echarts = await import('echarts')
    
    // 初始化趋势图
    if (trendChartRef.value) {
      trendChart = echarts.init(trendChartRef.value)
      updateTrendChart()
    }
    
    // 初始化渠道分布图
    if (channelChartRef.value) {
      channelChart = echarts.init(channelChartRef.value)
      updateChannelChart()
    }
    
    // 初始化成功率对比图
    if (successChartRef.value) {
      successChart = echarts.init(successChartRef.value)
      updateSuccessChart()
    }
    
    // 初始化用户行为图
    if (behaviorChartRef.value) {
      behaviorChart = echarts.init(behaviorChartRef.value)
      updateBehaviorChart()
    }
  } catch (__error) {
    console.error('初始化图表失败:', error)
  }
}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const updateTrendChart = (data?: unknown[]) => {
  if (!trendChart) return
  
  // 使用传入的数据或默认数据
  const chartData = data || [
    { date: '06-12', totalSent: 1200, successCount: 1140 },
    { date: '06-13', totalSent: 1350, successCount: 1280 },
    { date: '06-14', totalSent: 1180, successCount: 1120 },
    { date: '06-15', totalSent: 1420, successCount: 1350 },
    { date: '06-16', totalSent: 1250, successCount: 1180 },
    { date: '06-17', totalSent: 1380, successCount: 1310 },
    { date: '06-18', totalSent: 1250, successCount: 1180 }
  ]
  
  const option = {
    title: {
      text: '发送量趋势',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['发送量', '成功量'],
      bottom: 0
    },
    xAxis: {
      type: 'category',
      data: chartData.map(item => item.date)
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: 'HrHr发送量',
        type: 'line',
        data: chartData.map(item => item.totalSent),
        smooth: true,
        itemStyle: { color: '#409EFF' }
      },
      {
        name: '成功量',
        type: 'line',
        data: chartData.map(item => item.successCount),
        smooth: true,
        itemStyle: { color: '#67C23A' }
      }
    ]
  }
  
  trendChart.setOption(option)
}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const updateChannelChart = (data?: unknown[]) => {
  if (!channelChart) return
  
  // 使用传入的数据或默认数据
  const chartData = data || [
    { channelType: 'EMAIL', totalSent: 5200, name: '邮件' },
    { channelType: 'SMS', totalSent: 3100, name: '短信' },
    { channelType: 'INTERNAL', totalSent: 2800, name: '站内信' },
    { channelType: 'WECHAT', totalSent: 1500, name: '微信' },
    { channelType: 'DINGTALK', totalSent: 800, name: '钉钉' }
  ]
  
  const option = {
    title: {
      text: '渠道分布',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '发送量',
        type: 'pie',
        radius: '50%',
        data: chartData.map(item => ({
          value: item.totalSent,
          name: item.name
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  channelChart.setOption(option)
}

const updateSuccessChart = (data?: number[]) => {
  if (!successChart) return
  
  // 使用传入的数据或默认数据
  const successRates = data || [96.5, 92.3, 98.1, 94.7, 89.2]
  const channelNames = ['邮件', '短信', '站内信', '微信', '钉钉']
  
  const option = {
    title: {
      text: '渠道成功率',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: channelNames
    },
    yAxis: {
      type: 'value',
      max: 100,
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [
      {
        name: '成功率',
        type: 'bar',
        data: successRates,
        itemStyle: {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
          color: function(params: unknown) {
            const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de']
            return colors[params.dataIndex]
          }
        }
      }
    ]
  }
  
  successChart.setOption(option)
}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const updateBehaviorChart = (data?: unknown[]) => {
  if (!behaviorChart) return
  
  // 使用传入的数据或默认数据
  const behaviorData = data || [
    { channelType: 'EMAIL', openRate: 65.2, clickRate: 23.5, name: '邮件' },
    { channelType: 'SMS', openRate: 78.5, clickRate: 45.2, name: '短信' },
    { channelType: 'INTERNAL', openRate: 89.3, clickRate: 56.8, name: '站内信' },
    { channelType: 'WECHAT', openRate: 72.1, clickRate: 34.7, name: '微信' },
    { channelType: 'DINGTALK', openRate: 68.9, clickRate: 28.3, name: '钉钉' }
  ]
  
  const option = {
    title: {
      text: '用户行为',
      left: 'center',
      textStyle: { fontSize: 14 }
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['打开率', '点击率'],
      bottom: 0
    },
    xAxis: {
      type: 'category',
      data: behaviorData.map(item => item.name)
    },
    yAxis: {
      type: 'value',
      max: 100,
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [
      {
        name: '打开率',
        type: 'bar',
        data: behaviorData.map(item => item.openRate),
        itemStyle: { color: '#409EFF' }
      },
      {
        name: '点击率',
        type: 'bar',
        data: behaviorData.map(item => item.clickRate),
        itemStyle: { color: '#67C23A' }
      }
    ]
  }
  
  behaviorChart.setOption(option)
}

const destroyCharts = () => {
  if (trendChart) {
    trendChart.dispose()
    trendChart = null
  }
  if (channelChart) {
    channelChart.dispose()
    channelChart = null
  }
  if (successChart) {
    successChart.dispose()
    successChart = null
  }
  if (behaviorChart) {
    behaviorChart.dispose()
    behaviorChart = null
  }
}

const handleDateRangeChange = () => {
  loadAllData()
}

const refreshData = () => {
  loadAllData()
  ElMessage.success('数据已刷新')
}

const exportReport = async () => {
  try {
    ElMessage.success('报告导出成功')
  } catch (__error) {
    ElMessage.error('导出失败')
  }
}

const exportTableData = async () => {
  try {
    ElMessage.success('数据导出成功')
  } catch (__error) {
    ElMessage.error('导出失败')
  }
}

const handleTableSizeChange = (size: number) => {
  tablePagination.size = size
  loadTableData()
}

const handleTableCurrentChange = (page: number) => {
  tablePagination.page = page
  loadTableData()
}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const viewDetails = (row: unknown) => {
  selectedDetail.value = {
    ...row,
    lastUpdate: new Date().toISOString(),
    errors: [
      { errorCode: 'E001', errorMessage: '网络超时', count: 25, percentage: 35.7 },
      { errorCode: 'E002', errorMessage: '无效收件人', count: 18, percentage: 25.7 },
      { errorCode: 'E003', errorMessage: '内容被拒绝', count: 12, percentage: 17.1 }
    ]
  }
  showDetailDialog.value = true
}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const getSummaries = (param: unknown) => {
  const {columns: _columns, data} =  param
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const sums: unknown[] 
  background-color: #f5f7fa;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .header-content {
      .page-title {
        display: flex;
        align-items: center;
        font-size: 24px;
        font-weight: 600;
        color: #303133;
        margin: 0 0 8px 0;

        .el-icon {
          margin-right: 8px;
          color: #409eff;
        }
      }

      .page-description {
        color: #909399;
        margin: 0;
        font-size: 14px;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;
      align-items: center;
    }
  }

  .metrics-cards {
    margin-bottom: 20px;

    .metric-card {
      border: none;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      transition: transform 0.2s ease;

      &:hover {
        transform: translateY(-2px);
      }

      :deep(.el-card__body) {
        padding: 20px;
      }

      .metric-content {
        display: flex;
        align-items: center;

        .metric-icon {
          width: 48px;
          height: 48px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;

          .el-icon {
            font-size: 24px;
            color: white;
          }
        }

        .metric-info {
          flex: 1;

          .metric-value {
            font-size: 28px;
            font-weight: 600;
            color: #303133;
            line-height: 1;
            margin-bottom: 4px;
          }

          .metric-label {
            font-size: 14px;
            color: #909399;
            margin-bottom: 4px;
          }

          .metric-change {
            display: flex;
            align-items: center;
            font-size: 12px;
            font-weight: 500;

            .el-icon {
              margin-right: 2px;
            }

            &.positive {
              color: #67c23a;
            }

            &.negative {
              color: #f56c6c;
            }
          }
        }
      }

      &.total-sent .metric-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }

      &.success-rate .metric-icon {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      }

      &.avg-response .metric-icon {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
      }

      &.active-users .metric-icon {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }
    }
  }

  .charts-section {
    margin-bottom: 20px;

    .chart-card {
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .chart-container {
        height: 300px;
        width: 100%;
      }
    }
  }

  .data-table-card {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .table-controls {
        display: flex;
        gap: 12px;
        align-items: center;
      }
    }

    .success-rate-high {
      color: #67c23a;
      font-weight: 500;
    }

    .success-rate-medium {
      color: #e6a23c;
      font-weight: 500;
    }

    .success-rate-low {
      color: #f56c6c;
      font-weight: 500;
    }

    .pagination-wrapper {
      padding: 20px;
      display: flex;
      justify-content: center;
      border-top: 1px solid #ebeef5;
    }
  }

  .detail-content {
    .error-analysis {
      margin-top: 20px;

      h4 {
        margin-bottom: 12px;
        color: #303133;
        font-size: 16px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .notification-statistics {
    .charts-section {
      .el-col {
        margin-bottom: 20px;
      }
    }
  }
}

@media (max-width: 768px) {
  .notification-statistics {
    padding: 10px;

    .page-header {
      flex-direction: column;
      gap: 16px;

      .header-actions {
        width: 100%;
        justify-content: flex-start;
        flex-wrap: wrap;
      }
    }

    .metrics-cards {
      .el-col {
        margin-bottom: 16px;
      }

      .metric-content {
        .metric-icon {
          width: 40px;
          height: 40px;
          margin-right: 12px;

          .el-icon {
            font-size: 20px;
          }
        }

        .metric-info {
          .metric-value {
            font-size: 24px;
          }
        }
      }
    }

    .charts-section {
      .chart-container {
        height: 250px;
      }
    }

    .table-header {
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;

      .table-controls {
        width: 100%;
        justify-content: flex-start;
      }
    }
  }
}
</style>
