<template>
  <div class="appraisal-template-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon><Files /></el-icon>
        考核模板管理
      </h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增模板
        </el-button>
        <el-button @click="handleImport">
          <el-icon><Upload /></el-icon>
          批量导入
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出模板
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="模板名称">
          <el-input
            v-model="searchForm.templateName"
            placeholder="请输入模板名称"
            clearable
            style="width: 200px"
            />
        </el-form-item>
        <el-form-item label="模板类型">
          <el-select
            v-model="searchForm.templateType"
            placeholder="请选择模板类型"
            clearable
            style="width: 150px"
          >
            <el-option label="教学考核" value="teaching"  />
            <el-option label="管理考核" value="management"  />
            <el-option label="科研考核" value="research"  />
            <el-option label="综合考核" value="comprehensive"  />
          </el-select>
        </el-form-item>
        <el-form-item label="模板状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="草稿" value="draft"  />
            <el-option label="启用" value="active"  />
            <el-option label="停用" value="inactive"  />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><Files /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.total }}</div>
              <div class="stat-label">总模板数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon active">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.active }}</div>
              <div class="stat-label">启用中</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon used">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.used }}</div>
              <div class="stat-label">使用次数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon indicators">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.indicators }}</div>
              <div class="stat-label">平均指标数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 考核模板列表 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="templateCode" label="模板编号" width="120"  />
        <el-table-column prop="templateName" label="模板名称" min-width="200"  />
        <el-table-column prop="templateType" label="模板类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.templateType)">
              {{ getTypeLabel(row.templateType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="version" label="版本号" width="100"  />
        <el-table-column prop="indicatorCount" label="指标数量" width="100"  />
        <el-table-column prop="usageCount" label="使用次数" width="100"  />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="creator" label="创建人" width="100"  />
        <el-table-column prop="createTime" label="创建时间" width="120"  />
        <el-table-column prop="updateTime" label="更新时间" width="120"  />
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button type="text" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="text" size="small" @click="handlePreview(row)">
              预览
            </el-button>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
            <el-dropdown @command="(command: unknown) => handleCommand(command, row)">
              <el-button type="text" size="small">
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="activate" v-if="row.status === 'draft'">
                    启用模板
                  </el-dropdown-item>
                  <el-dropdown-item command="deactivate" v-if="row.status === 'active'">
                    停用模板
                  </el-dropdown-item>
                  <el-dropdown-item command="copy">复制模板</el-dropdown-item>
                  <el-dropdown-item command="version">版本管理</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 考核模板详情/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="900px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="模板编号" prop="templateCode">
              <el-input v-model="formData.templateCode" :disabled="isView"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="模板名称" prop="templateName">
              <el-input v-model="formData.templateName" :disabled="isView"   />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="模板类型" prop="templateType">
              <el-select v-model="formData.templateType" :disabled="isView" style="width: 100%">
                <el-option label="教学考核" value="teaching"  />
                <el-option label="管理考核" value="management"  />
                <el-option label="科研考核" value="research"  />
                <el-option label="综合考核" value="comprehensive"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="版本号" prop="version">
              <el-input v-model="formData.version" :disabled="isView" placeholder="如：v1.0"   />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="模板描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="4"
            :disabled="isView"
            placeholder="请输入模板描述"
            />
        </el-form-item>
        <el-form-item label="适用范围" prop="scope">
          <el-input
            v-model="formData.scope"
            type="textarea"
            :rows="3"
            :disabled="isView"
            placeholder="请输入适用范围"
            />
        </el-form-item>
        <el-form-item label="评分规则" prop="scoringRules">
          <el-input
            v-model="formData.scoringRules"
            type="textarea"
            :rows="4"
            :disabled="isView"
            placeholder="请输入评分规则"
            />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="formData.notes"
            type="textarea"
            :rows="2"
            :disabled="isView"
            placeholder="请输入备注信息"
            />
        </el-form-item>
      </el-form>
      <template #footer v-if="!isView">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'AppraisalTemplateManagement'
})
 
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Files,
  Plus,
  Upload,
  Download,
  Search,
  Refresh,
  CircleCheck,
  TrendCharts,
  DataAnalysis,
  ArrowDown
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isView = ref(false)

const searchForm = reactive({
  templateName: '',
  templateType: '',
  status: ''
})

const stats = reactive({
  total: 15,
  active: 8,
  used: 245,
  indicators: 8.5
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

const tableData = ref([
  {
    id: '1',
    templateCode: '*********',
    templateName: '教学人员年度考核模板',
    templateType: 'teaching',
    version: 'v2.1',
    indicatorCount: 12,
    usageCount: 45,
    status: 'active',
    creator: '张三',
    createTime: '2024-01-15',
    updateTime: '2024-06-10',
    description: '适用于教学人员的年度综合考核模板',
    scope: '全校教学人员，包括专任教师、兼职教师等',
    scoringRules: '采用百分制评分，各项指标按权重计算总分',
    notes: '重点关注教学质量和学生评价'
  }
])

const formData = reactive({
  templateCode: '',
  templateName: '',
  templateType: '',
  version: '',
  description: '',
  scope: '',
  scoringRules: '',
  notes: ''
})

const formRules = {
  templateCode: [{ required: true, message: '请输入模板编号', trigger: 'blur' }],
  templateName: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
  templateType: [{ required: true, message: '请选择模板类型', trigger: 'change' }],
  version: [{ required: true, message: '请输入版本号', trigger: 'blur' }]
}

// 方法
const handleSearch = () => {
  console.log('搜索:', searchForm)
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    templateName: '',
    templateType: '',
    status: ''
  })
  loadData()
}

const handleAdd = () => {
  dialogTitle.value = '新增考核模板'
  isView.value = false
  resetForm()
  dialogVisible.value = true
}

   
const handleView = (row: unknown) => {
  dialogTitle.value = '查看考核模板'
  isView.value = true
  Object.assign(formData, row)
  dialogVisible.value = true
}

   
const handleEdit = (row: unknown) => {
  dialogTitle.value = '编辑考核模板'
  isView.value = false
  Object.assign(formData, row)
  dialogVisible.value = true
}

   
const handlePreview = (row: unknown) => {
  ElMessage.info(`预览模板 ${row.templateName}`)
}

   
const handleCommand = (command: string, row: unknown) => {
  switch (command) {
    case 'activate':
      ElMessage.success(`考核模板 ${row.templateName} 已启用`)
      break
    case 'deactivate':
      ElMessage.success(`考核模板 ${row.templateName} 已停用`)
      break
    case 'copy':
      ElMessage.success(`已复制考核模板 ${row.templateName}`)
      break
    case 'version':
      ElMessage.info(`管理模板 ${row.templateName} 的版本`)
      break
    case 'delete':
      ElMessageBox.confirm(`确定要删除考核模板 ${row.templateName} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ElMessage.success('删除成功')
      })
      break
  }
}

const handleImport = () => {
  ElMessage.info('批量导入功能开发中')
}

const handleExport = () => {
  ElMessage.info('导出模板功能开发中')
}

const handleSubmit = () => {
  ElMessage.success('保存成功')
  dialogVisible.value = false
}

const handleDialogClose = () => {
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    templateCode: '',
    templateName: '',
    templateType: '',
    version: '',
    description: '',
    scope: '',
    scoringRules: '',
    notes: ''
  })
}

const loadData = () => {
  loading.value = true
  
  // 构建查询参数
   
  const params: unknown = {
    page: pagination.currentPage,
    size: pagination.pageSize
  }
  
  // 添加搜索条件
  if (searchForm.templateName) {
    params.templateName = searchForm.templateName
  }
  if (searchForm.templateType) {
    params.templateType = searchForm.templateType
  }
  if (searchForm.status) {
    params.status = searchForm.status
  }
  
  // 调用实际API（暂时使用模拟数据，等待后端API完成）
  setTimeout(() => {
    loading.value = false
    pagination.total = 15
  }, 1000)
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadData()
}

const getTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    teaching: 'primary',
    management: 'success',
    research: 'warning',
    comprehensive: 'info'
  }
  return colors[type] || ''
}

const getTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    teaching: '教学考核',
    management: '管理考核',
    research: '科研考核',
    comprehensive: '综合考核'
  }
  return labels[type] || type
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    draft: 'info',
    active: 'success',
    inactive: 'warning'
  }
  return types[status] || ''
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    draft: '草稿',
    active: '启用',
    inactive: '停用'
  }
  return labels[status] || status
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.appraisal-template-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.active {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-icon.used {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.indicators {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
