<template>
  <div class="appraisal-statistics-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon><TrendCharts /></el-icon>
        考核统计分析
      </h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleGenerateReport">
          <el-icon><Document /></el-icon>
          生成报告
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
        <el-button @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <el-form :model="filterForm" inline>
        <el-form-item label="考核周期">
          <el-select
            v-model="filterForm.period"
            placeholder="请选择考核周期"
            style="width: 200px"
            @change="handleFilterChange"
          >
            <el-option label="2024年度" value="2024"  />
            <el-option label="2024年第二季度" value="2024Q2"  />
            <el-option label="2024年第一季度" value="2024Q1"  />
            <el-option label="2023年度" value="2023"  />
          </el-select>
        </el-form-item>
        <el-form-item label="考核类型">
          <el-select
            v-model="filterForm.type"
            placeholder="请选择考核类型"
            style="width: 150px"
            @change="handleFilterChange"
          >
            <el-option label="年度考核" value="annual"  />
            <el-option label="季度考核" value="quarterly"  />
            <el-option label="月度考核" value="monthly"  />
            <el-option label="专项考核" value="special"  />
          </el-select>
        </el-form-item>
        <el-form-item label="部门">
          <el-select
            v-model="filterForm.department"
            placeholder="请选择部门"
            style="width: 200px"
            @change="handleFilterChange"
          >
            <el-option label="全部部门" value=""  />
            <el-option label="计算机学院" value="cs"  />
            <el-option label="机械工程学院" value="me"  />
            <el-option label="经济管理学院" value="em"  />
          </el-select>
        </el-form-item>
        <el-form-item label="人员类型">
          <el-select
            v-model="filterForm.staffType"
            placeholder="请选择人员类型"
            style="width: 150px"
            @change="handleFilterChange"
          >
            <el-option label="全部人员" value=""  />
            <el-option label="教学人员" value="teaching"  />
            <el-option label="管理人员" value="management"  />
            <el-option label="技术人员" value="technical"  />
          </el-select>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计概览 -->
    <el-row :gutter="20" class="stats-overview">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ overview.totalParticipants }}</div>
              <div class="stat-label">参与人数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon completed">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ overview.completedCount }}</div>
              <div class="stat-label">已完成</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon average">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ overview.averageScore }}</div>
              <div class="stat-label">平均分</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon excellent">
              <el-icon><Trophy /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ overview.excellentRate }}%</div>
              <div class="stat-label">优秀率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表分析 -->
    <el-row :gutter="20" class="charts-section">
      <!-- 分数分布图 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>分数分布统计</span>
              <el-button type="text" @click="handleChartDrillDown('score')">
                <el-icon><ZoomIn /></el-icon>
                详细分析
              </el-button>
            </div>
          </template>
          <div ref="scoreChartRef" class="chart-container"></div>
        </el-card>
      </el-col>

      <!-- 等级分布图 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>等级分布统计</span>
              <el-button type="text" @click="handleChartDrillDown('level')">
                <el-icon><ZoomIn /></el-icon>
                详细分析
              </el-button>
            </div>
          </template>
          <div ref="levelChartRef" class="chart-container"></div>
        </el-card>
      </el-col>

      <!-- 部门对比图 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>部门考核对比</span>
              <el-button type="text" @click="handleChartDrillDown('department')">
                <el-icon><ZoomIn /></el-icon>
                详细分析
              </el-button>
            </div>
          </template>
          <div ref="departmentChartRef" class="chart-container"></div>
        </el-card>
      </el-col>

      <!-- 趋势分析图 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>考核趋势分析</span>
              <el-button type="text" @click="handleChartDrillDown('trend')">
                <el-icon><ZoomIn /></el-icon>
                详细分析
              </el-button>
            </div>
          </template>
          <div ref="trendChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细统计表 -->
    <el-card class="table-card">
      <template #header>
        <div class="table-header">
          <span>详细统计数据</span>
          <div class="table-actions">
            <el-button type="text" @click="handleTableExport">
              <el-icon><Download /></el-icon>
              导出表格
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table
        :data="statisticsData"
        v-loading="loading"
        stripe
        style="width: 100%"
        :summary-method="getSummaries"
        show-summary
      >
        <el-table-column prop="department" label="部门" width="150"  />
        <el-table-column prop="totalCount" label="总人数" width="100"  />
        <el-table-column prop="completedCount" label="已完成" width="100"  />
        <el-table-column prop="completionRate" label="完成率" width="100">
          <template #default="{ row }">
            {{ row.completionRate }}%
          </template>
        </el-table-column>
        <el-table-column prop="averageScore" label="平均分" width="100"  />
        <el-table-column prop="excellentCount" label="优秀人数" width="100"  />
        <el-table-column prop="excellentRate" label="优秀率" width="100">
          <template #default="{ row }">
            {{ row.excellentRate }}%
          </template>
        </el-table-column>
        <el-table-column prop="goodCount" label="良好人数" width="100"  />
        <el-table-column prop="qualifiedCount" label="合格人数" width="100"  />
        <el-table-column prop="unqualifiedCount" label="不合格人数" width="120"  />
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handleViewDetail(row)">
              查看详情
            </el-button>
            <el-button type="text" size="small" @click="handleAnalyze(row)">
              深度分析
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'AppraisalStatisticsManagement'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import {
  TrendCharts,
  Document,
  Download,
  Refresh,
  User,
  CircleCheck,
  Trophy,
  ZoomIn
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const scoreChartRef = ref()
const levelChartRef = ref()
const departmentChartRef = ref()
const trendChartRef = ref()

const filterForm = reactive({
  period: '2024',
  type: 'annual',
  department: '',
  staffType: ''
})

const overview = reactive({
  totalParticipants: 456,
  completedCount: 398,
  averageScore: 85.6,
  excellentRate: 28.5
})

const statisticsData = ref([
  {
    department: '计算机学院',
    totalCount: 156,
    completedCount: 142,
    completionRate: 91.0,
    averageScore: 87.2,
    excellentCount: 45,
    excellentRate: 31.7,
    goodCount: 68,
    qualifiedCount: 25,
    unqualifiedCount: 4
  },
  {
    department: '机械工程学院',
    totalCount: 128,
    completedCount: 115,
    completionRate: 89.8,
    averageScore: 84.8,
    excellentCount: 32,
    excellentRate: 27.8,
    goodCount: 55,
    qualifiedCount: 24,
    unqualifiedCount: 4
  },
  {
    department: '经济管理学院',
    totalCount: 98,
    completedCount: 89,
    completionRate: 90.8,
    averageScore: 86.1,
    excellentCount: 28,
    excellentRate: 31.5,
    goodCount: 42,
    qualifiedCount: 16,
    unqualifiedCount: 3
  }
])

// 方法
const handleFilterChange = () => {
  loadData()
  updateCharts()
}

const handleGenerateReport = () => {
  ElMessage.info('生成报告功能开发中')
}

const handleExport = () => {
  ElMessage.info('导出数据功能开发中')
}

const handleRefresh = () => {
  loadData()
  updateCharts()
  ElMessage.success('数据已刷新')
}

const handleChartDrillDown = (type: string) => {
  ElMessage.info(`${type} 详细分析功能开发中`)
}

const handleTableExport = () => {
  ElMessage.info('导出表格功能开发中')
}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleViewDetail = (row: unknown) => {
  ElMessage.info(`查看 ${row.department} 的详细统计`)
}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleAnalyze = (row: unknown) => {
  ElMessage.info(`对 ${row.department} 进行深度分析`)
}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const getSummaries = (param: unknown) => {
  const {columns: _columns, data: _data} =  param
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const sums: unknown[] 
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.filter-card {
  margin-bottom: 20px;
}

.stats-overview {
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-icon.average {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.excellent {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.charts-section {
  margin-bottom: 20px;
}

.chart-card {
  margin-bottom: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 300px;
  width: 100%;
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
