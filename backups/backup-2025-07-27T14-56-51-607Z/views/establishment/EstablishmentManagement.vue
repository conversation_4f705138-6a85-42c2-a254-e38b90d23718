<template>
  <div class="establishment-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>编制管理</span>
          <el-button type="primary" size="small" @click="handleAdd">
            <el-icon class="mr-1"><Plus /></el-icon>
            新增编制
          </el-button>
        </div>
      </template>

      <!-- 搜索栏 -->
      <el-form :model="searchForm" inline class="mb-4">
        <el-form-item label="部门">
          <el-input v-model="searchForm.department" placeholder="请输入部门名称" clearable />
        </el-form-item>
        <el-form-item label="岗位">
          <el-input v-model="searchForm.position" placeholder="请输入岗位名称" clearable />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择" clearable>
            <el-option label="全部" value="" />
            <el-option label="已满编" value="full" />
            <el-option label="缺编" value="vacant" />
            <el-option label="超编" value="over" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table :data="tableData" v-loading="loading" stripe border style="width: 100%">
        <el-table-column prop="department" label="部门" width="180" />
        <el-table-column prop="position" label="岗位" width="150" />
        <el-table-column prop="authorized" label="编制数" width="100" align="center" />
        <el-table-column prop="actual" label="实际人数" width="100" align="center" />
        <el-table-column label="编制状态" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row)" size="small">
              {{ getStatusText(row) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="vacancy" label="缺编数" width="100" align="center">
          <template #default="{ row }">
            <span :class="{ 'text-danger': row.vacancy > 0 }">
              {{ row.vacancy }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160" />
        <el-table-column label="操作" fixed="right" width="200">
          <template #default="{ row }">
            <el-button type="primary" link size="small" @click="handleEdit(row)">编辑</el-button>
            <el-button type="primary" link size="small" @click="handleView(row)">查看</el-button>
            <el-button type="danger" link size="small" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        class="mt-4"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

// 搜索表单
const searchForm = ref({
  department: '',
  position: '',
  status: ''
})

// 表格数据
const tableData = ref<any[]>([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 获取状态类型
const getStatusType = (row: any) => {
  if (row.actual === row.authorized) return 'success'
  if (row.actual < row.authorized) return 'warning'
  return 'danger'
}

// 获取状态文本
const getStatusText = (row: any) => {
  if (row.actual === row.authorized) return '已满编'
  if (row.actual < row.authorized) return '缺编'
  return '超编'
}

// 查询数据
const loadData = async () => {
  loading.value = true
  try {
    // TODO: 调用实际API
    // 模拟数据
    const mockData = [
      {
        id: 1,
        department: '信息技术学院',
        position: '专任教师',
        authorized: 50,
        actual: 45,
        vacancy: 5,
        createTime: '2024-01-15 10:30:00'
      },
      {
        id: 2,
        department: '机电工程学院',
        position: '实验员',
        authorized: 15,
        actual: 15,
        vacancy: 0,
        createTime: '2024-01-10 14:20:00'
      },
      {
        id: 3,
        department: '商贸管理学院',
        position: '行政人员',
        authorized: 10,
        actual: 12,
        vacancy: -2,
        createTime: '2024-01-08 09:15:00'
      }
    ]

    tableData.value = mockData
    total.value = mockData.length
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  loadData()
}

// 重置
const handleReset = () => {
  searchForm.value = {
    department: '',
    position: '',
    status: ''
  }
  handleSearch()
}

// 新增
const handleAdd = () => {
  ElMessage.info('新增编制功能开发中')
}

// 编辑
const handleEdit = (row: any) => {
  ElMessage.info(`编辑编制：${row.department} - ${row.position}`)
}

// 查看
const handleView = (row: any) => {
  ElMessage.info(`查看编制详情：${row.department} - ${row.position}`)
}

// 删除
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除"${row.department} - ${row.position}"的编制吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    ElMessage.success('删除成功')
    loadData()
  } catch {
    // 用户取消操作
  }
}

// 分页变化
const handleSizeChange = (val: number) => {
  pageSize.value = val
  loadData()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  loadData()
}

// 初始化
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.establishment-management {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .text-danger {
    color: var(--el-color-danger);
  }
}
</style>
