<template>
  <div class="test-page">
    <el-card>
      <template #header>
        <span>机构变更日志功能测试</span>
      </template>

      <el-space direction="vertical" size="large" style="width: 100%">
        <!-- API连接测试 -->
        <el-card>
          <template #header>
            <span>API连接测试</span>
          </template>
          
          <el-space>
            <el-button @click="testHealth" :loading="healthLoading">
              健康检查
            </el-button>
            <el-button @click="testStatistics" :loading="statsLoading">
              统计数据
            </el-button>
            <el-button @click="testQuery" :loading="queryLoading">
              查询测试
            </el-button>
          </el-space>

          <div v-if="testResults.length > 0" style="margin-top: 20px;">
            <h4>测试结果：</h4>
            <el-timeline>
              <el-timeline-item
                v-for="(result, index) in testResults"
                :key="index"
                :timestamp="result.timestamp"
                :type="result.success ? 'success' : 'danger'"
              >
                <div>
                  <strong>{{ result.test }}</strong>: {{ result.message }}
                  <pre v-if="result.data" style="margin-top: 8px; font-size: 12px;">{{ JSON.stringify(result.data, null, 2) }}</pre>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-card>

        <!-- 组件测试 -->
        <el-card>
          <template #header>
            <span>组件功能测试</span>
          </template>

          <el-space>
            <el-button @click="showCreateDialog">
              测试创建对话框
            </el-button>
            <el-button @click="showDetailDialog">
              测试详情对话框
            </el-button>
            <el-button @click="showApprovalDialog">
              测试审批对话框
            </el-button>
            <el-button @click="showStatisticsDialog">
              测试统计对话框
            </el-button>
          </el-space>
        </el-card>

        <!-- 类型定义测试 -->
        <el-card>
          <template #header>
            <span>类型定义测试</span>
          </template>

          <el-descriptions :column="2" border>
            <el-descriptions-item label="变更类型枚举">
              {{ Object.values(ChangeType).join(', ') }}
            </el-descriptions-item>
            <el-descriptions-item label="审批状态枚举">
              {{ Object.values(ApprovalStatus).join(', ') }}
            </el-descriptions-item>
            <el-descriptions-item label="优先级枚举">
              {{ Object.values(Priority).join(', ') }}
            </el-descriptions-item>
            <el-descriptions-item label="风险等级枚举">
              {{ Object.values(RiskLevel).join(', ') }}
            </el-descriptions-item>
          </el-descriptions>

          <div style="margin-top: 20px;">
            <h4>选项配置测试：</h4>
            <el-row :gutter="20">
              <el-col :span="6">
                <h5>变更类型选项</h5>
                <el-tag 
                  v-for="option in changeTypeOptions" 
                  :key="option.value"
                  style="margin: 2px;"
                >
                  {{ option.label }}
                </el-tag>
              </el-col>
              <el-col :span="6">
                <h5>审批状态选项</h5>
                <el-tag 
                  v-for="option in approvalStatusOptions" 
                  :key="option.value"
                  :type="approvalStatusColors[option.value]"
                  style="margin: 2px;"
                >
                  {{ option.label }}
                </el-tag>
              </el-col>
              <el-col :span="6">
                <h5>优先级选项</h5>
                <el-tag 
                  v-for="option in priorityOptions" 
                  :key="option.value"
                  :type="priorityColors[option.value]"
                  style="margin: 2px;"
                >
                  {{ option.label }}
                </el-tag>
              </el-col>
              <el-col :span="6">
                <h5>风险等级选项</h5>
                <el-tag 
                  v-for="option in riskLevelOptions" 
                  :key="option.value"
                  :type="riskLevelColors[option.value]"
                  style="margin: 2px;"
                >
                  {{ option.label }}
                </el-tag>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-space>
    </el-card>

    <!-- 测试对话框 -->
    <InstitutionChangeLogDialog
      v-model="createDialogVisible"
      :is-edit="false"
      @success="handleDialogSuccess"
    />

    <InstitutionChangeLogDetail
      v-model="detailDialogVisible"
      :change-log="mockChangeLog"
    />

    <ApprovalDialog
      v-model="approvalDialogVisible"
      :change-log="mockChangeLog"
      @success="handleApprovalSuccess"
    />

    <StatisticsDialog
      v-model="statisticsDialogVisible"
    />
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'InstitutionChangeLogTest'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { institutionChangeLogApi } from '@/api/institutionChangeLog'
import type { InstitutionChangeLog } from '@/types/institutionChangeLog'
import {
  ChangeType,
  ApprovalStatus,
  Priority,
  RiskLevel,
  changeTypeOptions,
  approvalStatusOptions,
  priorityOptions,
  riskLevelOptions,
  approvalStatusColors,
  priorityColors,
  riskLevelColors
} from '@/types/institutionChangeLog'
import HrInstitutionChangeLogDialog from '@/components/institutionChangeLog/HrInstitutionChangeLogDialog.vue'
import HrInstitutionChangeLogDetail from '@/components/institutionChangeLog/HrInstitutionChangeLogDetail.vue'
import HrApprovalDialog from '@/components/institutionChangeLog/HrApprovalDialog.vue'
import HrStatisticsDialog from '@/components/institutionChangeLog/HrStatisticsDialog.vue'

// 响应式数据
const healthLoading = ref(false)
const statsLoading = ref(false)
const queryLoading = ref(false)
const testResults = ref<any[]>([])

const createDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const approvalDialogVisible = ref(false)
const statisticsDialogVisible = ref(false)

// 模拟数据
const mockChangeLog = reactive<InstitutionChangeLog>({
  id: 1,
  changeCode: 'CHG-2024-001',
  organizationId: 1, // 修复类型不匹配，使用数字类型
  organizationName: '计算机学院',
  changeType: ChangeType.MODIFY,
  changeReason: '组织架构调整',
  changeContent: '将软件工程系并入计算机科学与技术系',
  effectiveDate: '2024-01-15',
  applicantId: 'USER-001',
  applicantName: '张三',
  applicantDepartment: '人事处',
  approvalStatus: ApprovalStatus.PENDING,
  applyDate: '2024-01-10',
  approvalDate: '',
  approvalBy: '',
  approvalComment: '',
  attachmentJson: '',
  remark: '测试数据',
  priority: Priority.HIGH,
  urgent: true,
  expectedCompletionDate: '2024-01-20',
  impactScope: '影响计算机学院所有师生',
  riskLevel: RiskLevel.MEDIUM,
  riskDescription: '可能影响教学安排',
  createTime: '2024-01-10T10:00:00',
  createBy: 'admin',
  updateTime: '2024-01-10T10:00:00',
  updateBy: 'admin'
})

// API测试函数
const testHealth = async () => {
  healthLoading.value = true
  try {
    const result = await institutionChangeLogApi.health()
    addTestResult('健康检查', true, '服务正常', result)
  } catch (__error) {
    addTestResult('健康检查', false, '服务异常', error)
  } finally {
    healthLoading.value = false
  }
}

const testStatistics = async () => {
  statsLoading.value = true
  try {
    const result = await institutionChangeLogApi.getStatistics()
    addTestResult('统计数据', true, '获取成功', result)
  } catch (__error) {
    addTestResult('统计数据', false, '获取失败', error)
  } finally {
    statsLoading.value = false
  }
}

const testQuery = async () => {
  queryLoading.value = true
  try {
    const result = await institutionChangeLogApi.query({ page: 0, size: 10 })
    addTestResult('查询测试', true, '查询成功', result)
  } catch (__error) {
    addTestResult('查询测试', false, '查询失败', error)
  } finally {
    queryLoading.value = false
  }
}

// 添加测试结果
   
const addTestResult = (test: string, success: boolean, message: string, data?: unknown) => {
  testResults.value.unshift({
    test,
    success,
    message,
    data,
    timestamp: new Date().toLocaleString()
  })
  
  if (success) {
    ElMessage.success(`${test}: ${message}`)
  } else {
    ElMessage.error(`${test}: ${message}`)
  }
}

// 对话框测试函数
const showCreateDialog = () => {
  createDialogVisible.value = true
}

const showDetailDialog = () => {
  detailDialogVisible.value = true
}

const showApprovalDialog = () => {
  approvalDialogVisible.value = true
}

const showStatisticsDialog = () => {
  statisticsDialogVisible.value = true
}

// 事件处理
const handleDialogSuccess = () => {
  ElMessage.success('对话框操作成功')
}

const handleApprovalSuccess = () => {
  ElMessage.success('审批操作成功')
}
</script>

<style scoped>
.test-page {
  padding: 20px;
}

pre {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  max-height: 200px;
}
</style>
