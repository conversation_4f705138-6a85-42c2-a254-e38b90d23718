<template>
  <div class="institution-change-log-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>机构变更日志管理</span>
          <div class="header-actions">
            <el-button 
              type="primary" 
              @click="showCreateDialog"
              :icon="Plus"
            >
              新增变更记录
            </el-button>
            <el-button 
              @click="showStatistics"
              :icon="DataAnalysis"
            >
              统计分析
            </el-button>
            <el-button 
              @click="exportData"
              :icon="Download"
            >
              导出数据
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索筛选区域 -->
      <el-form 
        :model="queryForm" 
        :inline="true" 
        class="search-form"
        label-width="80px"
      >
        <el-form-item label="关键词">
          <el-input
            v-model="queryForm.keyword"
            placeholder="搜索变更编码、机构名称、申请人"
            :prefix-icon="Search"
            clearable
            style="width: 250px"
            />
        </el-form-item>

        <el-form-item label="变更类型">
          <el-select 
            v-model="queryForm.changeType" 
            placeholder="请选择变更类型"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="option in changeTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
             />
          </el-select>
        </el-form-item>

        <el-form-item label="审批状态">
          <el-select 
            v-model="queryForm.approvalStatus" 
            placeholder="请选择审批状态"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="option in approvalStatusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
             />
          </el-select>
        </el-form-item>

        <el-form-item label="优先级">
          <el-select 
            v-model="queryForm.priority" 
            placeholder="请选择优先级"
            clearable
            style="width: 120px"
          >
            <el-option
              v-for="option in priorityOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
             />
          </el-select>
        </el-form-item>

        <el-form-item label="申请日期">
          <el-date-picker
            v-model="applyDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
           />
        </el-form-item>

        <el-form-item>
          <el-checkbox v-model="queryForm.urgent">仅显示紧急</el-checkbox>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch" :icon="Search">搜索</el-button>
          <el-button @click="handleReset" :icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="changeLogList"
        stripe
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        
        <el-table-column prop="changeCode" label="变更编码" width="140" fixed="left">
          <template #default="{ row }">
            <el-link type="primary" @click="showDetail(row)">
              {{ row.changeCode }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column prop="organizationName" label="机构名称" width="180"  />

        <el-table-column prop="changeType" label="变更类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getChangeTypeTagType(row.changeType)">
              {{ getChangeTypeLabel(row.changeType) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="changeReason" label="变更原因" width="200" show-overflow-tooltip  />

        <el-table-column prop="applicantName" label="申请人" width="100"  />

        <el-table-column prop="applicantDepartment" label="申请部门" width="150"  />

        <el-table-column prop="priority" label="优先级" width="80">
          <template #default="{ row }">
            <el-tag 
              :type="(priorityColors as unknown)[row.priority] || ''"
              size="small"
              :effect="row.urgent ? 'dark' : 'light'"
            >
              {{ getPriorityLabel(row.priority) }}
              <el-icon v-if="row.urgent" style="margin-left: 4px;"><Warning /></el-icon>
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="approvalStatus" label="审批状态" width="100">
          <template #default="{ row }">
            <el-tag :type="(approvalStatusColors as unknown)[row.approvalStatus] || ''">
              {{ getApprovalStatusLabel(row.approvalStatus) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="applyDate" label="申请日期" width="110">
          <template #default="{ row }">
            {{ formatDate(row.applyDate) }}
          </template>
        </el-table-column>

        <el-table-column prop="effectiveDate" label="生效日期" width="110">
          <template #default="{ row }">
            {{ formatDate(row.effectiveDate) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              text
              @click="showDetail(row)"
              :icon="View"
            >
              详情
            </el-button>
            
            <el-button
              v-if="canEdit(row)"
              type="warning"
              size="small"
              text
              @click="showEditDialog(row)"
              :icon="Edit"
            >
              编辑
            </el-button>

            <el-button
              v-if="canApprove(row)"
              type="success"
              size="small"
              text
              @click="showApprovalDialog(row)"
              :icon="Check"
            >
              审批
            </el-button>

            <el-button
              v-if="canDelete(row)"
              type="danger"
              size="small"
              text
              @click="handleDelete(row)"
              :icon="Delete"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        class="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
    </el-card>

    <!-- 创建/编辑对话框 -->
    <InstitutionChangeLogDialog
      v-model="dialogVisible"
      :change-log="currentChangeLog"
      :is-edit="isEdit"
      @success="handleDialogSuccess"
    />

    <!-- 详情对话框 -->
    <InstitutionChangeLogDetail
      v-model="detailDialogVisible"
      :change-log="currentChangeLog"
    />

    <!-- 审批对话框 -->
    <ApprovalDialog
      v-model="approvalDialogVisible"
      :change-log="currentChangeLog"
      @success="handleApprovalSuccess"
    />

    <!-- 统计分析对话框 -->
    <StatisticsDialog
      v-model="statisticsDialogVisible"
    />
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'InstitutionChangeLogManagement'
})
 
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Search, Refresh, View, Edit, Delete, Check, Warning,
  DataAnalysis, Download
} from '@element-plus/icons-vue'
import { institutionChangeLogApi } from '@/api/institutionChangeLog'
import type {
  InstitutionChangeLog,
  InstitutionChangeLogQueryRequest,
  Priority
} from '@/types/institutionChangeLog'
import {
  ChangeType,
  ApprovalStatus
} from '@/types/institutionChangeLog'
import {
  changeTypeOptions,
  approvalStatusOptions,
  priorityOptions,
  approvalStatusColors,
  priorityColors
} from '@/types/institutionChangeLog'
import HrInstitutionChangeLogDialog from '@/components/institutionChangeLog/HrInstitutionChangeLogDialog.vue'
import HrInstitutionChangeLogDetail from '@/components/institutionChangeLog/HrInstitutionChangeLogDetail.vue'
import HrApprovalDialog from '@/components/institutionChangeLog/HrApprovalDialog.vue'
import HrStatisticsDialog from '@/components/institutionChangeLog/HrStatisticsDialog.vue'

// 响应式数据
const loading = ref(false)
const changeLogList = ref<InstitutionChangeLog[]>([])
const selectedRows = ref<InstitutionChangeLog[]>([])
const dialogVisible = ref(false)
const detailDialogVisible = ref(false)
const approvalDialogVisible = ref(false)
const statisticsDialogVisible = ref(false)
const currentChangeLog = ref<InstitutionChangeLog | null>(null)
const isEdit = ref(false)

// 查询表单
const queryForm = reactive<InstitutionChangeLogQueryRequest>({
  keyword: '',
  changeType: undefined,
  approvalStatus: undefined,
  priority: undefined,
  urgent: false,
  page: 0,
  size: 20
})

// 申请日期范围
const applyDateRange = ref<[string, string] | null>(null)

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 加载变更记录列表
const loadChangeLogList = async () => {
  try {
    loading.value = true
    
    const params: InstitutionChangeLogQueryRequest = {
      ...queryForm,
      page: pagination.page - 1, // 后端从0开始
      size: pagination.size
    }

    // 处理日期范围
    if (applyDateRange.value) {
      params.applyDateStart = applyDateRange.value[0]
      params.applyDateEnd = applyDateRange.value[1]
    }
    
    const response = await institutionChangeLogApi.query(params)
    changeLogList.value = response.content
    pagination.total = response.totalElements
  } catch (__error) {
    ElMessage.error('加载变更记录列表失败')
    console.error('Load change log list error:', error)
  } finally {
    loading.value = false
  }
}

// 处理搜索
const handleSearch = () => {
  pagination.page = 1
  loadChangeLogList()
}

// 处理重置
const handleReset = () => {
  Object.assign(queryForm, {
    keyword: '',
    changeType: undefined,
    approvalStatus: undefined,
    priority: undefined,
    urgent: false
  })
  applyDateRange.value = null
  handleSearch()
}

// 处理选择变化
const handleSelectionChange = (selection: InstitutionChangeLog[]) => {
  selectedRows.value = selection
}

// 处理分页大小变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  loadChangeLogList()
}

// 处理当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadChangeLogList()
}

// 显示创建对话框
const showCreateDialog = () => {
  currentChangeLog.value = null
  isEdit.value = false
  dialogVisible.value = true
}

// 显示编辑对话框
const showEditDialog = (changeLog: InstitutionChangeLog) => {
  currentChangeLog.value = changeLog
  isEdit.value = true
  dialogVisible.value = true
}

// 显示详情对话框
const showDetail = (changeLog: InstitutionChangeLog) => {
  currentChangeLog.value = changeLog
  detailDialogVisible.value = true
}

// 显示审批对话框
const showApprovalDialog = (changeLog: InstitutionChangeLog) => {
  currentChangeLog.value = changeLog
  approvalDialogVisible.value = true
}

// 显示统计分析
const showStatistics = () => {
  statisticsDialogVisible.value = true
}

// 处理对话框成功
const handleDialogSuccess = () => {
  loadChangeLogList()
}

// 处理审批成功
const handleApprovalSuccess = () => {
  loadChangeLogList()
}

// 处理删除
const handleDelete = async (changeLog: InstitutionChangeLog) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除变更记录 "${changeLog.changeCode}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await institutionChangeLogApi.delete(changeLog.id)
    ElMessage.success('删除成功')
    loadChangeLogList()
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error('Delete change log error:', error)
    }
  }
}

// 导出数据
const exportData = async () => {
  try {
    const params = { ...queryForm }
    if (applyDateRange.value) {
      params.applyDateStart = applyDateRange.value[0]
      params.applyDateEnd = applyDateRange.value[1]
    }

    const blob = await institutionChangeLogApi.exportData(params)
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `机构变更记录_${new Date().toISOString().slice(0, 10)}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('导出成功')
  } catch (__error) {
    ElMessage.error('导出失败')
    console.error('Export data error:', error)
  }
}

// 权限判断
const canEdit = (changeLog: InstitutionChangeLog) => {
  return changeLog.approvalStatus === ApprovalStatus.DRAFT
}

const canApprove = (changeLog: InstitutionChangeLog) => {
  return changeLog.approvalStatus === ApprovalStatus.PENDING
}

const canDelete = (changeLog: InstitutionChangeLog) => {
  return changeLog.approvalStatus === ApprovalStatus.DRAFT
}

// 获取标签类型和文本
const getChangeTypeTagType = (changeType: ChangeType) => {
  const typeMap = {
    [ChangeType.ESTABLISH]: 'success',
    [ChangeType.MODIFY]: 'primary',
    [ChangeType.TRANSFER]: 'warning',
    [ChangeType.REVOKE]: 'danger',
    [ChangeType.MERGE]: 'info'
  }
  return typeMap[changeType] || ''
}

const getChangeTypeLabel = (changeType: ChangeType) => {
  const option = changeTypeOptions.find(opt => opt.value === changeType)
  return option?.label || changeType
}

const getApprovalStatusLabel = (status: ApprovalStatus) => {
  const option = approvalStatusOptions.find(opt => opt.value === status)
  return option?.label || status
}

const getPriorityLabel = (priority: Priority) => {
  const option = priorityOptions.find(opt => opt.value === priority)
  return option?.label || priority
}

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return '-'
  return new Date(date).toLocaleDateString('zh-CN')
}

// 组件挂载时加载数据
onMounted(() => {
  loadChangeLogList()
})
</script>

<style scoped>
.institution-change-log-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
