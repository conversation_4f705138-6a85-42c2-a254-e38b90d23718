<template>
  <div class="attendance-statistics">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>考勤统计分析</h2>
      <div class="header-actions">
        <el-button @click="exportData">导出数据</el-button>
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 查询条件 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" size="default">
        <el-form-item label="统计月份">
          <el-date-picker
            v-model="searchForm.month"
            type="month"
            placeholder="选择月份"
            format="YYYY-MM"
            value-format="YYYY-MM"
           />
        </el-form-item>
        <el-form-item label="部门">
          <el-tree-select
            v-model="searchForm.departmentId"
            :data="departmentTree"
            :props="{ label: 'name', value: 'id' }"
            placeholder="请选择部门"
            clearable
            filterable
           />
        </el-form-item>
        <el-form-item label="员工">
          <el-select
            v-model="searchForm.employeeId"
            placeholder="请选择员工"
            clearable
            filterable
          >
            <el-option
              v-for="emp in employeeList"
              :key="emp.id"
              :label="emp.name"
              :value="emp.id"
             />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计概览 -->
    <div class="stats-overview">
      <el-row :gutter="20">
        <el-col :span="6" v-for="item in overviewData" :key="item.key">
          <div class="overview-card" :class="`card-${item.type}`">
            <div class="card-icon">
              <el-icon :size="32"><component :is="item.icon" /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ item.value }}</div>
              <div class="card-label">{{ item.label }}</div>
              <div class="card-trend" v-if="item.trend">
                <el-icon v-if="item.trend > 0" class="trend-up">
                  <TrendCharts />
                </el-icon>
                <el-icon v-else class="trend-down">
                  <TrendCharts style="transform: rotate(180deg)" />
                </el-icon>
                <span>{{ Math.abs(item.trend) }}%</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 图表展示 -->
    <el-row :gutter="20" class="charts-container">
      <!-- 考勤趋势图 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>考勤趋势分析</span>
              <el-radio-group v-model="trendType" size="small">
                <el-radio-button value="day">日</el-radio-button>
                <el-radio-button value="week">周</el-radio-button>
                <el-radio-button value="month">月</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div ref="trendChart" class="chart-container"></div>
        </el-card>
      </el-col>

      <!-- 部门对比图 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>部门考勤对比</span>
              <el-select v-model="compareType" size="small" style="width: 120px;">
                <el-option value="rate" label="出勤率"  />
                <el-option value="late" label="迟到率"  />
                <el-option value="overtime" label="加班时长"  />
              </el-select>
            </div>
          </template>
          <div ref="compareChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-container">
      <!-- 考勤分布饼图 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>考勤状态分布</span>
          </template>
          <div ref="distributionChart" class="chart-container"></div>
        </el-card>
      </el-col>

      <!-- 时段分析热力图 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>打卡时段分析</span>
          </template>
          <div ref="heatmapChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细数据表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>考勤明细数据</span>
          <div class="header-tools">
            <el-switch
              v-model="showSummary"
              active-text="显示汇总"
              inactive-text="隐藏汇总"
             />
          </div>
        </div>
      </template>
      
      <el-table
        :data="tableData"
        :summary-method="getSummaries"
        :show-summary="showSummary"
        stripe
        border
        height="400"
        v-loading="loading"
      >
        <el-table-column prop="employeeName" label="姓名" width="100" fixed  />
        <el-table-column prop="employeeNo" label="工号" width="100"  />
        <el-table-column prop="department" label="部门" width="120"  />
        <el-table-column prop="shouldDays" label="应出勤" width="80" align="center">
          <template #default="{ row }">
            {{ row.shouldDays }}天
          </template>
        </el-table-column>
        <el-table-column prop="actualDays" label="实出勤" width="80" align="center">
          <template #default="{ row }">
            <span :class="{ 'text-danger': row.actualDays < row.shouldDays }">
              {{ row.actualDays }}天
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="attendanceRate" label="出勤率" width="100" align="center">
          <template #default="{ row }">
            <el-progress
              :percentage="row.attendanceRate"
              :color="getProgressColor(row.attendanceRate)"
              :stroke-width="6"
              :format="(val) => val + '%'"
            />
          </template>
        </el-table-column>
        <el-table-column label="异常情况" align="center">
          <el-table-column prop="lateTimes" label="迟到" width="60" align="center">
            <template #default="{ row }">
              <el-tag v-if="row.lateTimes > 0" type="warning" size="small">
                {{ row.lateTimes }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="earlyTimes" label="早退" width="60" align="center">
            <template #default="{ row }">
              <el-tag v-if="row.earlyTimes > 0" type="warning" size="small">
                {{ row.earlyTimes }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="absentDays" label="旷工" width="60" align="center">
            <template #default="{ row }">
              <el-tag v-if="row.absentDays > 0" type="danger" size="small">
                {{ row.absentDays }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column prop="leaveDays" label="请假天数" width="80" align="center"  />
        <el-table-column prop="overtimeHours" label="加班时长" width="90" align="center">
          <template #default="{ row }">
            {{ row.overtimeHours }}h
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="text" @click="viewDetail(row)">详情</el-button>
            <el-button type="text" @click="viewCalendar(row)">日历</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="考勤详情"
      width="800px"
    >
      <AttendanceDetailCalendar
        v-if="detailDialogVisible"
        :employee-id="currentEmployee.id"
        :month="searchForm.month"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Refresh,
  Calendar,
  Clock,
  User,
  TrendCharts
} from '@element-plus/icons-vue'
import echarts, { createChart } from '@/utils/echarts'
import dayjs from 'dayjs'
import { attendanceApi } from '@/api/attendance'
import HrAttendanceDetailCalendar from '@/components/attendance/HrAttendanceDetailCalendar.vue'

// 搜索表单
const searchForm = reactive({
  month: dayjs().format('YYYY-MM'),
  departmentId: '',
  employeeId: ''
})

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 响应式数据
const loading = ref(false)
const departmentTree = ref([])
const employeeList = ref([])
const tableData = ref([])
const showSummary = ref(true)
const trendType = ref('day')
const compareType = ref('rate')
const detailDialogVisible = ref(false)
const currentEmployee = ref({})

// 图表实例
const trendChart = ref()
const compareChart = ref()
const distributionChart = ref()
const heatmapChart = ref()

// 概览数据
const overviewData = ref([
  {
    key: 'total',
    label: '总人数',
    value: 0,
    type: 'primary',
    icon: User,
    trend: 0
  },
  {
    key: 'attendance',
    label: '平均出勤率',
    value: '0%',
    type: 'success',
    icon: Calendar,
    trend: 0
  },
  {
    key: 'late',
    label: '迟到人次',
    value: 0,
    type: 'warning',
    icon: Clock,
    trend: 0
  },
  {
    key: 'overtime',
    label: '加班总时长',
    value: '0h',
    type: 'info',
    icon: Clock,
    trend: 0
  }
])

// 初始化图表
function initCharts() {
  // 考勤趋势图
  const trend = createChart(trendChart.value)
  trend.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' }
    },
    legend: {
      data: ['出勤率', '迟到率', '加班人数']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: []
    },
    yAxis: [
      {
        type: 'value',
        name: 'HrHr百分比',
        axisLabel: {
          formatter: '{value}%'
        }
      },
      {
        type: 'value',
        name: '人数'
      }
    ],
    series: [
      {
        name: '出勤率',
        type: 'line',
        smooth: true,
        data: []
      },
      {
        name: '迟到率',
        type: 'line',
        smooth: true,
        data: []
      },
      {
        name: '加班人数',
        type: 'bar',
        yAxisIndex: 1,
        data: []
      }
    ]
  })

  // 部门对比图
  const compare = createChart(compareChart.value)
  compare.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: []
    },
    series: [{
      type: 'bar',
      data: [],
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
          { offset: 0, color: '#83bff6' },
          { offset: 0.5, color: '#188df0' },
          { offset: 1, color: '#188df0' }
        ])
      }
    }]
  })

  // 考勤分布饼图
  const distribution = createChart(distributionChart.value)
  distribution.setOption({
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [{
      name: '考勤状态',
      type: 'pie',
      radius: ['40%', '70%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '20',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: []
    }]
  })

  // 打卡时段热力图
  const heatmap = createChart(heatmapChart.value)
  heatmap.setOption({
    tooltip: {
      position: 'top'
    },
    grid: {
      height: '80%',
      top: '10%'
    },
    xAxis: {
      type: 'category',
      data: ['6:00', '7:00', '8:00', '9:00', '10:00', '17:00', '18:00', '19:00', '20:00', '21:00'],
      splitArea: {
        show: true
      }
    },
    yAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五'],
      splitArea: {
        show: true
      }
    },
    visualMap: {
      min: 0,
      max: 100,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '0%',
      inRange: {
        color: ['#e0f2fe', '#0284c7']
      }
    },
    series: [{
      name: '打卡人数',
      type: 'heatmap',
      data: [],
      label: {
        show: true
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  })

  // 响应式调整
  window.addEventListener('resize', () => {
    trend.resize()
    compare.resize()
    distribution.resize()
    heatmap.resize()
  })
}

// 获取进度条颜色
function getProgressColor(percentage: number) {
  if (percentage >= 95) return '#67c23a'
  if (percentage >= 90) return '#e6a23c'
  return '#f56c6c'
}

// 查询数据
async function handleSearch() {
  loading.value = true
  try {
    // 获取统计数据
    const data = await attendanceApi.getStatistics({
      ...searchForm,
      page: pagination.currentPage,
      pageSize: pagination.pageSize
    })

    // 更新表格数据
    tableData.value = data.list
    pagination.total = data.total

    // 更新概览数据
    updateOverviewData(data.overview)

    // 更新图表数据
    updateChartsData(data.charts)

  } catch (__error) {
    ElMessage.error('获取统计数据失败')
  } finally {
    loading.value = false
  }
}

// 更新概览数据
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
function updateOverviewData(data: unknown) {
  overviewData.value[0].value = data.totalEmployees
  overviewData.value[0].trend = data.employeeTrend
  
  overviewData.value[1].value = data.averageAttendanceRate + '%'
  overviewData.value[1].trend = data.attendanceTrend
  
  overviewData.value[2].value = data.totalLateTimes
  overviewData.value[2].trend = data.lateTrend
  
  overviewData.value[3].value = data.totalOvertimeHours + 'h'
  overviewData.value[3].trend = data.overtimeTrend
}

// 更新图表数据
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
function updateChartsData(data: unknown) {
  // 更新趋势图
  const trendInstance = echarts.getInstanceByDom(trendChart.value)
  trendInstance.setOption({
    xAxis: { data: data.trend.xAxis },
    series: [
      { data: data.trend.attendanceRate },
      { data: data.trend.lateRate },
      { data: data.trend.overtimeCount }
    ]
  })

  // 更新部门对比图 - 调用专门的API
  updateDepartmentComparison()

  // 更新分布饼图
  const distributionInstance = echarts.getInstanceByDom(distributionChart.value)
  distributionInstance.setOption({
    series: [{ data: data.distribution }]
  })

  // 更新热力图
  const heatmapInstance = echarts.getInstanceByDom(heatmapChart.value)
  heatmapInstance.setOption({
    series: [{ data: data.heatmap }]
  })
}

// 获取部门对比数据
async function updateDepartmentComparison() {
  try {
    const compareInstance = echarts.getInstanceByDom(compareChart.value)
    if (!compareInstance) return
    
    // 显示加载动画
    compareInstance.showLoading()
    
    // 根据对比类型映射参数
    const compareTypeMap: Record<string, string> = {
      'rate': 'attendance',
      'late': 'late',
      'overtime': 'overtime'
    }
    
    // 调用部门对比API
    const {data: _data} =  await attendanceApi.getDepartmentComparison({
      month: searchForm.month,
      parentDepartmentId: searchForm.departmentId ? parseInt(searchForm.departmentId) : undefined,
      compareType: compareTypeMap[compareType.value] as unknown,
      includeSubDepartments: true,
      limit: 10
    })
    
    // 更新图表
    compareInstance.hideLoading()
    
    // 根据对比类型选择数据
    let seriesData: number[] 
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
    }
    
    .header-actions {
      display: flex;
      gap: 10px;
    }
  }
  
  .search-card {
    margin-bottom: 20px;
  }
  
  .stats-overview {
    margin-bottom: 20px;
    
    .overview-card {
      background: #fff;
      border-radius: 8px;
      padding: 20px;
      display: flex;
      align-items: center;
      gap: 16px;
      transition: all 0.3s;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      }
      
      .card-icon {
        width: 64px;
        height: 64px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .el-icon {
          color: #fff;
        }
      }
      
      &.card-primary .card-icon { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
      &.card-success .card-icon { background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%); }
      &.card-warning .card-icon { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
      &.card-info .card-icon { background: linear-gradient(135deg, #30cfd0 0%, #330867 100%); }
      
      .card-content {
        flex: 1;
        
        .card-value {
          font-size: 28px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 4px;
        }
        
        .card-label {
          font-size: 14px;
          color: #909399;
        }
        
        .card-trend {
          display: flex;
          align-items: center;
          gap: 4px;
          margin-top: 8px;
          font-size: 12px;
          
          .trend-up {
            color: #67c23a;
          }
          
          .trend-down {
            color: #f56c6c;
          }
        }
      }
    }
  }
  
  .charts-container {
    margin-bottom: 20px;
    
    .chart-card {
      height: 400px;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .chart-container {
        height: 320px;
      }
    }
  }
  
  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-tools {
        display: flex;
        align-items: center;
        gap: 16px;
      }
    }
    
    .text-danger {
      color: #f56c6c;
    }
    
    .el-pagination {
      margin-top: 20px;
      justify-content: flex-end;
    }
  }
}
</style>