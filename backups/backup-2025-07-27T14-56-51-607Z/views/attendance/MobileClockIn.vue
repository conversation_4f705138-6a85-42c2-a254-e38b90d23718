<template>
  <div class="mobile-clock-in">
    <!-- 顶部信息 -->
    <div class="header-info">
      <div class="date-time">
        <div class="date">{{ currentDate }}</div>
        <div class="time">{{ currentTime }}</div>
        <div class="week">{{ currentWeek }}</div>
      </div>
      <div class="user-info">
        <el-avatar  :size="60">
            <img v-lazy="userInfo.avatar" style="width: 60px; height: 60px; object-fit: cover;" />
          </el-avatar>
          {{ userInfo.name.charAt(0) }}
        </el-avatar>
        <div class="user-details">
          <div class="user-name">{{ userInfo.name }}</div>
          <div class="user-dept">{{ userInfo.department }}</div>
        </div>
      </div>
    </div>

    <!-- 打卡状态 -->
    <el-card shadow="never" class="status-card">
      <div class="clock-status">
        <div class="status-item">
          <div class="status-label">上班打卡</div>
          <div class="status-time" :class="{ done: todayRecord.clockIn }">
            {{ todayRecord.clockIn || '--:--' }}
          </div>
          <el-tag v-if="todayRecord.clockInStatus" :type="getStatusType(todayRecord.clockInStatus)" size="small">
            {{ todayRecord.clockInStatus }}
          </el-tag>
        </div>
        <div class="status-divider"></div>
        <div class="status-item">
          <div class="status-label">下班打卡</div>
          <div class="status-time" :class="{ done: todayRecord.clockOut }">
            {{ todayRecord.clockOut || '--:--' }}
          </div>
          <el-tag v-if="todayRecord.clockOutStatus" :type="getStatusType(todayRecord.clockOutStatus)" size="small">
            {{ todayRecord.clockOutStatus }}
          </el-tag>
        </div>
      </div>
      <div class="work-time">
        <span>今日工时：</span>
        <strong>{{ todayRecord.workHours || '0小时0分' }}</strong>
      </div>
    </el-card>

    <!-- 打卡方式选择 -->
    <el-card shadow="never" class="method-card">
      <div class="method-title">选择打卡方式</div>
      <el-radio-group v-model="clockMethod" size="large" class="method-group">
        <el-radio-button label="location">
          <el-icon><Location /></el-icon>
          <span>定位打卡</span>
        </el-radio-button>
        <el-radio-button label="wifi">
          <el-icon><Connection /></el-icon>
          <span>WiFi打卡</span>
        </el-radio-button>
        <el-radio-button label="face">
          <el-icon><User /></el-icon>
          <span>人脸识别</span>
        </el-radio-button>
      </el-radio-group>
    </el-card>

    <!-- 定位打卡 -->
    <el-card v-if="clockMethod === 'location'" shadow="never" class="location-card">
      <div class="location-info">
        <div class="location-status" :class="{ success: locationStatus.isInRange }">
          <el-icon v-if="locationStatus.loading" class="is-loading">
            <Loading />
          </el-icon>
          <el-icon v-else-if="locationStatus.isInRange">
            <CircleCheck />
          </el-icon>
          <el-icon v-else>
            <CircleClose />
          </el-icon>
          <span>{{ locationStatus.message }}</span>
        </div>
        <div class="location-details">
          <div class="detail-item">
            <span class="label">当前位置：</span>
            <span class="value">{{ locationInfo.address || '获取中...' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">考勤地点：</span>
            <span class="value">{{ locationInfo.targetName }}</span>
          </div>
          <div class="detail-item">
            <span class="label">距离范围：</span>
            <span class="value" :class="{ 'text-danger': !locationStatus.isInRange }">
              {{ locationInfo.distance }}米 / {{ locationInfo.allowedRadius }}米
            </span>
          </div>
        </div>
        <el-button type="primary" @click="refreshLocation" :loading="locationStatus.loading">
          <el-icon><Refresh /></el-icon>
          刷新定位
        </el-button>
      </div>
    </el-card>

    <!-- WiFi打卡 -->
    <el-card v-if="clockMethod === 'wifi'" shadow="never" class="wifi-card">
      <div class="wifi-info">
        <div class="wifi-status" :class="{ success: wifiStatus.isConnected }">
          <el-icon v-if="wifiStatus.isConnected">
            <CircleCheck />
          </el-icon>
          <el-icon v-else>
            <CircleClose />
          </el-icon>
          <span>{{ wifiStatus.message }}</span>
        </div>
        <div class="wifi-list">
          <div class="wifi-item" v-for="wifi in wifiList" :key="wifi.id">
            <el-icon><Connection /></el-icon>
            <span>{{ wifi.name }}</span>
            <el-tag v-if="wifi.isConnected" type="success" size="small">已连接</el-tag>
            <el-tag v-else-if="wifi.isAllowed" type="info" size="small">可用</el-tag>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 人脸识别 -->
    <el-card v-if="clockMethod === 'face'" shadow="never" class="face-card">
      <div class="face-recognition">
        <div class="camera-container">
          <video ref="videoRef" v-show="cameraActive" autoplay playsinline></video>
          <canvas ref="canvasRef" v-show="!cameraActive"></canvas>
          <div v-if="!cameraActive && !facePhoto" class="camera-placeholder">
            <el-icon><Camera /></el-icon>
            <span>点击开启摄像头</span>
          </div>
          <div v-if="faceStatus.detecting" class="face-overlay">
            <div class="face-frame"></div>
            <div class="scanning-line"></div>
          </div>
        </div>
        <div class="face-controls">
          <el-button v-if="!cameraActive" type="primary" @click="startCamera">
            <el-icon><VideoCamera /></el-icon>
            开启摄像头
          </el-button>
          <el-button v-else type="primary" @click="capturePhoto">
            <el-icon><Camera /></el-icon>
            拍照识别
          </el-button>
          <el-button v-if="cameraActive" @click="stopCamera">
            <el-icon><Close /></el-icon>
            关闭摄像头
          </el-button>
        </div>
        <div v-if="faceStatus.result" class="face-result" :class="{ success: faceStatus.success }">
          <el-icon v-if="faceStatus.success"><CircleCheck /></el-icon>
          <el-icon v-else><CircleClose /></el-icon>
          <span>{{ faceStatus.message }}</span>
        </div>
      </div>
    </el-card>

    <!-- 打卡按钮 -->
    <div class="clock-button-container">
      <div class="clock-button" @click="handleClockIn" :class="{ disabled: !canClockIn }">
        <div class="button-inner">
          <el-icon v-if="clockLoading" class="is-loading"><Loading /></el-icon>
          <el-icon v-else><Pointer /></el-icon>
          <span>{{ clockButtonText }}</span>
        </div>
      </div>
      <div class="button-hint">{{ clockHint }}</div>
    </div>

    <!-- 快捷操作 -->
    <el-card shadow="never" class="quick-actions">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="action-item" @click="handleViewRecords">
            <el-icon><Calendar /></el-icon>
            <span>打卡记录</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="action-item" @click="handleApplyLeave">
            <el-icon><Document /></el-icon>
            <span>请假申请</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="action-item" @click="handleFieldWork">
            <el-icon><MapLocation /></el-icon>
            <span>外勤打卡</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="action-item" @click="handleSupplementClock">
            <el-icon><Clock /></el-icon>
            <span>补卡申请</span>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 打卡成功对话框 -->
    <el-dialog
      v-model="successDialog"
      width="300px"
      center
      :show-close="false"
    >
      <div class="success-content">
        <el-icon class="success-icon" color="#67C23A" :size="60">
          <CircleCheck />
        </el-icon>
        <div class="success-title">打卡成功</div>
        <div class="success-time">{{ successInfo.time }}</div>
        <div class="success-type">{{ successInfo.type }}</div>
        <el-button type="primary" @click="successDialog = false">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Location,
  Connection,
  User,
  Camera,
  VideoCamera,
  CircleCheck,
  CircleClose,
  Loading,
  Refresh,
  Close,
  Pointer,
  Calendar,
  Document,
  MapLocation,
  Clock
} from '@element-plus/icons-vue'

// 用户信息
const userInfo = ref({
  name: 'HrHr张三',
  department: '计算机系',
  avatar: ''
})

// 当前时间
const currentDate = ref('')
const currentTime = ref('')
const currentWeek = ref('')

// 今日打卡记录
const todayRecord = ref({
  clockIn: '',
  clockOut: '',
  clockInStatus: '',
  clockOutStatus: '',
  workHours: ''
})

// 打卡方式
const clockMethod = ref('location')

// 定位相关
const locationStatus = reactive({
  loading: false,
  isInRange: false,
  message: '正在获取位置信息...'
})

const locationInfo = reactive({
  address: '',
  targetName: '主校区行政楼',
  distance: 0,
  allowedRadius: 300,
  latitude: 0,
  longitude: 0
})

// WiFi相关
const wifiStatus = reactive({
  isConnected: false,
  message: '未连接到指定WiFi'
})

const wifiList = ref([
  { id: 1, name: 'HUST-Staff', isConnected: true, isAllowed: true },
  { id: 2, name: 'HUST-Guest', isConnected: false, isAllowed: false },
  { id: 3, name: 'HUST-EDU', isConnected: false, isAllowed: true }
])

// 人脸识别相关
const videoRef = ref<HTMLVideoElement>()
const canvasRef = ref<HTMLCanvasElement>()
const cameraActive = ref(false)
const facePhoto = ref('')
const faceStatus = reactive({
  detecting: false,
  success: false,
  result: false,
  message: ''
})

// 打卡相关
const clockLoading = ref(false)
const successDialog = ref(false)
const successInfo = reactive({
  time: '',
  type: ''
})

// 计算属性
const canClockIn = computed(() => {
  if (clockMethod.value === 'location') {
    return locationStatus.isInRange
  } else if (clockMethod.value === 'wifi') {
    return wifiStatus.isConnected
  } else if (clockMethod.value === 'face') {
    return faceStatus.success
  }
  return false
})

const clockButtonText = computed(() => {
  if (clockLoading.value) return '打卡中...'
  if (!todayRecord.value.clockIn) return '上班打卡'
  if (!todayRecord.value.clockOut) return '下班打卡'
  return '已完成打卡'
})

const clockHint = computed(() => {
  if (!canClockIn.value) {
    if (clockMethod.value === 'location') return '请确保在考勤范围内'
    if (clockMethod.value === 'wifi') return '请连接到指定WiFi'
    if (clockMethod.value === 'face') return '请先完成人脸识别'
  }
  return '点击上方按钮进行打卡'
})

// 时间更新
   
let timeTimer: unknown
const updateTime = () => {
  const now = new Date()
  currentDate.value = now.toLocaleDateString('zh-CN')
  currentTime.value = now.toLocaleTimeString('zh-CN')
  const weeks = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  currentWeek.value = weeks[now.getDay()]
}

// 获取定位
const getLocation = () => {
  locationStatus.loading = true
  locationStatus.message = '正在获取位置信息...'
  
  // 模拟定位
  setTimeout(() => {
    locationInfo.address = '杭州市西湖区学院路50号'
    locationInfo.distance = 150
    locationStatus.isInRange = locationInfo.distance <= locationInfo.allowedRadius
    locationStatus.message = locationStatus.isInRange ? '已在考勤范围内' : '不在考勤范围内'
    locationStatus.loading = false
  }, 2000)
}

// 刷新定位
const refreshLocation = () => {
  getLocation()
}

// 开启摄像头
const startCamera = async () => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ video: true })
    if (videoRef.value) {
      videoRef.value.srcObject = stream
      cameraActive.value = true
      faceStatus.detecting = true
    }
  } catch (__error) {
    ElMessage.error('无法访问摄像头')
  }
}

// 关闭摄像头
const stopCamera = () => {
  if (videoRef.value && videoRef.value.srcObject) {
    const stream = videoRef.value.srcObject as MediaStream
    stream.getTracks().forEach(track => track.stop())
    videoRef.value.srcObject = null
  }
  cameraActive.value = false
  faceStatus.detecting = false
}

// 拍照识别
const capturePhoto = () => {
  if (videoRef.value && canvasRef.value) {
    const canvas = canvasRef.value
    const video = videoRef.value
    canvas.width = video.videoWidth
    canvas.height = video.videoHeight
    const ctx = canvas.getContext('2d')
    if (ctx) {
      ctx.drawImage(video, 0, 0)
      facePhoto.value = canvas.toDataURL('image/jpeg')
    }
    
    // 模拟人脸识别
    faceStatus.result = true
    faceStatus.message = '识别中...'
    setTimeout(() => {
      faceStatus.success = true
      faceStatus.message = '人脸识别成功'
      stopCamera()
    }, 2000)
  }
}

// 打卡
const handleClockIn = () => {
  if (!canClockIn.value || clockLoading.value) return
  
  clockLoading.value = true
  
  setTimeout(() => {
    const now = new Date()
    const timeStr = now.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
    
    if (!todayRecord.value.clockIn) {
      todayRecord.value.clockIn = timeStr
      todayRecord.value.clockInStatus = '正常'
      successInfo.type = '上班打卡'
    } else {
      todayRecord.value.clockOut = timeStr
      todayRecord.value.clockOutStatus = '正常'
      todayRecord.value.workHours = '8小时30分'
      successInfo.type = '下班打卡'
    }
    
    successInfo.time = timeStr
    successDialog.value = true
    clockLoading.value = false
  }, 1000)
}

// 获取状态类型
const getStatusType = (status: string) => {
  const map: Record<string, string> = {
    '正常': 'success',
    '迟到': 'warning',
    '早退': 'danger'
  }
  return map[status] || 'info'
}

// 快捷操作
const handleViewRecords = () => {
  ElMessage.info('查看打卡记录')
}

const handleApplyLeave = () => {
  ElMessage.info('请假申请')
}

const handleFieldWork = () => {
  ElMessage.info('外勤打卡')
}

const handleSupplementClock = () => {
  ElMessage.info('补卡申请')
}

// 生命周期
onMounted(() => {
  updateTime()
  timeTimer = setInterval(updateTime, 1000)
  
  if (clockMethod.value === 'location') {
    getLocation()
  }
})

onUnmounted(() => {
  clearInterval(timeTimer)
  stopCamera()
})
</script>

<style lang="scss" scoped>
.mobile-clock-in {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;

  // 头部信息
  .header-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: white;

    .date-time {
      .date {
        font-size: 16px;
        opacity: 0.9;
      }
      .time {
        font-size: 32px;
        font-weight: bold;
        margin: 5px 0;
      }
      .week {
        font-size: 14px;
        opacity: 0.8;
      }
    }

    .user-info {
      display: flex;
      align-items: center;
      gap: 15px;

      .user-details {
        text-align: right;
        .user-name {
          font-size: 18px;
          font-weight: bold;
        }
        .user-dept {
          font-size: 14px;
          opacity: 0.9;
        }
      }
    }
  }

  // 打卡状态卡片
  .status-card {
    margin-bottom: 20px;

    .clock-status {
      display: flex;
      justify-content: space-around;
      align-items: center;
      padding: 20px 0;

      .status-item {
        text-align: center;

        .status-label {
          font-size: 14px;
          color: #666;
          margin-bottom: 10px;
        }

        .status-time {
          font-size: 24px;
          font-weight: bold;
          color: #999;
          margin-bottom: 5px;

          &.done {
            color: #303133;
          }
        }
      }

      .status-divider {
        width: 1px;
        height: 60px;
        background-color: #e0e0e0;
      }
    }

    .work-time {
      text-align: center;
      padding-top: 15px;
      border-top: 1px solid #e0e0e0;
      color: #666;

      strong {
        color: #409EFF;
        font-size: 18px;
      }
    }
  }

  // 打卡方式
  .method-card {
    margin-bottom: 20px;

    .method-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
    }

    .method-group {
      display: flex;
      width: 100%;

      :deep(.el-radio-button__inner) {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 15px 20px;

        .el-icon {
          font-size: 24px;
          margin-bottom: 5px;
        }
      }
    }
  }

  // 定位卡片
  .location-card {
    margin-bottom: 20px;

    .location-info {
      .location-status {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 16px;
        margin-bottom: 20px;
        color: #F56C6C;

        &.success {
          color: #67C23A;
        }

        .el-icon {
          font-size: 24px;
        }
      }

      .location-details {
        margin-bottom: 20px;

        .detail-item {
          display: flex;
          margin-bottom: 10px;

          .label {
            width: 80px;
            color: #666;
          }

          .value {
            flex: 1;
            color: #303133;

            &.text-danger {
              color: #F56C6C;
            }
          }
        }
      }
    }
  }

  // WiFi卡片
  .wifi-card {
    margin-bottom: 20px;

    .wifi-status {
      display: flex;
      align-items: center;
      gap: 10px;
      font-size: 16px;
      margin-bottom: 20px;
      color: #F56C6C;

      &.success {
        color: #67C23A;
      }

      .el-icon {
        font-size: 24px;
      }
    }

    .wifi-list {
      .wifi-item {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 10px;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        margin-bottom: 10px;

        .el-icon {
          color: #409EFF;
        }

        span {
          flex: 1;
        }
      }
    }
  }

  // 人脸识别卡片
  .face-card {
    margin-bottom: 20px;

    .camera-container {
      position: relative;
      width: 100%;
      height: 300px;
      background-color: #000;
      border-radius: 8px;
      overflow: hidden;
      margin-bottom: 20px;

      video, canvas {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .camera-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: white;

        .el-icon {
          font-size: 48px;
          margin-bottom: 10px;
        }
      }

      .face-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;

        .face-frame {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 200px;
          height: 200px;
          border: 3px solid #67C23A;
          border-radius: 50%;
        }

        .scanning-line {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 220px;
          height: 2px;
          background: linear-gradient(90deg, transparent, #67C23A, transparent);
          animation: scan 2s linear infinite;
        }
      }
    }

    .face-controls {
      display: flex;
      justify-content: center;
      gap: 10px;
      margin-bottom: 20px;
    }

    .face-result {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
      font-size: 16px;
      color: #F56C6C;

      &.success {
        color: #67C23A;
      }

      .el-icon {
        font-size: 24px;
      }
    }
  }

  // 打卡按钮
  .clock-button-container {
    margin: 40px 0;
    text-align: center;

    .clock-button {
      display: inline-block;
      width: 150px;
      height: 150px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 50%;
      box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
      cursor: pointer;
      transition: all 0.3s;

      &:hover:not(.disabled) {
        transform: scale(1.05);
        box-shadow: 0 15px 40px rgba(102, 126, 234, 0.5);
      }

      &:active:not(.disabled) {
        transform: scale(0.98);
      }

      &.disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }

      .button-inner {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: white;

        .el-icon {
          font-size: 48px;
          margin-bottom: 10px;
        }

        span {
          font-size: 18px;
          font-weight: bold;
        }
      }
    }

    .button-hint {
      margin-top: 20px;
      color: #666;
      font-size: 14px;
    }
  }

  // 快捷操作
  .quick-actions {
    .action-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 15px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        color: #409EFF;
        transform: translateY(-2px);
      }

      .el-icon {
        font-size: 32px;
        margin-bottom: 8px;
      }

      span {
        font-size: 14px;
      }
    }
  }

  // 成功对话框
  .success-content {
    text-align: center;
    padding: 20px;

    .success-icon {
      margin-bottom: 20px;
    }

    .success-title {
      font-size: 20px;
      font-weight: bold;
      margin-bottom: 10px;
    }

    .success-time {
      font-size: 24px;
      color: #409EFF;
      margin-bottom: 5px;
    }

    .success-type {
      color: #666;
      margin-bottom: 20px;
    }
  }
}

// 动画
@keyframes scan {
  0% {
    transform: translate(-50%, -100px);
  }
  100% {
    transform: translate(-50%, 100px);
  }
}

// 响应式
@media (max-width: 768px) {
  .mobile-clock-in {
    padding: 10px;

    .header-info {
      flex-direction: column;
      text-align: center;

      .user-info {
        margin-top: 15px;
      }
    }
  }
}
</style>