<template>
  <div class="attendance-record-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>考勤记录管理</h2>
      <p>查询、补录和管理教职工考勤记录（虚拟滚动优化版）</p>
    </div>

    <!-- 搜索和操作区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-form">
        <el-row :gutter="20">
          <el-col :span="5">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索姓名、员工编号"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.attendanceType" placeholder="考勤类型" clearable>
              <el-option label="出勤" value="PRESENT"  />
              <el-option label="请假" value="LEAVE"  />
              <el-option label="缺勤" value="ABSENT"  />
              <el-option label="迟到" value="LATE"  />
              <el-option label="早退" value="EARLY_LEAVE"  />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.dataSource" placeholder="数据来源" clearable>
              <el-option label="考勤机" value="ATTENDANCE_MACHINE"  />
              <el-option label="请假系统" value="LEAVE_SYSTEM"  />
              <el-option label="手动录入" value="MANUAL_ENTRY"  />
              <el-option label="移动APP" value="MOBILE_APP"  />
            </el-select>
          </el-col>
          <el-col :span="5">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
             />
          </el-col>
          <el-col :span="6">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
            <el-button type="success" @click="handleAdd">
              <el-icon><Plus /></el-icon>
              补录考勤
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.total }}</div>
              <div class="stats-label">总记录数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon present">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.present }}</div>
              <div class="stats-label">出勤记录</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon leave">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.leave }}</div>
              <div class="stats-label">请假记录</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon abnormal">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.abnormal }}</div>
              <div class="stats-label">异常记录</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 功能切换标签 -->
    <el-card class="tab-card" shadow="never">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="考勤记录列表" name="list">
          <!-- 考勤记录表格 -->
          <div class="table-header">
            <span class="table-title">考勤记录列表</span>
            <div class="table-actions">
              <el-button size="small" @click="handleExport">
                <el-icon><Download /></el-icon>
                导出
              </el-button>
              <el-button size="small" @click="handleBatchDelete" :disabled="selectedRows.length === 0">
                <el-icon><Delete /></el-icon>
                批量删除
              </el-button>
            </div>
          </div>

          <!-- 使用虚拟滚动表格 -->
          <VirtualTable
            v-loading="loading"
            :data="tableData"
            :columns="virtualTableColumns"
            :row-height="60"
            height="600px"
            :show-selection="true"
            :stripe="true"
            @selection-change="handleSelectionChange"
            @row-click="handleRowClick"
          >
            <!-- 员工姓名列插槽 -->
            <template #employeeName="{ row }">
              <div class="employee-info">
                <span class="employee-name">{{ row.employeeName }}</span>
                <span class="employee-code">{{ row.employeeNo }}</span>
              </div>
            </template>

            <!-- 考勤类型列插槽 -->
            <template #recordType="{ row }">
              <el-tag :type="getRecordTypeTagType(row.recordType)">
                {{ getRecordTypeText(row.recordType) }}
              </el-tag>
            </template>

            <!-- 操作列插槽 -->
            <template #operations="{ row }">
              <el-button size="small" type="primary" link @click.stop="handleView(row)">
                查看
              </el-button>
              <el-button 
                v-if="canEdit(row)" 
                size="small" 
                type="primary" 
                link 
                @click.stop="handleEdit(row)"
              >
                编辑
              </el-button>
              <el-button 
                v-if="canDelete(row)" 
                size="small" 
                type="danger" 
                link 
                @click.stop="handleDelete(row)"
              >
                删除
              </el-button>
            </template>
          </VirtualTable>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="pagination.page"
              v-model:page-size="pagination.size"
              :page-sizes="[10, 20, 50, 100]"
              :total="pagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
             />
          </div>
        </el-tab-pane>

        <el-tab-pane label="考勤日历" name="calendar">
          <!-- 考勤日历组件 -->
          <AttendanceCalendar
            :employee-id="calendarEmployeeId"
            :month="calendarMonth"
            @date-click="handleDateClick"
            @employee-change="handleEmployeeChange"
            @month-change="handleMonthChange"
          />
        </el-tab-pane>

        <el-tab-pane label="考勤统计" name="statistics">
          <!-- 考勤统计图表 -->
          <AttendanceStatistics
            :organization-id="statisticsOrganizationId"
            :date-range="statisticsDateRange"
            @organization-change="handleOrganizationChange"
            @date-range-change="handleDateRangeChange"
          />
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 考勤记录详情/编辑对话框 -->
    <AttendanceRecordDialog
      v-model:visible="dialogVisible"
      :attendance-record="currentAttendanceRecord"
      :mode="dialogMode"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  Document,
  Check,
  Calendar,
  Warning,
  Download,
  Delete
} from '@element-plus/icons-vue'
import { 
  queryAttendanceRecords,
  deleteAttendanceRecord
} from '@/api/attendance'
import type { 
  AttendanceRecord, 
  PageResult 
} from '@/api/attendance'
import HrVirtualTable from '@/components/common/HrVirtualTable.vue'
import type { TableColumn } from '@/components/common/VirtualTable.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref<AttendanceRecord[]>([])
const selectedRows = ref<AttendanceRecord[]>([])
const activeTab = ref('list')

// 搜索表单
const searchForm = reactive<unknown>({
  page: 0,
  size: 20,
  keyword: '',
  attendanceType: undefined,
  dataSource: undefined,
  dateRange: undefined,
  attendanceDateStart: undefined,
  attendanceDateEnd: undefined
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 统计信息
const stats = reactive({
  total: 0,
  present: 0,
  leave: 0,
  abnormal: 0
})

// 对话框相关
const dialogVisible = ref(false)
const dialogMode = ref<'view' | 'add' | 'edit'>('view')
const currentAttendanceRecord = ref<AttendanceRecord | null>(null)

// 日历相关
const calendarEmployeeId = ref<number>()
const calendarMonth = ref(new Date().toISOString().slice(0, 7))

// 统计相关
const statisticsOrganizationId = ref<number>()
const statisticsDateRange = ref<string[]>([])

// 虚拟表格列配置
const virtualTableColumns = computed<TableColumn[]>(() => [
  {
    prop: 'selection',
    label: '选择',
    width: 55,
    fixed: 'left',
    slot: 'selection'
  },
  {
    prop: 'employeeNo',
    label: '员工编号',
    width: 120,
    fixed: 'left'
  },
  {
    prop: 'employeeName',
    label: '姓名',
    width: 120,
    fixed: 'left',
    slot: 'employeeName'
  },
  {
    prop: 'departmentName',
    label: '所属部门',
    width: 150,
    showOverflowTooltip: true
  },
  {
    prop: 'recordTime',
    label: '打卡时间',
    width: 180
  },
  {
    prop: 'recordType',
    label: '考勤类型',
    width: 100,
    slot: 'recordType'
  },
  {
    prop: 'direction',
    label: '进出方向',
    width: 100,
    formatter: (row: AttendanceRecord) => row.direction === 'in' ? '进' : '出'
  },
  {
    prop: 'deviceName',
    label: '考勤设备',
    width: 150,
    showOverflowTooltip: true
  },
  {
    prop: 'verifyMode',
    label: '验证方式',
    width: 100
  },
  {
    prop: 'temperature',
    label: '体温',
    width: 80,
    formatter: (row: AttendanceRecord) => row.temperature ? `${row.temperature}°C` : '-'
  },
  {
    prop: 'operations',
    label: '操作',
    width: 150,
    fixed: 'right',
    slot: 'operations'
  }
])

// 获取考勤记录列表
const fetchAttendanceRecords = async () => {
  try {
    loading.value = true
    const params = {
      ...searchForm,
      page: pagination.page - 1,
      size: pagination.size
    }
    
    // 处理日期范围
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.attendanceDateStart = searchForm.dateRange[0]
      params.attendanceDateEnd = searchForm.dateRange[1]
    }
    delete params.dateRange
    
    const result = await queryAttendanceRecords(params)
    tableData.value = result.content
    pagination.total = result.totalElements
    
    // 更新统计信息
    updateStats()
  } catch (__error) {
    ElMessage.error('获取考勤记录列表失败')
  } finally {
    loading.value = false
  }
}

// 更新统计信息
const updateStats = () => {
  stats.total = pagination.total
  // 根据记录类型进行统计
  stats.present = tableData.value.filter(item => 
    ['CHECK_IN', 'CHECK_OUT'].includes(item.recordType)
  ).length
  stats.leave = 0 // 请假记录需要从其他接口获取
  stats.abnormal = tableData.value.filter(item => 
    item.temperature && item.temperature > 37.3
  ).length
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchAttendanceRecords()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    page: 0,
    size: 20,
    keyword: '',
    attendanceType: undefined,
    dataSource: undefined,
    dateRange: undefined
  })
  pagination.page = 1
  fetchAttendanceRecords()
}

// 新增考勤记录
const handleAdd = () => {
  currentAttendanceRecord.value = null
  dialogMode.value = 'add'
  dialogVisible.value = true
}

// 查看考勤记录
const handleView = (record: AttendanceRecord) => {
  currentAttendanceRecord.value = record
  dialogMode.value = 'view'
  dialogVisible.value = true
}

// 编辑考勤记录
const handleEdit = (record: AttendanceRecord) => {
  currentAttendanceRecord.value = record
  dialogMode.value = 'edit'
  dialogVisible.value = true
}

// 删除考勤记录
const handleDelete = async (record: AttendanceRecord) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除 "${record.employeeName}" 在 "${record.attendanceDate}" 的考勤记录吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteAttendanceRecord(record.id!)
    ElMessage.success('删除成功')
    fetchAttendanceRecords()
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 权限判断
const canEdit = (record: AttendanceRecord) => {
  // 只有手动录入的记录可以编辑
  return record.verifyMode === 'MANUAL'
}

const canDelete = (record: AttendanceRecord) => {
  // 只有手动录入的记录可以删除
  return record.verifyMode === 'MANUAL'
}

// 行点击
const handleRowClick = (row: AttendanceRecord) => {
  handleView(row)
}

// 表格选择变化
const handleSelectionChange = (selection: AttendanceRecord[]) => {
  selectedRows.value = selection
}

// 批量删除
const handleBatchDelete = () => {
  ElMessage.info('批量删除功能开发中...')
}

// 导出
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 标签页切换
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
}

// 日历相关事件
const handleDateClick = (date: string) => {
  // 点击日期时的处理逻辑
  }

const handleEmployeeChange = (employeeId: number) => {
  calendarEmployeeId.value = employeeId
}

const handleMonthChange = (month: string) => {
  calendarMonth.value = month
}

// 统计相关事件
const handleOrganizationChange = (organizationId: number) => {
  statisticsOrganizationId.value = organizationId
}

const handleDateRangeChange = (dateRange: string[]) => {
  statisticsDateRange.value = dateRange
}

// 分页相关
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchAttendanceRecords()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchAttendanceRecords()
}

// 对话框成功回调
const handleDialogSuccess = () => {
  fetchAttendanceRecords()
}

// 获取记录类型标签类型
const getRecordTypeTagType = (type: string) => {
  switch (type) {
    case 'CHECK_IN':
      return 'success'
    case 'CHECK_OUT':
      return 'primary'
    case 'OVERTIME_IN':
      return 'warning'
    case 'OVERTIME_OUT':
      return 'warning'
    default:
      return 'info'
  }
}

// 获取记录类型文本
const getRecordTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'CHECK_IN': '上班打卡',
    'CHECK_OUT': '下班打卡',
    'OVERTIME_IN': '加班签到',
    'OVERTIME_OUT': '加班签退',
    'BREAK_OUT': '外出',
    'BREAK_IN': '返回'
  }
  return typeMap[type] || type
}

// 初始化
onMounted(() => {
  fetchAttendanceRecords()
})
</script>

<style scoped>
.attendance-record-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  padding: 10px 0;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stats-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 18px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.present {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-icon.leave {
  background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
}

.stats-icon.abnormal {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 12px;
  color: #909399;
}

.tab-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 员工信息样式 */
.employee-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.employee-name {
  font-weight: 500;
  color: #303133;
}

.employee-code {
  font-size: 12px;
  color: #909399;
}
</style>