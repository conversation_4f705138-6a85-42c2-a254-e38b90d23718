<template>
  <div class="attendance-calendar">
    <!-- 控制栏 -->
    <div class="calendar-controls">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-select
            v-model="selectedEmployeeId"
            placeholder="选择员工"
            filterable
            remote
            :remote-method="searchEmployees"
            :loading="employeeLoading"
            style="width: 100%"
            @change="handleEmployeeChange"
          >
            <el-option
              v-for="employee in employeeOptions"
              :key="employee.id"
              :label="`${employee.name} (${employee.employeeCode})`"
              :value="employee.id"
             />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-date-picker
            v-model="selectedMonth"
            type="month"
            placeholder="选择月份"
            format="YYYY-MM"
            value-format="YYYY-MM"
            style="width: 100%"
            @change="handleMonthChange"
           />
        </el-col>
        <el-col :span="10">
          <div class="calendar-legend">
            <span class="legend-item">
              <span class="legend-dot present"></span>
              出勤
            </span>
            <span class="legend-item">
              <span class="legend-dot leave"></span>
              请假
            </span>
            <span class="legend-item">
              <span class="legend-dot absent"></span>
              旷工
            </span>
            <span class="legend-item">
              <span class="legend-dot late"></span>
              迟到/早退
            </span>
            <span class="legend-item">
              <span class="legend-dot weekend"></span>
              周末
            </span>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 考勤统计概览 -->
    <el-card v-if="selectedEmployeeId" class="statistics-card" shadow="never">
      <template #header>
        <span>{{ currentEmployeeName }} - {{ selectedMonth }} 考勤统计</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="4">
          <div class="stat-item">
            <div class="stat-number">{{ monthlyStats.workDays }}</div>
            <div class="stat-label">应出勤天数</div>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="stat-item">
            <div class="stat-number">{{ monthlyStats.presentDays }}</div>
            <div class="stat-label">实际出勤</div>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="stat-item">
            <div class="stat-number">{{ monthlyStats.leaveDays }}</div>
            <div class="stat-label">请假天数</div>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="stat-item">
            <div class="stat-number">{{ monthlyStats.absentDays }}</div>
            <div class="stat-label">旷工天数</div>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="stat-item">
            <div class="stat-number">{{ monthlyStats.lateTimes }}</div>
            <div class="stat-label">迟到次数</div>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="stat-item">
            <div class="stat-number">{{ monthlyStats.attendanceRate }}%</div>
            <div class="stat-label">出勤率</div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 日历主体 -->
    <el-card class="calendar-card" shadow="never">
      <el-calendar v-model="calendarValue" :range="calendarRange">
        <template #date-cell="{ data }">
          <div
            class="calendar-day"
            :class="getDayClass(data.date)"
            @click="handleDateClick(data.date)"
          >
            <div class="day-number">{{ data.day.split('-').pop() }}</div>
            <div v-if="getAttendanceInfo(data.date)" class="attendance-info">
              <div class="attendance-status" :class="getAttendanceStatusClass(data.date)">
                {{ getAttendanceStatusText(data.date) }}
              </div>
              <div v-if="getCheckInTime(data.date)" class="check-time">
                {{ getCheckInTime(data.date) }}
              </div>
            </div>
            <div v-else-if="isWorkDay(data.date)" class="no-attendance">
              <span class="no-data">无记录</span>
            </div>
          </div>
        </template>
      </el-calendar>
    </el-card>

    <!-- 日期详情对话框 -->
    <el-dialog
      v-model="dateDetailVisible"
      :title="`${selectedDate} 考勤详情`"
      width="500px"
    >
      <div v-if="selectedDateAttendance" class="date-detail">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="员工">
            {{ currentEmployeeName }}
          </el-descriptions-item>
          <el-descriptions-item label="日期">
            {{ selectedDate }}
          </el-descriptions-item>
          <el-descriptions-item label="考勤状态">
            <el-tag :type="getAttendanceTypeTagType(selectedDateAttendance.attendanceType)">
              {{ selectedDateAttendance.attendanceTypeName }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item v-if="selectedDateAttendance.checkInTime" label="签到时间">
            {{ selectedDateAttendance.checkInTime }}
          </el-descriptions-item>
          <el-descriptions-item v-if="selectedDateAttendance.checkOutTime" label="签退时间">
            {{ selectedDateAttendance.checkOutTime }}
          </el-descriptions-item>
          <el-descriptions-item v-if="selectedDateAttendance.workHours" label="工作时长">
            {{ selectedDateAttendance.workHours }}小时
          </el-descriptions-item>
          <el-descriptions-item label="数据来源">
            <el-tag size="small" :type="getDataSourceTagType(selectedDateAttendance.dataSource)">
              {{ selectedDateAttendance.dataSourceName }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item v-if="selectedDateAttendance.remarks" label="备注">
            {{ selectedDateAttendance.remarks }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div v-else class="no-attendance-detail">
        <el-empty description="该日期无考勤记录"  />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'AttendanceCalendar'
})
 
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { attendanceApi, type AttendanceRecord } from '@/api/attendance'
import { employeeApi, type Employee } from '@/api/employee'

// Props
interface Props {
  employeeId?: number
  month?: string
}

const props = withDefaults(defineProps<Props>(), {
  month: () => new Date().toISOString().slice(0, 7)
})

// Emits
const emit = defineEmits<{
  'date-click': [date: string]
  'employee-change': [employeeId: number]
  'month-change': [month: string]
}>()

// 响应式数据
const employeeLoading = ref(false)
const employeeOptions = ref<Employee[]>([])
const selectedEmployeeId = ref<number>()
const selectedMonth = ref(props.month)
const currentEmployeeName = ref('')
const attendanceRecords = ref<AttendanceRecord[]>([])
const dateDetailVisible = ref(false)
const selectedDate = ref('')
const selectedDateAttendance = ref<AttendanceRecord | null>(null)

// 日历值和范围
const calendarValue = ref(new Date())
const calendarRange = computed(() => {
  const year = parseInt(selectedMonth.value.split('-')[0])
  const month = parseInt(selectedMonth.value.split('-')[1])
  return [
    new Date(year, month - 1, 1),
    new Date(year, month, 0)
  ]
})

// 月度统计
const monthlyStats = reactive({
  workDays: 0,
  presentDays: 0,
  leaveDays: 0,
  absentDays: 0,
  lateTimes: 0,
  attendanceRate: 0
})

// 搜索员工
const searchEmployees = async (query: string) => {
  if (!query) {
    employeeOptions.value = []
    return
  }
  
  try {
    employeeLoading.value = true
    const result = await employeeApi.queryEmployees({
      keyword: query,
      size: 20
    })
    employeeOptions.value = result.content
  } catch (__error) {
    console.error('搜索员工失败:', error)
  } finally {
    employeeLoading.value = false
  }
}

// 获取考勤记录
const fetchAttendanceRecords = async () => {
  if (!selectedEmployeeId.value || !selectedMonth.value) return

  try {
    const startDate = `${selectedMonth.value}-01`
    const year = parseInt(selectedMonth.value.split('-')[0])
    const month = parseInt(selectedMonth.value.split('-')[1])
    const endDate = `${selectedMonth.value}-${new Date(year, month, 0).getDate()}`
    
    const records = await attendanceApi.getAttendanceRecordsByEmployee(
      selectedEmployeeId.value,
      startDate,
      endDate
    )
    attendanceRecords.value = records
    calculateMonthlyStats()
  } catch (__error) {
    console.error('获取考勤记录失败:', error)
    ElMessage.error('获取考勤记录失败')
  }
}

// 计算月度统计
const calculateMonthlyStats = () => {
  const year = parseInt(selectedMonth.value.split('-')[0])
  const month = parseInt(selectedMonth.value.split('-')[1])
  const daysInMonth = new Date(year, month, 0).getDate()
  
  let workDays = 0
  let presentDays = 0
  let leaveDays = 0
  let absentDays = 0
  let lateTimes = 0
  
  for (let day = 1; day <= daysInMonth; day++) {
    const date = `${selectedMonth.value}-${day.toString().padStart(2, '0')}`
    const dayOfWeek = new Date(year, month - 1, day).getDay()
    
    // 简单判断工作日（周一到周五）
    if (dayOfWeek >= 1 && dayOfWeek <= 5) {
      workDays++
      
      const record = attendanceRecords.value.find(r => r.attendanceDate === date)
      if (record) {
        switch (record.attendanceType) {
          case 'PRESENT':
            presentDays++
            break
          case 'LEAVE':
            leaveDays++
            break
          case 'ABSENT':
            absentDays++
            break
          case 'LATE':
          case 'EARLY_LEAVE':
            presentDays++
            lateTimes++
            break
        }
      } else {
        absentDays++
      }
    }
  }
  
  Object.assign(monthlyStats, {
    workDays,
    presentDays,
    leaveDays,
    absentDays,
    lateTimes,
    attendanceRate: workDays > 0 ? Math.round((presentDays / workDays) * 100) : 0
  })
}

// 获取日期样式类
const getDayClass = (date: string) => {
  const dayOfWeek = new Date(date).getDay()
  const classes = []
  
  if (dayOfWeek === 0 || dayOfWeek === 6) {
    classes.push('weekend')
  } else {
    classes.push('workday')
  }
  
  const attendance = getAttendanceInfo(date)
  if (attendance) {
    classes.push(`attendance-${attendance.attendanceType.toLowerCase()}`)
  }
  
  return classes
}

// 获取考勤信息
const getAttendanceInfo = (date: string) => {
  return attendanceRecords.value.find(record => record.attendanceDate === date)
}

// 获取考勤状态样式类
const getAttendanceStatusClass = (date: string) => {
  const attendance = getAttendanceInfo(date)
  if (!attendance) return ''
  
  switch (attendance.attendanceType) {
    case 'PRESENT':
      return 'status-present'
    case 'LEAVE':
      return 'status-leave'
    case 'ABSENT':
      return 'status-absent'
    case 'LATE':
    case 'EARLY_LEAVE':
      return 'status-late'
    default:
      return ''
  }
}

// 获取考勤状态文本
const getAttendanceStatusText = (date: string) => {
  const attendance = getAttendanceInfo(date)
  return attendance?.attendanceTypeName || ''
}

// 获取签到时间
const getCheckInTime = (date: string) => {
  const attendance = getAttendanceInfo(date)
  return attendance?.checkInTime || ''
}

// 判断是否为工作日
const isWorkDay = (date: string) => {
  const dayOfWeek = new Date(date).getDay()
  return dayOfWeek >= 1 && dayOfWeek <= 5
}

// 处理日期点击
const handleDateClick = (date: string) => {
  selectedDate.value = date
  selectedDateAttendance.value = getAttendanceInfo(date) || null
  dateDetailVisible.value = true
  emit('date-click', date)
}

// 处理员工变化
const handleEmployeeChange = (employeeId: number) => {
  const employee = employeeOptions.value.find(emp => emp.id === employeeId)
  if (employee) {
    currentEmployeeName.value = employee.name
  }
  emit('employee-change', employeeId)
  fetchAttendanceRecords()
}

// 处理月份变化
const handleMonthChange = (month: string) => {
  emit('month-change', month)
  fetchAttendanceRecords()
}

// 获取考勤类型标签类型
const getAttendanceTypeTagType = (type: string) => {
  switch (type) {
    case 'PRESENT':
      return 'success'
    case 'LEAVE':
      return 'warning'
    case 'ABSENT':
      return 'danger'
    case 'LATE':
    case 'EARLY_LEAVE':
      return 'warning'
    default:
      return ''
  }
}

// 获取数据来源标签类型
const getDataSourceTagType = (source: string) => {
  switch (source) {
    case 'ATTENDANCE_MACHINE':
      return 'primary'
    case 'LEAVE_SYSTEM':
      return 'success'
    case 'MANUAL_ENTRY':
      return 'warning'
    case 'MOBILE_APP':
      return 'info'
    default:
      return ''
  }
}

// 监听props变化
watch(() => props.employeeId, (newVal) => {
  if (newVal) {
    selectedEmployeeId.value = newVal
  }
})

watch(() => props.month, (newVal) => {
  selectedMonth.value = newVal
})

// 初始化
onMounted(() => {
  if (props.employeeId) {
    selectedEmployeeId.value = props.employeeId
  }
})
</script>

<style scoped>
.attendance-calendar {
  padding: 20px;
}

.calendar-controls {
  margin-bottom: 20px;
}

.calendar-legend {
  display: flex;
  align-items: center;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #606266;
}

.legend-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.legend-dot.present {
  background: #67c23a;
}

.legend-dot.leave {
  background: #e6a23c;
}

.legend-dot.absent {
  background: #f56c6c;
}

.legend-dot.late {
  background: #f56c6c;
}

.legend-dot.weekend {
  background: #909399;
}

.statistics-card {
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background: #fafafa;
}

.stat-number {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.calendar-card {
  margin-bottom: 20px;
}

.calendar-day {
  height: 80px;
  padding: 4px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s;
  position: relative;
}

.calendar-day:hover {
  background: #f5f7fa;
}

.calendar-day.weekend {
  background: #f9f9f9;
}

.day-number {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.attendance-info {
  font-size: 10px;
  line-height: 1.2;
}

.attendance-status {
  padding: 1px 4px;
  border-radius: 2px;
  color: white;
  margin-bottom: 2px;
}

.status-present {
  background: #67c23a;
}

.status-leave {
  background: #e6a23c;
}

.status-absent {
  background: #f56c6c;
}

.status-late {
  background: #f56c6c;
}

.check-time {
  color: #606266;
  font-size: 9px;
}

.no-attendance {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.no-data {
  color: #c0c4cc;
  font-size: 10px;
}

.date-detail {
  padding: 10px 0;
}

.no-attendance-detail {
  text-align: center;
  padding: 20px 0;
}

:deep(.el-calendar__body) {
  padding: 12px;
}

:deep(.el-calendar-day) {
  height: 80px;
  padding: 0;
}

:deep(.el-calendar-day:hover) {
  background: transparent;
}
</style>
