<template>
  <div class="attendance-alert">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>异常预警管理</h2>
      <p>智能化考勤异常预警系统，提供预防性风险管理</p>
    </div>

    <!-- 统计仪表板 -->
    <el-card class="dashboard-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>预警概览</span>
          <div class="header-actions">
            <el-button size="small" @click="refreshDashboard">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="dashboard-content">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-card critical">
              <div class="stat-icon">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ dashboard.summary.criticalAlerts }}</div>
                <div class="stat-label">紧急预警</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card active">
              <div class="stat-icon">
                <el-icon><Bell /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ dashboard.summary.totalActiveAlerts }}</div>
                <div class="stat-label">活跃预警</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card today">
              <div class="stat-icon">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ dashboard.summary.newAlertsToday }}</div>
                <div class="stat-label">今日新增</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card response">
              <div class="stat-icon">
                <el-icon><Timer /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ formatDuration(dashboard.summary.avgResponseTime) }}</div>
                <div class="stat-label">平均响应时间</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 预警规则管理 -->
    <el-card class="rules-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>预警规则</span>
          <div class="header-actions">
            <el-button type="primary" @click="handleCreateRule">
              <el-icon><Plus /></el-icon>
              新建规则
            </el-button>
          </div>
        </div>
      </template>

      <!-- 规则筛选 -->
      <div class="rules-filter">
        <el-form :model="ruleFilter" inline>
          <el-form-item label="规则类型">
            <el-select v-model="ruleFilter.ruleType" placeholder="选择类型" clearable>
              <el-option label="频次预警" value="FREQUENCY"  />
              <el-option label="模式预警" value="PATTERN"  />
              <el-option label="阈值预警" value="THRESHOLD"  />
              <el-option label="趋势预警" value="TREND"  />
              <el-option label="异常预警" value="ANOMALY"  />
            </el-select>
          </el-form-item>
          <el-form-item label="目标类型">
            <el-select v-model="ruleFilter.targetType" placeholder="选择目标" clearable>
              <el-option label="员工" value="EMPLOYEE"  />
              <el-option label="部门" value="DEPARTMENT"  />
              <el-option label="岗位" value="POSITION"  />
              <el-option label="全局" value="GLOBAL"  />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="ruleFilter.status" placeholder="选择状态" clearable>
              <el-option label="启用" value="ACTIVE"  />
              <el-option label="禁用" value="INACTIVE"  />
              <el-option label="暂停" value="SUSPENDED"  />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleRuleSearch">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="handleRuleReset">
              <el-icon><RefreshLeft /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 规则列表 -->
      <el-table
        v-loading="ruleLoading"
        :data="ruleList"
        stripe
        @selection-change="handleRuleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="ruleName" label="规则名称" min-width="180"  />
        <el-table-column prop="ruleType" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getRuleTypeColor(row.ruleType)" size="small">
              {{ getRuleTypeLabel(row.ruleType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="targetType" label="目标类型" width="100">
          <template #default="{ row }">
            <el-tag type="info" size="small">
              {{ getTargetTypeLabel(row.targetType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="targetName" label="目标名称" width="120"  />
        <el-table-column prop="priority" label="优先级" width="100">
          <template #default="{ row }">
            <el-tag :type="getPriorityColor(row.priority)" size="small">
              {{ getPriorityLabel(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="triggerCount" label="触发次数" width="100"  />
        <el-table-column prop="lastTriggered" label="最近触发" width="160"  />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)" size="small">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" size="small" @click="handleRuleView(row)">
              查看
            </el-button>
            <el-button link type="primary" size="small" @click="handleRuleEdit(row)">
              编辑
            </el-button>
            <el-button link type="primary" size="small" @click="handleRuleTest(row)">
              测试
            </el-button>
            <el-button
              link
              :type="row.status === 'ACTIVE' ? 'warning' : 'success'"
              size="small"
              @click="handleRuleToggle(row)"
            >
              {{ row.status === 'ACTIVE' ? '禁用' : '启用' }}
            </el-button>
            <el-button link type="danger" size="small" @click="handleRuleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 规则列表分页 -->
      <el-pagination
        v-model:current-page="rulePagination.current"
        v-model:page-size="rulePagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="rulePagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleRuleSizeChange"
        @current-change="handleRuleCurrentChange"
       />
    </el-card>

    <!-- 预警记录 -->
    <el-card class="alerts-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>预警记录</span>
          <div class="header-actions">
            <el-button @click="handleExportAlerts">
              <el-icon><Download /></el-icon>
              导出记录
            </el-button>
          </div>
        </div>
      </template>

      <!-- 记录筛选 -->
      <div class="alerts-filter">
        <el-form :model="alertFilter" inline>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="alertFilter.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              clearable
             />
          </el-form-item>
          <el-form-item label="预警级别">
            <el-select v-model="alertFilter.alertLevel" placeholder="选择级别" clearable>
              <el-option label="警告" value="WARNING"  />
              <el-option label="严重" value="CRITICAL"  />
              <el-option label="紧急" value="EMERGENCY"  />
            </el-select>
          </el-form-item>
          <el-form-item label="处理状态">
            <el-select v-model="alertFilter.status" placeholder="选择状态" clearable>
              <el-option label="已触发" value="TRIGGERED"  />
              <el-option label="已确认" value="ACKNOWLEDGED"  />
              <el-option label="已解决" value="RESOLVED"  />
              <el-option label="已忽略" value="IGNORED"  />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleAlertSearch">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="handleAlertReset">
              <el-icon><RefreshLeft /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 记录列表 -->
      <el-table
        v-loading="alertLoading"
        :data="alertList"
        stripe
        @selection-change="handleAlertSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="triggerTime" label="触发时间" width="160"  />
        <el-table-column prop="ruleName" label="规则名称" min-width="180"  />
        <el-table-column prop="alertLevel" label="预警级别" width="100">
          <template #default="{ row }">
            <el-tag :type="getAlertLevelColor(row.alertLevel)" size="small">
              {{ getAlertLevelLabel(row.alertLevel) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="targetName" label="目标对象" width="120"  />
        <el-table-column prop="message" label="预警信息" min-width="200"  />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getAlertStatusColor(row.status)" size="small">
              {{ getAlertStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="acknowledgeBy" label="确认人" width="100"  />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" size="small" @click="handleAlertView(row)">
              查看
            </el-button>
            <el-button
              v-if="row.status === 'TRIGGERED'"
              link
              type="primary"
              size="small"
              @click="handleAlertAcknowledge(row)"
            >
              确认
            </el-button>
            <el-button
              v-if="row.status === 'ACKNOWLEDGED'"
              link
              type="success"
              size="small"
              @click="handleAlertResolve(row)"
            >
              解决
            </el-button>
            <el-button
              v-if="['TRIGGERED', 'ACKNOWLEDGED'].includes(row.status)"
              link
              type="warning"
              size="small"
              @click="handleAlertIgnore(row)"
            >
              忽略
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 记录列表分页 -->
      <el-pagination
        v-model:current-page="alertPagination.current"
        v-model:page-size="alertPagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="alertPagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleAlertSizeChange"
        @current-change="handleAlertCurrentChange"
       />
    </el-card>

    <!-- 规则创建/编辑对话框 -->
    <RuleDialog
      v-model="ruleDialogVisible"
      :rule="currentRule"
      :mode="ruleDialogMode"
      @confirm="handleRuleDialogConfirm"
    />

    <!-- 预警详情对话框 -->
    <AlertDetailDialog
      v-model="alertDetailVisible"
      :alert="currentAlert"
      @action="handleAlertAction"
    />

    <!-- 规则测试对话框 -->
    <RuleTestDialog
      v-model="ruleTestVisible"
      :rule="currentRule"
      @test="handleRuleTestConfirm"
    />
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'AttendanceAlert'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  RefreshLeft,
  Plus,
  Download,
  Refresh,
  Warning,
  Bell,
  TrendCharts,
  Timer
} from '@element-plus/icons-vue'
import { attendanceApi } from '@/api/attendance'
import RuleDialog from './components/RuleDialog.vue'
import HrAlertDetailDialog from './components/HrAlertDetailDialog.vue'
import RuleTestDialog from './components/RuleTestDialog.vue'

// 响应式数据
const ruleLoading = ref(false)
const alertLoading = ref(false)
const ruleList = ref([])
const alertList = ref([])
const selectedRules = ref([])
const selectedAlerts = ref([])
const ruleDialogVisible = ref(false)
const alertDetailVisible = ref(false)
const ruleTestVisible = ref(false)
const currentRule = ref(null)
const currentAlert = ref(null)
const ruleDialogMode = ref('create')

// 仪表板数据
const dashboard = reactive({
  summary: {
    totalActiveAlerts: 0,
    newAlertsToday: 0,
    criticalAlerts: 0,
    avgResponseTime: 0,
    topAlertTypes: []
  },
  recentAlerts: [],
  trendData: {
    labels: [],
    alertCounts: [],
    responseTime: [],
    resolutionRate: []
  }
})

// 规则筛选
const ruleFilter = reactive({
  ruleType: '',
  targetType: '',
  status: ''
})

// 规则分页
const rulePagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

// 预警筛选
const alertFilter = reactive({
  dateRange: [],
  alertLevel: '',
  status: ''
})

// 预警分页
const alertPagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

// 获取仪表板数据
const fetchDashboard = async () => {
  try {
    const {data: _data} =  await attendanceApi.getAlertDashboard({
      dateRange: [],
      refreshInterval: 5000
    })
    Object.assign(dashboard, data)
  } catch (__error) {
    console.error('获取仪表板数据失败:', error)
    ElMessage.error('获取数据失败')
  }
}

// 获取规则列表
const fetchRules 

  .page-header {
    margin-bottom: 20px;
    
    h2 {
      margin: 0 0 5px 0;
      color: #303133;
    }
    
    p {
      margin: 0;
      color: #909399;
      font-size: 14px;
    }
  }

  .dashboard-card {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .dashboard-content {
      .stat-card {
        display: flex;
        align-items: center;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 6px;
        border-left: 4px solid #ddd;
        
        &.critical {
          border-left-color: #f56c6c;
          
          .stat-icon {
            color: #f56c6c;
          }
        }
        
        &.active {
          border-left-color: #409eff;
          
          .stat-icon {
            color: #409eff;
          }
        }
        
        &.today {
          border-left-color: #67c23a;
          
          .stat-icon {
            color: #67c23a;
          }
        }
        
        &.response {
          border-left-color: #e6a23c;
          
          .stat-icon {
            color: #e6a23c;
          }
        }
        
        .stat-icon {
          font-size: 24px;
          margin-right: 15px;
        }
        
        .stat-info {
          .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 5px;
          }
          
          .stat-label {
            font-size: 14px;
            color: #606266;
          }
        }
      }
    }
  }

  .rules-card,
  .alerts-card {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .rules-filter,
    .alerts-filter {
      margin-bottom: 20px;
      padding: 15px;
      background: #f8f9fa;
      border-radius: 6px;
    }

    :deep(.el-pagination) {
      margin-top: 20px;
      justify-content: flex-end;
    }
  }
}
</style>