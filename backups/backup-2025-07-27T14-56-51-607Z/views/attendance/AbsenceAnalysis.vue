<template>
  <div class="absence-analysis">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>缺勤分析</h2>
      <div class="header-actions">
        <el-button @click="exportData">导出报告</el-button>
        <el-button @click="showPredictionDialog">预测分析</el-button>
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 查询条件 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" size="default">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
           />
        </el-form-item>
        <el-form-item label="部门">
          <el-tree-select
            v-model="searchForm.departmentId"
            :data="departmentTree"
            :props="{ label: 'name', value: 'id' }"
            placeholder="请选择部门"
            clearable
            filterable
           />
        </el-form-item>
        <el-form-item label="员工">
          <el-select
            v-model="searchForm.employeeId"
            placeholder="请选择员工"
            clearable
            filterable
          >
            <el-option
              v-for="emp in employeeList"
              :key="emp.id"
              :label="emp.name"
              :value="emp.id"
             />
          </el-select>
        </el-form-item>
        <el-form-item label="缺勤类型">
          <el-select
            v-model="searchForm.absenceType"
            placeholder="全部类型"
            clearable
            multiple
          >
            <el-option
              v-for="reason in absenceReasons"
              :key="reason.code"
              :label="reason.name"
              :value="reason.code"
             />
          </el-select>
        </el-form-item>
        <el-form-item label="分析类型">
          <el-select v-model="searchForm.analysisType" placeholder="分析类型">
            <el-option value="summary" label="综合分析"  />
            <el-option value="trend" label="趋势分析"  />
            <el-option value="cause" label="原因分析"  />
            <el-option value="prediction" label="预测分析"  />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 缺勤概览 -->
    <div class="absence-overview">
      <el-row :gutter="20">
        <el-col :span="4" v-for="item in overviewData" :key="item.key">
          <div class="overview-card" :class="`card-${item.type}`">
            <div class="card-icon">
              <el-icon :size="32"><component :is="item.icon" /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ item.value }}</div>
              <div class="card-label">{{ item.label }}</div>
              <div class="card-trend" v-if="item.trend !== undefined">
                <el-icon v-if="item.trend > 0" class="trend-up">
                  <CaretTop />
                </el-icon>
                <el-icon v-else-if="item.trend < 0" class="trend-down">
                  <CaretBottom />
                </el-icon>
                <span>{{ Math.abs(item.trend) }}%</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 图表展示 -->
    <el-row :gutter="20" class="charts-container">
      <!-- 缺勤趋势图 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>缺勤趋势分析</span>
          </template>
          <div ref="trendChart" class="chart-container"></div>
        </el-card>
      </el-col>

      <!-- 缺勤类型分布 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>缺勤类型分布</span>
          </template>
          <div ref="typeChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-container">
      <!-- 部门缺勤对比 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>部门缺勤对比</span>
          </template>
          <div ref="departmentChart" class="chart-container"></div>
        </el-card>
      </el-col>

      <!-- 季节性模式 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>缺勤季节性模式</span>
          </template>
          <div ref="seasonalChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-container">
      <!-- 风险矩阵 -->
      <el-col :span="24">
        <el-card class="chart-card">
          <template #header>
            <span>员工缺勤风险矩阵</span>
          </template>
          <div ref="riskChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 缺勤明细表格 -->
    <el-card class="table-card">
      <template #header>
        <span>缺勤分析明细</span>
      </template>
      
      <el-table
        :data="tableData"
        stripe
        border
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column prop="employeeName" label="姓名" width="100" fixed="left"  />
        <el-table-column prop="employeeNo" label="工号" width="100"  />
        <el-table-column prop="department" label="部门" width="120"  />
        <el-table-column prop="position" label="职位" width="100"  />
        <el-table-column prop="totalAbsenceDays" label="缺勤天数" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getAbsenceTag(row.totalAbsenceDays)">
              {{ row.totalAbsenceDays }}天
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="absenceRate" label="缺勤率" width="100" align="center">
          <template #default="{ row }">
            {{ row.absenceRate }}%
          </template>
        </el-table-column>
        <el-table-column label="缺勤类型统计" align="center" min-width="200">
          <template #default="{ row }">
            <div class="absence-types">
              <el-tag
                v-for="(data, type) in row.absencesByType"
                :key="type"
                size="small"
                style="margin: 2px;"
              >
                {{ getAbsenceTypeName(type) }}: {{ data.days }}天
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="风险等级" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getRiskTag(row.absencePattern.riskLevel)">
              {{ getRiskText(row.absencePattern.riskLevel) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="影响分析" align="center" min-width="150">
          <template #default="{ row }">
            <el-progress
              :percentage="row.impactAnalysis.workloadImpact"
              :format="() => '工作负荷'"
              :stroke-width="6"
              style="margin-bottom: 4px;"
            />
            <el-progress
              :percentage="row.impactAnalysis.teamImpact"
              :format="() => '团队影响'"
              :stroke-width="6"
              color="#e6a23c"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="text" @click="viewDetail(row)">详情</el-button>
            <el-button type="text" @click="viewPattern(row)">模式</el-button>
            <el-button type="text" @click="viewImpact(row)">影响</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="缺勤详细分析"
      width="900px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-descriptions title="基本信息" :column="1" border>
            <el-descriptions-item label="员工姓名">{{ currentEmployee.employeeName }}</el-descriptions-item>
            <el-descriptions-item label="工号">{{ currentEmployee.employeeNo }}</el-descriptions-item>
            <el-descriptions-item label="部门">{{ currentEmployee.department }}</el-descriptions-item>
            <el-descriptions-item label="职位">{{ currentEmployee.position }}</el-descriptions-item>
            <el-descriptions-item label="总缺勤天数">{{ currentEmployee.totalAbsenceDays }}天</el-descriptions-item>
            <el-descriptions-item label="缺勤率">{{ currentEmployee.absenceRate }}%</el-descriptions-item>
          </el-descriptions>
        </el-col>
        <el-col :span="12">
          <el-descriptions title="影响分析" :column="1" border>
            <el-descriptions-item label="工作负荷影响">{{ currentEmployee.impactAnalysis?.workloadImpact }}%</el-descriptions-item>
            <el-descriptions-item label="团队影响">{{ currentEmployee.impactAnalysis?.teamImpact }}%</el-descriptions-item>
            <el-descriptions-item label="成本影响">¥{{ currentEmployee.impactAnalysis?.costImpact }}</el-descriptions-item>
            <el-descriptions-item label="风险等级">
              <el-tag :type="getRiskTag(currentEmployee.absencePattern?.riskLevel)">
                {{ getRiskText(currentEmployee.absencePattern?.riskLevel) }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-col>
      </el-row>
      
      <el-divider>最近缺勤记录</el-divider>
      <el-table :data="currentEmployee.recentAbsences" size="small">
        <el-table-column prop="date" label="日期" width="100"  />
        <el-table-column prop="type" label="类型" width="100">
          <template #default="{ row }">
            {{ getAbsenceTypeName(row.type) }}
          </template>
        </el-table-column>
        <el-table-column prop="reason" label="原因" min-width="150"  />
        <el-table-column prop="duration" label="时长" width="80">
          <template #default="{ row }">
            {{ row.duration }}天
          </template>
        </el-table-column>
        <el-table-column prop="approved" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.approved ? 'success' : 'warning'" size="small">
              {{ row.approved ? '已批准' : '待审批' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 模式分析对话框 -->
    <el-dialog
      v-model="patternDialogVisible"
      title="缺勤模式分析"
      width="700px"
    >
      <div class="pattern-analysis">
        <div class="pattern-section">
          <h4>高频缺勤日期</h4>
          <div class="pattern-tags">
            <el-tag
              v-for="day in currentEmployee.absencePattern?.frequentDays"
              :key="day"
              type="warning"
              style="margin-right: 8px; margin-bottom: 8px;"
            >
              {{ day }}
            </el-tag>
          </div>
        </div>
        <div class="pattern-section">
          <h4>季节性趋势</h4>
          <p>{{ currentEmployee.absencePattern?.seasonalTrend }}</p>
        </div>
        <div class="pattern-section">
          <h4>风险评估</h4>
          <el-tag :type="getRiskTag(currentEmployee.absencePattern?.riskLevel)" size="large">
            {{ getRiskText(currentEmployee.absencePattern?.riskLevel) }}风险
          </el-tag>
        </div>
      </div>
    </el-dialog>

    <!-- 影响分析对话框 -->
    <el-dialog
      v-model="impactDialogVisible"
      title="缺勤影响分析"
      width="600px"
    >
      <div class="impact-analysis">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="impact-item">
              <h4>工作负荷影响</h4>
              <el-progress
                type="circle"
                :percentage="currentEmployee.impactAnalysis?.workloadImpact"
                :color="getProgressColor(currentEmployee.impactAnalysis?.workloadImpact)"
               />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="impact-item">
              <h4>团队影响</h4>
              <el-progress
                type="circle"
                :percentage="currentEmployee.impactAnalysis?.teamImpact"
                :color="getProgressColor(currentEmployee.impactAnalysis?.teamImpact)"
               />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="impact-item">
              <h4>成本影响</h4>
              <div class="cost-impact">
                <span class="cost-value">¥{{ currentEmployee.impactAnalysis?.costImpact }}</span>
                <span class="cost-label">直接成本</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <!-- 预测分析对话框 -->
    <el-dialog
      v-model="predictionDialogVisible"
      title="缺勤预测分析"
      width="800px"
    >
      <div class="prediction-content" v-loading="predictionLoading">
        <el-tabs v-model="predictionTab">
          <el-tab-pane label="个人预测" name="personal">
            <el-table :data="predictionData.nextMonthRisk" size="small">
              <el-table-column prop="employeeName" label="员工" width="100"  />
              <el-table-column prop="riskScore" label="风险评分" width="100">
                <template #default="{ row }">
                  <el-tag :type="getRiskScoreTag(row.riskScore)">
                    {{ row.riskScore }}分
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="predictedDays" label="预测缺勤天数" width="120">
                <template #default="{ row }">
                  {{ row.predictedDays }}天
                </template>
              </el-table-column>
              <el-table-column prop="recommendations" label="建议措施" min-width="200">
                <template #default="{ row }">
                  <el-tag
                    v-for="rec in row.recommendations"
                    :key="rec"
                    size="small"
                    style="margin: 2px;"
                  >
                    {{ rec }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="部门预测" name="department">
            <el-table :data="predictionData.departmentRisk" size="small">
              <el-table-column prop="departmentName" label="部门" width="150"  />
              <el-table-column prop="riskLevel" label="风险等级" width="100">
                <template #default="{ row }">
                  <el-tag :type="getRiskTag(row.riskLevel)">
                    {{ getRiskText(row.riskLevel) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="predictedImpact" label="预测影响" width="100">
                <template #default="{ row }">
                  {{ row.predictedImpact }}%
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Refresh,
  Calendar,
  User,
  Warning,
  Money,
  TrendCharts,
  CaretTop,
  CaretBottom
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import { attendanceApi } from '@/api/attendance'
import { exportToExcel } from '@/utils/export'

// 搜索表单
const searchForm = reactive({
  dateRange: [
    dayjs().subtract(90, 'day').format('YYYY-MM-DD'),
    dayjs().format('YYYY-MM-DD')
  ],
  departmentId: '',
  employeeId: '',
  absenceType: [],
  analysisType: 'summary'
})

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 响应式数据
const loading = ref(false)
const predictionLoading = ref(false)
const departmentTree = ref([])
const employeeList = ref([])
const absenceReasons = ref([])
const tableData = ref([])
const detailDialogVisible = ref(false)
const patternDialogVisible = ref(false)
const impactDialogVisible = ref(false)
const predictionDialogVisible = ref(false)
const currentEmployee = ref<unknown>({})
const predictionData = ref<unknown>({})
const predictionTab = ref('personal')

// 图表实例
const trendChart = ref()
const typeChart = ref()
const departmentChart = ref()
const seasonalChart = ref()
const riskChart = ref()

// 概览数据
const overviewData = ref([
  {
    key: 'totalDays',
    label: '总缺勤天数',
    value: 0,
    type: 'danger',
    icon: Calendar,
    trend: 0
  },
  {
    key: 'absenceRate',
    label: '平均缺勤率',
    value: '0%',
    type: 'warning',
    icon: TrendCharts,
    trend: 0
  },
  {
    key: 'affectedEmployees',
    label: '涉及人数',
    value: 0,
    type: 'info',
    icon: User,
    trend: 0
  },
  {
    key: 'highRiskEmployees',
    label: '高风险人员',
    value: 0,
    type: 'danger',
    icon: Warning,
    trend: 0
  },
  {
    key: 'totalCost',
    label: '总成本影响',
    value: '¥0',
    type: 'primary',
    icon: Money,
    trend: 0
  }
])

// 初始化图表
function initCharts() {
  // 趋势图
  const trend = echarts.init(trendChart.value)
  trend.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' }
    },
    legend: {
      data: ['缺勤天数', '缺勤率', '成本影响']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: []
    },
    yAxis: [
      {
        type: 'value',
        name: 'HrHr天数/比率',
        position: 'left'
      },
      {
        type: 'value',
        name: '成本(万元)',
        position: 'right'
      }
    ],
    series: [
      {
        name: '缺勤天数',
        type: 'line',
        smooth: true,
        data: [],
        itemStyle: { color: '#f56c6c' }
      },
      {
        name: '缺勤率',
        type: 'line',
        smooth: true,
        data: [],
        itemStyle: { color: '#e6a23c' }
      },
      {
        name: '成本影响',
        type: 'line',
        smooth: true,
        yAxisIndex: 1,
        data: [],
        itemStyle: { color: '#909399' }
      }
    ]
  })

  // 类型分布图
  const type = echarts.init(typeChart.value)
  type.setOption({
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}天 ({d}%)'
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      left: 'left'
    },
    series: [{
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['60%', '50%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '16',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: []
    }]
  })

  // 部门对比图
  const department = echarts.init(departmentChart.value)
  department.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    legend: {
      data: ['缺勤天数', '缺勤率']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: []
    },
    yAxis: [
      {
        type: 'value',
        name: '天数',
        position: 'left'
      },
      {
        type: 'value',
        name: '比率(%)',
        position: 'right'
      }
    ],
    series: [
      {
        name: '缺勤天数',
        type: 'bar',
        data: [],
        itemStyle: { color: '#f56c6c' }
      },
      {
        name: '缺勤率',
        type: 'line',
        yAxisIndex: 1,
        data: [],
        itemStyle: { color: '#e6a23c' }
      }
    ]
  })

  // 季节性模式图
  const seasonal = echarts.init(seasonalChart.value)
  seasonal.setOption({
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    },
    yAxis: {
      type: 'value',
      name: '缺勤天数'
    },
    series: [{
      type: 'bar',
      data: [],
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#83bff6' },
          { offset: 0.5, color: '#188df0' },
          { offset: 1, color: '#188df0' }
        ])
      }
    }]
  })

  // 风险矩阵散点图
  const risk = echarts.init(riskChart.value)
  risk.setOption({
    tooltip: {
      trigger: 'item',
   
      formatter: function(params: unknown) {
        return `${params.data[3]}<br/>频率: ${params.data[0]}<br/>影响: ${params.data[1]}<br/>风险值: ${params.data[2]}`
      }
    },
    grid: {
      left: '10%',
      right: '10%',
      bottom: '10%',
      top: '10%'
    },
    xAxis: {
      type: 'value',
      name: '缺勤频率',
      scale: true
    },
    yAxis: {
      type: 'value',
      name: '影响程度',
      scale: true
    },
    series: [{
      type: 'scatter',
   
      symbolSize: function(data: unknown) {
        return Math.sqrt(data[2]) * 10
      },
      data: [],
      itemStyle: {
   
        color: function(params: unknown) {
          const risk = params.data[2]
          if (risk > 70) return '#f56c6c'
          if (risk > 40) return '#e6a23c'
          return '#67c23a'
        }
      }
    }]
  })

  // 响应式调整
  window.addEventListener('resize', () => {
    trend.resize()
    type.resize()
    department.resize()
    seasonal.resize()
    risk.resize()
  })
}

// 查询数据
async function handleSearch() {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      page: pagination.currentPage,
      pageSize: pagination.pageSize
    }

    const data = await attendanceApi.getAbsenceAnalysis(params)

    // 更新表格数据
    tableData.value = data.list
    pagination.total = data.total

    // 更新概览数据
    updateOverviewData(data.overview)

    // 更新图表数据
    updateChartsData(data.charts)

  } catch (__error) {
    ElMessage.error('获取缺勤分析数据失败')
  } finally {
    loading.value = false
  }
}

// 更新概览数据
   
function updateOverviewData(data: unknown) {
  overviewData.value[0].value = data.totalAbsenceDays
  overviewData.value[0].trend = data.absenceTrend
  
  overviewData.value[1].value = data.totalAbsenceRate + '%'
  overviewData.value[1].trend = data.rateTrend
  
  overviewData.value[2].value = data.affectedEmployees
  
  overviewData.value[3].value = data.highRiskEmployees
  
  overviewData.value[4].value = '¥' + (data.totalCost / 10000).toFixed(1) + '万'
  overviewData.value[4].trend = data.costTrend
}

// 更新图表数据
   
function updateChartsData(data: unknown) {
  // 更新趋势图
  const trendInstance = echarts.getInstanceByDom(trendChart.value)
  trendInstance.setOption({
    xAxis: { data: data.trend.xAxis },
    series: [
      { data: data.trend.absenceDays },
      { data: data.trend.absenceRate },
      { data: data.trend.cost.map((c: number) => (c / 10000).toFixed(1)) }
    ]
  })

  // 更新类型分布图
  const typeInstance = echarts.getInstanceByDom(typeChart.value)
  typeInstance.setOption({
    series: [{ data: data.typeDistribution }]
  })

  // 更新部门对比图
  const departmentInstance = echarts.getInstanceByDom(departmentChart.value)
  departmentInstance.setOption({
    xAxis: { data: data.departmentComparison.map(d => d.name) },
    series: [
      { data: data.departmentComparison.map(d => d.absenceDays) },
      { data: data.departmentComparison.map(d => d.absenceRate) }
    ]
  })

  // 更新季节性模式图
  const seasonalInstance = echarts.getInstanceByDom(seasonalChart.value)
  seasonalInstance.setOption({
    series: [{ data: data.seasonalPattern.map(d => d.absenceDays) }]
  })

  // 更新风险矩阵图
  const riskInstance = echarts.getInstanceByDom(riskChart.value)
  const riskData = data.riskMatrix.map(item => [
    item.frequency,
    item.impact,
    item.risk,
    item.employee
  ])
  riskInstance.setOption({
    series: [{ data: riskData }]
  })
}

// 获取缺勤标签类型
function getAbsenceTag(days: number) {
  if (days > 10) return 'danger'
  if (days > 5) return 'warning'
  return 'success'
}

// 获取风险标签类型
function getRiskTag(level: string) {
  const map: Record<string, string> = {
    low: 'success',
    medium: 'warning',
    high: 'danger'
  }
  return map[level] || ''
}

// 获取风险文本
function getRiskText(level: string) {
  const map: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高'
  }
  return map[level] || level
}

// 获取风险评分标签
function getRiskScoreTag(score: number) {
  if (score >= 80) return 'danger'
  if (score >= 60) return 'warning'
  return 'success'
}

// 获取进度条颜色
function getProgressColor(percentage: number) {
  if (percentage >= 80) return '#f56c6c'
  if (percentage >= 60) return '#e6a23c'
  return '#67c23a'
}

// 获取缺勤类型名称
function getAbsenceTypeName(code: string) {
  const reason = absenceReasons.value.find(r => r.code === code)
  return reason ? reason.name : code
}

// 重置搜索
function resetSearch() {
  searchForm.dateRange = [
    dayjs().subtract(90, 'day').format('YYYY-MM-DD'),
    dayjs().format('YYYY-MM-DD')
  ]
  searchForm.departmentId = ''
  searchForm.employeeId = ''
  searchForm.absenceType = []
  searchForm.analysisType = 'summary'
  handleSearch()
}

// 刷新数据
function refreshData() {
  handleSearch()
}

// 导出数据
async function exportData() {
  try {
    loading.value = true
    const data = await attendanceApi.exportAbsenceAnalysis(searchForm)
    
    exportToExcel({
      data: data.list,
      columns: [
        { key: 'employeeName', title: '姓名' },
        { key: 'employeeNo', title: '工号' },
        { key: 'department', title: '部门' },
        { key: 'position', title: '职位' },
        { key: 'totalAbsenceDays', title: '总缺勤天数' },
        { key: 'absenceRate', title: '缺勤率(%)' },
        { key: 'riskLevel', title: '风险等级' },
        { key: 'workloadImpact', title: '工作负荷影响(%)' },
        { key: 'teamImpact', title: '团队影响(%)' },
        { key: 'costImpact', title: '成本影响(元)' }
      ],
      filename: `缺勤分析报告_${dayjs().format('YYYYMMDD')}`
    })
    
    ElMessage.success('导出成功')
  } catch (__error) {
    ElMessage.error('导出失败')
  } finally {
    loading.value = false
  }
}

// 显示预测分析
async function showPredictionDialog() {
  predictionDialogVisible.value = true
  predictionLoading.value = true
  
  try {
    const data = await attendanceApi.getAbsencePrediction({
      departmentId: searchForm.departmentId,
      predictionPeriod: 'month'
    })
    
    predictionData.value = data
  } catch (__error) {
    ElMessage.error('获取预测数据失败')
  } finally {
    predictionLoading.value = false
  }
}

// 查看详情
   
function viewDetail(row: unknown) {
  currentEmployee.value = row
  detailDialogVisible.value = true
}

// 查看模式
   
function viewPattern(row: unknown) {
  currentEmployee.value = row
  patternDialogVisible.value = true
}

// 查看影响
   
function viewImpact(row: unknown) {
  currentEmployee.value = row
  impactDialogVisible.value = true
}

// 分页处理
function handleSizeChange(val: number) {
  pagination.pageSize = val
  handleSearch()
}

function handleCurrentChange(val: number) {
  pagination.currentPage = val
  handleSearch()
}

// 监听部门变化，加载员工列表
watch(() => searchForm.departmentId, async (newVal) => {
  if (newVal) {
    try {
      employeeList.value = await attendanceApi.getDepartmentEmployees(newVal)
    } catch (__error) {
      console.error('获取员工列表失败:', error)
    }
  } else {
    employeeList.value = []
    searchForm.employeeId = ''
  }
})

// 初始化
onMounted(async () => {
  // 加载部门树
  try {
    departmentTree.value = await attendanceApi.getDepartmentTree()
  } catch (__error) {
    console.error('获取部门树失败:', error)
  }

  // 加载缺勤原因
  try {
    absenceReasons.value = await attendanceApi.getAbsenceReasons()
  } catch (__error) {
    console.error('获取缺勤原因失败:', error)
  }

  // 初始化图表
  initCharts()

  // 加载数据
  handleSearch()
})
</script>

<style lang="scss" scoped>
.absence-analysis {
  padding: 20px;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
    }
    
    .header-actions {
      display: flex;
      gap: 10px;
    }
  }
  
  .search-card {
    margin-bottom: 20px;
  }
  
  .absence-overview {
    margin-bottom: 20px;
    
    .overview-card {
      background: #fff;
      border-radius: 8px;
      padding: 20px;
      display: flex;
      align-items: center;
      gap: 16px;
      transition: all 0.3s;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      }
      
      .card-icon {
        width: 64px;
        height: 64px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .el-icon {
          color: #fff;
        }
      }
      
      &.card-primary .card-icon { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
      &.card-success .card-icon { background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%); }
      &.card-warning .card-icon { background: linear-gradient(135deg, #e6a23c 0%, #ffd93d 100%); }
      &.card-danger .card-icon { background: linear-gradient(135deg, #f56c6c 0%, #ff8a80 100%); }
      &.card-info .card-icon { background: linear-gradient(135deg, #409eff 0%, #36cfc9 100%); }
      
      .card-content {
        flex: 1;
        
        .card-value {
          font-size: 28px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 4px;
        }
        
        .card-label {
          font-size: 14px;
          color: #909399;
        }
        
        .card-trend {
          display: flex;
          align-items: center;
          gap: 4px;
          margin-top: 8px;
          font-size: 12px;
          
          .trend-up {
            color: #f56c6c;
          }
          
          .trend-down {
            color: #67c23a;
          }
        }
      }
    }
  }
  
  .charts-container {
    margin-bottom: 20px;
    
    .chart-card {
      height: 400px;
      
      .chart-container {
        height: 320px;
      }
    }
  }
  
  .table-card {
    .absence-types {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
    }
    
    .el-pagination {
      margin-top: 20px;
      justify-content: flex-end;
    }
  }
  
  .pattern-analysis,
  .impact-analysis {
    .pattern-section {
      margin-bottom: 20px;
      
      h4 {
        margin: 0 0 12px 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
      }
      
      .pattern-tags {
        min-height: 40px;
      }
    }
    
    .impact-item {
      text-align: center;
      
      h4 {
        margin: 0 0 16px 0;
        color: #303133;
        font-size: 14px;
        font-weight: 600;
      }
      
      .cost-impact {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px;
        
        .cost-value {
          font-size: 24px;
          font-weight: 600;
          color: #409eff;
          margin-bottom: 8px;
        }
        
        .cost-label {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
  
  .prediction-content {
    min-height: 300px;
  }
}
</style>