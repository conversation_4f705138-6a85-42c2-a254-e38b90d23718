<template>
  <div class="attendance-rule-config">
    <!-- 规则列表 -->
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span>考勤规则配置</span>
          <el-button type="primary" @click="handleCreate">
            <el-icon><Plus /></el-icon>
            新建规则
          </el-button>
        </div>
      </template>

      <!-- 搜索条件 -->
      <el-form :model="searchForm" inline>
        <el-form-item label="规则名称">
          <el-input
            v-model="searchForm.ruleName"
            placeholder="请输入规则名称"
            clearable
            />
        </el-form-item>
        <el-form-item label="工作制度">
          <el-select v-model="searchForm.workType" placeholder="全部" clearable>
            <el-option label="标准工时制" value="standard"  />
            <el-option label="综合工时制" value="comprehensive"  />
            <el-option label="不定时工时制" value="flexible"  />
            <el-option label="弹性工作制" value="elastic"  />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="全部" clearable>
            <el-option label="启用" value="enabled"  />
            <el-option label="停用" value="disabled"  />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table :data="ruleList" stripe v-loading="loading">
        <el-table-column prop="ruleName" label="规则名称" min-width="150"  />
        <el-table-column prop="workType" label="工作制度" width="120">
          <template #default="{ row }">
            <el-tag>{{ getWorkTypeText(row.workType) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="workDays" label="工作日" width="150">
          <template #default="{ row }">
            {{ formatWorkDays(row.workDays) }}
          </template>
        </el-table-column>
        <el-table-column prop="workTime" label="工作时间" width="150">
          <template #default="{ row }">
            {{ row.startTime }} - {{ row.endTime }}
          </template>
        </el-table-column>
        <el-table-column prop="lateTime" label="迟到判定" width="100">
          <template #default="{ row }">
            {{ row.lateTime }}分钟
          </template>
        </el-table-column>
        <el-table-column prop="earlyTime" label="早退判定" width="100">
          <template #default="{ row }">
            {{ row.earlyTime }}分钟
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              :active-value="'enabled'"
              :inactive-value="'disabled'"
              @change="handleStatusChange(row)"
             />
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160"  />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button link type="primary" size="small" @click="handleCopy(row)">
              复制
            </el-button>
            <el-button link type="primary" size="small" @click="handleHistory(row)">
              历史
            </el-button>
            <el-button link type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pageInfo.currentPage"
        v-model:page-size="pageInfo.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pageInfo.total"
        layout="total, sizes, prev, pager, next, jumper"
        style="margin-top: 20px"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
    </el-card>

    <!-- 规则配置对话框 -->
    <el-dialog
      v-model="ruleDialog"
      :title="dialogTitle"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="ruleFormRef"
        :model="ruleForm"
        :rules="rules"
        label-width="120px"
      >
        <el-tabs v-model="activeTab">
          <!-- 基本信息 -->
          <el-tab-pane label="基本信息" name="basic">
            <el-form-item label="规则名称" prop="ruleName">
              <el-input
                v-model="ruleForm.ruleName"
                placeholder="请输入规则名称"
                maxlength="50"
                />
            </el-form-item>
            <el-form-item label="工作制度" prop="workType">
              <el-select
                v-model="ruleForm.workType"
                placeholder="请选择"
                @change="handleWorkTypeChange"
              >
                <el-option label="标准工时制" value="standard"  />
                <el-option label="综合工时制" value="comprehensive"  />
                <el-option label="不定时工时制" value="flexible"  />
                <el-option label="弹性工作制" value="elastic"  />
              </el-select>
            </el-form-item>
            <el-form-item label="适用部门">
              <el-tree-select
                v-model="ruleForm.departments"
                :data="departmentTree"
                :props="{ label: 'name', value: 'id' }"
                multiple
                show-checkbox
                placeholder="选择适用部门"
                style="width: 100%"
               />
            </el-form-item>
            <el-form-item label="规则描述">
              <el-input
                v-model="ruleForm.description"
                type="textarea"
                :rows="3"
                placeholder="请输入规则描述"
                />
            </el-form-item>
          </el-tab-pane>

          <!-- 工作时间 -->
          <el-tab-pane label="工作时间" name="workTime">
            <el-form-item label="工作日设置" prop="workDays">
              <el-checkbox-group v-model="ruleForm.workDays">
                <el-checkbox label="1">周一</el-checkbox>
                <el-checkbox label="2">周二</el-checkbox>
                <el-checkbox label="3">周三</el-checkbox>
                <el-checkbox label="4">周四</el-checkbox>
                <el-checkbox label="5">周五</el-checkbox>
                <el-checkbox label="6">周六</el-checkbox>
                <el-checkbox label="0">周日</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="上班时间" prop="startTime">
              <el-time-picker
                v-model="ruleForm.startTime"
                format="HH:mm"
                value-format="HH:mm"
                placeholder="选择上班时间"
               />
            </el-form-item>
            <el-form-item label="下班时间" prop="endTime">
              <el-time-picker
                v-model="ruleForm.endTime"
                format="HH:mm"
                value-format="HH:mm"
                placeholder="选择下班时间"
               />
            </el-form-item>
            <el-form-item label="午休时间">
              <el-time-picker
                v-model="ruleForm.breakStartTime"
                format="HH:mm"
                value-format="HH:mm"
                placeholder="午休开始"
                style="width: 48%"
               />
              <span style="margin: 0 2%">至</span>
              <el-time-picker
                v-model="ruleForm.breakEndTime"
                format="HH:mm"
                value-format="HH:mm"
                placeholder="午休结束"
                style="width: 48%"
               />
            </el-form-item>
            <el-form-item label="弹性时间" v-if="ruleForm.workType === 'elastic'">
              <el-input-number
                v-model="ruleForm.flexibleMinutes"
                :min="0"
                :max="120"
                placeholder="分钟"
                />
              <span style="margin-left: 10px">分钟</span>
            </el-form-item>
          </el-tab-pane>

          <!-- 考勤判定 -->
          <el-tab-pane label="考勤判定" name="judgment">
            <el-form-item label="迟到判定">
              <el-input-number
                v-model="ruleForm.lateTime"
                :min="1"
                :max="60"
                placeholder="分钟"
                />
              <span style="margin-left: 10px">分钟后算迟到</span>
            </el-form-item>
            <el-form-item label="早退判定">
              <el-input-number
                v-model="ruleForm.earlyTime"
                :min="1"
                :max="60"
                placeholder="分钟"
                />
              <span style="margin-left: 10px">分钟前算早退</span>
            </el-form-item>
            <el-form-item label="旷工判定">
              <el-radio-group v-model="ruleForm.absentType">
                <el-radio label="late">迟到超过</el-radio>
                <el-input-number
                  v-model="ruleForm.absentLateMinutes"
                  :min="30"
                  :max="480"
                  :disabled="ruleForm.absentType !== 'late'"
                  style="margin: 0 10px"
                  />
                <span>分钟</span>
              </el-radio-group>
              <el-radio-group v-model="ruleForm.absentType" style="margin-top: 10px">
                <el-radio label="noRecord">全天无打卡记录</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="加班判定">
              <el-input-number
                v-model="ruleForm.overtimeMinutes"
                :min="30"
                :max="240"
                placeholder="分钟"
                />
              <span style="margin-left: 10px">分钟后算加班</span>
            </el-form-item>
          </el-tab-pane>

          <!-- 考勤地点 -->
          <el-tab-pane label="考勤地点" name="location">
            <el-form-item label="考勤方式">
              <el-checkbox-group v-model="ruleForm.attendanceMethods">
                <el-checkbox label="device">考勤机打卡</el-checkbox>
                <el-checkbox label="mobile">移动定位打卡</el-checkbox>
                <el-checkbox label="wifi">WiFi打卡</el-checkbox>
                <el-checkbox label="face">人脸识别</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="考勤地点" v-if="ruleForm.attendanceMethods.includes('mobile')">
              <el-button type="primary" size="small" @click="handleAddLocation">
                <el-icon><Plus /></el-icon>
                添加地点
              </el-button>
              <div v-for="(location, index) in ruleForm.locations" :key="index" class="location-item">
                <el-input
                  v-model="location.name"
                  placeholder="地点名称"
                  style="width: 200px"
                  />
                <el-input
                  v-model="location.address"
                  placeholder="详细地址"
                  style="width: 300px; margin: 0 10px"
                  />
                <el-input-number
                  v-model="location.radius"
                  :min="50"
                  :max="1000"
                  placeholder="范围"
                  style="width: 120px"
                  />
                <span style="margin: 0 10px">米</span>
                <el-button
                  type="danger"
                  size="small"
                  :icon="Delete"
                  @click="handleRemoveLocation(index)"
                  />
              </div>
            </el-form-item>
            <el-form-item label="WiFi设置" v-if="ruleForm.attendanceMethods.includes('wifi')">
              <el-button type="primary" size="small" @click="handleAddWifi">
                <el-icon><Plus /></el-icon>
                添加WiFi
              </el-button>
              <div v-for="(wifi, index) in ruleForm.wifiList" :key="index" class="wifi-item">
                <el-input
                  v-model="wifi.name"
                  placeholder="WiFi名称"
                  style="width: 200px"
                  />
                <el-input
                  v-model="wifi.mac"
                  placeholder="MAC地址"
                  style="width: 200px; margin: 0 10px"
                  />
                <el-button
                  type="danger"
                  size="small"
                  :icon="Delete"
                  @click="handleRemoveWifi(index)"
                  />
              </div>
            </el-form-item>
          </el-tab-pane>

          <!-- 特殊规则 -->
          <el-tab-pane label="特殊规则" name="special">
            <el-form-item label="校历集成">
              <el-switch v-model="ruleForm.calendarIntegration"  />
              <span style="margin-left: 10px; color: #909399">
                自动同步学校校历，识别工作日、节假日和调休
              </span>
            </el-form-item>
            <el-form-item label="允许外勤">
              <el-switch v-model="ruleForm.allowFieldWork"  />
            </el-form-item>
            <el-form-item label="免打卡人员">
              <el-select
                v-model="ruleForm.exemptUsers"
                multiple
                filterable
                placeholder="选择免打卡人员"
                style="width: 100%"
              >
                <el-option
                  v-for="user in userList"
                  :key="user.id"
                  :label="user.name"
                  :value="user.id"
                 />
              </el-select>
            </el-form-item>
            <el-form-item label="规则优先级">
              <el-input-number
                v-model="ruleForm.priority"
                :min="1"
                :max="100"
                placeholder="数字越大优先级越高"
                />
              <span style="margin-left: 10px; color: #909399">
                当员工匹配多个规则时，使用优先级最高的规则
              </span>
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>

      <template #footer>
        <el-button @click="ruleDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </template>
    </el-dialog>

    <!-- 历史版本对话框 -->
    <el-dialog
      v-model="historyDialog"
      title="规则变更历史"
      width="800px"
    >
      <el-table :data="historyList" stripe>
        <el-table-column prop="version" label="版本号" width="80"  />
        <el-table-column prop="updateTime" label="更新时间" width="160"  />
        <el-table-column prop="updater" label="更新人" width="100"  />
        <el-table-column prop="changeDesc" label="变更说明" show-overflow-tooltip  />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" size="small" @click="handleViewVersion(row)">
              查看
            </el-button>
            <el-button link type="primary" size="small" @click="handleRestoreVersion(row)">
              恢复
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'

// 搜索表单
const searchForm = reactive({
  ruleName: '',
  workType: '',
  status: ''
})

// 分页信息
const pageInfo = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 加载状态
const loading = ref(false)

// 规则列表
const ruleList = ref([
  {
    id: 1,
    ruleName: '教学人员考勤规则',
    workType: 'standard',
    workDays: ['1', '2', '3', '4', '5'],
    startTime: '08:00',
    endTime: '17:00',
    lateTime: 10,
    earlyTime: 5,
    status: 'enabled',
    createTime: '2025-01-01 10:00:00'
  },
  {
    id: 2,
    ruleName: '行政人员考勤规则',
    workType: 'standard',
    workDays: ['1', '2', '3', '4', '5'],
    startTime: '08:30',
    endTime: '17:30',
    lateTime: 5,
    earlyTime: 5,
    status: 'enabled',
    createTime: '2025-01-01 10:00:00'
  },
  {
    id: 3,
    ruleName: '后勤人员考勤规则',
    workType: 'comprehensive',
    workDays: ['1', '2', '3', '4', '5', '6'],
    startTime: '07:00',
    endTime: '16:00',
    lateTime: 15,
    earlyTime: 10,
    status: 'enabled',
    createTime: '2025-01-01 10:00:00'
  }
])

// 对话框
const ruleDialog = ref(false)
const historyDialog = ref(false)
const dialogTitle = ref('新建考勤规则')
const activeTab = ref('basic')

// 表单
const ruleFormRef = ref()
const ruleForm = reactive({
  ruleName: '',
  workType: 'standard',
  departments: [],
  description: '',
  workDays: ['1', '2', '3', '4', '5'],
  startTime: '08:30',
  endTime: '17:30',
  breakStartTime: '12:00',
  breakEndTime: '13:00',
  flexibleMinutes: 30,
  lateTime: 10,
  earlyTime: 5,
  absentType: 'late',
  absentLateMinutes: 120,
  overtimeMinutes: 60,
  attendanceMethods: ['device'],
  locations: [],
  wifiList: [],
  calendarIntegration: true,
  allowFieldWork: false,
  exemptUsers: [],
  priority: 50
})

// 部门树
const departmentTree = ref([
  {
    id: '1',
    name: 'HrHr教学部门',
    children: [
      { id: '1-1', name: '计算机系' },
      { id: '1-2', name: '数学系' },
      { id: '1-3', name: '物理系' }
    ]
  },
  {
    id: '2',
    name: '行政部门',
    children: [
      { id: '2-1', name: '人事处' },
      { id: '2-2', name: '财务处' },
      { id: '2-3', name: '教务处' }
    ]
  },
  {
    id: '3',
    name: '后勤部门',
    children: [
      { id: '3-1', name: '总务处' },
      { id: '3-2', name: '保卫处' }
    ]
  }
])

// 用户列表
const userList = ref([
  { id: 1, name: '张校长' },
  { id: 2, name: '李副校长' },
  { id: 3, name: '王主任' }
])

// 历史列表
const historyList = ref([
  {
    version: 'v1.2',
    updateTime: '2025-01-15 14:30:00',
    updater: '人事管理员',
    changeDesc: '调整迟到判定时间从15分钟改为10分钟'
  },
  {
    version: 'v1.1',
    updateTime: '2025-01-10 10:00:00',
    updater: '人事管理员',
    changeDesc: '新增WiFi打卡方式'
  },
  {
    version: 'v1.0',
    updateTime: '2025-01-01 10:00:00',
    updater: '系统管理员',
    changeDesc: '初始创建'
  }
])

// 表单规则
const rules = reactive({
  ruleName: [
    { required: true, message: '请输入规则名称', trigger: 'blur' }
  ],
  workType: [
    { required: true, message: '请选择工作制度', trigger: 'change' }
  ],
  workDays: [
    { required: true, message: '请选择工作日', trigger: 'change' }
  ],
  startTime: [
    { required: true, message: '请选择上班时间', trigger: 'change' }
  ],
  endTime: [
    { required: true, message: '请选择下班时间', trigger: 'change' }
  ]
})

// 查询
const handleSearch = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('查询成功')
  }, 500)
}

// 重置
const handleReset = () => {
  searchForm.ruleName = ''
  searchForm.workType = ''
  searchForm.status = ''
  handleSearch()
}

// 新建
const handleCreate = () => {
  dialogTitle.value = '新建考勤规则'
  ruleDialog.value = true
  activeTab.value = 'basic'
  ruleFormRef.value?.resetFields()
}

// 编辑
   
const handleEdit = (row: unknown) => {
  dialogTitle.value = '编辑考勤规则'
  Object.assign(ruleForm, row)
  ruleDialog.value = true
  activeTab.value = 'basic'
}

// 复制
   
const handleCopy = (row: unknown) => {
  dialogTitle.value = '复制考勤规则'
  Object.assign(ruleForm, row)
  ruleForm.ruleName = `${row.ruleName}_副本`
  ruleDialog.value = true
  activeTab.value = 'basic'
}

// 查看历史
   
const handleHistory = (row: unknown) => {
  historyDialog.value = true
}

// 删除
   
const handleDelete = async (row: unknown) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除该考勤规则吗？删除后不可恢复。',
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    const index = ruleList.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      ruleList.value.splice(index, 1)
    }
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

// 状态切换
   
const handleStatusChange = (row: unknown) => {
  const text = row.status === 'enabled' ? '启用' : '停用'
  ElMessage.success(`${text}成功`)
}

// 工作制度改变
const handleWorkTypeChange = (value: string) => {
  if (value === 'flexible') {
    // 不定时工时制不需要设置具体时间
    ruleForm.startTime = ''
    ruleForm.endTime = ''
  } else if (!ruleForm.startTime) {
    ruleForm.startTime = '08:30'
    ruleForm.endTime = '17:30'
  }
}

// 添加考勤地点
const handleAddLocation = () => {
  ruleForm.locations.push({
    name: '',
    address: '',
    radius: 300
  })
}

// 移除考勤地点
const handleRemoveLocation = (index: number) => {
  ruleForm.locations.splice(index, 1)
}

// 添加WiFi
const handleAddWifi = () => {
  ruleForm.wifiList.push({
    name: '',
    mac: ''
  })
}

// 移除WiFi
const handleRemoveWifi = (index: number) => {
  ruleForm.wifiList.splice(index, 1)
}

// 保存
const handleSave = async () => {
  const valid = await ruleFormRef.value.validate()
  if (!valid) return

  ElMessage.success('保存成功')
  ruleDialog.value = false
}

// 查看版本
   
const handleViewVersion = (row: unknown) => {
  ElMessage.info(`查看版本 ${row.version}`)
}

// 恢复版本
   
const handleRestoreVersion = async (row: unknown) => {
  try {
    await ElMessageBox.confirm(
      `确定要恢复到版本 ${row.version} 吗？当前配置将被覆盖。`,
      '恢复确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    ElMessage.success('恢复成功')
  } catch {
    // 用户取消
  }
}

// 分页
const handleSizeChange = (val: number) => {
  pageInfo.pageSize = val
  handleSearch()
}

const handleCurrentChange = (val: number) => {
  pageInfo.currentPage = val
  handleSearch()
}

// 获取工作制度文本
const getWorkTypeText = (type: string) => {
  const map: Record<string, string> = {
    standard: '标准工时制',
    comprehensive: '综合工时制',
    flexible: '不定时工时制',
    elastic: '弹性工作制'
  }
  return map[type] || type
}

// 格式化工作日
const formatWorkDays = (days: string[]) => {
  const dayMap: Record<string, string> = {
    '1': '一',
    '2': '二',
    '3': '三',
    '4': '四',
    '5': '五',
    '6': '六',
    '0': '日'
  }
  return days.map(day => `周${dayMap[day]}`).join('、')
}
</script>

<style lang="scss" scoped>
.attendance-rule-config {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .location-item,
  .wifi-item {
    display: flex;
    align-items: center;
    margin-top: 10px;
  }
}
</style>