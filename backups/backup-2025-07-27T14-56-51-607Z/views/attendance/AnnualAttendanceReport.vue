<template>
  <div class="annual-attendance-report">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>年度考勤报表</h2>
      <div class="header-actions">
        <el-button @click="handlePrint">
          <el-icon><Printer /></el-icon>
          打印
        </el-button>
        <el-button @click="exportPDF">
          <el-icon><Document /></el-icon>
          导出PDF
        </el-button>
        <el-button type="primary" @click="exportExcel">
          <el-icon><Download /></el-icon>
          导出Excel
        </el-button>
      </div>
    </div>

    <!-- 查询条件 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" size="default">
        <el-form-item label="报表年度">
          <el-date-picker
            v-model="searchForm.year"
            type="year"
            placeholder="选择年度"
            format="YYYY年"
            value-format="YYYY"
           />
        </el-form-item>
        <el-form-item label="部门">
          <el-tree-select
            v-model="searchForm.departmentId"
            :data="departmentTree"
            :props="{ label: 'name', value: 'id' }"
            placeholder="请选择部门"
            clearable
            filterable
            check-strictly
           />
        </el-form-item>
        <el-form-item label="对比年度">
          <el-switch
            v-model="searchForm.compareMode"
            active-text="开启"
            inactive-text="关闭"
           />
        </el-form-item>
        <el-form-item label="对比年度" v-if="searchForm.compareMode">
          <el-date-picker
            v-model="searchForm.compareYear"
            type="year"
            placeholder="选择对比年度"
            format="YYYY年"
            value-format="YYYY"
           />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="generateReport">生成报表</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 报表内容 -->
    <div class="report-content" v-if="reportData">
      <!-- 年度总览 -->
      <el-card class="overview-card">
        <template #header>
          <div class="card-header">
            <h3>{{ searchForm.year }}年度考勤总览</h3>
            <el-tag v-if="searchForm.compareMode" type="info">
              对比{{ searchForm.compareYear }}年
            </el-tag>
          </div>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="6" v-for="item in overviewData" :key="item.key">
            <div class="overview-item">
              <div class="item-icon" :style="{ backgroundColor: item.color }">
                <el-icon :size="32"><component :is="item.icon" /></el-icon>
              </div>
              <div class="item-content">
                <div class="item-value">
                  {{ item.value }}
                  <span class="item-unit">{{ item.unit }}</span>
                </div>
                <div class="item-label">{{ item.label }}</div>
                <div class="item-compare" v-if="searchForm.compareMode && item.compareValue">
                  <el-icon :class="item.trend > 0 ? 'trend-up' : 'trend-down'">
                    <TrendCharts />
                  </el-icon>
                  <span>{{ item.trend > 0 ? '+' : '' }}{{ item.trend }}%</span>
                  <span class="compare-value">（去年：{{ item.compareValue }}）</span>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 月度趋势分析 -->
      <el-card class="chart-card">
        <template #header>
          <div class="card-header">
            <span>月度趋势分析</span>
            <el-radio-group v-model="trendType" size="small">
              <el-radio-button value="attendance">出勤率</el-radio-button>
              <el-radio-button value="overtime">加班时长</el-radio-button>
              <el-radio-button value="leave">请假天数</el-radio-button>
              <el-radio-button value="abnormal">异常次数</el-radio-button>
            </el-radio-group>
          </div>
        </template>
        <div ref="monthlyTrendChart" class="chart-container"></div>
      </el-card>

      <el-row :gutter="20">
        <!-- 部门年度对比 -->
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <span>部门年度对比</span>
            </template>
            <div ref="departmentChart" class="chart-container"></div>
          </el-card>
        </el-col>

        <!-- 季度分析 -->
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <span>季度考勤分析</span>
            </template>
            <div ref="quarterChart" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 年度考勤明细 -->
      <el-card class="detail-card">
        <template #header>
          <div class="card-header">
            <span>年度考勤明细</span>
            <div class="header-tools">
              <el-radio-group v-model="detailView" size="small">
                <el-radio-button value="month">按月汇总</el-radio-button>
                <el-radio-button value="department">按部门汇总</el-radio-button>
                <el-radio-button value="employee">按员工汇总</el-radio-button>
              </el-radio-group>
            </div>
          </div>
        </template>

        <!-- 按月汇总 -->
        <el-table
          v-if="detailView === 'month'"
          :data="monthlyData"
          stripe
          border
          show-summary
          :summary-method="getSummaryMethod"
        >
          <el-table-column prop="month" label="月份" width="100" fixed  />
          <el-table-column prop="workDays" label="工作日" width="80" align="center"  />
          <el-table-column prop="totalPeople" label="人数" width="80" align="center"  />
          <el-table-column label="出勤情况" align="center">
            <el-table-column prop="avgAttendanceRate" label="出勤率" width="90" align="center">
              <template #default="{ row }">
                <el-progress
                  :percentage="row.avgAttendanceRate"
                  :stroke-width="6"
                  :color="getProgressColor(row.avgAttendanceRate)"
                 />
              </template>
            </el-table-column>
            <el-table-column prop="totalAttendanceDays" label="总出勤天数" width="100" align="center"  />
          </el-table-column>
          <el-table-column label="请假统计" align="center">
            <el-table-column prop="totalLeaveDays" label="请假天数" width="90" align="center"  />
            <el-table-column prop="totalLeaveHours" label="请假时长" width="90" align="center"  />
          </el-table-column>
          <el-table-column label="加班统计" align="center">
            <el-table-column prop="totalOvertimeHours" label="加班时长" width="90" align="center"  />
            <el-table-column prop="overtimePeople" label="加班人次" width="90" align="center"  />
          </el-table-column>
          <el-table-column label="异常统计" align="center">
            <el-table-column prop="lateTimes" label="迟到" width="70" align="center"  />
            <el-table-column prop="earlyTimes" label="早退" width="70" align="center"  />
            <el-table-column prop="absentTimes" label="旷工" width="70" align="center"  />
          </el-table-column>
        </el-table>

        <!-- 按部门汇总 -->
        <el-table
          v-else-if="detailView === 'department'"
          :data="departmentData"
          stripe
          border
          default-expand-all
          row-key="id"
        >
          <el-table-column prop="name" label="部门" width="200"  />
          <el-table-column prop="avgPeople" label="平均人数" width="100" align="center"  />
          <el-table-column prop="yearAttendanceRate" label="年度出勤率" width="120" align="center">
            <template #default="{ row }">
              <span :class="{ 'text-danger': row.yearAttendanceRate < 90 }">
                {{ row.yearAttendanceRate }}%
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="totalLeaveDays" label="总请假天数" width="100" align="center"  />
          <el-table-column prop="avgLeaveDays" label="人均请假" width="100" align="center"  />
          <el-table-column prop="totalOvertimeHours" label="总加班时长" width="100" align="center"  />
          <el-table-column prop="avgOvertimeHours" label="人均加班" width="100" align="center"  />
          <el-table-column prop="abnormalRate" label="异常率" width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="getAbnormalType(row.abnormalRate)">
                {{ row.abnormalRate }}%
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="排名" width="80" align="center">
            <template #default="{ row }">
              <span class="rank-badge" :class="{ 'top3': row.rank <= 3 }">
                {{ row.rank }}
              </span>
            </template>
          </el-table-column>
        </el-table>

        <!-- 按员工汇总 -->
        <div v-else-if="detailView === 'employee'">
          <!-- 搜索栏 -->
          <div class="search-bar">
            <el-input
              v-model="employeeSearch"
              placeholder="搜索员工姓名或工号"
              style="width: 300px"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select v-model="sortBy" placeholder="排序方式" style="width: 150px">
              <el-option value="attendanceRate" label="按出勤率"  />
              <el-option value="leaveDays" label="按请假天数"  />
              <el-option value="overtimeHours" label="按加班时长"  />
              <el-option value="abnormalTimes" label="按异常次数"  />
            </el-select>
          </div>

          <el-table
            :data="filteredEmployeeData"
            stripe
            border
            max-height="500"
          >
            <el-table-column prop="employeeName" label="姓名" width="100" fixed  />
            <el-table-column prop="employeeNo" label="工号" width="100"  />
            <el-table-column prop="department" label="部门" width="150"  />
            <el-table-column prop="position" label="职位" width="120"  />
            <el-table-column prop="yearAttendanceRate" label="年度出勤率" width="120" align="center">
              <template #default="{ row }">
                <el-progress
                  :percentage="row.yearAttendanceRate"
                  :stroke-width="6"
                  :color="getProgressColor(row.yearAttendanceRate)"
                 />
              </template>
            </el-table-column>
            <el-table-column prop="totalAttendanceDays" label="出勤天数" width="90" align="center"  />
            <el-table-column prop="totalLeaveDays" label="请假天数" width="90" align="center"  />
            <el-table-column prop="totalOvertimeHours" label="加班时长" width="90" align="center"  />
            <el-table-column prop="lateTimes" label="迟到次数" width="90" align="center"  />
            <el-table-column prop="abnormalScore" label="考勤评分" width="100" align="center">
              <template #default="{ row }">
                <el-rate
                  v-model="row.abnormalScore"
                  disabled
                  show-score
                  text-color="#ff9900"
                  score-template="{value}"
                 />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100" fixed="right">
              <template #default="{ row }">
                <el-button type="text" @click="viewEmployeeDetail(row)">详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>

      <!-- 年度考勤排行榜 -->
      <el-card class="ranking-card">
        <template #header>
          <span>年度考勤排行榜</span>
        </template>
        
        <el-row :gutter="20">
          <!-- 出勤之星 -->
          <el-col :span="8">
            <div class="ranking-section">
              <h4>
                <el-icon><Trophy /></el-icon>
                出勤之星
              </h4>
              <div class="ranking-list">
                <div
                  v-for="(item, index) in rankingData.attendance"
                  :key="item.id"
                  class="ranking-item"
                >
                  <span class="rank-medal" v-if="index < 3">
                    {{ ['🥇', '🥈', '🥉'][index] }}
                  </span>
                  <span class="rank-number" v-else>{{ index + 1 }}</span>
                  <el-avatar :size="32" class="rank-avatar">
                    {{ item.name.charAt(0) }}
                  </el-avatar>
                  <div class="rank-info">
                    <div class="rank-name">{{ item.name }}</div>
                    <div class="rank-dept">{{ item.department }}</div>
                  </div>
                  <div class="rank-value">{{ item.value }}%</div>
                </div>
              </div>
            </div>
          </el-col>

          <!-- 勤奋之星 -->
          <el-col :span="8">
            <div class="ranking-section">
              <h4>
                <el-icon><Clock /></el-icon>
                勤奋之星
              </h4>
              <div class="ranking-list">
                <div
                  v-for="(item, index) in rankingData.overtime"
                  :key="item.id"
                  class="ranking-item"
                >
                  <span class="rank-medal" v-if="index < 3">
                    {{ ['🥇', '🥈', '🥉'][index] }}
                  </span>
                  <span class="rank-number" v-else>{{ index + 1 }}</span>
                  <el-avatar :size="32" class="rank-avatar">
                    {{ item.name.charAt(0) }}
                  </el-avatar>
                  <div class="rank-info">
                    <div class="rank-name">{{ item.name }}</div>
                    <div class="rank-dept">{{ item.department }}</div>
                  </div>
                  <div class="rank-value">{{ item.value }}h</div>
                </div>
              </div>
            </div>
          </el-col>

          <!-- 需关注人员 -->
          <el-col :span="8">
            <div class="ranking-section">
              <h4>
                <el-icon><Warning /></el-icon>
                需关注人员
              </h4>
              <div class="ranking-list">
                <div
                  v-for="item in rankingData.attention"
                  :key="item.id"
                  class="ranking-item attention"
                >
                  <el-avatar :size="32" class="rank-avatar">
                    {{ item.name.charAt(0) }}
                  </el-avatar>
                  <div class="rank-info">
                    <div class="rank-name">{{ item.name }}</div>
                    <div class="rank-dept">{{ item.department }}</div>
                  </div>
                  <el-tag type="danger" size="small">{{ item.reason }}</el-tag>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 年度总结 -->
      <el-card class="summary-card">
        <template #header>
          <span>年度考勤总结</span>
        </template>
        
        <div class="summary-content">
          <el-row :gutter="20">
            <el-col :span="12">
              <h4>考勤亮点</h4>
              <ul class="summary-list">
                <li v-for="item in summaryData.highlights" :key="item">
                  <el-icon class="success"><SuccessFilled /></el-icon>
                  {{ item }}
                </li>
              </ul>
            </el-col>
            <el-col :span="12">
              <h4>改进建议</h4>
              <ul class="summary-list">
                <li v-for="item in summaryData.suggestions" :key="item">
                  <el-icon class="warning"><InfoFilled /></el-icon>
                  {{ item }}
                </li>
              </ul>
            </el-col>
          </el-row>
          
          <div class="summary-footer">
            <p>{{ summaryData.conclusion }}</p>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 员工详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="员工年度考勤详情"
      width="80%"
    >
      <EmployeeAnnualDetail
        v-if="detailDialogVisible"
        :employee-id="currentEmployeeId"
        :year="searchForm.year"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import {
  Printer,
  Document,
  Download,
  Search,
  TrendCharts,
  Trophy,
  Clock,
  Warning,
  SuccessFilled,
  InfoFilled,
  Calendar,
  User,
  DataAnalysis,
  Timer
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import { attendanceApi } from '@/api/attendance'
import { exportToExcel } from '@/utils/export'
import { generatePDF } from '@/utils/pdf'
import HrEmployeeAnnualDetail from '@/components/attendance/HrEmployeeAnnualDetail.vue'

// 搜索表单
const searchForm = reactive({
  year: dayjs().format('YYYY'),
  departmentId: '',
  compareMode: false,
  compareYear: dayjs().subtract(1, 'year').format('YYYY')
})

// 响应式数据
const reportData = ref<unknown>(null)
const departmentTree = ref([])
const trendType = ref('attendance')
const detailView = ref('month')
const employeeSearch = ref('')
const sortBy = ref('attendanceRate')
const detailDialogVisible = ref(false)
const currentEmployeeId = ref('')

// 图表实例
const monthlyTrendChart = ref()
const departmentChart = ref()
const quarterChart = ref()

// 总览数据
const overviewData = ref([
  {
    key: 'workDays',
    label: '全年工作日',
    value: 0,
    unit: '天',
    icon: Calendar,
    color: '#409eff',
    compareValue: 0,
    trend: 0
  },
  {
    key: 'avgPeople',
    label: '平均在职人数',
    value: 0,
    unit: '人',
    icon: User,
    color: '#67c23a',
    compareValue: 0,
    trend: 0
  },
  {
    key: 'avgAttendanceRate',
    label: '年度出勤率',
    value: 0,
    unit: '%',
    icon: DataAnalysis,
    color: '#e6a23c',
    compareValue: 0,
    trend: 0
  },
  {
    key: 'totalOvertimeHours',
    label: '总加班时长',
    value: 0,
    unit: '小时',
    icon: Timer,
    color: '#f56c6c',
    compareValue: 0,
    trend: 0
  }
])

// 月度数据
const monthlyData = ref([])
// 部门数据
const departmentData = ref([])
// 员工数据
const employeeData = ref([])
// 排行榜数据
const rankingData = ref({
  attendance: [],
  overtime: [],
  attention: []
})
// 总结数据
const summaryData = ref({
  highlights: [],
  suggestions: [],
  conclusion: ''
})

// 过滤后的员工数据
const filteredEmployeeData = computed(() => {
  let data = [...employeeData.value]
  
  // 搜索过滤
  if (employeeSearch.value) {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
    data = data.filter((item: unknown) =>
      item.employeeName.includes(employeeSearch.value) ||
      item.employeeNo.includes(employeeSearch.value)
    )
  }
  
  // 排序
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data.sort((a: unknown, b: unknown) => {
    const field = sortBy.value
    return b[field] - a[field]
  })
  
  return data
})

// 生成报表
async function generateReport() {
  if (!searchForm.year) {
    ElMessage.warning('请选择报表年度')
    return
  }

  const loading = ElLoading.service({
    text: '正在生成年度报表...',
    background: 'rgba(0, 0, 0, 0.7)'
  })

  try {
    const params = {
      year: searchForm.year,
      departmentId: searchForm.departmentId,
      compareYear: searchForm.compareMode ? searchForm.compareYear : null
    }

    const data = await attendanceApi.getAnnualReport(params)
    
    reportData.value = data
    updateOverviewData(data.overview)
    monthlyData.value = data.monthly
    departmentData.value = data.departments
    employeeData.value = data.employees
    rankingData.value = data.ranking
    summaryData.value = data.summary

    // 初始化图表
    setTimeout(() => {
      initCharts(data.charts)
    }, 100)

  } catch (__error) {
    ElMessage.error('生成报表失败')
  } finally {
    loading.close()
  }
}

// 更新总览数据
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
function updateOverviewData(data: unknown) {
  overviewData.value.forEach(item => {
    item.value = data[item.key]
    if (searchForm.compareMode && data.compare) {
      item.compareValue = data.compare[item.key]
      item.trend = calculateTrend(item.value, item.compareValue)
    }
  })
}

// 计算趋势
function calculateTrend(current: number, previous: number): number {
  if (previous === 0) return 0
  return Number(((current - previous) / previous * 100).toFixed(1))
}

// 初始化图表
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
function initCharts(data: unknown) {
  // 月度趋势图
  if (monthlyTrendChart.value) {
    const chart = echarts.init(monthlyTrendChart.value)
    const series = [{
      name: searchForm.year,
      type: 'line',
      smooth: true,
      data: data.trend[trendType.value],
      areaStyle: {
        opacity: 0.3
      }
    }]
    
    if (searchForm.compareMode) {
      series.push({
        name: searchForm.compareYear,
        type: 'line',
        smooth: true,
        data: data.compareTrend[trendType.value],
        lineStyle: {
          type: 'dashed'
        }
      })
    }
    
    chart.setOption({
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'cross' }
      },
      legend: {
        data: searchForm.compareMode ? [searchForm.year, searchForm.compareYear] : [searchForm.year]
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
      },
      yAxis: {
        type: 'value',
        name: getTrendYAxisName()
      },
      series
    })
  }

  // 部门对比图
  if (departmentChart.value) {
    const chart = echarts.init(departmentChart.value)
    chart.setOption({
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' }
      },
      legend: {
        data: ['出勤率', '人均请假', '人均加班']
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: data.departmentNames,
        axisLabel: {
          rotate: 45
        }
      },
      yAxis: [
        {
          type: 'value',
          name: 'HrHr百分比/天数',
          position: 'left'
        },
        {
          type: 'value',
          name: '小时',
          position: 'right'
        }
      ],
      series: [
        {
          name: '出勤率',
          type: 'bar',
          data: data.departmentAttendance
        },
        {
          name: '人均请假',
          type: 'bar',
          data: data.departmentLeave
        },
        {
          name: '人均加班',
          type: 'bar',
          yAxisIndex: 1,
          data: data.departmentOvertime
        }
      ]
    })
  }

  // 季度分析图
  if (quarterChart.value) {
    const chart = echarts.init(quarterChart.value)
    chart.setOption({
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: '季度分布',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['60%', '50%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '20',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: data.quarterData
        }
      ]
    })
  }
}

// 获取趋势Y轴名称
function getTrendYAxisName() {
  const map: Record<string, string> = {
    attendance: '出勤率(%)',
    overtime: '加班时长(小时)',
    leave: '请假天数',
    abnormal: '异常次数'
  }
  return map[trendType.value] || ''
}

// 获取进度条颜色
function getProgressColor(percentage: number) {
  if (percentage >= 95) return '#67c23a'
  if (percentage >= 90) return '#e6a23c'
  return '#f56c6c'
}

// 获取异常类型
function getAbnormalType(rate: number) {
  if (rate <= 5) return 'success'
  if (rate <= 10) return 'warning'
  return 'danger'
}

// 查看员工详情
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
function viewEmployeeDetail(row: unknown) {
  currentEmployeeId.value = row.employeeId
  detailDialogVisible.value = true
}

// 获取汇总方法
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
function getSummaryMethod(param: unknown) {
  const {columns: _columns, data: _data} =  param
  const sums: string[] 
  background-color: #f5f7fa;
  min-height: 100vh;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    background: #fff;
    padding: 20px;
    border-radius: 4px;
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
    }
    
    .header-actions {
      display: flex;
      gap: 10px;
    }
  }
  
  .search-card {
    margin-bottom: 20px;
  }
  
  .overview-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      align-items: center;
      gap: 12px;
      
      h3 {
        margin: 0;
        font-size: 18px;
      }
    }
    
    .overview-item {
      display: flex;
      align-items: center;
      padding: 20px;
      background: #f5f7fa;
      border-radius: 8px;
      
      .item-icon {
        width: 64px;
        height: 64px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        
        .el-icon {
          color: #fff;
        }
      }
      
      .item-content {
        flex: 1;
        
        .item-value {
          font-size: 28px;
          font-weight: 600;
          color: #303133;
          
          .item-unit {
            font-size: 16px;
            font-weight: normal;
            color: #606266;
            margin-left: 4px;
          }
        }
        
        .item-label {
          font-size: 14px;
          color: #909399;
          margin-top: 4px;
        }
        
        .item-compare {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-top: 8px;
          font-size: 13px;
          
          .trend-up {
            color: #67c23a;
          }
          
          .trend-down {
            color: #f56c6c;
          }
          
          .compare-value {
            color: #909399;
          }
        }
      }
    }
  }
  
  .chart-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .chart-container {
      height: 350px;
    }
  }
  
  .detail-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-tools {
        display: flex;
        gap: 16px;
      }
    }
    
    .search-bar {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;
    }
    
    .text-danger {
      color: #f56c6c;
    }
    
    .rank-badge {
      display: inline-block;
      width: 24px;
      height: 24px;
      line-height: 24px;
      text-align: center;
      background: #909399;
      color: #fff;
      border-radius: 50%;
      font-size: 12px;
      font-weight: bold;
      
      &.top3 {
        background: linear-gradient(135deg, #f5af19 0%, #f12711 100%);
      }
    }
  }
  
  .ranking-card {
    margin-bottom: 20px;
    
    .ranking-section {
      h4 {
        display: flex;
        align-items: center;
        gap: 8px;
        margin: 0 0 16px 0;
        font-size: 16px;
        color: #303133;
      }
      
      .ranking-list {
        .ranking-item {
          display: flex;
          align-items: center;
          padding: 12px 0;
          border-bottom: 1px solid #ebeef5;
          
          &:last-child {
            border-bottom: none;
          }
          
          &.attention {
            background: #fef0f0;
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 8px;
            border: none;
          }
          
          .rank-medal {
            font-size: 24px;
            margin-right: 12px;
          }
          
          .rank-number {
            width: 24px;
            text-align: center;
            font-weight: bold;
            color: #909399;
            margin-right: 12px;
          }
          
          .rank-avatar {
            margin-right: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          }
          
          .rank-info {
            flex: 1;
            
            .rank-name {
              font-weight: 500;
              color: #303133;
            }
            
            .rank-dept {
              font-size: 12px;
              color: #909399;
              margin-top: 2px;
            }
          }
          
          .rank-value {
            font-weight: 600;
            color: #409eff;
          }
        }
      }
    }
  }
  
  .summary-card {
    .summary-content {
      h4 {
        margin: 0 0 16px 0;
        font-size: 16px;
        color: #303133;
      }
      
      .summary-list {
        list-style: none;
        padding: 0;
        margin: 0;
        
        li {
          display: flex;
          align-items: flex-start;
          margin-bottom: 12px;
          
          .el-icon {
            margin-right: 8px;
            margin-top: 2px;
            
            &.success {
              color: #67c23a;
            }
            
            &.warning {
              color: #e6a23c;
            }
          }
        }
      }
      
      .summary-footer {
        margin-top: 24px;
        padding-top: 24px;
        border-top: 1px solid #ebeef5;
        
        p {
          margin: 0;
          color: #606266;
          line-height: 1.8;
        }
      }
    }
  }
}

@media print {
  .page-header .header-actions,
  .search-card {
    display: none !important;
  }
  
  .annual-attendance-report {
    background-color: #fff;
  }
  
  .el-card {
    box-shadow: none;
    border: 1px solid #dcdfe6;
  }
}
</style>