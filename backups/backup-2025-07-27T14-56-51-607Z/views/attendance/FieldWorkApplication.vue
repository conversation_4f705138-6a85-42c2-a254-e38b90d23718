<template>
  <div class="field-work-application">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>外勤申请</h2>
      <div class="header-actions">
        <el-button @click="viewHistory">申请记录</el-button>
        <el-button @click="viewFieldWorkMap">外勤地图</el-button>
      </div>
    </div>

    <!-- 申请表单 -->
    <el-card class="form-card">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        size="large"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="外勤类型" prop="fieldWorkType">
              <el-select v-model="form.fieldWorkType" placeholder="请选择外勤类型">
                <el-option value="client_visit" label="客户拜访"  />
                <el-option value="meeting" label="外部会议"  />
                <el-option value="training" label="外出培训"  />
                <el-option value="business_trip" label="出差"  />
                <el-option value="site_work" label="现场作业"  />
                <el-option value="other" label="其他"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="紧急程度" prop="urgency">
              <el-radio-group v-model="form.urgency">
                <el-radio value="normal">普通</el-radio>
                <el-radio value="urgent">紧急</el-radio>
                <el-radio value="critical">特急</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                v-model="form.startTime"
                type="datetime"
                placeholder="选择开始时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
                @change="calculateDuration"
               />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                v-model="form.endTime"
                type="datetime"
                placeholder="选择结束时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
                :disabled-date="disabledEndDate"
                @change="calculateDuration"
               />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="外勤时长">
              <el-input v-model="form.duration" readonly>
                <template #append>小时</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="交通方式" prop="transportation">
              <el-select v-model="form.transportation" placeholder="请选择交通方式">
                <el-option value="public" label="公共交通"  />
                <el-option value="company_car" label="公司车辆"  />
                <el-option value="private_car" label="私家车"  />
                <el-option value="taxi" label="出租车/网约车"  />
                <el-option value="walk" label="步行"  />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 目的地信息 -->
        <el-form-item label="目的地" prop="destination" required>
          <el-input 
            v-model="form.destination" 
            placeholder="请输入详细地址"
            style="width: calc(100% - 120px);"
          >
            <template #append>
              <el-button @click="selectLocation">
                <el-icon><Location /></el-icon>
                地图选择
              </el-button>
            </template>
          </el-input>
          <div class="location-info" v-if="form.location">
            <el-tag type="info" size="small">
              经度：{{ form.location.lng }}，纬度：{{ form.location.lat }}
            </el-tag>
            <el-tag type="success" size="small" v-if="distance">
              距离：{{ distance }}公里
            </el-tag>
          </div>
        </el-form-item>

        <!-- 联系信息 -->
        <el-row :gutter="20" v-if="form.fieldWorkType === 'client_visit'">
          <el-col :span="12">
            <el-form-item label="客户名称" prop="clientName">
              <el-input v-model="form.clientName" placeholder="请输入客户名称"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人" prop="contactPerson">
              <el-input v-model="form.contactPerson" placeholder="请输入联系人"   />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="外勤事由" prop="reason">
          <el-input
            v-model="form.reason"
            type="textarea"
            :rows="4"
            placeholder="请详细说明外勤原因和工作内容（不少于20个字）"
            maxlength="500"
            show-word-limit
            />
        </el-form-item>

        <!-- 预期成果 -->
        <el-form-item label="预期成果" prop="expectedOutcome">
          <el-input
            v-model="form.expectedOutcome"
            type="textarea"
            :rows="3"
            placeholder="请说明外勤预期达成的目标或成果"
            maxlength="200"
            />
        </el-form-item>

        <!-- 同行人员 -->
        <el-form-item label="同行人员">
          <el-select 
            v-model="form.companions" 
            multiple 
            placeholder="请选择同行人员（可选）"
            style="width: 100%;"
          >
            <el-option
              v-for="person in colleagueList"
              :key="person.id"
              :label="person.name"
              :value="person.id"
            >
              <span>{{ person.name }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">
                {{ person.department }}
              </span>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 费用预估 -->
        <el-form-item label="费用预估">
          <el-row :gutter="10">
            <el-col :span="6">
              <el-input-number 
                v-model="form.estimatedCost.transport" 
                :min="0" 
                :precision="2"
                placeholder="交通费"
                style="width: 100%;"
                />
            </el-col>
            <el-col :span="6">
              <el-input-number 
                v-model="form.estimatedCost.meal" 
                :min="0" 
                :precision="2"
                placeholder="餐费"
                style="width: 100%;"
                />
            </el-col>
            <el-col :span="6">
              <el-input-number 
                v-model="form.estimatedCost.accommodation" 
                :min="0" 
                :precision="2"
                placeholder="住宿费"
                style="width: 100%;"
                />
            </el-col>
            <el-col :span="6">
              <el-input-number 
                v-model="form.estimatedCost.other" 
                :min="0" 
                :precision="2"
                placeholder="其他费用"
                style="width: 100%;"
                />
            </el-col>
          </el-row>
          <div class="cost-total" v-if="totalCost > 0">
            预估总费用：<span class="cost-amount">¥{{ totalCost.toFixed(2) }}</span>
          </div>
        </el-form-item>

        <!-- 位置追踪授权 -->
        <el-form-item label="位置追踪" prop="enableTracking">
          <el-switch v-model="form.enableTracking"  />
          <span class="switch-label">
            {{ form.enableTracking ? '已授权' : '未授权' }}
          </span>
          <el-tooltip content="开启后，外勤期间将记录您的位置轨迹" placement="top">
            <el-icon style="margin-left: 10px;"><InfoFilled /></el-icon>
          </el-tooltip>
        </el-form-item>

        <!-- 审批流程 -->
        <el-form-item label="审批流程">
          <div class="approval-flow">
            <div 
              v-for="(node, index) in approvalFlow" 
              :key="index"
              class="flow-node"
            >
              <el-badge :value="node.duration" type="info" v-if="node.duration">
                <div class="node-box">
                  <el-icon :size="20"><UserFilled /></el-icon>
                  <div class="node-title">{{ node.title }}</div>
                </div>
              </el-badge>
              <el-icon class="flow-arrow" v-if="index < approvalFlow.length - 1">
                <ArrowRight />
              </el-icon>
            </div>
          </div>
        </el-form-item>

        <!-- 提交按钮 -->
        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="loading">
            提交申请
          </el-button>
          <el-button @click="saveAsDraft">保存草稿</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 地图选择对话框 -->
    <el-dialog
      v-model="mapDialogVisible"
      title="选择目的地"
      width="800px"
      destroy-on-close
    >
      <div class="map-container">
        <div class="map-search">
          <el-input 
            v-model="mapSearch" 
            placeholder="搜索地点"
            @keyup.enter="searchLocation"
          >
            <template #append>
              <el-button @click="searchLocation">搜索</el-button>
            </template>
          </el-input>
        </div>
        <div id="location-map" style="height: 400px;"></div>
      </div>
      <template #footer>
        <el-button @click="mapDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmLocation">确定</el-button>
      </template>
    </el-dialog>

    <!-- 外勤地图对话框 -->
    <el-dialog
      v-model="fieldWorkMapVisible"
      title="今日外勤分布"
      width="900px"
      destroy-on-close
    >
      <div class="field-work-map">
        <div class="map-filter">
          <el-select v-model="mapFilter.type" placeholder="外勤类型" clearable>
            <el-option value="client_visit" label="客户拜访"  />
            <el-option value="meeting" label="外部会议"  />
            <el-option value="business_trip" label="出差"  />
          </el-select>
          <el-select v-model="mapFilter.department" placeholder="部门" clearable>
            <el-option 
              v-for="dept in departmentList" 
              :key="dept.id"
              :value="dept.id"
              :label="dept.name"
             />
          </el-select>
          <el-button type="primary" @click="refreshFieldWorkMap">刷新</el-button>
        </div>
        <div id="field-work-map" style="height: 500px;"></div>
        <div class="map-legend">
          <span class="legend-item">
            <i class="dot" style="background: #67c23a;"></i>进行中
          </span>
          <span class="legend-item">
            <i class="dot" style="background: #909399;"></i>已结束
          </span>
          <span class="legend-item">
            <i class="dot" style="background: #e6a23c;"></i>即将开始
          </span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted } from 'vue'
import { 
  Location, 
  UserFilled, 
  ArrowRight,
  InfoFilled 
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores'
// 表单数据
const form = reactive({
  fieldWorkType: '',
  urgency: 'normal',
  startTime: '',
  endTime: '',
  duration: 0,
  transportation: '',
  destination: '',
  location: null as unknown,
  clientName: '',
  contactPerson: '',
  reason: '',
  expectedOutcome: '',
  companions: [],
  estimatedCost: {
    transport: 0,
    meal: 0,
    accommodation: 0,
    other: 0
  },
  enableTracking: true
})

// 表单验证规则
const rules = {
  fieldWorkType: [
    { required: true, message: '请选择外勤类型', trigger: 'change' }
  ],
  urgency: [
    { required: true, message: '请选择紧急程度', trigger: 'change' }
  ],
  startTime: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  endTime: [
    { required: true, message: '请选择结束时间', trigger: 'change' },
    { validator: validateEndTime, trigger: 'change' }
  ],
  transportation: [
    { required: true, message: '请选择交通方式', trigger: 'change' }
  ],
  destination: [
    { required: true, message: '请输入目的地', trigger: 'blur' }
  ],
  reason: [
    { required: true, message: '请填写外勤事由', trigger: 'blur' },
    { min: 20, message: '外勤事由不少于20个字', trigger: 'blur' }
  ],
  expectedOutcome: [
    { required: true, message: '请填写预期成果', trigger: 'blur' }
  ],
  clientName: [
    { required: true, message: '请输入客户名称', trigger: 'blur' }
  ],
  contactPerson: [
    { required: true, message: '请输入联系人', trigger: 'blur' }
  ]
}

// 响应式变量
const formRef = ref()
const loading = ref(false)
const mapDialogVisible = ref(false)
const fieldWorkMapVisible = ref(false)
const mapSearch = ref('')
const colleagueList = ref([])
const departmentList = ref([])
const distance = ref(0)
const mapFilter = reactive({
  type: '',
  department: ''
})

// 计算属性
const totalCost = computed(() => {
  const {transport: _transport, meal: _meal, accommodation: _accommodation, other: _other} =  form.estimatedCost
  return transport + meal + accommodation + other
})

const approvalFlow 
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
    }
  }
  
  .form-card {
    .el-form {
      max-width: 1000px;
      margin: 0 auto;
    }
    
    .location-info {
      margin-top: 8px;
      
      .el-tag {
        margin-right: 8px;
      }
    }
    
    .cost-total {
      margin-top: 10px;
      font-size: 14px;
      color: #606266;
      
      .cost-amount {
        font-size: 18px;
        font-weight: 600;
        color: #f56c6c;
      }
    }
    
    .switch-label {
      margin-left: 10px;
      color: #606266;
    }
    
    .approval-flow {
      display: flex;
      align-items: center;
      gap: 20px;
      
      .flow-node {
        display: flex;
        align-items: center;
        
        .node-box {
          background: #f5f7fa;
          padding: 15px 20px;
          border-radius: 4px;
          text-align: center;
          
          .el-icon {
            color: #409eff;
            margin-bottom: 5px;
          }
          
          .node-title {
            font-size: 14px;
            color: #303133;
          }
        }
        
        .flow-arrow {
          color: #c0c4cc;
        }
      }
    }
  }
}

.map-container {
  .map-search {
    margin-bottom: 15px;
  }
  
  #location-map {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
  }
}

.field-work-map {
  .map-filter {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    
    .el-select {
      width: 150px;
    }
  }
  
  #field-work-map {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
  }
  
  .map-legend {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 15px;
    
    .legend-item {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #606266;
      
      .dot {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 5px;
      }
    }
  }
}
</style>