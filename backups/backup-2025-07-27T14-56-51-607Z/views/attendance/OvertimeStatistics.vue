<template>
  <div class="overtime-statistics">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>加班统计分析</h2>
      <div class="header-actions">
        <el-button @click="exportData">导出报表</el-button>
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 查询条件 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" size="default">
        <el-form-item label="统计期间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="monthrange"
            range-separator="至"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            value-format="YYYY-MM"
           />
        </el-form-item>
        <el-form-item label="部门">
          <el-tree-select
            v-model="searchForm.departmentId"
            :data="departmentTree"
            :props="{ label: 'name', value: 'id' }"
            placeholder="请选择部门"
            clearable
            filterable
           />
        </el-form-item>
        <el-form-item label="加班类型">
          <el-select
            v-model="searchForm.overtimeType"
            placeholder="全部类型"
            clearable
          >
            <el-option value="weekday" label="工作日加班"  />
            <el-option value="weekend" label="周末加班"  />
            <el-option value="holiday" label="节假日加班"  />
          </el-select>
        </el-form-item>
        <el-form-item label="补偿方式">
          <el-select
            v-model="searchForm.compensationType"
            placeholder="全部方式"
            clearable
          >
            <el-option value="pay" label="加班费"  />
            <el-option value="leave" label="调休"  />
            <el-option value="mixed" label="混合"  />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 加班概览 -->
    <div class="overtime-overview">
      <el-row :gutter="20">
        <el-col :span="6" v-for="item in overviewData" :key="item.key">
          <div class="overview-card">
            <div class="card-icon" :style="{ background: item.color }">
              <el-icon :size="28"><component :is="item.icon" /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ item.value }}</div>
              <div class="card-label">{{ item.label }}</div>
              <div class="card-subtext">{{ item.subtext }}</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 统计图表 -->
    <el-row :gutter="20" class="charts-container">
      <!-- 加班时长趋势图 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>加班时长趋势</span>
              <el-radio-group v-model="trendType" size="small">
                <el-radio-button value="hours">时长</el-radio-button>
                <el-radio-button value="people">人次</el-radio-button>
                <el-radio-button value="cost">费用</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div ref="trendChart" class="chart-container"></div>
        </el-card>
      </el-col>

      <!-- 部门加班对比 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>部门加班对比</span>
          </template>
          <div ref="departmentChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-container">
      <!-- 加班类型分布 -->
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <span>加班类型分布</span>
          </template>
          <div ref="typeChart" class="chart-container"></div>
        </el-card>
      </el-col>

      <!-- 补偿方式分布 -->
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <span>补偿方式分布</span>
          </template>
          <div ref="compensationChart" class="chart-container"></div>
        </el-card>
      </el-col>

      <!-- 时段分布 -->
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <span>加班时段分布</span>
          </template>
          <div ref="hourChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 加班排行榜 -->
    <el-row :gutter="20" class="ranking-container">
      <!-- 部门排行 -->
      <el-col :span="12">
        <el-card class="ranking-card">
          <template #header>
            <div class="card-header">
              <span>部门加班TOP10</span>
              <el-select v-model="deptRankType" size="small" style="width: 100px;">
                <el-option value="total" label="总时长"  />
                <el-option value="average" label="人均"  />
              </el-select>
            </div>
          </template>
          <div class="ranking-list">
            <div 
              v-for="(item, index) in deptRankData" 
              :key="item.id"
              class="ranking-item"
            >
              <span class="rank-number" :class="{ 'top3': index < 3 }">
                {{ index + 1 }}
              </span>
              <span class="rank-name">{{ item.name }}</span>
              <span class="rank-value">{{ item.value }}h</span>
              <el-progress
                :percentage="item.percentage"
                :stroke-width="6"
                :show-text="false"
                :color="getProgressColor(index)"
               />
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 个人排行 -->
      <el-col :span="12">
        <el-card class="ranking-card">
          <template #header>
            <div class="card-header">
              <span>个人加班TOP10</span>
              <el-select v-model="personRankType" size="small" style="width: 100px;">
                <el-option value="hours" label="时长"  />
                <el-option value="days" label="天数"  />
              </el-select>
            </div>
          </template>
          <div class="ranking-list">
            <div 
              v-for="(item, index) in personRankData" 
              :key="item.id"
              class="ranking-item"
            >
              <span class="rank-number" :class="{ 'top3': index < 3 }">
                {{ index + 1 }}
              </span>
              <el-avatar :size="24" class="rank-avatar">
                {{ item.name.charAt(0) }}
              </el-avatar>
              <span class="rank-name">{{ item.name }}</span>
              <span class="rank-dept">{{ item.department }}</span>
              <span class="rank-value">{{ item.value }}{{ personRankType === 'hours' ? 'h' : '天' }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细数据表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>加班明细</span>
          <div class="header-tools">
            <el-switch
              v-model="showSummary"
              active-text="显示汇总"
              inactive-text="隐藏汇总"
             />
          </div>
        </div>
      </template>
      
      <el-table
        :data="tableData"
        :summary-method="getSummaries"
        :show-summary="showSummary"
        stripe
        border
        v-loading="loading"
      >
        <el-table-column prop="employeeName" label="姓名" width="100" fixed  />
        <el-table-column prop="employeeNo" label="工号" width="100"  />
        <el-table-column prop="department" label="部门" width="120"  />
        <el-table-column label="加班统计" align="center">
          <el-table-column prop="weekdayHours" label="工作日(h)" width="90" align="center"  />
          <el-table-column prop="weekendHours" label="周末(h)" width="80" align="center"  />
          <el-table-column prop="holidayHours" label="节假日(h)" width="90" align="center"  />
          <el-table-column prop="totalHours" label="合计(h)" width="80" align="center">
            <template #default="{ row }">
              <span class="total-hours">{{ row.totalHours }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="补偿情况" align="center">
          <el-table-column prop="paidAmount" label="加班费(元)" width="100" align="center">
            <template #default="{ row }">
              <span v-if="row.paidAmount > 0">¥{{ row.paidAmount }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="leaveHours" label="调休(h)" width="80" align="center">
            <template #default="{ row }">
              <span v-if="row.leaveHours > 0">{{ row.leaveHours }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="usedLeaveHours" label="已用调休(h)" width="100" align="center"  />
        </el-table-column>
        <el-table-column prop="overtimeCount" label="加班次数" width="90" align="center"  />
        <el-table-column prop="monthlyLimit" label="月限额使用" width="100" align="center">
          <template #default="{ row }">
            <el-progress
              :percentage="row.limitUsage"
              :stroke-width="6"
              :color="getLimitColor(row.limitUsage)"
             />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="text" @click="viewDetail(row)">详情</el-button>
            <el-button type="text" @click="viewCalendar(row)">日历</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="加班详情"
      width="900px"
    >
      <el-tabs v-model="activeTab">
        <el-tab-pane label="加班记录" name="records">
          <el-table :data="detailRecords" stripe border max-height="400">
            <el-table-column prop="date" label="日期" width="100"  />
            <el-table-column prop="type" label="类型" width="100">
              <template #default="{ row }">
                <el-tag :type="getOvertimeTypeTag(row.type)" size="small">
                  {{ getOvertimeTypeText(row.type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="startTime" label="开始时间" width="150"  />
            <el-table-column prop="endTime" label="结束时间" width="150"  />
            <el-table-column prop="hours" label="时长(h)" width="80" align="center"  />
            <el-table-column prop="reason" label="加班原因" min-width="200"  />
            <el-table-column prop="compensationType" label="补偿方式" width="100">
              <template #default="{ row }">
                {{ getCompensationText(row.compensationType) }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="row.status === 'approved' ? 'success' : 'info'" size="small">
                  {{ row.status === 'approved' ? '已批准' : '待审批' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="统计分析" name="analysis">
          <div class="detail-analysis">
            <el-row :gutter="20">
              <el-col :span="12">
                <div ref="detailTypeChart" style="height: 300px;"></div>
              </el-col>
              <el-col :span="12">
                <div ref="detailTrendChart" style="height: 300px;"></div>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Refresh,
  Clock,
  TrendCharts,
  Coin,
  Calendar
} from '@element-plus/icons-vue'
import echarts, { createChart } from '@/utils/echarts'
import dayjs from 'dayjs'
import { attendanceApi } from '@/api/attendance'
import { exportToExcel } from '@/utils/export'

// 搜索表单
const searchForm = reactive({
  dateRange: [
    dayjs().subtract(3, 'month').format('YYYY-MM'),
    dayjs().format('YYYY-MM')
  ],
  departmentId: '',
  overtimeType: '',
  compensationType: ''
})

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 响应式数据
const loading = ref(false)
const departmentTree = ref([])
const tableData = ref([])
const showSummary = ref(true)
const trendType = ref('hours')
const deptRankType = ref('total')
const personRankType = ref('hours')
const detailDialogVisible = ref(false)
const activeTab = ref('records')
const currentEmployee = ref<unknown>({})
const detailRecords = ref([])

// 图表实例
const trendChart = ref()
const departmentChart = ref()
const typeChart = ref()
const compensationChart = ref()
const hourChart = ref()
const detailTypeChart = ref()
const detailTrendChart = ref()

// 概览数据
const overviewData = ref([
  {
    key: 'totalHours',
    label: '总加班时长',
    value: '0',
    subtext: '本期累计',
    icon: Clock,
    color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
  },
  {
    key: 'totalPeople',
    label: '加班人次',
    value: '0',
    subtext: '参与加班',
    icon: TrendCharts,
    color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
  },
  {
    key: 'totalCost',
    label: '加班费用',
    value: '¥0',
    subtext: '费用总计',
    icon: Coin,
    color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
  },
  {
    key: 'totalLeave',
    label: '调休时长',
    value: '0h',
    subtext: '可用调休',
    icon: Calendar,
    color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
  }
])

// 排行榜数据
const deptRankData = ref([])
const personRankData = ref([])

// 初始化图表
function initCharts() {
  // 加班趋势图
  const trend = createChart(trendChart.value)
  trend.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' }
    },
    legend: {
      data: ['工作日', '周末', '节假日']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: []
    },
    yAxis: {
      type: 'value',
      name: 'HrHr小时'
    },
    series: [
      {
        name: '工作日',
        type: 'line',
        stack: 'Total',
        smooth: true,
        areaStyle: {},
        emphasis: { focus: 'series' },
        data: []
      },
      {
        name: '周末',
        type: 'line',
        stack: 'Total',
        smooth: true,
        areaStyle: {},
        emphasis: { focus: 'series' },
        data: []
      },
      {
        name: '节假日',
        type: 'line',
        stack: 'Total',
        smooth: true,
        areaStyle: {},
        emphasis: { focus: 'series' },
        data: []
      }
    ]
  })

  // 部门对比图
  const department = createChart(departmentChart.value)
  department.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    legend: {
      data: ['总时长', '人均时长']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: []
    },
    yAxis: [
      {
        type: 'value',
        name: '总时长(h)',
        position: 'left'
      },
      {
        type: 'value',
        name: '人均(h)',
        position: 'right'
      }
    ],
    series: [
      {
        name: '总时长',
        type: 'bar',
        data: [],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 1, color: '#188df0' }
          ])
        }
      },
      {
        name: '人均时长',
        type: 'line',
        yAxisIndex: 1,
        data: [],
        itemStyle: { color: '#ff6b6b' }
      }
    ]
  })

  // 加班类型分布
  const type = createChart(typeChart.value)
  type.setOption({
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}h ({d}%)'
    },
    series: [{
      name: '加班类型',
      type: 'pie',
      radius: ['40%', '70%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '16',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: []
    }]
  })

  // 补偿方式分布
  const compensation = createChart(compensationChart.value)
  compensation.setOption({
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [{
      name: '补偿方式',
      type: 'pie',
      radius: '60%',
      center: ['50%', '50%'],
      roseType: 'radius',
      itemStyle: {
        borderRadius: 8
      },
      label: {
        formatter: '{b}\n{d}%'
      },
      data: []
    }]
  })

  // 时段分布
  const hour = createChart(hourChart.value)
  hour.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['18-19', '19-20', '20-21', '21-22', '22-23', '23-24'],
      axisTick: { alignWithLabel: true }
    },
    yAxis: {
      type: 'value',
      name: '人次'
    },
    series: [{
      type: 'bar',
      barWidth: '60%',
      data: [],
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#ffeaa7' },
          { offset: 1, color: '#fab1a0' }
        ]),
        borderRadius: [4, 4, 0, 0]
      }
    }]
  })

  // 响应式调整
  window.addEventListener('resize', () => {
    trend.resize()
    department.resize()
    type.resize()
    compensation.resize()
    hour.resize()
  })
}

// 获取进度条颜色
function getProgressColor(index: number) {
  const colors = ['#f56c6c', '#e6a23c', '#f56c6c', '#409eff', '#67c23a']
  return colors[Math.min(index, colors.length - 1)]
}

// 获取限额颜色
function getLimitColor(percentage: number) {
  if (percentage >= 90) return '#f56c6c'
  if (percentage >= 70) return '#e6a23c'
  return '#67c23a'
}

// 获取加班类型标签
function getOvertimeTypeTag(type: string) {
  const map: Record<string, string> = {
    weekday: '',
    weekend: 'warning',
    holiday: 'danger'
  }
  return map[type] || ''
}

// 获取加班类型文本
function getOvertimeTypeText(type: string) {
  const map: Record<string, string> = {
    weekday: '工作日',
    weekend: '周末',
    holiday: '节假日'
  }
  return map[type] || type
}

// 获取补偿方式文本
function getCompensationText(type: string) {
  const map: Record<string, string> = {
    pay: '加班费',
    leave: '调休',
    mixed: '混合'
  }
  return map[type] || type
}

// 查询数据
async function handleSearch() {
  loading.value = true
  try {
    const data = await attendanceApi.getOvertimeStatistics({
      ...searchForm,
      page: pagination.currentPage,
      pageSize: pagination.pageSize
    })

    // 更新表格数据
    tableData.value = data.list
    pagination.total = data.total

    // 更新概览数据
    updateOverviewData(data.overview)

    // 更新图表数据
    updateChartsData(data.charts)

    // 更新排行榜
    updateRankingData(data.ranking)

  } catch (__error) {
    ElMessage.error('获取统计数据失败')
  } finally {
    loading.value = false
  }
}

// 更新概览数据
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
function updateOverviewData(data: unknown) {
  overviewData.value[0].value = data.totalHours + 'h'
  overviewData.value[1].value = data.totalPeople.toLocaleString()
  overviewData.value[2].value = '¥' + data.totalCost.toLocaleString()
  overviewData.value[3].value = data.totalLeave + 'h'
}

// 更新图表数据
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
function updateChartsData(data: unknown) {
  // 更新趋势图
  const trendInstance = echarts.getInstanceByDom(trendChart.value)
  trendInstance.setOption({
    xAxis: { data: data.trend.xAxis },
    series: [
      { data: data.trend.weekday },
      { data: data.trend.weekend },
      { data: data.trend.holiday }
    ]
  })

  // 更新部门对比图
  const departmentInstance = echarts.getInstanceByDom(departmentChart.value)
  departmentInstance.setOption({
    xAxis: { data: data.department.names },
    series: [
      { data: data.department.total },
      { data: data.department.average }
    ]
  })

  // 更新类型分布图
  const typeInstance = echarts.getInstanceByDom(typeChart.value)
  typeInstance.setOption({
    series: [{ data: data.typeDistribution }]
  })

  // 更新补偿方式图
  const compensationInstance = echarts.getInstanceByDom(compensationChart.value)
  compensationInstance.setOption({
    series: [{ data: data.compensationDistribution }]
  })

  // 更新时段分布图
  const hourInstance = echarts.getInstanceByDom(hourChart.value)
  hourInstance.setOption({
    series: [{ data: data.hourDistribution }]
  })
}

// 更新排行榜数据
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
function updateRankingData(data: unknown) {
  // 部门排行
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  deptRankData.value = data.departmentRanking.map((item: unknown, index: number) => ({
    ...item,
    percentage: (item.value / data.departmentRanking[0].value) * 100
  }))

  // 个人排行
  personRankData.value = data.personalRanking
}

// 重置搜索
function resetSearch() {
  searchForm.dateRange = [
    dayjs().subtract(3, 'month').format('YYYY-MM'),
    dayjs().format('YYYY-MM')
  ]
  searchForm.departmentId = ''
  searchForm.overtimeType = ''
  searchForm.compensationType = ''
  handleSearch()
}

// 刷新数据
function refreshData() {
  handleSearch()
}

// 导出数据
async function exportData() {
  try {
    loading.value = true
    const data = await attendanceApi.exportOvertimeStatistics(searchForm)
    
    exportToExcel({
      data: data.list,
      columns: [
        { key: 'employeeName', title: '姓名' },
        { key: 'employeeNo', title: '工号' },
        { key: 'department', title: '部门' },
        { key: 'weekdayHours', title: '工作日加班(h)' },
        { key: 'weekendHours', title: '周末加班(h)' },
        { key: 'holidayHours', title: '节假日加班(h)' },
        { key: 'totalHours', title: '合计(h)' },
        { key: 'paidAmount', title: '加班费(元)' },
        { key: 'leaveHours', title: '调休(h)' },
        { key: 'usedLeaveHours', title: '已用调休(h)' },
        { key: 'overtimeCount', title: '加班次数' }
      ],
      filename: `加班统计_${dayjs().format('YYYYMMDD')}`
    })
    
    ElMessage.success('导出成功')
  } catch (__error) {
    ElMessage.error('导出失败')
  } finally {
    loading.value = false
  }
}

// 查看详情
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
async function viewDetail(row: unknown) {
  currentEmployee.value = row
  
  try {
    // 获取加班记录
    const records = await attendanceApi.getEmployeeOvertimeRecords({
      employeeId: row.employeeId,
      dateRange: searchForm.dateRange
    })
    detailRecords.value = records
    
    // 初始化详情图表
    setTimeout(() => {
      initDetailCharts(records)
    }, 100)
    
    detailDialogVisible.value = true
  } catch (__error) {
    ElMessage.error('获取详情失败')
  }
}

// 初始化详情图表
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
function initDetailCharts(records: unknown[]) {
  // 类型分布
  if (detailTypeChart.value) {
    const typeInstance = createChart(detailTypeChart.value)
    const typeData = [
      { value: records.filter(r => r.type === 'weekday').length, name: '工作日' },
      { value: records.filter(r => r.type === 'weekend').length, name: '周末' },
      { value: records.filter(r => r.type === 'holiday').length, name: '节假日' }
    ]
    
    typeInstance.setOption({
      title: { text: '加班类型分布', left: 'center' },
      tooltip: { trigger: 'item' },
      series: [{
        type: 'pie',
        radius: '50%',
        data: typeData
      }]
    })
  }

  // 月度趋势
  if (detailTrendChart.value) {
    const trendInstance = createChart(detailTrendChart.value)
    const monthData = records.reduce((acc, record) => {
      const month = dayjs(record.date).format('YYYY-MM')
      acc[month] = (acc[month] || 0) + record.hours
      return acc
    }, {} as Record<string, number>)
    
    trendInstance.setOption({
      title: { text: '月度加班趋势', left: 'center' },
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: Object.keys(monthData)
      },
      yAxis: {
        type: 'value',
        name: '小时'
      },
      series: [{
        type: 'bar',
        data: Object.values(monthData),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 1, color: '#188df0' }
          ])
        }
      }]
    })
  }
}

// 查看日历
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
function viewCalendar(row: unknown) {
  window.location.href = `/attendance/calendar/${row.employeeId}?type=overtime&range=${searchForm.dateRange.join(',')}`
}

// 计算汇总
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
function getSummaries(param: unknown) {
  const {columns: _columns, data: _data} =  param
  const sums: string[] 
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
    }
    
    .header-actions {
      display: flex;
      gap: 10px;
    }
  }
  
  .search-card {
    margin-bottom: 20px;
  }
  
  .overtime-overview {
    margin-bottom: 20px;
    
    .overview-card {
      background: #fff;
      border-radius: 8px;
      padding: 20px;
      display: flex;
      align-items: center;
      gap: 16px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
      transition: all 0.3s;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
      }
      
      .card-icon {
        width: 56px;
        height: 56px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .el-icon {
          color: #fff;
        }
      }
      
      .card-content {
        flex: 1;
        
        .card-value {
          font-size: 24px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 4px;
        }
        
        .card-label {
          font-size: 14px;
          color: #606266;
        }
        
        .card-subtext {
          font-size: 12px;
          color: #909399;
          margin-top: 4px;
        }
      }
    }
  }
  
  .charts-container {
    margin-bottom: 20px;
    
    .chart-card {
      height: 400px;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .chart-container {
        height: 320px;
      }
    }
  }
  
  .ranking-container {
    margin-bottom: 20px;
    
    .ranking-card {
      height: 460px;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .ranking-list {
        padding: 10px 0;
        
        .ranking-item {
          display: flex;
          align-items: center;
          padding: 8px 0;
          border-bottom: 1px solid #f0f0f0;
          
          &:last-child {
            border-bottom: none;
          }
          
          .rank-number {
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: #909399;
            
            &.top3 {
              background: linear-gradient(135deg, #f5af19 0%, #f12711 100%);
              color: #fff;
              border-radius: 50%;
            }
          }
          
          .rank-avatar {
            margin: 0 10px;
          }
          
          .rank-name {
            flex: 1;
            font-weight: 500;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          
          .rank-dept {
            font-size: 12px;
            color: #909399;
            margin-right: 10px;
          }
          
          .rank-value {
            font-weight: 600;
            color: #409eff;
            margin-right: 10px;
            min-width: 60px;
            text-align: right;
          }
          
          .el-progress {
            flex: 1;
            max-width: 200px;
          }
        }
      }
    }
  }
  
  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-tools {
        display: flex;
        align-items: center;
        gap: 16px;
      }
    }
    
    .total-hours {
      font-weight: 600;
      color: #409eff;
    }
    
    .el-pagination {
      margin-top: 20px;
      justify-content: flex-end;
    }
  }
  
  .detail-analysis {
    padding: 20px 0;
  }
}
</style>