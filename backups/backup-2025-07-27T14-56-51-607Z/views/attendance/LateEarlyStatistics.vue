<template>
  <div class="late-early-statistics">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>迟到早退统计</h2>
      <div class="header-actions">
        <el-button @click="exportData">导出数据</el-button>
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 查询条件 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" size="default">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
           />
        </el-form-item>
        <el-form-item label="部门">
          <el-tree-select
            v-model="searchForm.departmentId"
            :data="departmentTree"
            :props="{ label: 'name', value: 'id' }"
            placeholder="请选择部门"
            clearable
            filterable
           />
        </el-form-item>
        <el-form-item label="员工">
          <el-select
            v-model="searchForm.employeeId"
            placeholder="请选择员工"
            clearable
            filterable
          >
            <el-option
              v-for="emp in employeeList"
              :key="emp.id"
              :label="emp.name"
              :value="emp.id"
             />
          </el-select>
        </el-form-item>
        <el-form-item label="统计类型">
          <el-select v-model="searchForm.statisticsType" placeholder="统计类型">
            <el-option value="summary" label="汇总统计"  />
            <el-option value="detail" label="详细统计"  />
            <el-option value="trend" label="趋势分析"  />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计概览 -->
    <div class="stats-overview">
      <el-row :gutter="20">
        <el-col :span="4" v-for="item in overviewData" :key="item.key">
          <div class="overview-card" :class="`card-${item.type}`">
            <div class="card-icon">
              <el-icon :size="32"><component :is="item.icon" /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ item.value }}</div>
              <div class="card-label">{{ item.label }}</div>
              <div class="card-trend" v-if="item.trend !== undefined">
                <el-icon v-if="item.trend > 0" class="trend-up">
                  <CaretTop />
                </el-icon>
                <el-icon v-else-if="item.trend < 0" class="trend-down">
                  <CaretBottom />
                </el-icon>
                <span>{{ Math.abs(item.trend) }}%</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 图表展示 -->
    <el-row :gutter="20" class="charts-container">
      <!-- 迟到早退趋势图 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>迟到早退趋势</span>
              <el-radio-group v-model="timeGranularity" size="small">
                <el-radio-button value="day">日</el-radio-button>
                <el-radio-button value="week">周</el-radio-button>
                <el-radio-button value="month">月</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div ref="trendChart" class="chart-container"></div>
        </el-card>
      </el-col>

      <!-- 部门对比图 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>部门迟到早退对比</span>
          </template>
          <div ref="departmentChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-container">
      <!-- 时段分布图 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>迟到早退时段分布</span>
          </template>
          <div ref="timeChart" class="chart-container"></div>
        </el-card>
      </el-col>

      <!-- TOP员工排行 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>TOP10员工排行</span>
              <el-select v-model="topType" size="small" style="width: 120px;">
                <el-option value="late" label="迟到次数"  />
                <el-option value="early" label="早退次数"  />
                <el-option value="duration" label="时长"  />
              </el-select>
            </div>
          </template>
          <div ref="topChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细数据表格 -->
    <el-card class="table-card">
      <template #header>
        <span>迟到早退明细</span>
      </template>
      
      <el-table
        :data="tableData"
        stripe
        border
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column prop="employeeName" label="姓名" width="100" fixed="left"  />
        <el-table-column prop="employeeNo" label="工号" width="100"  />
        <el-table-column prop="department" label="部门" width="120"  />
        <el-table-column prop="position" label="职位" width="100"  />
        <el-table-column label="迟到统计" align="center">
          <el-table-column prop="lateCount" label="次数" width="80" align="center">
            <template #default="{ row }">
              <el-tag v-if="row.lateCount > 0" type="warning" size="small">
                {{ row.lateCount }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="lateDuration" label="总时长" width="100" align="center">
            <template #default="{ row }">
              {{ row.lateDuration }}分钟
            </template>
          </el-table-column>
          <el-table-column prop="averageLateDuration" label="平均时长" width="100" align="center">
            <template #default="{ row }">
              {{ row.averageLateDuration }}分钟
            </template>
          </el-table-column>
          <el-table-column prop="lateRate" label="迟到率" width="80" align="center">
            <template #default="{ row }">
              {{ row.lateRate }}%
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="早退统计" align="center">
          <el-table-column prop="earlyCount" label="次数" width="80" align="center">
            <template #default="{ row }">
              <el-tag v-if="row.earlyCount > 0" type="danger" size="small">
                {{ row.earlyCount }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="earlyDuration" label="总时长" width="100" align="center">
            <template #default="{ row }">
              {{ row.earlyDuration }}分钟
            </template>
          </el-table-column>
          <el-table-column prop="averageEarlyDuration" label="平均时长" width="100" align="center">
            <template #default="{ row }">
              {{ row.averageEarlyDuration }}分钟
            </template>
          </el-table-column>
          <el-table-column prop="earlyRate" label="早退率" width="80" align="center">
            <template #default="{ row }">
              {{ row.earlyRate }}%
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="text" @click="viewDetail(row)">详情</el-button>
            <el-button type="text" @click="viewPattern(row)">规律</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="迟到早退详情"
      width="800px"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="员工姓名">{{ currentEmployee.employeeName }}</el-descriptions-item>
        <el-descriptions-item label="工号">{{ currentEmployee.employeeNo }}</el-descriptions-item>
        <el-descriptions-item label="部门">{{ currentEmployee.department }}</el-descriptions-item>
        <el-descriptions-item label="职位">{{ currentEmployee.position }}</el-descriptions-item>
        <el-descriptions-item label="迟到次数">{{ currentEmployee.lateCount }}</el-descriptions-item>
        <el-descriptions-item label="早退次数">{{ currentEmployee.earlyCount }}</el-descriptions-item>
        <el-descriptions-item label="迟到总时长">{{ currentEmployee.lateDuration }}分钟</el-descriptions-item>
        <el-descriptions-item label="早退总时长">{{ currentEmployee.earlyDuration }}分钟</el-descriptions-item>
        <el-descriptions-item label="平均迟到时长">{{ currentEmployee.averageLateDuration }}分钟</el-descriptions-item>
        <el-descriptions-item label="平均早退时长">{{ currentEmployee.averageEarlyDuration }}分钟</el-descriptions-item>
        <el-descriptions-item label="最长迟到时长">{{ currentEmployee.maxLateDuration }}分钟</el-descriptions-item>
        <el-descriptions-item label="最长早退时长">{{ currentEmployee.maxEarlyDuration }}分钟</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 规律分析对话框 -->
    <el-dialog
      v-model="patternDialogVisible"
      title="迟到早退规律分析"
      width="700px"
    >
      <div class="pattern-analysis">
        <div class="pattern-section">
          <h4>迟到规律</h4>
          <div class="pattern-tags">
            <el-tag
              v-for="pattern in currentEmployee.recentLatePattern"
              :key="pattern"
              type="warning"
              style="margin-right: 8px; margin-bottom: 8px;"
            >
              {{ pattern }}
            </el-tag>
          </div>
        </div>
        <div class="pattern-section">
          <h4>早退规律</h4>
          <div class="pattern-tags">
            <el-tag
              v-for="pattern in currentEmployee.recentEarlyPattern"
              :key="pattern"
              type="danger"
              style="margin-right: 8px; margin-bottom: 8px;"
            >
              {{ pattern }}
            </el-tag>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Refresh,
  Clock,
  User,
  Timer,
  CaretTop,
  CaretBottom
} from '@element-plus/icons-vue'
import echarts, { createChart } from '@/utils/echarts'
import dayjs from 'dayjs'
import { attendanceApi } from '@/api/attendance'
import { exportToExcel } from '@/utils/export'

// 搜索表单
const searchForm = reactive({
  dateRange: [
    dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
    dayjs().format('YYYY-MM-DD')
  ],
  departmentId: '',
  employeeId: '',
  statisticsType: 'summary'
})

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 响应式数据
const loading = ref(false)
const departmentTree = ref([])
const employeeList = ref([])
const tableData = ref([])
const timeGranularity = ref('week')
const topType = ref('late')
const detailDialogVisible = ref(false)
const patternDialogVisible = ref(false)
const currentEmployee = ref<unknown>({})

// 图表实例
const trendChart = ref()
const departmentChart = ref()
const timeChart = ref()
const topChart = ref()

// 概览数据
const overviewData = ref([
  {
    key: 'totalLate',
    label: '迟到总次数',
    value: 0,
    type: 'warning',
    icon: Clock,
    trend: 0
  },
  {
    key: 'totalEarly',
    label: '早退总次数',
    value: 0,
    type: 'danger',
    icon: Timer,
    trend: 0
  },
  {
    key: 'affectedEmployees',
    label: '涉及人数',
    value: 0,
    type: 'info',
    icon: User,
    trend: 0
  },
  {
    key: 'avgLateRate',
    label: '平均迟到率',
    value: '0%',
    type: 'primary',
    icon: Clock,
    trend: 0
  },
  {
    key: 'avgEarlyRate',
    label: '平均早退率',
    value: '0%',
    type: 'primary',
    icon: Timer,
    trend: 0
  },
  {
    key: 'affectedDepartments',
    label: '涉及部门',
    value: 0,
    type: 'success',
    icon: User
  }
])

// 初始化图表
function initCharts() {
  // 趋势图
  const trend = createChart(trendChart.value)
  trend.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' }
    },
    legend: {
      data: ['迟到次数', '早退次数', '迟到时长', '早退时长']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: []
    },
    yAxis: [
      {
        type: 'value',
        name: 'HrHr次数',
        position: 'left'
      },
      {
        type: 'value',
        name: '时长(分钟)',
        position: 'right'
      }
    ],
    series: [
      {
        name: '迟到次数',
        type: 'line',
        smooth: true,
        data: [],
        itemStyle: { color: '#e6a23c' }
      },
      {
        name: '早退次数',
        type: 'line',
        smooth: true,
        data: [],
        itemStyle: { color: '#f56c6c' }
      },
      {
        name: '迟到时长',
        type: 'line',
        smooth: true,
        yAxisIndex: 1,
        data: [],
        itemStyle: { color: '#909399' }
      },
      {
        name: '早退时长',
        type: 'line',
        smooth: true,
        yAxisIndex: 1,
        data: [],
        itemStyle: { color: '#606266' }
      }
    ]
  })

  // 部门对比图
  const department = createChart(departmentChart.value)
  department.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    legend: {
      data: ['迟到次数', '早退次数']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: []
    },
    yAxis: {
      type: 'value',
      name: '次数'
    },
    series: [
      {
        name: '迟到次数',
        type: 'bar',
        data: [],
        itemStyle: { color: '#e6a23c' }
      },
      {
        name: '早退次数',
        type: 'bar',
        data: [],
        itemStyle: { color: '#f56c6c' }
      }
    ]
  })

  // 时段分布图
  const time = createChart(timeChart.value)
  time.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    legend: {
      data: ['迟到', '早退']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: Array.from({ length: 24 }, (_, i) => `${i}:00`)
    },
    yAxis: {
      type: 'value',
      name: '次数'
    },
    series: [
      {
        name: '迟到',
        type: 'bar',
        data: [],
        itemStyle: { color: '#e6a23c' }
      },
      {
        name: '早退',
        type: 'bar',
        data: [],
        itemStyle: { color: '#f56c6c' }
      }
    ]
  })

  // TOP排行图
  const top = createChart(topChart.value)
  top.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: []
    },
    series: [{
      type: 'bar',
      data: [],
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
          { offset: 0, color: '#e6a23c' },
          { offset: 1, color: '#f56c6c' }
        ])
      },
      label: {
        show: true,
        position: 'right'
      }
    }]
  })

  // 响应式调整
  window.addEventListener('resize', () => {
    trend.resize()
    department.resize()
    time.resize()
    top.resize()
  })
}

// 查询数据
async function handleSearch() {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      timeGranularity: timeGranularity.value,
      page: pagination.currentPage,
      pageSize: pagination.pageSize
    }

    const data = await attendanceApi.getLateEarlyStatistics(params)

    // 更新表格数据
    tableData.value = data.list
    pagination.total = data.total

    // 更新概览数据
    updateOverviewData(data.overview)

    // 更新图表数据
    updateChartsData(data.charts)

  } catch (__error) {
    ElMessage.error('获取迟到早退统计数据失败')
  } finally {
    loading.value = false
  }
}

// 更新概览数据
   
function updateOverviewData(data: unknown) {
  overviewData.value[0].value = data.totalLateCount
  overviewData.value[0].trend = data.lateTrend
  
  overviewData.value[1].value = data.totalEarlyCount
  overviewData.value[1].trend = data.earlyTrend
  
  overviewData.value[2].value = data.affectedEmployees
  
  overviewData.value[3].value = data.avgLateRate + '%'
  
  overviewData.value[4].value = data.avgEarlyRate + '%'
  
  overviewData.value[5].value = data.affectedDepartments
}

// 更新图表数据
   
function updateChartsData(data: unknown) {
  // 更新趋势图
  const trendInstance = echarts.getInstanceByDom(trendChart.value)
  trendInstance.setOption({
    xAxis: { data: data.trend.xAxis },
    series: [
      { data: data.trend.late },
      { data: data.trend.early },
      { data: data.trend.lateDuration },
      { data: data.trend.earlyDuration }
    ]
  })

  // 更新部门对比图
  const departmentInstance = echarts.getInstanceByDom(departmentChart.value)
  departmentInstance.setOption({
    xAxis: { data: data.department.map(d => d.name) },
    series: [
      { data: data.department.map(d => d.lateCount) },
      { data: data.department.map(d => d.earlyCount) }
    ]
  })

  // 更新时段分布图
  const timeInstance = echarts.getInstanceByDom(timeChart.value)
  const timeData = Array.from({ length: 24 }, (_, i) => {
    const hourData = data.timeDistribution.find(d => d.hour === i)
    return hourData || { lateCount: 0, earlyCount: 0 }
  })
  timeInstance.setOption({
    series: [
      { data: timeData.map(d => d.lateCount) },
      { data: timeData.map(d => d.earlyCount) }
    ]
  })

  // 更新TOP排行图
  const topInstance = echarts.getInstanceByDom(topChart.value)
  let topData = []
  if (topType.value === 'late') {
    topData = data.top.lateValues
  } else {
    topData = data.top.earlyValues
  }
  topInstance.setOption({
    yAxis: { data: data.top.employees },
    series: [{ data: topData }]
  })
}

// 重置搜索
function resetSearch() {
  searchForm.dateRange = [
    dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
    dayjs().format('YYYY-MM-DD')
  ]
  searchForm.departmentId = ''
  searchForm.employeeId = ''
  searchForm.statisticsType = 'summary'
  handleSearch()
}

// 刷新数据
function refreshData() {
  handleSearch()
}

// 导出数据
async function exportData() {
  try {
    loading.value = true
    const data = await attendanceApi.exportLateEarlyStatistics(searchForm)
    
    exportToExcel({
      data: data.list,
      columns: [
        { key: 'employeeName', title: '姓名' },
        { key: 'employeeNo', title: '工号' },
        { key: 'department', title: '部门' },
        { key: 'position', title: '职位' },
        { key: 'lateCount', title: '迟到次数' },
        { key: 'earlyCount', title: '早退次数' },
        { key: 'lateDuration', title: '迟到总时长(分钟)' },
        { key: 'earlyDuration', title: '早退总时长(分钟)' },
        { key: 'lateRate', title: '迟到率(%)' },
        { key: 'earlyRate', title: '早退率(%)' },
        { key: 'averageLateDuration', title: '平均迟到时长(分钟)' },
        { key: 'averageEarlyDuration', title: '平均早退时长(分钟)' }
      ],
      filename: `迟到早退统计_${dayjs().format('YYYYMMDD')}`
    })
    
    ElMessage.success('导出成功')
  } catch (__error) {
    ElMessage.error('导出失败')
  } finally {
    loading.value = false
  }
}

// 查看详情
   
function viewDetail(row: unknown) {
  currentEmployee.value = row
  detailDialogVisible.value = true
}

// 查看规律
   
function viewPattern(row: unknown) {
  currentEmployee.value = row
  patternDialogVisible.value = true
}

// 分页处理
function handleSizeChange(val: number) {
  pagination.pageSize = val
  handleSearch()
}

function handleCurrentChange(val: number) {
  pagination.currentPage = val
  handleSearch()
}

// 监听部门变化，加载员工列表
watch(() => searchForm.departmentId, async (newVal) => {
  if (newVal) {
    try {
      employeeList.value = await attendanceApi.getDepartmentEmployees(newVal)
    } catch (__error) {
      }
  } else {
    employeeList.value = []
    searchForm.employeeId = ''
  }
})

// 监听时间粒度和TOP类型变化
watch([timeGranularity, topType], () => {
  if (trendChart.value && topChart.value) {
    handleSearch()
  }
})

// 初始化
onMounted(async () => {
  // 加载部门树
  try {
    departmentTree.value = await attendanceApi.getDepartmentTree()
  } catch (__error) {
    }

  // 初始化图表
  initCharts()

  // 加载数据
  handleSearch()
})
</script>

<style lang="scss" scoped>
.late-early-statistics {
  padding: 20px;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
    }
    
    .header-actions {
      display: flex;
      gap: 10px;
    }
  }
  
  .search-card {
    margin-bottom: 20px;
  }
  
  .stats-overview {
    margin-bottom: 20px;
    
    .overview-card {
      background: #fff;
      border-radius: 8px;
      padding: 20px;
      display: flex;
      align-items: center;
      gap: 16px;
      transition: all 0.3s;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      }
      
      .card-icon {
        width: 64px;
        height: 64px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .el-icon {
          color: #fff;
        }
      }
      
      &.card-primary .card-icon { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
      &.card-success .card-icon { background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%); }
      &.card-warning .card-icon { background: linear-gradient(135deg, #e6a23c 0%, #ffd93d 100%); }
      &.card-danger .card-icon { background: linear-gradient(135deg, #f56c6c 0%, #ff8a80 100%); }
      &.card-info .card-icon { background: linear-gradient(135deg, #409eff 0%, #36cfc9 100%); }
      
      .card-content {
        flex: 1;
        
        .card-value {
          font-size: 28px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 4px;
        }
        
        .card-label {
          font-size: 14px;
          color: #909399;
        }
        
        .card-trend {
          display: flex;
          align-items: center;
          gap: 4px;
          margin-top: 8px;
          font-size: 12px;
          
          .trend-up {
            color: #f56c6c;
          }
          
          .trend-down {
            color: #67c23a;
          }
        }
      }
    }
  }
  
  .charts-container {
    margin-bottom: 20px;
    
    .chart-card {
      height: 400px;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .chart-container {
        height: 320px;
      }
    }
  }
  
  .table-card {
    .el-pagination {
      margin-top: 20px;
      justify-content: flex-end;
    }
  }
  
  .pattern-analysis {
    .pattern-section {
      margin-bottom: 20px;
      
      h4 {
        margin: 0 0 12px 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
      }
      
      .pattern-tags {
        min-height: 40px;
      }
    }
  }
}
</style>