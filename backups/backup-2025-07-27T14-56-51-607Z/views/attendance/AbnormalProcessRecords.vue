<template>
  <div class="abnormal-process-records">
    <!-- 搜索区域 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" inline>
        <el-form-item label="处理时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            clearable
           />
        </el-form-item>
        <el-form-item label="部门">
          <el-select v-model="searchForm.departmentId" placeholder="选择部门" clearable>
            <el-option
              v-for="dept in departments"
              :key="dept.id"
              :label="dept.name"
              :value="dept.id"
             />
          </el-select>
        </el-form-item>
        <el-form-item label="员工">
          <el-select
            v-model="searchForm.employeeId"
            placeholder="选择员工"
            clearable
            filterable
          >
            <el-option
              v-for="emp in employees"
              :key="emp.id"
              :label="`${emp.name} (${emp.employeeNo})`"
              :value="emp.id"
             />
          </el-select>
        </el-form-item>
        <el-form-item label="处理类型">
          <el-select v-model="searchForm.processType" placeholder="选择处理类型" clearable>
            <el-option label="通过" value="APPROVED"  />
            <el-option label="驳回" value="REJECTED"  />
            <el-option label="扣款" value="DEDUCTED"  />
            <el-option label="忽略" value="IGNORED"  />
            <el-option label="待处理" value="PENDING"  />
          </el-select>
        </el-form-item>
        <el-form-item label="处理人">
          <el-select v-model="searchForm.processorId" placeholder="选择处理人" clearable>
            <el-option
              v-for="processor in processors"
              :key="processor.id"
              :label="processor.name"
              :value="processor.id"
             />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="handleReset">
            <el-icon><RefreshLeft /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <el-row :gutter="20">
        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ statistics.totalProcessed }}</div>
              <div class="stat-label">总处理数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ statistics.approvedCount }}</div>
              <div class="stat-label">已通过</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ statistics.rejectedCount }}</div>
              <div class="stat-label">已驳回</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ statistics.deductedCount }}</div>
              <div class="stat-label">已扣款</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ statistics.pendingCount }}</div>
              <div class="stat-label">待处理</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ formatAmount(statistics.totalDeductAmount) }}</div>
              <div class="stat-label">总扣款金额</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 操作区域 -->
    <el-card class="action-card" shadow="never">
      <div class="action-wrapper">
        <div class="action-left">
          <el-button type="primary" @click="handleBatchProcess" :disabled="!selectedRecords.length">
            <el-icon><Operation /></el-icon>
            批量处理
          </el-button>
          <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            导出记录
          </el-button>
        </div>
        <div class="action-right">
          <el-button @click="handleViewStatistics">
            <el-icon><DataAnalysis /></el-icon>
            统计分析
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <el-card shadow="never">
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="abnormalDate" label="异常日期" width="110"  />
        <el-table-column prop="employeeName" label="员工姓名" width="100">
          <template #default="{ row }">
            <div>{{ row.employeeName }}</div>
            <div class="text-secondary">{{ row.employeeNo }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="department" label="部门" width="120"  />
        <el-table-column prop="abnormalType" label="异常类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getAbnormalTypeColor(row.abnormalType)" size="small">
              {{ getAbnormalTypeLabel(row.abnormalType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="abnormalDescription" label="异常描述" min-width="200"  />
        <el-table-column prop="processType" label="处理类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getProcessTypeColor(row.processType)" size="small">
              {{ getProcessTypeLabel(row.processType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="processResult" label="处理结果" min-width="180"  />
        <el-table-column prop="deductAmount" label="扣款金额" width="100">
          <template #default="{ row }">
            <span v-if="row.deductAmount" class="text-danger">
              {{ formatAmount(row.deductAmount) }}
            </span>
            <span v-else class="text-secondary">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="processorName" label="处理人" width="100"  />
        <el-table-column prop="processTime" label="处理时间" width="160"  />
        <el-table-column prop="approvalStatus" label="审批状态" width="100">
          <template #default="{ row }">
            <el-tag
              v-if="row.approvalRequired"
              :type="getApprovalStatusColor(row.approvalStatus)"
              size="small"
            >
              {{ getApprovalStatusLabel(row.approvalStatus) }}
            </el-tag>
            <span v-else class="text-secondary">无需审批</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button
              v-if="row.processType === 'PENDING'"
              link
              type="primary"
              size="small"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="row.approvalRequired && row.approvalStatus === 'PENDING'"
              link
              type="primary"
              size="small"
              @click="handleApprove(row)"
            >
              审批
            </el-button>
            <el-button
              link
              type="primary"
              size="small"
              @click="handleViewHistory(row)"
            >
              历史
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
    </el-card>

    <!-- 处理记录详情对话框 -->
    <ProcessRecordDialog
      v-model="dialogVisible"
      :record="currentRecord"
      :mode="dialogMode"
      @confirm="handleDialogConfirm"
    />

    <!-- 批量处理对话框 -->
    <BatchProcessDialog
      v-model="batchDialogVisible"
      :records="selectedRecords"
      @confirm="handleBatchConfirm"
    />

    <!-- 统计分析对话框 -->
    <StatisticsDialog
      v-model="statisticsDialogVisible"
      :search-params="searchForm"
    />
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'AbnormalProcessRecords'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, RefreshLeft, Operation, Download, DataAnalysis } from '@element-plus/icons-vue'
import { attendanceApi } from '@/api/attendance'
import ProcessRecordDialog from './components/ProcessRecordDialog.vue'
import BatchProcessDialog from './components/BatchProcessDialog.vue'
import HrStatisticsDialog from './components/HrStatisticsDialog.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const selectedRecords = ref([])
const dialogVisible = ref(false)
const batchDialogVisible = ref(false)
const statisticsDialogVisible = ref(false)
const currentRecord = ref(null)
const dialogMode = ref('view')

// 搜索表单
const searchForm = reactive({
  dateRange: [],
  departmentId: '',
  employeeId: '',
  processType: '',
  processorId: ''
})

// 分页参数
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

// 统计数据
const statistics = reactive({
  totalProcessed: 0,
  approvedCount: 0,
  rejectedCount: 0,
  deductedCount: 0,
  ignoredCount: 0,
  pendingCount: 0,
  avgProcessTime: 0,
  totalDeductAmount: 0
})

// 基础数据
const departments = ref([])
const employees = ref([])
const processors = ref([])

// 获取处理记录列表
const fetchRecords = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      page: pagination.current,
      pageSize: pagination.pageSize
    }
    
    const {data: _data} =  await attendanceApi.getAbnormalProcessRecords(params)
    tableData.value 

  .search-card {
    margin-bottom: 20px;
  }

  .statistics-cards {
    margin-bottom: 20px;

    .stat-card {
      .stat-content {
        text-align: center;
        padding: 10px;

        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #1890ff;
          margin-bottom: 5px;
        }

        .stat-label {
          font-size: 14px;
          color: #666;
        }
      }
    }
  }

  .action-card {
    margin-bottom: 20px;

    .action-wrapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .text-secondary {
    color: #909399;
    font-size: 12px;
  }

  .text-danger {
    color: #f56c6c;
    font-weight: bold;
  }

  :deep(.el-pagination) {
    margin-top: 20px;
    justify-content: flex-end;
  }
}
</style>