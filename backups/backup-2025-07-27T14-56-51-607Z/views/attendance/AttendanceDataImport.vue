<template>
  <div class="attendance-data-import">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>考勤数据导入</h2>
      <div class="header-actions">
        <el-button @click="downloadTemplate">
          <el-icon><Download /></el-icon>
          下载模板
        </el-button>
      </div>
    </div>

    <!-- 导入步骤 -->
    <el-card class="import-steps-card">
      <el-steps :active="currentStep" align-center>
        <el-step title="上传文件" description="选择Excel文件上传"  />
        <el-step title="数据预览" description="预览并确认数据"  />
        <el-step title="数据校验" description="检查数据有效性"  />
        <el-step title="导入结果" description="查看导入结果"  />
      </el-steps>
    </el-card>

    <!-- 步骤1：文件上传 -->
    <el-card v-if="currentStep === 0" class="step-content-card">
      <div class="upload-container">
        <el-upload
          ref="uploadRef"
          class="upload-dragger"
          drag
          :auto-upload="false"
          :file-list="fileList"
          :limit="1"
          :on-change="handleFileChange"
          :on-exceed="handleExceed"
          accept=".xlsx,.xls"
        >
          <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              只能上传 Excel 文件，且不超过 10MB
            </div>
          </template>
        </el-upload>

        <div class="upload-tips">
          <h4>导入说明：</h4>
          <ul>
            <li>请使用系统提供的标准模板进行数据填写</li>
            <li>支持的文件格式：.xlsx、.xls</li>
            <li>单次导入记录数不超过10000条</li>
            <li>日期格式：YYYY-MM-DD HH:mm:ss</li>
            <li>必填字段：员工工号、打卡时间、打卡类型</li>
          </ul>
        </div>

        <div class="action-buttons">
          <el-button type="primary" @click="handleUpload" :disabled="!fileList.length">
            开始上传
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 步骤2：数据预览 -->
    <el-card v-if="currentStep === 1" class="step-content-card">
      <div class="preview-header">
        <h3>数据预览</h3>
        <el-text type="info">共 {{ previewData.length }} 条记录</el-text>
      </div>

      <el-table :data="previewData" max-height="400">
        <el-table-column prop="rowIndex" label="行号" width="60"  />
        <el-table-column prop="employeeNo" label="员工工号" width="120"  />
        <el-table-column prop="employeeName" label="员工姓名" width="120"  />
        <el-table-column prop="department" label="部门" width="150"  />
        <el-table-column prop="checkTime" label="打卡时间" width="160"  />
        <el-table-column prop="checkType" label="打卡类型" width="100">
          <template #default="{ row }">
            <el-tag size="small">{{ checkTypeMap[row.checkType] }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="location" label="打卡地点"  />
        <el-table-column prop="remark" label="备注"  />
      </el-table>

      <div class="action-buttons">
        <el-button @click="currentStep--">上一步</el-button>
        <el-button type="primary" @click="handleValidate">下一步</el-button>
      </div>
    </el-card>

    <!-- 步骤3：数据校验 -->
    <el-card v-if="currentStep === 2" class="step-content-card">
      <div class="validation-result">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="result-item success">
              <div class="result-icon">
                <el-icon :size="48"><CircleCheck /></el-icon>
              </div>
              <div class="result-info">
                <div class="result-count">{{ validationResult.validCount }}</div>
                <div class="result-label">有效记录</div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="result-item warning">
              <div class="result-icon">
                <el-icon :size="48"><Warning /></el-icon>
              </div>
              <div class="result-info">
                <div class="result-count">{{ validationResult.warningCount }}</div>
                <div class="result-label">警告记录</div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="result-item error">
              <div class="result-icon">
                <el-icon :size="48"><CircleClose /></el-icon>
              </div>
              <div class="result-info">
                <div class="result-count">{{ validationResult.errorCount }}</div>
                <div class="result-label">错误记录</div>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 错误详情 -->
        <div class="error-details" v-if="validationErrors.length > 0">
          <h4>错误详情：</h4>
          <el-table :data="validationErrors" max-height="300">
            <el-table-column prop="row" label="行号" width="60"  />
            <el-table-column prop="field" label="字段" width="120"  />
            <el-table-column prop="value" label="值" width="150"  />
            <el-table-column prop="message" label="错误信息"  />
            <el-table-column label="操作" width="100">
              <template #default="{ row }">
                <el-button link type="primary" @click="handleFixError(row)">修正</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 警告详情 -->
        <div class="warning-details" v-if="validationWarnings.length > 0">
          <h4>警告信息：</h4>
          <el-table :data="validationWarnings" max-height="200">
            <el-table-column prop="row" label="行号" width="60"  />
            <el-table-column prop="message" label="警告信息"  />
            <el-table-column prop="suggestion" label="建议"  />
          </el-table>
        </div>

        <div class="action-buttons">
          <el-button @click="currentStep--">上一步</el-button>
          <el-button 
            type="primary" 
            @click="handleImport"
            :disabled="validationResult.errorCount > 0"
          >
            {{ validationResult.errorCount > 0 ? '存在错误，无法导入' : '确认导入' }}
          </el-button>
          <el-button 
            v-if="validationResult.warningCount > 0"
            type="warning"
            @click="handleIgnoreWarnings"
          >
            忽略警告并导入
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 步骤4：导入结果 -->
    <el-card v-if="currentStep === 3" class="step-content-card">
      <div class="import-result">
        <el-result
          :icon="importResult.success ? 'success' : 'error'"
          :title="importResult.success ? '导入成功' : '导入失败'"
        >
          <template #sub-title>
            <div class="result-summary">
              共处理 <strong>{{ importResult.totalCount }}</strong> 条记录，
              成功 <strong class="text-success">{{ importResult.successCount }}</strong> 条，
              失败 <strong class="text-danger">{{ importResult.failedCount }}</strong> 条
            </div>
          </template>
          <template #extra>
            <el-button type="primary" @click="handleFinish">完成</el-button>
            <el-button @click="handleDownloadResult" v-if="importResult.failedCount > 0">
              下载失败记录
            </el-button>
          </template>
        </el-result>

        <!-- 导入详情 -->
        <div class="import-details" v-if="importResult.details">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="导入批次号">{{ importResult.batchNo }}</el-descriptions-item>
            <el-descriptions-item label="导入时间">{{ importResult.importTime }}</el-descriptions-item>
            <el-descriptions-item label="操作人">{{ importResult.operator }}</el-descriptions-item>
            <el-descriptions-item label="文件名">{{ importResult.fileName }}</el-descriptions-item>
            <el-descriptions-item label="处理耗时">{{ importResult.duration }}秒</el-descriptions-item>
            <el-descriptions-item label="冲突处理">{{ conflictStrategyMap[importResult.conflictStrategy] }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 失败记录列表 -->
        <div class="failed-records" v-if="importResult.failedRecords?.length > 0">
          <h4>失败记录：</h4>
          <el-table :data="importResult.failedRecords" max-height="300">
            <el-table-column prop="row" label="原始行号" width="80"  />
            <el-table-column prop="employeeNo" label="员工工号" width="120"  />
            <el-table-column prop="employeeName" label="员工姓名" width="120"  />
            <el-table-column prop="checkTime" label="打卡时间" width="160"  />
            <el-table-column prop="reason" label="失败原因"  />
          </el-table>
        </div>
      </div>
    </el-card>

    <!-- 导入设置对话框 -->
    <el-dialog v-model="showImportSettings" title="导入设置" width="500px">
      <el-form :model="importSettings" label-width="120px">
        <el-form-item label="冲突处理策略">
          <el-radio-group v-model="importSettings.conflictStrategy">
            <el-radio label="skip">跳过重复记录</el-radio>
            <el-radio label="override">覆盖重复记录</el-radio>
            <el-radio label="merge">合并重复记录</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="时间范围限制">
          <el-switch v-model="importSettings.enableTimeRange"  />
        </el-form-item>
        <el-form-item label="允许的时间范围" v-if="importSettings.enableTimeRange">
          <el-date-picker
            v-model="importSettings.timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
           />
        </el-form-item>
        <el-form-item label="自动修正">
          <el-checkbox-group v-model="importSettings.autoFix">
            <el-checkbox label="employeeNo">自动补全工号前缀</el-checkbox>
            <el-checkbox label="datetime">自动修正日期格式</el-checkbox>
            <el-checkbox label="department">自动匹配部门名称</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showImportSettings = false">取消</el-button>
        <el-button type="primary" @click="handleSaveSettings">保存设置</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'AttendanceDataImport'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { UploadProps, UploadRawFile } from 'element-plus'
import { 
  Download,
  UploadFilled,
  CircleCheck,
  Warning,
  CircleClose
} from '@element-plus/icons-vue'

// 数据定义
const currentStep = ref(0)
const uploadRef = ref()
const fileList = ref<any[]>([])
const showImportSettings = ref(false)

// 预览数据
const previewData = ref([
  {
    rowIndex: 2,
    employeeNo: 'EMP001',
    employeeName: '张三',
    department: '技术部',
    checkTime: '2025-01-21 08:30:00',
    checkType: 'in',
    location: '一楼大厅',
    remark: ''
  },
  {
    rowIndex: 3,
    employeeNo: 'EMP001',
    employeeName: '张三',
    department: '技术部',
    checkTime: '2025-01-21 18:00:00',
    checkType: 'out',
    location: '一楼大厅',
    remark: ''
  }
])

// 校验结果
const validationResult = reactive({
  validCount: 0,
  warningCount: 0,
  errorCount: 0
})

// 校验错误
const validationErrors = ref([
  {
    row: 5,
    field: 'employeeNo',
    value: 'EMP999',
    message: '员工工号不存在'
  }
])

// 校验警告
const validationWarnings = ref([
  {
    row: 10,
    message: '打卡时间超出正常范围（22:00后）',
    suggestion: '请确认是否为加班打卡'
  }
])

// 导入结果
const importResult = reactive({
  success: false,
  totalCount: 0,
  successCount: 0,
  failedCount: 0,
  batchNo: '',
  importTime: '',
  operator: '',
  fileName: '',
  duration: 0,
  conflictStrategy: 'skip',
  details: null,
  failedRecords: []
})

// 导入设置
const importSettings = reactive({
  conflictStrategy: 'skip',
  enableTimeRange: false,
  timeRange: [],
  autoFix: ['datetime']
})

// 枚举映射
const checkTypeMap: Record<string, string> = {
  in: '上班',
  out: '下班',
  breakOut: '休息开始',
  breakIn: '休息结束'
}

const conflictStrategyMap: Record<string, string> = {
  skip: '跳过重复',
  override: '覆盖重复',
  merge: '合并记录'
}

// 方法定义
const downloadTemplate = () => {
  ElMessage.success('正在下载模板...')
  // 实际下载模板文件
}

const handleFileChange: UploadProps['onChange'] = (uploadFile) => {
  fileList.value = [uploadFile]
}

const handleExceed: UploadProps['onExceed'] = () => {
  ElMessage.warning('只能上传一个文件')
}

const handleUpload = async () => {
  if (!fileList.value.length) {
    ElMessage.warning('请选择要上传的文件')
    return
  }

  // 模拟上传和解析
  ElMessage.info('正在解析文件...')
  setTimeout(() => {
    currentStep.value = 1
    ElMessage.success('文件解析成功')
  }, 1500)
}

const handleValidate = () => {
  ElMessage.info('正在校验数据...')
  
  // 模拟校验过程
  setTimeout(() => {
    validationResult.validCount = 98
    validationResult.warningCount = 1
    validationResult.errorCount = 1
    currentStep.value = 2
  }, 1000)
}

   
const handleFixError = (error: unknown) => {
  ElMessage.info(`修正错误：${error.message}`)
  // 实际修正逻辑
}

const handleImport = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要导入这些考勤数据吗？',
      '提示',
      { type: 'warning' }
    )
    
    performImport()
  } catch {
    // 用户取消
  }
}

const handleIgnoreWarnings = () => {
  ElMessageBox.confirm(
    '忽略警告可能导致数据异常，确定要继续吗？',
    '警告',
    { type: 'warning' }
  ).then(() => {
    performImport()
  })
}

const performImport = () => {
  ElMessage.info('正在导入数据...')
  
  // 模拟导入过程
  setTimeout(() => {
    importResult.success = true
    importResult.totalCount = 100
    importResult.successCount = 98
    importResult.failedCount = 2
    importResult.batchNo = 'IMP20250121093000'
    importResult.importTime = '2025-01-21 09:30:00'
    importResult.operator = '管理员'
    importResult.fileName = 'attendance_202501.xlsx'
    importResult.duration = 3.5
    importResult.details = true
    importResult.failedRecords = [
      {
        row: 5,
        employeeNo: 'EMP999',
        employeeName: '未知',
        checkTime: '2025-01-20 08:30:00',
        reason: '员工工号不存在'
      },
      {
        row: 50,
        employeeNo: 'EMP050',
        employeeName: '王五',
        checkTime: '2025-01-32 08:30:00',
        reason: '日期格式错误'
      }
    ]
    
    currentStep.value = 3
  }, 2000)
}

const handleFinish = () => {
  // 重置状态
  currentStep.value = 0
  fileList.value = []
  ElMessage.success('导入流程已完成')
}

const handleDownloadResult = () => {
  ElMessage.success('正在下载失败记录...')
  // 实际下载失败记录
}

const handleSaveSettings = () => {
  showImportSettings.value = false
  ElMessage.success('导入设置已保存')
}
</script>

<style scoped>
.attendance-data-import {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.import-steps-card {
  margin-bottom: 20px;
}

.step-content-card {
  min-height: 400px;
}

.upload-container {
  padding: 20px;
}

.upload-dragger {
  width: 100%;
}

.upload-tips {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.upload-tips h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #606266;
}

.upload-tips ul {
  margin: 0;
  padding-left: 20px;
  color: #909399;
  font-size: 13px;
}

.upload-tips li {
  margin-bottom: 5px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.preview-header h3 {
  margin: 0;
  font-size: 16px;
}

.validation-result {
  padding: 20px;
}

.result-item {
  text-align: center;
  padding: 20px;
  border-radius: 8px;
  background-color: #f5f7fa;
}

.result-item.success {
  background-color: #f0f9ff;
}

.result-item.warning {
  background-color: #fef5e7;
}

.result-item.error {
  background-color: #fef0f0;
}

.result-icon {
  margin-bottom: 10px;
}

.result-icon .el-icon {
  color: #67C23A;
}

.warning .result-icon .el-icon {
  color: #E6A23C;
}

.error .result-icon .el-icon {
  color: #F56C6C;
}

.result-count {
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 5px;
}

.result-label {
  font-size: 14px;
  color: #666;
}

.error-details,
.warning-details {
  margin-top: 20px;
}

.error-details h4,
.warning-details h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #606266;
}

.action-buttons {
  text-align: center;
  margin-top: 30px;
}

.action-buttons .el-button {
  margin: 0 10px;
}

.import-result {
  padding: 20px;
}

.result-summary {
  font-size: 16px;
  line-height: 1.5;
}

.text-success {
  color: #67C23A;
}

.text-danger {
  color: #F56C6C;
}

.import-details {
  margin-top: 30px;
}

.failed-records {
  margin-top: 20px;
}

.failed-records h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #606266;
}
</style>