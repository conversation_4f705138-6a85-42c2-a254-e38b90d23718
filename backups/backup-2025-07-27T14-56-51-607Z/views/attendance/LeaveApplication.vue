<template>
  <div class="leave-application">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>请假申请</h2>
      <div class="header-actions">
        <el-button @click="viewHistory">申请记录</el-button>
        <el-button @click="checkBalance">假期余额</el-button>
      </div>
    </div>

    <!-- 申请表单 -->
    <el-card class="form-card">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        size="large"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="请假类型" prop="leaveType">
              <el-select v-model="form.leaveType" placeholder="请选择请假类型" @change="onLeaveTypeChange">
                <el-option
                  v-for="type in leaveTypes"
                  :key="type.value"
                  :label="type.label"
                  :value="type.value"
                >
                  <span>{{ type.label }}</span>
                  <span class="option-desc">{{ type.desc }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="紧急程度" prop="urgency">
              <el-radio-group v-model="form.urgency">
                <el-radio value="normal">普通</el-radio>
                <el-radio value="urgent">紧急</el-radio>
                <el-radio value="critical">特急</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                v-model="form.startTime"
                type="datetime"
                placeholder="选择开始时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
                :disabled-date="disabledStartDate"
                @change="calculateDays"
               />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                v-model="form.endTime"
                type="datetime"
                placeholder="选择结束时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
                :disabled-date="disabledEndDate"
                @change="calculateDays"
               />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="请假天数">
              <el-input v-model="form.leaveDays" readonly>
                <template #append>天</template>
              </el-input>
              <div class="form-tip" v-if="currentBalance">
                剩余额度：{{ currentBalance.remainingQuota }}天
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否出境" prop="isOverseas">
              <el-switch v-model="form.isOverseas"  />
              <span class="switch-label">{{ form.isOverseas ? '是' : '否' }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="请假事由" prop="reason">
          <el-input
            v-model="form.reason"
            type="textarea"
            :rows="4"
            placeholder="请详细说明请假原因（不少于10个字）"
            maxlength="500"
            show-word-limit
            />
        </el-form-item>

        <!-- 教师调课安排 -->
        <el-form-item label="调课安排" prop="courseAdjustment" v-if="isTeacher">
          <el-input
            v-model="form.courseAdjustment"
            type="textarea"
            :rows="3"
            placeholder="请说明课程调整安排"
            />
          <el-button type="text" @click="showCourseSchedule">查看课表</el-button>
        </el-form-item>

        <!-- 附件上传 -->
        <el-form-item label="附件材料" :required="attachmentRequired">
          <el-upload
            v-model:file-list="form.attachments"
            class="attachment-upload"
            action="#"
            :auto-upload="false"
            :limit="5"
            accept=".jpg,.jpeg,.png,.pdf,.doc,.docx"
            :on-exceed="handleExceed"
            :before-remove="beforeRemove"
          >
            <el-button type="primary" plain>
              <el-icon><Upload /></el-icon>
              选择文件
            </el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持jpg/png/pdf/doc格式，单个文件不超过10MB
                <span v-if="attachmentRequired" class="required-tip">
                  （{{ attachmentTip }}）
                </span>
              </div>
            </template>
          </el-upload>
        </el-form-item>

        <!-- 工作交接 -->
        <el-form-item label="工作交接人" v-if="form.leaveDays > 3">
          <el-select v-model="form.handoverPerson" placeholder="请选择工作交接人">
            <el-option
              v-for="person in colleagueList"
              :key="person.id"
              :label="person.name"
              :value="person.id"
             />
          </el-select>
        </el-form-item>

        <!-- 审批流程预览 -->
        <el-form-item label="审批流程">
          <div class="approval-flow">
            <div 
              v-for="(node, index) in approvalFlow" 
              :key="index"
              class="flow-node"
              :class="{ active: index === 0 }"
            >
              <div class="node-icon">
                <el-icon><UserFilled /></el-icon>
              </div>
              <div class="node-info">
                <div class="node-title">{{ node.title }}</div>
                <div class="node-desc">{{ node.approver }}</div>
              </div>
              <el-icon class="flow-arrow" v-if="index < approvalFlow.length - 1">
                <ArrowRight />
              </el-icon>
            </div>
          </div>
        </el-form-item>

        <!-- 提交按钮 -->
        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="loading">
            提交申请
          </el-button>
          <el-button @click="saveAsDraft">保存草稿</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 假期余额对话框 -->
    <el-dialog
      v-model="balanceDialogVisible"
      title="假期余额查询"
      width="800px"
    >
      <el-table :data="leaveBalances" stripe>
        <el-table-column prop="typeName" label="假期类型" width="120"  />
        <el-table-column prop="year" label="年度" width="80"  />
        <el-table-column prop="totalQuota" label="总额度" width="100">
          <template #default="{ row }">
            {{ row.totalQuota }}天
          </template>
        </el-table-column>
        <el-table-column prop="usedQuota" label="已使用" width="100">
          <template #default="{ row }">
            <span :class="{ 'text-warning': row.usedQuota > row.totalQuota * 0.8 }">
              {{ row.usedQuota }}天
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="remainingQuota" label="剩余" width="100">
          <template #default="{ row }">
            <span :class="{ 'text-danger': row.remainingQuota < 5 }">
              {{ row.remainingQuota }}天
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="expireDate" label="过期时间" width="120"  />
        <el-table-column label="操作" width="100">
          <template #default="{ row }">
            <el-button type="text" @click="viewBalanceDetail(row)">
              明细
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 课表对话框 -->
    <el-dialog
      v-model="courseDialogVisible"
      title="本周课表"
      width="900px"
    >
      <div class="course-schedule">
        <el-table :data="courseSchedule" height="400">
          <el-table-column prop="weekday" label="星期" width="80"  />
          <el-table-column prop="period" label="节次" width="100"  />
          <el-table-column prop="courseName" label="课程名称"  />
          <el-table-column prop="className" label="班级" width="120"  />
          <el-table-column prop="location" label="地点" width="120"  />
          <el-table-column label="冲突" width="80">
            <template #default="{ row }">
              <el-tag v-if="checkCourseConflict(row)" type="danger" size="small">
                冲突
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Upload, 
  UserFilled, 
  ArrowRight 
} from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { useUserStore } from '@/stores'
import { workflowApi } from '@/api/workflow'
import { attendanceApi } from '@/api/attendance'
// 表单数据
const form = reactive({
  leaveType: '',
  urgency: 'normal',
  startTime: '',
  endTime: '',
  leaveDays: 0,
  reason: '',
  courseAdjustment: '',
  isOverseas: false,
  attachments: [],
  handoverPerson: ''
})

// 表单验证规则
const rules = {
  leaveType: [
    { required: true, message: '请选择请假类型', trigger: 'change' }
  ],
  urgency: [
    { required: true, message: '请选择紧急程度', trigger: 'change' }
  ],
  startTime: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  endTime: [
    { required: true, message: '请选择结束时间', trigger: 'change' },
    { validator: validateEndTime, trigger: 'change' }
  ],
  reason: [
    { required: true, message: '请填写请假事由', trigger: 'blur' },
    { min: 10, message: '请假事由不少于10个字', trigger: 'blur' }
  ],
  courseAdjustment: [
    { required: true, message: '请填写调课安排', trigger: 'blur' }
  ]
}

// 请假类型配置
const leaveTypes = [
  { value: 'annual', label: '年假', desc: '带薪年休假' },
  { value: 'sick', label: '病假', desc: '因病需要休息' },
  { value: 'personal', label: '事假', desc: '因私事需要处理' },
  { value: 'marriage', label: '婚假', desc: '结婚假期' },
  { value: 'maternity', label: '产假', desc: '生育假期' },
  { value: 'paternity', label: '陪产假', desc: '配偶生育陪护' },
  { value: 'bereavement', label: '丧假', desc: '直系亲属去世' },
  { value: 'compensatory', label: '调休', desc: '加班补休' },
  { value: 'study', label: '学习假', desc: '脱产学习培训' }
]

// 响应式变量
const formRef = ref()
const loading = ref(false)
const userStore = useUserStore()
const isTeacher = computed(() => userStore.userInfo?.employeeType === 'teacher')
const balanceDialogVisible = ref(false)
const courseDialogVisible = ref(false)
const leaveBalances = ref([])
const courseSchedule = ref([])
const colleagueList = ref([])
const currentBalance = ref(null)

// 计算属性
const attachmentRequired = computed(() => {
  // 病假超过3天需要医院证明
  if (form.leaveType === 'sick' && form.leaveDays > 3) return true
  // 婚假需要结婚证
  if (form.leaveType === 'marriage') return true
  // 产假需要相关证明
  if (['maternity', 'paternity'].includes(form.leaveType)) return true
  return false
})

const attachmentTip = computed(() => {
  const tips = {
    sick: '需提供医院诊断证明',
    marriage: '需提供结婚证复印件',
    maternity: '需提供准生证或出生证明',
    paternity: '需提供准生证或出生证明',
    bereavement: '需提供死亡证明或户口本'
  }
  return tips[form.leaveType] || '需提供相关证明材料'
})

// 审批流程预览
const approvalFlow = computed(() => {
  const flows = []
  
  // 直接主管审批
  flows.push({
    title: '直接主管',
    approver: '部门负责人'
  })
  
  // 根据请假类型和天数动态添加审批节点
  if (form.leaveDays > 3 || ['marriage', 'maternity'].includes(form.leaveType)) {
    flows.push({
      title: '人事审批',
      approver: '人事处'
    })
  }
  
  if (form.leaveDays > 15) {
    flows.push({
      title: '分管领导',
      approver: '分管校领导'
    })
  }
  
  if (form.leaveDays > 90) {
    flows.push({
      title: '校长审批',
      approver: '校长办公会'
    })
  }
  
  return flows
})

// 方法
   
function validateEndTime(_rule: unknown, value: unknown, callback: unknown) {
  if (value && form.startTime) {
    if (dayjs(value).isBefore(dayjs(form.startTime))) {
      callback(new Error('结束时间不能早于开始时间'))
    } else {
      callback()
    }
  } else {
    callback()
  }
}

function disabledStartDate(date: Date) {
  // 不能选择过去的日期（紧急情况除外）
  if (form.urgency !== 'critical') {
    return date < new Date(new Date().setHours(0, 0, 0, 0))
  }
  return false
}

function disabledEndDate(date: Date) {
  if (!form.startTime) return false
  return date < new Date(form.startTime)
}

async function calculateDays() {
  if (form.startTime && form.endTime) {
    try {
      const days = await attendanceApi.calculateLeaveDays({
        startTime: form.startTime,
        endTime: form.endTime,
        leaveType: form.leaveType
      })
      form.leaveDays = days
      
      // 检查余额
      if (currentBalance.value && days > currentBalance.value.remainingQuota) {
        ElMessage.warning(`剩余额度不足，当前剩余${currentBalance.value.remainingQuota}天`)
      }
    } catch (__error) {
      }
  }
}

async function onLeaveTypeChange(type: string) {
  // 清空之前的计算结果
  form.leaveDays = 0
  
  // 获取该类型的假期余额
  try {
    const balances = await attendanceApi.getLeaveBalance(userStore.userInfo.employeeId)
    currentBalance.value = balances.find(b => b.type === type)
    
    if (currentBalance.value && currentBalance.value.remainingQuota === 0) {
      ElMessage.warning('该类型假期余额为0')
    }
  } catch (__error) {
    }
  
  // 重新计算天数
  if (form.startTime && form.endTime) {
    await calculateDays()
  }
}

async function checkBalance() {
  try {
    loading.value = true
    const data = await attendanceApi.getLeaveBalance(userStore.userInfo.employeeId)
    leaveBalances.value = data.map(item => ({
      ...item,
      typeName: leaveTypes.find(t => t.value === item.type)?.label || item.type,
      expireDate: item.validUntil || '年底'
    }))
    balanceDialogVisible.value = true
  } catch (__error) {
    ElMessage.error('查询假期余额失败')
  } finally {
    loading.value = false
  }
}

   
function viewBalanceDetail(row: unknown) {
  // 跳转到余额明细页面
  }

async function showCourseSchedule() {
  try {
    // 获取本周课表
    const schedule = await attendanceApi.getTeacherSchedule({
      teacherId: userStore.userInfo.employeeId,
      startDate: form.startTime,
      endDate: form.endTime
    })
    courseSchedule.value = schedule
    courseDialogVisible.value = true
  } catch (__error) {
    ElMessage.error('获取课表失败')
  }
}

   
function checkCourseConflict(course: unknown) {
  if (!form.startTime || !form.endTime) return false
  // 检查课程是否与请假时间冲突
  const courseDate = dayjs(course.date)
  return courseDate.isBetween(form.startTime, form.endTime, 'day', '[]')
}

function handleExceed() {
  ElMessage.warning('最多只能上传5个文件')
}

   
function beforeRemove(file: unknown) {
  return ElMessageBox.confirm(`确定删除 ${file.name}？`)
}

async function submitForm() {
  const valid = await formRef.value.validate()
  if (!valid) return
  
  // 检查附件
  if (attachmentRequired.value && form.attachments.length === 0) {
    ElMessage.error('请上传必要的证明材料')
    return
  }
  
  // 检查余额
  if (currentBalance.value && form.leaveDays > currentBalance.value.remainingQuota) {
    ElMessage.error('假期余额不足，无法提交申请')
    return
  }
  
  try {
    loading.value = true
    
    // 准备提交数据
    const submitData = {
      ...form,
      employeeId: userStore.userInfo.employeeId,
      employeeName: userStore.userInfo.name,
      department: userStore.userInfo.department,
      employeeType: userStore.userInfo.employeeType,
      applyTime: new Date().toISOString()
    }
    
    // 上传附件
    if (form.attachments.length > 0) {
      const fileIds = await uploadAttachments()
      submitData.attachmentIds = fileIds
    }
    
    // 启动流程
    const result = await workflowApi.startProcess({
      processKey: 'leave_approval',
      businessKey: `LEAVE_${Date.now()}`,
      variables: submitData
    })
    
    ElMessage.success('请假申请已提交')
    
    // 跳转到申请详情页
    setTimeout(() => {
      window.location.href = `/workflow/instance/${result.instanceId}`
    }, 1500)
    
  } catch (__error) {
    ElMessage.error('提交申请失败')
  } finally {
    loading.value = false
  }
}

async function uploadAttachments() {
  // 实际项目中应该实现文件上传逻辑
  return form.attachments.map((file, index) => `file_${index}`)
}

function saveAsDraft() {
  // 保存草稿功能
  const draft = {
    ...form,
    savedAt: new Date().toISOString()
  }
  localStorage.setItem('leave_application_draft', JSON.stringify(draft))
  ElMessage.success('草稿已保存')
}

function resetForm() {
  formRef.value.resetFields()
  form.attachments = []
  currentBalance.value = null
}

function viewHistory() {
  // 跳转到申请记录页面
  window.location.href = '/attendance/leave-history'
}

// 生命周期
onMounted(async () => {
  // 加载草稿
  const draft = localStorage.getItem('leave_application_draft')
  if (draft) {
    const draftData = JSON.parse(draft)
    Object.assign(form, draftData)
    ElMessage.info('已加载上次保存的草稿')
  }
  
  // 加载同事列表
  if (userStore.userInfo?.department) {
    try {
      colleagueList.value = await attendanceApi.getDepartmentEmployees(
        userStore.userInfo.department
      )
    } catch (__error) {
      }
  }
})
</script>

<style lang="scss" scoped>
.leave-application {
  padding: 20px;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
    }
  }
  
  .form-card {
    .el-form {
      max-width: 1000px;
      margin: 0 auto;
    }
    
    .option-desc {
      float: right;
      color: #999;
      font-size: 12px;
    }
    
    .form-tip {
      margin-top: 5px;
      color: #999;
      font-size: 12px;
      
      &.text-danger {
        color: #f56c6c;
      }
    }
    
    .switch-label {
      margin-left: 10px;
      color: #606266;
    }
    
    .attachment-upload {
      width: 100%;
      
      .el-upload__tip {
        margin-top: 7px;
        
        .required-tip {
          color: #f56c6c;
        }
      }
    }
    
    .approval-flow {
      display: flex;
      align-items: center;
      padding: 20px;
      background: #f5f7fa;
      border-radius: 4px;
      
      .flow-node {
        display: flex;
        align-items: center;
        margin-right: 20px;
        
        &.active {
          .node-icon {
            background: #409eff;
            color: #fff;
          }
        }
        
        .node-icon {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: #e4e7ed;
          color: #909399;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 10px;
        }
        
        .node-info {
          .node-title {
            font-weight: 500;
            margin-bottom: 4px;
          }
          
          .node-desc {
            font-size: 12px;
            color: #909399;
          }
        }
        
        .flow-arrow {
          margin-left: 10px;
          color: #c0c4cc;
        }
      }
    }
  }
}

.course-schedule {
  .el-table {
    .el-tag {
      margin-right: 5px;
    }
  }
}

.text-warning {
  color: #e6a23c;
}

.text-danger {
  color: #f56c6c;
}
</style>