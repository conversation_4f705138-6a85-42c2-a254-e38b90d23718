<template>
  <div class="dingtalk-integration">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>钉钉考勤集成</h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleSyncNow" :disabled="!isConfigured">
          <el-icon><Refresh /></el-icon>
          立即同步
        </el-button>
      </div>
    </div>

    <!-- 配置状态 -->
    <el-card class="config-status-card">
      <template #header>
        <div class="card-header">
          <h3>集成配置状态</h3>
          <el-tag :type="isConfigured ? 'success' : 'warning'">
            {{ isConfigured ? '已配置' : '未配置' }}
          </el-tag>
        </div>
      </template>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-descriptions :column="1" border>
            <el-descriptions-item label="企业名称">
              {{ configInfo.corpName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="企业ID">
              {{ configInfo.corpId || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="AppKey">
              {{ configInfo.appKey ? '***' + configInfo.appKey.slice(-4) : '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="授权状态">
              <el-tag :type="configInfo.authStatus === 'authorized' ? 'success' : 'danger'" size="small">
                {{ configInfo.authStatus === 'authorized' ? '已授权' : '未授权' }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-col>
        <el-col :span="12">
          <el-descriptions :column="1" border>
            <el-descriptions-item label="最后同步时间">
              {{ configInfo.lastSyncTime || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="同步周期">
              {{ syncCycleMap[configInfo.syncCycle] || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="同步范围">
              {{ configInfo.syncScope || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="自动同步">
              <el-switch v-model="configInfo.autoSync" @change="handleAutoSyncChange"  />
            </el-descriptions-item>
          </el-descriptions>
        </el-col>
      </el-row>

      <div class="config-actions">
        <el-button type="primary" @click="showConfigDialog = true">
          <el-icon><Setting /></el-icon>
          配置设置
        </el-button>
        <el-button @click="handleTestConnection" :disabled="!isConfigured">
          <el-icon><Connection /></el-icon>
          测试连接
        </el-button>
        <el-button @click="handleReauthorize" :disabled="!isConfigured">
          <el-icon><RefreshRight /></el-icon>
          重新授权
        </el-button>
      </div>
    </el-card>

    <!-- 同步规则 -->
    <el-card class="sync-rules-card">
      <template #header>
        <div class="card-header">
          <h3>同步规则设置</h3>
          <el-button type="primary" size="small" @click="showAddRule = true">
            <el-icon><Plus /></el-icon>
            添加规则
          </el-button>
        </div>
      </template>

      <el-table :data="syncRules" v-loading="loading">
        <el-table-column prop="ruleName" label="规则名称"  />
        <el-table-column prop="dingDepartment" label="钉钉部门"  />
        <el-table-column prop="hrDepartment" label="HR部门"  />
        <el-table-column prop="syncFields" label="同步字段" width="200">
          <template #default="{ row }">
            <el-tag v-for="field in row.syncFields" :key="field" size="small" class="mr-1">
              {{ fieldMap[field] }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="enabled" label="状态" width="80">
          <template #default="{ row }">
            <el-switch v-model="row.enabled" @change="handleRuleStatusChange(row)"  />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleEditRule(row)">编辑</el-button>
            <el-button link type="danger" @click="handleDeleteRule(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 同步记录 -->
    <el-card class="sync-history-card">
      <template #header>
        <div class="card-header">
          <h3>同步历史记录</h3>
          <el-space>
            <el-select v-model="historyFilter.status" placeholder="同步状态" clearable size="small">
              <el-option label="全部" value=""  />
              <el-option label="成功" value="success"  />
              <el-option label="失败" value="failed"  />
              <el-option label="部分成功" value="partial"  />
            </el-select>
            <el-date-picker
              v-model="historyFilter.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
             />
          </el-space>
        </div>
      </template>

      <el-table :data="syncHistory">
        <el-table-column prop="syncId" label="同步ID" width="180"  />
        <el-table-column prop="syncTime" label="同步时间" width="160"  />
        <el-table-column prop="syncType" label="同步类型" width="100">
          <template #default="{ row }">
            <el-tag size="small">{{ syncTypeMap[row.syncType] }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="department" label="部门"  />
        <el-table-column prop="totalCount" label="总人数" width="80"  />
        <el-table-column prop="successCount" label="成功" width="70">
          <template #default="{ row }">
            <el-text type="success">{{ row.successCount }}</el-text>
          </template>
        </el-table-column>
        <el-table-column prop="failedCount" label="失败" width="70">
          <template #default="{ row }">
            <el-text type="danger">{{ row.failedCount }}</el-text>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="90">
          <template #default="{ row }">
            <el-tag :type="getSyncStatusType(row.status)" size="small">
              {{ syncStatusMap[row.status] }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleViewDetail(row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="fetchSyncHistory"
        @current-change="fetchSyncHistory"
       />
    </el-card>

    <!-- 配置对话框 -->
    <el-dialog v-model="showConfigDialog" title="钉钉集成配置" width="600px">
      <el-form :model="configForm" :rules="configRules" ref="configFormRef" label-width="120px">
        <el-form-item label="企业ID" prop="corpId">
          <el-input v-model="configForm.corpId" placeholder="请输入钉钉企业ID"   />
        </el-form-item>
        <el-form-item label="AppKey" prop="appKey">
          <el-input v-model="configForm.appKey" placeholder="请输入应用AppKey"   />
        </el-form-item>
        <el-form-item label="AppSecret" prop="appSecret">
          <el-input v-model="configForm.appSecret" type="password" placeholder="请输入应用AppSecret" show-password   />
        </el-form-item>
        <el-form-item label="同步周期" prop="syncCycle">
          <el-select v-model="configForm.syncCycle" placeholder="请选择同步周期">
            <el-option label="每小时" value="hourly"  />
            <el-option label="每天" value="daily"  />
            <el-option label="每周" value="weekly"  />
            <el-option label="每月" value="monthly"  />
          </el-select>
        </el-form-item>
        <el-form-item label="同步时间" prop="syncTime" v-if="configForm.syncCycle !== 'hourly'">
          <el-time-picker
            v-model="configForm.syncTime"
            placeholder="选择时间"
            format="HH:mm"
           />
        </el-form-item>
        <el-form-item label="同步范围" prop="syncScope">
          <el-checkbox-group v-model="configForm.syncScope">
            <el-checkbox label="attendance">考勤数据</el-checkbox>
            <el-checkbox label="leave">请假数据</el-checkbox>
            <el-checkbox label="overtime">加班数据</el-checkbox>
            <el-checkbox label="fieldWork">外勤数据</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showConfigDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSaveConfig">保存配置</el-button>
      </template>
    </el-dialog>

    <!-- 添加规则对话框 -->
    <el-dialog v-model="showAddRule" :title="editingRule ? '编辑同步规则' : '添加同步规则'" width="600px">
      <el-form :model="ruleForm" :rules="ruleRules" ref="ruleFormRef" label-width="100px">
        <el-form-item label="规则名称" prop="ruleName">
          <el-input v-model="ruleForm.ruleName" placeholder="请输入规则名称"   />
        </el-form-item>
        <el-form-item label="钉钉部门" prop="dingDepartmentId">
          <el-tree-select
            v-model="ruleForm.dingDepartmentId"
            :data="dingDepartmentTree"
            :props="{ label: 'name', value: 'id' }"
            placeholder="请选择钉钉部门"
            check-strictly
           />
        </el-form-item>
        <el-form-item label="HR部门" prop="hrDepartmentId">
          <el-tree-select
            v-model="ruleForm.hrDepartmentId"
            :data="hrDepartmentTree"
            :props="{ label: 'name', value: 'id' }"
            placeholder="请选择HR部门"
            check-strictly
           />
        </el-form-item>
        <el-form-item label="同步字段" prop="syncFields">
          <el-checkbox-group v-model="ruleForm.syncFields">
            <el-checkbox label="checkIn">打卡记录</el-checkbox>
            <el-checkbox label="leave">请假记录</el-checkbox>
            <el-checkbox label="overtime">加班记录</el-checkbox>
            <el-checkbox label="fieldWork">外勤记录</el-checkbox>
            <el-checkbox label="schedule">排班信息</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="映射规则">
          <el-button type="primary" size="small" @click="showFieldMapping = true">配置字段映射</el-button>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddRule = false">取消</el-button>
        <el-button type="primary" @click="handleSaveRule">保存规则</el-button>
      </template>
    </el-dialog>

    <!-- 同步详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="同步详情" width="800px">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="基本信息" name="basic">
          <el-descriptions :column="2" border v-if="currentSync">
            <el-descriptions-item label="同步ID">{{ currentSync.syncId }}</el-descriptions-item>
            <el-descriptions-item label="同步时间">{{ currentSync.syncTime }}</el-descriptions-item>
            <el-descriptions-item label="同步类型">{{ syncTypeMap[currentSync.syncType] }}</el-descriptions-item>
            <el-descriptions-item label="同步状态">
              <el-tag :type="getSyncStatusType(currentSync.status)">
                {{ syncStatusMap[currentSync.status] }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="总记录数">{{ currentSync.totalCount }}</el-descriptions-item>
            <el-descriptions-item label="成功/失败">
              <el-text type="success">{{ currentSync.successCount }}</el-text> / 
              <el-text type="danger">{{ currentSync.failedCount }}</el-text>
            </el-descriptions-item>
            <el-descriptions-item label="耗时" :span="2">{{ currentSync.duration }}秒</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        
        <el-tab-pane label="成功记录" name="success">
          <el-table :data="currentSync?.successRecords || []" max-height="400">
            <el-table-column prop="employeeNo" label="员工工号" width="120"  />
            <el-table-column prop="employeeName" label="员工姓名" width="120"  />
            <el-table-column prop="recordType" label="记录类型" width="100"  />
            <el-table-column prop="recordTime" label="记录时间"  />
            <el-table-column prop="syncTime" label="同步时间"  />
          </el-table>
        </el-tab-pane>

        <el-tab-pane label="失败记录" name="failed">
          <el-table :data="currentSync?.failedRecords || []" max-height="400">
            <el-table-column prop="employeeNo" label="员工工号" width="120"  />
            <el-table-column prop="employeeName" label="员工姓名" width="120"  />
            <el-table-column prop="recordType" label="记录类型" width="100"  />
            <el-table-column prop="errorReason" label="失败原因"  />
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Refresh, 
  Setting, 
  Connection,
  RefreshRight,
  Plus
} from '@element-plus/icons-vue'

// 数据定义
const loading = ref(false)
const showConfigDialog = ref(false)
const showAddRule = ref(false)
const showDetailDialog = ref(false)
const showFieldMapping = ref(false)
const editingRule = ref(null)
const currentSync = ref(null)
const activeTab = ref('basic')
const configFormRef = ref()
const ruleFormRef = ref()

// 配置信息
const configInfo = reactive({
  corpName: '杭州科技大学',
  corpId: 'ding123456',
  appKey: 'dingoak123456789',
  authStatus: 'authorized',
  lastSyncTime: '2025-01-21 08:00:00',
  syncCycle: 'daily',
  syncScope: '全部部门',
  autoSync: true
})

// 同步规则
const syncRules = ref([
  {
    id: 1,
    ruleName: '教学部门同步',
    dingDepartment: '教学部',
    hrDepartment: '教学部',
    syncFields: ['checkIn', 'leave', 'overtime'],
    enabled: true
  },
  {
    id: 2,
    ruleName: '行政部门同步',
    dingDepartment: '行政部',
    hrDepartment: '行政管理部',
    syncFields: ['checkIn', 'leave'],
    enabled: true
  }
])

// 同步历史
const syncHistory = ref([
  {
    syncId: 'SYNC20250121080000',
    syncTime: '2025-01-21 08:00:00',
    syncType: 'auto',
    department: '全部部门',
    totalCount: 500,
    successCount: 498,
    failedCount: 2,
    status: 'partial',
    duration: 45
  },
  {
    syncId: 'SYNC20250120200000',
    syncTime: '2025-01-20 20:00:00',
    syncType: 'auto',
    department: '全部部门',
    totalCount: 500,
    successCount: 500,
    failedCount: 0,
    status: 'success',
    duration: 38
  }
])

// 分页参数
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 筛选条件
const historyFilter = reactive({
  status: '',
  dateRange: []
})

// 配置表单
const configForm = reactive({
  corpId: '',
  appKey: '',
  appSecret: '',
  syncCycle: 'daily',
  syncTime: '20:00',
  syncScope: ['attendance', 'leave', 'overtime']
})

// 规则表单
const ruleForm = reactive({
  ruleName: '',
  dingDepartmentId: '',
  hrDepartmentId: '',
  syncFields: ['checkIn']
})

// 部门树数据
const dingDepartmentTree = ref([
  {
    id: '1',
    name: 'HrHr杭州科技大学',
    children: [
      { id: '11', name: '教学部' },
      { id: '12', name: '行政部' },
      { id: '13', name: '后勤部' }
    ]
  }
])

const hrDepartmentTree = ref([
  {
    id: '1',
    name: '杭州科技大学',
    children: [
      { id: '11', name: '教学部' },
      { id: '12', name: '行政管理部' },
      { id: '13', name: '后勤保障部' }
    ]
  }
])

// 表单验证规则
const configRules = {
  corpId: [
    { required: true, message: '请输入企业ID', trigger: 'blur' }
  ],
  appKey: [
    { required: true, message: '请输入AppKey', trigger: 'blur' }
  ],
  appSecret: [
    { required: true, message: '请输入AppSecret', trigger: 'blur' }
  ],
  syncCycle: [
    { required: true, message: '请选择同步周期', trigger: 'change' }
  ]
}

const ruleRules = {
  ruleName: [
    { required: true, message: '请输入规则名称', trigger: 'blur' }
  ],
  dingDepartmentId: [
    { required: true, message: '请选择钉钉部门', trigger: 'change' }
  ],
  hrDepartmentId: [
    { required: true, message: '请选择HR部门', trigger: 'change' }
  ],
  syncFields: [
    { required: true, message: '请选择同步字段', trigger: 'change' }
  ]
}

// 枚举映射
const syncCycleMap: Record<string, string> = {
  hourly: '每小时',
  daily: '每天',
  weekly: '每周',
  monthly: '每月'
}

const syncTypeMap: Record<string, string> = {
  auto: '自动同步',
  manual: '手动同步'
}

const syncStatusMap: Record<string, string> = {
  success: '成功',
  failed: '失败',
  partial: '部分成功',
  running: '同步中'
}

const fieldMap: Record<string, string> = {
  checkIn: '打卡记录',
  leave: '请假记录',
  overtime: '加班记录',
  fieldWork: '外勤记录',
  schedule: '排班信息'
}

// 计算属性
const isConfigured = computed(() => {
  return configInfo.corpId && configInfo.appKey && configInfo.authStatus === 'authorized'
})

// 方法定义
const handleSyncNow = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要立即同步钉钉考勤数据吗？',
      '提示',
      { type: 'warning' }
    )
    
    loading.value = true
    // 模拟同步过程
    setTimeout(() => {
      loading.value = false
      ElMessage.success('同步任务已启动')
      fetchSyncHistory()
    }, 2000)
  } catch {
    // 用户取消
  }
}

const handleAutoSyncChange = (value: boolean) => {
  ElMessage.success(value ? '已开启自动同步' : '已关闭自动同步')
}

const handleTestConnection = () => {
  ElMessage.info('正在测试连接...')
  setTimeout(() => {
    ElMessage.success('连接测试成功')
  }, 1500)
}

const handleReauthorize = () => {
  ElMessage.info('正在跳转到钉钉授权页面...')
  // 实际跳转到授权页面
}

const handleSaveConfig = async () => {
  const valid = await configFormRef.value.validate()
  if (!valid) return
  
  showConfigDialog.value = false
  ElMessage.success('配置保存成功')
}

   
const handleEditRule = (rule: unknown) => {
  editingRule.value = rule
  Object.assign(ruleForm, rule)
  showAddRule.value = true
}

   
const handleDeleteRule = async (rule: unknown) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除规则"${rule.ruleName}"吗？`,
      '提示',
      { type: 'warning' }
    )
    
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

   
const handleRuleStatusChange = (rule: unknown) => {
  ElMessage.success(rule.enabled ? '规则已启用' : '规则已禁用')
}

const handleSaveRule = async () => {
  const valid = await ruleFormRef.value.validate()
  if (!valid) return
  
  showAddRule.value = false
  ElMessage.success(editingRule.value ? '规则更新成功' : '规则添加成功')
}

   
const handleViewDetail = (sync: unknown) => {
  currentSync.value = {
    ...sync,
    successRecords: [
      {
        employeeNo: 'EMP001',
        employeeName: '张三',
        recordType: '上班打卡',
        recordTime: '2025-01-21 08:30:00',
        syncTime: '2025-01-21 08:00:15'
      }
    ],
    failedRecords: sync.failedCount > 0 ? [
      {
        employeeNo: 'EMP100',
        employeeName: '李四',
        recordType: '下班打卡',
        errorReason: '员工工号在HR系统中不存在'
      }
    ] : []
  }
  showDetailDialog.value = true
}

const getSyncStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    success: 'success',
    failed: 'danger',
    partial: 'warning',
    running: 'primary'
  }
  return typeMap[status] || 'info'
}

const fetchSyncHistory = () => {
  // 获取同步历史
}

// 生命周期
onMounted(() => {
  fetchSyncHistory()
})
</script>

<style scoped>
.dingtalk-integration {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
}

.config-status-card,
.sync-rules-card,
.sync-history-card {
  margin-bottom: 20px;
}

.config-actions {
  margin-top: 20px;
  text-align: center;
}

.config-actions .el-button {
  margin: 0 5px;
}

.mr-1 {
  margin-right: 4px;
}

:deep(.el-descriptions__label) {
  width: 120px;
}
</style>