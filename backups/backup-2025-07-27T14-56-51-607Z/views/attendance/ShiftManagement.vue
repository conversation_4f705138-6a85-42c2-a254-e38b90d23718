<template>
  <div class="shift-management">
    <!-- 班次管理 -->
    <el-card shadow="never" class="mb-4">
      <template #header>
        <div class="card-header">
          <span>班次设置</span>
          <el-button type="primary" size="small" @click="handleAddShift">
            <el-icon><Plus /></el-icon>
            新增班次
          </el-button>
        </div>
      </template>
      
      <div class="shift-list">
        <div
          v-for="shift in shiftList"
          :key="shift.id"
          class="shift-item"
          :style="{ borderColor: shift.color }"
        >
          <div class="shift-color" :style="{ backgroundColor: shift.color }"></div>
          <div class="shift-info">
            <div class="shift-name">{{ shift.name }}</div>
            <div class="shift-time">{{ shift.startTime }} - {{ shift.endTime }}</div>
          </div>
          <div class="shift-actions">
            <el-button link type="primary" size="small" @click="handleEditShift(shift)">
              编辑
            </el-button>
            <el-button link type="danger" size="small" @click="handleDeleteShift(shift)">
              删除
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 排班日历 -->
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span>排班日历</span>
          <div class="header-controls">
            <el-select v-model="selectedDepartment" placeholder="选择部门" size="small" style="width: 200px; margin-right: 10px">
              <el-option
                v-for="dept in departmentList"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
               />
            </el-select>
            <el-date-picker
              v-model="selectedMonth"
              type="month"
              placeholder="选择月份"
              size="small"
              @change="handleMonthChange"
             />
            <el-button-group style="margin-left: 10px">
              <el-button size="small" @click="handlePrevMonth">
                <el-icon><ArrowLeft /></el-icon>
              </el-button>
              <el-button size="small" @click="handleNextMonth">
                <el-icon><ArrowRight /></el-icon>
              </el-button>
            </el-button-group>
            <el-button type="primary" size="small" style="margin-left: 10px" @click="handleBatchSchedule">
              批量排班
            </el-button>
            <el-button size="small" @click="handleImportTemplate">导入模板</el-button>
            <el-button size="small" @click="handleExportSchedule">导出排班表</el-button>
          </div>
        </div>
      </template>

      <!-- 员工排班表 -->
      <div class="schedule-table" v-loading="loading">
        <div class="schedule-header">
          <div class="employee-column">员工</div>
          <div class="day-columns">
            <div
              v-for="day in calendarDays"
              :key="day.date"
              class="day-column"
              :class="{ weekend: day.isWeekend, today: day.isToday }"
            >
              <div class="day-date">{{ day.day }}</div>
              <div class="day-week">{{ day.weekDay }}</div>
            </div>
          </div>
        </div>
        <div class="schedule-body">
          <div v-for="employee in employeeSchedules" :key="employee.id" class="employee-row">
            <div class="employee-info">
              <div class="employee-name">{{ employee.name }}</div>
              <div class="employee-dept">{{ employee.department }}</div>
            </div>
            <div class="schedule-cells">
              <div
                v-for="(schedule, index) in employee.schedules"
                :key="index"
                class="schedule-cell"
                :class="{ weekend: calendarDays[index].isWeekend }"
                @click="handleCellClick(employee, index)"
                @dragover.prevent
                @drop="handleDrop(employee, index)"
              >
                <div
                  v-if="schedule.shift"
                  class="shift-block"
                  :style="{ backgroundColor: schedule.shift.color }"
                  draggable="true"
                  @dragstart="handleDragStart(schedule.shift, employee, index)"
                >
                  {{ schedule.shift.name }}
                </div>
                <div v-else class="empty-shift">休</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="schedule-statistics">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-label">本月应出勤</div>
              <div class="stat-value">{{ statistics.totalDays }}天</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-label">已排班</div>
              <div class="stat-value">{{ statistics.scheduledDays }}天</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-label">未排班</div>
              <div class="stat-value">{{ statistics.unscheduledDays }}天</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-label">排班完成率</div>
              <div class="stat-value">{{ statistics.completionRate }}%</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 班次编辑对话框 -->
    <el-dialog
      v-model="shiftDialog"
      :title="shiftDialogTitle"
      width="500px"
    >
      <el-form ref="shiftFormRef" :model="shiftForm" :rules="shiftRules" label-width="100px">
        <el-form-item label="班次名称" prop="name">
          <el-input v-model="shiftForm.name" placeholder="请输入班次名称"   />
        </el-form-item>
        <el-form-item label="班次代码" prop="code">
          <el-input v-model="shiftForm.code" placeholder="请输入班次代码"   />
        </el-form-item>
        <el-form-item label="开始时间" prop="startTime">
          <el-time-picker
            v-model="shiftForm.startTime"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="选择开始时间"
           />
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime">
          <el-time-picker
            v-model="shiftForm.endTime"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="选择结束时间"
           />
        </el-form-item>
        <el-form-item label="班次颜色" prop="color">
          <el-color-picker v-model="shiftForm.color"  />
        </el-form-item>
        <el-form-item label="休息时间">
          <el-time-picker
            v-model="shiftForm.breakTime"
            is-range
            format="HH:mm"
            value-format="HH:mm"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
           />
        </el-form-item>
        <el-form-item label="班次说明">
          <el-input
            v-model="shiftForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入班次说明"
            />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="shiftDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSaveShift">保存</el-button>
      </template>
    </el-dialog>

    <!-- 批量排班对话框 -->
    <el-dialog
      v-model="batchDialog"
      title="批量排班"
      width="600px"
    >
      <el-form ref="batchFormRef" :model="batchForm" label-width="100px">
        <el-form-item label="排班方式">
          <el-radio-group v-model="batchForm.type">
            <el-radio label="cycle">循环排班</el-radio>
            <el-radio label="copy">复制排班</el-radio>
            <el-radio label="template">应用模板</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 循环排班 -->
        <template v-if="batchForm.type === 'cycle'">
          <el-form-item label="循环周期">
            <el-input-number v-model="batchForm.cycleDays" :min="1" :max="30"   />
            <span style="margin-left: 10px">天</span>
          </el-form-item>
          <el-form-item label="班次顺序">
            <div class="cycle-shifts">
              <div v-for="(shift, index) in batchForm.cycleShifts" :key="index" class="cycle-shift-item">
                <span>第{{ index + 1 }}天：</span>
                <el-select v-model="shift.shiftId" placeholder="选择班次" size="small">
                  <el-option
                    v-for="s in shiftList"
                    :key="s.id"
                    :label="s.name"
                    :value="s.id"
                   />
                </el-select>
                <el-button
                  v-if="index === batchForm.cycleShifts.length - 1"
                  type="primary"
                  size="small"
                  :icon="Plus"
                  @click="handleAddCycleShift"
                  />
                <el-button
                  v-if="batchForm.cycleShifts.length > 1"
                  type="danger"
                  size="small"
                  :icon="Delete"
                  @click="handleRemoveCycleShift(index)"
                />
              </div>
            </div>
          </el-form-item>
        </template>

        <!-- 复制排班 -->
        <template v-if="batchForm.type === 'copy'">
          <el-form-item label="复制来源">
            <el-date-picker
              v-model="batchForm.copySource"
              type="week"
              format="第 ww 周"
              placeholder="选择要复制的周"
             />
          </el-form-item>
        </template>

        <!-- 应用模板 -->
        <template v-if="batchForm.type === 'template'">
          <el-form-item label="选择模板">
            <el-select v-model="batchForm.templateId" placeholder="请选择排班模板">
              <el-option
                v-for="template in templateList"
                :key="template.id"
                :label="template.name"
                :value="template.id"
               />
            </el-select>
          </el-form-item>
        </template>

        <el-form-item label="应用范围">
          <el-checkbox-group v-model="batchForm.employees">
            <el-checkbox
              v-for="emp in employeeSchedules"
              :key="emp.id"
              :label="emp.id"
            >
              {{ emp.name }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="时间范围">
          <el-date-picker
            v-model="batchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
           />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="batchDialog = false">取消</el-button>
        <el-button type="primary" @click="handleBatchSave">确定</el-button>
      </template>
    </el-dialog>

    <!-- 单元格编辑弹出框 -->
    <el-popover
      v-model:visible="cellPopover"
      placement="bottom"
      width="200"
      trigger="click"
    >
      <div class="shift-selector">
        <div
          v-for="shift in shiftList"
          :key="shift.id"
          class="shift-option"
          @click="handleSelectShift(shift)"
        >
          <div class="shift-color" :style="{ backgroundColor: shift.color }"></div>
          <div class="shift-name">{{ shift.name }}</div>
        </div>
        <div class="shift-option" @click="handleSelectShift(null)">
          <div class="shift-color" style="background-color: #f5f5f5"></div>
          <div class="shift-name">休息</div>
        </div>
      </div>
      <template #reference>
        <div ref="cellPopoverRef" style="display: none"></div>
      </template>
    </el-popover>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Delete, ArrowLeft, ArrowRight } from '@element-plus/icons-vue'

// 班次列表
const shiftList = ref([
  {
    id: 1,
    name: 'HrHr早班',
    code: 'A',
    startTime: '08:00',
    endTime: '16:00',
    color: '#409EFF',
    breakTime: ['12:00', '13:00'],
    description: '早班班次'
  },
  {
    id: 2,
    name: '中班',
    code: 'B',
    startTime: '16:00',
    endTime: '00:00',
    color: '#67C23A',
    breakTime: ['19:00', '20:00'],
    description: '中班班次'
  },
  {
    id: 3,
    name: '晚班',
    code: 'C',
    startTime: '00:00',
    endTime: '08:00',
    color: '#E6A23C',
    breakTime: ['03:00', '04:00'],
    description: '晚班班次'
  }
])

// 部门列表
const departmentList = ref([
  { id: 1, name: '教学部门' },
  { id: 2, name: '行政部门' },
  { id: 3, name: '后勤部门' }
])

// 模板列表
const templateList = ref([
  { id: 1, name: '三班倒模板' },
  { id: 2, name: '两班倒模板' },
  { id: 3, name: '行政班模板' }
])

// 员工排班数据
const employeeSchedules = ref([
  {
    id: 1,
    name: '张三',
    department: '教学部门',
    schedules: Array(31).fill(null).map(() => ({ shift: null }))
  },
  {
    id: 2,
    name: '李四',
    department: '教学部门',
    schedules: Array(31).fill(null).map(() => ({ shift: null }))
  },
  {
    id: 3,
    name: '王五',
    department: '行政部门',
    schedules: Array(31).fill(null).map(() => ({ shift: null }))
  }
])

// 选中的部门和月份
const selectedDepartment = ref(1)
const selectedMonth = ref(new Date())
const loading = ref(false)

// 日历天数
const calendarDays = computed(() => {
  const year = selectedMonth.value.getFullYear()
  const month = selectedMonth.value.getMonth()
  const daysInMonth = new Date(year, month + 1, 0).getDate()
  const today = new Date()
  
  return Array.from({ length: daysInMonth }, (_, i) => {
    const date = new Date(year, month, i + 1)
    const weekDay = ['日', '一', '二', '三', '四', '五', '六'][date.getDay()]
    return {
      date: date.toISOString().split('T')[0],
      day: i + 1,
      weekDay,
      isWeekend: date.getDay() === 0 || date.getDay() === 6,
      isToday: date.toDateString() === today.toDateString()
    }
  })
})

// 统计信息
const statistics = computed(() => {
  const totalDays = calendarDays.value.length * employeeSchedules.value.length
  const scheduledDays = employeeSchedules.value.reduce((sum, emp) => {
    return sum + emp.schedules.filter(s => s.shift).length
  }, 0)
  const unscheduledDays = totalDays - scheduledDays
  const completionRate = totalDays > 0 ? Math.round((scheduledDays / totalDays) * 100) : 0

  return {
    totalDays,
    scheduledDays,
    unscheduledDays,
    completionRate
  }
})

// 班次对话框
const shiftDialog = ref(false)
const shiftDialogTitle = ref('新增班次')
const shiftFormRef = ref()
const shiftForm = reactive({
  name: '',
  code: '',
  startTime: '',
  endTime: '',
  color: '#409EFF',
  breakTime: [],
  description: ''
})

const shiftRules = reactive({
  name: [{ required: true, message: '请输入班次名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入班次代码', trigger: 'blur' }],
  startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
  endTime: [{ required: true, message: '请选择结束时间', trigger: 'change' }],
  color: [{ required: true, message: '请选择班次颜色', trigger: 'change' }]
})

// 批量排班对话框
const batchDialog = ref(false)
const batchFormRef = ref()
const batchForm = reactive({
  type: 'cycle',
  cycleDays: 3,
  cycleShifts: [{ shiftId: null }],
  copySource: null,
  templateId: null,
  employees: [],
  dateRange: []
})

// 单元格编辑
const cellPopover = ref(false)
const cellPopoverRef = ref()
const currentEditCell = ref<unknown>(null)

// 拖拽数据

// 新增班次
const handleAddShift = () => {
  shiftDialogTitle.value = '新增班次'
  Object.assign(shiftForm, {
    name: '',
    code: '',
    startTime: '',
    endTime: '',
    color: '#409EFF',
    breakTime: [],
    description: ''
  })
  shiftDialog.value = true
}

// 编辑班次
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleEditShift = (shift: unknown) => {
  shiftDialogTitle.value = '编辑班次'
  Object.assign(shiftForm, shift)
  shiftDialog.value = true
}

// 删除班次
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleDeleteShift = async (shift: unknown) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除该班次吗？',
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    const index = shiftList.value.findIndex(s => s.id === shift.id)
    if (index > -1) {
      shiftList.value.splice(index, 1)
    }
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

// 保存班次
const handleSaveShift = async () => {
  const valid = await shiftFormRef.value.validate()
  if (!valid) return

  ElMessage.success('保存成功')
  shiftDialog.value = false
}

// 月份切换
const handleMonthChange = () => {
  loadScheduleData()
}

const handlePrevMonth = () => {
  const date = new Date(selectedMonth.value)
  date.setMonth(date.getMonth() - 1)
  selectedMonth.value = date
  loadScheduleData()
}

const handleNextMonth = () => {
  const date = new Date(selectedMonth.value)
  date.setMonth(date.getMonth() + 1)
  selectedMonth.value = date
  loadScheduleData()
}

// 批量排班
const handleBatchSchedule = () => {
  batchForm.employees = employeeSchedules.value.map(e => e.id)
  batchDialog.value = true
}

// 导入模板
const handleImportTemplate = () => {
  ElMessage.info('导入排班模板')
}

// 导出排班表
const handleExportSchedule = () => {
  ElMessage.success('导出排班表成功')
}

// 单元格点击
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleCellClick = (employee: unknown, dayIndex: number) => {
  currentEditCell.value = { employee, dayIndex }
  cellPopover.value = true
  // 这里应该定位到对应的单元格
}

// 选择班次
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleSelectShift = (shift: unknown) => {
  if (currentEditCell.value) {
    const {employee, dayIndex: _dayIndex} =  currentEditCell.value
    employee.schedules[dayIndex].shift 
    justify-content: space-between;
    align-items: center;
  }

  .header-controls {
    display: flex;
    align-items: center;
  }

  // 班次列表
  .shift-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
  }

  .shift-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-left: 4px solid;
    border-radius: 4px;
    transition: all 0.3s;

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .shift-color {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 10px;
  }

  .shift-info {
    flex: 1;

    .shift-name {
      font-weight: bold;
      margin-bottom: 4px;
    }

    .shift-time {
      font-size: 12px;
      color: #666;
    }
  }

  .shift-actions {
    display: flex;
    gap: 5px;
  }

  // 排班表
  .schedule-table {
    overflow-x: auto;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
  }

  .schedule-header {
    display: flex;
    background-color: #f5f7fa;
    border-bottom: 1px solid #e0e0e0;
  }

  .employee-column {
    width: 150px;
    padding: 10px;
    font-weight: bold;
    border-right: 1px solid #e0e0e0;
    position: sticky;
    left: 0;
    background-color: #f5f7fa;
    z-index: 10;
  }

  .day-columns {
    display: flex;
    flex: 1;
  }

  .day-column {
    flex: 1;
    min-width: 60px;
    text-align: center;
    padding: 5px;
    border-right: 1px solid #e0e0e0;

    &.weekend {
      background-color: #fef0f0;
    }

    &.today {
      background-color: #f0f9ff;
    }

    .day-date {
      font-weight: bold;
      font-size: 14px;
    }

    .day-week {
      font-size: 12px;
      color: #666;
    }
  }

  .schedule-body {
    max-height: 500px;
    overflow-y: auto;
  }

  .employee-row {
    display: flex;
    border-bottom: 1px solid #e0e0e0;

    &:hover {
      background-color: #f5f7fa;
    }
  }

  .employee-info {
    width: 150px;
    padding: 10px;
    border-right: 1px solid #e0e0e0;
    position: sticky;
    left: 0;
    background-color: white;
    z-index: 5;

    .employee-name {
      font-weight: bold;
      margin-bottom: 4px;
    }

    .employee-dept {
      font-size: 12px;
      color: #666;
    }
  }

  .schedule-cells {
    display: flex;
    flex: 1;
  }

  .schedule-cell {
    flex: 1;
    min-width: 60px;
    height: 50px;
    border-right: 1px solid #e0e0e0;
    padding: 5px;
    cursor: pointer;
    transition: background-color 0.3s;

    &:hover {
      background-color: #e6f7ff;
    }

    &.weekend {
      background-color: #fef0f0;
    }
  }

  .shift-block {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    border-radius: 4px;
    cursor: move;
  }

  .empty-shift {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 12px;
  }

  // 统计信息
  .schedule-statistics {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e0e0e0;
  }

  .stat-item {
    text-align: center;

    .stat-label {
      font-size: 14px;
      color: #666;
      margin-bottom: 5px;
    }

    .stat-value {
      font-size: 24px;
      font-weight: bold;
      color: #303133;
    }
  }

  // 班次选择器
  .shift-selector {
    .shift-option {
      display: flex;
      align-items: center;
      padding: 8px;
      cursor: pointer;
      transition: background-color 0.3s;

      &:hover {
        background-color: #f5f7fa;
      }

      .shift-color {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        margin-right: 8px;
      }

      .shift-name {
        font-size: 14px;
      }
    }
  }

  // 循环班次
  .cycle-shifts {
    .cycle-shift-item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      span {
        margin-right: 10px;
      }

      .el-select {
        width: 150px;
        margin-right: 10px;
      }
    }
  }
}
</style>