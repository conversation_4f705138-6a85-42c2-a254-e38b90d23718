<template>
  <div class="virtual-table-demo">
    <h1>虚拟表格演示</h1>
    
    <!-- 基础演示 -->
    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <h3>基础演示 - 10万条数据</h3>
          <el-button type="primary" size="small" @click="generateBasicData(100000)">
            生成10万条
          </el-button>
        </div>
      </template>
      
      <div class="demo-content">
        <VirtualTable
          ref="basicTableRef"
          :data="basicData"
          :columns="basicColumns"
          :height="500"
          row-key="id"
          @sort-change="handleSortChange"
          @row-click="handleRowClick"
        />
        
        <div class="info-panel">
          <el-alert type="info" :closable="false">
            <template #title>
              <span>当前选中行：{{ currentRow ? `ID: ${currentRow.id}, 名称: ${currentRow.name}` : '无' }}</span>
            </template>
          </el-alert>
        </div>
      </div>
    </el-card>
    
    <!-- 员工表格演示 -->
    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <h3>员工表格演示 - 实际业务场景</h3>
          <div class="header-actions">
            <el-input 
              v-model="searchKeyword" 
              placeholder="搜索员工" 
              style="width: 200px"
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-button @click="generateEmployeeData(50000)">生成5万条</el-button>
            <el-button @click="exportTable">导出表格</el-button>
          </div>
        </div>
      </template>
      
      <div class="demo-content">
        <VirtualTable
          ref="employeeTableRef"
          :data="filteredEmployees"
          :columns="employeeColumns"
          :height="600"
          row-key="id"
          :loading="loading"
          stripe
          highlight-current-row
          @column-resize="handleColumnResize"
          @columns-change="handleColumnsChange"
        >
          <!-- 员工信息列 -->
          <template #employee="{ row }">
            <div class="employee-info">
              <el-avatar :size="36" :src="row.avatar">
                {{ row.name.charAt(0) }}
              </el-avatar>
              <div class="info-text">
                <div class="name">{{ row.name }}</div>
                <div class="code">{{ row.code }}</div>
              </div>
            </div>
          </template>
          
          <!-- 状态列 -->
          <template #status="{ value }">
            <el-tag :type="value === '在职' ? 'success' : 'info'" size="small">
              {{ value }}
            </el-tag>
          </template>
          
          <!-- 操作列 -->
          <template #actions="{ row }">
            <el-button text type="primary" size="small" @click="viewEmployee(row)">
              查看
            </el-button>
            <el-button text type="primary" size="small" @click="editEmployee(row)">
              编辑
            </el-button>
            <el-dropdown trigger="click">
              <el-button text type="primary" size="small">
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>调岗</el-dropdown-item>
                  <el-dropdown-item>离职</el-dropdown-item>
                  <el-dropdown-item divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </VirtualTable>
      </div>
    </el-card>
    
    <!-- 固定列演示 -->
    <el-card class="demo-card">
      <template #header>
        <h3>固定列演示</h3>
      </template>
      
      <div class="demo-content">
        <VirtualTable
          :data="fixedColumnData"
          :columns="fixedColumns"
          :height="400"
          row-key="id"
          border
        />
        
        <el-alert type="info" :closable="false" style="margin-top: 16px">
          <template #title>
            <ul class="feature-list">
              <li>左侧固定：ID、姓名列</li>
              <li>右侧固定：操作列</li>
              <li>中间列可横向滚动</li>
            </ul>
          </template>
        </el-alert>
      </div>
    </el-card>
    
    <!-- 性能对比 -->
    <el-card class="demo-card">
      <template #header>
        <h3>性能对比</h3>
      </template>
      
      <div class="demo-content">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="comparison-item">
              <h4>虚拟滚动表格</h4>
              <el-descriptions :column="1" border>
                <el-descriptions-item label="渲染时间">~100ms</el-descriptions-item>
                <el-descriptions-item label="DOM节点数">~50个</el-descriptions-item>
                <el-descriptions-item label="内存占用">~50MB</el-descriptions-item>
                <el-descriptions-item label="滚动帧率">60 FPS</el-descriptions-item>
                <el-descriptions-item label="数据容量">100万+</el-descriptions-item>
              </el-descriptions>
            </div>
          </el-col>
          
          <el-col :span="12">
            <div class="comparison-item">
              <h4>普通表格</h4>
              <el-descriptions :column="1" border>
                <el-descriptions-item label="渲染时间">~10,000ms</el-descriptions-item>
                <el-descriptions-item label="DOM节点数">100,000个</el-descriptions-item>
                <el-descriptions-item label="内存占用">~2GB</el-descriptions-item>
                <el-descriptions-item label="滚动帧率">5-10 FPS</el-descriptions-item>
                <el-descriptions-item label="数据容量"><1万</el-descriptions-item>
              </el-descriptions>
            </div>
          </el-col>
        </el-row>
        
        <div class="performance-chart">
          <h4>渲染性能对比图表</h4>
          <div class="chart-container">
            <div class="chart-item">
              <span class="label">1千条</span>
              <div class="bars">
                <div class="bar virtual" style="width: 5%">50ms</div>
                <div class="bar normal" style="width: 10%">100ms</div>
              </div>
            </div>
            <div class="chart-item">
              <span class="label">1万条</span>
              <div class="bars">
                <div class="bar virtual" style="width: 10%">100ms</div>
                <div class="bar normal" style="width: 100%">1000ms</div>
              </div>
            </div>
            <div class="chart-item">
              <span class="label">10万条</span>
              <div class="bars">
                <div class="bar virtual" style="width: 10%">100ms</div>
                <div class="bar normal" style="width: 100%">10s+</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>
    
    <!-- 高级功能 -->
    <el-card class="demo-card">
      <template #header>
        <h3>高级功能演示</h3>
      </template>
      
      <div class="demo-content">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="排序功能" name="sort">
            <p>点击表头进行升序/降序排序，支持多列排序</p>
            <VirtualTable
              :data="sortData"
              :columns="sortColumns"
              :height="300"
              :default-sort="{ prop: 'age', order: 'asc' }"
            />
          </el-tab-pane>
          
          <el-tab-pane label="列宽调整" name="resize">
            <p>拖拽列边框调整列宽</p>
            <VirtualTable
              :data="resizeData"
              :columns="resizeColumns"
              :height="300"
              enable-column-resize
            />
          </el-tab-pane>
          
          <el-tab-pane label="右键菜单" name="context">
            <p>右键点击行显示上下文菜单</p>
            <VirtualTable
              :data="contextData"
              :columns="contextColumns"
              :height="300"
              enable-context-menu
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, computed, reactive } from 'vue'
import HrVirtualTable from '@/components/common/HrVirtualTable.vue'
import { Search, ArrowDown } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 基础数据
const basicData = ref<any[]>([])
const basicTableRef = ref()
const currentRow = ref<unknown>(null)

// 基础列配置
const basicColumns = [
  { prop: 'id', label: 'ID', width: '80px', sortable: true },
  { prop: 'name', label: '名称', sortable: true },
  { prop: 'description', label: '描述' },
  { prop: 'status', label: '状态', width: '100px' },
  { prop: 'createTime', label: '创建时间', width: '180px', sortable: true },
  { prop: 'updateTime', label: '更新时间', width: '180px' }
]

// 员工数据
const employeeData = ref<any[]>([])
const searchKeyword = ref('')
const loading = ref(false)
const employeeTableRef = ref()

// 员工列配置
const employeeColumns = ref([
  { 
    prop: 'employee', 
    label: '员工信息', 
    width: '200px',
    slot: 'employee',
    resizable: false
  },
  { prop: 'department', label: '部门', width: '120px', sortable: true },
  { prop: 'position', label: '职位', width: '120px' },
  { prop: 'email', label: '邮箱', minWidth: '180px' },
  { prop: 'phone', label: '电话', width: '140px' },
  { prop: 'entryDate', label: '入职日期', width: '120px', sortable: true },
  { 
    prop: 'status', 
    label: '状态', 
    width: '80px',
    slot: 'status',
    align: 'center'
  },
  { 
    prop: 'actions', 
    label: '操作', 
    width: '200px',
    slot: 'actions',
    fixed: 'right',
    resizable: false
  }
])

// 固定列数据
const fixedColumnData = ref<any[]>([])
const fixedColumns = [
  { prop: 'id', label: 'ID', width: '80px', fixed: 'left' },
  { prop: 'name', label: '姓名', width: '120px', fixed: 'left' },
  { prop: 'age', label: '年龄', width: '80px' },
  { prop: 'address', label: '地址', width: '200px' },
  { prop: 'company', label: '公司', width: '150px' },
  { prop: 'department', label: '部门', width: '120px' },
  { prop: 'position', label: '职位', width: '120px' },
  { prop: 'salary', label: '基本工资', width: '120px' },
  { prop: 'bonus', label: '奖金', width: '120px' },
  { prop: 'total', label: '总计', width: '120px' },
  { prop: 'actions', label: '操作', width: '120px', fixed: 'right' }
]

// 功能演示数据
const activeTab = ref('sort')
const sortData = ref<any[]>([])
const sortColumns = [
  { prop: 'name', label: '姓名', sortable: true },
  { prop: 'age', label: '年龄', sortable: true },
  { prop: 'score', label: '分数', sortable: true },
  { prop: 'rank', label: '排名', sortable: true }
]

const resizeData = ref<any[]>([])
const resizeColumns = [
  { prop: 'id', label: 'ID', width: '80px' },
  { prop: 'title', label: '标题', minWidth: '200px' },
  { prop: 'content', label: '内容', minWidth: '300px' },
  { prop: 'author', label: '作者', width: '120px' }
]

const contextData = ref<any[]>([])
const contextColumns = [
  { prop: 'file', label: '文件名' },
  { prop: 'size', label: '大小', width: '120px' },
  { prop: 'type', label: '类型', width: '120px' },
  { prop: 'modifiedTime', label: '修改时间', width: '180px' }
]

// 计算属性
const filteredEmployees = computed(() => {
  if (!searchKeyword.value) return employeeData.value
  
  const keyword = searchKeyword.value.toLowerCase()
  return employeeData.value.filter(emp => 
    emp.name.toLowerCase().includes(keyword) ||
    emp.code.toLowerCase().includes(keyword) ||
    emp.department.toLowerCase().includes(keyword) ||
    emp.position.toLowerCase().includes(keyword)
  )
})

// 生成基础数据
const generateBasicData = (count: number) => {
  basicData.value = Array.from({ length: count }, (_, i) => ({
    id: i + 1,
    name: `数据项 ${i + 1}`,
    description: `这是第 ${i + 1} 条数据的详细描述信息，包含了一些测试内容`,
    status: ['已激活', '待处理', '已归档'][Math.floor(Math.random() * 3)],
    createTime: new Date(Date.now() - Math.random() * 86400000 * 365).toLocaleString(),
    updateTime: new Date(Date.now() - Math.random() * 86400000 * 30).toLocaleString()
  }))
}

// 生成员工数据
const generateEmployeeData = (count: number) => {
  loading.value = true
  
  const departments = ['技术部', '产品部', '市场部', '人事部', '财务部', '运营部']
  const positions = ['经理', '主管', '专员', '工程师', '设计师', '分析师']
  
  setTimeout(() => {
    employeeData.value = Array.from({ length: count }, (_, i) => ({
      id: `EMP${String(i + 1).padStart(6, '0')}`,
      name: `员工${i + 1}`,
      code: `E${String(i + 1).padStart(5, '0')}`,
      department: departments[Math.floor(Math.random() * departments.length)],
      position: positions[Math.floor(Math.random() * positions.length)],
      email: `employee${i + 1}@company.com`,
      phone: `138${String(Math.floor(Math.random() * *********)).padStart(8, '0')}`,
      entryDate: new Date(Date.now() - Math.random() * 86400000 * 365 * 5).toISOString().split('T')[0],
      status: Math.random() > 0.1 ? '在职' : '离职',
      avatar: ''
    }))
    loading.value = false
  }, 500)
}

// 生成固定列数据
const generateFixedColumnData = () => {
  fixedColumnData.value = Array.from({ length: 100 }, (_, i) => ({
    id: i + 1,
    name: `张三${i + 1}`,
    age: 20 + Math.floor(Math.random() * 30),
    address: `北京市朝阳区某某街道${i + 1}号`,
    company: `某某科技有限公司`,
    department: `技术部`,
    position: `高级工程师`,
    salary: Math.floor(Math.random() * 20000) + 10000,
    bonus: Math.floor(Math.random() * 5000),
    total: 0
  }))
  
  fixedColumnData.value.forEach(item => {
    item.total = item.salary + item.bonus
  })
}

// 生成功能演示数据
const generateDemoData = () => {
  // 排序数据
  sortData.value = Array.from({ length: 100 }, (_, i) => ({
    name: `学生${i + 1}`,
    age: 18 + Math.floor(Math.random() * 10),
    score: 60 + Math.floor(Math.random() * 40),
    rank: 0
  }))
  
  // 调整列宽数据
  resizeData.value = Array.from({ length: 50 }, (_, i) => ({
    id: i + 1,
    title: `文章标题 ${i + 1}`,
    content: `这是文章 ${i + 1} 的内容摘要，包含了一些示例文本...`,
    author: `作者${i + 1}`
  }))
  
  // 右键菜单数据
  contextData.value = Array.from({ length: 50 }, (_, i) => ({
    file: `document_${i + 1}.${['pdf', 'docx', 'xlsx', 'pptx'][Math.floor(Math.random() * 4)]}`,
    size: `${Math.floor(Math.random() * 10000) / 10} KB`,
    type: ['文档', '表格', '演示文稿', 'PDF'][Math.floor(Math.random() * 4)],
    modifiedTime: new Date(Date.now() - Math.random() * 86400000 * 30).toLocaleString()
  }))
}

// 事件处理
const handleSortChange = ({ prop, order }: { prop: string; order: 'asc' | 'desc' | null }) => {
  console.log('排序变化：', prop, order)
}

   
const handleRowClick = (row: unknown, index: number) => {
  currentRow.value = row
  console.log('行点击：', row, index)
}

const handleSearch = () => {
  // 搜索已通过计算属性实现
}

   
const viewEmployee = (row: unknown) => {
  ElMessage.info(`查看员工：${row.name}`)
}

   
const editEmployee = (row: unknown) => {
  ElMessage.info(`编辑员工：${row.name}`)
}

const exportTable = () => {
  ElMessage.success('导出表格数据')
}

   
const handleColumnResize = (column: unknown, width: number) => {
  console.log('列宽调整：', column.label, width)
}

   
const handleColumnsChange = (columns: unknown[]) => {
  console.log('列配置变化：', columns)
}

// 初始化数据
generateBasicData(10000)
generateEmployeeData(1000)
generateFixedColumnData()
generateDemoData()
</script>

<style lang="scss" scoped>
.virtual-table-demo {
  padding: 20px;
  
  h1 {
    margin-bottom: 20px;
    font-size: 24px;
    color: #303133;
  }
  
  .demo-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      h3 {
        margin: 0;
        font-size: 18px;
        color: #303133;
      }
      
      .header-actions {
        display: flex;
        gap: 12px;
      }
    }
  }
  
  .demo-content {
    .info-panel {
      margin-top: 16px;
    }
    
    // 员工信息样式
    .employee-info {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .info-text {
        .name {
          font-weight: 500;
          color: #303133;
        }
        
        .code {
          font-size: 12px;
          color: #909399;
        }
      }
    }
    
    // 特性列表
    .feature-list {
      margin: 0;
      padding-left: 20px;
      
      li {
        line-height: 1.8;
        color: #606266;
      }
    }
    
    // 对比项
    .comparison-item {
      h4 {
        margin: 0 0 16px 0;
        font-size: 16px;
        color: #303133;
      }
    }
    
    // 性能图表
    .performance-chart {
      margin-top: 24px;
      
      h4 {
        margin: 0 0 16px 0;
        font-size: 16px;
        color: #303133;
      }
      
      .chart-container {
        .chart-item {
          display: flex;
          align-items: center;
          margin-bottom: 12px;
          
          .label {
            width: 80px;
            font-size: 14px;
            color: #606266;
          }
          
          .bars {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
            
            .bar {
              height: 30px;
              line-height: 30px;
              padding: 0 12px;
              border-radius: 4px;
              font-size: 12px;
              color: #fff;
              
              &.virtual {
                background: #67c23a;
              }
              
              &.normal {
                background: #f56c6c;
              }
            }
          }
        }
      }
    }
  }
}

// 响应式
@media (max-width: 768px) {
  .virtual-table-demo {
    padding: 10px;
    
    .demo-card {
      .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
        
        .header-actions {
          width: 100%;
          flex-wrap: wrap;
        }
      }
    }
  }
}
</style>