<template>
  <div class="empty-state-demo">
    <hr-page-header title="空状态设计演示" :show-back="true">
      演示各种场景下的空数据提示优化
    </hr-page-header>

    <!-- 基础用法 -->
    <el-card class="demo-section">
      <template #header>
        <span>基础用法</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="demo-item">
            <h5>默认空状态</h5>
            <div class="demo-content">
              <EmptyState />
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="demo-item">
            <h5>自定义文案</h5>
            <div class="demo-content">
              <EmptyState
                title="还没有任何内容"
                description="这里将显示您创建的内容"
              />
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="demo-item">
            <h5>简洁模式</h5>
            <div class="demo-content">
              <EmptyState
                title="暂无数据"
                :simple="true"
              />
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 尺寸变体 -->
    <el-card class="demo-section">
      <template #header>
        <span>尺寸变体</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="demo-item">
            <h5>大尺寸</h5>
            <div class="demo-content">
              <EmptyState
                size="large"
                title="暂无数据"
                description="当前没有任何数据"
              />
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="demo-item">
            <h5>默认尺寸</h5>
            <div class="demo-content">
              <EmptyState
                size="default"
                title="暂无数据"
                description="当前没有任何数据"
              />
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="demo-item">
            <h5>小尺寸</h5>
            <div class="demo-content">
              <EmptyState
                size="small"
                title="暂无数据"
                description="当前没有任何数据"
              />
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 带图标的空状态 -->
    <el-card class="demo-section">
      <template #header>
        <span>带图标的空状态</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="demo-item">
            <h5>搜索图标</h5>
            <div class="demo-content">
              <EmptyState
                :icon="Search"
                title="没有搜索结果"
                description="请尝试其他关键词"
              />
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="demo-item">
            <h5>文件夹图标</h5>
            <div class="demo-content">
              <EmptyState
                :icon="FolderOpened"
                title="文件夹是空的"
                description="暂无文件"
                icon-color="#E6A23C"
              />
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="demo-item">
            <h5>网络错误图标</h5>
            <div class="demo-content">
              <EmptyState
                :icon="Connection"
                title="网络连接失败"
                description="请检查网络设置"
                icon-color="#F56C6C"
              />
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 带操作的空状态 -->
    <el-card class="demo-section">
      <template #header>
        <span>带操作的空状态</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="demo-item">
            <h5>单个操作</h5>
            <div class="demo-content">
              <EmptyState
                title="还没有创建任何项目"
                description="创建您的第一个项目，开始使用系统"
                :show-action="true"
                action-text="创建项目"
                :action-icon="Plus"
                @action="handleCreate"
              />
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="demo-item">
            <h5>多个操作</h5>
            <div class="demo-content">
              <EmptyState
                title="暂无数据"
                description="您可以导入现有数据或创建新数据"
              >
                <template #action>
                  <el-button type="primary" :icon="Upload">导入数据</el-button>
                  <el-button :icon="Plus">创建新数据</el-button>
                </template>
              </EmptyState>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 带建议的空状态 -->
    <el-card class="demo-section">
      <template #header>
        <span>带建议的空状态</span>
      </template>
      
      <EmptyState
        :icon="Search"
        title="没有找到匹配的员工"
        description="搜索 '张三' 没有找到相关结果"
        :suggestions="[
          '检查输入的关键词是否正确',
          '尝试使用姓名、工号或部门名称搜索',
          '使用拼音或拼音首字母搜索',
          '减少筛选条件，扩大搜索范围'
        ]"
        :show-action="true"
        action-text="清空搜索"
        @action="handleClearSearch"
      />
    </el-card>

    <!-- 预设场景 -->
    <el-card class="demo-section">
      <template #header>
        <span>预设场景</span>
      </template>
      
      <el-tabs v-model="activePreset">
        <el-tab-pane label="搜索无结果" name="search">
          <EmptyState v-bind="presets.search" @action="handleRefresh" />
        </el-tab-pane>
        <el-tab-pane label="列表为空" name="list">
          <EmptyState v-bind="presets.list" @action="handleCreate" />
        </el-tab-pane>
        <el-tab-pane label="收藏为空" name="favorite">
          <EmptyState v-bind="presets.favorite" />
        </el-tab-pane>
        <el-tab-pane label="消息为空" name="message">
          <EmptyState v-bind="presets.message" />
        </el-tab-pane>
        <el-tab-pane label="网络错误" name="network">
          <EmptyState v-bind="presets.network" @action="handleRefresh" />
        </el-tab-pane>
        <el-tab-pane label="权限不足" name="permission">
          <EmptyState v-bind="presets.permission" />
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 自定义插槽 -->
    <el-card class="demo-section">
      <template #header>
        <span>自定义插槽</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="demo-item">
            <h5>自定义图片</h5>
            <div class="demo-content">
              <EmptyState>
                <template #image>
                  <img 
                    src="https://element-plus.org/images/element-plus-logo.svg" 
                    alt="Empty"
                    style="width: 100px; height: 100px;"
                  />
                </template>
                <template #title>
                  <span style="color: #409EFF;">暂无数据</span>
                </template>
                <template #description>
                  <span>这是自定义的描述内容</span>
                </template>
              </EmptyState>
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="demo-item">
            <h5>额外内容</h5>
            <div class="demo-content">
              <EmptyState
                title="暂无订单"
                description="您还没有任何订单"
              >
                <template #extra>
                  <el-divider   />
                  <div style="color: #909399; font-size: 14px;">
                    <p>常见问题：</p>
                    <ul style="text-align: left; display: inline-block;">
                      <li>如何下单？</li>
                      <li>如何查看订单状态？</li>
                      <li>如何申请退款？</li>
                    </ul>
                  </div>
                </template>
              </EmptyState>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 实际应用场景 -->
    <el-card class="demo-section">
      <template #header>
        <span>实际应用场景</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="demo-item">
            <h5>表格空数据</h5>
            <el-table :data="[]" style="width: 100%">
              <el-table-column prop="name" label="姓名"  />
              <el-table-column prop="department" label="部门"  />
              <el-table-column prop="position" label="职位"  />
              <template #empty>
                <EmptyState
                  size="small"
                  title="暂无员工数据"
                  :show-action="true"
                  action-text="添加员工"
                  @action="handleCreate"
                />
              </template>
            </el-table>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="demo-item">
            <h5>卡片列表空数据</h5>
            <div class="card-list">
              <EmptyState
                title="暂无公告"
                description="当前没有发布任何公告"
                :icon="Notification"
                :show-action="true"
                action-text="发布公告"
                action-type="primary"
                @action="handleCreate"
              />
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 动态切换演示 -->
    <el-card class="demo-section">
      <template #header>
        <div class="card-header">
          <span>动态切换演示</span>
          <el-button-group>
            <el-button 
              v-for="state in dynamicStates" 
              :key="state.key"
              :type="currentState === state.key ? 'primary' : ''"
              size="small"
              @click="currentState = state.key"
            >
              {{ state.label }}
            </el-button>
          </el-button-group>
        </div>
      </template>
      
      <div style="min-height: 300px; display: flex; align-items: center; justify-content: center;">
        <EmptyState
          v-if="currentState === 'empty'"
          title="暂无数据"
          description="当前列表中没有任何数据"
          :show-action="true"
          action-text="添加数据"
          @action="currentState = 'loading'"
        />
        <div v-else-if="currentState === 'loading'" style="text-align: center;">
          <el-icon class="is-loading" :size="40" style="color: #409EFF;">
            <Loading />
          </el-icon>
          <p style="margin-top: 16px; color: #909399;">加载中...</p>
        </div>
        <EmptyState
          v-else-if="currentState === 'error'"
          :icon="CircleClose"
          icon-color="#F56C6C"
          title="加载失败"
          description="数据加载失败，请重试"
          :show-action="true"
          action-text="重新加载"
          action-type="danger"
          @action="currentState = 'loading'"
        />
        <div v-else-if="currentState === 'data'" style="width: 100%;">
          <el-table :data="tableData" style="width: 100%">
            <el-table-column prop="name" label="姓名"  />
            <el-table-column prop="department" label="部门"  />
            <el-table-column prop="position" label="职位"  />
          </el-table>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Search,
  FolderOpened,
  Connection,
  Plus,
  Upload,
  Notification,
  CircleClose,
  Loading,
  Star,
  ChatDotRound,
  Finished,
  Lock,
  DocumentDelete,
  Folder,
  ShoppingCart
} from '@element-plus/icons-vue'
import HrPageHeader from '@/components/common/HrPageHeader.vue'
import EmptyState, { emptyPresets } from '@/components/common/EmptyState.vue'

// 预设配置
const presets = reactive({
  search: {
    ...emptyPresets.search,
    icon: Search
  },
  list: {
    ...emptyPresets.list,
    icon: Plus
  },
  favorite: {
    ...emptyPresets.favorite,
    icon: Star
  },
  message: {
    ...emptyPresets.message,
    icon: ChatDotRound
  },
  network: {
    ...emptyPresets.network,
    icon: Connection
  },
  permission: {
    ...emptyPresets.permission,
    icon: Lock
  }
})

// 当前预设
const activePreset = ref('search')

// 动态状态
const dynamicStates = [
  { key: 'empty', label: '空数据' },
  { key: 'loading', label: '加载中' },
  { key: 'error', label: '加载失败' },
  { key: 'data', label: '有数据' }
]
const currentState = ref('empty')

// 表格数据
const tableData = ref([
  { name: 'HrHr张三', department: '技术部', position: '前端工程师' },
  { name: '李四', department: '产品部', position: '产品经理' },
  { name: '王五', department: '设计部', position: 'UI设计师' }
])

// 事件处理
const handleCreate = () => {
  ElMessage.success('点击了创建按钮')
}

const handleRefresh = () => {
  ElMessage.info('刷新数据...')
}

const handleClearSearch = () => {
  ElMessage.info('已清空搜索条件')
}
</script>

<style lang="scss" scoped>
.empty-state-demo {
  padding: 20px;
  
  .demo-section {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .demo-item {
      h5 {
        margin: 0 0 16px;
        font-size: 14px;
        color: var(--el-text-color-regular);
        text-align: center;
      }
      
      .demo-content {
        border: 1px solid var(--el-border-color-lighter);
        border-radius: 4px;
        min-height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--el-fill-color-lighter);
      }
    }
    
    .card-list {
      border: 1px solid var(--el-border-color-lighter);
      border-radius: 4px;
      min-height: 300px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--el-bg-color);
    }
  }
  
  .is-loading {
    animation: rotating 2s linear infinite;
  }
  
  @keyframes rotating {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
}
</style>