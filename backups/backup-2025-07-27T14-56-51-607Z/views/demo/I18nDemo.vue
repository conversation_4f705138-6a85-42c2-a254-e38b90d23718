<template>
  <div class="i18n-demo">
    <el-card class="demo-header">
      <div class="header-content">
        <div>
          <h1>{{ t('common.switchLanguage') }}</h1>
          <p>{{ t('settings.language') }} - {{ currentLanguage.label }}</p>
        </div>
        <hr-language-selector />
      </div>
    </el-card>

    <!-- 基础翻译演示 -->
    <el-card class="demo-section">
      <template #header>
        <span>基础翻译</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <h3>常用操作</h3>
          <div class="translation-grid">
            <div class="translation-item" v-for="key in commonKeys" :key="key">
              <span class="key">{{ key }}:</span>
              <span class="value">{{ t(`common.${key}`) }}</span>
            </div>
          </div>
        </el-col>
        
        <el-col :span="12">
          <h3>系统菜单</h3>
          <div class="translation-grid">
            <div class="translation-item" v-for="key in menuKeys" :key="key">
              <span class="key">{{ key }}:</span>
              <span class="value">{{ t(`menu.${key}`) }}</span>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 插值翻译演示 -->
    <el-card class="demo-section">
      <template #header>
        <span>插值翻译</span>
      </template>
      
      <el-table :data="interpolationData" style="width: 100%">
        <el-table-column prop="key" label="键" width="200"  />
        <el-table-column prop="params" label="参数" width="200"  />
        <el-table-column label="结果">
          <template #default="{ row }">
            {{ t(row.key, row.params) }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 日期格式化演示 -->
    <el-card class="demo-section">
      <template #header>
        <span>日期时间格式化</span>
      </template>
      
      <el-table :data="dateFormatData" style="width: 100%">
        <el-table-column prop="label" label="格式" width="150"  />
        <el-table-column label="示例">
          <template #default="{ row }">
            {{ row.formatter(currentDate) }}
          </template>
        </el-table-column>
      </el-table>
      
      <el-divider   />
      
      <h3>相对时间</h3>
      <el-table :data="relativeTimeData" style="width: 100%">
        <el-table-column prop="label" label="时间" width="150"  />
        <el-table-column label="相对表示">
          <template #default="{ row }">
            {{ formatRelativeTime(row.date) }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 数字格式化演示 -->
    <el-card class="demo-section">
      <template #header>
        <span>数字格式化</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <h3>货币</h3>
          <div class="number-list">
            <div v-for="value in currencyValues" :key="value">
              {{ value }} → {{ formatCurrency(value) }}
            </div>
          </div>
        </el-col>
        
        <el-col :span="8">
          <h3>百分比</h3>
          <div class="number-list">
            <div v-for="value in percentValues" :key="value">
              {{ value }} → {{ formatPercent(value) }}
            </div>
          </div>
        </el-col>
        
        <el-col :span="8">
          <h3>千分位</h3>
          <div class="number-list">
            <div v-for="value in numberValues" :key="value">
              {{ value }} → {{ formatNumber(value) }}
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 表单验证信息 -->
    <el-card class="demo-section">
      <template #header>
        <span>表单验证信息</span>
      </template>
      
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item :label="t('user.username')" prop="username">
          <el-input v-model="form.username" :placeholder="t('login.usernamePlaceholder')"   />
        </el-form-item>
        
        <el-form-item :label="t('user.email')" prop="email">
          <el-input v-model="form.email" :placeholder="t('validation.email')"   />
        </el-form-item>
        
        <el-form-item :label="t('user.phone')" prop="phone">
          <el-input v-model="form.phone" :placeholder="t('validation.phone')"   />
        </el-form-item>
        
        <el-form-item :label="t('employee.age')" prop="age">
          <el-input-number v-model="form.age" :min="18" :max="65"   />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="validateForm">
            {{ t('common.submit') }}
          </el-button>
          <el-button @click="resetForm">
            {{ t('common.reset') }}
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 组件集成演示 -->
    <el-card class="demo-section">
      <template #header>
        <span>Element Plus 组件集成</span>
      </template>
      
      <el-space direction="vertical" :size="20" fill style="width: 100%">
        <!-- 日期选择器 -->
        <div>
          <h4>日期选择器</h4>
          <el-date-picker
            v-model="dateValue"
            type="date"
            :placeholder="t('common.startDate')"
           />
          <span style="margin: 0 10px">{{ t('common.to') || '至' }}</span>
          <el-date-picker
            v-model="dateValue2"
            type="date"
            :placeholder="t('common.endDate')"
           />
        </div>
        
        <!-- 分页 -->
        <div>
          <h4>分页组件</h4>
          <el-pagination
            v-model:current-page="currentPage"
            :page-size="10"
            :total="100"
            layout="total, prev, pager, next"
           />
        </div>
        
        <!-- 对话框 -->
        <div>
          <h4>对话框</h4>
          <el-button @click="dialogVisible = true">
            {{ t('common.preview') }}
          </el-button>
          
          <el-dialog
            v-model="dialogVisible"
            :title="t('user.profile')"
            width="500px"
          >
            <p>{{ t('message.confirmSubmit') }}</p>
            <template #footer>
              <el-button @click="dialogVisible = false">
                {{ t('common.cancel') }}
              </el-button>
              <el-button type="primary" @click="dialogVisible = false">
                {{ t('common.confirm') }}
              </el-button>
            </template>
          </el-dialog>
        </div>
      </el-space>
    </el-card>

    <!-- 语言文件管理 -->
    <el-card class="demo-section">
      <template #header>
        <span>语言文件提取工具</span>
      </template>
      
      <el-alert type="info" :closable="false">
        <template #title>
          <div>
            <p>在实际项目中，可以使用以下工具提取和管理国际化文本：</p>
            <ul style="margin: 10px 0 0 20px;">
              <li><code>vue-i18n-extract</code> - 自动提取未翻译的文本</li>
              <li><code>i18n-ally</code> - VSCode 插件，提供内联翻译预览</li>
              <li><code>BabelEdit</code> - 专业的翻译文件编辑器</li>
              <li>自定义脚本扫描代码中的中文字符串</li>
            </ul>
          </div>
        </template>
      </el-alert>
      
      <el-divider   />
      
      <div class="extract-demo">
        <h4>文本提取示例</h4>
        <el-input
          v-model="extractText"
          type="textarea"
          :rows="4"
          placeholder="输入包含中文的代码片段"
          />
        <el-button type="primary" @click="extractChinese" style="margin-top: 10px">
          提取中文文本
        </el-button>
        
        <div v-if="extractedTexts.length > 0" class="extracted-results">
          <h4>提取结果：</h4>
          <el-tag v-for="(text, index) in extractedTexts" :key="index" style="margin: 5px">
            {{ text }}
          </el-tag>
        </div>
      </div>
    </el-card>

    <!-- 最佳实践 -->
    <el-card class="demo-section">
      <template #header>
        <span>国际化最佳实践</span>
      </template>
      
      <el-collapse v-model="activeNames">
        <el-collapse-item title="1. 键名规范" name="1">
          <ul>
            <li>使用有意义的、描述性的键名</li>
            <li>采用点分隔的层级结构：<code>module.section.key</code></li>
            <li>避免使用数字或特殊字符开头</li>
            <li>保持键名简洁但清晰</li>
          </ul>
        </el-collapse-item>
        
        <el-collapse-item title="2. 文本组织" name="2">
          <ul>
            <li>按功能模块组织语言文件</li>
            <li>公共文本放在 common 命名空间</li>
            <li>验证信息统一管理</li>
            <li>避免重复定义相同文本</li>
          </ul>
        </el-collapse-item>
        
        <el-collapse-item title="3. 动态内容" name="3">
          <ul>
            <li>使用插值而非字符串拼接</li>
            <li>数字、日期使用格式化函数</li>
            <li>复数形式需要特殊处理</li>
            <li>HTML 内容使用 v-html 或组件插槽</li>
          </ul>
        </el-collapse-item>
        
        <el-collapse-item title="4. 性能优化" name="4">
          <ul>
            <li>按需加载语言包（大型项目）</li>
            <li>使用懒加载分割语言文件</li>
            <li>缓存常用翻译结果</li>
            <li>避免在循环中频繁调用 $t</li>
          </ul>
        </el-collapse-item>
        
        <el-collapse-item title="5. 开发流程" name="5">
          <ul>
            <li>开发时使用默认语言（通常是英语）</li>
            <li>定期提取和更新翻译文本</li>
            <li>使用翻译管理平台协作</li>
            <li>进行多语言测试和审查</li>
          </ul>
        </el-collapse-item>
      </el-collapse>
    </el-card>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'I18nDemo'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed } from 'vue'
import { useI18n } from '@/composables/useI18n'
import { languages, getLocale } from '@/i18n'
import HrLanguageSelector from '@/components/common/HrLanguageSelector.vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'

// 使用国际化
const {t: _t, locale: _locale, formatDate: _formatDate, formatTime: _formatTime, formatDateTime: _formatDateTime, formatRelativeTime: _formatRelativeTime, formatCurrency: _formatCurrency, formatPercent: _formatPercent, formatNumber: _formatNumber} =  useI18n()

// 当前语言
const currentLanguage 
  
  .demo-header {
    margin-bottom: 20px;
    
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      h1 {
        margin: 0 0 10px;
        font-size: 24px;
      }
      
      p {
        margin: 0;
        color: var(--el-text-color-secondary);
      }
    }
  }
  
  .demo-section {
    margin-bottom: 20px;
    
    h3 {
      margin: 0 0 16px;
      font-size: 16px;
    }
    
    h4 {
      margin: 16px 0 12px;
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
  }
  
  .translation-grid {
    display: grid;
    gap: 12px;
    
    .translation-item {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      background-color: var(--el-fill-color-light);
      border-radius: 4px;
      
      .key {
        flex-shrink: 0;
        width: 100px;
        font-weight: 500;
        color: var(--el-text-color-secondary);
      }
      
      .value {
        flex: 1;
        color: var(--el-text-color-primary);
      }
    }
  }
  
  .number-list {
    div {
      padding: 8px 12px;
      margin-bottom: 8px;
      background-color: var(--el-fill-color-light);
      border-radius: 4px;
      font-family: monospace;
    }
  }
  
  .extract-demo {
    .extracted-results {
      margin-top: 20px;
      padding: 16px;
      background-color: var(--el-fill-color-light);
      border-radius: 4px;
    }
  }
  
  :deep(.el-collapse-item__content) {
    padding: 16px 20px;
    
    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        line-height: 1.6;
      }
    }
    
    code {
      padding: 2px 6px;
      background-color: var(--el-fill-color);
      border-radius: 3px;
      font-family: monospace;
      font-size: 13px;
    }
  }
}
</style>