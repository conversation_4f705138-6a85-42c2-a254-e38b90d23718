<template>
  <div class="pwa-enhancement-demo">
    <hr-page-header title="PWA功能增强演示" :show-back="true">
      展示渐进式Web应用的离线访问和其他高级功能
    </hr-page-header>

    <!-- PWA状态 -->
    <el-card class="demo-section">
      <template #header>
        <span>PWA状态检测</span>
      </template>
      
      <el-descriptions :column="2" border>
        <el-descriptions-item label="网络状态">
          <el-tag :type="isOnline ? 'success' : 'warning'">
            {{ isOnline ? '在线' : '离线' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="Service Worker">
          <el-tag :type="swStatus.registered ? 'success' : 'info'">
            {{ swStatus.status }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="安装状态">
          <el-tag :type="isInstalled ? 'success' : 'default'">
            {{ isInstalled ? '已安装' : '未安装' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="更新检查">
          <el-button size="small" @click="checkForUpdates" :loading="checkingUpdate">
            检查更新
          </el-button>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 离线功能测试 -->
    <el-card class="demo-section">
      <template #header>
        <span>离线功能测试</span>
      </template>
      
      <el-alert
        type="info"
        :closable="false"
        show-icon
      >
        断开网络连接后，以下功能仍然可用
      </el-alert>
      
      <div class="offline-features">
        <div class="feature-item">
          <h4>缓存的员工数据</h4>
          <el-button @click="loadCachedEmployees">加载员工列表</el-button>
          <div v-if="cachedEmployees.length" class="cached-data">
            <el-table :data="cachedEmployees" size="small" max-height="200">
              <el-table-column prop="name" label="姓名"  />
              <el-table-column prop="department" label="部门"  />
              <el-table-column prop="position" label="职位"  />
            </el-table>
          </div>
        </div>
        
        <el-divider   />
        
        <div class="feature-item">
          <h4>离线表单提交</h4>
          <el-form :model="offlineForm" label-width="80px">
            <el-form-item label="姓名">
              <el-input v-model="offlineForm.name" placeholder="输入姓名"   />
            </el-form-item>
            <el-form-item label="内容">
              <el-input
                v-model="offlineForm.content"
                type="textarea"
                placeholder="输入内容"
                />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="submitOfflineForm">
                提交（离线时将缓存）
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </el-card>

    <!-- 缓存管理 -->
    <el-card class="demo-section">
      <template #header>
        <div class="card-header">
          <span>缓存管理</span>
          <el-button size="small" @click="openOfflineManager">
            打开离线管理器
          </el-button>
        </div>
      </template>
      
      <div class="cache-actions">
        <el-button @click="preloadResources">预加载资源</el-button>
        <el-button @click="clearAllCache" type="danger">清除所有缓存</el-button>
      </div>
      
      <el-divider   />
      
      <div class="cache-list">
        <h4>已缓存的资源</h4>
        <el-tree
          :data="cachedResources"
          :props="{ label: 'name', children: 'items' }"
          default-expand-all
        >
          <template #default="{ node, data }">
            <span class="tree-node">
              <span>{{ node.label }}</span>
              <span class="node-info">
                <el-tag size="small" v-if="data.size">
                  {{ formatSize(data.size) }}
                </el-tag>
                <el-tag size="small" v-if="data.count" type="info">
                  {{ data.count }} 项
                </el-tag>
              </span>
            </span>
          </template>
        </el-tree>
      </div>
    </el-card>

    <!-- 推送通知 -->
    <el-card class="demo-section">
      <template #header>
        <span>推送通知测试</span>
      </template>
      
      <el-form label-width="100px">
        <el-form-item label="通知权限">
          <el-tag :type="getNotificationPermissionType()">
            {{ notificationPermission }}
          </el-tag>
          <el-button
            v-if="notificationPermission === 'default'"
            size="small"
            @click="requestNotificationPermission"
            style="margin-left: 12px"
          >
            请求权限
          </el-button>
        </el-form-item>
        <el-form-item label="测试通知">
          <el-button
            @click="sendTestNotification"
            :disabled="notificationPermission !== 'granted'"
          >
            发送测试通知
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 后台同步 -->
    <el-card class="demo-section">
      <template #header>
        <span>后台同步演示</span>
      </template>
      
      <div class="sync-demo">
        <p>在离线状态下提交的数据将在恢复网络后自动同步</p>
        
        <div class="pending-sync">
          <h4>待同步项目</h4>
          <el-empty v-if="pendingSyncItems.length === 0" description="暂无待同步数据"  />
          <div v-else class="sync-items">
            <div
              v-for="(item, index) in pendingSyncItems"
              :key="index"
              class="sync-item"
            >
              <el-icon><Clock /></el-icon>
              <span>{{ item.type }}: {{ item.data }}</span>
              <el-tag size="small" type="warning">待同步</el-tag>
            </div>
          </div>
        </div>
        
        <el-button
          @click="triggerBackgroundSync"
          :disabled="pendingSyncItems.length === 0"
          style="margin-top: 16px"
        >
          立即同步
        </el-button>
      </div>
    </el-card>

    <!-- App Shell 演示 -->
    <el-card class="demo-section">
      <template #header>
        <span>App Shell 架构</span>
      </template>
      
      <p>App Shell 是 PWA 的核心架构模式，将应用的基础框架与动态内容分离：</p>
      
      <div class="app-shell-demo">
        <div class="shell-structure">
          <div class="shell-part shell">
            <h5>App Shell（缓存）</h5>
            <ul>
              <li>导航栏</li>
              <li>侧边栏</li>
              <li>基础样式</li>
              <li>核心脚本</li>
            </ul>
          </div>
          <div class="shell-part content">
            <h5>动态内容（按需加载）</h5>
            <ul>
              <li>员工数据</li>
              <li>统计图表</li>
              <li>实时通知</li>
              <li>用户生成内容</li>
            </ul>
          </div>
        </div>
      </div>
    </el-card>

    <!-- PWA增强组件 -->
    <PWAEnhancement ref="pwaEnhancement" />
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import { Clock } from '@element-plus/icons-vue'
import HrPageHeader from '@/components/common/HrPageHeader.vue'
import HrPWAEnhancement from '@/components/ux/HrPWAEnhancement.vue'

// 引用
const pwaEnhancement = ref<InstanceType<typeof PWAEnhancement>>()

// 状态
const isOnline = ref(navigator.onLine)
const isInstalled = ref(false)
const checkingUpdate = ref(false)
const notificationPermission = ref(Notification.permission)

// Service Worker 状态
const swStatus = reactive({
  registered: false,
  status: '未注册'
})

// 缓存的员工数据
const cachedEmployees = ref<any[]>([])

// 离线表单
const offlineForm = reactive({
  name: '',
  content: ''
})

// 待同步项目
const pendingSyncItems = ref<Array<{ type: string; data: string }>>([])

// 缓存的资源
const cachedResources = ref([
  {
    name: 'HrHr静态资源',
    count: 0,
    items: []
  },
  {
    name: 'API数据',
    count: 0,
    items: []
  },
  {
    name: '图片资源',
    count: 0,
    items: []
  }
])

// 格式化文件大小
const formatSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 获取通知权限类型
const getNotificationPermissionType = () => {
  switch (notificationPermission.value) {
    case 'granted':
      return 'success'
    case 'denied':
      return 'danger'
    default:
      return 'warning'
  }
}

// 检查Service Worker状态
const checkServiceWorker = async () => {
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.getRegistration()
      if (registration) {
        swStatus.registered = true
        if (registration.active) {
          swStatus.status = '已激活'
        } else if (registration.installing) {
          swStatus.status = '安装中'
        } else if (registration.waiting) {
          swStatus.status = '等待激活'
        } else {
          swStatus.status = '已注册'
        }
      }
    } catch (__error) {
      console.error('检查Service Worker失败:', error)
    }
  } else {
    swStatus.status = '不支持'
  }
}

// 检查安装状态
const checkInstallStatus = () => {
  // 检查是否在独立模式运行
  if (window.matchMedia('(display-mode: standalone)').matches) {
    isInstalled.value = true
  }
  
  // iOS检查
  if ('standalone' in window.navigator && (window.navigator as unknown).standalone) {
    isInstalled.value = true
  }
}

// 检查更新
const checkForUpdates = async () => {
  checkingUpdate.value = true
  
  try {
    const registration = await navigator.serviceWorker.getRegistration()
    if (registration) {
      await registration.update()
      ElMessage.success('已检查更新，如有新版本将自动提示')
    }
  } catch (__error) {
    ElMessage.error('检查更新失败')
    console.error('检查更新失败:', error)
  } finally {
    checkingUpdate.value = false
  }
}

// 加载缓存的员工数据
const loadCachedEmployees = async () => {
  // 模拟从缓存加载数据
  cachedEmployees.value = [
    { name: '张三', department: '技术部', position: '前端工程师' },
    { name: '李四', department: '产品部', position: '产品经理' },
    { name: '王五', department: '设计部', position: 'UI设计师' }
  ]
  
  ElMessage.success('已加载缓存的员工数据')
}

// 提交离线表单
const submitOfflineForm = async () => {
  if (!offlineForm.name || !offlineForm.content) {
    ElMessage.warning('请填写完整信息')
    return
  }

  if (!isOnline.value) {
    // 离线状态，保存到待同步队列
    pendingSyncItems.value.push({
      type: '表单提交',
      data: `${offlineForm.name}: ${offlineForm.content}`
    })
    
    // 保存到IndexedDB
    try {
      const db = await openDB()
      const tx = db.transaction(['pending-forms'], 'readwrite')
      await tx.objectStore('pending-forms').add({
        ...offlineForm,
        timestamp: Date.now()
      })
      
      ElMessage.warning('当前离线，数据已缓存，将在恢复网络后自动提交')
    } catch (__error) {
      console.error('保存离线数据失败:', error)
      ElMessage.error('保存失败')
    }
  } else {
    // 在线状态，直接提交
    ElMessage.success('表单提交成功')
  }
  
  // 清空表单
  offlineForm.name = ''
  offlineForm.content = ''
}

// 打开离线管理器
const openOfflineManager = () => {
  pwaEnhancement.value?.showOfflineManager()
}

// 预加载资源
const preloadResources = async () => {
  const loading = ElMessage.loading('正在预加载资源...')
  
  try {
    // 预加载重要页面
    const pagesToCache = [
      '/employee/list',
      '/organization/structure',
      '/dashboard'
    ]
    
    for (const page of pagesToCache) {
      await fetch(page)
    }
    
    // 更新缓存统计
    await updateCacheStats()
    
    loading.close()
    ElMessage.success('资源预加载完成')
  } catch (__error) {
    loading.close()
    console.error('预加载失败:', error)
    ElMessage.error('预加载失败')
  }
}

// 清除所有缓存
const clearAllCache = async () => {
  try {
    const cacheNames = await caches.keys()
    await Promise.all(cacheNames.map(name => caches.delete(name)))
    
    await updateCacheStats()
    ElMessage.success('缓存已清除')
  } catch (__error) {
    console.error('清除缓存失败:', error)
    ElMessage.error('清除缓存失败')
  }
}

// 更新缓存统计
const updateCacheStats = async () => {
  try {
    const cacheNames = await caches.keys()
    
    for (const resource of cachedResources.value) {
      resource.items = []
      resource.count = 0
    }
    
    for (const cacheName of cacheNames) {
      const cache = await caches.open(cacheName)
      const keys = await cache.keys()
      
      let resourceType = 0
      if (cacheName.includes('static')) {
        resourceType = 0
      } else if (cacheName.includes('api')) {
        resourceType = 1
      } else if (cacheName.includes('image')) {
        resourceType = 2
      }
      
      cachedResources.value[resourceType].count = keys.length
      cachedResources.value[resourceType].items = keys.slice(0, 5).map(req => ({
        name: new URL(req.url).pathname,
        size: 0
      }))
    }
  } catch (__error) {
    console.error('更新缓存统计失败:', error)
  }
}

// 请求通知权限
const requestNotificationPermission = async () => {
  try {
    const permission = await Notification.requestPermission()
    notificationPermission.value = permission
    
    if (permission === 'granted') {
      ElMessage.success('通知权限已授予')
    } else if (permission === 'denied') {
      ElMessage.warning('通知权限被拒绝')
    }
  } catch (__error) {
    console.error('请求通知权限失败:', error)
    ElMessage.error('请求权限失败')
  }
}

// 发送测试通知
const sendTestNotification = () => {
  if (Notification.permission === 'granted') {
    const notification = new Notification('杭科院HR系统', {
      body: '这是一条测试通知，PWA功能正常工作！',
      icon: '/icons/icon-192x192.png',
      badge: '/icons/badge-72x72.png',
      tag: 'test-notification',
      requireInteraction: false
    })
    
    notification.onclick = () => {
      window.focus()
      notification.close()
      ElNotification.success('您点击了通知')
    }
  }
}

// 触发后台同步
const triggerBackgroundSync = async () => {
  try {
    const registration = await navigator.serviceWorker.ready
    
    if ('sync' in registration) {
      await registration.sync.register('sync-offline-data')
      ElMessage.success('已触发后台同步')
      
      // 模拟同步完成
      setTimeout(() => {
        pendingSyncItems.value = []
        ElNotification.success('数据同步完成')
      }, 2000)
    } else {
      ElMessage.warning('当前浏览器不支持后台同步')
    }
  } catch (__error) {
    console.error('触发同步失败:', error)
    ElMessage.error('同步失败')
  }
}

// 打开IndexedDB
const openDB = (): Promise<IDBDatabase> => {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('pwa-demo', 1)
    
    request.onerror = () => reject(request.error)
    request.onsuccess = () => resolve(request.result)
    
    request.onupgradeneeded = (_event) => {
      const db = (event.target as IDBOpenDBRequest).result
      
      if (!db.objectStoreNames.contains('pending-forms')) {
        db.createObjectStore('pending-forms', { keyPath: 'id', autoIncrement: true })
      }
    }
  })
}

// 生命周期
onMounted(() => {
  // 检查状态
  checkServiceWorker()
  checkInstallStatus()
  updateCacheStats()
  
  // 监听网络状态
  window.addEventListener('online', () => {
    isOnline.value = true
    ElMessage.success('网络已恢复')
  })
  
  window.addEventListener('offline', () => {
    isOnline.value = false
    ElMessage.warning('网络已断开')
  })
})
</script>

<style lang="scss" scoped>
.pwa-enhancement-demo {
  padding: 20px;
  
  .demo-section {
    margin-bottom: 20px;
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .offline-features {
    .feature-item {
      margin: 20px 0;
      
      h4 {
        margin-bottom: 12px;
      }
      
      .cached-data {
        margin-top: 12px;
      }
    }
  }
  
  .cache-actions {
    display: flex;
    gap: 12px;
  }
  
  .cache-list {
    h4 {
      margin-bottom: 12px;
    }
    
    .tree-node {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      
      .node-info {
        display: flex;
        gap: 8px;
      }
    }
  }
  
  .sync-demo {
    .pending-sync {
      margin: 20px 0;
      
      h4 {
        margin-bottom: 12px;
      }
      
      .sync-items {
        .sync-item {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 12px;
          background: var(--el-fill-color-light);
          border-radius: 4px;
          margin-bottom: 8px;
          
          .el-icon {
            color: var(--el-color-warning);
          }
          
          span {
            flex: 1;
          }
        }
      }
    }
  }
  
  .app-shell-demo {
    margin-top: 16px;
    
    .shell-structure {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      
      .shell-part {
        padding: 20px;
        border-radius: 8px;
        
        h5 {
          margin-bottom: 12px;
        }
        
        ul {
          list-style: none;
          padding: 0;
          
          li {
            padding: 4px 0;
            padding-left: 20px;
            position: relative;
            
            &::before {
              content: '•';
              position: absolute;
              left: 0;
            }
          }
        }
        
        &.shell {
          background: var(--el-color-primary-light-9);
          border: 1px solid var(--el-color-primary-light-5);
        }
        
        &.content {
          background: var(--el-color-success-light-9);
          border: 1px solid var(--el-color-success-light-5);
        }
      }
    }
  }
}
</style>