<template>
  <div class="skeleton-demo">
    <h1>骨架屏加载优化示例</h1>

    <!-- 基础骨架屏 -->
    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <h3>基础骨架屏类型</h3>
          <el-space>
            <el-button @click="toggleLoading('basic')">
              {{ loadingStates.basic ? '停止' : '开始' }}加载
            </el-button>
            <el-switch v-model="animated" active-text="动画"  />
          </el-space>
        </div>
      </template>
      
      <el-tabs v-model="basicTab">
        <el-tab-pane label="基础类型" name="basic">
          <SkeletonLoader :loading="loadingStates.basic" :skeleton="{ type: 'basic', rows: 4, animated }">
            <div class="content-text">
              <h4>这是标题内容</h4>
              <p>这是第一段文本内容，用于展示骨架屏加载效果。</p>
              <p>这是第二段文本内容，包含更多的描述信息。</p>
              <p>这是第三段文本内容，可能会有不同的长度。</p>
            </div>
          </SkeletonLoader>
        </el-tab-pane>
        
        <el-tab-pane label="头像类型" name="avatar">
          <SkeletonLoader :loading="loadingStates.basic" :skeleton="{ type: 'avatar', animated }">
            <div class="avatar-content">
              <el-avatar :size="64" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"   />
              <div class="info">
                <h4>张三</h4>
                <p>前端开发工程师 | 技术部</p>
              </div>
            </div>
          </SkeletonLoader>
        </el-tab-pane>
        
        <el-tab-pane label="卡片类型" name="card">
          <SkeletonLoader :loading="loadingStates.basic" :skeleton="{ type: 'card', animated }">
            <el-card shadow="never">
              <img src="https://shadow.elemecdn.com/app/element/hamburger.9cf7b091-55e9-11e9-a976-7f4d0b07eef6.png" class="image" />
              <div class="card-content">
                <h4>产品标题</h4>
                <p>这是产品描述信息，包含产品的特点和优势。</p>
                <div class="actions">
                  <el-button type="primary">查看详情</el-button>
                  <el-button>加入购物车</el-button>
                </div>
              </div>
            </el-card>
          </SkeletonLoader>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 复杂骨架屏 -->
    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <h3>复杂骨架屏类型</h3>
          <el-button @click="toggleLoading('complex')">
            {{ loadingStates.complex ? '停止' : '开始' }}加载
          </el-button>
        </div>
      </template>
      
      <el-tabs v-model="complexTab">
        <el-tab-pane label="表格骨架屏" name="table">
          <SkeletonLoader 
            :loading="loadingStates.complex" 
            :skeleton="{ type: 'table', rows: 5, columns: 6, animated }"
          >
            <el-table :data="tableData" stripe>
              <el-table-column prop="id" label="ID" width="80"  />
              <el-table-column prop="name" label="姓名"  />
              <el-table-column prop="department" label="部门"  />
              <el-table-column prop="position" label="职位"  />
              <el-table-column prop="email" label="邮箱"  />
              <el-table-column label="操作" width="120">
                <template #default>
                  <el-button text type="primary" size="small">编辑</el-button>
                  <el-button text type="danger" size="small">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </SkeletonLoader>
        </el-tab-pane>
        
        <el-tab-pane label="列表骨架屏" name="list">
          <SkeletonLoader 
            :loading="loadingStates.complex" 
            :skeleton="{ type: 'list', rows: 4, showAvatar: true, showMeta: true, animated }"
          >
            <div class="list-container">
              <div class="list-item" v-for="item in listData" :key="item.id">
                <el-avatar :size="48">{{ item.name.charAt(0) }}</el-avatar>
                <div class="list-content">
                  <h4>{{ item.title }}</h4>
                  <p>{{ item.description }}</p>
                  <div class="meta">
                    <span>{{ item.author }}</span>
                    <span>{{ item.date }}</span>
                    <span>{{ item.views }} 阅读</span>
                  </div>
                </div>
                <el-button type="primary">查看</el-button>
              </div>
            </div>
          </SkeletonLoader>
        </el-tab-pane>
        
        <el-tab-pane label="表单骨架屏" name="form">
          <SkeletonLoader 
            :loading="loadingStates.complex" 
            :skeleton="{ type: 'form', rows: 5, showActions: true, animated }"
          >
            <el-form label-width="100px">
              <el-form-item label="用户名">
                <el-input v-model="formData.username"   />
              </el-form-item>
              <el-form-item label="邮箱">
                <el-input v-model="formData.email"   />
              </el-form-item>
              <el-form-item label="部门">
                <el-select v-model="formData.department" placeholder="请选择">
                  <el-option label="技术部" value="tech"  />
                  <el-option label="产品部" value="product"  />
                </el-select>
              </el-form-item>
              <el-form-item label="入职日期">
                <el-date-picker v-model="formData.date" type="date"  />
              </el-form-item>
              <el-form-item label="个人简介">
                <el-input v-model="formData.bio" type="textarea" rows="3"   />
              </el-form-item>
              <el-form-item>
                <el-button type="primary">提交</el-button>
                <el-button>取消</el-button>
              </el-form-item>
            </el-form>
          </SkeletonLoader>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 自动骨架屏 -->
    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <h3>智能自动骨架屏</h3>
          <el-button @click="toggleLoading('auto')">
            {{ loadingStates.auto ? '停止' : '开始' }}加载
          </el-button>
        </div>
      </template>
      
      <AutoSkeleton :loading="loadingStates.auto">
        <div class="auto-content">
          <el-row :gutter="20">
            <el-col :span="8" v-for="i in 3" :key="i">
              <div class="stat-card">
                <div class="stat-icon">
                  <el-icon :size="32"><User /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-title">用户总数</div>
                  <div class="stat-value">{{ 12345 + i * 1000 }}</div>
                  <div class="stat-trend">
                    <span class="trend-up">+12.5%</span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
          
          <el-divider   />
          
          <div class="chart-container">
            <h4>数据趋势图</h4>
            <div class="chart-placeholder" style="height: 300px; background: #f0f0f0; display: flex; align-items: center; justify-content: center;">
              图表区域
            </div>
          </div>
        </div>
      </AutoSkeleton>
    </el-card>

    <!-- 页面级骨架屏 -->
    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <h3>页面级加载状态</h3>
          <el-space>
            <el-button @click="simulatePageLoad">模拟页面加载</el-button>
            <el-button @click="simulatePageError" type="danger">模拟加载失败</el-button>
            <el-button @click="simulateEmptyPage" type="warning">模拟空数据</el-button>
          </el-space>
        </div>
      </template>
      
      <PageLoader
        :loading="pageLoading"
        :error="pageError"
        :empty="pageEmpty"
        :progress="loadProgress"
        :show-progress="true"
        skeleton-type="detail"
        @retry="handleRetry"
      >
        <div class="page-content">
          <h2>页面标题</h2>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="姓名">张三</el-descriptions-item>
            <el-descriptions-item label="手机号">18888888888</el-descriptions-item>
            <el-descriptions-item label="部门">技术部</el-descriptions-item>
            <el-descriptions-item label="职位">前端工程师</el-descriptions-item>
            <el-descriptions-item label="邮箱"><EMAIL></el-descriptions-item>
            <el-descriptions-item label="入职时间">2023-01-01</el-descriptions-item>
          </el-descriptions>
          
          <h3 style="margin-top: 24px">工作经历</h3>
          <el-timeline>
            <el-timeline-item timestamp="2023/01/01" placement="top">
              <el-card>
                <h4>加入公司</h4>
                <p>作为前端工程师加入技术部</p>
              </el-card>
            </el-timeline-item>
            <el-timeline-item timestamp="2023/06/01" placement="top">
              <el-card>
                <h4>项目负责人</h4>
                <p>负责人事管理系统前端开发</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>
      </PageLoader>
    </el-card>

    <!-- 最佳实践 -->
    <el-card class="demo-card">
      <template #header>
        <h3>骨架屏最佳实践</h3>
      </template>
      
      <el-alert type="info" :closable="false">
        <template #title>
          <h4>使用建议</h4>
          <ul class="best-practices">
            <li><strong>选择合适的类型：</strong>根据内容结构选择对应的骨架屏类型</li>
            <li><strong>保持一致性：</strong>骨架屏布局应与实际内容布局保持一致</li>
            <li><strong>添加动画效果：</strong>使用微妙的动画让等待过程不那么枯燥</li>
            <li><strong>避免过度使用：</strong>对于快速加载的内容，可以设置延迟显示</li>
            <li><strong>提供加载进度：</strong>对于长时间加载，显示进度条或百分比</li>
            <li><strong>处理错误状态：</strong>提供友好的错误提示和重试机制</li>
            <li><strong>考虑空状态：</strong>数据为空时显示合适的空状态界面</li>
          </ul>
        </template>
      </el-alert>
      
      <el-divider   />
      
      <div class="code-example">
        <h4>代码示例</h4>
        <pre><code>// 基础用法
&lt;SkeletonLoader :loading="loading" :skeleton="{ type: 'table', rows: 5 }"&gt;
  &lt;el-table :data="tableData"&gt;
    &lt;!-- 表格内容 --&gt;
  &lt;/el-table&gt;
&lt;/SkeletonLoader&gt;

// 智能自动检测
&lt;AutoSkeleton :loading="loading"&gt;
  &lt;!-- 自动根据内容生成骨架屏 --&gt;
  &lt;div class="your-content"&gt;...&lt;/div&gt;
&lt;/AutoSkeleton&gt;

// 页面级加载
&lt;PageLoader 
  :loading="loading" 
  :error="error"
  :empty="empty"
  @retry="handleRetry"
&gt;
  &lt;!-- 页面内容 --&gt;
&lt;/PageLoader&gt;</code></pre>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive } from 'vue'
import HrSkeleton from '@/components/common/HrSkeleton.vue'
import HrSkeletonLoader from '@/components/common/HrSkeletonLoader.vue'
import HrAutoSkeleton from '@/components/common/HrAutoSkeleton.vue'
import HrPageLoader from '@/components/common/HrPageLoader.vue'
import { User } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 加载状态
const loadingStates = reactive({
  basic: false,
  complex: false,
  auto: false
})

// 页面加载状态
const pageLoading = ref(false)
const pageError = ref<Error | null>(null)
const pageEmpty = ref(false)
const loadProgress = ref(0)

// 选项卡
const basicTab = ref('basic')
const complexTab = ref('table')
const animated = ref(true)

// 表格数据
const tableData = ref([
  { id: 1, name: 'HrHr张三', department: '技术部', position: '前端工程师', email: '<EMAIL>' },
  { id: 2, name: '李四', department: '产品部', position: '产品经理', email: '<EMAIL>' },
  { id: 3, name: '王五', department: '设计部', position: 'UI设计师', email: '<EMAIL>' },
  { id: 4, name: '赵六', department: '市场部', position: '市场专员', email: '<EMAIL>' },
  { id: 5, name: '钱七', department: '技术部', position: '后端工程师', email: '<EMAIL>' }
])

// 列表数据
const listData = ref([
  {
    id: 1,
    name: '张三',
    title: 'Vue 3 性能优化实践',
    description: '本文介绍了在 Vue 3 项目中进行性能优化的各种技巧和最佳实践。',
    author: '张三',
    date: '2024-01-15',
    views: 1234
  },
  {
    id: 2,
    name: '李四',
    title: 'TypeScript 高级技巧',
    description: '深入探讨 TypeScript 的高级特性和实际应用场景。',
    author: '李四',
    date: '2024-01-14',
    views: 2345
  },
  {
    id: 3,
    name: '王五',
    title: '前端工程化实战',
    description: '从零开始搭建一个完整的前端工程化项目。',
    author: '王五',
    date: '2024-01-13',
    views: 3456
  }
])

// 表单数据
const formData = reactive({
  username: '',
  email: '',
  department: '',
  date: '',
  bio: ''
})

// 切换加载状态
const toggleLoading = (type: keyof typeof loadingStates) => {
  loadingStates[type] = !loadingStates[type]
  
  // 模拟加载完成
  if (loadingStates[type]) {
    setTimeout(() => {
      loadingStates[type] = false
      ElMessage.success('加载完成')
    }, 3000)
  }
}

// 模拟页面加载
const simulatePageLoad = () => {
  pageLoading.value = true
  pageError.value = null
  pageEmpty.value = false
  loadProgress.value = 0
  
  // 模拟进度
  const interval = setInterval(() => {
    loadProgress.value += Math.random() * 20
    if (loadProgress.value >= 100) {
      loadProgress.value = 100
      clearInterval(interval)
      setTimeout(() => {
        pageLoading.value = false
        ElMessage.success('页面加载完成')
      }, 500)
    }
  }, 500)
}

// 模拟加载失败
const simulatePageError = () => {
  pageLoading.value = true
  pageError.value = null
  pageEmpty.value = false
  
  setTimeout(() => {
    pageLoading.value = false
    pageError.value = new Error('网络连接失败')
  }, 2000)
}

// 模拟空数据
const simulateEmptyPage = () => {
  pageLoading.value = true
  pageError.value = null
  pageEmpty.value = false
  
  setTimeout(() => {
    pageLoading.value = false
    pageEmpty.value = true
  }, 2000)
}

// 重试处理
const handleRetry = () => {
  ElMessage.info('重新加载中...')
  simulatePageLoad()
}
</script>

<style lang="scss" scoped>
.skeleton-demo {
  padding: 20px;
  
  h1 {
    margin-bottom: 20px;
    font-size: 24px;
    color: #303133;
  }
  
  .demo-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      h3 {
        margin: 0;
        font-size: 18px;
        color: #303133;
      }
    }
  }
  
  // 内容样式
  .content-text {
    h4 {
      margin: 0 0 16px 0;
      font-size: 18px;
    }
    
    p {
      margin: 12px 0;
      line-height: 1.6;
      color: #606266;
    }
  }
  
  .avatar-content {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .info {
      h4 {
        margin: 0 0 8px 0;
        font-size: 18px;
      }
      
      p {
        margin: 0;
        color: #909399;
      }
    }
  }
  
  .card-content {
    padding: 16px 0;
    
    .image {
      width: 100%;
      height: 150px;
      object-fit: cover;
      margin-bottom: 16px;
    }
    
    h4 {
      margin: 0 0 8px 0;
    }
    
    p {
      margin: 0 0 16px 0;
      color: #606266;
    }
    
    .actions {
      display: flex;
      gap: 12px;
    }
  }
  
  // 列表容器
  .list-container {
    .list-item {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 16px 0;
      border-bottom: 1px solid #ebeef5;
      
      &:last-child {
        border-bottom: none;
      }
      
      .list-content {
        flex: 1;
        
        h4 {
          margin: 0 0 8px 0;
          font-size: 16px;
        }
        
        p {
          margin: 0 0 8px 0;
          color: #606266;
        }
        
        .meta {
          display: flex;
          gap: 16px;
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
  
  // 统计卡片
  .stat-card {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    background: #f5f7fa;
    border-radius: 8px;
    
    .stat-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 64px;
      height: 64px;
      background: #fff;
      border-radius: 8px;
      color: var(--el-color-primary);
    }
    
    .stat-content {
      flex: 1;
      
      .stat-title {
        font-size: 14px;
        color: #909399;
        margin-bottom: 4px;
      }
      
      .stat-value {
        font-size: 24px;
        font-weight: bold;
        color: #303133;
      }
      
      .stat-trend {
        margin-top: 4px;
        font-size: 12px;
        
        .trend-up {
          color: var(--el-color-success);
        }
      }
    }
  }
  
  // 最佳实践
  .best-practices {
    margin: 12px 0 0 20px;
    
    li {
      line-height: 1.8;
      color: #606266;
      
      strong {
        color: #303133;
      }
    }
  }
  
  // 代码示例
  .code-example {
    h4 {
      margin: 0 0 12px 0;
      font-size: 16px;
      color: #303133;
    }
    
    pre {
      background: #f5f7fa;
      padding: 16px;
      border-radius: 4px;
      overflow-x: auto;
      
      code {
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        font-size: 14px;
        line-height: 1.5;
        color: #303133;
      }
    }
  }
}
</style>