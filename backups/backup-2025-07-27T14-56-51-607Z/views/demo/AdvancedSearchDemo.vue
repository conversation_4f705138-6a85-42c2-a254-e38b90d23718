<template>
  <div class="advanced-search-demo">
    <h1>高级搜索组件演示</h1>
    
    <HrAdvancedSearch
      :fields="searchFields"
      :expanded="true"
      :show-save-button="true"
      storage-key="employee-search"
      @search="handleSearch"
      @reset="handleReset"
      @save-template="handleSaveTemplate"
      @update-template="handleUpdateTemplate"
      @delete-template="handleDeleteTemplate"
      @load-template="handleLoadTemplate"
    />
    
    <el-card style="margin-top: 20px">
      <template #header>
        <h3>搜索结果</h3>
      </template>
      <el-descriptions :column="3" border>
        <el-descriptions-item
          v-for="(value, key) in searchResult"
          :key="key"
          :label="getFieldLabel(key)"
        >
          {{ formatValue(value) }}
        </el-descriptions-item>
      </el-descriptions>
      <el-empty v-if="!hasSearchResult" description="请输入搜索条件"  />
    </el-card>
    
    <el-card style="margin-top: 20px">
      <template #header>
        <h3>事件日志</h3>
      </template>
      <el-timeline>
        <el-timeline-item
          v-for="(log, index) in eventLogs"
          :key="index"
          :timestamp="log.time"
          :type="log.type"
        >
          {{ log.message }}
        </el-timeline-item>
      </el-timeline>
      <el-empty v-if="eventLogs.length === 0" description="暂无事件"  />
    </el-card>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'AdvancedSearchDemo'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, computed } from 'vue'
import HrAdvancedSearch from '@/components/common/HrAdvancedSearch.vue'

// 搜索字段配置
const searchFields = ref([
  {
    key: 'name',
    label: '姓名',
    type: 'input',
    placeholder: '请输入员工姓名'
  },
  {
    key: 'employeeNo',
    label: '工号',
    type: 'input',
    placeholder: '请输入员工工号'
  },
  {
    key: 'department',
    label: '部门',
    type: 'select',
    multiple: true,
    filterable: true,
    options: [
      { label: '技术部', value: 'tech' },
      { label: '人事部', value: 'hr' },
      { label: '财务部', value: 'finance' },
      { label: '市场部', value: 'marketing' },
      { label: '行政部', value: 'admin' }
    ]
  },
  {
    key: 'position',
    label: '职位',
    type: 'select',
    filterable: true,
    options: [
      { label: '总经理', value: 'ceo' },
      { label: '部门经理', value: 'manager' },
      { label: '主管', value: 'supervisor' },
      { label: '员工', value: 'employee' },
      { label: '实习生', value: 'intern' }
    ]
  },
  {
    key: 'status',
    label: '在职状态',
    type: 'radio',
    options: [
      { label: '在职', value: 'active' },
      { label: '离职', value: 'inactive' },
      { label: '休假', value: 'leave' }
    ]
  },
  {
    key: 'joinDate',
    label: '入职日期',
    type: 'daterange',
    format: 'YYYY-MM-DD',
    valueFormat: 'YYYY-MM-DD'
  },
  {
    key: 'salary',
    label: '薪资范围',
    type: 'numberrange',
    min: 0,
    max: 100000,
    step: 1000
  },
  {
    key: 'education',
    label: '学历',
    type: 'checkbox',
    options: [
      { label: '博士', value: 'phd' },
      { label: '硕士', value: 'master' },
      { label: '本科', value: 'bachelor' },
      { label: '专科', value: 'college' }
    ]
  },
  {
    key: 'skills',
    label: '技能标签',
    type: 'select',
    multiple: true,
    filterable: true,
    options: [
      { label: 'JavaScript', value: 'js' },
      { label: 'Vue.js', value: 'vue' },
      { label: 'React', value: 'react' },
      { label: 'Node.js', value: 'node' },
      { label: 'Python', value: 'python' },
      { label: 'Java', value: 'java' }
    ]
  },
  {
    key: 'isFullTime',
    label: '全职',
    type: 'switch',
    activeText: '是',
    inactiveText: '否'
  }
])

const searchResult = ref<unknown>({})
const eventLogs = ref<Array<{
  time: string
  type: string
  message: string
}>>([])

const hasSearchResult = computed(() => {
  return Object.keys(searchResult.value).length > 0
})

// 获取字段标签
const getFieldLabel = (key: string) => {
  const field = searchFields.value.find(f => f.key === key)
  return field ? field.label : key
}

// 格式化值
   
const formatValue = (value: unknown) => {
  if (Array.isArray(value)) {
    return value.join(', ')
  }
  if (typeof value === 'boolean') {
    return value ? '是' : '否'
  }
  return value || '-'
}

// 添加事件日志
const addLog = (message: string, type = 'primary') => {
  eventLogs.value.unshift({
    time: new Date().toLocaleTimeString(),
    type,
    message
  })
  
  // 限制日志数量
  if (eventLogs.value.length > 20) {
    eventLogs.value = eventLogs.value.slice(0, 20)
  }
}

// 事件处理
   
const handleSearch = (conditions: unknown) => {
  searchResult.value = conditions
  const count = Object.keys(conditions).filter(key => {
    const value = conditions[key]
    return value !== undefined && value !== null && value !== '' &&
           (!Array.isArray(value) || value.length > 0)
  }).length
  addLog(`执行搜索，包含 ${count} 个条件`, 'success')
}

const handleReset = () => {
  searchResult.value = {}
  addLog('重置搜索条件', 'info')
}

   
const handleSaveTemplate = (template: unknown) => {
  addLog(`保存搜索模板：${template.name}`, 'success')
}

   
const handleUpdateTemplate = (template: unknown) => {
  addLog(`更新搜索模板：${template.name}`, 'warning')
}

const handleDeleteTemplate = (templateId: string) => {
  addLog(`删除搜索模板：${templateId}`, 'danger')
}

   
const handleLoadTemplate = (template: unknown) => {
  addLog(`加载搜索模板：${template.name}`, 'primary')
}
</script>

<style lang="scss" scoped>
.advanced-search-demo {
  padding: 20px;
  
  h1 {
    margin-bottom: 20px;
    font-size: 24px;
  }
  
  h3 {
    margin: 0;
    font-size: 18px;
  }
  
  .el-timeline {
    max-height: 400px;
    overflow-y: auto;
    padding: 10px 0;
  }
}
</style>