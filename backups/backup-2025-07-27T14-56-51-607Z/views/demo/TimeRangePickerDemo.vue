<template>
  <div class="time-range-picker-demo">
    <h1>时间范围选择器演示</h1>
    
    <!-- 基础用法 -->
    <el-card class="demo-card">
      <template #header>
        <h3>基础用法</h3>
      </template>
      
      <div class="demo-content">
        <TimeRangePicker
          v-model="timeRange1"
          @change="handleTimeChange"
        />
        
        <div class="result" v-if="timeRange1">
          <p>选择的时间范围：{{ timeRange1[0] }} - {{ timeRange1[1] }}</p>
        </div>
      </div>
    </el-card>
    
    <!-- 快捷选项 -->
    <el-card class="demo-card">
      <template #header>
        <h3>快捷选项和预设</h3>
      </template>
      
      <div class="demo-content">
        <TimeRangePicker
          v-model="timeRange2"
          :show-shortcuts="true"
          :show-presets="true"
          :show-info="true"
          :show-interval-info="true"
        />
        
        <el-alert type="info" :closable="false" style="margin-top: 20px;">
          <template #title>
            <div class="feature-list">
              <h4>功能特性：</h4>
              <ul>
                <li>⏰ <strong>快捷选项</strong> - 上午、下午、全天、加班时段等</li>
                <li>📌 <strong>常用预设</strong> - 早班、中班、晚班等预设时间段</li>
                <li>⏱️ <strong>时长计算</strong> - 自动计算并显示时间跨度</li>
                <li>🌙 <strong>跨天支持</strong> - 支持跨天时间段（如夜班）</li>
                <li>📊 <strong>时段信息</strong> - 显示上午/下午/晚间/跨天时段</li>
                <li>🔢 <strong>精确到分钟</strong> - 支持分钟级别的时间选择</li>
              </ul>
            </div>
          </template>
        </el-alert>
      </div>
    </el-card>
    
    <!-- 高级选项 -->
    <el-card class="demo-card">
      <template #header>
        <h3>高级选项</h3>
      </template>
      
      <div class="demo-content">
        <TimeRangePicker
          v-model="timeRange3"
          :show-advanced="true"
          :min-duration="60"
          :max-duration="480"
          :work-hours="[8, 20]"
        />
        
        <p class="tip">
          * 展开高级选项可以设置：步进间隔、时长限制、工作时间限制等
        </p>
      </div>
    </el-card>
    
    <!-- 自定义格式 -->
    <el-card class="demo-card">
      <template #header>
        <h3>自定义格式</h3>
      </template>
      
      <div class="demo-content">
        <el-row :gutter="20">
          <el-col :span="12">
            <h4>12小时制</h4>
            <TimeRangePicker
              v-model="timeRange4"
              format="hh:mm A"
              value-format="HH:mm"
              :show-shortcuts="false"
            />
          </el-col>
          
          <el-col :span="12">
            <h4>带秒显示</h4>
            <TimeRangePicker
              v-model="timeRange5"
              format="HH:mm:ss"
              value-format="HH:mm:ss"
              :show-shortcuts="false"
            />
          </el-col>
        </el-row>
      </div>
    </el-card>
    
    <!-- 不同尺寸 -->
    <el-card class="demo-card">
      <template #header>
        <h3>不同尺寸</h3>
      </template>
      
      <div class="demo-content">
        <el-row :gutter="20">
          <el-col :span="8">
            <h4>大尺寸</h4>
            <TimeRangePicker
              v-model="timeRange6"
              size="large"
              :show-shortcuts="false"
              :show-presets="false"
            />
          </el-col>
          
          <el-col :span="8">
            <h4>默认尺寸</h4>
            <TimeRangePicker
              v-model="timeRange7"
              size="default"
              :show-shortcuts="false"
              :show-presets="false"
            />
          </el-col>
          
          <el-col :span="8">
            <h4>小尺寸</h4>
            <TimeRangePicker
              v-model="timeRange8"
              size="small"
              :show-shortcuts="false"
              :show-presets="false"
            />
          </el-col>
        </el-row>
      </div>
    </el-card>
    
    <!-- 箭头控制 -->
    <el-card class="demo-card">
      <template #header>
        <h3>箭头控制模式</h3>
      </template>
      
      <div class="demo-content">
        <TimeRangePicker
          v-model="timeRange9"
          :arrow-control="true"
          :show-shortcuts="false"
        />
        
        <p class="tip">* 使用箭头按钮调整时间，适合触屏设备</p>
      </div>
    </el-card>
    
    <!-- 业务场景示例 -->
    <el-card class="demo-card">
      <template #header>
        <h3>业务场景示例</h3>
      </template>
      
      <div class="demo-content">
        <el-form :model="scheduleForm" label-width="120px">
          <el-form-item label="工作时间">
            <TimeRangePicker
              v-model="scheduleForm.workTime"
              :shortcuts="workShortcuts"
              :show-presets="false"
            />
          </el-form-item>
          
          <el-form-item label="会议时间">
            <TimeRangePicker
              v-model="scheduleForm.meetingTime"
              :shortcuts="meetingShortcuts"
              :min-duration="30"
              :max-duration="180"
              :show-advanced="true"
            />
          </el-form-item>
          
          <el-form-item label="值班时间">
            <TimeRangePicker
              v-model="scheduleForm.dutyTime"
              :shortcuts="dutyShortcuts"
            />
          </el-form-item>
          
          <el-form-item label="培训时段">
            <TimeRangePicker
              v-model="scheduleForm.trainingTime"
              :work-hours="[9, 17]"
              :show-advanced="true"
            />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="saveSchedule">保存排班</el-button>
            <el-button @click="resetSchedule">重置</el-button>
          </el-form-item>
        </el-form>
        
        <div v-if="scheduleResult" class="schedule-result">
          <h4>排班结果：</h4>
          <pre>{{ JSON.stringify(scheduleResult, null, 2) }}</pre>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'TimeRangePickerDemo'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive } from 'vue'
import HrTimeRangePicker from '@/components/common/HrTimeRangePicker.vue'
import { ElMessage } from 'element-plus'

// 基础用法
const timeRange1 = ref<[string, string] | null>(null)

// 快捷选项
const timeRange2 = ref<[string, string] | null>(null)

// 高级选项
const timeRange3 = ref<[string, string] | null>(null)

// 自定义格式
const timeRange4 = ref<[string, string] | null>(null)
const timeRange5 = ref<[string, string] | null>(null)

// 不同尺寸
const timeRange6 = ref<[string, string] | null>(null)
const timeRange7 = ref<[string, string] | null>(null)
const timeRange8 = ref<[string, string] | null>(null)

// 箭头控制
const timeRange9 = ref<[string, string] | null>(null)

// 业务场景
const scheduleForm = reactive({
  workTime: null as [string, string] | null,
  meetingTime: null as [string, string] | null,
  dutyTime: null as [string, string] | null,
  trainingTime: null as [string, string] | null
})

const scheduleResult = ref<unknown>(null)

// 工作时间快捷选项
const workShortcuts = [
  {
    text: '标准工时',
    value: () => ['09:00', '18:00'] as [string, string]
  },
  {
    text: '弹性工时',
    value: () => ['08:00', '17:00'] as [string, string]
  },
  {
    text: '夏季工时',
    value: () => ['08:30', '17:30'] as [string, string]
  }
]

// 会议时间快捷选项
const meetingShortcuts = [
  {
    text: '30分钟',
    value: () => {
      const now = new Date()
      const start = `${now.getHours().toString().padStart(2, '0')}:${Math.floor(now.getMinutes() / 15) * 15}`
      const end = new Date(now.getTime() + 30 * 60000)
      return [start, `${end.getHours().toString().padStart(2, '0')}:${end.getMinutes().toString().padStart(2, '0')}`] as [string, string]
    }
  },
  {
    text: '1小时',
    value: () => {
      const now = new Date()
      const start = `${now.getHours().toString().padStart(2, '0')}:${Math.floor(now.getMinutes() / 15) * 15}`
      const end = new Date(now.getTime() + 60 * 60000)
      return [start, `${end.getHours().toString().padStart(2, '0')}:${end.getMinutes().toString().padStart(2, '0')}`] as [string, string]
    }
  },
  {
    text: '2小时',
    value: () => {
      const now = new Date()
      const start = `${now.getHours().toString().padStart(2, '0')}:${Math.floor(now.getMinutes() / 15) * 15}`
      const end = new Date(now.getTime() + 120 * 60000)
      return [start, `${end.getHours().toString().padStart(2, '0')}:${end.getMinutes().toString().padStart(2, '0')}`] as [string, string]
    }
  }
]

// 值班时间快捷选项
const dutyShortcuts = [
  {
    text: '白班',
    value: () => ['08:00', '20:00'] as [string, string]
  },
  {
    text: '夜班',
    value: () => ['20:00', '08:00'] as [string, string]
  },
  {
    text: '全天',
    value: () => ['00:00', '23:59'] as [string, string]
  }
]

// 处理时间变化
const handleTimeChange = (value: [string, string] | null) => {
  console.log('时间范围变化：', value)
  if (value) {
    ElMessage.info(`选择了 ${value[0]} - ${value[1]}`)
  }
}

// 保存排班
const saveSchedule = () => {
  if (!scheduleForm.workTime) {
    ElMessage.warning('请选择工作时间')
    return
  }
  
  scheduleResult.value = {
    ...scheduleForm,
    保存时间: new Date().toLocaleString()
  }
  
  ElMessage.success('排班信息已保存')
}

// 重置排班
const resetSchedule = () => {
  scheduleForm.workTime = null
  scheduleForm.meetingTime = null
  scheduleForm.dutyTime = null
  scheduleForm.trainingTime = null
  scheduleResult.value = null
  ElMessage.info('已重置所有时间设置')
}
</script>

<style lang="scss" scoped>
.time-range-picker-demo {
  padding: 20px;
  
  h1 {
    margin-bottom: 20px;
    font-size: 24px;
    color: #303133;
  }
  
  .demo-card {
    margin-bottom: 20px;
    
    h3 {
      margin: 0;
      font-size: 18px;
      color: #303133;
    }
    
    h4 {
      margin: 0 0 10px 0;
      font-size: 14px;
      color: #606266;
    }
  }
  
  .demo-content {
    .result {
      margin-top: 20px;
      padding: 15px;
      background: #f5f7fa;
      border-radius: 4px;
      
      p {
        margin: 0;
        color: #606266;
      }
    }
    
    .tip {
      margin-top: 10px;
      font-size: 12px;
      color: #909399;
    }
    
    .feature-list {
      h4 {
        margin: 0 0 10px 0;
        font-size: 14px;
        color: #303133;
      }
      
      ul {
        margin: 0;
        padding-left: 20px;
        
        li {
          line-height: 1.8;
          color: #606266;
          
          strong {
            color: #303133;
          }
        }
      }
    }
    
    .schedule-result {
      margin-top: 20px;
      padding: 15px;
      background: #f5f7fa;
      border-radius: 4px;
      
      h4 {
        margin: 0 0 10px 0;
        color: #303133;
      }
      
      pre {
        margin: 0;
        font-size: 12px;
        color: #606266;
        white-space: pre-wrap;
      }
    }
  }
}

@media (max-width: 768px) {
  .time-range-picker-demo {
    padding: 10px;
    
    .el-row {
      .el-col {
        margin-bottom: 20px;
      }
    }
  }
}
</style>