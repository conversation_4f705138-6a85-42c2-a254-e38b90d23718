<template>
  <div class="step-form-demo">
    <hr-page-header title="分步表单优化演示" :show-back="true">
      演示分步表单、进度追踪、数据暂存等功能
    </hr-page-header>

    <!-- 基础示例 -->
    <el-card class="demo-section">
      <template #header>
        <span>员工信息录入（分步表单）</span>
      </template>
      
      <StepForm
        v-model="currentStep"
        :steps="formSteps"
        :form-data="formData"
        :show-progress="true"
        :show-save="true"
        :show-sidebar="true"
        :auto-save="autoSave"
        @prev="handlePrev"
        @next="handleNext"
        @submit="handleSubmit"
        @save="handleSave"
        @cancel="handleCancel"
      >
        <!-- 步骤1：基本信息 -->
        <template #step-0="{ data }">
          <el-form
            ref="basicFormRef"
            :model="data"
            :rules="basicRules"
            label-width="120px"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="姓名" prop="fullName" required>
                  <el-input v-model="data.fullName" placeholder="请输入姓名"   />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="工号" prop="employeeNumber" required>
                  <el-input v-model="data.employeeNumber" placeholder="请输入工号"   />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="性别" prop="gender" required>
                  <el-radio-group v-model="data.gender">
                    <el-radio label="male">男</el-radio>
                    <el-radio label="female">女</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="出生日期" prop="dateOfBirth" required>
                  <el-date-picker
                    v-model="data.dateOfBirth"
                    type="date"
                    placeholder="选择日期"
                    style="width: 100%;"
                   />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="证件类型" prop="idType" required>
                  <el-select v-model="data.idType" placeholder="请选择">
                    <el-option label="身份证" value="idCard"  />
                    <el-option label="护照" value="passport"  />
                    <el-option label="港澳通行证" value="hkMacaoPass"  />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="证件号码" prop="idNumber" required>
                  <el-input v-model="data.idNumber" placeholder="请输入证件号码"   />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="手机号" prop="phoneNumber" required>
                  <el-input v-model="data.phoneNumber" placeholder="请输入手机号"   />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="邮箱" prop="email">
                  <el-input v-model="data.email" placeholder="请输入邮箱"   />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="联系地址" prop="contactAddress">
                  <el-input v-model="data.contactAddress" placeholder="请输入联系地址"   />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </template>

        <!-- 步骤2：工作信息 -->
        <template #step-1="{ data }">
          <el-form
            ref="workFormRef"
            :model="data"
            :rules="workRules"
            label-width="120px"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="所属部门" prop="department" required>
                  <el-cascader
                    v-model="data.department"
                    :options="departmentOptions"
                    placeholder="请选择部门"
                    style="width: 100%;"
                   />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="岗位" prop="position" required>
                  <el-select v-model="data.position" placeholder="请选择岗位">
                    <el-option label="工程师" value="engineer"  />
                    <el-option label="经理" value="manager"  />
                    <el-option label="主管" value="supervisor"  />
                    <el-option label="专员" value="specialist"  />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="职级" prop="jobLevel">
                  <el-select v-model="data.jobLevel" placeholder="请选择职级">
                    <el-option label="P5" value="P5"  />
                    <el-option label="P6" value="P6"  />
                    <el-option label="P7" value="P7"  />
                    <el-option label="P8" value="P8"  />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="入职日期" prop="hireDate" required>
                  <el-date-picker
                    v-model="data.hireDate"
                    type="date"
                    placeholder="选择入职日期"
                    style="width: 100%;"
                   />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="合同类型" prop="contractType">
                  <el-select v-model="data.contractType" placeholder="请选择">
                    <el-option label="固定期限" value="fixed"  />
                    <el-option label="无固定期限" value="permanent"  />
                    <el-option label="实习协议" value="internship"  />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="试用期" prop="probationPeriod">
                  <el-select v-model="data.probationPeriod" placeholder="请选择">
                    <el-option label="无试用期" value="0"  />
                    <el-option label="1个月" value="1"  />
                    <el-option label="3个月" value="3"  />
                    <el-option label="6个月" value="6"  />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </template>

        <!-- 步骤3：教育背景 -->
        <template #step-2="{ data }">
          <div class="education-section">
            <div class="section-header">
              <span>教育经历</span>
              <el-button type="primary" @click="addEducation">
                <el-icon><Plus /></el-icon>
                添加教育经历
              </el-button>
            </div>
            
            <div v-if="data.educationList && data.educationList.length > 0" class="education-list">
              <el-card
                v-for="(edu, index) in data.educationList"
                :key="index"
                class="education-item"
              >
                <template #header>
                  <div class="card-header">
                    <span>教育经历 {{ index + 1 }}</span>
                    <el-button type="danger" text @click="removeEducation(index)">
                      删除
                    </el-button>
                  </div>
                </template>
                
                <el-form :model="edu" label-width="100px">
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="学校名称" required>
                        <el-input v-model="edu.school" placeholder="请输入学校名称"   />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="学历" required>
                        <el-select v-model="edu.degree" placeholder="请选择">
                          <el-option label="博士" value="doctor"  />
                          <el-option label="硕士" value="master"  />
                          <el-option label="本科" value="bachelor"  />
                          <el-option label="专科" value="college"  />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="专业">
                        <el-input v-model="edu.major" placeholder="请输入专业"   />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="时间段">
                        <el-date-picker
                          v-model="edu.period"
                          type="daterange"
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                          style="width: 100%;"
                         />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-card>
            </div>
            
            <el-empty v-else description="暂无教育经历，请添加"  />
          </div>
        </template>

        <!-- 步骤4：确认信息 -->
        <template #step-3="{ data }">
          <div class="confirm-section">
            <el-alert
              title="请确认以下信息是否正确"
              type="info"
              :closable="false"
              show-icon
             />
            
            <el-descriptions :column="2" border class="info-section">
              <el-descriptions-item label="姓名">{{ data.fullName }}</el-descriptions-item>
              <el-descriptions-item label="工号">{{ data.employeeNumber }}</el-descriptions-item>
              <el-descriptions-item label="性别">{{ data.gender === 'male' ? '男' : '女' }}</el-descriptions-item>
              <el-descriptions-item label="出生日期">{{ data.dateOfBirth }}</el-descriptions-item>
              <el-descriptions-item label="证件类型">{{ getIdTypeText(data.idType) }}</el-descriptions-item>
              <el-descriptions-item label="证件号码">{{ data.idNumber }}</el-descriptions-item>
              <el-descriptions-item label="手机号">{{ data.phoneNumber }}</el-descriptions-item>
              <el-descriptions-item label="邮箱">{{ data.email }}</el-descriptions-item>
              <el-descriptions-item label="联系地址" :span="2">{{ data.contactAddress }}</el-descriptions-item>
            </el-descriptions>
            
            <el-descriptions :column="2" border class="info-section">
              <el-descriptions-item label="所属部门">{{ getDepartmentText(data.department) }}</el-descriptions-item>
              <el-descriptions-item label="岗位">{{ getPositionText(data.position) }}</el-descriptions-item>
              <el-descriptions-item label="职级">{{ data.jobLevel }}</el-descriptions-item>
              <el-descriptions-item label="入职日期">{{ data.hireDate }}</el-descriptions-item>
              <el-descriptions-item label="合同类型">{{ getContractTypeText(data.contractType) }}</el-descriptions-item>
              <el-descriptions-item label="试用期">{{ data.probationPeriod }}个月</el-descriptions-item>
            </el-descriptions>
            
            <div v-if="data.educationList && data.educationList.length > 0" class="education-summary">
              <h4>教育经历</h4>
              <el-timeline>
                <el-timeline-item
                  v-for="(edu, index) in data.educationList"
                  :key="index"
                  :timestamp="edu.period ? `${edu.period[0]} - ${edu.period[1]}` : ''"
                >
                  <p><strong>{{ edu.school }}</strong></p>
                  <p>{{ edu.degree }} - {{ edu.major }}</p>
                </el-timeline-item>
              </el-timeline>
            </div>
          </div>
        </template>
      </StepForm>
    </el-card>

    <!-- 配置选项 -->
    <el-card class="demo-section">
      <template #header>
        <span>配置选项</span>
      </template>
      
      <el-form label-width="120px">
        <el-form-item label="自动保存">
          <el-switch v-model="autoSave"  />
          <span class="form-tip">开启后会自动保存表单数据</span>
        </el-form-item>
        <el-form-item label="当前步骤">
          <el-radio-group v-model="currentStep">
            <el-radio :label="0">基本信息</el-radio>
            <el-radio :label="1">工作信息</el-radio>
            <el-radio :label="2">教育背景</el-radio>
            <el-radio :label="3">确认信息</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="预填数据">
          <el-button @click="fillTestData">填充测试数据</el-button>
          <el-button @click="clearData">清空数据</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 功能特性 -->
    <el-card class="demo-section">
      <template #header>
        <span>功能特性</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <h4>步骤管理</h4>
          <ul>
            <li>清晰的步骤指示和进度展示</li>
            <li>支持步骤跳转和回退</li>
            <li>步骤验证和错误提示</li>
            <li>侧边栏快速导航</li>
          </ul>
        </el-col>
        <el-col :span="8">
          <h4>数据管理</h4>
          <ul>
            <li>表单数据自动保存</li>
            <li>草稿暂存功能</li>
            <li>数据恢复机制</li>
            <li>字段完成度统计</li>
          </ul>
        </el-col>
        <el-col :span="8">
          <h4>用户体验</h4>
          <ul>
            <li>平滑的切换动画</li>
            <li>进度条实时更新</li>
            <li>操作确认提示</li>
            <li>响应式布局适配</li>
          </ul>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'StepFormDemo'
})
 
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import { Plus, User, Briefcase, School, Check } from '@element-plus/icons-vue'
import HrPageHeader from '@/components/common/HrPageHeader.vue'
import HrStepForm from '@/components/form/HrStepForm.vue'
import type { StepConfig } from '@/components/form/StepForm.vue'

// 表单引用
const basicFormRef = ref()
const workFormRef = ref()

// 配置
const autoSave = ref(true)
const currentStep = ref(0)

// 表单步骤配置
const formSteps: StepConfig[] = [
  {
    title: '基本信息',
    description: '填写员工基本信息',
    icon: User,
    fields: ['fullName', 'employeeNumber', 'gender', 'dateOfBirth', 'idType', 'idNumber', 'phoneNumber'],
    validator: async () => {
      if (!basicFormRef.value) return true
      try {
        await basicFormRef.value.validate()
        return true
      } catch {
        return false
      }
    }
  },
  {
    title: '工作信息',
    description: '填写工作相关信息',
    icon: Briefcase,
    fields: ['department', 'position', 'hireDate'],
    validator: async () => {
      if (!workFormRef.value) return true
      try {
        await workFormRef.value.validate()
        return true
      } catch {
        return false
      }
    }
  },
  {
    title: '教育背景',
    description: '填写教育经历',
    icon: School,
    fields: ['educationList'],
    validator: async () => {
      if (!formData.educationList || formData.educationList.length === 0) {
        ElMessage.error('请至少添加一条教育经历')
        return false
      }
      return true
    }
  },
  {
    title: '确认信息',
    description: '确认填写的信息',
    icon: Check,
    fields: [],
    validator: async () => {
      return true
    }
  }
]

// 表单数据
const formData = reactive({
  // 基本信息
  fullName: '',
  employeeNumber: '',
  gender: '',
  dateOfBirth: '',
  idType: '',
  idNumber: '',
  phoneNumber: '',
  email: '',
  contactAddress: '',
  // 工作信息
  department: [],
  position: '',
  jobLevel: '',
  hireDate: '',
  contractType: '',
  probationPeriod: '',
  // 教育背景
  educationList: []
})

// 表单验证规则
const basicRules = {
  fullName: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  employeeNumber: [
    { required: true, message: '请输入工号', trigger: 'blur' }
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ],
  dateOfBirth: [
    { required: true, message: '请选择出生日期', trigger: 'change' }
  ],
  idType: [
    { required: true, message: '请选择证件类型', trigger: 'change' }
  ],
  idNumber: [
    { required: true, message: '请输入证件号码', trigger: 'blur' }
  ],
  phoneNumber: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
  ]
}

const workRules = {
  department: [
    { required: true, message: '请选择部门', trigger: 'change' }
  ],
  position: [
    { required: true, message: '请选择岗位', trigger: 'change' }
  ],
  hireDate: [
    { required: true, message: '请选择入职日期', trigger: 'change' }
  ]
}

// 部门选项
const departmentOptions = [
  {
    value: 'tech',
    label: '技术部',
    children: [
      { value: 'frontend', label: '前端组' },
      { value: 'backend', label: '后端组' },
      { value: 'test', label: '测试组' }
    ]
  },
  {
    value: 'product',
    label: '产品部',
    children: [
      { value: 'design', label: '设计组' },
      { value: 'pm', label: '产品组' }
    ]
  }
]

// 添加教育经历
const addEducation = () => {
  if (!formData.educationList) {
    formData.educationList = []
  }
  formData.educationList.push({
    school: '',
    degree: '',
    major: '',
    period: []
  })
}

// 删除教育经历
const removeEducation = (index: number) => {
  formData.educationList.splice(index, 1)
}

// 事件处理
const handlePrev = (step: number) => {
  console.log('Previous step:', step)
}

const handleNext = (step: number) => {
  console.log('Next step:', step)
}

   
const handleSubmit = async (data: unknown) => {
  console.log('Submit data:', data)
  ElMessage.success('提交成功')
  ElNotification({
    title: '提交成功',
    message: '员工信息已成功录入系统',
    type: 'success'
  })
}

   
const handleSave = (data: unknown) => {
  console.log('Save data:', data)
  // 模拟保存到本地存储
  localStorage.setItem('employee-form-draft', JSON.stringify(data))
}

const handleCancel = () => {
  ElMessage.info('已取消')
}

// 填充测试数据
const fillTestData = () => {
  Object.assign(formData, {
    fullName: '张三',
    employeeNumber: 'EMP001',
    gender: 'male',
    dateOfBirth: '1990-01-01',
    idType: 'idCard',
    idNumber: '330106199001010001',
    phoneNumber: '13800138000',
    email: '<EMAIL>',
    contactAddress: '浙江省杭州市西湖区某某路123号',
    department: ['tech', 'frontend'],
    position: 'engineer',
    jobLevel: 'P6',
    hireDate: '2024-01-01',
    contractType: 'fixed',
    probationPeriod: '3',
    educationList: [
      {
        school: '浙江大学',
        degree: 'master',
        major: '计算机科学与技术',
        period: ['2010-09-01', '2014-07-01']
      }
    ]
  })
  ElMessage.success('已填充测试数据')
}

// 清空数据
const clearData = () => {
  Object.assign(formData, {
    fullName: '',
    employeeNumber: '',
    gender: '',
    dateOfBirth: '',
    idType: '',
    idNumber: '',
    phoneNumber: '',
    email: '',
    contactAddress: '',
    department: [],
    position: '',
    jobLevel: '',
    hireDate: '',
    contractType: '',
    probationPeriod: '',
    educationList: []
  })
  localStorage.removeItem('employee-form-draft')
  ElMessage.success('已清空数据')
}

// 工具函数
const getIdTypeText = (type: string) => {
  const map: Record<string, string> = {
    idCard: '身份证',
    passport: '护照',
    hkMacaoPass: '港澳通行证'
  }
  return map[type] || type
}

const getDepartmentText = (dept: string[]) => {
  if (!dept || dept.length === 0) return ''
  return dept.join(' / ')
}

const getPositionText = (position: string) => {
  const map: Record<string, string> = {
    engineer: '工程师',
    manager: '经理',
    supervisor: '主管',
    specialist: '专员'
  }
  return map[position] || position
}

const getContractTypeText = (type: string) => {
  const map: Record<string, string> = {
    fixed: '固定期限',
    permanent: '无固定期限',
    internship: '实习协议'
  }
  return map[type] || type
}

// 初始化
onMounted(() => {
  // 尝试恢复草稿
  const draft = localStorage.getItem('employee-form-draft')
  if (draft) {
    try {
      const data = JSON.parse(draft)
      Object.assign(formData, data)
      ElMessage.info('已恢复上次的草稿')
    } catch (__e) {
      console.error('Failed to restore draft:', e)
    }
  }
})
</script>

<style lang="scss" scoped>
.step-form-demo {
  padding: 20px;
  
  .demo-section {
    margin-bottom: 20px;
    
    .form-tip {
      margin-left: 12px;
      font-size: 12px;
      color: var(--el-text-color-secondary);
    }
  }
  
  .education-section {
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      
      span {
        font-size: 16px;
        font-weight: 500;
      }
    }
    
    .education-list {
      .education-item {
        margin-bottom: 16px;
        
        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
      }
    }
  }
  
  .confirm-section {
    .el-alert {
      margin-bottom: 20px;
    }
    
    .info-section {
      margin-bottom: 20px;
    }
    
    .education-summary {
      h4 {
        margin: 20px 0 16px;
        font-size: 16px;
        font-weight: 500;
      }
    }
  }
  
  h4 {
    margin: 0 0 12px;
    font-size: 16px;
    color: var(--el-text-color-primary);
  }
  
  ul {
    margin: 0;
    padding-left: 20px;
    
    li {
      margin-bottom: 8px;
      color: var(--el-text-color-regular);
    }
  }
}
</style>