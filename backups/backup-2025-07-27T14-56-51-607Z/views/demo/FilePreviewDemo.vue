<template>
  <div class="file-preview-demo">
    <hr-page-header title="文件预览演示" :show-back="true">
      支持多种文件格式的预览组件，包括图片、视频、音频、文档、代码等
    </hr-page-header>

    <!-- 图片预览 -->
    <el-card>
      <template #header>
        <span>图片预览</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="12">
          <h4>JPG图片</h4>
          <FilePreview
            :file="imageFiles.jpg"
            :max-height="400"
          />
        </el-col>
        <el-col :span="12">
          <h4>SVG矢量图</h4>
          <FilePreview
            :file="imageFiles.svg"
            :max-height="400"
          />
        </el-col>
      </el-row>
    </el-card>

    <!-- 视频预览 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <span>视频预览</span>
      </template>
      <FilePreview
        :file="videoFile"
        :max-height="500"
        :controls="true"
        :muted="true"
      />
    </el-card>

    <!-- 音频预览 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <span>音频预览</span>
      </template>
      <FilePreview
        :file="audioFile"
        :max-height="300"
        :show-waveform="false"
      />
    </el-card>

    <!-- 文档预览 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <span>文档预览</span>
      </template>
      <el-tabs v-model="docTab">
        <el-tab-pane label="PDF文档" name="pdf">
          <FilePreview
            :file="docFiles.pdf"
            :max-height="600"
          />
        </el-tab-pane>
        <el-tab-pane label="Word文档" name="word">
          <FilePreview
            :file="docFiles.word"
            :max-height="600"
          />
        </el-tab-pane>
        <el-tab-pane label="Excel表格" name="excel">
          <FilePreview
            :file="docFiles.excel"
            :max-height="600"
          />
        </el-tab-pane>
        <el-tab-pane label="CSV数据" name="csv">
          <FilePreview
            :file="docFiles.csv"
            :max-height="600"
          />
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 代码预览 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <span>代码预览</span>
      </template>
      <el-tabs v-model="codeTab">
        <el-tab-pane label="JavaScript" name="js">
          <FilePreview
            :file="codeFiles.js"
            :max-height="500"
            :show-line-numbers="true"
          />
        </el-tab-pane>
        <el-tab-pane label="Vue组件" name="vue">
          <FilePreview
            :file="codeFiles.vue"
            :max-height="500"
            :show-line-numbers="true"
          />
        </el-tab-pane>
        <el-tab-pane label="Python" name="py">
          <FilePreview
            :file="codeFiles.py"
            :max-height="500"
            :show-line-numbers="true"
          />
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 特殊格式预览 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <span>特殊格式预览</span>
      </template>
      <el-tabs v-model="specialTab">
        <el-tab-pane label="Markdown" name="markdown">
          <FilePreview
            :file="specialFiles.markdown"
            :max-height="600"
          />
        </el-tab-pane>
        <el-tab-pane label="JSON" name="json">
          <FilePreview
            :file="specialFiles.json"
            :max-height="600"
          />
        </el-tab-pane>
        <el-tab-pane label="压缩包" name="zip">
          <FilePreview
            :file="specialFiles.zip"
            :max-height="600"
          />
        </el-tab-pane>
        <el-tab-pane label="3D模型" name="model">
          <FilePreview
            :file="specialFiles.model"
            :max-height="600"
          />
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 错误处理 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <span>错误处理</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="12">
          <h4>文件不存在</h4>
          <FilePreview
            :file="errorFiles.notFound"
            :max-height="300"
          />
        </el-col>
        <el-col :span="12">
          <h4>不支持的格式</h4>
          <FilePreview
            :file="errorFiles.unsupported"
            :max-height="300"
          />
        </el-col>
      </el-row>
    </el-card>

    <!-- 配置选项 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <span>配置选项演示</span>
      </template>
      <el-form label-width="120px">
        <el-form-item label="显示信息">
          <el-switch v-model="options.showInfo"  />
        </el-form-item>
        <el-form-item label="显示工具栏">
          <el-switch v-model="options.showToolbar"  />
        </el-form-item>
        <el-form-item label="显示行号">
          <el-switch v-model="options.showLineNumbers"  />
        </el-form-item>
        <el-form-item label="最大高度">
          <el-input-number v-model="options.maxHeight" :min="200" :max="800" :step="100"   />
        </el-form-item>
      </el-form>
      
      <el-divider   />
      
      <h4>自定义配置预览</h4>
      <FilePreview
        :file="demoFile"
        :max-height="options.maxHeight"
        :show-info="options.showInfo"
        :show-toolbar="options.showToolbar"
        :show-line-numbers="options.showLineNumbers"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive } from 'vue'
import HrPageHeader from '@/components/common/HrPageHeader.vue'
import HrFilePreview from '@/components/common/HrFilePreview.vue'

// 标签页
const docTab = ref('pdf')
const codeTab = ref('js')
const specialTab = ref('markdown')

// 配置选项
const options = reactive({
  showInfo: true,
  showToolbar: true,
  showLineNumbers: true,
  maxHeight: 400
})

// 演示文件
const demoFile = {
  name: 'HrDemo.vue',
  size: 2048,
  url: '/api/files/demo.vue'
}

// 图片文件
const imageFiles = {
  jpg: {
    name: '产品图片.jpg',
    size: 1024 * 512,
    url: 'https://picsum.photos/800/600'
  },
  svg: {
    name: 'logo.svg',
    size: 8192,
    url: 'https://element-plus.org/images/element-plus-logo.svg'
  }
}

// 视频文件
const videoFile = {
  name: '产品演示.mp4',
  size: 1024 * 1024 * 10,
  url: 'https://www.w3schools.com/html/mov_bbb.mp4'
}

// 音频文件
const audioFile = {
  name: '背景音乐.mp3',
  size: 1024 * 1024 * 3,
  url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3'
}

// 文档文件
const docFiles = {
  pdf: {
    name: '用户手册.pdf',
    size: 1024 * 1024 * 2,
    url: '/api/files/manual.pdf'
  },
  word: {
    name: '项目方案.docx',
    size: 1024 * 800,
    url: '/api/files/proposal.docx'
  },
  excel: {
    name: '数据统计.xlsx',
    size: 1024 * 500,
    url: '/api/files/statistics.xlsx'
  },
  csv: {
    name: '员工名单.csv',
    size: 1024 * 100,
    url: '/api/files/employees.csv'
  }
}

// 代码文件
const codeFiles = {
  js: {
    name: 'utils.js',
    size: 4096,
    url: '/api/files/utils.js'
  },
  vue: {
    name: 'Component.vue',
    size: 8192,
    url: '/api/files/Component.vue'
  },
  py: {
    name: 'analysis.py',
    size: 6144,
    url: '/api/files/analysis.py'
  }
}

// 特殊格式文件
const specialFiles = {
  markdown: {
    name: 'README.md',
    size: 10240,
    url: '/api/files/README.md'
  },
  json: {
    name: 'config.json',
    size: 2048,
    url: '/api/files/config.json'
  },
  zip: {
    name: '项目文件.zip',
    size: 1024 * 1024 * 5,
    url: '/api/files/project.zip'
  },
  model: {
    name: 'model.obj',
    size: 1024 * 1024 * 8,
    url: '/api/files/model.obj'
  }
}

// 错误文件
const errorFiles = {
  notFound: {
    name: 'missing.txt',
    size: 0,
    url: '/api/files/not-found.txt'
  },
  unsupported: {
    name: 'unknown.xyz',
    size: 1024,
    url: '/api/files/unknown.xyz'
  }
}
</script>

<style lang="scss" scoped>
.file-preview-demo {
  padding: 20px;
  
  h4 {
    margin-bottom: 16px;
    color: #303133;
  }
  
  .el-card {
    :deep(.el-card__body) {
      padding: 20px;
    }
  }
  
  .el-tabs {
    :deep(.el-tabs__content) {
      padding-top: 20px;
    }
  }
}
</style>