<template>
  <div class="lazy-load-demo">
    <h1>图片懒加载示例</h1>
    
    <!-- 基本使用 -->
    <section>
      <h2>基本使用</h2>
      <div class="demo-grid">
        <div v-for="i in 20" :key="`basic-${i}`" class="demo-item">
          <img
            v-lazy="`https://picsum.photos/400/300?random=${i}`"
            alt="测试图片"
            class="demo-image"
          />
          <p>图片 {{ i }}</p>
        </div>
      </div>
    </section>
    
    <!-- 自定义配置 -->
    <section>
      <h2>自定义加载/错误图片</h2>
      <div class="demo-grid">
        <div v-for="i in 6" :key="`custom-${i}`" class="demo-item">
          <img
            v-lazy="{
              src: `https://picsum.photos/400/300?random=${i + 20}`,
              loading: '/images/loading.gif',
              error: '/images/error.png'
            }"
            alt="自定义图片"
            class="demo-image"
          />
          <p>自定义图片 {{ i }}</p>
        </div>
      </div>
    </section>
    
    <!-- 背景图片懒加载 -->
    <section>
      <h2>背景图片懒加载</h2>
      <div class="demo-grid">
        <div
          v-for="i in 6"
          :key="`bg-${i}`"
          v-lazy="`https://picsum.photos/400/300?random=${i + 30}`"
          class="demo-bg-item"
        >
          <div class="content">
            <h3>背景图片 {{ i }}</h3>
            <p>这是一个使用懒加载的背景图片</p>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 响应式图片 -->
    <section>
      <h2>响应式图片懒加载</h2>
      <div class="demo-responsive">
        <div v-for="i in 4" :key="`responsive-${i}`" class="responsive-item">
          <div class="lazy-image-container" :data-aspect-ratio="aspectRatios[i % 3]">
            <img
              v-lazy="`https://picsum.photos/800/600?random=${i + 40}`"
              alt="响应式图片"
            />
          </div>
          <h3>宽高比: {{ aspectRatios[i % 3] }}</h3>
        </div>
      </div>
    </section>
    
    <!-- 渐进式加载 -->
    <section>
      <h2>渐进式加载效果</h2>
      <div class="demo-grid">
        <div v-for="i in 6" :key="`progressive-${i}`" class="demo-item">
          <img
            v-lazy="`https://picsum.photos/400/300?random=${i + 50}`"
            alt="渐进式图片"
            class="demo-image lazy-progressive"
          />
          <p>渐进式加载 {{ i }}</p>
        </div>
      </div>
    </section>
    
    <!-- 骨架屏效果 -->
    <section>
      <h2>骨架屏效果</h2>
      <div class="demo-grid">
        <div v-for="i in 6" :key="`skeleton-${i}`" class="demo-item">
          <div class="lazy-skeleton" style="height: 200px;">
            <img
              v-lazy="`https://picsum.photos/400/300?random=${i + 60}`"
              alt="骨架屏图片"
              class="demo-image"
              style="height: 100%;"
            />
          </div>
          <p>骨架屏加载 {{ i }}</p>
        </div>
      </div>
    </section>
    
    <!-- 操作按钮 -->
    <div class="demo-actions">
      <el-button type="primary" @click="preloadImages">预加载图片</el-button>
      <el-button @click="clearCache">清除缓存</el-button>
      <el-button @click="refreshImages">刷新图片</el-button>
    </div>
    
    <!-- 性能监控 -->
    <div class="performance-monitor">
      <h3>性能监控</h3>
      <p>已加载图片：{{ loadedImages }} / {{ totalImages }}</p>
      <p>加载耗时：{{ avgLoadTime }}ms</p>
      <el-progress :percentage="loadProgress" :stroke-width="10"  />
    </div>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'LazyLoadDemo'
})
 
import { ref, computed, onMounted, getCurrentInstance } from 'vue'
import { ElMessage } from 'element-plus'

// 获取当前组件实例
const instance = getCurrentInstance()

// 宽高比选项
const aspectRatios = ['16:9', '4:3', '1:1']

// 性能监控数据
const totalImages = ref(0)
const loadedImages = ref(0)
const loadTimes = ref<number[]>([])

// 计算属性
const loadProgress = computed(() => {
  if (totalImages.value === 0) return 0
  return Math.round((loadedImages.value / totalImages.value) * 100)
})

const avgLoadTime = computed(() => {
  if (loadTimes.value.length === 0) return 0
  const sum = loadTimes.value.reduce((a, b) => a + b, 0)
  return Math.round(sum / loadTimes.value.length)
})

// 预加载图片
const preloadImages = () => {
  const urls = [
    'https://picsum.photos/400/300?random=100',
    'https://picsum.photos/400/300?random=101',
    'https://picsum.photos/400/300?random=102'
  ]
  
  urls.forEach(url => {
    instance?.appContext.config.globalProperties.$lazyLoad.preload(url)
  })
  
  ElMessage.success(`预加载 ${urls.length} 张图片`)
}

// 清除缓存
const clearCache = () => {
  instance?.appContext.config.globalProperties.$lazyLoad.clearCache()
  loadedImages.value = 0
  loadTimes.value = []
  ElMessage.success('缓存已清除')
}

// 刷新图片
const refreshImages = () => {
  window.location.reload()
}

// 监听懒加载事件
onMounted(() => {
  // 统计总图片数
  totalImages.value = document.querySelectorAll('[v-lazy]').length
  
  // 监听加载成功事件
   
  document.addEventListener('lazy-loaded', (e: unknown) => {
    loadedImages.value++
    const loadTime = Date.now() - (e.detail.startTime || Date.now())
    loadTimes.value.push(loadTime)
  })
  
  // 监听加载失败事件
   
  document.addEventListener('lazy-error', (e: unknown) => {
    console.error('图片加载失败:', e.detail.src)
  })
})
</script>

<style scoped lang="scss">
.lazy-load-demo {
  padding: 20px;
  
  h1 {
    text-align: center;
    margin-bottom: 40px;
  }
  
  h2 {
    margin: 40px 0 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--el-border-color);
  }
  
  section {
    margin-bottom: 60px;
  }
}

// 网格布局
.demo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.demo-item {
  text-align: center;
  
  .demo-image {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  p {
    margin-top: 10px;
    color: var(--el-text-color-secondary);
  }
}

// 背景图片项
.demo-bg-item {
  height: 200px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background-size: cover;
  background-position: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .content {
    position: relative;
    z-index: 1;
    text-align: center;
    color: white;
    padding: 20px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 8px;
    
    h3 {
      margin: 0 0 10px;
    }
    
    p {
      margin: 0;
    }
  }
}

// 响应式布局
.demo-responsive {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
  
  .responsive-item {
    h3 {
      margin-top: 10px;
      text-align: center;
      color: var(--el-text-color-secondary);
    }
  }
}

// 操作按钮
.demo-actions {
  position: fixed;
  bottom: 100px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 100;
}

// 性能监控
.performance-monitor {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  min-width: 200px;
  
  h3 {
    margin: 0 0 10px;
    font-size: 16px;
  }
  
  p {
    margin: 5px 0;
    font-size: 14px;
    color: var(--el-text-color-secondary);
  }
}

// 响应式适配
@media (max-width: 768px) {
  .demo-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
  }
  
  .demo-actions,
  .performance-monitor {
    display: none;
  }
}
</style>