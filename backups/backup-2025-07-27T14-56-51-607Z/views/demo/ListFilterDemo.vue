<template>
  <div class="list-filter-demo">
    <hr-page-header title="列表筛选优化演示" :show-back="true">
      演示筛选条件持久化、预设管理、激活标签等功能
    </hr-page-header>

    <!-- 基础示例 -->
    <el-card class="demo-section">
      <template #header>
        <span>基础筛选</span>
      </template>
      
      <ListFilter
        :fields="basicFields"
        persist-key="demo-basic"
        @search="handleBasicSearch"
        @reset="handleBasicReset"
      />
      
      <div class="result-display">
        <h4>筛选结果：</h4>
        <el-tag v-if="!basicResult">未执行筛选</el-tag>
        <pre v-else>{{ JSON.stringify(basicResult, null, 2) }}</pre>
      </div>
    </el-card>

    <!-- 高级示例 -->
    <el-card class="demo-section">
      <template #header>
        <span>高级筛选（带预设）</span>
      </template>
      
      <ListFilter
        ref="advancedFilterRef"
        :fields="advancedFields"
        :label-width="120"
        :default-span="12"
        persist-key="demo-advanced"
        @search="handleAdvancedSearch"
        @change="handleAdvancedChange"
      >
        <!-- 自定义字段插槽 -->
        <template #tags="{ value, form }">
          <el-select
            v-model="form.tags"
            multiple
            filterable
            allow-create
            default-first-option
            placeholder="请选择或输入标签"
          >
            <el-option
              v-for="tag in tagOptions"
              :key="tag"
              :label="tag"
              :value="tag"
             />
          </el-select>
        </template>
      </ListFilter>
      
      <div class="result-display">
        <h4>筛选结果：</h4>
        <el-tag v-if="!advancedResult">未执行筛选</el-tag>
        <pre v-else>{{ JSON.stringify(advancedResult, null, 2) }}</pre>
      </div>
    </el-card>

    <!-- 内联示例 -->
    <el-card class="demo-section">
      <template #header>
        <span>内联筛选（自动搜索）</span>
      </template>
      
      <ListFilter
        :fields="inlineFields"
        :inline="true"
        :auto-search="true"
        :show-active-tags="false"
        persist-key="demo-inline"
        @search="handleInlineSearch"
      />
      
      <el-divider   />
      
      <!-- 模拟数据列表 -->
      <el-table :data="filteredData" stripe>
        <el-table-column prop="id" label="ID" width="80"  />
        <el-table-column prop="name" label="姓名"  />
        <el-table-column prop="department" label="部门"  />
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'info'">
              {{ row.status === 'active' ? '在职' : '离职' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="salary" label="薪资">
          <template #default="{ row }">
            ¥{{ row.salary.toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="joinDate" label="入职日期"  />
      </el-table>
    </el-card>

    <!-- 自定义示例 -->
    <el-card class="demo-section">
      <template #header>
        <span>自定义布局</span>
      </template>
      
      <ListFilter
        :fields="customFields"
        :form-inline="true"
        :collapsible="false"
        :show-preset="false"
        @search="handleCustomSearch"
      />
      
      <div class="custom-results">
        <el-row :gutter="16">
          <el-col
            v-for="item in customResults"
            :key="item.id"
            :span="6"
          >
            <el-card class="result-card">
              <div class="result-header">
                <el-avatar :style="{ backgroundColor: item.color }">
                  {{ item.name.charAt(0) }}
                </el-avatar>
                <h4>{{ item.name }}</h4>
              </div>
              <div class="result-info">
                <p>部门：{{ item.department }}</p>
                <p>职位：{{ item.position }}</p>
                <p>等级：{{ item.level }}</p>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 功能说明 -->
    <el-card class="demo-section">
      <template #header>
        <span>功能说明</span>
      </template>
      
      <el-descriptions :column="2" border>
        <el-descriptions-item label="持久化存储">
          <el-tag>localStorage</el-tag>
          筛选条件会自动保存，刷新页面后自动恢复
        </el-descriptions-item>
        <el-descriptions-item label="预设管理">
          <el-tag>支持</el-tag>
          可以保存常用筛选条件为预设，方便快速切换
        </el-descriptions-item>
        <el-descriptions-item label="激活标签">
          <el-tag>可视化</el-tag>
          显示当前激活的筛选条件，支持快速移除
        </el-descriptions-item>
        <el-descriptions-item label="自动搜索">
          <el-tag>可选</el-tag>
          支持输入时自动触发搜索，适合简单筛选
        </el-descriptions-item>
        <el-descriptions-item label="折叠展开">
          <el-tag>支持</el-tag>
          筛选条件较多时可以折叠，节省空间
        </el-descriptions-item>
        <el-descriptions-item label="字段类型">
          <el-tag>丰富</el-tag>
          支持输入框、选择器、日期、数字范围等多种类型
        </el-descriptions-item>
        <el-descriptions-item label="自定义插槽">
          <el-tag>灵活</el-tag>
          支持自定义字段组件，满足特殊需求
        </el-descriptions-item>
        <el-descriptions-item label="响应式布局">
          <el-tag>自适应</el-tag>
          支持栅格布局，自动适应不同屏幕尺寸
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, computed } from 'vue'
import HrPageHeader from '@/components/common/HrPageHeader.vue'
import HrListFilter from '@/components/common/HrListFilter.vue'
import type { FilterField } from '@/components/common/ListFilter.vue'

// 基础字段配置
const basicFields: FilterField[] = [
  {
    prop: 'keyword',
    label: '关键词',
    type: 'input',
    placeholder: '请输入关键词'
  },
  {
    prop: 'status',
    label: '状态',
    type: 'select',
    options: [
      { label: '全部', value: '' },
      { label: '启用', value: 'enabled' },
      { label: '禁用', value: 'disabled' }
    ]
  },
  {
    prop: 'dateRange',
    label: '日期范围',
    type: 'dateRange',
    props: {
      valueFormat: 'YYYY-MM-DD'
    }
  }
]

// 高级字段配置
const advancedFields: FilterField[] = [
  {
    prop: 'name',
    label: '员工姓名',
    type: 'input',
    span: 8
  },
  {
    prop: 'department',
    label: '所属部门',
    type: 'cascader',
    span: 8,
    props: {
      options: [
        {
          value: 'tech',
          label: '技术部',
          children: [
            { value: 'frontend', label: '前端组' },
            { value: 'backend', label: '后端组' },
            { value: 'test', label: '测试组' }
          ]
        },
        {
          value: 'product',
          label: '产品部',
          children: [
            { value: 'design', label: '设计组' },
            { value: 'pm', label: '产品组' }
          ]
        }
      ]
    }
  },
  {
    prop: 'position',
    label: '职位',
    type: 'select',
    span: 8,
    options: [
      { label: '工程师', value: 'engineer' },
      { label: '经理', value: 'manager' },
      { label: '总监', value: 'director' }
    ]
  },
  {
    prop: 'salaryRange',
    label: '薪资范围',
    type: 'numberRange',
    span: 12,
    props: {
      min: 0,
      max: 100000,
      step: 1000
    }
  },
  {
    prop: 'joinDate',
    label: '入职日期',
    type: 'dateRange',
    span: 12,
    props: {
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    prop: 'skills',
    label: '技能标签',
    type: 'multiSelect',
    span: 12,
    options: [
      { label: 'Vue', value: 'vue' },
      { label: 'React', value: 'react' },
      { label: 'Angular', value: 'angular' },
      { label: 'Node.js', value: 'nodejs' },
      { label: 'Python', value: 'python' },
      { label: 'Java', value: 'java' }
    ]
  },
  {
    prop: 'tags',
    label: '自定义标签',
    type: 'custom',
    span: 12,
    slot: 'tags'
  },
  {
    prop: 'isFullTime',
    label: '全职',
    type: 'switch',
    span: 6
  },
  {
    prop: 'hasStock',
    label: '期权',
    type: 'switch',
    span: 6
  }
]

// 内联字段配置
const inlineFields: FilterField[] = [
  {
    prop: 'keyword',
    label: '搜索',
    type: 'input',
    placeholder: '姓名/部门'
  },
  {
    prop: 'status',
    label: '状态',
    type: 'select',
    options: [
      { label: '全部', value: '' },
      { label: '在职', value: 'active' },
      { label: '离职', value: 'inactive' }
    ],
    defaultValue: ''
  },
  {
    prop: 'salary',
    label: '薪资',
    type: 'select',
    options: [
      { label: '全部', value: '' },
      { label: '10k以下', value: '0-10000' },
      { label: '10k-20k', value: '10000-20000' },
      { label: '20k-30k', value: '20000-30000' },
      { label: '30k以上', value: '30000-999999' }
    ],
    defaultValue: ''
  }
]

// 自定义字段配置
const customFields: FilterField[] = [
  {
    prop: 'level',
    label: '等级',
    type: 'select',
    options: [
      { label: 'P1-P3', value: 'junior' },
      { label: 'P4-P6', value: 'middle' },
      { label: 'P7-P9', value: 'senior' }
    ]
  },
  {
    prop: 'team',
    label: '团队',
    type: 'select',
    options: [
      { label: '核心团队', value: 'core' },
      { label: '支撑团队', value: 'support' },
      { label: '创新团队', value: 'innovation' }
    ]
  },
  {
    prop: 'performance',
    label: '绩效',
    type: 'select',
    options: [
      { label: '优秀', value: 'A' },
      { label: '良好', value: 'B' },
      { label: '合格', value: 'C' }
    ]
  }
]

// 标签选项
const tagOptions = ['优秀员工', '技术专家', '团队leader', '新人培训师', '最佳贡献奖']

// 模拟数据
const mockData = [
  { id: 1, name: 'HrHr张三', department: '技术部', status: 'active', salary: 15000, joinDate: '2020-01-01' },
  { id: 2, name: '李四', department: '产品部', status: 'active', salary: 18000, joinDate: '2019-06-15' },
  { id: 3, name: '王五', department: '技术部', status: 'inactive', salary: 20000, joinDate: '2018-03-20' },
  { id: 4, name: '赵六', department: '设计部', status: 'active', salary: 12000, joinDate: '2021-09-10' },
  { id: 5, name: '钱七', department: '技术部', status: 'active', salary: 25000, joinDate: '2017-11-05' },
  { id: 6, name: '孙八', department: '运营部', status: 'active', salary: 16000, joinDate: '2020-07-22' }
]

// 自定义结果数据
const customResultsData = [
  { id: 1, name: '张三', department: '技术部', position: '高级工程师', level: 'P6', color: '#409eff' },
  { id: 2, name: '李四', department: '产品部', position: '产品经理', level: 'P5', color: '#67c23a' },
  { id: 3, name: '王五', department: '设计部', position: '设计总监', level: 'P7', color: '#e6a23c' },
  { id: 4, name: '赵六', department: '技术部', position: '架构师', level: 'P8', color: '#f56c6c' }
]

// Refs
const advancedFilterRef = ref()

// 状态
const basicResult = ref(null)
const advancedResult = ref(null)
const inlineFilters = ref({})
const customFilters = ref({})

// 计算属性
const filteredData = computed(() => {
  let result = [...mockData]
  
  // 关键词筛选
  if (inlineFilters.value.keyword) {
    const keyword = inlineFilters.value.keyword.toLowerCase()
    result = result.filter(item =>
      item.name.toLowerCase().includes(keyword) ||
      item.department.toLowerCase().includes(keyword)
    )
  }
  
  // 状态筛选
  if (inlineFilters.value.status) {
    result = result.filter(item => item.status === inlineFilters.value.status)
  }
  
  // 薪资筛选
  if (inlineFilters.value.salary) {
    const [min, max] = inlineFilters.value.salary.split('-').map(Number)
    result = result.filter(item => item.salary >= min && item.salary <= max)
  }
  
  return result
})

const customResults = computed(() => {
  let result = [...customResultsData]
  
  // 等级筛选
  if (customFilters.value.level) {
    const levelMap = {
      junior: ['P1', 'P2', 'P3'],
      middle: ['P4', 'P5', 'P6'],
      senior: ['P7', 'P8', 'P9']
    }
    const levels = levelMap[customFilters.value.level] || []
    result = result.filter(item => levels.includes(item.level))
  }
  
  // 团队筛选
  if (customFilters.value.team) {
    // 这里简化处理，实际应该有团队映射
    result = result.filter((_, index) => {
      if (customFilters.value.team === 'core') return index < 2
      if (customFilters.value.team === 'support') return index >= 2 && index < 4
      return index >= 4
    })
  }
  
  return result
})

// 处理基础搜索
   
const handleBasicSearch = (filters: unknown) => {
  console.log('基础搜索:', filters)
  basicResult.value =   filters
}

// 处理基础重置
const handleBasicReset = () => {
  console.log('基础重置')
  basicResult.value =   null
}

// 处理高级搜索
   
const handleAdvancedSearch = (filters: unknown) => {
  console.log('高级搜索:', filters)
  advancedResult.value =   filters
}

// 处理高级变化
   
const handleAdvancedChange = (filters: unknown) => {
  console.log('筛选条件变化:', filters)
}

// 处理内联搜索
   
const handleInlineSearch = (filters: unknown) => {
  console.log('内联搜索:', filters)
  inlineFilters.value =   filters
}

// 处理自定义搜索
   
const handleCustomSearch = (filters: unknown) => {
  console.log('自定义搜索:', filters)
  customFilters.value =   filters
}
</script>

<style lang="scss" scoped>
.list-filter-demo {
  padding: 20px;
  
  .demo-section {
    margin-bottom: 20px;
    
    .result-display {
      margin-top: 20px;
      padding: 16px;
      background-color: var(--el-fill-color-light);
      border-radius: 4px;
      
      h4 {
        margin: 0 0 12px;
        font-size: 14px;
        color: var(--el-text-color-regular);
      }
      
      pre {
        margin: 0;
        padding: 12px;
        background-color: var(--el-bg-color);
        border-radius: 4px;
        font-size: 12px;
        font-family: 'Monaco', 'Consolas', monospace;
        overflow-x: auto;
      }
    }
    
    .custom-results {
      margin-top: 20px;
      
      .result-card {
        height: 100%;
        transition: all 0.3s;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .result-header {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 12px;
          
          h4 {
            margin: 0;
            font-size: 16px;
          }
        }
        
        .result-info {
          p {
            margin: 4px 0;
            font-size: 14px;
            color: var(--el-text-color-regular);
          }
        }
      }
    }
  }
}
</style>