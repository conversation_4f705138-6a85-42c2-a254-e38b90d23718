<template>
  <div class="first-screen-demo">
    <h1>首屏加载优化演示</h1>
    
    <!-- 性能指标展示 -->
    <el-card class="metrics-card">
      <template #header>
        <div class="card-header">
          <span>首屏性能指标</span>
          <el-button @click="refreshMetrics" :icon="Refresh" circle   />
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6">
          <el-statistic title="FCP (首次内容绘制)" :value="metrics.FCP" suffix="ms">
            <template #suffix>
              <span style="font-size: 14px">ms</span>
            </template>
          </el-statistic>
        </el-col>
        <el-col :xs="12" :sm="6">
          <el-statistic title="LCP (最大内容绘制)" :value="metrics.LCP" suffix="ms">
            <template #suffix>
              <span style="font-size: 14px">ms</span>
            </template>
          </el-statistic>
        </el-col>
        <el-col :xs="12" :sm="6">
          <el-statistic title="TTI (可交互时间)" :value="metrics.TTI" suffix="ms">
            <template #suffix>
              <span style="font-size: 14px">ms</span>
            </template>
          </el-statistic>
        </el-col>
        <el-col :xs="12" :sm="6">
          <el-statistic title="资源加载数" :value="metrics.resourceCount"  />
        </el-col>
      </el-row>
      
      <div class="performance-chart" ref="performanceChart"></div>
    </el-card>
    
    <!-- 优化技术展示 -->
    <el-card class="techniques-card">
      <template #header>
        <span>首屏优化技术</span>
      </template>
      
      <el-timeline>
        <el-timeline-item timestamp="1. 关键CSS内联" placement="top">
          <el-card>
            <p>将首屏必需的CSS直接内联到HTML中，避免额外的网络请求</p>
            <el-tag type="success">已实现</el-tag>
          </el-card>
        </el-timeline-item>
        
        <el-timeline-item timestamp="2. 资源预加载" placement="top">
          <el-card>
            <p>使用 preload 和 prefetch 提前加载关键资源</p>
            <el-tag type="success">已实现</el-tag>
          </el-card>
        </el-timeline-item>
        
        <el-timeline-item timestamp="3. 延迟加载" placement="top">
          <el-card>
            <p>非关键资源使用 requestIdleCallback 延迟加载</p>
            <el-tag type="success">已实现</el-tag>
          </el-card>
        </el-timeline-item>
        
        <el-timeline-item timestamp="4. 代码分割" placement="top">
          <el-card>
            <p>路由级别的代码分割，按需加载</p>
            <el-tag type="success">已实现</el-tag>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </el-card>
    
    <!-- 资源加载瀑布图 -->
    <el-card class="waterfall-card">
      <template #header>
        <span>资源加载瀑布图</span>
      </template>
      
      <div class="waterfall-container">
        <div 
          v-for="resource in resources" 
          :key="resource.name"
          class="resource-bar"
          :style="getResourceStyle(resource)"
        >
          <div class="resource-name">{{ getResourceName(resource.name) }}</div>
          <div class="resource-time">{{ resource.duration.toFixed(0) }}ms</div>
        </div>
      </div>
    </el-card>
    
    <!-- 优化建议 -->
    <el-card class="suggestions-card">
      <template #header>
        <span>优化建议</span>
      </template>
      
      <el-alert
        v-for="suggestion in suggestions"
        :key="suggestion.title"
        :title="suggestion.title"
        :type="suggestion.type"
        :description="suggestion.description"
        show-icon
        :closable="false"
        style="margin-bottom: 10px"
       />
    </el-card>
    
    <!-- 对比测试 -->
    <el-card class="comparison-card">
      <template #header>
        <span>优化前后对比</span>
      </template>
      
      <div ref="comparisonChart" style="height: 400px;"></div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, onMounted, nextTick } from 'vue'
import { Refresh } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { firstScreenOptimizer } from '@/utils/first-screen'

// 性能指标
const metrics = ref({
  FCP: 0,
  LCP: 0,
  TTI: 0,
  FID: 0,
  resourceCount: 0,
  totalResourceTime: 0
})

// 资源列表
const resources = ref<any[]>([])

// 图表引用
const performanceChart = ref<HTMLElement>()
const comparisonChart = ref<HTMLElement>()

// 优化建议
const suggestions = ref([
  {
    title: '使用 WebP 图片格式',
    type: 'warning',
    description: '将 JPEG/PNG 图片转换为 WebP 格式，可减少 25-35% 的文件大小'
  },
  {
    title: '启用 HTTP/2 推送',
    type: 'info',
    description: '使用 HTTP/2 Server Push 主动推送关键资源'
  },
  {
    title: '优化字体加载',
    type: 'info',
    description: '使用 font-display: swap 避免字体加载时的不可见文本'
  },
  {
    title: '减少 JavaScript 执行时间',
    type: 'warning',
    description: '考虑将计算密集型任务移至 Web Worker'
  }
])

// 刷新性能指标
const refreshMetrics = () => {
  // 获取首屏指标
  const screenMetrics = firstScreenOptimizer.getFirstScreenMetrics()
  
  // 获取 Performance API 数据
  const navTiming = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
  
  metrics.value = {
    FCP: screenMetrics.FCP || 0,
    LCP: getLCP(),
    TTI: getTTI(),
    FID: getFID(),
    resourceCount: screenMetrics.resourceCount,
    totalResourceTime: screenMetrics.totalResourceTime
  }
  
  // 获取资源加载时间
  const resourceEntries = performance.getEntriesByType('resource') as PerformanceResourceTiming[]
  resources.value = resourceEntries
    .filter(entry => entry.duration > 0)
    .sort((a, b) => a.startTime - b.startTime)
    .slice(0, 20) // 只显示前20个资源
  
  // 更新图表
  updatePerformanceChart()
  updateComparisonChart()
}

// 获取 LCP
const getLCP = (): number => {
  let lcp = 0
  const observer = new PerformanceObserver((list) => {
    const entries = list.getEntries()
    const lastEntry = entries[entries.length - 1] as unknown
    lcp = lastEntry.renderTime || lastEntry.loadTime
  })
  
  try {
    observer.observe({ type: 'largest-contentful-paint', buffered: true })
  } catch (__e) {
    // LCP 不支持
  }
  
  return lcp
}

// 获取 TTI
const getTTI = (): number => {
  const navTiming = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
  return navTiming.loadEventEnd - navTiming.fetchStart
}

// 获取 FID
const getFID = (): number => {
  // FID 需要用户交互，这里返回模拟值
  return 50
}

// 获取资源样式
const getResourceStyle = (resource: PerformanceResourceTiming) => {
  const maxDuration = Math.max(...resources.value.map(r => r.duration))
  const width = (resource.duration / maxDuration) * 100
  const left = (resource.startTime / 3000) * 100 // 假设3秒内
  
  return {
    width: `${width}%`,
    left: `${left}%`,
    backgroundColor: getResourceColor(resource)
  }
}

// 获取资源颜色
const getResourceColor = (resource: PerformanceResourceTiming) => {
  if (resource.name.includes('.js')) return '#f56c6c'
  if (resource.name.includes('.css')) return '#67c23a'
  if (resource.name.includes('.png') || resource.name.includes('.jpg')) return '#409eff'
  if (resource.name.includes('.woff') || resource.name.includes('.ttf')) return '#e6a23c'
  return '#909399'
}

// 获取资源名称
const getResourceName = (url: string) => {
  const parts = url.split('/')
  return parts[parts.length - 1] || url
}

// 更新性能图表
const updatePerformanceChart = () => {
  if (!performanceChart.value) return
  
  const chart = echarts.init(performanceChart.value)
  const option = {
    title: {
      text: 'Core Web Vitals',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    radar: {
      indicator: [
        { name: 'FCP', max: 3000 },
        { name: 'LCP', max: 4000 },
        { name: 'TTI', max: 5000 },
        { name: 'FID', max: 300 },
        { name: 'CLS', max: 0.25 }
      ]
    },
    series: [{
      type: 'radar',
      data: [{
        value: [
          metrics.value.FCP,
          metrics.value.LCP,
          metrics.value.TTI,
          metrics.value.FID,
          0.05 // CLS 模拟值
        ],
        name: '当前性能'
      }]
    }]
  }
  
  chart.setOption(option)
}

// 更新对比图表
const updateComparisonChart = () => {
  if (!comparisonChart.value) return
  
  const chart = echarts.init(comparisonChart.value)
  const option = {
    title: {
      text: '优化前后性能对比',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['优化前', '优化后'],
      bottom: 0
    },
    xAxis: {
      type: 'category',
      data: ['首屏时间', 'JS大小', 'CSS大小', '图片大小', '总请求数']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '优化前',
        type: 'bar',
        data: [3500, 800, 200, 1500, 50],
        itemStyle: {
          color: '#f56c6c'
        }
      },
      {
        name: '优化后',
        type: 'bar',
        data: [1800, 400, 50, 800, 25],
        itemStyle: {
          color: '#67c23a'
        }
      }
    ]
  }
  
  chart.setOption(option)
}

// 挂载时初始化
onMounted(async () => {
  await nextTick()
  refreshMetrics()
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    updatePerformanceChart()
    updateComparisonChart()
  })
})
</script>

<style scoped lang="scss">
.first-screen-demo {
  padding: 20px;
  
  h1 {
    text-align: center;
    margin-bottom: 30px;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metrics-card,
.techniques-card,
.waterfall-card,
.suggestions-card,
.comparison-card {
  margin-bottom: 20px;
}

.performance-chart {
  height: 300px;
  margin-top: 20px;
}

.waterfall-container {
  position: relative;
  height: 400px;
  overflow-x: auto;
  overflow-y: auto;
  background: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
}

.resource-bar {
  position: absolute;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  border-radius: 4px;
  color: white;
  font-size: 12px;
  margin-bottom: 5px;
  transition: all 0.3s;
  
  &:hover {
    transform: translateX(5px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }
  
  .resource-name {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 70%;
  }
  
  .resource-time {
    font-weight: bold;
  }
}

// 响应式
@media (max-width: 768px) {
  .el-statistic {
    text-align: center;
  }
  
  .waterfall-container {
    height: 300px;
  }
}
</style>