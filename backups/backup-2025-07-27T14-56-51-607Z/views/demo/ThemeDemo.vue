<template>
  <div class="theme-demo">
    <el-card class="demo-header">
      <div class="header-content">
        <div>
          <h1>主题系统演示</h1>
          <p>完整的主题切换功能，支持浅色/深色/跟随系统，以及自定义主题色</p>
        </div>
        <div class="header-actions">
          <hr-theme-color-picker />
          <hr-theme-selector mode="dropdown" show-label />
        </div>
      </div>
    </el-card>

    <!-- 主题切换器展示 -->
    <el-card class="demo-section">
      <template #header>
        <span>主题切换器组件</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <h4>下拉菜单模式</h4>
          <ThemeSelector mode="dropdown" />
        </el-col>
        
        <el-col :span="6">
          <h4>按钮组模式</h4>
          <ThemeSelector mode="button-group" show-label />
        </el-col>
        
        <el-col :span="6">
          <h4>切换按钮模式</h4>
          <ThemeSelector mode="toggle" />
        </el-col>
        
        <el-col :span="6">
          <h4>单选框模式</h4>
          <ThemeSelector mode="radio" />
        </el-col>
      </el-row>
    </el-card>

    <!-- CSS 变量展示 -->
    <el-card class="demo-section">
      <template #header>
        <span>CSS 变量体系</span>
      </template>
      
      <el-tabs v-model="activeTab">
        <el-tab-pane label="颜色变量" name="colors">
          <div class="variable-grid">
            <div v-for="(group, key) in colorVariables" :key="key" class="variable-group">
              <h4>{{ group.title }}</h4>
              <div class="color-list">
                <div 
                  v-for="item in group.items" 
                  :key="item.name"
                  class="color-item"
                >
                  <div 
                    class="color-box" 
                    :style="{ backgroundColor: `var(${item.var})` }"
                  />
                  <div class="color-info">
                    <span class="color-name">{{ item.name }}</span>
                    <code class="color-var">{{ item.var }}</code>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="尺寸变量" name="sizes">
          <div class="variable-list">
            <div v-for="item in sizeVariables" :key="item.var" class="variable-item">
              <span class="var-name">{{ item.name }}</span>
              <code class="var-value">{{ item.var }}</code>
              <span class="var-preview" :style="item.style">{{ item.value }}</span>
            </div>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="阴影变量" name="shadows">
          <div class="shadow-list">
            <div v-for="item in shadowVariables" :key="item.var" class="shadow-item">
              <div class="shadow-box" :style="{ boxShadow: `var(${item.var})` }">
                <span>{{ item.name }}</span>
              </div>
              <code class="shadow-var">{{ item.var }}</code>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 组件主题适配展示 -->
    <el-card class="demo-section">
      <template #header>
        <span>组件主题适配</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <h4>按钮</h4>
          <el-space wrap>
            <el-button>默认按钮</el-button>
            <el-button type="primary">主要按钮</el-button>
            <el-button type="success">成功按钮</el-button>
            <el-button type="warning">警告按钮</el-button>
            <el-button type="danger">危险按钮</el-button>
            <el-button type="info">信息按钮</el-button>
          </el-space>
          
          <h4>标签</h4>
          <el-space wrap>
            <el-tag>默认标签</el-tag>
            <el-tag type="primary">主要标签</el-tag>
            <el-tag type="success">成功标签</el-tag>
            <el-tag type="warning">警告标签</el-tag>
            <el-tag type="danger">危险标签</el-tag>
            <el-tag type="info">信息标签</el-tag>
          </el-space>
          
          <h4>进度条</h4>
          <el-progress :percentage="50"  />
          <el-progress :percentage="70" status="success"  />
          <el-progress :percentage="80" status="warning"  />
          <el-progress :percentage="90" status="exception"  />
        </el-col>
        
        <el-col :span="12">
          <h4>输入框</h4>
          <el-input v-model="inputValue" placeholder="请输入内容"   />
          <br><br>
          <el-input v-model="textareaValue" type="textarea" :rows="3" placeholder="请输入内容"   />
          
          <h4>选择器</h4>
          <el-select v-model="selectValue" placeholder="请选择">
            <el-option label="选项1" value="1"  />
            <el-option label="选项2" value="2"  />
            <el-option label="选项3" value="3"  />
          </el-select>
          
          <h4>开关</h4>
          <el-switch v-model="switchValue"  />
          &nbsp;&nbsp;
          <el-switch v-model="switchValue2" active-text="开启" inactive-text="关闭"  />
        </el-col>
      </el-row>
      
      <el-divider   />
      
      <h4>表格</h4>
      <el-table :data="tableData" stripe style="width: 100%">
        <el-table-column prop="date" label="日期" width="180"  />
        <el-table-column prop="name" label="姓名" width="180"  />
        <el-table-column prop="address" label="地址"  />
        <el-table-column label="操作" width="120">
          <template #default>
            <el-button link type="primary" size="small">编辑</el-button>
            <el-button link type="danger" size="small">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 自定义主题色 -->
    <el-card class="demo-section">
      <template #header>
        <span>自定义主题色</span>
      </template>
      
      <div class="custom-theme-demo">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="theme-preview-card" style="--theme-color: #409EFF">
              <div class="preview-header">默认蓝</div>
              <div class="preview-content">
                <el-button type="primary">主按钮</el-button>
                <el-tag type="primary">标签</el-tag>
              </div>
            </div>
          </el-col>
          
          <el-col :span="8">
            <div class="theme-preview-card" style="--theme-color: #6B5CE6">
              <div class="preview-header">优雅紫</div>
              <div class="preview-content">
                <el-button color="#6B5CE6">主按钮</el-button>
                <el-tag color="#6B5CE6">标签</el-tag>
              </div>
            </div>
          </el-col>
          
          <el-col :span="8">
            <div class="theme-preview-card" style="--theme-color: #1EBFB3">
              <div class="preview-header">清新青</div>
              <div class="preview-content">
                <el-button color="#1EBFB3">主按钮</el-button>
                <el-tag color="#1EBFB3">标签</el-tag>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 主题持久化 -->
    <el-card class="demo-section">
      <template #header>
        <span>主题持久化</span>
      </template>
      
      <el-alert type="info" :closable="false">
        <template #title>
          <div>
            <h4>主题设置会自动保存到本地存储</h4>
            <ul style="margin: 10px 0 0 20px;">
              <li>主题模式：保存在 <code>hr-theme</code> 键中</li>
              <li>自定义颜色：保存在 <code>hr-theme-color</code> 键中</li>
              <li>最近使用的颜色：保存在 <code>hr-recent-colors</code> 键中</li>
              <li>刷新页面后会自动恢复上次的主题设置</li>
            </ul>
          </div>
        </template>
      </el-alert>
      
      <el-divider   />
      
      <el-space>
        <el-button @click="clearThemeSettings">清除主题设置</el-button>
        <el-button @click="exportThemeSettings">导出主题配置</el-button>
        <el-button @click="importThemeSettings">导入主题配置</el-button>
      </el-space>
    </el-card>

    <!-- 最佳实践 -->
    <el-card class="demo-section">
      <template #header>
        <span>主题系统最佳实践</span>
      </template>
      
      <el-collapse v-model="activeNames">
        <el-collapse-item title="1. CSS 变量使用" name="1">
          <ul>
            <li>使用统一的 CSS 变量命名规范：<code>--hr-[category]-[name]</code></li>
            <li>在组件中优先使用 CSS 变量而非硬编码颜色值</li>
            <li>为不同主题定义相同的变量名，只改变值</li>
            <li>使用语义化的变量名，如 <code>--hr-text-color-primary</code></li>
          </ul>
        </el-collapse-item>
        
        <el-collapse-item title="2. 主题切换实现" name="2">
          <ul>
            <li>使用 <code>data-theme</code> 属性控制主题</li>
            <li>支持系统主题自动检测：<code>prefers-color-scheme</code></li>
            <li>主题切换时添加过渡动画，提升用户体验</li>
            <li>避免在切换时出现闪烁或布局跳动</li>
          </ul>
        </el-collapse-item>
        
        <el-collapse-item title="3. 组件适配" name="3">
          <ul>
            <li>确保所有组件都支持主题切换</li>
            <li>第三方组件需要额外的主题适配</li>
            <li>图标和图片考虑不同主题下的显示效果</li>
            <li>注意对比度和可访问性要求</li>
          </ul>
        </el-collapse-item>
        
        <el-collapse-item title="4. 性能优化" name="4">
          <ul>
            <li>主题样式按需加载，避免打包所有主题</li>
            <li>使用 CSS 变量避免重复样式定义</li>
            <li>主题切换时避免重新渲染整个应用</li>
            <li>缓存主题设置，减少重复计算</li>
          </ul>
        </el-collapse-item>
        
        <el-collapse-item title="5. 自定义主题" name="5">
          <ul>
            <li>提供主题色自定义功能</li>
            <li>自动生成配色方案（明暗变体）</li>
            <li>支持导入导出主题配置</li>
            <li>预设常用主题供用户选择</li>
          </ul>
        </el-collapse-item>
      </el-collapse>
    </el-card>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive } from 'vue'
import HrThemeSelector from '@/components/common/HrThemeSelector.vue'
import HrThemeColorPicker from '@/components/common/HrThemeColorPicker.vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 状态
const activeTab = ref('colors')
const activeNames = ref(['1'])
const inputValue = ref('')
const textareaValue = ref('')
const selectValue = ref('')
const switchValue = ref(true)
const switchValue2 = ref(false)

// 表格数据
const tableData = [
  {
    date: '2024-01-01',
    name: 'HrHr张三',
    address: '上海市普陀区金沙江路 1518 弄'
  },
  {
    date: '2024-01-02',
    name: '李四',
    address: '上海市普陀区金沙江路 1517 弄'
  },
  {
    date: '2024-01-03',
    name: '王五',
    address: '上海市普陀区金沙江路 1519 弄'
  }
]

// 颜色变量
const colorVariables = {
  primary: {
    title: '主题色',
    items: [
      { name: 'Primary', var: '--hr-color-primary' },
      { name: 'Primary Light 3', var: '--hr-color-primary-light-3' },
      { name: 'Primary Light 5', var: '--hr-color-primary-light-5' },
      { name: 'Primary Light 7', var: '--hr-color-primary-light-7' },
      { name: 'Primary Light 9', var: '--hr-color-primary-light-9' },
      { name: 'Primary Dark', var: '--hr-color-primary-dark-1' }
    ]
  },
  functional: {
    title: '功能色',
    items: [
      { name: 'Success', var: '--hr-color-success' },
      { name: 'Warning', var: '--hr-color-warning' },
      { name: 'Danger', var: '--hr-color-danger' },
      { name: 'Info', var: '--hr-color-info' }
    ]
  },
  text: {
    title: '文字色',
    items: [
      { name: 'Primary', var: '--hr-text-color-primary' },
      { name: 'Regular', var: '--hr-text-color-regular' },
      { name: 'Secondary', var: '--hr-text-color-secondary' },
      { name: 'Placeholder', var: '--hr-text-color-placeholder' },
      { name: 'Disabled', var: '--hr-text-color-disabled' }
    ]
  },
  background: {
    title: '背景色',
    items: [
      { name: 'Default', var: '--hr-bg-color' },
      { name: 'Page', var: '--hr-bg-color-page' },
      { name: 'Overlay', var: '--hr-bg-color-overlay' }
    ]
  },
  border: {
    title: '边框色',
    items: [
      { name: 'Default', var: '--hr-border-color' },
      { name: 'Light', var: '--hr-border-color-light' },
      { name: 'Lighter', var: '--hr-border-color-lighter' },
      { name: 'Extra Light', var: '--hr-border-color-extra-light' }
    ]
  }
}

// 尺寸变量
const sizeVariables = [
  { name: '特大字号', var: '--hr-font-size-extra-large', value: '20px', style: { fontSize: '20px' } },
  { name: '大字号', var: '--hr-font-size-large', value: '18px', style: { fontSize: '18px' } },
  { name: '中字号', var: '--hr-font-size-medium', value: '16px', style: { fontSize: '16px' } },
  { name: '基础字号', var: '--hr-font-size-base', value: '14px', style: { fontSize: '14px' } },
  { name: '小字号', var: '--hr-font-size-small', value: '13px', style: { fontSize: '13px' } },
  { name: '特小字号', var: '--hr-font-size-extra-small', value: '12px', style: { fontSize: '12px' } }
]

// 阴影变量
const shadowVariables = [
  { name: '基础阴影', var: '--hr-shadow-base' },
  { name: '浅阴影', var: '--hr-shadow-light' },
  { name: '更浅阴影', var: '--hr-shadow-lighter' },
  { name: '深阴影', var: '--hr-shadow-dark' }
]

// 清除主题设置
const clearThemeSettings = async () => {
  try {
    await ElMessageBox.confirm('确定要清除所有主题设置吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    localStorage.removeItem('hr-theme')
    localStorage.removeItem('hr-theme-color')
    localStorage.removeItem('hr-recent-colors')
    
    ElMessage.success('主题设置已清除，请刷新页面')
  } catch {
    // 用户取消
  }
}

// 导出主题配置
const exportThemeSettings = () => {
  const settings = {
    theme: localStorage.getItem('hr-theme') || 'auto',
    themeColor: localStorage.getItem('hr-theme-color') || '#409EFF',
    recentColors: JSON.parse(localStorage.getItem('hr-recent-colors') || '[]')
  }
  
  const blob = new Blob([JSON.stringify(settings, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = 'theme-settings.json'
  a.click()
  URL.revokeObjectURL(url)
  
  ElMessage.success('主题配置已导出')
}

// 导入主题配置
const importThemeSettings = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = 'application/json'
   
  input.onchange = (e: unknown) => {
    const file = e.target.files[0]
    if (!file) return
    
    const reader = new FileReader()
   
    reader.onload = (e: unknown) => {
      try {
        const settings = JSON.parse(e.target.result)
        
        if (settings.theme) {
          localStorage.setItem('hr-theme', settings.theme)
        }
        if (settings.themeColor) {
          localStorage.setItem('hr-theme-color', settings.themeColor)
        }
        if (settings.recentColors) {
          localStorage.setItem('hr-recent-colors', JSON.stringify(settings.recentColors))
        }
        
        ElMessage.success('主题配置已导入，请刷新页面')
      } catch (__error) {
        ElMessage.error('导入失败，请检查文件格式')
      }
    }
    reader.readAsText(file)
  }
  input.click()
}
</script>

<style lang="scss" scoped>
.theme-demo {
  padding: 20px;
  
  .demo-header {
    margin-bottom: 20px;
    
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      h1 {
        margin: 0 0 10px;
        font-size: 24px;
      }
      
      p {
        margin: 0;
        color: var(--hr-text-color-secondary);
      }
      
      .header-actions {
        display: flex;
        align-items: center;
        gap: 16px;
      }
    }
  }
  
  .demo-section {
    margin-bottom: 20px;
    
    h4 {
      margin: 16px 0 12px;
      font-size: 14px;
      color: var(--hr-text-color-regular);
    }
  }
  
  .variable-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 24px;
    
    .variable-group {
      h4 {
        margin: 0 0 12px;
        font-size: 16px;
        font-weight: 500;
      }
      
      .color-list {
        display: flex;
        flex-direction: column;
        gap: 8px;
        
        .color-item {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 8px;
          border-radius: 4px;
          background-color: var(--hr-fill-color-lighter);
          
          .color-box {
            width: 40px;
            height: 40px;
            border-radius: 4px;
            border: 1px solid var(--hr-border-color-light);
          }
          
          .color-info {
            flex: 1;
            
            .color-name {
              display: block;
              font-size: 14px;
              font-weight: 500;
              margin-bottom: 2px;
            }
            
            .color-var {
              font-size: 12px;
              color: var(--hr-text-color-secondary);
            }
          }
        }
      }
    }
  }
  
  .variable-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    
    .variable-item {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 12px;
      background-color: var(--hr-fill-color-lighter);
      border-radius: 4px;
      
      .var-name {
        width: 120px;
        font-weight: 500;
      }
      
      .var-value {
        flex: 1;
        font-family: monospace;
        color: var(--hr-text-color-secondary);
      }
      
      .var-preview {
        font-weight: 500;
      }
    }
  }
  
  .shadow-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    
    .shadow-item {
      .shadow-box {
        height: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: var(--hr-bg-color);
        border-radius: 8px;
        margin-bottom: 8px;
      }
      
      .shadow-var {
        font-size: 12px;
        color: var(--hr-text-color-secondary);
      }
    }
  }
  
  .theme-preview-card {
    border: 1px solid var(--hr-border-color-light);
    border-radius: 8px;
    overflow: hidden;
    
    .preview-header {
      padding: 12px;
      background-color: var(--theme-color);
      color: white;
      font-weight: 500;
      text-align: center;
    }
    
    .preview-content {
      padding: 20px;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12px;
    }
  }
  
  code {
    padding: 2px 6px;
    background-color: var(--hr-fill-color);
    border-radius: 3px;
    font-family: monospace;
    font-size: 13px;
  }
  
  :deep(.el-collapse-item__content) {
    padding: 16px 20px;
    
    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        line-height: 1.6;
      }
    }
  }
}
</style>