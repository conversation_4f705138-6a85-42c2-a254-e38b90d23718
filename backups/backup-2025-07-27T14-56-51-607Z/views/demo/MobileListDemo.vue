<template>
  <div class="mobile-list-demo">
    <hr-page-header title="移动端列表优化演示" :show-back="true">
      演示下拉刷新、上拉加载、触摸手势等移动端优化功能
    </hr-page-header>

    <!-- 基础示例 -->
    <el-card class="demo-section">
      <template #header>
        <div class="card-header">
          <span>员工列表（移动端优化）</span>
          <div class="header-actions">
            <el-button size="small" @click="resetList">重置列表</el-button>
            <el-button size="small" type="primary" @click="addItems">添加数据</el-button>
          </div>
        </div>
      </template>
      
      <div class="mobile-container">
        <MobileList
          ref="mobileListRef"
          :items="listData"
          :loading="loading"
          :has-more="hasMore"
          :enable-refresh="enableRefresh"
          :enable-load-more="enableLoadMore"
          :enable-back-top="enableBackTop"
          :show-skeleton="showSkeleton"
          :skeleton-count="skeletonCount"
          :empty-text="emptyText"
          @refresh="handleRefresh"
          @load-more="handleLoadMore"
          @item-click="handleItemClick"
        >
          <!-- 自定义列表项 -->
          <template #default="{ item, index }">
            <div class="custom-item">
              <div class="item-avatar">
                <el-avatar :size="48" :src="item.avatar">
                  {{ item.name.charAt(0) }}
                </el-avatar>
              </div>
              <div class="item-content">
                <div class="item-header">
                  <h4>{{ item.name }}</h4>
                  <el-tag size="small" :type="item.statusType">
                    {{ item.status }}
                  </el-tag>
                </div>
                <div class="item-info">
                  <span class="info-item">
                    <el-icon><Briefcase /></el-icon>
                    {{ item.department }}
                  </span>
                  <span class="info-item">
                    <el-icon><User /></el-icon>
                    {{ item.position }}
                  </span>
                </div>
                <div class="item-meta">
                  工号：{{ item.employeeNo }} | 入职时间：{{ item.joinDate }}
                </div>
              </div>
              <el-icon class="item-arrow" :size="20">
                <ArrowRight />
              </el-icon>
            </div>
          </template>

          <!-- 自定义空状态 -->
          <template #empty>
            <el-empty description="暂无员工数据">
              <el-button type="primary" @click="initData">
                加载示例数据
              </el-button>
            </el-empty>
          </template>

          <!-- 列表头部 -->
          <template #header>
            <div class="list-stats">
              <el-statistic title="总人数" :value="totalCount"  />
              <el-statistic title="在职人数" :value="activeCount"  />
              <el-statistic title="离职人数" :value="inactiveCount"  />
            </div>
          </template>
        </MobileList>
      </div>
    </el-card>

    <!-- 配置选项 -->
    <el-card class="demo-section">
      <template #header>
        <span>配置选项</span>
      </template>
      
      <el-form label-width="120px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="下拉刷新">
              <el-switch v-model="enableRefresh"  />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="上拉加载">
              <el-switch v-model="enableLoadMore"  />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="返回顶部">
              <el-switch v-model="enableBackTop"  />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="骨架屏">
              <el-switch v-model="showSkeleton"  />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="骨架屏数量">
              <el-input-number v-model="skeletonCount" :min="1" :max="10"   />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="空状态文本">
              <el-input v-model="emptyText" placeholder="请输入空状态提示文本"   />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="测试操作">
          <el-space>
            <el-button @click="simulateRefresh">模拟刷新</el-button>
            <el-button @click="simulateLoadMore">模拟加载更多</el-button>
            <el-button @click="clearData">清空数据</el-button>
            <el-button @click="setNoMore">设置无更多数据</el-button>
          </el-space>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 功能特性 -->
    <el-card class="demo-section">
      <template #header>
        <span>功能特性</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <h4>触摸手势</h4>
          <ul>
            <li>下拉刷新：支持下拉触发刷新</li>
            <li>阻尼效果：下拉时的阻尼动画</li>
            <li>释放提示：松开时的视觉反馈</li>
            <li>加载动画：刷新时的旋转动画</li>
          </ul>
        </el-col>
        <el-col :span="8">
          <h4>无限滚动</h4>
          <ul>
            <li>自动加载：滚动到底部自动加载</li>
            <li>加载状态：显示加载中提示</li>
            <li>无更多数据：展示结束提示</li>
            <li>手动触发：点击加载更多</li>
          </ul>
        </el-col>
        <el-col :span="8">
          <h4>用户体验</h4>
          <ul>
            <li>骨架屏：初始加载时的占位</li>
            <li>空状态：无数据时的友好提示</li>
            <li>返回顶部：快速返回列表顶部</li>
            <li>列表动画：新增项的进入动画</li>
          </ul>
        </el-col>
      </el-row>
    </el-card>

    <!-- 使用说明 -->
    <el-card class="demo-section">
      <template #header>
        <span>使用说明</span>
      </template>
      
      <div class="usage-guide">
        <h4>1. 下拉刷新</h4>
        <p>在列表顶部向下拖动，当看到"松开刷新"提示时释放手指即可触发刷新。</p>
        
        <h4>2. 上拉加载</h4>
        <p>滚动到列表底部时会自动触发加载更多，也可以点击"加载更多"按钮手动触发。</p>
        
        <h4>3. 自定义内容</h4>
        <p>支持通过插槽自定义列表项、空状态、头部和底部内容。</p>
        
        <h4>4. 性能优化</h4>
        <p>使用了 passive 事件监听器和节流处理，确保滚动性能流畅。</p>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Briefcase, User, ArrowRight } from '@element-plus/icons-vue'
import HrPageHeader from '@/components/common/HrPageHeader.vue'
import HrMobileList from '@/components/mobile/HrMobileList.vue'

// 组件引用
const mobileListRef = ref()

// 配置选项
const enableRefresh = ref(true)
const enableLoadMore = ref(true)
const enableBackTop = ref(true)
const showSkeleton = ref(false)
const skeletonCount = ref(5)
const emptyText = ref('暂无数据')

// 数据状态
const listData = ref<any[]>([])
const loading = ref(false)
const hasMore = ref(true)
const currentPage = ref(1)
const pageSize = ref(10)

// 统计数据
const totalCount = computed(() => listData.value.length)
const activeCount = computed(() => listData.value.filter(item => item.status === '在职').length)
const inactiveCount = computed(() => listData.value.filter(item => item.status === '离职').length)

// 生成模拟数据
const generateMockData = (page: number, size: number) => {
  const departments = ['技术部', '产品部', '市场部', '人事部', '财务部']
  const positions = ['工程师', '经理', '主管', '专员', '总监']
  const statuses = [
    { text: '在职', type: 'success' },
    { text: '离职', type: 'info' },
    { text: '休假', type: 'warning' }
  ]

  const data = []
  const start = (page - 1) * size
  for (let i = 0; i < size; i++) {
    const index = start + i
    const status = statuses[Math.floor(Math.random() * statuses.length)]
    data.push({
      id: index + 1,
      name: `员工${index + 1}`,
      employeeNo: `EMP${String(index + 1).padStart(5, '0')}`,
      department: departments[Math.floor(Math.random() * departments.length)],
      position: positions[Math.floor(Math.random() * positions.length)],
      status: status.text,
      statusType: status.type,
      joinDate: `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
      avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${index}`
    })
  }
  return data
}

// 初始化数据
const initData = () => {
  showSkeleton.value = true
  setTimeout(() => {
    listData.value = generateMockData(1, pageSize.value)
    currentPage.value = 1
    hasMore.value = true
    showSkeleton.value = false
  }, 1500)
}

// 处理刷新
const handleRefresh = () => {
  console.log('触发刷新')
  
  // 模拟异步请求
  setTimeout(() => {
    listData.value = generateMockData(1, pageSize.value)
    currentPage.value = 1
    hasMore.value = true
    
    // 调用组件方法结束刷新
    mobileListRef.value?.finishRefresh()
    
    ElMessage.success('刷新成功')
  }, 2000)
}

// 处理加载更多
const handleLoadMore = () => {
  console.log('触发加载更多')
  
  if (!hasMore.value) return
  
  loading.value = true
  
  // 模拟异步请求
  setTimeout(() => {
    currentPage.value++
    const newData = generateMockData(currentPage.value, pageSize.value)
    listData.value.push(...newData)
    
    // 模拟没有更多数据
    if (currentPage.value >= 5) {
      hasMore.value = false
    }
    
    loading.value = false
    
    // 调用组件方法结束加载
    mobileListRef.value?.finishLoadMore()
  }, 1500)
}

// 处理列表项点击
   
const handleItemClick = (item: unknown, index: number) => {
  ElMessage.info(`点击了第 ${index + 1} 项：${item.name}`)
}

// 重置列表
const resetList = () => {
  listData.value = []
  currentPage.value = 1
  hasMore.value = true
  initData()
}

// 添加数据
const addItems = () => {
  const newItems = generateMockData(1, 5)
  listData.value.unshift(...newItems)
  ElMessage.success('已添加5条数据')
}

// 模拟刷新
const simulateRefresh = () => {
  if (!enableRefresh.value) {
    ElMessage.warning('请先开启下拉刷新功能')
    return
  }
  mobileListRef.value?.refresh()
}

// 模拟加载更多
const simulateLoadMore = () => {
  if (!enableLoadMore.value) {
    ElMessage.warning('请先开启上拉加载功能')
    return
  }
  handleLoadMore()
}

// 清空数据
const clearData = () => {
  listData.value = []
  currentPage.value = 1
  hasMore.value = true
  ElMessage.success('已清空数据')
}

// 设置无更多数据
const setNoMore = () => {
  hasMore.value = false
  ElMessage.success('已设置为无更多数据')
}

// 初始化
onMounted(() => {
  initData()
})
</script>

<style lang="scss" scoped>
.mobile-list-demo {
  padding: 20px;
  
  .demo-section {
    margin-bottom: 20px;
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  // 移动端容器
  .mobile-container {
    height: 600px;
    max-width: 400px;
    margin: 0 auto;
    border: 1px solid var(--el-border-color);
    border-radius: 8px;
    overflow: hidden;
    background: var(--el-bg-color-page);
  }
  
  // 自定义列表项
  .custom-item {
    display: flex;
    align-items: center;
    padding: 16px;
    gap: 12px;
    
    .item-avatar {
      flex-shrink: 0;
    }
    
    .item-content {
      flex: 1;
      overflow: hidden;
      
      .item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        
        h4 {
          margin: 0;
          font-size: 16px;
          font-weight: 500;
          color: var(--el-text-color-primary);
        }
      }
      
      .item-info {
        display: flex;
        gap: 16px;
        margin-bottom: 8px;
        
        .info-item {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 14px;
          color: var(--el-text-color-regular);
          
          .el-icon {
            color: var(--el-text-color-secondary);
          }
        }
      }
      
      .item-meta {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }
    
    .item-arrow {
      flex-shrink: 0;
      color: var(--el-text-color-placeholder);
    }
  }
  
  // 列表统计
  .list-stats {
    display: flex;
    justify-content: space-around;
    padding: 16px;
    background: var(--el-fill-color-light);
    border-radius: 4px;
    
    .el-statistic {
      text-align: center;
    }
  }
  
  // 使用说明
  .usage-guide {
    h4 {
      margin: 16px 0 8px;
      font-size: 15px;
      color: var(--el-text-color-primary);
      
      &:first-child {
        margin-top: 0;
      }
    }
    
    p {
      margin: 0 0 12px;
      line-height: 1.6;
      color: var(--el-text-color-regular);
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .mobile-list-demo {
    padding: 10px;
    
    .mobile-container {
      max-width: 100%;
      height: calc(100vh - 200px);
    }
  }
}
</style>