<template>
  <div class="list-optimization-demo">
    <hr-page-header title="列表优化演示" :show-back="true">
      演示分页加载、虚拟滚动、无限滚动、数据预加载等优化功能
    </hr-page-header>

    <!-- 配置面板 -->
    <el-card class="config-panel">
      <template #header>
        <span>列表配置</span>
      </template>
      
      <el-form :model="config" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="数据总量">
              <el-input-number
                v-model="config.totalCount"
                :min="0"
                :max="100000"
                :step="1000"
                />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="每页数量">
              <el-select v-model="config.pageSize">
                <el-option :value="10" label="10条"  />
                <el-option :value="20" label="20条"  />
                <el-option :value="50" label="50条"  />
                <el-option :value="100" label="100条"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="加载延迟">
              <el-input-number
                v-model="config.loadDelay"
                :min="0"
                :max="5000"
                :step="100"
                suffix="ms"
                />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="虚拟滚动">
              <el-switch v-model="config.enableVirtual"  />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="无限滚动">
              <el-switch v-model="config.infiniteScroll"  />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="数据预加载">
              <el-switch v-model="config.enablePreload"  />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="数据缓存">
              <el-switch v-model="config.enableCache"  />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item>
          <el-button type="primary" @click="resetDemo">重置演示</el-button>
          <el-button @click="clearCache">清空缓存</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 性能指标 -->
    <el-card class="metrics-panel">
      <template #header>
        <span>性能指标</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic title="加载时间" :value="metrics.loadTime" suffix="ms">
            <template #prefix>
              <el-icon><Timer /></el-icon>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="渲染项数" :value="metrics.renderCount">
            <template #prefix>
              <el-icon><List /></el-icon>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="缓存命中" :value="metrics.cacheHits">
            <template #prefix>
              <el-icon><Box /></el-icon>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="预加载页数" :value="metrics.preloadedPages">
            <template #prefix>
              <el-icon><Download /></el-icon>
            </template>
          </el-statistic>
        </el-col>
      </el-row>
    </el-card>

    <!-- 列表演示 -->
    <el-card class="list-demo">
      <OptimizedList
        ref="listRef"
        :data="listData"
        :total="total"
        :loading="loading"
        :loading-more="loadingMore"
        :page="page"
        :page-size="config.pageSize"
        :enable-virtual="config.enableVirtual"
        :infinite-scroll="config.infiniteScroll"
        :has-more="hasMore"
        :show-progress="true"
        :animate-items="true"
        :item-height="100"
        :container-height="'600px'"
        @update:page="handlePageChange"
        @update:page-size="handleSizeChange"
        @load-more="handleLoadMore"
      >
        <template #default="{ item, index }">
          <el-card class="demo-item" :body-style="{ padding: '16px' }">
            <div class="item-content">
              <div class="item-avatar">
                <el-avatar :size="64" :style="{ backgroundColor: item.color }">
                  {{ item.name.charAt(0) }}
                </el-avatar>
              </div>
              <div class="item-info">
                <h3>{{ item.name }}</h3>
                <p class="item-desc">{{ item.description }}</p>
                <div class="item-meta">
                  <span>ID: {{ item.id }}</span>
                  <span>索引: {{ index }}</span>
                  <span>创建时间: {{ item.createTime }}</span>
                </div>
              </div>
              <div class="item-stats">
                <AnimatedNumber
                  :value="item.value"
                  prefix="¥"
                  :separator="true"
                  :decimals="2"
                />
              </div>
            </div>
          </el-card>
        </template>
        
        <template #loading>
          <div v-for="i in 5" :key="i" class="skeleton-item">
            <el-skeleton :rows="3" animated  />
          </div>
        </template>
        
        <template #empty>
          <el-empty description="暂无数据"  />
        </template>
      </OptimizedList>
    </el-card>

    <!-- 操作日志 -->
    <el-card class="log-panel">
      <template #header>
        <div class="log-header">
          <span>操作日志</span>
          <el-button text @click="clearLogs">清空</el-button>
        </div>
      </template>
      
      <div class="log-content">
        <el-scrollbar height="200px">
          <div
            v-for="(log, index) in logs"
            :key="index"
            class="log-item"
            :class="`log-${log.type}`"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </el-scrollbar>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, watch } from 'vue'
import { Timer, List, Box, Download } from '@element-plus/icons-vue'
import HrPageHeader from '@/components/common/HrPageHeader.vue'
import HrOptimizedList from '@/components/list/HrOptimizedList.vue'
import HrAnimatedNumber from '@/components/common/HrAnimatedNumber.vue'
import { useListOptimization } from '@/composables/useListOptimization'

// 配置
const config = reactive({
  totalCount: 10000,
  pageSize: 20,
  loadDelay: 500,
  enableVirtual: true,
  infiniteScroll: false,
  enablePreload: true,
  enableCache: true
})

// 性能指标
const metrics = reactive({
  loadTime: 0,
  renderCount: 0,
  cacheHits: 0,
  preloadedPages: 0
})

// 日志
const logs = ref<Array<{
  time: string,
  type: 'info' | 'success' | 'warning' | 'error',
  message: string
}>>([])

// 列表引用
const listRef = ref()

// 模拟数据生成
const generateMockData = (page: number, pageSize: number) => {
  const colors = ['#409eff', '#67c23a', '#e6a23c', '#f56c6c', '#909399']
  const start = (page - 1) * pageSize
  
  return Array.from({ length: pageSize }, (_, index) => ({
    id: start + index + 1,
    name: `数据项 ${start + index + 1}`,
    description: `这是第 ${page} 页的第 ${index + 1} 条数据，用于演示列表优化功能`,
    value: Math.floor(Math.random() * 10000),
    color: colors[Math.floor(Math.random() * colors.length)],
    createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString()
  }))
}

// 添加日志
const addLog = (type: 'info' | 'success' | 'warning' | 'error', message: string) => {
  const now = new Date()
  const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}.${now.getMilliseconds().toString().padStart(3, '0')}`
  
  logs.value.unshift({ time, type, message })
  
  // 限制日志数量
  if (logs.value.length > 100) {
    logs.value.pop()
  }
}

// 使用列表优化
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const fetchData = async (params: unknown) => {
  const startTime = Date.now()
  
  // 检查缓存
  const cacheKey = `${params.page}-${params.pageSize}`
  const cached = listOptimization.getFromCache(params.page)
  if (cached) {
    metrics.cacheHits++
    addLog('success', `缓存命中: 页码 ${params.page}`)
    return {
      data: cached.data,
      total: cached.total
    }
  }
  
  addLog('info', `开始加载: 页码 ${params.page}, 每页 ${params.pageSize} 条`)
  
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, config.loadDelay))
  
  // 生成数据
  const data = generateMockData(params.page, params.pageSize)
  const total = config.totalCount
  
  // 记录加载时间
  const loadTime = Date.now() - startTime
  metrics.loadTime = loadTime
  
  addLog('success', `加载完成: 耗时 ${loadTime}ms`)
  
  return { data, total }
}

const listOptimization = useListOptimization(fetchData, {
  storageKey: 'list-optimization-demo',
  defaultPageSize: config.pageSize,
  enablePreload: config.enablePreload,
  enableCache: config.enableCache,
  enableVirtualScroll: config.enableVirtual,
  enableInfiniteScroll: config.infiniteScroll
})

const {data: _data, total: _total, page: _page, loading: _loading, loadingMore: _loadingMore, hasMore: _hasMore, loadData: _loadData, refresh: _refresh, changePage: _changePage, changePageSize: _changePageSize, cleanExpiredCache: _cleanExpiredCache} =  listOptimization

// 处理页码变化
const handlePageChange 
  
  .config-panel,
  .metrics-panel,
  .list-demo,
  .log-panel {
    margin-bottom: 20px;
  }
  
  .metrics-panel {
    :deep(.el-statistic) {
      text-align: center;
    }
  }
  
  .demo-item {
    margin-bottom: 8px;
    transition: all 0.3s;
    
    &:hover {
      transform: translateX(4px);
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    }
    
    .item-content {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .item-avatar {
        flex-shrink: 0;
      }
      
      .item-info {
        flex: 1;
        
        h3 {
          margin: 0 0 8px;
          font-size: 16px;
          font-weight: 500;
        }
        
        .item-desc {
          margin: 0 0 8px;
          color: var(--el-text-color-secondary);
          font-size: 14px;
        }
        
        .item-meta {
          display: flex;
          gap: 16px;
          font-size: 12px;
          color: var(--el-text-color-placeholder);
        }
      }
      
      .item-stats {
        flex-shrink: 0;
        font-size: 24px;
        font-weight: 500;
        color: var(--el-color-primary);
      }
    }
  }
  
  .skeleton-item {
    margin-bottom: 8px;
    padding: 16px;
    background: var(--el-bg-color);
    border-radius: 4px;
    border: 1px solid var(--el-border-color-lighter);
  }
  
  .log-panel {
    .log-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .log-content {
      font-family: 'Monaco', 'Consolas', monospace;
      font-size: 12px;
      
      .log-item {
        padding: 4px 0;
        display: flex;
        gap: 12px;
        
        .log-time {
          color: var(--el-text-color-secondary);
          flex-shrink: 0;
        }
        
        .log-message {
          flex: 1;
        }
        
        &.log-info {
          color: var(--el-text-color-regular);
        }
        
        &.log-success {
          color: var(--el-color-success);
        }
        
        &.log-warning {
          color: var(--el-color-warning);
        }
        
        &.log-error {
          color: var(--el-color-danger);
        }
      }
    }
  }
}
</style>