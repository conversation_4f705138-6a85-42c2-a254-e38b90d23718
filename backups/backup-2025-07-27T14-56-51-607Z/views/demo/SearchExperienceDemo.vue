<template>
  <div class="search-experience-demo">
    <h1>搜索体验优化演示</h1>
    
    <el-card class="demo-card">
      <template #header>
        <h3>全局智能搜索</h3>
      </template>
      
      <SearchExperienceOptimization
        ref="searchRef"
        placeholder="试试搜索员工、部门或职位..."
        :enable-voice-search="true"
        :enable-shortcuts="true"
        @search="handleSearch"
        @select-result="handleSelectResult"
        @advanced-search="handleAdvancedSearch"
      />
      
      <div class="demo-info">
        <el-alert type="info" :closable="false">
          <template #title>
            <div class="info-content">
              <p>功能特性：</p>
              <ul>
                <li>🔍 智能搜索建议 - 根据输入内容智能推荐搜索词</li>
                <li>⏰ 搜索历史记录 - 自动保存最近的搜索记录</li>
                <li>🔥 热门搜索展示 - 显示系统热门搜索关键词</li>
                <li>🎯 实时搜索预览 - 输入时实时显示搜索结果</li>
                <li>🎤 语音搜索支持 - 支持语音输入搜索（需浏览器支持）</li>
                <li>⚡ 快捷键支持 - Ctrl+K 快速搜索，F1 查看帮助</li>
                <li>🔧 高级搜索 - 支持多条件组合搜索</li>
                <li>✨ 结果高亮显示 - 搜索关键词高亮标记</li>
              </ul>
            </div>
          </template>
        </el-alert>
      </div>
    </el-card>
    
    <el-card class="demo-card">
      <template #header>
        <h3>搜索结果</h3>
      </template>
      
      <div v-if="searchResult.query" class="search-result">
        <div class="result-header">
          <span>搜索词：<strong>{{ searchResult.query }}</strong></span>
          <span>选项：{{ JSON.stringify(searchResult.options) }}</span>
        </div>
        
        <el-divider   />
        
        <div v-if="selectedResult" class="selected-result">
          <h4>选中的结果：</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="ID">{{ selectedResult.id }}</el-descriptions-item>
            <el-descriptions-item label="标题">{{ selectedResult.title }}</el-descriptions-item>
            <el-descriptions-item label="描述" :span="2">{{ selectedResult.description }}</el-descriptions-item>
            <el-descriptions-item label="类型">{{ selectedResult.type }}</el-descriptions-item>
            <el-descriptions-item label="路径">{{ selectedResult.path }}</el-descriptions-item>
            <el-descriptions-item label="链接">{{ selectedResult.url }}</el-descriptions-item>
            <el-descriptions-item label="相关度">{{ (selectedResult.relevance * 100).toFixed(0) }}%</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
      
      <el-empty v-else description="暂无搜索结果"  />
    </el-card>
    
    <el-card class="demo-card">
      <template #header>
        <h3>高级搜索结果</h3>
      </template>
      
      <div v-if="advancedSearchResult" class="advanced-result">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="关键词">{{ advancedSearchResult.keywords }}</el-descriptions-item>
          <el-descriptions-item label="类型">{{ advancedSearchResult.type }}</el-descriptions-item>
          <el-descriptions-item label="日期范围">
            {{ advancedSearchResult.dateRange?.join(' 至 ') || '不限' }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">{{ advancedSearchResult.status }}</el-descriptions-item>
          <el-descriptions-item label="排序方式">{{ advancedSearchResult.sortBy }}</el-descriptions-item>
          <el-descriptions-item label="搜索字段">{{ advancedSearchResult.fields.join(', ') }}</el-descriptions-item>
        </el-descriptions>
      </div>
      
      <el-empty v-else description="暂无高级搜索结果"  />
    </el-card>
    
    <el-card class="demo-card">
      <template #header>
        <h3>搜索统计</h3>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic title="总搜索次数" :value="statistics.totalSearches"  />
        </el-col>
        <el-col :span="6">
          <el-statistic title="语音搜索次数" :value="statistics.voiceSearches"  />
        </el-col>
        <el-col :span="6">
          <el-statistic title="高级搜索次数" :value="statistics.advancedSearches"  />
        </el-col>
        <el-col :span="6">
          <el-statistic title="平均响应时间" :value="statistics.avgResponseTime" suffix="ms"  />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'SearchExperienceDemo'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive } from 'vue'
import HrSearchExperienceOptimization from '@/components/ux/HrSearchExperienceOptimization.vue'

// 组件引用
const searchRef = ref()

// 搜索结果
const searchResult = ref({
  query: '',
  options: null
})

const selectedResult = ref<unknown>(null)
const advancedSearchResult = ref<unknown>(null)

// 统计数据
const statistics = reactive({
  totalSearches: 0,
  voiceSearches: 0,
  advancedSearches: 0,
  avgResponseTime: 0
})

// 处理搜索
   
const handleSearch = (query: string, options?: unknown) => {
  console.log('搜索触发:', query, options)
  
  searchResult.value = {
    query,
    options
  }
  
  statistics.totalSearches++
  
  // 模拟响应时间
  const responseTime = Math.floor(Math.random() * 300) + 100
  statistics.avgResponseTime = Math.floor(
    (statistics.avgResponseTime * (statistics.totalSearches - 1) + responseTime) / statistics.totalSearches
  )
}

// 处理选择结果
   
const handleSelectResult = (result: unknown) => {
  console.log('选择结果:', result)
  selectedResult.value = result
}

// 处理高级搜索
   
const handleAdvancedSearch = (form: unknown) => {
  console.log('高级搜索:', form)
  advancedSearchResult.value = form
  statistics.advancedSearches++
}
</script>

<style lang="scss" scoped>
.search-experience-demo {
  padding: 20px;
  
  h1 {
    margin-bottom: 20px;
    font-size: 24px;
  }
  
  .demo-card {
    margin-bottom: 20px;
    
    h3 {
      margin: 0;
      font-size: 18px;
    }
  }
  
  .demo-info {
    margin-top: 20px;
    
    .info-content {
      p {
        margin: 0 0 10px 0;
        font-weight: 500;
      }
      
      ul {
        margin: 0;
        padding-left: 20px;
        
        li {
          line-height: 1.8;
        }
      }
    }
  }
  
  .search-result {
    .result-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      strong {
        color: var(--el-color-primary);
      }
    }
    
    .selected-result {
      h4 {
        margin: 0 0 16px 0;
        font-size: 16px;
      }
    }
  }
  
  .advanced-result {
    padding: 10px 0;
  }
}
</style>