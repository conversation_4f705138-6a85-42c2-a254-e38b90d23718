<template>
  <div class="editable-table-demo">
    <h1>单元格编辑优化示例</h1>

    <!-- 基础编辑 -->
    <el-card class="demo-card">
      <template #header>
        <h3>基础编辑功能</h3>
      </template>
      
      <EditableTable
        ref="basicTableRef"
        v-model:data="basicData"
        :columns="basicColumns"
        :height="400"
        show-index
        @add="handleAdd"
        @save="handleSave"
        @delete="handleDelete"
      />
      
      <el-alert type="info" :closable="false" style="margin-top: 16px">
        <template #title>
          功能说明：
          <ul style="margin: 8px 0 0 20px">
            <li>点击单元格进入编辑模式</li>
            <li>支持 Enter 键保存并移到下一行</li>
            <li>支持 Tab 键移到下一列</li>
            <li>支持 Esc 键取消编辑</li>
            <li>支持数据验证和错误提示</li>
          </ul>
        </template>
      </el-alert>
    </el-card>

    <!-- 批量编辑 -->
    <el-card class="demo-card">
      <template #header>
        <h3>批量编辑模式</h3>
      </template>
      
      <EditableTable
        ref="batchTableRef"
        v-model:data="batchData"
        :columns="batchColumns"
        :height="400"
        :edit-trigger="'manual'"
        show-selection
        @batch-save="handleBatchSave"
      />
    </el-card>

    <!-- 高级编辑 -->
    <el-card class="demo-card">
      <template #header>
        <h3>高级编辑功能</h3>
      </template>
      
      <EditableTable
        ref="advancedTableRef"
        v-model:data="advancedData"
        :columns="advancedColumns"
        :height="500"
        :auto-save="true"
        :auto-save-delay="2000"
        show-index
        stripe
        border
      >
        <template #toolbar-left>
          <el-button @click="importData">
            <el-icon><Upload /></el-icon>
            导入数据
          </el-button>
          <el-button @click="exportData">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </template>
      </EditableTable>
      
      <el-divider   />
      
      <div class="stats">
        <el-statistic title="总记录数" :value="advancedData.length"  />
        <el-statistic title="编辑中" :value="editingCount" suffix="行"  />
        <el-statistic title="已保存" :value="savedCount" suffix="行"  />
      </div>
    </el-card>

    <!-- 表单式编辑 -->
    <el-card class="demo-card">
      <template #header>
        <h3>表单式编辑</h3>
      </template>
      
      <el-form ref="formRef" :model="formData" label-width="100px">
        <EditableTable
          v-model:data="formData.items"
          :columns="formColumns"
          :height="350"
          :show-toolbar="false"
          :can-delete="true"
          border
        >
          <template #footer>
            <tr>
              <td colspan="2" style="text-align: right; font-weight: bold">合计：</td>
              <td style="text-align: right">{{ totalQuantity }}</td>
              <td style="text-align: right">-</td>
              <td style="text-align: right">{{ formatMoney(totalAmount) }}</td>
              <td></td>
            </tr>
          </template>
        </EditableTable>
        
        <el-form-item label="备注">
          <el-input v-model="formData.remark" type="textarea" :rows="2"   />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="submitForm">提交表单</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
 
import { ref, computed, reactive } from 'vue'
import HrEditableTable from '@/components/data-table/HrEditableTable.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload, Download } from '@element-plus/icons-vue'
import type { TableColumn } from '@/components/data-table/types'

// 基础编辑数据
const basicData = ref([
  { id: 1, name: 'HrHr张三', age: 28, department: 'tech', position: '前端工程师', salary: 15000 },
  { id: 2, name: '李四', age: 32, department: 'product', position: '产品经理', salary: 20000 },
  { id: 3, name: '王五', age: 26, department: 'design', position: 'UI设计师', salary: 12000 }
])

const basicColumns: TableColumn[] = [
  { 
    prop: 'name', 
    label: '姓名', 
    editable: true,
    editRules: [
      { required: true, message: '姓名不能为空' }
    ]
  },
  { 
    prop: 'age', 
    label: '年龄', 
    editable: true,
    editType: 'number',
    min: 18,
    max: 65,
    editRules: [
      { required: true, message: '年龄不能为空' },
      { min: 18, max: 65, message: '年龄必须在18-65之间' }
    ]
  },
  { 
    prop: 'department', 
    label: '部门',
    editable: true,
    editType: 'select',
    editOptions: [
      { label: '技术部', value: 'tech' },
      { label: '产品部', value: 'product' },
      { label: '设计部', value: 'design' },
      { label: '市场部', value: 'market' }
    ]
  },
  { 
    prop: 'position', 
    label: '职位',
    editable: true
  },
  { 
    prop: 'salary', 
    label: '薪资',
    editable: true,
    editType: 'number',
    min: 0,
    step: 1000,
   
    formatter: (row: unknown) => `¥${row.salary.toLocaleString()}`
  }
]

// 批量编辑数据
const batchData = ref([
  { id: 1, product: '产品A', category: '电子产品', price: 999, stock: 100, status: 'active' },
  { id: 2, product: '产品B', category: '家居用品', price: 299, stock: 200, status: 'active' },
  { id: 3, product: '产品C', category: '服装配饰', price: 199, stock: 50, status: 'inactive' },
  { id: 4, product: '产品D', category: '食品饮料', price: 59, stock: 500, status: 'active' },
  { id: 5, product: '产品E', category: '图书音像', price: 89, stock: 150, status: 'active' }
])

const batchColumns: TableColumn[] = [
  { prop: 'product', label: '产品名称', editable: true },
  { 
    prop: 'category', 
    label: '分类',
    editable: true,
    editType: 'select',
    editOptions: [
      { label: '电子产品', value: '电子产品' },
      { label: '家居用品', value: '家居用品' },
      { label: '服装配饰', value: '服装配饰' },
      { label: '食品饮料', value: '食品饮料' },
      { label: '图书音像', value: '图书音像' }
    ]
  },
  { 
    prop: 'price', 
    label: '价格',
    editable: true,
    editType: 'number',
    min: 0,
    precision: 2,
   
    formatter: (row: unknown) => `¥${row.price.toFixed(2)}`
  },
  { 
    prop: 'stock', 
    label: '库存',
    editable: true,
    editType: 'number',
    min: 0
  },
  { 
    prop: 'status', 
    label: '状态',
    editable: true,
    editType: 'switch',
    activeValue: 'active',
    inactiveValue: 'inactive',
   
    formatter: (row: unknown) => row.status === 'active' ? '上架' : '下架'
  }
]

// 高级编辑数据
const advancedData = ref(generateAdvancedData(20))
const savedCount = ref(0)
const advancedTableRef = ref()

const advancedColumns: TableColumn[] = [
  { 
    prop: 'code', 
    label: '员工编号',
    width: 120,
    editable: false
  },
  { 
    prop: 'name', 
    label: '姓名',
    editable: true,
    editRules: [{ required: true, message: '姓名必填' }]
  },
  { 
    prop: 'email', 
    label: '邮箱',
    minWidth: 180,
    editable: true,
    editRules: [
      { required: true, message: '邮箱必填' },
      { pattern: /^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/, message: '邮箱格式不正确' }
    ]
  },
  { 
    prop: 'phone', 
    label: '手机号',
    width: 140,
    editable: true,
    editRules: [
      { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确' }
    ]
  },
  { 
    prop: 'joinDate', 
    label: '入职日期',
    width: 140,
    editable: true,
    editType: 'date'
  },
  { 
    prop: 'level', 
    label: '级别',
    width: 120,
    editable: true,
    editType: 'select',
    editOptions: [
      { label: 'P1-初级', value: 'P1' },
      { label: 'P2-中级', value: 'P2' },
      { label: 'P3-高级', value: 'P3' },
      { label: 'P4-资深', value: 'P4' },
      { label: 'P5-专家', value: 'P5' }
    ]
  },
  { 
    prop: 'performance', 
    label: '绩效评分',
    width: 120,
    editable: true,
    editType: 'number',
    min: 0,
    max: 100,
    step: 0.5,
    precision: 1
  },
  { 
    prop: 'remark', 
    label: '备注',
    minWidth: 200,
    editable: true,
    editType: 'textarea',
    rows: 2
  }
]

// 表单编辑数据
const formData = reactive({
  remark: '',
  items: [
    { id: 1, name: '商品1', quantity: 10, price: 100, amount: 1000 },
    { id: 2, name: '商品2', quantity: 5, price: 200, amount: 1000 },
    { id: 3, name: '商品3', quantity: 8, price: 150, amount: 1200 }
  ]
})

const formColumns: TableColumn[] = [
  { 
    prop: 'name', 
    label: '商品名称',
    editable: true,
    editRules: [{ required: true, message: '商品名称必填' }]
  },
  { 
    prop: 'quantity', 
    label: '数量',
    width: 120,
    editable: true,
    editType: 'number',
    min: 1,
    editRules: [{ required: true, message: '数量必填' }]
  },
  { 
    prop: 'price', 
    label: '单价',
    width: 120,
    editable: true,
    editType: 'number',
    min: 0,
    precision: 2,
   
    formatter: (row: unknown) => `¥${row.price.toFixed(2)}`
  },
  { 
    prop: 'amount', 
    label: '金额',
    width: 120,
   
    formatter: (row: unknown) => `¥${row.amount.toFixed(2)}`
  }
]

// 计算属性
const editingCount = computed(() => {
  if (!advancedTableRef.value) return 0
  return advancedTableRef.value.getEditingRows().length
})

const totalQuantity = computed(() => {
  return formData.items.reduce((sum, item) => sum + item.quantity, 0)
})

const totalAmount = computed(() => {
  return formData.items.reduce((sum, item) => sum + item.amount, 0)
})

// 生成高级数据
function generateAdvancedData(count: number) {
  return Array.from({ length: count }, (_, i) => ({
    id: i + 1,
    code: `EMP${String(i + 1).padStart(4, '0')}`,
    name: `员工${i + 1}`,
    email: `employee${i + 1}@company.com`,
    phone: `138${String(Math.floor(Math.random() * *********)).padStart(8, '0')}`,
    joinDate: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    level: ['P1', 'P2', 'P3', 'P4', 'P5'][Math.floor(Math.random() * 5)],
    performance: Math.floor(Math.random() * 40) + 60,
    remark: ''
  }))
}

// 格式化金额
function formatMoney(value: number) {
  return `¥${value.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`
}

// 事件处理
   
const handleAdd = (row: unknown) => {
  console.log('新增行：', row)
}

   
const handleSave = (row: unknown, index: number, changes: unknown) => {
  console.log('保存行：', row, index, changes)
  savedCount.value++
  ElMessage.success('保存成功')
}

   
const handleDelete = (row: unknown, index: number) => {
  console.log('删除行：', row, index)
}

   
const handleBatchSave = (rows: unknown[]) => {
  console.log('批量保存：', rows)
  ElMessage.success(`批量保存 ${rows.length} 条数据成功`)
}

const importData = () => {
  ElMessage.info('导入功能开发中...')
}

const exportData = () => {
  ElMessage.info('导出功能开发中...')
}

const submitForm = () => {
  // 自动计算金额
  formData.items.forEach(item => {
    item.amount = item.quantity * item.price
  })
  
  ElMessage.success('表单提交成功')
  console.log('表单数据：', formData)
}

const resetForm = () => {
  formData.items = [
    { id: 1, name: '商品1', quantity: 10, price: 100, amount: 1000 },
    { id: 2, name: '商品2', quantity: 5, price: 200, amount: 1000 },
    { id: 3, name: '商品3', quantity: 8, price: 150, amount: 1200 }
  ]
  formData.remark = ''
}

// 监听表单项变化，自动计算金额
import { watch } from 'vue'
watch(() => formData.items, (items) => {
  items.forEach(item => {
    if (item.quantity && item.price) {
      item.amount = item.quantity * item.price
    }
  })
}, { deep: true })
</script>

<style lang="scss" scoped>
.editable-table-demo {
  padding: 20px;
  
  h1 {
    margin-bottom: 20px;
    font-size: 24px;
    color: #303133;
  }
  
  .demo-card {
    margin-bottom: 20px;
    
    h3 {
      margin: 0;
      font-size: 18px;
      color: #303133;
    }
  }
  
  .stats {
    display: flex;
    justify-content: space-around;
    
    .el-statistic {
      text-align: center;
    }
  }
  
  :deep(table) {
    td {
      padding: 8px 12px;
    }
  }
}
</style>