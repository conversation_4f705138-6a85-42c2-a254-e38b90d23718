<template>
  <div class="date-range-picker-demo">
    <h1>日期范围选择器演示</h1>
    
    <!-- 基础用法 -->
    <el-card class="demo-card">
      <template #header>
        <h3>基础用法</h3>
      </template>
      
      <div class="demo-content">
        <DateRangePicker
          v-model="dateRange1"
          @change="handleDateChange"
        />
        
        <div class="result" v-if="dateRange1">
          <p>选择的日期范围：{{ dateRange1[0] }} 至 {{ dateRange1[1] }}</p>
        </div>
      </div>
    </el-card>
    
    <!-- 快捷选项 -->
    <el-card class="demo-card">
      <template #header>
        <h3>快捷选项和预设</h3>
      </template>
      
      <div class="demo-content">
        <DateRangePicker
          v-model="dateRange2"
          :show-shortcuts="true"
          :allow-custom-shortcuts="true"
          :show-info="true"
          :show-week-info="true"
        />
        
        <el-alert type="info" :closable="false" style="margin-top: 20px;">
          <template #title>
            <div class="feature-list">
              <h4>功能特性：</h4>
              <ul>
                <li>📅 <strong>快捷选项</strong> - 今天、本周、本月、本季度、本年等</li>
                <li>⚡ <strong>自定义快捷</strong> - 可添加自定义的快捷选项</li>
                <li>⭐ <strong>保存预设</strong> - 将常用日期范围保存为预设</li>
                <li>📊 <strong>日期信息</strong> - 显示选择的天数和周数</li>
                <li>💾 <strong>本地存储</strong> - 自定义选项和预设保存到本地</li>
                <li>🎯 <strong>相对日期</strong> - 支持"最近N天"等相对日期</li>
              </ul>
            </div>
          </template>
        </el-alert>
      </div>
    </el-card>
    
    <!-- 自定义格式 -->
    <el-card class="demo-card">
      <template #header>
        <h3>自定义格式</h3>
      </template>
      
      <div class="demo-content">
        <el-row :gutter="20">
          <el-col :span="12">
            <h4>中文格式</h4>
            <DateRangePicker
              v-model="dateRange3"
              format="YYYY年MM月DD日"
              value-format="YYYY-MM-DD"
              range-separator="到"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </el-col>
          
          <el-col :span="12">
            <h4>月份选择</h4>
            <el-date-picker
              v-model="monthRange"
              type="monthrange"
              format="YYYY年MM月"
              value-format="YYYY-MM"
              range-separator="至"
              start-placeholder="开始月份"
              end-placeholder="结束月份"
             />
          </el-col>
        </el-row>
      </div>
    </el-card>
    
    <!-- 禁用日期 -->
    <el-card class="demo-card">
      <template #header>
        <h3>禁用日期和默认时间</h3>
      </template>
      
      <div class="demo-content">
        <DateRangePicker
          v-model="dateRange4"
          :disabled-date="disabledDate"
          :default-time="[
            new Date(2000, 0, 1, 9, 0, 0),
            new Date(2000, 0, 1, 18, 0, 0)
          ]"
        />
        
        <p class="tip">* 只能选择今天及以后的日期，默认时间为 9:00 - 18:00</p>
      </div>
    </el-card>
    
    <!-- 不同尺寸 -->
    <el-card class="demo-card">
      <template #header>
        <h3>不同尺寸</h3>
      </template>
      
      <div class="demo-content">
        <el-row :gutter="20">
          <el-col :span="8">
            <h4>大尺寸</h4>
            <DateRangePicker
              v-model="dateRange5"
              size="large"
              :show-shortcuts="false"
            />
          </el-col>
          
          <el-col :span="8">
            <h4>默认尺寸</h4>
            <DateRangePicker
              v-model="dateRange6"
              size="default"
              :show-shortcuts="false"
            />
          </el-col>
          
          <el-col :span="8">
            <h4>小尺寸</h4>
            <DateRangePicker
              v-model="dateRange7"
              size="small"
              :show-shortcuts="false"
            />
          </el-col>
        </el-row>
      </div>
    </el-card>
    
    <!-- 隐藏快捷选项 -->
    <el-card class="demo-card">
      <template #header>
        <h3>隐藏快捷选项面板</h3>
      </template>
      
      <div class="demo-content">
        <DateRangePicker
          v-model="dateRange8"
          :hide-shortcuts="true"
          :show-footer="true"
        />
        
        <p class="tip">* 快捷选项在下拉面板内显示，并添加了底部操作按钮</p>
      </div>
    </el-card>
    
    <!-- 业务场景示例 -->
    <el-card class="demo-card">
      <template #header>
        <h3>业务场景示例</h3>
      </template>
      
      <div class="demo-content">
        <el-form :model="reportForm" label-width="120px">
          <el-form-item label="考勤统计时间">
            <DateRangePicker
              v-model="reportForm.attendanceRange"
              :shortcuts="attendanceShortcuts"
            />
          </el-form-item>
          
          <el-form-item label="薪资发放月份">
            <DateRangePicker
              v-model="reportForm.salaryRange"
              :shortcuts="salaryShortcuts"
            />
          </el-form-item>
          
          <el-form-item label="合同有效期">
            <DateRangePicker
              v-model="reportForm.contractRange"
              :show-shortcuts="false"
              :default-time="[
                new Date(2000, 0, 1, 0, 0, 0),
                new Date(2000, 0, 1, 23, 59, 59)
              ]"
            />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="generateReport">生成报表</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'DateRangePickerDemo'
})
 
import { ref, reactive } from 'vue'
import HrDateRangePicker from '@/components/common/HrDateRangePicker.vue'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'

// 基础用法
const dateRange1 = ref<[string, string] | null>(null)

// 快捷选项
const dateRange2 = ref<[string, string] | null>(null)

// 自定义格式
const dateRange3 = ref<[string, string] | null>(null)
const monthRange = ref<[string, string] | null>(null)

// 禁用日期
const dateRange4 = ref<[string, string] | null>(null)

// 不同尺寸
const dateRange5 = ref<[string, string] | null>(null)
const dateRange6 = ref<[string, string] | null>(null)
const dateRange7 = ref<[string, string] | null>(null)

// 隐藏快捷选项
const dateRange8 = ref<[string, string] | null>(null)

// 业务场景
const reportForm = reactive({
  attendanceRange: null as [string, string] | null,
  salaryRange: null as [string, string] | null,
  contractRange: null as [string, string] | null
})

// 考勤统计快捷选项
const attendanceShortcuts = [
  {
    text: '本月',
    value: () => {
      const start = dayjs().startOf('month').toDate()
      const end = dayjs().endOf('month').toDate()
      return [start, end]
    }
  },
  {
    text: '上月',
    value: () => {
      const start = dayjs().subtract(1, 'month').startOf('month').toDate()
      const end = dayjs().subtract(1, 'month').endOf('month').toDate()
      return [start, end]
    }
  },
  {
    text: '本季度',
    value: () => {
      const start = dayjs().startOf('quarter').toDate()
      const end = dayjs().endOf('quarter').toDate()
      return [start, end]
    }
  },
  {
    text: '上季度',
    value: () => {
      const start = dayjs().subtract(1, 'quarter').startOf('quarter').toDate()
      const end = dayjs().subtract(1, 'quarter').endOf('quarter').toDate()
      return [start, end]
    }
  }
]

// 薪资发放快捷选项
const salaryShortcuts = [
  {
    text: '本月',
    value: () => {
      const start = dayjs().startOf('month').toDate()
      const end = dayjs().endOf('month').toDate()
      return [start, end]
    }
  },
  {
    text: '最近3个月',
    value: () => {
      const end = dayjs().endOf('month').toDate()
      const start = dayjs().subtract(2, 'month').startOf('month').toDate()
      return [start, end]
    }
  },
  {
    text: '最近6个月',
    value: () => {
      const end = dayjs().endOf('month').toDate()
      const start = dayjs().subtract(5, 'month').startOf('month').toDate()
      return [start, end]
    }
  },
  {
    text: '本年度',
    value: () => {
      const start = dayjs().startOf('year').toDate()
      const end = dayjs().endOf('year').toDate()
      return [start, end]
    }
  }
]

// 处理日期变化
const handleDateChange = (value: [string, string] | null) => {
  console.log('日期范围变化：', value)
  if (value) {
    ElMessage.info(`选择了 ${value[0]} 至 ${value[1]}`)
  }
}

// 禁用今天之前的日期
const disabledDate = (date: Date) => {
  return date < new Date(new Date().setHours(0, 0, 0, 0))
}

// 生成报表
const generateReport = () => {
  if (!reportForm.attendanceRange) {
    ElMessage.warning('请选择考勤统计时间')
    return
  }
  
  ElMessage.success('报表生成中...')
  console.log('报表参数：', reportForm)
}
</script>

<style lang="scss" scoped>
.date-range-picker-demo {
  padding: 20px;
  
  h1 {
    margin-bottom: 20px;
    font-size: 24px;
    color: #303133;
  }
  
  .demo-card {
    margin-bottom: 20px;
    
    h3 {
      margin: 0;
      font-size: 18px;
      color: #303133;
    }
    
    h4 {
      margin: 0 0 10px 0;
      font-size: 14px;
      color: #606266;
    }
  }
  
  .demo-content {
    .result {
      margin-top: 20px;
      padding: 15px;
      background: #f5f7fa;
      border-radius: 4px;
      
      p {
        margin: 0;
        color: #606266;
      }
    }
    
    .tip {
      margin-top: 10px;
      font-size: 12px;
      color: #909399;
    }
    
    .feature-list {
      h4 {
        margin: 0 0 10px 0;
        font-size: 14px;
        color: #303133;
      }
      
      ul {
        margin: 0;
        padding-left: 20px;
        
        li {
          line-height: 1.8;
          color: #606266;
          
          strong {
            color: #303133;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .date-range-picker-demo {
    padding: 10px;
    
    .el-row {
      .el-col {
        margin-bottom: 20px;
      }
    }
  }
}
</style>