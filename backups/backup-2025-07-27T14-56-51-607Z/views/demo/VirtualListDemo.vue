<template>
  <div class="virtual-list-demo">
    <h1>虚拟列表演示</h1>
    
    <!-- 基础演示 -->
    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <h3>基础演示 - 10万条数据</h3>
          <el-button type="primary" size="small" @click="generateData(100000)">
            生成数据
          </el-button>
        </div>
      </template>
      
      <div class="demo-content">
        <VirtualList
          ref="virtualListRef"
          :data="basicData"
          :height="400"
          :item-height="50"
          @scroll="handleScroll"
          @reach-bottom="handleReachBottom"
        >
          <template #default="{ item, index }">
            <div class="list-item">
              <el-avatar :size="36">{{ index + 1 }}</el-avatar>
              <div class="item-content">
                <div class="item-title">{{ item.name }}</div>
                <div class="item-desc">{{ item.description }}</div>
              </div>
              <el-tag>{{ item.status }}</el-tag>
            </div>
          </template>
        </VirtualList>
        
        <div class="info-panel">
          <el-descriptions :column="4" border size="small">
            <el-descriptions-item label="数据总量">{{ basicData.length }}</el-descriptions-item>
            <el-descriptions-item label="可见范围">{{ visibleRange.start }} - {{ visibleRange.end }}</el-descriptions-item>
            <el-descriptions-item label="渲染数量">{{ visibleRange.end - visibleRange.start }}</el-descriptions-item>
            <el-descriptions-item label="滚动位置">{{ scrollInfo.scrollTop }}px</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-card>
    
    <!-- 员工列表演示 -->
    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <h3>员工列表 - 支持搜索</h3>
          <div class="header-actions">
            <el-input 
              v-model="searchKeyword" 
              placeholder="搜索" 
              style="width: 200px"
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-button @click="generateEmployees(50000)">生成5万员工</el-button>
          </div>
        </div>
      </template>
      
      <div class="demo-content">
        <VirtualList
          :data="filteredEmployees"
          :height="500"
          :item-height="80"
          :buffer="5"
          key-field="id"
          :loading="loading"
        >
          <template #default="{ item }">
            <div class="employee-item">
              <el-avatar :size="60" :src="item.avatar">
                {{ item.name.charAt(0) }}
              </el-avatar>
              <div class="employee-info">
                <div class="employee-header">
                  <span class="employee-name">{{ item.name }}</span>
                  <el-tag size="small">{{ item.code }}</el-tag>
                  <el-tag 
                    :type="item.status === '在职' ? 'success' : 'info'" 
                    size="small"
                  >
                    {{ item.status }}
                  </el-tag>
                </div>
                <div class="employee-details">
                  <span><el-icon><OfficeBuilding /></el-icon> {{ item.department }}</span>
                  <span><el-icon><User /></el-icon> {{ item.position }}</span>
                  <span><el-icon><Phone /></el-icon> {{ item.phone }}</span>
                  <span><el-icon><Message /></el-icon> {{ item.email }}</span>
                </div>
              </div>
              <div class="employee-actions">
                <el-button text type="primary" size="small">查看</el-button>
                <el-button text type="primary" size="small">编辑</el-button>
              </div>
            </div>
          </template>
          
          <template #empty>
            <el-empty description="没有匹配的员工"  />
          </template>
        </VirtualList>
      </div>
    </el-card>
    
    <!-- 动态高度演示 -->
    <el-card class="demo-card">
      <template #header>
        <h3>动态高度演示</h3>
      </template>
      
      <div class="demo-content">
        <VirtualList
          :data="dynamicData"
          :height="400"
          :item-height="100"
          :dynamic-height="true"
          :estimated-item-height="100"
        >
          <template #default="{ item }">
            <div class="dynamic-item">
              <h4>{{ item.title }}</h4>
              <p>{{ item.content }}</p>
              <div v-if="item.hasImage" class="item-image">
                <img :src="`https://picsum.photos/200/100?random=${item.id}`" alt="">
              </div>
              <div class="item-footer">
                <span>{{ item.author }}</span>
                <span>{{ item.date }}</span>
              </div>
            </div>
          </template>
        </VirtualList>
      </div>
    </el-card>
    
    <!-- 配置演示 -->
    <el-card class="demo-card">
      <template #header>
        <h3>配置演示</h3>
      </template>
      
      <div class="demo-content">
        <el-form :model="config" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="列表高度">
                <el-slider v-model="config.height" :min="200" :max="800" show-input  />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="项目高度">
                <el-slider v-model="config.itemHeight" :min="30" :max="150" show-input  />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="缓冲区">
                <el-slider v-model="config.buffer" :min="1" :max="10" show-input  />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="数据量">
                <el-input-number v-model="config.dataCount" :min="100" :max="100000" :step="1000"   />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="显示滚动条">
                <el-switch v-model="config.showScrollbar"  />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="快速跳转">
                <el-switch v-model="config.showQuickJump"  />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-form-item>
            <el-button type="primary" @click="applyConfig">应用配置</el-button>
            <el-button @click="resetConfig">重置</el-button>
          </el-form-item>
        </el-form>
        
        <VirtualList
          ref="configListRef"
          :data="configData"
          :height="config.height"
          :item-height="config.itemHeight"
          :buffer="config.buffer"
          :show-scrollbar="config.showScrollbar"
          :show-quick-jump="config.showQuickJump"
        >
          <template #default="{ item, index }">
            <div class="config-item" :style="{ height: config.itemHeight + 'px' }">
              <span class="item-index">{{ index + 1 }}</span>
              <span class="item-text">{{ item.text }}</span>
              <el-progress 
                :percentage="item.progress" 
                :stroke-width="6"
                style="flex: 1"
               />
            </div>
          </template>
        </VirtualList>
      </div>
    </el-card>
    
    <!-- 性能对比 -->
    <el-card class="demo-card">
      <template #header>
        <h3>性能对比</h3>
      </template>
      
      <div class="demo-content">
        <el-alert type="info" :closable="false">
          <template #title>
            <div class="performance-info">
              <h4>虚拟列表 vs 普通列表性能对比</h4>
              <p>虚拟列表通过只渲染可见区域的元素，大幅减少DOM节点数量，提升渲染性能</p>
              <ul>
                <li>📊 <strong>渲染速度</strong>：10万条数据毫秒级渲染</li>
                <li>💾 <strong>内存占用</strong>：仅保持可见区域DOM</li>
                <li>🚀 <strong>滚动性能</strong>：60fps流畅滚动</li>
                <li>🔧 <strong>响应式更新</strong>：数据变化即时响应</li>
              </ul>
            </div>
          </template>
        </el-alert>
        
        <div class="performance-charts">
          <div class="chart-item">
            <h5>DOM节点数量对比</h5>
            <el-progress :percentage="5" color="#67C23A"  />
            <span class="chart-label">虚拟列表：~50个节点</span>
            <el-progress :percentage="100" color="#F56C6C"  />
            <span class="chart-label">普通列表：100,000个节点</span>
          </div>
          
          <div class="chart-item">
            <h5>渲染时间对比</h5>
            <el-progress :percentage="10" color="#67C23A"  />
            <span class="chart-label">虚拟列表：~50ms</span>
            <el-progress :percentage="90" color="#F56C6C"  />
            <span class="chart-label">普通列表：~5000ms</span>
          </div>
        </div>
      </div>
    </el-card>
    
    <!-- API演示 -->
    <el-card class="demo-card">
      <template #header>
        <h3>API演示</h3>
      </template>
      
      <div class="demo-content">
        <div class="api-actions">
          <el-button @click="scrollToTop">滚动到顶部</el-button>
          <el-button @click="scrollToBottom">滚动到底部</el-button>
          <el-button @click="scrollToMiddle">滚动到中间</el-button>
          <el-input-number 
            v-model="jumpToIndex" 
            :min="0" 
            :max="basicData.length - 1"
            placeholder="索引"
            style="width: 150px"
            />
          <el-button @click="scrollToSpecific">跳转到指定位置</el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed } from 'vue'
import HrVirtualList from '@/components/common/HrVirtualList.vue'
import { 
  Search, 
  OfficeBuilding, 
  User, 
  Phone, 
  Message 
} from '@element-plus/icons-vue'

// 基础数据
const basicData = ref<any[]>([])
const virtualListRef = ref()
const visibleRange = ref({ start: 0, end: 0 })
const scrollInfo = ref({ scrollTop: 0 })

// 员工数据
const employees = ref<any[]>([])
const searchKeyword = ref('')
const loading = ref(false)

// 动态数据
const dynamicData = ref<any[]>([])

// 配置数据
const configData = ref<any[]>([])
const configListRef = ref()
const jumpToIndex = ref(0)

// 配置项
const config = reactive({
  height: 400,
  itemHeight: 50,
  buffer: 3,
  dataCount: 10000,
  showScrollbar: true,
  showQuickJump: true
})

// 部门和职位
const departments = ['技术部', '产品部', '市场部', '人事部', '财务部', '行政部', '运营部']
const positions = ['经理', '主管', '工程师', '专员', '助理', '总监']

// 过滤员工
const filteredEmployees = computed(() => {
  if (!searchKeyword.value) return employees.value
  
  const keyword = searchKeyword.value.toLowerCase()
  return employees.value.filter(emp => 
    emp.name.toLowerCase().includes(keyword) ||
    emp.code.toLowerCase().includes(keyword) ||
    emp.department.toLowerCase().includes(keyword) ||
    emp.position.toLowerCase().includes(keyword)
  )
})

// 生成基础数据
const generateData = (count: number) => {
  basicData.value = Array.from({ length: count }, (_, i) => ({
    id: i + 1,
    name: `数据项 ${i + 1}`,
    description: `这是第 ${i + 1} 条数据的描述信息`,
    status: ['活跃', '待处理', '已完成'][Math.floor(Math.random() * 3)]
  }))
}

// 生成员工数据
const generateEmployees = (count: number) => {
  loading.value = true
  setTimeout(() => {
    employees.value = Array.from({ length: count }, (_, i) => ({
      id: `EMP${String(i + 1).padStart(6, '0')}`,
      name: `员工${i + 1}`,
      code: `E${String(i + 1).padStart(5, '0')}`,
      department: departments[Math.floor(Math.random() * departments.length)],
      position: positions[Math.floor(Math.random() * positions.length)],
      phone: `138${String(Math.floor(Math.random() * *********)).padStart(8, '0')}`,
      email: `employee${i + 1}@company.com`,
      status: Math.random() > 0.1 ? '在职' : '离职',
      avatar: ''
    }))
    loading.value = false
  }, 500)
}

// 生成动态数据
const generateDynamicData = () => {
  dynamicData.value = Array.from({ length: 1000 }, (_, i) => ({
    id: i + 1,
    title: `文章标题 ${i + 1}`,
    content: `这是文章的内容，动态高度演示。${Math.random() > 0.5 ? '这段内容比较长，包含了更多的文字信息。虚拟列表能够很好地处理动态高度的情况，通过估算高度和实时计算来保证滚动的平滑性。' : '短内容'}`,
    hasImage: Math.random() > 0.7,
    author: `作者${i + 1}`,
    date: new Date(Date.now() - Math.random() * 86400000 * 30).toLocaleDateString()
  }))
}

// 生成配置演示数据
const generateConfigData = () => {
  configData.value = Array.from({ length: config.dataCount }, (_, i) => ({
    id: i + 1,
    text: `配置项 ${i + 1}`,
    progress: Math.floor(Math.random() * 100)
  }))
}

// 滚动事件
const handleScroll = (event: Event, range: { start: number, end: number }) => {
  visibleRange.value = range
  scrollInfo.value.scrollTop = Math.round((event.target as HTMLElement).scrollTop)
}

// 到达底部
const handleReachBottom = () => {
  console.log('到达底部，可以加载更多数据')
}

// 搜索
const handleSearch = () => {
  // 搜索已经通过计算属性过滤了
}

// 应用配置
const applyConfig = () => {
  generateConfigData()
}

// 重置配置
const resetConfig = () => {
  Object.assign(config, {
    height: 400,
    itemHeight: 50,
    buffer: 3,
    dataCount: 10000,
    showScrollbar: true,
    showQuickJump: true
  })
  generateConfigData()
}

// 滚动控制
const scrollToTop = () => {
  virtualListRef.value?.scrollToTop()
}

const scrollToBottom = () => {
  virtualListRef.value?.scrollToBottom()
}

const scrollToMiddle = () => {
  const middle = Math.floor(basicData.value.length / 2)
  virtualListRef.value?.scrollToIndex(middle)
}

const scrollToSpecific = () => {
  virtualListRef.value?.scrollToIndex(jumpToIndex.value)
}

// 初始化数据
generateData(10000)
generateEmployees(1000)
generateDynamicData()
generateConfigData()
</script>

<style lang="scss" scoped>
.virtual-list-demo {
  padding: 20px;
  
  h1 {
    margin-bottom: 20px;
    font-size: 24px;
    color: #303133;
  }
  
  .demo-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      h3 {
        margin: 0;
        font-size: 18px;
        color: #303133;
      }
      
      .header-actions {
        display: flex;
        gap: 12px;
      }
    }
  }
  
  .demo-content {
    .info-panel {
      margin-top: 16px;
    }
    
    // 基础列表项样式
    .list-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 0 16px;
      height: 100%;
      border-bottom: 1px solid #ebeef5;
      
      .item-content {
        flex: 1;
        
        .item-title {
          font-size: 14px;
          color: #303133;
          font-weight: 500;
        }
        
        .item-desc {
          font-size: 12px;
          color: #909399;
          margin-top: 2px;
        }
      }
    }
    
    // 员工列表项样式
    .employee-item {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 12px 16px;
      border-bottom: 1px solid #ebeef5;
      
      .employee-info {
        flex: 1;
        
        .employee-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;
          
          .employee-name {
            font-size: 16px;
            font-weight: 500;
            color: #303133;
          }
        }
        
        .employee-details {
          display: flex;
          gap: 16px;
          font-size: 12px;
          color: #606266;
          
          span {
            display: flex;
            align-items: center;
            gap: 4px;
            
            .el-icon {
              font-size: 14px;
              color: #909399;
            }
          }
        }
      }
      
      .employee-actions {
        display: flex;
        gap: 8px;
      }
    }
    
    // 动态高度项样式
    .dynamic-item {
      padding: 16px;
      border-bottom: 1px solid #ebeef5;
      
      h4 {
        margin: 0 0 8px 0;
        font-size: 16px;
        color: #303133;
      }
      
      p {
        margin: 0 0 12px 0;
        font-size: 14px;
        color: #606266;
        line-height: 1.6;
      }
      
      .item-image {
        margin-bottom: 12px;
        
        img {
          max-width: 100%;
          border-radius: 4px;
        }
      }
      
      .item-footer {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        color: #909399;
      }
    }
    
    // 配置项样式
    .config-item {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 0 16px;
      border-bottom: 1px solid #ebeef5;
      
      .item-index {
        width: 60px;
        font-weight: 500;
        color: #606266;
      }
      
      .item-text {
        width: 120px;
        color: #303133;
      }
    }
    
    // 性能信息
    .performance-info {
      h4 {
        margin: 0 0 8px 0;
        font-size: 16px;
        color: #303133;
      }
      
      p {
        margin: 0 0 12px 0;
        color: #606266;
      }
      
      ul {
        margin: 0;
        padding-left: 20px;
        
        li {
          line-height: 1.8;
          color: #606266;
          
          strong {
            color: #303133;
          }
        }
      }
    }
    
    .performance-charts {
      margin-top: 20px;
      display: flex;
      gap: 40px;
      
      .chart-item {
        flex: 1;
        
        h5 {
          margin: 0 0 12px 0;
          font-size: 14px;
          color: #303133;
        }
        
        .el-progress {
          margin-bottom: 4px;
        }
        
        .chart-label {
          display: block;
          font-size: 12px;
          color: #909399;
          margin-bottom: 12px;
        }
      }
    }
    
    // API操作
    .api-actions {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-wrap: wrap;
    }
  }
}

// 响应式
@media (max-width: 768px) {
  .virtual-list-demo {
    padding: 10px;
    
    .demo-card {
      .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
        
        .header-actions {
          width: 100%;
          flex-direction: column;
        }
      }
    }
    
    .employee-item {
      flex-direction: column;
      align-items: flex-start;
      
      .employee-details {
        flex-wrap: wrap;
        gap: 8px;
      }
      
      .employee-actions {
        margin-top: 8px;
      }
    }
    
    .performance-charts {
      flex-direction: column;
      gap: 20px;
    }
  }
}
</style>