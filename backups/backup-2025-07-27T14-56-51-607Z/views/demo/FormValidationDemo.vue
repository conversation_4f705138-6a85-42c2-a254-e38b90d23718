<template>
  <div class="form-validation-demo">
    <hr-page-header title="表单验证增强演示" :show-back="true">
      演示实时验证反馈、密码强度、字符计数等功能
    </hr-page-header>

    <!-- 基础示例 -->
    <el-card class="demo-section">
      <template #header>
        <span>实时验证反馈</span>
      </template>
      
      <RealtimeForm
        ref="basicFormRef"
        :model="basicForm"
        :rules="basicRules"
        label-width="120px"
        @validate="handleValidate"
        @field-change="handleFieldChange"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <RealtimeFormItem
              prop="username"
              label="用户名"
              hint="4-16位字母、数字或下划线"
              success-text="用户名可用"
            >
              <el-input
                v-model="basicForm.username"
                placeholder="请输入用户名"
                clearable
                />
            </RealtimeFormItem>
            
            <RealtimeFormItem
              prop="email"
              label="邮箱"
              hint="请输入有效的邮箱地址"
            >
              <el-input
                v-model="basicForm.email"
                placeholder="例如：<EMAIL>"
                clearable
                />
            </RealtimeFormItem>
            
            <RealtimeFormItem
              prop="mobile"
              label="手机号"
              hint="中国大陆手机号码"
            >
              <el-input
                v-model="basicForm.mobile"
                placeholder="请输入手机号"
                clearable
                />
            </RealtimeFormItem>
          </el-col>
          
          <el-col :span="12">
            <RealtimeFormItem
              prop="password"
              label="密码"
              hint="至少8位，包含大小写字母、数字和特殊字符"
              :show-strength="true"
            >
              <el-input
                v-model="basicForm.password"
                type="password"
                placeholder="请输入密码"
                show-password
                clearable
                />
            </RealtimeFormItem>
            
            <RealtimeFormItem
              prop="confirmPassword"
              label="确认密码"
              hint="请再次输入密码"
            >
              <el-input
                v-model="basicForm.confirmPassword"
                type="password"
                placeholder="请再次输入密码"
                show-password
                clearable
                />
            </RealtimeFormItem>
            
            <RealtimeFormItem
              prop="age"
              label="年龄"
              hint="请输入1-150之间的数字"
            >
              <el-input-number
                v-model="basicForm.age"
                :min="1"
                :max="150"
                placeholder="请输入年龄"
                />
            </RealtimeFormItem>
          </el-col>
        </el-row>
        
        <el-form-item>
          <el-button type="primary" @click="submitBasicForm">提交</el-button>
          <el-button @click="resetBasicForm">重置</el-button>
        </el-form-item>
      </RealtimeForm>
    </el-card>

    <!-- 高级示例 -->
    <el-card class="demo-section">
      <template #header>
        <span>高级验证功能</span>
      </template>
      
      <RealtimeForm
        ref="advancedFormRef"
        :model="advancedForm"
        :rules="advancedRules"
        label-width="120px"
        :show-validation-hints="true"
        :show-strength-indicator="true"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <RealtimeFormItem
              prop="idCard"
              label="身份证号"
              hint="18位身份证号码"
            >
              <el-input
                v-model="advancedForm.idCard"
                placeholder="请输入身份证号"
                maxlength="18"
                clearable
                />
            </RealtimeFormItem>
            
            <RealtimeFormItem
              prop="bankCard"
              label="银行卡号"
              hint="16-19位银行卡号"
            >
              <el-input
                v-model="advancedForm.bankCard"
                placeholder="请输入银行卡号"
                clearable
                />
            </RealtimeFormItem>
            
            <RealtimeFormItem
              prop="website"
              label="个人网站"
              hint="请输入完整的网址"
            >
              <el-input
                v-model="advancedForm.website"
                placeholder="https://example.com"
                clearable
                />
            </RealtimeFormItem>
            
            <RealtimeFormItem
              prop="introduction"
              label="个人简介"
              hint="简单介绍一下自己"
              :show-count="true"
              :max-length="200"
            >
              <el-input
                v-model="advancedForm.introduction"
                type="textarea"
                :rows="4"
                placeholder="请输入个人简介"
                maxlength="200"
                show-word-limit
                />
            </RealtimeFormItem>
          </el-col>
          
          <el-col :span="12">
            <RealtimeFormItem
              prop="nickname"
              label="昵称"
              hint="支持中英文、数字，2-10个字符"
              :show-count="true"
              :min-length="2"
              :max-length="10"
              success-text="昵称可用"
            >
              <el-input
                v-model="advancedForm.nickname"
                placeholder="请输入昵称"
                clearable
                />
            </RealtimeFormItem>
            
            <RealtimeFormItem
              prop="zipCode"
              label="邮政编码"
              hint="6位数字"
            >
              <el-input
                v-model="advancedForm.zipCode"
                placeholder="请输入邮政编码"
                maxlength="6"
                clearable
                />
            </RealtimeFormItem>
            
            <RealtimeFormItem
              prop="ipAddress"
              label="IP地址"
              hint="IPv4地址格式"
            >
              <el-input
                v-model="advancedForm.ipAddress"
                placeholder="例如：***********"
                clearable
                />
            </RealtimeFormItem>
            
            <RealtimeFormItem
              prop="tags"
              label="标签"
              hint="选择或输入标签"
            >
              <el-select
                v-model="advancedForm.tags"
                multiple
                filterable
                allow-create
                default-first-option
                placeholder="请选择标签"
              >
                <el-option
                  v-for="tag in tagOptions"
                  :key="tag"
                  :label="tag"
                  :value="tag"
                 />
              </el-select>
            </RealtimeFormItem>
          </el-col>
        </el-row>
        
        <el-form-item>
          <el-button type="primary" @click="submitAdvancedForm">提交</el-button>
          <el-button @click="resetAdvancedForm">重置</el-button>
        </el-form-item>
      </RealtimeForm>
    </el-card>

    <!-- 条件验证示例 -->
    <el-card class="demo-section">
      <template #header>
        <span>条件验证</span>
      </template>
      
      <RealtimeForm
        ref="conditionalFormRef"
        :model="conditionalForm"
        :rules="conditionalRules"
        label-width="120px"
      >
        <RealtimeFormItem prop="userType" label="用户类型">
          <el-radio-group v-model="conditionalForm.userType">
            <el-radio value="personal">个人用户</el-radio>
            <el-radio value="enterprise">企业用户</el-radio>
          </el-radio-group>
        </RealtimeFormItem>
        
        <template v-if="conditionalForm.userType === 'personal'">
          <RealtimeFormItem
            prop="realName"
            label="真实姓名"
            hint="请输入真实姓名"
          >
            <el-input
              v-model="conditionalForm.realName"
              placeholder="请输入真实姓名"
              clearable
              />
          </RealtimeFormItem>
          
          <RealtimeFormItem
            prop="birthday"
            label="出生日期"
            hint="选择您的出生日期"
          >
            <el-date-picker
              v-model="conditionalForm.birthday"
              type="date"
              placeholder="选择日期"
              :disabled-date="disabledDate"
             />
          </RealtimeFormItem>
        </template>
        
        <template v-else-if="conditionalForm.userType === 'enterprise'">
          <RealtimeFormItem
            prop="companyName"
            label="企业名称"
            hint="请输入企业全称"
          >
            <el-input
              v-model="conditionalForm.companyName"
              placeholder="请输入企业名称"
              clearable
              />
          </RealtimeFormItem>
          
          <RealtimeFormItem
            prop="businessLicense"
            label="营业执照号"
            hint="请输入18位统一社会信用代码"
          >
            <el-input
              v-model="conditionalForm.businessLicense"
              placeholder="请输入营业执照号"
              maxlength="18"
              clearable
              />
          </RealtimeFormItem>
          
          <RealtimeFormItem
            prop="contactPerson"
            label="联系人"
            hint="请输入联系人姓名"
          >
            <el-input
              v-model="conditionalForm.contactPerson"
              placeholder="请输入联系人"
              clearable
              />
          </RealtimeFormItem>
        </template>
        
        <RealtimeFormItem
          prop="agreement"
          label=""
        >
          <el-checkbox v-model="conditionalForm.agreement">
            我已阅读并同意《用户服务协议》
          </el-checkbox>
        </RealtimeFormItem>
        
        <el-form-item>
          <el-button type="primary" @click="submitConditionalForm">提交</el-button>
          <el-button @click="resetConditionalForm">重置</el-button>
        </el-form-item>
      </RealtimeForm>
    </el-card>

    <!-- 验证状态展示 -->
    <el-card class="demo-section">
      <template #header>
        <span>验证状态</span>
      </template>
      
      <el-descriptions :column="2" border>
        <el-descriptions-item label="当前验证字段">
          {{ currentValidatingField || '无' }}
        </el-descriptions-item>
        <el-descriptions-item label="验证结果">
          <el-tag v-if="lastValidationResult === null">未验证</el-tag>
          <el-tag v-else-if="lastValidationResult" type="success">通过</el-tag>
          <el-tag v-else type="danger">失败</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="错误信息">
          {{ lastValidationMessage || '无' }}
        </el-descriptions-item>
        <el-descriptions-item label="字段变化">
          {{ lastChangedField || '无' }}
        </el-descriptions-item>
      </el-descriptions>
      
      <el-divider   />
      
      <h4>功能特性</h4>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card shadow="never">
            <h5>实时验证</h5>
            <ul>
              <li>输入时立即验证</li>
              <li>防抖处理避免频繁验证</li>
              <li>异步验证支持</li>
              <li>验证状态实时反馈</li>
            </ul>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="never">
            <h5>视觉反馈</h5>
            <ul>
              <li>验证提示信息</li>
              <li>成功状态标识</li>
              <li>错误信息展示</li>
              <li>加载状态显示</li>
            </ul>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="never">
            <h5>增强功能</h5>
            <ul>
              <li>密码强度指示</li>
              <li>字符计数显示</li>
              <li>条件验证规则</li>
              <li>自定义验证器</li>
            </ul>
          </el-card>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import HrPageHeader from '@/components/common/HrPageHeader.vue'
import HrRealtimeForm from '@/components/form/HrRealtimeForm.vue'
import HrRealtimeFormItem from '@/components/form/HrRealtimeFormItem.vue'
import { validators, combineValidators, conditionalValidator } from '@/utils/validators'

// 基础表单
const basicFormRef = ref()
const basicForm = reactive({
  username: '',
  email: '',
  mobile: '',
  password: '',
  confirmPassword: '',
  age: null
})

// 基础验证规则
const basicRules = {
  username: combineValidators(
    validators.required('请输入用户名'),
    validators.username({ realtime: true })
  ),
  email: combineValidators(
    validators.required('请输入邮箱'),
    validators.email({ realtime: true })
  ),
  mobile: combineValidators(
    validators.required('请输入手机号'),
    validators.mobile({ realtime: true })
  ),
  password: combineValidators(
    validators.required('请输入密码'),
    validators.strongPassword({ realtime: true })
  ),
  confirmPassword: combineValidators(
    validators.required('请再次输入密码'),
    validators.confirmPassword('password', '两次输入的密码不一致')
  ),
  age: combineValidators(
    validators.required('请输入年龄'),
    validators.range(1, 150, '年龄必须在1-150之间')
  )
}

// 高级表单
const advancedFormRef = ref()
const advancedForm = reactive({
  idCard: '',
  bankCard: '',
  website: '',
  introduction: '',
  nickname: '',
  zipCode: '',
  ipAddress: '',
  tags: []
})

// 高级验证规则
const advancedRules = {
  idCard: validators.idCard({ realtime: true }),
  bankCard: validators.bankCard({ realtime: true }),
  website: validators.url({ realtime: true, allowEmpty: true }),
  introduction: validators.length(0, 200),
  nickname: combineValidators(
    validators.required('请输入昵称'),
    validators.length(2, 10, '昵称长度必须在2-10个字符之间'),
    validators.custom(/^[\u4e00-\u9fa5a-zA-Z0-9]+$/, '只能包含中英文和数字')
  ),
  zipCode: validators.zipCode({ realtime: true, allowEmpty: true }),
  ipAddress: validators.ip({ realtime: true, allowEmpty: true }),
  tags: {
    type: 'array',
    max: 5,
    message: '最多选择5个标签',
    trigger: 'change'
  }
}

// 条件表单
const conditionalFormRef = ref()
const conditionalForm = reactive({
  userType: 'personal',
  realName: '',
  birthday: '',
  companyName: '',
  businessLicense: '',
  contactPerson: '',
  agreement: false
})

// 条件验证规则
const conditionalRules = computed(() => ({
  userType: validators.required('请选择用户类型'),
  realName: conditionalValidator(
    () => conditionalForm.userType === 'personal',
    combineValidators(
      validators.required('请输入真实姓名'),
      validators.chinese({ min: 2, max: 10 })
    )
  ),
  birthday: conditionalValidator(
    () => conditionalForm.userType === 'personal',
    validators.required('请选择出生日期')
  ),
  companyName: conditionalValidator(
    () => conditionalForm.userType === 'enterprise',
    combineValidators(
      validators.required('请输入企业名称'),
      validators.length(4, 50)
    )
  ),
  businessLicense: conditionalValidator(
    () => conditionalForm.userType === 'enterprise',
    combineValidators(
      validators.required('请输入营业执照号'),
      validators.custom(/^[A-Z0-9]{18}$/, '请输入正确的统一社会信用代码')
    )
  ),
  contactPerson: conditionalValidator(
    () => conditionalForm.userType === 'enterprise',
    validators.required('请输入联系人')
  ),
  agreement: {
   
    validator: (rule: unknown, value: unknown, callback: unknown) => {
      if (!value) {
        callback(new Error('请同意用户服务协议'))
      } else {
        callback()
      }
    },
    trigger: 'change'
  }
}))

// 标签选项
const tagOptions = ['前端开发', '后端开发', '全栈开发', '移动开发', '数据分析', '产品设计', '项目管理']

// 验证状态
const currentValidatingField = ref('')
const lastValidationResult = ref<boolean | null>(null)
const lastValidationMessage = ref('')
const lastChangedField = ref('')

// 处理验证事件
const handleValidate = (field: string, valid: boolean, message?: string) => {
  currentValidatingField.value = field
  lastValidationResult.value = valid
  lastValidationMessage.value = message || ''
}

// 处理字段变化
   
const handleFieldChange = (field: string, value: unknown) => {
  lastChangedField.value = `${field}: ${JSON.stringify(value)}`
}

// 禁用未来日期
const disabledDate = (date: Date) => {
  return date > new Date()
}

// 提交基础表单
const submitBasicForm = async () => {
  try {
    const valid = await basicFormRef.value.validate()
    if (valid) {
      ElMessage.success('基础表单验证通过')
      console.log('基础表单数据:', basicForm)
    }
  } catch (__error) {
    ElMessage.error('请检查表单填写是否正确')
  }
}

// 重置基础表单
const resetBasicForm = () => {
  basicFormRef.value.resetFields()
}

// 提交高级表单
const submitAdvancedForm = async () => {
  try {
    const valid = await advancedFormRef.value.validate()
    if (valid) {
      ElMessage.success('高级表单验证通过')
      console.log('高级表单数据:', advancedForm)
    }
  } catch (__error) {
    ElMessage.error('请检查表单填写是否正确')
  }
}

// 重置高级表单
const resetAdvancedForm = () => {
  advancedFormRef.value.resetFields()
}

// 提交条件表单
const submitConditionalForm = async () => {
  try {
    const valid = await conditionalFormRef.value.validate()
    if (valid) {
      ElMessage.success('条件表单验证通过')
      console.log('条件表单数据:', conditionalForm)
    }
  } catch (__error) {
    ElMessage.error('请检查表单填写是否正确')
  }
}

// 重置条件表单
const resetConditionalForm = () => {
  conditionalFormRef.value.resetFields()
}
</script>

<style lang="scss" scoped>
.form-validation-demo {
  padding: 20px;
  
  .demo-section {
    margin-bottom: 20px;
    
    h4 {
      margin: 0 0 16px;
      font-size: 16px;
      color: var(--el-text-color-primary);
    }
    
    h5 {
      margin: 0 0 12px;
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
    
    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        color: var(--el-text-color-regular);
        font-size: 14px;
      }
    }
    
    .el-card {
      height: 100%;
      
      &.is-never-shadow {
        border: 1px solid var(--el-border-color-lighter);
      }
    }
  }
}
</style>