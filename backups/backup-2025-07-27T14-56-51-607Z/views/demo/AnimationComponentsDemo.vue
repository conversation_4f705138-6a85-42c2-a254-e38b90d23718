<template>
  <div class="animation-components-demo">
    <hr-page-header title="动画组件演示" :show-back="true">
      动画数字组件和进度条动画组件的功能演示
    </hr-page-header>

    <!-- 动画数字组件 -->
    <el-card>
      <template #header>
        <span>动画数字组件</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <h4>基础用法</h4>
          <div class="demo-item">
            <AnimatedNumber :value="basicValue" />
            <el-button @click="basicValue = Math.floor(Math.random() * 10000)" size="small">
              随机数字
            </el-button>
          </div>
          
          <h4>千分位分隔符</h4>
          <div class="demo-item">
            <AnimatedNumber :value="thousandValue" :separator="true" />
            <el-button @click="thousandValue += 12345" size="small">
              增加
            </el-button>
          </div>
          
          <h4>小数位数</h4>
          <div class="demo-item">
            <AnimatedNumber :value="decimalValue" :decimals="2" prefix="¥" />
            <el-button @click="decimalValue = Math.random() * 1000" size="small">
              随机价格
            </el-button>
          </div>
          
          <h4>自定义格式化</h4>
          <div class="demo-item">
            <AnimatedNumber 
              :value="percentValue" 
              :formatter="percentFormatter"
            />
            <el-slider v-model="percentValue" :max="100"  />
          </div>
        </el-col>
        
        <el-col :span="12">
          <h4>前缀后缀</h4>
          <div class="demo-item">
            <AnimatedNumber 
              :value="scoreValue" 
              prefix="得分: " 
              suffix=" 分"
            />
            <el-button @click="scoreValue += 10" size="small">
              加10分
            </el-button>
          </div>
          
          <h4>颜色变化</h4>
          <div class="demo-item">
            <AnimatedNumber 
              :value="colorValue" 
              :colorize="true"
              prefix="$"
              :decimals="2"
            />
            <el-button-group size="small">
              <el-button @click="colorValue += 100">+100</el-button>
              <el-button @click="colorValue -= 100">-100</el-button>
            </el-button-group>
          </div>
          
          <h4>动画控制</h4>
          <div class="demo-item">
            <AnimatedNumber 
              ref="animatedNumberRef"
              :value="controlValue" 
              :duration="2000"
              :autoplay="false"
            />
            <el-space size="small">
              <el-button @click="handleStart">开始</el-button>
              <el-button @click="handlePause">暂停</el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-space>
          </div>
          
          <h4>不同缓动函数</h4>
          <div class="demo-item">
            <el-radio-group v-model="easingType" size="small">
              <el-radio-button label="linear">线性</el-radio-button>
              <el-radio-button label="ease-in">加速</el-radio-button>
              <el-radio-button label="ease-out">减速</el-radio-button>
              <el-radio-button label="ease-in-out">加速减速</el-radio-button>
            </el-radio-group>
            <AnimatedNumber 
              :value="easingValue" 
              :easing="easingType"
              :duration="1500"
            />
            <el-button @click="easingValue = Math.random() * 1000" size="small">
              触发动画
            </el-button>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 状态徽章组件 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <span>状态徽章组件</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <h4>基础状态</h4>
          <el-space wrap>
            <StatusBadge status="primary" />
            <StatusBadge status="success" />
            <StatusBadge status="warning" />
            <StatusBadge status="danger" />
            <StatusBadge status="info" />
          </el-space>
          
          <h4>流程状态</h4>
          <el-space wrap>
            <StatusBadge status="pending" />
            <StatusBadge status="processing" />
            <StatusBadge status="completed" />
            <StatusBadge status="failed" />
            <StatusBadge status="cancelled" />
          </el-space>
          
          <h4>账户状态</h4>
          <el-space wrap>
            <StatusBadge status="active" />
            <StatusBadge status="inactive" />
            <StatusBadge status="disabled" />
            <StatusBadge status="expired" />
            <StatusBadge status="locked" />
          </el-space>
          
          <h4>支付状态</h4>
          <el-space wrap>
            <StatusBadge status="paid" />
            <StatusBadge status="unpaid" />
            <StatusBadge status="partial" />
            <StatusBadge status="refunded" />
            <StatusBadge status="overdue" />
          </el-space>
        </el-col>
        
        <el-col :span="12">
          <h4>动画效果</h4>
          <el-space wrap>
            <StatusBadge status="processing" :blink="true" />
            <StatusBadge status="active" :pulse="true" />
            <StatusBadge 
              status="hot" 
              :gradient="true"
              :gradient-colors="['#f56c6c', '#e6a23c']"
            />
          </el-space>
          
          <h4>自定义配置</h4>
          <el-space wrap>
            <StatusBadge 
              status="custom"
              text="自定义"
              :icon="Star"
              color="#fff"
              background-color="#7c3aed"
            />
            <StatusBadge 
              status="custom"
              text="渐变"
              :gradient="true"
              :gradient-colors="['#667eea', '#764ba2']"
            />
          </el-space>
          
          <h4>显示计数</h4>
          <el-space wrap>
            <StatusBadge status="pending" :show-count="true" :count="5" />
            <StatusBadge status="processing" :show-count="true" :count="128" />
            <StatusBadge status="completed" :show-count="true" :count="9999" />
            <StatusBadge status="failed" :show-count="true" :count="23456" />
          </el-space>
          
          <h4>尺寸</h4>
          <el-space wrap align="center">
            <StatusBadge status="success" size="small" />
            <StatusBadge status="success" size="default" />
            <StatusBadge status="success" size="large" />
          </el-space>
        </el-col>
      </el-row>
    </el-card>

    <!-- 进度条动画组件 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <span>进度条动画组件</span>
      </template>
      
      <h4>基础用法</h4>
      <ProgressBarAnimated :percentage="progress1" />
      <el-slider v-model="progress1" style="margin-top: 20px"  />
      
      <h4>渐变色</h4>
      <ProgressBarAnimated 
        :percentage="progress2"
        :color="['#409eff', '#67c23a']"
      />
      <ProgressBarAnimated 
        :percentage="progress2"
        :color="['#f56c6c', '#e6a23c', '#67c23a']"
        style="margin-top: 10px"
      />
      
      <h4>条纹效果</h4>
      <ProgressBarAnimated 
        :percentage="progress3"
        :striped="true"
        status="primary"
      />
      <ProgressBarAnimated 
        :percentage="progress3"
        :striped="true"
        :striped-animate="true"
        status="success"
        style="margin-top: 10px"
      />
      
      <h4>特效</h4>
      <el-space direction="vertical" :size="10" style="width: 100%">
        <ProgressBarAnimated 
          :percentage="progress4"
          :glow="true"
          status="primary"
        />
        <ProgressBarAnimated 
          :percentage="progress4"
          :wave="true"
          status="success"
        />
        <ProgressBarAnimated 
          :percentage="progress4"
          :pulse="true"
          status="warning"
        />
      </el-space>
      
      <h4>分段显示</h4>
      <ProgressBarAnimated 
        :percentage="progress5"
        :segments="5"
        status="primary"
      />
      
      <h4>里程碑</h4>
      <ProgressBarAnimated 
        :percentage="progress6"
        :milestones="milestones"
        @milestone-reached="handleMilestoneReached"
      />
      <el-button-group style="margin-top: 20px" size="small">
        <el-button @click="progress6 = 0">0%</el-button>
        <el-button @click="progress6 = 25">25%</el-button>
        <el-button @click="progress6 = 50">50%</el-button>
        <el-button @click="progress6 = 75">75%</el-button>
        <el-button @click="progress6 = 100">100%</el-button>
      </el-button-group>
      
      <h4>文本位置</h4>
      <el-space direction="vertical" :size="30" style="width: 100%">
        <ProgressBarAnimated 
          :percentage="progress7"
          text-position="inside"
          :text-inside="true"
          :height="24"
        />
        <ProgressBarAnimated 
          :percentage="progress7"
          text-position="top"
        />
        <ProgressBarAnimated 
          :percentage="progress7"
          text-position="bottom"
        />
      </el-space>
      
      <h4>不确定进度</h4>
      <ProgressBarAnimated 
        :indeterminate="true"
        status="primary"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'AnimationComponentsDemo'
})
 
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Star } from '@element-plus/icons-vue'
import HrPageHeader from '@/components/common/HrPageHeader.vue'
import HrAnimatedNumber from '@/components/common/HrAnimatedNumber.vue'
import HrStatusBadge from '@/components/common/HrStatusBadge.vue'
import HrProgressBarAnimated from '@/components/common/HrProgressBarAnimated.vue'

// 动画数字组件数据
const basicValue = ref(1234)
const thousandValue = ref(1234567)
const decimalValue = ref(123.45)
const percentValue = ref(75)
const scoreValue = ref(86)
const colorValue = ref(1000)
const controlValue = ref(500)
const easingValue = ref(100)
const easingType = ref<'linear' | 'ease-in' | 'ease-out' | 'ease-in-out'>('ease-out')

// 动画数字组件引用
const animatedNumberRef = ref()

// 百分比格式化
const percentFormatter = (value: number) => {
  return `${value.toFixed(0)}%`
}

// 动画控制
const handleStart = () => {
  controlValue.value = Math.random() * 1000
  animatedNumberRef.value?.start()
}

const handlePause = () => {
  animatedNumberRef.value?.pause()
}

const handleReset = () => {
  animatedNumberRef.value?.reset()
}

// 进度条数据
const progress1 = ref(30)
const progress2 = ref(60)
const progress3 = ref(45)
const progress4 = ref(70)
const progress5 = ref(80)
const progress6 = ref(0)
const progress7 = ref(50)

// 里程碑
const milestones = [
  { value: 25, label: '第一阶段' },
  { value: 50, label: '中期目标' },
  { value: 75, label: '冲刺阶段' },
  { value: 100, label: '完成' }
]

// 里程碑到达事件
   
const handleMilestoneReached = (milestone: unknown) => {
  ElMessage.success(`达到里程碑：${milestone.label}`)
}
</script>

<style lang="scss" scoped>
.animation-components-demo {
  padding: 20px;
  
  h4 {
    margin: 20px 0 12px;
    color: #303133;
    
    &:first-child {
      margin-top: 0;
    }
  }
  
  .demo-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    
    .animated-number {
      font-size: 24px;
      font-weight: 500;
      color: #303133;
    }
  }
  
  .el-card {
    :deep(.el-card__body) {
      padding: 20px;
    }
  }
  
  .el-space {
    width: 100%;
  }
}
</style>