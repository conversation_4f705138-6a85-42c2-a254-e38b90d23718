<template>
  <div class="offline-demo">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>离线缓存演示</h3>
          <el-tag :type="isOnline ? 'success' : 'warning'">
            {{ isOnline ? '在线' : '离线' }}
          </el-tag>
        </div>
      </template>
      
      <el-descriptions :column="3" border>
        <el-descriptions-item label="Service Worker">
          <el-tag :type="pwaSupport.serviceWorker ? 'success' : 'danger'">
            {{ pwaSupport.serviceWorker ? '支持' : '不支持' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="通知API">
          <el-tag :type="pwaSupport.notification ? 'success' : 'danger'">
            {{ pwaSupport.notification ? '支持' : '不支持' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="后台同步">
          <el-tag :type="pwaSupport.backgroundSync ? 'success' : 'danger'">
            {{ pwaSupport.backgroundSync ? '支持' : '不支持' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="推送管理">
          <el-tag :type="pwaSupport.pushManager ? 'success' : 'danger'">
            {{ pwaSupport.pushManager ? '支持' : '不支持' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="持久存储">
          <el-tag :type="pwaSupport.persistentStorage ? 'success' : 'danger'">
            {{ pwaSupport.persistentStorage ? '支持' : '不支持' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="网络类型">
          {{ networkType }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
    
    <el-card style="margin-top: 20px">
      <template #header>
        <h3>缓存管理</h3>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <h4>缓存规则</h4>
          <el-table :data="cacheRules" style="width: 100%">
            <el-table-column prop="name" label="规则名称"  />
            <el-table-column prop="strategy" label="策略"  />
            <el-table-column label="操作" width="120">
              <template #default="scope">
                <el-button
                  link
                  type="danger"
                  size="small"
                  @click="removeRule(scope.row.name)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <el-button
            style="margin-top: 10px"
            @click="showAddRule = true"
          >
            添加规则
          </el-button>
        </el-col>
        
        <el-col :span="12">
          <h4>缓存统计</h4>
          <div v-if="cacheStats.length > 0">
            <div
              v-for="stat in cacheStats"
              :key="stat.cacheName"
              class="cache-stat"
            >
              <el-card>
                <div class="stat-header">
                  <span class="cache-name">{{ stat.cacheName }}</span>
                  <el-button
                    link
                    type="danger"
                    size="small"
                    @click="clearCache(stat.cacheName)"
                  >
                    清空
                  </el-button>
                </div>
                <el-row>
                  <el-col :span="8">
                    <div class="stat-item">
                      <div class="stat-value">{{ stat.count }}</div>
                      <div class="stat-label">条目数</div>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="stat-item">
                      <div class="stat-value">{{ formatSize(stat.size) }}</div>
                      <div class="stat-label">大小</div>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="stat-item">
                      <div class="stat-value">{{ (stat.hitRate * 100).toFixed(1) }}%</div>
                      <div class="stat-label">命中率</div>
                    </div>
                  </el-col>
                </el-row>
              </el-card>
            </div>
          </div>
          <el-empty v-else description="暂无缓存数据"  />
        </el-col>
      </el-row>
    </el-card>
    
    <el-card style="margin-top: 20px">
      <template #header>
        <h3>离线同步测试</h3>
      </template>
      
      <el-form :model="testForm" label-width="120px">
        <el-form-item label="任务类型">
          <el-select v-model="testForm.type" placeholder="选择任务类型">
            <el-option label="API请求" value="api"  />
            <el-option label="文件上传" value="upload"  />
            <el-option label="文件下载" value="download"  />
            <el-option label="自定义任务" value="custom"  />
          </el-select>
        </el-form-item>
        
        <el-form-item v-if="testForm.type === 'api'" label="请求URL">
          <el-input v-model="testForm.url" placeholder="输入API地址"   />
        </el-form-item>
        
        <el-form-item v-if="testForm.type === 'api'" label="请求方法">
          <el-radio-group v-model="testForm.method">
            <el-radio label="GET">GET</el-radio>
            <el-radio label="POST">POST</el-radio>
            <el-radio label="PUT">PUT</el-radio>
            <el-radio label="DELETE">DELETE</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item v-if="testForm.type === 'upload'" label="选择文件">
          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :limit="1"
            :on-change="handleFileChange"
          >
            <template v-slot:trigger>
<el-button >选择文件</el-button>
</template>
          </el-upload>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="addSyncTask">
            添加同步任务
          </el-button>
          <el-button @click="simulateOffline">
            模拟离线
          </el-button>
          <el-button @click="simulateOnline">
            模拟在线
          </el-button>
        </el-form-item>
      </el-form>
      
      <h4>同步任务队列</h4>
      <el-table :data="syncTasks" style="width: 100%">
        <el-table-column prop="id" label="任务ID" width="200"  />
        <el-table-column prop="type" label="类型" width="100"  />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="retryCount" label="重试次数" width="100"  />
        <el-table-column label="创建时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right">
          <template #default="scope">
            <el-button
              v-if="scope.row.status === 'pending'"
              link
              type="primary"
              size="small"
              @click="syncTask(scope.row.id)"
            >
              立即同步
            </el-button>
            <el-button
              link
              type="danger"
              size="small"
              @click="removeTask(scope.row.id)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <el-card style="margin-top: 20px">
      <template #header>
        <h3>离线数据管理</h3>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-button type="primary" @click="exportData('all')">
            导出所有数据
          </el-button>
          <el-button @click="exportData('employees')">
            导出员工数据
          </el-button>
          <el-button @click="exportData('organization')">
            导出组织数据
          </el-button>
        </el-col>
        <el-col :span="12">
          <el-upload
            :auto-upload="false"
            :limit="1"
            accept=".json"
            :on-change="handleImportChange"
          >
            <el-button>导入离线数据</el-button>
          </el-upload>
        </el-col>
      </el-row>
      
      <div class="storage-info">
        <h4>存储使用情况</h4>
        <el-progress
          :percentage="storageUsage.percentage"
          :color="getStorageColor"
         />
        <p>
          已使用 {{ formatSize(storageUsage.usage) }} / {{ formatSize(storageUsage.quota) }}
        </p>
      </div>
    </el-card>
    
    <!-- 添加缓存规则对话框 -->
    <el-dialog
      v-model="showAddRule"
      title="添加缓存规则"
      width="500px"
    >
      <el-form :model="newRule" label-width="100px">
        <el-form-item label="规则名称" required>
          <el-input v-model="newRule.name" placeholder="输入规则名称"   />
        </el-form-item>
        <el-form-item label="URL模式" required>
          <el-input v-model="newRule.urlPattern" placeholder="如: /api/.*"   />
        </el-form-item>
        <el-form-item label="缓存策略" required>
          <el-select v-model="newRule.strategy" placeholder="选择策略">
            <el-option label="缓存优先" value="cache-first"  />
            <el-option label="网络优先" value="network-first"  />
            <el-option label="仅缓存" value="cache-only"  />
            <el-option label="仅网络" value="network-only"  />
            <el-option label="缓存并更新" value="stale-while-revalidate"  />
          </el-select>
        </el-form-item>
        <el-form-item label="过期时间">
          <el-input-number
            v-model="newRule.maxAge"
            :min="0"
            placeholder="毫秒"
            />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddRule = false">取消</el-button>
        <el-button type="primary" @click="confirmAddRule">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getCacheManager,
  checkPWASupport,
  getStorageUsage,
  exportOfflineData,
  importOfflineData,
  registerSyncTask,
  CacheStrategy
} from '@/utils/offline'
import type { CacheRule, CacheStats, SyncTask } from '@/utils/offline/types'

// 状态
const isOnline = ref(navigator.onLine)
const networkType = ref('未知')
const pwaSupport = ref(checkPWASupport())
const cacheRules = ref<CacheRule[]>([])
const cacheStats = ref<CacheStats[]>([])
const syncTasks = ref<SyncTask[]>([])
const storageUsage = ref({ usage: 0, quota: 0, percentage: 0 })
const showAddRule = ref(false)
const uploadRef = ref()

// 表单数据
const testForm = ref({
  type: 'api',
  url: '/api/test',
  method: 'GET',
  file: null as File | null
})

const newRule = ref({
  name: '',
  urlPattern: '',
  strategy: CacheStrategy.NETWORK_FIRST,
  maxAge: 3600000
})

// 缓存管理器
let cacheManager: ReturnType<typeof getCacheManager> | null = null
let updateTimer: number | null = null

// 方法
const updateCacheStats = async () => {
  if (cacheManager) {
    cacheStats.value = await cacheManager.getCacheStats()
  }
}

const updateSyncTasks = () => {
  if (cacheManager) {
    syncTasks.value = cacheManager.getSyncTasks()
  }
}

const updateStorageUsage = async () => {
  const usage = await getStorageUsage()
  if (usage) {
    storageUsage.value = usage
  }
}

const updateNetworkStatus = () => {
  if (cacheManager) {
    const state = cacheManager.getOfflineState()
    isOnline.value = state.isOnline
    networkType.value = state.networkType || '未知'
  }
}

const removeRule = (name: string) => {
  if (cacheManager) {
    cacheManager.removeRule(name)
    cacheRules.value = cacheManager.getRules()
    ElMessage.success('规则已删除')
  }
}

const confirmAddRule = () => {
  if (!newRule.value.name || !newRule.value.urlPattern) {
    ElMessage.warning('请填写必填项')
    return
  }
  
  if (cacheManager) {
    const rule: CacheRule = {
      name: newRule.value.name,
      urlPattern: new RegExp(newRule.value.urlPattern),
      strategy: newRule.value.strategy as CacheStrategy,
      options: {
        maxAge: newRule.value.maxAge
      }
    }
    
    cacheManager.addRule(rule)
    cacheRules.value = cacheManager.getRules()
    showAddRule.value = false
    ElMessage.success('规则添加成功')
    
    // 重置表单
    newRule.value = {
      name: '',
      urlPattern: '',
      strategy: CacheStrategy.NETWORK_FIRST,
      maxAge: 3600000
    }
  }
}

const clearCache = async (cacheName: string) => {
  try {
    await ElMessageBox.confirm(
      `确定要清空缓存 ${cacheName} 吗？`,
      '清空缓存',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    if (cacheManager) {
      await cacheManager.clearCache(cacheName)
      await updateCacheStats()
      ElMessage.success('缓存已清空')
    }
  } catch {
    // 用户取消
  }
}

   
const handleFileChange = (file: unknown) => {
  testForm.value.file = file.raw
}

const addSyncTask = async () => {
   
  let taskData: unknown = {}
  
  switch (testForm.value.type) {
    case 'api':
      taskData = {
        url: testForm.value.url,
        method: testForm.value.method
      }
      break
      
    case 'upload':
      if (!testForm.value.file) {
        ElMessage.warning('请选择文件')
        return
      }
      taskData = {
        url: '/api/upload',
        file: testForm.value.file
      }
      break
      
    case 'download':
      taskData = {
        url: '/api/download/test.pdf',
        filename: 'HrTest.pdf'
      }
      break
      
    case 'custom':
      taskData = {
        handler: async (task: SyncTask) => {
          console.log('执行自定义任务:', task)
        }
      }
      break
  }
  
  const taskId = await registerSyncTask(testForm.value.type as unknown, taskData)
  ElMessage.success(`同步任务已添加: ${taskId}`)
  updateSyncTasks()
}

const syncTask = (taskId: string) => {
  // 任务会自动同步
  ElMessage.info('正在同步...')
}

const removeTask = (taskId: string) => {
  if (cacheManager) {
    cacheManager.removeSyncTask(taskId)
    updateSyncTasks()
    ElMessage.success('任务已删除')
  }
}

const simulateOffline = () => {
  // 模拟离线状态
  window.dispatchEvent(new Event('offline'))
  ElMessage.warning('已模拟离线状态')
}

const simulateOnline = () => {
  // 模拟在线状态
  window.dispatchEvent(new Event('online'))
  ElMessage.success('已模拟在线状态')
}

const exportData = async (type: 'employees' | 'organization' | 'all') => {
  try {
    const blob = await exportOfflineData(type)
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `hr-${type}-data-${Date.now()}.json`
    a.click()
    URL.revokeObjectURL(url)
    
    ElMessage.success('数据导出成功')
  } catch (__error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

   
const handleImportChange = async (file: unknown) => {
  try {
    await importOfflineData(file.raw)
    ElMessage.success('数据导入成功')
    await updateCacheStats()
  } catch (__error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败：' + (error as Error).message)
  }
}

const formatSize = (bytes: number): string => {
  const units = ['B', 'KB', 'MB', 'GB']
  let size = bytes
  let unitIndex = 0
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }
  
  return `${size.toFixed(2)} ${units[unitIndex]}`
}

const formatDate = (timestamp: number): string => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

const getStorageColor = (percentage: number) => {
  if (percentage < 60) return '#67c23a'
  if (percentage < 80) return '#e6a23c'
  return '#f56c6c'
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    'pending': 'warning',
    'syncing': 'primary',
    'success': 'success',
    'failed': 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    'pending': '待同步',
    'syncing': '同步中',
    'success': '已完成',
    'failed': '失败'
  }
  return texts[status] || status
}

// 生命周期
onMounted(() => {
  cacheManager = getCacheManager()
  
  // 初始化数据
  cacheRules.value = cacheManager.getRules()
  updateCacheStats()
  updateSyncTasks()
  updateStorageUsage()
  updateNetworkStatus()
  
  // 监听事件
  cacheManager.on('sync-success', updateSyncTasks)
  cacheManager.on('sync-failed', updateSyncTasks)
  cacheManager.on('connection-change', updateNetworkStatus)
  
  // 定期更新
  updateTimer = window.setInterval(() => {
    updateCacheStats()
    updateSyncTasks()
    updateStorageUsage()
    updateNetworkStatus()
  }, 5000)
})

onUnmounted(() => {
  if (updateTimer) {
    clearInterval(updateTimer)
  }
})
</script>

<style lang="scss" scoped>
.offline-demo {
  padding: 20px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h3 {
      margin: 0;
    }
  }
  
  h4 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 500;
  }
  
  .cache-stat {
    margin-bottom: 12px;
    
    .stat-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      
      .cache-name {
        font-weight: 500;
      }
    }
    
    .stat-item {
      text-align: center;
      
      .stat-value {
        font-size: 20px;
        font-weight: 600;
        color: var(--el-color-primary);
      }
      
      .stat-label {
        font-size: 12px;
        color: var(--el-text-color-secondary);
        margin-top: 4px;
      }
    }
  }
  
  .storage-info {
    margin-top: 20px;
    
    p {
      text-align: center;
      margin-top: 8px;
      color: var(--el-text-color-secondary);
    }
  }
}
</style>