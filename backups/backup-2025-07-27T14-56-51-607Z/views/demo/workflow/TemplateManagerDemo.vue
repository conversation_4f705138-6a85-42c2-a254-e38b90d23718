<template>
  <div class="template-manager-demo">
    <el-card class="demo-header">
      <template #header>
        <div class="card-header">
          <span>流程模板管理演示</span>
          <el-tag type="success">WF-DESIGNER-012</el-tag>
        </div>
      </template>
      
      <el-alert
        title="功能说明"
        type="info"
        :closable="false"
        show-icon
      >
        流程模板管理功能允许您保存常用的流程设计为模板，方便快速创建新流程。支持内置模板和自定义模板，提供分类、搜索、导入导出等功能。
      </el-alert>
      
      <el-row :gutter="20" class="feature-list">
        <el-col :span="6">
          <div class="feature-item">
            <el-icon :size="24" color="#409EFF"><FolderOpened /></el-icon>
            <div>模板分类管理</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="feature-item">
            <el-icon :size="24" color="#67C23A"><Search /></el-icon>
            <div>智能搜索过滤</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="feature-item">
            <el-icon :size="24" color="#E6A23C"><Upload /></el-icon>
            <div>导入导出支持</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="feature-item">
            <el-icon :size="24" color="#F56C6C"><Share /></el-icon>
            <div>模板分享机制</div>
          </div>
        </el-col>
      </el-row>
    </el-card>
    
    <!-- 设计器区域 -->
    <el-card class="designer-section">
      <template #header>
        <span>流程设计器（带模板功能）</span>
      </template>
      
      <BpmnDesigner
        ref="designerRef"
        v-model="processXml"
        :height="500"
        :toolbar-config="toolbarConfig"
      />
    </el-card>
    
    <!-- 模板管理独立展示 -->
    <el-card class="template-section">
      <template #header>
        <div class="section-header">
          <span>模板库管理</span>
          <el-space>
            <el-button type="primary" size="small" @click="handleImportTemplate">
              <el-icon><Upload /></el-icon>
              导入模板
            </el-button>
            <el-button size="small" @click="handleClearCustom">
              <el-icon><Delete /></el-icon>
              清空自定义
            </el-button>
          </el-space>
        </div>
      </template>
      
      <TemplateList
        ref="templateListRef"
        @select="handleTemplateSelect"
        @use="handleTemplateUse"
        @create="handleTemplateCreate"
      />
    </el-card>
    
    <!-- 使用说明 -->
    <el-card class="usage-guide">
      <template #header>
        <span>使用说明</span>
      </template>
      
      <el-timeline>
        <el-timeline-item>
          <h4>内置模板</h4>
          <p>系统提供了常用的流程模板，包括：</p>
          <ul>
            <li>基础审批流程 - 简单的申请-审批-结束流程</li>
            <li>请假申请流程 - 根据天数自动分级审批</li>
            <li>员工入职流程 - 多部门并行协作</li>
            <li>费用报销流程 - 根据金额分级审批</li>
          </ul>
        </el-timeline-item>
        
        <el-timeline-item>
          <h4>创建自定义模板</h4>
          <ol>
            <li>在设计器中设计好流程</li>
            <li>点击工具栏的"模板"按钮</li>
            <li>选择"创建模板"</li>
            <li>填写模板信息并保存</li>
          </ol>
        </el-timeline-item>
        
        <el-timeline-item>
          <h4>使用模板</h4>
          <ol>
            <li>点击工具栏的"模板"按钮</li>
            <li>浏览或搜索需要的模板</li>
            <li>点击模板卡片上的"使用"按钮</li>
            <li>模板内容将自动加载到设计器</li>
          </ol>
        </el-timeline-item>
        
        <el-timeline-item>
          <h4>管理模板</h4>
          <ul>
            <li><strong>复制</strong>：基于现有模板创建副本</li>
            <li><strong>编辑</strong>：修改自定义模板信息</li>
            <li><strong>导出</strong>：将模板导出为JSON文件</li>
            <li><strong>删除</strong>：删除不需要的自定义模板</li>
          </ul>
        </el-timeline-item>
      </el-timeline>
    </el-card>
    
    <!-- 导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="导入模板"
      width="500px"
    >
      <el-upload
        ref="uploadRef"
        class="upload-demo"
        drag
        :auto-upload="false"
        :limit="1"
        accept=".json"
        :on-change="handleFileChange"
      >
        <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
        <div class="el-upload__text">
          将JSON文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            只支持导入通过系统导出的模板JSON文件
          </div>
        </template>
      </el-upload>
      
      <template #footer>
        <el-button @click="importDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmImport">确定导入</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'TemplateManagerDemo'
})
 
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  FolderOpened,
  Search,
  Upload,
  Share,
  Delete,
  UploadFilled
} from '@element-plus/icons-vue'
import HrBpmnDesigner from '@/workflow/components/designer/HrBpmnDesigner.vue'
import TemplateList from '@/workflow/components/template/TemplateList.vue'
import type { ProcessTemplate } from '@/workflow/utils/templateManager'

// 状态
const designerRef = ref()
const templateListRef = ref()
const uploadRef = ref()
const processXml = ref('')
const importDialogVisible = ref(false)
const importedContent = ref('')

// 自定义工具栏配置（突出模板功能）
const toolbarConfig = [
  { action: 'save', title: '保存', icon: 'save', type: 'primary' },
  { action: 'template', title: '模板', icon: 'folder', type: 'success' },
  { action: 'import', title: '导入导出', icon: 'transfer', type: 'default' },
  { type: 'separator' },
  { action: 'undo', title: '撤销', icon: 'undo' },
  { action: 'redo', title: '重做', icon: 'redo' },
  { type: 'separator' },
  { action: 'zoom-fit', title: '适应窗口', icon: 'fit-window' }
]

// 处理模板选择
const handleTemplateSelect = (template: ProcessTemplate) => {
  console.log('选择模板:', template)
  ElMessage.info(`选择了模板：${template.name}`)
}

// 处理模板使用
const handleTemplateUse = (template: ProcessTemplate) => {
  processXml.value = template.xml
  ElMessage.success(`已应用模板：${template.name}`)
}

// 处理创建模板
const handleTemplateCreate = () => {
  ElMessage.info('请在设计器中设计流程，然后点击模板按钮保存')
}

// 导入模板
const handleImportTemplate = () => {
  importDialogVisible.value = true
}

// 处理文件变化
   
const handleFileChange = async (file: unknown) => {
  if (!file.raw) return
  
  try {
    const text = await file.raw.text()
    importedContent.value = text
  } catch (__error) {
    console.error('Failed to read file:', error)
    ElMessage.error('文件读取失败')
  }
}

// 确认导入
const confirmImport = () => {
  if (!importedContent.value) {
    ElMessage.warning('请先选择文件')
    return
  }
  
  try {
    const templateManager = templateListRef.value?.getTemplateManager()
    if (templateManager) {
      const template = templateManager.importTemplate(importedContent.value)
      if (template) {
        templateListRef.value?.refresh()
        importDialogVisible.value = false
        uploadRef.value?.clearFiles()
        importedContent.value = ''
      }
    }
  } catch (__error) {
    console.error('Failed to import template:', error)
    ElMessage.error('模板导入失败')
  }
}

// 清空自定义模板
const handleClearCustom = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有自定义模板吗？此操作不可恢复。',
      '清空确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const templateManager = templateListRef.value?.getTemplateManager()
    if (templateManager) {
      templateManager.clearCustomTemplates()
      templateListRef.value?.refresh()
      ElMessage.success('自定义模板已清空')
    }
  } catch {
    // 用户取消
  }
}
</script>

<style lang="scss" scoped>
.template-manager-demo {
  padding: 20px;
  
  .demo-header {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .feature-list {
      margin-top: 20px;
      
      .feature-item {
        text-align: center;
        padding: 20px;
        background: #f5f7fa;
        border-radius: 4px;
        
        .el-icon {
          margin-bottom: 10px;
        }
      }
    }
  }
  
  .designer-section {
    margin-bottom: 20px;
  }
  
  .template-section {
    margin-bottom: 20px;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .usage-guide {
    .el-timeline {
      padding: 0;
      
      h4 {
        margin: 0 0 10px 0;
        color: #303133;
      }
      
      ul, ol {
        margin: 0;
        padding-left: 20px;
        color: #606266;
        
        li {
          margin-bottom: 5px;
        }
      }
    }
  }
  
  .upload-demo {
    width: 100%;
    
    .el-upload-dragger {
      width: 100%;
      
      .el-icon--upload {
        font-size: 67px;
        color: #c0c4cc;
        margin-bottom: 16px;
        line-height: 50px;
      }
    }
  }
}
</style>