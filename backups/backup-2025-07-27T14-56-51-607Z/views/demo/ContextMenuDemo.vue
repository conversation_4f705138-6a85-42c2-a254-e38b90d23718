<template>
  <div class="context-menu-demo">
    <hr-page-header title="右键菜单演示" :show-back="true">
      展示各种场景下的右键菜单功能
    </hr-page-header>

    <!-- 基础右键菜单 -->
    <el-card class="demo-section">
      <template #header>
        <span>基础右键菜单</span>
      </template>
      
      <div 
        class="context-area basic-area" 
        @contextmenu.prevent="showBasicMenu"
      >
        <el-icon :size="48"><Mouse /></el-icon>
        <p>在此区域右键点击显示菜单</p>
      </div>
    </el-card>

    <!-- 表格右键菜单 -->
    <el-card class="demo-section">
      <template #header>
        <span>表格右键菜单</span>
      </template>
      
      <el-table 
        :data="tableData" 
        @row-contextmenu="showTableMenu"
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80"  />
        <el-table-column prop="name" label="姓名"  />
        <el-table-column prop="department" label="部门"  />
        <el-table-column prop="position" label="职位"  />
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag :type="row.status === '在职' ? 'success' : 'info'">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 列表右键菜单 -->
    <el-card class="demo-section">
      <template #header>
        <span>列表右键菜单</span>
      </template>
      
      <div class="list-container">
        <div 
          v-for="item in listItems" 
          :key="item.id"
          class="list-item"
          @contextmenu.prevent="showListMenu($event, item)"
        >
          <el-icon><component :is="item.icon" /></el-icon>
          <div class="item-content">
            <div class="item-title">{{ item.title }}</div>
            <div class="item-desc">{{ item.description }}</div>
          </div>
          <el-tag size="small">{{ item.type }}</el-tag>
        </div>
      </div>
    </el-card>

    <!-- 树形结构右键菜单 -->
    <el-card class="demo-section">
      <template #header>
        <span>树形结构右键菜单</span>
      </template>
      
      <el-tree
        ref="treeRef"
        :data="treeData"
        :props="{ label: 'label', children: 'children' }"
        default-expand-all
        @node-contextmenu="showTreeMenu"
       />
    </el-card>

    <!-- 文本编辑右键菜单 -->
    <el-card class="demo-section">
      <template #header>
        <span>文本编辑右键菜单</span>
      </template>
      
      <el-input
        v-model="textContent"
        type="textarea"
        :rows="6"
        placeholder="在文本框内右键点击显示编辑菜单"
        @contextmenu.native.prevent="showTextMenu"
        />
    </el-card>

    <!-- 自定义右键菜单 -->
    <el-card class="demo-section">
      <template #header>
        <span>自定义右键菜单</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <div 
            class="context-area custom-area" 
            @contextmenu.prevent="showCustomMenu"
          >
            <el-icon :size="48"><Setting /></el-icon>
            <p>自定义菜单项</p>
          </div>
        </el-col>
        <el-col :span="12">
          <div 
            class="context-area dynamic-area" 
            @contextmenu.prevent="showDynamicMenu"
          >
            <el-icon :size="48"><Operation /></el-icon>
            <p>动态菜单项</p>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 组件集成示例 -->
    <el-card class="demo-section">
      <template #header>
        <span>组件集成示例</span>
      </template>
      
      <ContextMenu
        ref="componentMenuRef"
        :items="componentMenuItems"
        :target="'.target-element'"
        @select="handleComponentMenuSelect"
      />
      
      <div class="target-element context-area">
        <el-icon :size="48"><ElementPlus /></el-icon>
        <p>使用组件方式的右键菜单</p>
      </div>
    </el-card>

    <!-- 操作日志 -->
    <el-card class="demo-section">
      <template #header>
        <div class="card-header">
          <span>操作日志</span>
          <el-button size="small" @click="clearLogs">清空</el-button>
        </div>
      </template>
      
      <el-scrollbar height="200px">
        <div class="log-container">
          <div v-for="(log, index) in logs" :key="index" class="log-item">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
          <el-empty v-if="logs.length === 0" description="暂无操作记录"  />
        </div>
      </el-scrollbar>
    </el-card>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'ContextMenuDemo'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Mouse, 
  Document, 
  FolderOpened, 
  Picture, 
  Setting,
  Operation,
  ElementPlus
} from '@element-plus/icons-vue'
import HrPageHeader from '@/components/common/HrPageHeader.vue'
import HrContextMenu from '@/components/common/HrContextMenu.vue'
import { useContextMenu, showContextMenu } from '@/composables/useContextMenu'
import type { ContextMenuItem } from '@/components/common/ContextMenu.vue'

// 获取预设菜单
const {getPresetMenu, createMenuItem, createDivider, createGroup} =  useContextMenu()

// 表格数据
const tableData  message: string }>>([])

// 组件菜单项
const componentMenuItems = ref<ContextMenuItem[]>([
  {
    label: '组件操作',
    icon: 'Operation',
    children: [
      { label: '刷新', icon: 'Refresh', handler: () => addLog('执行刷新') },
      { label: '重置', icon: 'RefreshLeft', handler: () => addLog('执行重置') }
    ]
  },
  { type: 'divider' },
  {
    label: '查看源码',
    icon: 'View',
    handler: () => addLog('查看源码')
  }
])

// 组件引用
const componentMenuRef = ref<InstanceType<typeof ContextMenu>>()
const treeRef = ref()

// 添加日志
const addLog = (message: string) => {
  const time = new Date().toLocaleTimeString()
  logs.value.unshift({ time, message })
  if (logs.value.length > 20) {
    logs.value.pop()
  }
}

// 清空日志
const clearLogs = () => {
  logs.value = []
  ElMessage.success('日志已清空')
}

// 显示基础菜单
const showBasicMenu = (event: MouseEvent) => {
  showContextMenu({
    event,
    items: [
      {
        label: '刷新',
        icon: 'Refresh',
        handler: () => {
          addLog('执行刷新操作')
          ElMessage.success('刷新成功')
        }
      },
      {
        label: '设置',
        icon: 'Setting',
        handler: () => {
          addLog('打开设置')
        }
      },
      { type: 'divider' },
      {
        label: '帮助',
        icon: 'QuestionFilled',
        children: [
          { label: '查看文档', handler: () => addLog('查看文档') },
          { label: '快捷键', handler: () => addLog('查看快捷键') },
          { label: '关于', handler: () => addLog('查看关于') }
        ]
      }
    ],
    onSelect: (item) => {
      console.log('选择了菜单项:', item)
    }
  })
}

// 显示表格菜单
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const showTableMenu = (row: unknown, column: unknown, event: MouseEvent) => {
  const tableMenuItems = getPresetMenu('table').map(item => {
    if (item.label === '编辑') {
      return {
        ...item,
        handler: () => {
          addLog(`编辑员工: ${row.name}`)
          ElMessage.info(`编辑 ${row.name}`)
        }
      }
    }
    if (item.label === '删除') {
      return {
        ...item,
        handler: () => {
          addLog(`删除员工: ${row.name}`)
          ElMessage.warning(`删除 ${row.name}`)
        }
      }
    }
    return item
  })

  showContextMenu({
    event,
    title: `员工: ${row.name}`,
    items: tableMenuItems
  })
}

// 显示列表菜单
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const showListMenu = (event: MouseEvent, item: unknown) => {
  showContextMenu({
    event,
    items: [
      {
        label: '打开',
        icon: 'FolderOpened',
        handler: () => {
          addLog(`打开: ${item.title}`)
          ElMessage.success(`打开 ${item.title}`)
        }
      },
      {
        label: '在新标签页打开',
        icon: 'Link',
        handler: () => addLog(`新标签页打开: ${item.title}`)
      },
      { type: 'divider' },
      ...getPresetMenu('list').slice(2)
    ]
  })
}

// 显示树形菜单
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const showTreeMenu = (event: MouseEvent, data: unknown, node: unknown) => {
  const treeMenuItems = getPresetMenu('tree').map(item => {
    if (item.label === '新建' && item.children) {
      return {
        ...item,
        children: item.children.map(child => ({
          ...child,
          handler: () => {
            addLog(`在 ${data.label} 下${child.label}`)
            ElMessage.success(`${child.label}成功`)
          }
        }))
      }
    }
    return {
      ...item,
      handler: item.handler ? () => {
        addLog(`对 ${data.label} 执行 ${item.label}`)
        item.handler?.()
      } : undefined
    }
  })

  showContextMenu({
    event,
    title: data.label,
    items: treeMenuItems
  })
}

// 显示文本菜单
const showTextMenu = (event: MouseEvent) => {
  showContextMenu({
    event,
    items: getPresetMenu('text').map(item => ({
      ...item,
      handler: item.handler ? () => {
        addLog(`文本操作: ${item.label}`)
        item.handler()
      } : undefined
    }))
  })
}

// 显示自定义菜单
const showCustomMenu = (event: MouseEvent) => {
  showContextMenu({
    event,
    items: [
      createGroup('颜色主题', [
        createMenuItem('亮色主题', () => addLog('切换到亮色主题'), { icon: 'Sunny' }),
        createMenuItem('暗色主题', () => addLog('切换到暗色主题'), { icon: 'Moon' }),
        createMenuItem('自动', () => addLog('主题跟随系统'), { icon: 'Monitor' })
      ]),
      createDivider(),
      createGroup('语言设置', [
        createMenuItem('简体中文', () => addLog('切换到简体中文')),
        createMenuItem('English', () => addLog('Switch to English')),
        createMenuItem('日本語', () => addLog('日本語に切り替え'))
      ])
    ],
    width: 200
  })
}

// 显示动态菜单
const showDynamicMenu = (event: MouseEvent) => {
  const dynamicItems: ContextMenuItem[] = []
  
  // 根据时间动态生成菜单
  const hour = new Date().getHours()
  
  if (hour < 12) {
    dynamicItems.push(
      createMenuItem('早安问候', () => addLog('早安！'), { icon: 'Sunrise' })
    )
  } else if (hour < 18) {
    dynamicItems.push(
      createMenuItem('午后休息', () => addLog('休息一下'), { icon: 'Coffee' })
    )
  } else {
    dynamicItems.push(
      createMenuItem('晚安祝福', () => addLog('晚安！'), { icon: 'Moon' })
    )
  }
  
  dynamicItems.push(createDivider())
  
  // 随机生成一些操作
  const actions = ['分析', '优化', '检查', '修复', '更新']
  const randomActions = actions
    .sort(() => Math.random() - 0.5)
    .slice(0, 3)
    .map(action => 
      createMenuItem(`${action}系统`, () => addLog(`执行${action}操作`))
    )
  
  dynamicItems.push(...randomActions)
  
  showContextMenu({
    event,
    items: dynamicItems,
    title: '动态菜单'
  })
}

// 处理组件菜单选择
const handleComponentMenuSelect = (item: ContextMenuItem) => {
  console.log('组件菜单选择:', item)
}
</script>

<style lang="scss" scoped>
.context-menu-demo {
  padding: 20px;
  
  .demo-section {
    margin-bottom: 20px;
  }
  
  .context-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    border: 2px dashed var(--el-border-color);
    border-radius: 8px;
    background: var(--el-fill-color-lighter);
    cursor: context-menu;
    transition: all 0.3s;
    user-select: none;
    
    &:hover {
      background: var(--el-fill-color);
      border-color: var(--el-border-color-darker);
    }
    
    p {
      margin: 16px 0 0;
      color: var(--el-text-color-secondary);
    }
  }
  
  .list-container {
    .list-item {
      display: flex;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid var(--el-border-color-lighter);
      cursor: context-menu;
      transition: background 0.3s;
      
      &:last-child {
        border-bottom: none;
      }
      
      &:hover {
        background: var(--el-fill-color-light);
      }
      
      .el-icon {
        font-size: 24px;
        margin-right: 12px;
        color: var(--el-color-primary);
      }
      
      .item-content {
        flex: 1;
        
        .item-title {
          font-weight: 500;
          margin-bottom: 4px;
        }
        
        .item-desc {
          font-size: 12px;
          color: var(--el-text-color-secondary);
        }
      }
    }
  }
  
  .log-container {
    .log-item {
      display: flex;
      padding: 8px 12px;
      border-bottom: 1px solid var(--el-border-color-lighter);
      font-size: 13px;
      
      &:last-child {
        border-bottom: none;
      }
      
      .log-time {
        color: var(--el-text-color-secondary);
        margin-right: 12px;
        flex-shrink: 0;
      }
      
      .log-message {
        color: var(--el-text-color-primary);
      }
    }
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .target-element {
    margin-top: 20px;
  }
}
</style>