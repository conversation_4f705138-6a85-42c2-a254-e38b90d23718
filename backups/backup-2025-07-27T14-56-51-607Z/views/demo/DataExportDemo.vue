<template>
  <div class="data-export-demo">
    <h1>数据导出组件演示</h1>
    
    <el-card class="demo-card">
      <template #header>
        <h3>多格式数据导出</h3>
      </template>
      
      <div class="demo-content">
        <div class="demo-actions">
          <HrDataExport
            button-text="导出员工数据"
            dialog-title="员工数据导出"
            :fields="exportFields"
            :fetch-data="fetchExportData"
            :selection="selectedRows"
            :total-count="totalCount"
            :templates="exportTemplates"
            @exported="handleExported"
            @save-template="handleSaveTemplate"
            @delete-template="handleDeleteTemplate"
          />
          
          <el-button @click="selectRandomRows">
            随机选择数据
          </el-button>
          
          <el-button @click="clearSelection">
            清除选择
          </el-button>
        </div>
        
        <el-alert type="info" :closable="false">
          <template #title>
            <div class="info-content">
              <p>支持的导出格式：</p>
              <ul>
                <li>📊 Excel (.xlsx) - 支持样式、列宽、冻结表头</li>
                <li>📄 CSV (.csv) - 支持UTF-8和GBK编码</li>
                <li>📑 PDF (.pdf) - 支持中文字体、分页、页眉页脚</li>
                <li>🔤 JSON (.json) - 支持格式化输出</li>
                <li>📋 XML (.xml) - 支持自定义节点名称</li>
              </ul>
              <p>功能特性：</p>
              <ul>
                <li>✅ 字段选择 - 灵活选择导出字段</li>
                <li>✅ 范围控制 - 当前页/全部/选中数据</li>
                <li>✅ 模板保存 - 保存常用导出配置</li>
                <li>✅ 大数据支持 - 超过10000条自动后台导出</li>
                <li>✅ 进度提示 - 导出进度实时反馈</li>
              </ul>
            </div>
          </template>
        </el-alert>
        
        <div class="data-info">
          <el-statistic title="总数据量" :value="totalCount"  />
          <el-statistic title="选中数据" :value="selectedRows.length"  />
        </div>
      </div>
    </el-card>
    
    <el-card class="demo-card">
      <template #header>
        <h3>示例数据预览</h3>
      </template>
      
      <el-table
        :data="tableData"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="employeeNo" label="工号" width="100"  />
        <el-table-column prop="name" label="姓名" width="100"  />
        <el-table-column prop="department" label="部门"  />
        <el-table-column prop="position" label="职位"  />
        <el-table-column prop="joinDate" label="入职日期" width="120"  />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.status === '在职' ? 'success' : 'danger'">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="salary" label="薪资" width="100"  />
      </el-table>
    </el-card>
    
    <el-card class="demo-card">
      <template #header>
        <h3>导出记录</h3>
      </template>
      
      <el-table :data="exportHistory" style="width: 100%">
        <el-table-column prop="timestamp" label="导出时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.timestamp) }}
          </template>
        </el-table-column>
        <el-table-column prop="format" label="格式" width="100">
          <template #default="scope">
            <el-tag>{{ scope.row.format.toUpperCase() }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="filename" label="文件名"  />
        <el-table-column prop="count" label="数据量" width="100"  />
        <el-table-column prop="fields" label="字段数" width="100"  />
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.background ? 'warning' : 'success'">
              {{ scope.row.background ? '后台处理' : '已完成' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
      
      <el-empty v-if="exportHistory.length === 0" description="暂无导出记录"  />
    </el-card>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive } from 'vue'
import HrDataExport from '@/components/common/HrDataExport.vue'
import { ElMessage } from 'element-plus'

// 导出字段配置
const exportFields = [
  {
    group: 'basic',
    groupLabel: '基本信息',
    value: 'employeeNo',
    label: '工号',
    required: true
  },
  {
    group: 'basic',
    groupLabel: '基本信息',
    value: 'name',
    label: '姓名',
    required: true
  },
  {
    group: 'basic',
    groupLabel: '基本信息',
    value: 'gender',
    label: '性别'
  },
  {
    group: 'basic',
    groupLabel: '基本信息',
    value: 'age',
    label: '年龄'
  },
  {
    group: 'work',
    groupLabel: '工作信息',
    value: 'department',
    label: '部门'
  },
  {
    group: 'work',
    groupLabel: '工作信息',
    value: 'position',
    label: '职位'
  },
  {
    group: 'work',
    groupLabel: '工作信息',
    value: 'level',
    label: '职级'
  },
  {
    group: 'work',
    groupLabel: '工作信息',
    value: 'joinDate',
    label: '入职日期'
  },
  {
    group: 'work',
    groupLabel: '工作信息',
    value: 'status',
    label: '在职状态'
  },
  {
    group: 'salary',
    groupLabel: '薪资信息',
    value: 'salary',
    label: '基本工资'
  },
  {
    group: 'salary',
    groupLabel: '薪资信息',
    value: 'bonus',
    label: '奖金'
  },
  {
    group: 'salary',
    groupLabel: '薪资信息',
    value: 'totalSalary',
    label: '总薪资'
  },
  {
    group: 'contact',
    groupLabel: '联系方式',
    value: 'phone',
    label: '手机号'
  },
  {
    group: 'contact',
    groupLabel: '联系方式',
    value: 'email',
    label: '邮箱'
  },
  {
    group: 'contact',
    groupLabel: '联系方式',
    value: 'address',
    label: '地址'
  }
]

// 示例数据
const generateMockData = (count: number) => {
  const departments = ['技术部', '人事部', '财务部', '市场部', '行政部']
  const positions = ['工程师', '经理', '主管', '专员', '助理']
  const statuses = ['在职', '离职']
  
  return Array.from({ length: count }, (_, index) => ({
    id: index + 1,
    employeeNo: `EMP${String(index + 1).padStart(4, '0')}`,
    name: `员工${index + 1}`,
    gender: index % 2 === 0 ? '男' : '女',
    age: 25 + Math.floor(Math.random() * 20),
    department: departments[Math.floor(Math.random() * departments.length)],
    position: positions[Math.floor(Math.random() * positions.length)],
    level: `L${Math.floor(Math.random() * 5) + 1}`,
    joinDate: new Date(2020 + Math.floor(Math.random() * 4), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toISOString().split('T')[0],
    status: statuses[Math.floor(Math.random() * statuses.length)],
    salary: Math.floor(Math.random() * 20000) + 5000,
    bonus: Math.floor(Math.random() * 5000),
    totalSalary: 0,
    phone: `138${Math.floor(Math.random() * *********).toString().padStart(8, '0')}`,
    email: `employee${index + 1}@company.com`,
    address: `城市${Math.floor(Math.random() * 10) + 1}区街道${Math.floor(Math.random() * 100) + 1}号`
  })).map(item => ({
    ...item,
    totalSalary: item.salary + item.bonus
  }))
}

// 数据
const totalCount = 1000
const tableData = ref(generateMockData(20))
const selectedRows = ref<any[]>([])
const exportHistory = ref<any[]>([])
const exportTemplates = ref([
  {
    id: '1',
    name: 'HrHr基本信息导出',
    fields: ['employeeNo', 'name', 'department', 'position'],
    fieldCount: 4,
    createTime: new Date().toISOString()
  },
  {
    id: '2',
    name: '完整信息导出',
    fields: exportFields.map(f => f.value),
    fieldCount: exportFields.length,
    createTime: new Date().toISOString()
  }
])

// 获取导出数据
   
const fetchExportData = async (params: unknown) => {
  // 模拟API调用延迟
  await new Promise(resolve => setTimeout(resolve, 500))
  
   
  let data: unknown[]
  
  if (params.range === 'selected' && params.selection) {
    data = params.selection
  } else if (params.range === 'current') {
    data = tableData.value
  } else {
    // 模拟获取全部数据
    data = generateMockData(totalCount)
  }
  
  // 只返回选中的字段
  return data.map(row => {
   
    const newRow: unknown = {}
    params.fields.forEach((field: string) => {
      newRow[field] = row[field]
    })
    return newRow
  })
}

// 处理选择变化
   
const handleSelectionChange = (selection: unknown[]) => {
  selectedRows.value = selection
}

// 随机选择数据
const selectRandomRows = () => {
  const count = Math.floor(Math.random() * 10) + 1
  const indices = new Set<number>()
  
  while (indices.size < count && indices.size < tableData.value.length) {
    indices.add(Math.floor(Math.random() * tableData.value.length))
  }
  
  selectedRows.value = Array.from(indices).map(i => tableData.value[i])
  ElMessage.success(`已随机选择 ${selectedRows.value.length} 条数据`)
}

// 清除选择
const clearSelection = () => {
  selectedRows.value = []
  ElMessage.info('已清除选择')
}

// 处理导出完成
   
const handleExported = (result: unknown) => {
  exportHistory.value.unshift(result)
  
  // 限制历史记录数量
  if (exportHistory.value.length > 10) {
    exportHistory.value = exportHistory.value.slice(0, 10)
  }
}

// 保存模板
   
const handleSaveTemplate = (template: unknown) => {
  template.id = Date.now().toString()
  exportTemplates.value.push(template)
  ElMessage.success('模板保存成功')
}

// 删除模板
const handleDeleteTemplate = (id: string) => {
  const index = exportTemplates.value.findIndex(t => t.id === id)
  if (index > -1) {
    exportTemplates.value.splice(index, 1)
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}
</script>

<style lang="scss" scoped>
.data-export-demo {
  padding: 20px;
  
  h1 {
    margin-bottom: 20px;
    font-size: 24px;
  }
  
  .demo-card {
    margin-bottom: 20px;
    
    h3 {
      margin: 0;
      font-size: 18px;
    }
  }
  
  .demo-content {
    .demo-actions {
      display: flex;
      gap: 12px;
      margin-bottom: 20px;
    }
    
    .info-content {
      p {
        margin: 0 0 10px 0;
        font-weight: 500;
        
        &:last-child {
          margin-top: 15px;
        }
      }
      
      ul {
        margin: 0;
        padding-left: 20px;
        
        li {
          line-height: 1.8;
        }
      }
    }
    
    .data-info {
      display: flex;
      gap: 40px;
      margin-top: 20px;
      
      .el-statistic {
        text-align: center;
      }
    }
  }
}
</style>