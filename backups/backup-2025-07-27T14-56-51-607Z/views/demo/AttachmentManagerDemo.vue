<template>
  <div class="attachment-manager-demo">
    <hr-page-header title="附件管理器演示" :show-back="true">
      增强的附件管理器，支持多种视图模式、批量操作、文件预览等功能
    </hr-page-header>

    <el-card>
      <template #header>
        <div class="card-header">
          <span>基础附件管理器</span>
          <el-button type="primary" size="small" @click="resetFiles">重置文件</el-button>
        </div>
      </template>
      
      <EnhancedAttachmentManager
        v-model="fileList1"
        :upload-url="uploadUrl"
        :headers="uploadHeaders"
        :multiple="true"
        :limit="20"
        :max-size="50 * 1024 * 1024"
        show-upload
        show-preview
        show-search
        show-selection
        show-view-switch
        show-upload-info
        @upload-success="handleUploadSuccess"
        @delete="handleDelete"
        @preview="handlePreview"
        @download="handleDownload"
      />
    </el-card>

    <el-card style="margin-top: 20px">
      <template #header>
        <span>只读模式</span>
      </template>
      
      <EnhancedAttachmentManager
        v-model="fileList2"
        :readonly="true"
        :show-upload="false"
        :show-selection="false"
        view-mode="grid"
      />
    </el-card>

    <el-card style="margin-top: 20px">
      <template #header>
        <span>画廊模式</span>
      </template>
      
      <EnhancedAttachmentManager
        v-model="imageList"
        view-mode="gallery"
        :accept="'.jpg,.jpeg,.png,.gif,.bmp,.webp'"
        :show-view-switch="false"
        upload-text="上传图片"
        empty-text="暂无图片"
      />
    </el-card>

    <el-card style="margin-top: 20px">
      <template #header>
        <span>简单模式</span>
      </template>
      
      <AttachmentManager
        v-model="fileList3"
        :simple="true"
        :limit="5"
        :max-size="10 * 1024 * 1024"
      />
    </el-card>

    <el-card style="margin-top: 20px">
      <template #header>
        <span>使用组合式函数</span>
      </template>
      
      <div class="composable-demo">
        <el-button-group>
          <el-button @click="attachmentStore.loadAttachments()">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          <el-button @click="handleUploadWithComposable">
            <el-icon><Upload /></el-icon>
            上传文件
          </el-button>
          <el-button 
            :disabled="attachmentStore.selectedCount.value === 0"
            @click="attachmentStore.batchDownload()"
          >
            <el-icon><Download /></el-icon>
            批量下载 ({{ attachmentStore.selectedCount.value }})
          </el-button>
          <el-button 
            :disabled="attachmentStore.selectedCount.value === 0"
            type="danger"
            @click="handleBatchDelete"
          >
            <el-icon><Delete /></el-icon>
            批量删除 ({{ attachmentStore.selectedCount.value }})
          </el-button>
        </el-button-group>

        <el-table
          :data="attachmentStore.attachments.value"
          v-loading="attachmentStore.loading.value"
          style="margin-top: 20px"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="50"  />
          <el-table-column prop="name" label="文件名" min-width="200"  />
          <el-table-column label="大小" width="100">
            <template #default="{ row }">
              {{ formatFileSize(row.size) }}
            </template>
          </el-table-column>
          <el-table-column prop="uploadUser" label="上传人" width="100"  />
          <el-table-column label="上传时间" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.uploadTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-space>
                <el-button link type="primary" size="small" @click="attachmentStore.preview(row)">
                  预览
                </el-button>
                <el-button link type="primary" size="small" @click="attachmentStore.download(row)">
                  下载
                </el-button>
                <el-button link type="danger" size="small" @click="attachmentStore.remove(row)">
                  删除
                </el-button>
              </el-space>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          v-model:current-page="attachmentStore.pagination.page"
          v-model:page-size="attachmentStore.pagination.size"
          :total="attachmentStore.pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="attachmentStore.handlePageChange"
          @size-change="attachmentStore.handleSizeChange"
          style="margin-top: 20px"
         />
      </div>
    </el-card>

    <!-- 文件预览对话框 -->
    <FilePreviewDialog
      v-model="previewVisible"
      :file="previewFile"
    />
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Upload, Download, Delete } from '@element-plus/icons-vue'
import HrPageHeader from '@/components/common/HrPageHeader.vue'
import HrAttachmentManager from '@/components/common/HrAttachmentManager.vue'
import HrEnhancedAttachmentManager from '@/components/common/HrEnhancedAttachmentManager.vue'
import HrFilePreviewDialog from '@/components/common/HrFilePreviewDialog.vue'
import { useAttachment } from '@/composables/useAttachment'

// 模拟数据
const mockFiles = [
  {
    id: '1',
    name: 'HrHr项目需求文档.docx',
    size: 1024 * 245,
    type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    url: '/api/files/1.docx',
    uploadTime: new Date('2024-01-15 10:30:00'),
    uploadUser: '张三'
  },
  {
    id: '2',
    name: '系统架构图.png',
    size: 1024 * 512,
    type: 'image/png',
    url: 'https://element-plus.org/images/element-plus-logo.png',
    uploadTime: new Date('2024-01-15 11:00:00'),
    uploadUser: '李四'
  },
  {
    id: '3',
    name: '测试报告.pdf',
    size: 1024 * 1024 * 2,
    type: 'application/pdf',
    url: '/api/files/3.pdf',
    uploadTime: new Date('2024-01-15 14:20:00'),
    uploadUser: '王五'
  },
  {
    id: '4',
    name: '会议录音.mp3',
    size: 1024 * 1024 * 5,
    type: 'audio/mpeg',
    url: '/api/files/4.mp3',
    uploadTime: new Date('2024-01-15 15:30:00'),
    uploadUser: '赵六'
  },
  {
    id: '5',
    name: '产品演示视频.mp4',
    size: 1024 * 1024 * 20,
    type: 'video/mp4',
    url: '/api/files/5.mp4',
    uploadTime: new Date('2024-01-15 16:00:00'),
    uploadUser: '张三'
  }
]

const mockImages = [
  {
    id: '10',
    name: 'UI设计稿1.png',
    size: 1024 * 800,
    type: 'image/png',
    url: 'https://picsum.photos/id/10/400/300',
    uploadTime: new Date('2024-01-10 09:00:00'),
    uploadUser: '设计师A'
  },
  {
    id: '11',
    name: 'UI设计稿2.jpg',
    size: 1024 * 650,
    type: 'image/jpeg',
    url: 'https://picsum.photos/id/20/400/300',
    uploadTime: new Date('2024-01-11 10:00:00'),
    uploadUser: '设计师B'
  },
  {
    id: '12',
    name: '原型图.png',
    size: 1024 * 1200,
    type: 'image/png',
    url: 'https://picsum.photos/id/30/400/300',
    uploadTime: new Date('2024-01-12 11:00:00'),
    uploadUser: '产品经理'
  }
]

// 状态
const fileList1 = ref([...mockFiles])
const fileList2 = ref([...mockFiles])
const fileList3 = ref(mockFiles.slice(0, 3))
const imageList = ref([...mockImages])
const previewVisible = ref(false)
const previewFile = ref<unknown>(null)

// 上传配置
const uploadUrl = '/api/attachment/upload'
const uploadHeaders = {
  Authorization: `Bearer ${localStorage.getItem('token') || ''}`
}

// 使用组合式函数
const attachmentStore = useAttachment({
  businessType: 'demo',
  businessId: '123',
  autoLoad: false,
  onUploadSuccess: (file) => {
    ElMessage.success(`文件 ${file.name} 上传成功`)
  },
  onDeleteSuccess: (_id) => {
    ElMessage.success('删除成功')
  }
})

// 格式化文件大小
function formatFileSize(size: number): string {
  if (!size) return '0B'
  
  const units = ['B', 'KB', 'MB', 'GB']
  let index = 0
  let fileSize = size
  
  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024
    index++
  }
  
  return `${fileSize.toFixed(2)}${units[index]}`
}

// 格式化日期时间
function formatDateTime(time: string | Date): string {
  if (!time) return '-'
  return new Date(time).toLocaleString()
}

// 重置文件
function resetFiles() {
  fileList1.value = [...mockFiles]
  ElMessage.success('文件列表已重置')
}

// 上传成功
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
function handleUploadSuccess(_response: unknown, file: unknown) {
  ElMessage.success(`文件 ${file.name} 上传成功`)
}

// 删除文件
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
function handleDelete(file: unknown) {
  ElMessage.success(`文件 ${file.name} 已删除`)
}

// 预览文件
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
function handlePreview(file: unknown) {
  previewFile.value = file
  previewVisible.value = true
}

// 下载文件
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
function handleDownload(file: unknown) {
  ElMessage.info(`开始下载文件：${file.name}`)
}

// 使用组合式函数上传
async function handleUploadWithComposable() {
  const input = document.createElement('input')
  input.type = 'file'
  input.multiple = true
  input.accept = '*'
  
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  input.onchange = async (e: unknown) => {
    const files = Array.from(e.target.files) as File[]
    if (files.length === 0) return
    
    const {results: _results, errors: _errors} =  await attachmentStore.batchUpload(files)
    
    if (errors.length > 0) {
      ElMessage.warning(`${results.length} 个文件上传成功，${errors.length} 个文件上传失败`)
    } else {
      ElMessage.success(`成功上传 ${results.length} 个文件`)
    }
    
    // 刷新列表
    attachmentStore.loadAttachments()
  }
  
  input.click()
}

// 批量删除
async function handleBatchDelete() {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${attachmentStore.selectedCount.value} 个文件吗？`,
      '批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await attachmentStore.batchRemove()
  } catch {
    // 取消
  }
}

// 选择变化
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
function handleSelectionChange(selection: unknown[]) {
  attachmentStore.selectedIds.value 
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .composable-demo {
    padding: 20px 0;
  }
  
  .el-card {
    :deep(.el-card__body) {
      padding: 20px;
    }
  }
}
</style>