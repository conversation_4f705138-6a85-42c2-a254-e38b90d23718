<template>
  <div class="batch-operation-demo">
    <hr-page-header title="批量操作优化演示" :show-back="true">
      演示批量选择交互、批量操作、快捷键等功能
    </hr-page-header>

    <!-- 基础批量操作 -->
    <el-card class="demo-section">
      <template #header>
        <span>基础批量操作</span>
      </template>
      
      <BatchOperationOptimization
        v-model:selected-items="basicSelectedItems"
        :total="basicData.length"
        :actions="basicActions"
        @select-all="handleBasicSelectAll"
        @clear-selection="handleBasicClearSelection"
        @batch-action="handleBasicBatchAction"
      >
        <el-table
          ref="basicTableRef"
          :data="basicData"
          @selection-change="handleBasicSelectionChange"
        >
          <el-table-column type="selection" width="55"  />
          <el-table-column prop="id" label="ID" width="80"  />
          <el-table-column prop="name" label="姓名"  />
          <el-table-column prop="department" label="部门"  />
          <el-table-column prop="status" label="状态">
            <template #default="{ row }">
              <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
                {{ row.status === 'active' ? '在职' : '离职' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="{ row }">
              <el-button type="text" @click="handleEdit(row)">编辑</el-button>
              <el-button type="text" @click="handleView(row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </BatchOperationOptimization>
    </el-card>

    <!-- 高级批量操作 -->
    <el-card class="demo-section">
      <template #header>
        <span>高级批量操作</span>
      </template>
      
      <BatchOperationOptimization
        v-model:selected-items="advancedSelectedItems"
        :total="advancedTotal"
        :actions="advancedActions"
        :more-actions="moreActions"
        :fixed="true"
        :show-select-across-pages="true"
        :show-shortcuts="true"
        :show-statistics="true"
        :statistics="advancedStatistics"
        @select-all="handleAdvancedSelectAll"
        @select-across-pages="handleSelectAcrossPages"
        @clear-selection="handleAdvancedClearSelection"
        @batch-action="handleAdvancedBatchAction"
      >
        <el-table
          ref="advancedTableRef"
          :data="advancedData"
          @selection-change="handleAdvancedSelectionChange"
          @row-click="handleRowClick"
        >
          <el-table-column type="selection" width="55"  />
          <el-table-column prop="id" label="工号" width="100"  />
          <el-table-column prop="name" label="姓名"  />
          <el-table-column prop="department" label="部门"  />
          <el-table-column prop="position" label="职位"  />
          <el-table-column prop="salary" label="薪资">
            <template #default="{ row }">
              ¥{{ row.salary.toLocaleString() }}
            </template>
          </el-table-column>
          <el-table-column prop="joinDate" label="入职日期"  />
          <el-table-column prop="status" label="状态" width="80">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
        
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="advancedTotal"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </BatchOperationOptimization>
    </el-card>

    <!-- 卡片式批量操作 -->
    <el-card class="demo-section">
      <template #header>
        <span>卡片式批量操作</span>
      </template>
      
      <BatchOperationOptimization
        v-model:selected-items="cardSelectedItems"
        :total="cardData.length"
        :actions="cardActions"
        :show-history="true"
        :operation-history="operationHistory"
        @batch-action="handleCardBatchAction"
      >
        <el-row :gutter="16">
          <el-col
            v-for="item in cardData"
            :key="item.id"
            :xs="24"
            :sm="12"
            :md="8"
            :lg="6"
          >
            <el-card
              class="card-item"
              :class="{ 'is-selected': isCardSelected(item) }"
              @click="toggleCardSelection(item)"
            >
              <div class="card-checkbox">
                <el-checkbox
                  :model-value="isCardSelected(item)"
                  @click.stop
                  @change="toggleCardSelection(item)"
                 />
              </div>
              
              <div class="card-content">
                <el-avatar :size="60" :src="item.avatar">
                  {{ item.name.charAt(0) }}
                </el-avatar>
                <h4>{{ item.name }}</h4>
                <p>{{ item.position }}</p>
                <el-tag size="small">{{ item.department }}</el-tag>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </BatchOperationOptimization>
    </el-card>

    <!-- 功能特性展示 -->
    <el-card class="demo-section">
      <template #header>
        <span>功能特性</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card shadow="never">
            <h5>选择功能</h5>
            <ul>
              <li>单选/多选切换</li>
              <li>全选/反选操作</li>
              <li>跨页全选支持</li>
              <li>连续选择（Shift）</li>
              <li>增量选择（Ctrl）</li>
            </ul>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="never">
            <h5>批量操作</h5>
            <ul>
              <li>操作确认提示</li>
              <li>操作数量限制</li>
              <li>操作权限控制</li>
              <li>操作历史记录</li>
              <li>操作撤销支持</li>
            </ul>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="never">
            <h5>交互优化</h5>
            <ul>
              <li>固定操作栏</li>
              <li>快捷键支持</li>
              <li>选择统计信息</li>
              <li>批量操作反馈</li>
              <li>响应式布局</li>
            </ul>
          </el-card>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import {
  Delete,
  Download,
  Upload,
  Edit,
  View,
  Setting,
  Check,
  Close,
  Promotion,
  PriceTag,
  Lock,
  Unlock
} from '@element-plus/icons-vue'
import HrPageHeader from '@/components/common/HrPageHeader.vue'
import HrBatchOperationOptimization from '@/components/ux/HrBatchOperationOptimization.vue'
import type { BatchAction, OperationRecord } from '@/components/ux/BatchOperationOptimization.vue'

// 基础数据
const basicData = ref([
  { id: 1, name: 'HrHr张三', department: '技术部', status: 'active' },
  { id: 2, name: '李四', department: '产品部', status: 'active' },
  { id: 3, name: '王五', department: '设计部', status: 'inactive' },
  { id: 4, name: '赵六', department: '运营部', status: 'active' },
  { id: 5, name: '钱七', department: '市场部', status: 'active' }
])

const basicSelectedItems = ref<any[]>([])
const basicTableRef = ref()

// 基础操作
const basicActions: BatchAction[] = [
  {
    key: 'delete',
    label: '删除',
    type: 'danger',
    icon: Delete,
    confirm: true,
    handler: async (items) => {
      // 模拟删除操作
      await new Promise(resolve => setTimeout(resolve, 1000))
      basicData.value = basicData.value.filter(
        item => !items.some(selected => selected.id === item.id)
      )
      ElMessage.success(`成功删除 ${items.length} 条记录`)
      basicSelectedItems.value = []
    }
  },
  {
    key: 'export',
    label: '导出',
    icon: Download,
    handler: async (items) => {
      ElMessage.info(`正在导出 ${items.length} 条记录...`)
    }
  }
]

// 高级数据
const advancedData = ref<any[]>([])
const advancedSelectedItems = ref<any[]>([])
const advancedTableRef = ref()
const currentPage = ref(1)
const pageSize = ref(10)
const advancedTotal = ref(100)
const isSelectingAllPages = ref(false)

// 生成高级数据
const generateAdvancedData = () => {
  const departments = ['技术部', '产品部', '设计部', '运营部', '市场部', '人事部']
  const positions = ['工程师', '经理', '主管', '专员', '总监']
  const statuses = ['active', 'inactive', 'pending', 'suspended']
  
  advancedData.value = Array.from({ length: pageSize.value }, (_, i) => {
    const index = (currentPage.value - 1) * pageSize.value + i
    return {
      id: `EMP${String(index + 1).padStart(4, '0')}`,
      name: `员工${index + 1}`,
      department: departments[Math.floor(Math.random() * departments.length)],
      position: positions[Math.floor(Math.random() * positions.length)],
      salary: Math.floor(Math.random() * 50000) + 10000,
      joinDate: new Date(2020 + Math.floor(Math.random() * 4), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toLocaleDateString(),
      status: statuses[Math.floor(Math.random() * statuses.length)]
    }
  })
}

generateAdvancedData()

// 高级操作
const advancedActions: BatchAction[] = [
  {
    key: 'approve',
    label: '批准',
    type: 'success',
    icon: Check,
    minSelection: 1,
    showCount: true,
    confirm: '确定要批准选中的员工申请吗？',
    handler: async (items) => {
      await new Promise(resolve => setTimeout(resolve, 1500))
      ElNotification({
        title: '批准成功',
        message: `已批准 ${items.length} 个员工的申请`,
        type: 'success'
      })
    }
  },
  {
    key: 'reject',
    label: '拒绝',
    type: 'danger',
    icon: Close,
    minSelection: 1,
    confirm: '确定要拒绝选中的员工申请吗？',
    confirmType: 'warning',
    handler: async (items) => {
      await new Promise(resolve => setTimeout(resolve, 1000))
      ElMessage.warning(`已拒绝 ${items.length} 个申请`)
    }
  },
  {
    key: 'transfer',
    label: '调岗',
    type: 'primary',
    icon: Promotion,
    minSelection: 1,
    maxSelection: 10,
    disabled: (items) => items.some(item => item.status !== 'active'),
    handler: async (items) => {
      ElMessage.info('打开批量调岗对话框...')
    }
  }
]

// 更多操作
const moreActions: BatchAction[] = [
  {
    key: 'tag',
    label: '添加标签',
    icon: PriceTag,
    handler: async (items) => {
      ElMessage.info(`为 ${items.length} 个员工添加标签`)
    }
  },
  {
    key: 'lock',
    label: '锁定账号',
    icon: Lock,
    divided: true,
    confirm: '确定要锁定选中的员工账号吗？',
    handler: async (items) => {
      ElMessage.warning(`已锁定 ${items.length} 个账号`)
    }
  },
  {
    key: 'unlock',
    label: '解锁账号',
    icon: Unlock,
    handler: async (items) => {
      ElMessage.success(`已解锁 ${items.length} 个账号`)
    }
  }
]

// 统计信息
const advancedStatistics = computed(() => {
  const selected = advancedSelectedItems.value
  const totalSalary = selected.reduce((sum, item) => sum + item.salary, 0)
  const activeCount = selected.filter(item => item.status === 'active').length
  
  return [
    {
      key: 'total',
      label: '选中总数',
      value: selected.length
    },
    {
      key: 'salary',
      label: '薪资总额',
      value: `¥${totalSalary.toLocaleString()}`
    },
    {
      key: 'active',
      label: '在职人数',
      value: activeCount
    }
  ]
})

// 卡片数据
const cardData = ref([
  { id: 1, name: '张三', position: '前端工程师', department: '技术部', avatar: '' },
  { id: 2, name: '李四', position: '产品经理', department: '产品部', avatar: '' },
  { id: 3, name: '王五', position: 'UI设计师', department: '设计部', avatar: '' },
  { id: 4, name: '赵六', position: '运营专员', department: '运营部', avatar: '' },
  { id: 5, name: '钱七', position: '市场经理', department: '市场部', avatar: '' },
  { id: 6, name: '孙八', position: '后端工程师', department: '技术部', avatar: '' }
])

const cardSelectedItems = ref<any[]>([])

// 卡片操作
const cardActions: BatchAction[] = [
  {
    key: 'message',
    label: '发送消息',
    type: 'primary',
    minSelection: 1,
    showCount: true,
    handler: async (items) => {
      ElMessage.success(`准备向 ${items.length} 人发送消息`)
      addOperationRecord('发送消息', `向 ${items.length} 名员工发送了消息`, 'success')
    }
  },
  {
    key: 'assign',
    label: '分配任务',
    minSelection: 1,
    handler: async (items) => {
      ElMessage.info('打开任务分配对话框...')
      addOperationRecord('分配任务', `为 ${items.length} 名员工分配了新任务`, 'primary')
    }
  }
]

// 操作历史
const operationHistory = ref<OperationRecord[]>([])

   
const addOperationRecord = (action: string, description: string, status: unknown = 'info') => {
  operationHistory.value.unshift({
    id: `op-${Date.now()}`,
    action,
    description,
    time: new Date().toLocaleTimeString(),
    status,
    canUndo: true,
    undoHandler: () => {
      ElMessage.info(`撤销操作：${action}`)
    }
  })
}

// 状态类型映射
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    active: 'success',
    inactive: 'danger',
    pending: 'warning',
    suspended: 'info'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    active: '在职',
    inactive: '离职',
    pending: '待定',
    suspended: '停职'
  }
  return textMap[status] || status
}

// 基础表格事件处理
   
const handleBasicSelectionChange = (selection: unknown[]) => {
  basicSelectedItems.value = selection
}

const handleBasicSelectAll = (selected: boolean) => {
  if (selected) {
    basicTableRef.value?.toggleAllSelection()
  } else {
    basicTableRef.value?.clearSelection()
  }
}

const handleBasicClearSelection = () => {
  basicTableRef.value?.clearSelection()
}

   
const handleBasicBatchAction = (action: BatchAction, items: unknown[]) => {
  console.log('Batch action:', action.key, items)
}

// 高级表格事件处理
   
const handleAdvancedSelectionChange = (selection: unknown[]) => {
  if (!isSelectingAllPages.value) {
    advancedSelectedItems.value = selection
  }
}

const handleAdvancedSelectAll = (selected: boolean) => {
  if (selected) {
    advancedTableRef.value?.toggleAllSelection()
  } else {
    advancedTableRef.value?.clearSelection()
    isSelectingAllPages.value = false
  }
}

const handleSelectAcrossPages = () => {
  isSelectingAllPages.value = true
  advancedSelectedItems.value = Array.from({ length: advancedTotal.value }, (_, i) => ({ id: i + 1 }))
  ElMessage.info(`已选择所有 ${advancedTotal.value} 条数据`)
}

const handleAdvancedClearSelection = () => {
  advancedTableRef.value?.clearSelection()
  isSelectingAllPages.value = false
}

   
const handleAdvancedBatchAction = (action: BatchAction, items: unknown[]) => {
  if (isSelectingAllPages.value) {
    ElMessage.info(`对所有 ${advancedTotal.value} 条数据执行操作：${action.label}`)
  }
}

// 行点击和Shift/Ctrl选择
let lastClickedIndex = -1

   
const handleRowClick = (row: unknown, column: unknown, event: MouseEvent) => {
  if (column.type === 'selection') return
  
  const currentIndex = advancedData.value.findIndex(item => item.id === row.id)
  
  if (event.shiftKey && lastClickedIndex !== -1) {
    // Shift + 点击：连续选择
    const start = Math.min(lastClickedIndex, currentIndex)
    const end = Math.max(lastClickedIndex, currentIndex)
    
    for (let i = start; i <= end; i++) {
      advancedTableRef.value?.toggleRowSelection(advancedData.value[i], true)
    }
  } else if (event.ctrlKey || event.metaKey) {
    // Ctrl/Cmd + 点击：切换选择
    advancedTableRef.value?.toggleRowSelection(row)
  } else {
    // 普通点击：单选
    advancedTableRef.value?.clearSelection()
    advancedTableRef.value?.toggleRowSelection(row, true)
  }
  
  lastClickedIndex = currentIndex
}

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val
  generateAdvancedData()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  generateAdvancedData()
  if (!isSelectingAllPages.value) {
    advancedSelectedItems.value = []
  }
}

// 单个操作
   
const handleEdit = (row: unknown) => {
  ElMessage.info(`编辑：${row.name}`)
}

   
const handleView = (row: unknown) => {
  ElMessage.info(`查看：${row.name}`)
}

// 卡片选择
   
const isCardSelected = (item: unknown) => {
  return cardSelectedItems.value.some(selected => selected.id === item.id)
}

   
const toggleCardSelection = (item: unknown) => {
  const index = cardSelectedItems.value.findIndex(selected => selected.id === item.id)
  if (index > -1) {
    cardSelectedItems.value.splice(index, 1)
  } else {
    cardSelectedItems.value.push(item)
  }
}

   
const handleCardBatchAction = (action: BatchAction, items: unknown[]) => {
  console.log('Card batch action:', action.key, items)
}
</script>

<style lang="scss" scoped>
.batch-operation-demo {
  padding: 20px;
  
  .demo-section {
    margin-bottom: 20px;
    
    h5 {
      margin: 0 0 12px;
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
    
    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        color: var(--el-text-color-regular);
        font-size: 14px;
      }
    }
    
    .el-card {
      &.is-never-shadow {
        border: 1px solid var(--el-border-color-lighter);
      }
    }
  }
  
  // 卡片项
  .card-item {
    margin-bottom: 16px;
    cursor: pointer;
    transition: all 0.3s;
    position: relative;
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }
    
    &.is-selected {
      border-color: var(--el-color-primary);
      box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.2);
    }
    
    .card-checkbox {
      position: absolute;
      top: 12px;
      right: 12px;
    }
    
    .card-content {
      text-align: center;
      padding: 20px 0;
      
      .el-avatar {
        margin-bottom: 12px;
      }
      
      h4 {
        margin: 0 0 8px;
        font-size: 16px;
        font-weight: 500;
      }
      
      p {
        margin: 0 0 12px;
        color: var(--el-text-color-regular);
        font-size: 14px;
      }
    }
  }
}
</style>