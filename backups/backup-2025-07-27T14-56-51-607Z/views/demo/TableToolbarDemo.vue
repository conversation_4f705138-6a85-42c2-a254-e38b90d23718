<template>
  <div class="table-toolbar-demo">
    <h1>表格工具栏演示</h1>
    <p class="demo-desc">展示表格工具栏的各种功能，包括搜索、导出、列设置、密度调整等</p>

    <!-- 完整工具栏示例 -->
    <section class="demo-section">
      <h2>完整工具栏示例</h2>
      <el-card>
        <div class="table-toolbar">
          <div class="toolbar-left">
            <el-space>
              <el-input
                v-model="searchText"
                placeholder="搜索员工姓名、部门"
                :prefix-icon="Search"
                clearable
                style="width: 300px"
                @input="handleSearch"
                />
              <el-button type="primary" :icon="Plus" @click="handleAdd">新增员工</el-button>
              <el-button :icon="Upload" @click="handleImport">导入</el-button>
              <el-button :icon="Download" @click="handleExport">导出</el-button>
            </el-space>
          </div>
          
          <div class="toolbar-right">
            <el-space>
              <el-dropdown @command="handleDensityChange">
                <el-button :icon="Menu">
                  密度<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="large">宽松</el-dropdown-item>
                    <el-dropdown-item command="default">正常</el-dropdown-item>
                    <el-dropdown-item command="small">紧凑</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              
              <el-popover placement="bottom" width="200" trigger="click">
                <template #reference>
                  <el-button :icon="Setting">列设置</el-button>
                </template>
                <div class="column-settings">
                  <el-checkbox
                    v-for="col in columns"
                    :key="col.prop"
                    v-model="col.visible"
                    :label="col.label"
                   />
                </div>
              </el-popover>
              
              <el-button :icon="Refresh" circle @click="handleRefresh"   />
            </el-space>
          </div>
        </div>
        
        <el-table
          :data="tableData"
          :size="tableSize"
          v-loading="loading"
          style="width: 100%; margin-top: 20px"
        >
          <el-table-column
            v-for="col in visibleColumns"
            :key="col.prop"
            :prop="col.prop"
            :label="col.label"
            :width="col.width"
            :min-width="col.minWidth"
           />
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" link>编辑</el-button>
              <el-button type="danger" link>删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          style="margin-top: 20px; justify-content: flex-end"
         />
      </el-card>
    </section>

    <!-- 高级搜索示例 -->
    <section class="demo-section">
      <h2>高级搜索工具栏</h2>
      <el-card>
        <div class="advanced-search-toolbar">
          <el-form :inline="true" :model="searchForm" class="search-form">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="姓名">
                  <el-input v-model="searchForm.name" placeholder="请输入姓名"   />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="部门">
                  <el-select v-model="searchForm.department" placeholder="请选择部门">
                    <el-option label="技术部" value="tech"  />
                    <el-option label="人事部" value="hr"  />
                    <el-option label="财务部" value="finance"  />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="职位">
                  <el-select v-model="searchForm.position" placeholder="请选择职位">
                    <el-option label="经理" value="manager"  />
                    <el-option label="主管" value="supervisor"  />
                    <el-option label="专员" value="specialist"  />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="状态">
                  <el-select v-model="searchForm.status" placeholder="请选择状态">
                    <el-option label="在职" value="active"  />
                    <el-option label="离职" value="inactive"  />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24" style="text-align: right">
                <el-button type="primary" @click="handleAdvancedSearch">搜索</el-button>
                <el-button @click="handleResetSearch">重置</el-button>
                <el-button link @click="showMore = !showMore">
                  {{ showMore ? '收起' : '展开' }}
                  <el-icon class="el-icon--right">
                    <ArrowDown v-if="!showMore" />
                    <ArrowUp v-else />
                  </el-icon>
                </el-button>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </el-card>
    </section>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed } from 'vue'
import { 
  Search, 
  Plus, 
  Upload, 
  Download, 
  Menu, 
  Setting, 
  Refresh,
  ArrowDown,
  ArrowUp
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 表格数据
const loading = ref(false)
const searchText = ref('')
const tableData = ref<any[]>([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(100)
const tableSize = ref<'large' | 'default' | 'small'>('default')
const showMore = ref(false)

// 列配置
const columns = reactive([
  { prop: 'id', label: 'ID', width: '80', visible: true },
  { prop: 'name', label: '姓名', width: '120', visible: true },
  { prop: 'department', label: '部门', width: '120', visible: true },
  { prop: 'position', label: '职位', width: '120', visible: true },
  { prop: 'email', label: '邮箱', minWidth: '180', visible: true },
  { prop: 'phone', label: '电话', width: '140', visible: true },
  { prop: 'status', label: '状态', width: '100', visible: true }
])

// 高级搜索表单
const searchForm = reactive({
  name: '',
  department: '',
  position: '',
  status: ''
})

// 计算可见列
const visibleColumns = computed(() => {
  return columns.filter(col => col.visible)
})

// 生成模拟数据
const generateMockData = () => {
  const departments = ['技术部', '人事部', '财务部', '市场部']
  const positions = ['经理', '主管', '专员', '工程师']
  
  tableData.value = Array.from({ length: 20 }, (_, i) => ({
    id: i + 1,
    name: `员工${i + 1}`,
    department: departments[Math.floor(Math.random() * departments.length)],
    position: positions[Math.floor(Math.random() * positions.length)],
    email: `employee${i + 1}@company.com`,
    phone: `138${Math.floor(Math.random() * *********).toString().padStart(8, '0')}`,
    status: Math.random() > 0.3 ? '在职' : '离职'
  }))
}

// 搜索处理
const handleSearch = () => {
  loading.value = true
  setTimeout(() => {
    generateMockData()
    loading.value = false
    ElMessage.success('搜索完成')
  }, 500)
}

// 操作处理
const handleAdd = () => {
  ElMessage.info('新增员工功能开发中...')
}

const handleImport = () => {
  ElMessage.info('导入功能开发中...')
}

const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

const handleDensityChange = (command: string) => {
  tableSize.value = command as unknown
  ElMessage.success(`表格密度已调整为${command === 'large' ? '宽松' : command === 'small' ? '紧凑' : '正常'}`)
}

const handleRefresh = () => {
  loading.value = true
  setTimeout(() => {
    generateMockData()
    loading.value = false
    ElMessage.success('数据已刷新')
  }, 500)
}

const handleAdvancedSearch = () => {
  console.log('高级搜索:', searchForm)
  handleSearch()
}

const handleResetSearch = () => {
  searchForm.name = ''
  searchForm.department = ''
  searchForm.position = ''
  searchForm.status = ''
  handleSearch()
}

// 初始化数据
generateMockData()
</script>

<style scoped>
.table-toolbar-demo {
  padding: 20px;
}

.demo-desc {
  color: #666;
  margin-bottom: 20px;
}

.demo-section {
  margin-bottom: 40px;
}

.demo-section h2 {
  margin-bottom: 20px;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.toolbar-left {
  flex: 1;
}

.toolbar-right {
  flex-shrink: 0;
}

.column-settings {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.advanced-search-toolbar {
  padding: 20px;
  background: #f5f7fa;
  border-radius: 4px;
}

.search-form {
  width: 100%;
}
</style>