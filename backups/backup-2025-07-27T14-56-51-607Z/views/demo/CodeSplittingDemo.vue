<template>
  <div class="code-splitting-demo">
    <h1>代码分割优化演示</h1>
    
    <!-- 预加载状态 -->
    <el-card class="status-card">
      <template #header>
        <div class="card-header">
          <span>预加载状态</span>
          <el-button @click="refreshStatus" :icon="Refresh" circle   />
        </div>
      </template>
      
      <el-descriptions :column="2" border>
        <el-descriptions-item label="网络速度">
          <el-tag :type="getNetworkType(preloadStatus.networkSpeed)">
            {{ preloadStatus.networkSpeed }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="已加载模块">
          <el-tag type="success">{{ preloadStatus.loaded.length }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="加载中">
          <el-tag type="warning">{{ preloadStatus.loading.length }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="加载失败">
          <el-tag type="danger">{{ preloadStatus.failed.length }}</el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
    
    <!-- 动态加载组件 -->
    <el-card class="demo-card">
      <template #header>
        <span>动态加载组件</span>
      </template>
      
      <el-space wrap>
        <el-button
          v-for="comp in components"
          :key="comp.name"
          :type="comp.loaded ? 'success' : 'default'"
          :loading="comp.loading"
          @click="loadComponent(comp)"
        >
          {{ comp.loaded ? '✓' : '' }} {{ comp.label }}
        </el-button>
      </el-space>
      
      <div v-if="currentComponent" class="component-preview">
        <h3>组件预览: {{ currentComponent.label }}</h3>
        <component :is="currentComponent.component" />
      </div>
    </el-card>
    
    <!-- 路由预加载 -->
    <el-card class="demo-card">
      <template #header>
        <span>路由预加载演示</span>
      </template>
      
      <el-table :data="routeModules" style="width: 100%">
        <el-table-column prop="name" label="模块名称"  />
        <el-table-column prop="priority" label="优先级">
          <template #default="{ row }">
            <el-progress
              :percentage="row.priority"
              :stroke-width="10"
              :color="getProgressColor(row.priority)"
             />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button
              size="small"
              :loading="row.loading"
              @click="preloadModule(row)"
            >
              预加载
            </el-button>
            <el-button
              size="small"
              type="primary"
              @click="navigateToModule(row)"
            >
              访问
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- chunk 分析 -->
    <el-card class="demo-card">
      <template #header>
        <span>Chunk 分割策略</span>
      </template>
      
      <el-tree
        :data="chunkTree"
        :props="treeProps"
        default-expand-all
        node-key="id"
      >
        <template #default="{ node, data }">
          <span class="tree-node">
            <el-icon v-if="data.icon" :size="16">
              <component :is="data.icon" />
            </el-icon>
            <span>{{ node.label }}</span>
            <el-tag v-if="data.size" type="info" size="small">
              {{ formatSize(data.size) }}
            </el-tag>
          </span>
        </template>
      </el-tree>
    </el-card>
    
    <!-- 性能对比 -->
    <el-card class="demo-card">
      <template #header>
        <span>性能对比</span>
      </template>
      
      <div ref="performanceChart" style="height: 400px;"></div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, onMounted, getCurrentInstance, markRaw } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Refresh, FolderOpened, Document } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { smartPreloader, preloadComponent, MODULE_PRIORITY } from '@/utils/code-splitting'

// 获取应用实例
const instance = getCurrentInstance()
const router = useRouter()

// 预加载状态
const preloadStatus = ref({
  loaded: [],
  loading: [],
  failed: [],
  networkSpeed: 'unknown'
})

// 动态组件列表
const components = reactive([
  {
    name: 'EmployeeList',
    label: '员工列表',
    path: '@/views/employee/list/index.vue',
    loaded: false,
    loading: false,
    component: null
  },
  {
    name: 'OrganizationChart',
    label: '组织架构图',
    path: '@/views/organization/index.vue',
    loaded: false,
    loading: false,
    component: null
  },
  {
    name: 'SalaryCalculator',
    label: '薪资计算器',
    path: '@/components/salary/SalaryCalculator.vue',
    loaded: false,
    loading: false,
    component: null
  },
  {
    name: 'WorkflowDesigner',
    label: '流程设计器',
    path: '@/views/workflow/ProcessDesigner.vue',
    loaded: false,
    loading: false,
    component: null
  }
])

// 当前加载的组件
const currentComponent = ref(null)

// 路由模块
const routeModules = ref([
  { name: '看板', path: '/dashboard', priority: 100, loading: false },
  { name: '员工管理', path: '/employee', priority: 90, loading: false },
  { name: '组织架构', path: '/organization', priority: 80, loading: false },
  { name: '薪资管理', path: '/salary', priority: 70, loading: false },
  { name: '考勤管理', path: '/attendance', priority: 60, loading: false },
  { name: '绩效管理', path: '/performance', priority: 50, loading: false },
  { name: '工作流', path: '/workflow', priority: 40, loading: false },
  { name: '系统设置', path: '/system', priority: 30, loading: false }
])

// chunk 树形结构
const chunkTree = ref([
  {
    id: '1',
    label: 'main.js (主入口)',
    icon: markRaw(Document),
    size: 150000,
    children: [
      {
        id: '1-1',
        label: 'vue-vendor (框架核心)',
        icon: markRaw(FolderOpened),
        size: 300000,
        children: [
          { id: '1-1-1', label: 'vue', size: 120000 },
          { id: '1-1-2', label: 'vue-router', size: 80000 },
          { id: '1-1-3', label: 'pinia', size: 60000 }
        ]
      },
      {
        id: '1-2',
        label: 'element-plus (UI库)',
        icon: markRaw(FolderOpened),
        size: 500000
      },
      {
        id: '1-3',
        label: '业务模块',
        icon: markRaw(FolderOpened),
        children: [
          { id: '1-3-1', label: 'employee-module', size: 200000 },
          { id: '1-3-2', label: 'organization-module', size: 150000 },
          { id: '1-3-3', label: 'salary-module', size: 180000 },
          { id: '1-3-4', label: 'workflow-module', size: 250000 }
        ]
      }
    ]
  }
])

// 树形结构配置
const treeProps = {
  children: 'children',
  label: 'label'
}

// 性能图表
const performanceChart = ref(null)

// 刷新预加载状态
const refreshStatus = () => {
  preloadStatus.value = smartPreloader.getPreloadStatus()
}

// 加载组件
   
const loadComponent = async (comp: unknown) => {
  comp.loading = true
  const startTime = Date.now()
  
  try {
    const module = await import(/* @vite-ignore */ comp.path)
    const loadTime = Date.now() - startTime
    
    comp.component = markRaw(module.default)
    comp.loaded = true
    currentComponent.value = comp
    
    ElMessage.success(`组件加载成功，耗时: ${loadTime}ms`)
  } catch (__error) {
    ElMessage.error('组件加载失败')
    console.error(error)
  } finally {
    comp.loading = false
  }
}

// 预加载模块
   
const preloadModule = async (module: unknown) => {
  module.loading = true
  
  try {
    // 模拟预加载模块组件
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success(`模块 ${module.name} 预加载完成`)
  } catch (__error) {
    ElMessage.error('预加载失败')
  } finally {
    module.loading = false
  }
}

// 导航到模块
   
const navigateToModule = (module: unknown) => {
  router.push(module.path)
}

// 获取网络类型
const getNetworkType = (speed: string) => {
  const typeMap: Record<string, string> = {
    'fast': 'success',
    'slow': 'warning',
    'unknown': 'info'
  }
  return typeMap[speed] || 'info'
}

// 获取进度条颜色
const getProgressColor = (percentage: number) => {
  if (percentage >= 80) return '#67c23a'
  if (percentage >= 60) return '#409eff'
  if (percentage >= 40) return '#e6a23c'
  return '#f56c6c'
}

// 格式化文件大小
const formatSize = (size: number) => {
  if (size < 1024) return `${size} B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`
  return `${(size / (1024 * 1024)).toFixed(1)} MB`
}

// 初始化性能图表
const initPerformanceChart = () => {
  if (!performanceChart.value) return
  
  const chart = echarts.init(performanceChart.value)
  
  const option = {
    title: {
      text: '代码分割前后性能对比',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['分割前', '分割后'],
      bottom: 0
    },
    xAxis: {
      type: 'category',
      data: ['首屏加载', '路由切换', '组件加载', '总体大小']
    },
    yAxis: [
      {
        type: 'value',
        name: '时间 (ms)',
        position: 'left'
      },
      {
        type: 'value',
        name: '大小 (MB)',
        position: 'right'
      }
    ],
    series: [
      {
        name: '分割前',
        type: 'bar',
        data: [3500, 800, 1200, 15],
        itemStyle: {
          color: '#f56c6c'
        }
      },
      {
        name: '分割后',
        type: 'bar',
        data: [1800, 300, 500, 8],
        itemStyle: {
          color: '#67c23a'
        }
      }
    ]
  }
  
  chart.setOption(option)
  
  // 响应式
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

// 挂载时初始化
onMounted(() => {
  refreshStatus()
  initPerformanceChart()
  
  // 定时刷新状态
  setInterval(refreshStatus, 5000)
})
</script>

<style scoped lang="scss">
.code-splitting-demo {
  padding: 20px;
  
  h1 {
    text-align: center;
    margin-bottom: 30px;
  }
}

.status-card {
  margin-bottom: 20px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.demo-card {
  margin-bottom: 20px;
}

.component-preview {
  margin-top: 20px;
  padding: 20px;
  border: 1px dashed var(--el-border-color);
  border-radius: 4px;
  
  h3 {
    margin-top: 0;
  }
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  
  .el-icon {
    color: var(--el-color-primary);
  }
}

// 响应式
@media (max-width: 768px) {
  .el-descriptions {
    :deep(.el-descriptions__body) {
      display: block;
    }
  }
}
</style>