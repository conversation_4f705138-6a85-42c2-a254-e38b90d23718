<template>
  <div class="accessibility-demo">
    <el-card class="demo-header">
      <h1>无障碍功能演示</h1>
      <p>本页面展示了系统中实现的各种无障碍(ARIA)功能，确保所有用户都能顺畅使用系统。</p>
    </el-card>

    <!-- ARIA属性演示 -->
    <el-card class="demo-section">
      <template #header>
        <div class="card-header">
          <span>ARIA属性演示</span>
          <el-tag type="success">WCAG 2.1 AA</el-tag>
        </div>
      </template>
      
      <el-tabs v-model="activeTab">
        <el-tab-pane label="数据表格" name="table">
          <div class="demo-item">
            <h3>带ARIA属性的数据表格</h3>
            <p class="demo-description">
              表格组件已添加完整的ARIA属性支持，包括：
              - role="table" 标识表格角色
              - aria-label 提供表格描述
              - aria-sort 标识排序状态
              - 键盘导航支持（ESC清除选择，Ctrl+A全选）
              - 实时通知（选择数量、排序状态、翻页等）
            </p>
            
            <HrDataTable
              :data="tableData"
              :columns="tableColumns"
              :show-selection="true"
              :show-index="true"
              :show-pagination="true"
              :total="100"
              aria-label="员工信息表格"
              style="margin-top: 20px"
            />
            
            <div class="demo-tips">
              <el-alert type="info" :closable="false">
                <template #title>
                  <strong>键盘操作提示：</strong>
                  <ul style="margin: 5px 0 0 20px;">
                    <li>Tab键：在表格元素间导航</li>
                    <li>空格键：选择/取消选择当前行</li>
                    <li>Ctrl/Cmd + A：全选所有数据</li>
                    <li>ESC键：清除所有选择</li>
                  </ul>
                </template>
              </el-alert>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="表单组件" name="form">
          <div class="demo-item">
            <h3>带ARIA属性的表单</h3>
            <p class="demo-description">
              表单组件支持完整的ARIA属性和键盘导航：
              - role="form" 标识表单区域
              - aria-labelledby 关联表单标题
              - 分组支持展开/折叠的键盘操作
              - 表单验证的实时通知
            </p>
            
            <HrFormBuilder
              :model="formData"
              :groups="formGroups"
              title="员工信息表单"
              description="请填写员工的基本信息，带*的为必填项"
              aria-label="员工信息录入表单"
              style="margin-top: 20px"
            />
          </div>
        </el-tab-pane>

        <el-tab-pane label="虚拟列表" name="list">
          <div class="demo-item">
            <h3>带ARIA属性的虚拟滚动列表</h3>
            <p class="demo-description">
              虚拟列表组件支持：
              - role="list" 和 role="listitem" 语义标记
              - aria-setsize 和 aria-posinset 标识列表大小和位置
              - 完整的键盘导航（方向键、PageUp/Down、Home/End）
              - 滚动进度的实时通知
            </p>
            
            <VirtualList
              :data="largeListData"
              :item-height="60"
              height="400px"
              :show-quick-jump="true"
              :enable-keyboard-nav="true"
              aria-label="员工列表（10000条数据）"
              style="margin-top: 20px; border: 1px solid #e4e7ed; border-radius: 4px;"
            >
              <template #default="{ item, index }">
                <div style="padding: 0 20px; display: flex; align-items: center; justify-content: space-between;">
                  <div>
                    <div style="font-weight: 500;">{{ item.name }}</div>
                    <div style="font-size: 12px; color: #909399;">{{ item.department }} - {{ item.position }}</div>
                  </div>
                  <el-tag>{{ index + 1 }}</el-tag>
                </div>
              </template>
            </VirtualList>
            
            <div class="demo-tips">
              <el-alert type="info" :closable="false">
                <template #title>
                  <strong>键盘导航：</strong>
                  <ul style="margin: 5px 0 0 20px;">
                    <li>上/下方向键：逐项导航</li>
                    <li>PageUp/PageDown：翻页</li>
                    <li>Home/End：跳转到开始/结束</li>
                  </ul>
                </template>
              </el-alert>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="混入工具" name="mixin">
          <div class="demo-item">
            <h3>无障碍混入工具演示</h3>
            <p class="demo-description">
              系统提供了 accessibilityMixin 和 useAccessibility 工具，方便开发者快速添加无障碍支持。
            </p>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-card>
                  <template #header>
                    <span>焦点陷阱演示</span>
                  </template>
                  <el-button @click="showFocusTrapDialog = true">打开对话框</el-button>
                  <el-dialog
                    v-model="showFocusTrapDialog"
                    title="焦点陷阱演示"
                    width="400px"
                  >
                    <p>在这个对话框中，Tab键导航会被限制在对话框内部，不会跳出到外部元素。</p>
                    <el-input v-model="dialogInput1" placeholder="输入框1" style="margin-top: 10px;"   />
                    <el-input v-model="dialogInput2" placeholder="输入框2" style="margin-top: 10px;"   />
                    <template #footer>
                      <el-button @click="showFocusTrapDialog = false">取消</el-button>
                      <el-button type="primary" @click="showFocusTrapDialog = false">确定</el-button>
                    </template>
                  </el-dialog>
                </el-card>
              </el-col>
              
              <el-col :span="12">
                <el-card>
                  <template #header>
                    <span>实时通知区域</span>
                  </template>
                  <el-button @click="announceMessageWithLog('操作成功！')">发送成功通知</el-button>
                  <el-button @click="announceMessageWithLog('请注意：有新的消息', 'polite')" style="margin-left: 10px;">发送普通通知</el-button>
                  <el-button @click="announceMessageWithLog('错误：操作失败！', 'assertive')" type="danger" style="margin-left: 10px;">发送错误通知</el-button>
                  <div class="notification-log">
                    <h4>通知历史：</h4>
                    <ul>
                      <li v-for="(log, index) in notificationLogs" :key="index">
                        {{ log.time }} - {{ log.message }} ({{ log.priority }})
                      </li>
                    </ul>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>

        <el-tab-pane label="键盘导航" name="keyboard">
          <div class="demo-item">
            <h3>键盘导航测试区</h3>
            <p class="demo-description">
              测试各种键盘导航功能，所有交互元素都应该可以通过键盘访问。
            </p>
            
            <div class="keyboard-test-area" tabindex="0" @keydown="handleKeyboardTest">
              <p>聚焦此区域后，按下不同的键查看效果：</p>
              <div class="key-display">
                <div>最后按下的键：<strong>{{ lastKey || '无' }}</strong></div>
                <div>组合键：
                  <el-tag v-if="keyModifiers.ctrl">Ctrl</el-tag>
                  <el-tag v-if="keyModifiers.alt">Alt</el-tag>
                  <el-tag v-if="keyModifiers.shift">Shift</el-tag>
                  <el-tag v-if="keyModifiers.meta">Meta/Cmd</el-tag>
                </div>
              </div>
              
              <el-divider   />
              
              <div class="focusable-grid">
                <div 
                  v-for="i in 9" 
                  :key="i"
                  class="grid-item"
                  :class="{ focused: focusedGridItem === i }"
                  tabindex="0"
                  @focus="focusedGridItem = i"
                  @blur="focusedGridItem = focusedGridItem === i ? 0 : focusedGridItem"
                >
                  项目 {{ i }}
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 无障碍检查清单 -->
    <el-card class="demo-section">
      <template #header>
        <span>无障碍检查清单</span>
      </template>
      
      <el-table :data="checklistData" stripe>
        <el-table-column prop="category" label="类别" width="150"  />
        <el-table-column prop="item" label="检查项"  />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-icon v-if="row.status === 'done'" style="color: #67c23a;"><CircleCheck /></el-icon>
            <el-icon v-else-if="row.status === 'partial'" style="color: #e6a23c;"><Warning /></el-icon>
            <el-icon v-else style="color: #909399;"><Clock /></el-icon>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="说明"  />
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'AccessibilityDemo'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { CircleCheck, Warning, Clock } from '@element-plus/icons-vue'
import HrDataTable from '@/components/data-table/HrDataTable.vue'
import HrFormBuilder from '@/components/form-builder/HrFormBuilder.vue'
import HrVirtualList from '@/components/common/HrVirtualList.vue'
import { useAccessibility } from '@/mixins/accessibility'

const {announceMessage: _announceMessage} =  useAccessibility()

// 状态
const activeTab 

  .demo-header {
    margin-bottom: 20px;
    
    h1 {
      margin: 0 0 10px;
      font-size: 24px;
    }
    
    p {
      margin: 0;
      color: #606266;
    }
  }

  .demo-section {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }

  .demo-item {
    margin-bottom: 30px;
    
    h3 {
      margin: 0 0 10px;
      font-size: 18px;
    }
    
    .demo-description {
      margin: 0 0 20px;
      color: #606266;
      line-height: 1.6;
    }
    
    .demo-tips {
      margin-top: 20px;
    }
  }

  .keyboard-test-area {
    padding: 20px;
    border: 2px dashed #409eff;
    border-radius: 4px;
    background-color: #f5f7fa;
    outline: none;
    
    &:focus {
      border-color: #66b1ff;
      background-color: #ecf5ff;
    }
    
    .key-display {
      margin: 20px 0;
      padding: 15px;
      background: white;
      border-radius: 4px;
      
      strong {
        color: #409eff;
        font-size: 18px;
      }
      
      .el-tag {
        margin-left: 5px;
      }
    }
    
    .focusable-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 10px;
      
      .grid-item {
        padding: 20px;
        text-align: center;
        background: white;
        border: 2px solid #dcdfe6;
        border-radius: 4px;
        cursor: pointer;
        outline: none;
        transition: all 0.3s;
        
        &:focus,
        &.focused {
          border-color: #409eff;
          background-color: #ecf5ff;
          transform: scale(1.05);
        }
      }
    }
  }

  .notification-log {
    margin-top: 20px;
    padding: 15px;
    background: #f5f7fa;
    border-radius: 4px;
    
    h4 {
      margin: 0 0 10px;
      font-size: 14px;
    }
    
    ul {
      margin: 0;
      padding-left: 20px;
      font-size: 12px;
      color: #606266;
      
      li {
        margin-bottom: 5px;
      }
    }
  }
}
</style>