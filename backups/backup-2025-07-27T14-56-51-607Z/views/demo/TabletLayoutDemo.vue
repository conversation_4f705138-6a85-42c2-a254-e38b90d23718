<template>
  <div class="tablet-layout-demo">
    <hr-tablet-layout-optimization
      :layout="layout"
      :sidebar-mode="sidebarMode"
      :show-sidebar="showSidebar"
      :show-header="showHeader"
      :show-breadcrumb="showBreadcrumb"
      :show-search="showSearch"
      :show-user-info="showUserInfo"
      :title="title"
      :logo="logo"
      :menu-items="menuItems"
      :breadcrumb-items="breadcrumbItems"
      :active-menu="activeMenu"
      :user-name="userName"
      :user-avatar="userAvatar"
      @menu-select="handleMenuSelect"
      @search="handleSearch"
      @user-command="handleUserCommand"
    >
      <!-- 自定义侧边栏底部 -->
      <template #sidebar-footer>
        <div class="sidebar-footer-content">
          <el-button type="text" @click="showSettings = true">
            <el-icon><Setting /></el-icon>
            系统设置
          </el-button>
        </div>
      </template>

      <!-- 内容头部 -->
      <template #content-header>
        <PageHeader 
          title="平板布局优化演示" 
          :show-back="false"
        >
          展示自适应布局、响应式设计、触摸优化等特性
        </PageHeader>
      </template>

      <!-- 主要内容 -->
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="i in 8" :key="i">
          <el-card class="demo-card">
            <template #header>
              <span>卡片 {{ i }}</span>
            </template>
            <div class="card-content">
              <el-statistic 
                :title="`指标 ${i}`" 
                :value="1000 + i * 123"
                :precision="0"
               />
              <div class="card-actions">
                <el-button type="text">查看详情</el-button>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 响应式表格 -->
      <el-card class="demo-section">
        <template #header>
          <span>响应式数据表格</span>
        </template>
        
        <el-table 
          :data="tableData" 
          style="width: 100%"
          :size="tableSize"
        >
          <el-table-column
            v-for="col in visibleColumns"
            :key="col.prop"
            :prop="col.prop"
            :label="col.label"
            :width="col.width"
            :min-width="col.minWidth"
           />
          <el-table-column fixed="right" label="操作" :width="operationWidth">
            <template #default>
              <el-button link type="primary" size="small">编辑</el-button>
              <el-button link type="danger" size="small">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 响应式表单 -->
      <el-card class="demo-section">
        <template #header>
          <span>响应式表单布局</span>
        </template>
        
        <el-form :model="form" label-width="auto">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8">
              <el-form-item label="姓名">
                <el-input v-model="form.name"   />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <el-form-item label="邮箱">
                <el-input v-model="form.email"   />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <el-form-item label="电话">
                <el-input v-model="form.phone"   />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <el-form-item label="部门">
                <el-select v-model="form.department" style="width: 100%">
                  <el-option label="技术部" value="tech"  />
                  <el-option label="产品部" value="product"  />
                  <el-option label="市场部" value="market"  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <el-form-item label="职位">
                <el-select v-model="form.position" style="width: 100%">
                  <el-option label="工程师" value="engineer"  />
                  <el-option label="经理" value="manager"  />
                  <el-option label="主管" value="supervisor"  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8">
              <el-form-item label="入职日期">
                <el-date-picker
                  v-model="form.joinDate"
                  type="date"
                  placeholder="选择日期"
                  style="width: 100%"
                 />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>

      <!-- 浮动操作按钮 -->
      <template #floating-actions>
        <el-button
          circle
          type="primary"
          :icon="Plus"
          @click="handleAdd"
          />
        <el-button
          circle
          :icon="Refresh"
          @click="handleRefresh"
          />
      </template>
    </TabletLayoutOptimization>

    <!-- 设置对话框 -->
    <el-dialog
      v-model="showSettings"
      title="布局设置"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form label-width="120px">
        <el-form-item label="布局模式">
          <el-radio-group v-model="layout">
            <el-radio label="default">默认</el-radio>
            <el-radio label="compact">紧凑</el-radio>
            <el-radio label="wide">宽屏</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="侧边栏模式">
          <el-radio-group v-model="sidebarMode">
            <el-radio label="normal">正常</el-radio>
            <el-radio label="mini">迷你</el-radio>
            <el-radio label="overlay">浮层</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="显示侧边栏">
          <el-switch v-model="showSidebar"  />
        </el-form-item>
        
        <el-form-item label="显示顶栏">
          <el-switch v-model="showHeader"  />
        </el-form-item>
        
        <el-form-item label="显示面包屑">
          <el-switch v-model="showBreadcrumb"  />
        </el-form-item>
        
        <el-form-item label="显示搜索">
          <el-switch v-model="showSearch"  />
        </el-form-item>
        
        <el-form-item label="显示用户信息">
          <el-switch v-model="showUserInfo"  />
        </el-form-item>
        
        <el-form-item label="系统标题">
          <el-input v-model="title"   />
        </el-form-item>
        
        <el-form-item label="用户名称">
          <el-input v-model="userName"   />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showSettings = false">取消</el-button>
        <el-button type="primary" @click="showSettings = false">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
 
import { ref, computed, inject, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Setting, 
  Plus, 
  Refresh,
  HomeFilled,
  User,
  Document,
  DataAnalysis,
  List,
  Edit,
  View
} from '@element-plus/icons-vue'
import HrPageHeader from '@/components/common/HrPageHeader.vue'
import HrTabletLayoutOptimization from '@/components/ux/HrTabletLayoutOptimization.vue'

// 注入布局信息
const layoutInfo = inject('tabletLayout', {
  isMobile: computed(() => false),
  isTablet: computed(() => false),
  isDesktop: computed(() => true)
})

// 配置
const showSettings = ref(false)
const layout = ref('default')
const sidebarMode = ref('normal')
const showSidebar = ref(true)
const showHeader = ref(true)
const showBreadcrumb = ref(true)
const showSearch = ref(true)
const showUserInfo = ref(true)

// 内容配置
const title = ref('人事管理系统')
const logo = ref('/logo.png')
const userName = ref('管理员')
const userAvatar = ref('https://api.dicebear.com/7.x/avataaars/svg?seed=Admin')
const activeMenu = ref('dashboard')

// 菜单项
const menuItems = ref([
  {
    id: 'dashboard',
    title: '仪表板',
    icon: HomeFilled,
    path: '/dashboard'
  },
  {
    id: 'employee',
    title: '员工管理',
    icon: User,
    children: [
      {
        id: 'employee-list',
        title: '员工列表',
        icon: List,
        path: '/employee/list'
      },
      {
        id: 'employee-add',
        title: '新增员工',
        icon: Edit,
        path: '/employee/add'
      },
      {
        id: 'employee-detail',
        title: '员工详情',
        icon: View,
        path: '/employee/detail'
      }
    ]
  },
  {
    id: 'document',
    title: '文档管理',
    icon: Document,
    path: '/document'
  },
  {
    id: 'analysis',
    title: '数据分析',
    icon: DataAnalysis,
    path: '/analysis'
  }
])

// 面包屑
const breadcrumbItems = ref([
  { title: '首页', path: '/' },
  { title: '演示页面', path: '/demo' },
  { title: '平板布局优化' }
])

// 表单数据
const form = ref({
  name: '',
  email: '',
  phone: '',
  department: '',
  position: '',
  joinDate: ''
})

// 表格数据
const tableData = ref([
  {
    id: 1,
    name: 'HrHr张三',
    department: '技术部',
    position: '前端工程师',
    email: '<EMAIL>',
    phone: '13800138001',
    status: '在职'
  },
  {
    id: 2,
    name: '李四',
    department: '产品部',
    position: '产品经理',
    email: '<EMAIL>',
    phone: '13800138002',
    status: '在职'
  },
  {
    id: 3,
    name: '王五',
    department: '市场部',
    position: '市场总监',
    email: '<EMAIL>',
    phone: '13800138003',
    status: '休假'
  }
])

// 响应式表格配置
const tableSize = computed(() => {
  if (layoutInfo.isMobile.value) return 'small'
  if (layoutInfo.isTablet.value) return 'default'
  return 'default'
})

const operationWidth = computed(() => {
  if (layoutInfo.isMobile.value) return 100
  return 120
})

const visibleColumns = computed(() => {
  const allColumns = [
    { prop: 'name', label: '姓名', minWidth: 100 },
    { prop: 'department', label: '部门', minWidth: 120 },
    { prop: 'position', label: '职位', minWidth: 120 },
    { prop: 'email', label: '邮箱', minWidth: 180 },
    { prop: 'phone', label: '电话', minWidth: 120 },
    { prop: 'status', label: '状态', width: 80 }
  ]
  
  if (layoutInfo.isMobile.value) {
    // 移动端只显示关键列
    return allColumns.filter(col => 
      ['name', 'department', 'status'].includes(col.prop)
    )
  }
  
  if (layoutInfo.isTablet.value) {
    // 平板隐藏邮箱和电话
    return allColumns.filter(col => 
      !['email', 'phone'].includes(col.prop)
    )
  }
  
  return allColumns
})

// 事件处理
const handleMenuSelect = (id: string) => {
  activeMenu.value = id
  ElMessage.success(`选择菜单：${id}`)
}

const handleSearch = (query: string) => {
  ElMessage.info(`搜索：${query}`)
}

const handleUserCommand = (command: string) => {
  switch (command) {
    case 'profile':
      ElMessage.info('查看个人中心')
      break
    case 'settings':
      showSettings.value = true
      break
    case 'logout':
      ElMessage.warning('退出登录')
      break
  }
}

const handleAdd = () => {
  ElMessage.success('添加新项目')
}

const handleRefresh = () => {
  ElMessage.success('刷新数据')
}

// 初始化
onMounted(() => {
  // 根据设备类型设置默认值
  if (layoutInfo.isMobile.value) {
    showSidebar.value = false
  }
})
</script>

<style lang="scss" scoped>
.tablet-layout-demo {
  .demo-card {
    margin-bottom: 20px;
    
    .card-content {
      .card-actions {
        margin-top: 16px;
        text-align: right;
      }
    }
  }
  
  .demo-section {
    margin-bottom: 20px;
  }
  
  .sidebar-footer-content {
    text-align: center;
    
    .el-button {
      width: 100%;
      justify-content: flex-start;
    }
  }
}

// 响应式调整
@media (max-width: 768px) {
  .tablet-layout-demo {
    .el-dialog {
      width: 90% !important;
    }
  }
}
</style>