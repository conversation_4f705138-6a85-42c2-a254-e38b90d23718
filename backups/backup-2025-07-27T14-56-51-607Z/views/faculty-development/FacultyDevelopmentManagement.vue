<template>
  <div class="modern-faculty-development" :class="{ 'mobile-layout': isMobile }">
    <!-- 现代化页面头部 -->
    <div class="page-header" role="banner">
      <div class="header-content">
        <div class="header-title">
          <h1>
            <el-icon class="title-icon"><Reading /></el-icon>
            师资发展管理
          </h1>
          <p class="subtitle">智能化教师培训与职业发展管理平台</p>
        </div>

        <!-- 快速操作按钮 -->
        <div class="header-actions" v-if="!isMobile">
          <el-button type="primary" size="large" @click="createTrainingPlan">
            <el-icon><Plus /></el-icon>
            创建培训计划
          </el-button>

          <el-dropdown trigger="click" placement="bottom-end">
            <el-button size="large">
              <el-icon><MoreFilled /></el-icon>
              更多操作
            </el-button>

            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="enrollTraining()">
                  <el-icon><UserFilled /></el-icon>
                  培训报名
                </el-dropdown-item>
                <el-dropdown-item @click="applyFurtherStudy">
                  <el-icon><Notebook /></el-icon>
                  申请进修
                </el-dropdown-item>
                <el-dropdown-item @click="viewStatistics" divided>
                  <el-icon><DataAnalysis /></el-icon>
                  查看统计
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>

        <!-- 移动端操作按钮 -->
        <div class="mobile-actions" v-if="isMobile">
          <el-button type="primary" circle @click="createTrainingPlan">
            <el-icon><Plus /></el-icon>
          </el-button>
          <el-button circle @click="showMobileMenu = true">
            <el-icon><MoreFilled /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon training">
              <el-icon><School /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.activeTrainings }}</div>
              <div class="stats-label">进行中培训</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon participants">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.totalParticipants }}</div>
              <div class="stats-label">培训参与人次</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon hours">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.totalHours }}</div>
              <div class="stats-label">累计学时</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon study">
              <el-icon><Notebook /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statistics.furtherStudy }}</div>
              <div class="stats-label">在职进修</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 功能导航 -->
    <el-row :gutter="20" class="function-nav">
      <el-col :span="6">
        <el-card class="function-card" @click="navigateTo('/faculty-development/training-plans')">
          <div class="function-content">
            <div class="function-icon">
              <el-icon><School /></el-icon>
            </div>
            <div class="function-info">
              <h3>培训计划管理</h3>
              <p>制定和管理教师培训计划</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="function-card" @click="navigateTo('/faculty-development/training-records')">
          <div class="function-content">
            <div class="function-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="function-info">
              <h3>培训记录管理</h3>
              <p>查看和管理培训记录</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="function-card" @click="navigateTo('/faculty-development/further-study')">
          <div class="function-content">
            <div class="function-icon">
              <el-icon><Notebook /></el-icon>
            </div>
            <div class="function-info">
              <h3>在职学历进修</h3>
              <p>管理教师在职学历进修</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="function-card" @click="navigateTo('/faculty-development/social-practice')">
          <div class="function-content">
            <div class="function-icon">
              <el-icon><Suitcase /></el-icon>
            </div>
            <div class="function-info">
              <h3>社会实践管理</h3>
              <p>管理教师社会实践活动</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作 -->
    <el-card class="quick-actions">
      <template #header>
        <div class="card-header">
          <span>快速操作</span>
        </div>
      </template>
      <el-row :gutter="16">
        <el-col :span="6">
          <el-button type="primary" size="large" @click="createTrainingPlan" style="width: 100%">
            <el-icon><Plus /></el-icon>
            创建培训计划
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="success" size="large" @click="enrollTraining" style="width: 100%">
            <el-icon><UserFilled /></el-icon>
            培训报名
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="warning" size="large" @click="applyFurtherStudy" style="width: 100%">
            <el-icon><Notebook /></el-icon>
            申请进修
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="info" size="large" @click="viewStatistics" style="width: 100%">
            <el-icon><DataAnalysis /></el-icon>
            查看统计
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 近期培训 -->
    <el-card class="recent-trainings">
      <template #header>
        <div class="card-header">
          <span>近期培训</span>
          <el-button type="text" @click="viewAllTrainings">查看全部</el-button>
        </div>
      </template>
      <el-table :data="recentTrainings" style="width: 100%">
        <el-table-column prop="planName" label="培训名称" min-width="200"  />
        <el-table-column prop="instructorName" label="讲师" width="120"  />
        <el-table-column prop="trainingLocation" label="地点" width="150"  />
        <el-table-column prop="startTime" label="开始时间" width="160"  />
        <el-table-column prop="creditHours" label="学时" width="80" align="center"  />
        <el-table-column prop="currentParticipants" label="报名人数" width="100" align="center"  />
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status)"
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="viewTraining(row)">
              查看
            </el-button>
            <el-button
              v-if="canEnroll(row)"
              type="text"
              size="small"
              @click="enrollTraining(row)"
            >
              报名
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 个人发展画像 -->
    <el-card class="personal-profile">
      <template #header>
        <div class="card-header">
          <span>个人发展画像</span>
          <el-button type="text" @click="viewFullProfile">查看详情</el-button>
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="profile-chart">
            <h4>学时完成情况</h4>
            <StudyHoursChart 
              :data="studyHoursData"
              height="250px"
            />
          </div>
        </el-col>
        <el-col :span="12">
          <div class="profile-chart">
            <h4>培训类别分布</h4>
            <TrainingCategoryChart 
              :data="trainingCategoryData"
              height="250px"
            />
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'FacultyDevelopmentManagement'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Reading,
  School,
  UserFilled,
  Clock,
  Notebook,
  Document,
  Suitcase,
  Plus,
  DataAnalysis,
  MoreFilled
} from '@element-plus/icons-vue'
import { TrainingStatus, trainingPlanApi } from '@/api/facultyDevelopment'
import { useMobile } from '@/composables/useMobile'
import HrStudyHoursChart from '@/components/charts/HrStudyHoursChart.vue'
import HrTrainingCategoryChart from '@/components/charts/HrTrainingCategoryChart.vue'

const router = useRouter()

// 移动端适配
const {isMobile: _isMobile} =  useMobile()

// UI状态
const showMobileMenu 
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 24px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stats-icon.training {
  background: linear-gradient(135deg, #409eff, #67c23a);
}

.stats-icon.participants {
  background: linear-gradient(135deg, #e6a23c, #f56c6c);
}

.stats-icon.hours {
  background: linear-gradient(135deg, #909399, #606266);
}

.stats-icon.study {
  background: linear-gradient(135deg, #f56c6c, #e6a23c);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.function-nav {
  margin-bottom: 24px;
}

.function-card {
  cursor: pointer;
  transition: all 0.3s ease;
  height: 120px;
}

.function-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.function-content {
  display: flex;
  align-items: center;
  gap: 16px;
  height: 100%;
}

.function-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background: linear-gradient(135deg, #409eff, #67c23a);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.function-info h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.function-info p {
  margin: 0;
  font-size: 14px;
  color: #606266;
}

.quick-actions,
.recent-trainings,
.personal-profile {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.profile-chart {
  text-align: center;
}

.profile-chart h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.chart-placeholder {
  height: 200px;
  background: #f5f7fa;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 14px;
}
</style>
