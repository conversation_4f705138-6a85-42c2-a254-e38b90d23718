<template>
  <div class="social-practice-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon><Suitcase /></el-icon>
        社会实践管理
      </h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增实践
        </el-button>
        <el-button @click="handleImport">
          <el-icon><Upload /></el-icon>
          批量导入
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="员工姓名">
          <el-input
            v-model="searchForm.employeeName"
            placeholder="请输入员工姓名"
            clearable
            style="width: 150px"
            />
        </el-form-item>
        <el-form-item label="实践类型">
          <el-select
            v-model="searchForm.practiceType"
            placeholder="请选择实践类型"
            clearable
            style="width: 150px"
          >
            <el-option label="企业挂职" value="enterprise"  />
            <el-option label="社会调研" value="research"  />
            <el-option label="志愿服务" value="volunteer"  />
            <el-option label="技术服务" value="technical"  />
          </el-select>
        </el-form-item>
        <el-form-item label="实践状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="计划中" value="planning"  />
            <el-option label="进行中" value="ongoing"  />
            <el-option label="已完成" value="completed"  />
            <el-option label="已取消" value="cancelled"  />
          </el-select>
        </el-form-item>
        <el-form-item label="实践单位">
          <el-input
            v-model="searchForm.organization"
            placeholder="请输入实践单位"
            clearable
            style="width: 200px"
            />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><Suitcase /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.total }}</div>
              <div class="stat-label">总实践数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon ongoing">
              <el-icon><Timer /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.ongoing }}</div>
              <div class="stat-label">进行中</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon participants">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.participants }}</div>
              <div class="stat-label">参与人数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon days">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.days }}</div>
              <div class="stat-label">累计天数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 社会实践列表 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="practiceId" label="实践编号" width="120"  />
        <el-table-column prop="employeeName" label="员工姓名" width="100"  />
        <el-table-column prop="employeeId" label="员工工号" width="120"  />
        <el-table-column prop="department" label="所属部门" width="120"  />
        <el-table-column prop="practiceType" label="实践类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.practiceType)">
              {{ getTypeLabel(row.practiceType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="organization" label="实践单位" min-width="150"  />
        <el-table-column prop="position" label="实践岗位" min-width="120"  />
        <el-table-column prop="startDate" label="开始时间" width="120"  />
        <el-table-column prop="endDate" label="结束时间" width="120"  />
        <el-table-column prop="duration" label="实践天数" width="100">
          <template #default="{ row }">
            {{ row.duration }}天
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button type="text" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="text" size="small" @click="handleReport(row)">
              报告
            </el-button>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
            <el-dropdown @command="(command: unknown) => handleCommand(command, row)">
              <el-button type="text" size="small">
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="start" v-if="row.status === 'planning'">
                    开始实践
                  </el-dropdown-item>
                  <el-dropdown-item command="complete" v-if="row.status === 'ongoing'">
                    完成实践
                  </el-dropdown-item>
                  <el-dropdown-item command="evaluate">实践评价</el-dropdown-item>
                  <el-dropdown-item command="certificate">实践证明</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 社会实践详情/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="900px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="实践编号" prop="practiceId">
              <el-input v-model="formData.practiceId" :disabled="isView"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="员工姓名" prop="employeeName">
              <el-select v-model="formData.employeeName" :disabled="isView" style="width: 100%">
                <el-option label="张三" value="张三"  />
                <el-option label="李四" value="李四"  />
                <el-option label="王五" value="王五"  />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="实践类型" prop="practiceType">
              <el-select v-model="formData.practiceType" :disabled="isView" style="width: 100%">
                <el-option label="企业挂职" value="enterprise"  />
                <el-option label="社会调研" value="research"  />
                <el-option label="志愿服务" value="volunteer"  />
                <el-option label="技术服务" value="technical"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="实践单位" prop="organization">
              <el-input v-model="formData.organization" :disabled="isView"   />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="实践岗位" prop="position">
              <el-input v-model="formData.position" :disabled="isView"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人" prop="contact">
              <el-input v-model="formData.contact" :disabled="isView"   />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startDate">
              <el-date-picker
                v-model="formData.startDate"
                type="date"
                :disabled="isView"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endDate">
              <el-date-picker
                v-model="formData.endDate"
                type="date"
                :disabled="isView"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="实践天数" prop="duration">
              <el-input-number
                v-model="formData.duration"
                :min="1"
                :disabled="isView"
                style="width: 100%"
                />
              <span style="margin-left: 8px;">天</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="实践地点" prop="location">
              <el-input v-model="formData.location" :disabled="isView"   />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="实践目标" prop="objectives">
          <el-input
            v-model="formData.objectives"
            type="textarea"
            :rows="3"
            :disabled="isView"
            placeholder="请输入实践目标"
            />
        </el-form-item>
        <el-form-item label="实践内容" prop="content">
          <el-input
            v-model="formData.content"
            type="textarea"
            :rows="4"
            :disabled="isView"
            placeholder="请输入实践内容"
            />
        </el-form-item>
        <el-form-item label="预期成果" prop="expectedResults">
          <el-input
            v-model="formData.expectedResults"
            type="textarea"
            :rows="3"
            :disabled="isView"
            placeholder="请输入预期成果"
            />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="formData.notes"
            type="textarea"
            :rows="2"
            :disabled="isView"
            placeholder="请输入备注信息"
            />
        </el-form-item>
      </el-form>
      <template #footer v-if="!isView">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'SocialPracticeManagement'
})
 
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Suitcase,
  Plus,
  Upload,
  Download,
  Search,
  Refresh,
  Timer,
  User,
  Calendar,
  ArrowDown
} from '@element-plus/icons-vue'
import { socialPracticeApi } from '@/api/facultyDevelopment'

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isView = ref(false)

const searchForm = reactive({
  employeeName: '',
  practiceType: '',
  status: '',
  organization: ''
})

const stats = reactive({
  total: 89,
  ongoing: 23,
  participants: 156,
  days: 1245
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

const tableData = ref([
  {
    id: '1',
    practiceId: '*********',
    employeeName: '张三',
    employeeId: 'EMP001',
    department: '计算机学院',
    practiceType: 'enterprise',
    organization: '阿里巴巴集团',
    position: '技术专家',
    contact: '李经理',
    startDate: '2024-07-01',
    endDate: '2024-08-31',
    duration: 60,
    location: '杭州',
    status: 'ongoing',
    objectives: '深入了解企业技术发展趋势，提升实践教学能力',
    content: '参与企业技术项目开发，了解企业运营模式',
    expectedResults: '获得企业实践经验，改进教学内容',
    notes: '优秀教师企业实践计划'
  }
])

const formData = reactive({
  practiceId: '',
  employeeName: '',
  practiceType: '',
  organization: '',
  position: '',
  contact: '',
  startDate: '',
  endDate: '',
  duration: 1,
  location: '',
  objectives: '',
  content: '',
  expectedResults: '',
  notes: ''
})

const formRules = {
  practiceId: [{ required: true, message: '请输入实践编号', trigger: 'blur' }],
  employeeName: [{ required: true, message: '请选择员工', trigger: 'change' }],
  practiceType: [{ required: true, message: '请选择实践类型', trigger: 'change' }],
  organization: [{ required: true, message: '请输入实践单位', trigger: 'blur' }],
  position: [{ required: true, message: '请输入实践岗位', trigger: 'blur' }],
  startDate: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
  endDate: [{ required: true, message: '请选择结束时间', trigger: 'change' }]
}

// 方法
const handleSearch = () => {
  console.log('搜索:', searchForm)
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    employeeName: '',
    practiceType: '',
    status: '',
    organization: ''
  })
  loadData()
}

const handleAdd = () => {
  dialogTitle.value = '新增社会实践'
  isView.value = false
  resetForm()
  dialogVisible.value = true
}

   
const handleView = (row: unknown) => {
  dialogTitle.value = '查看社会实践'
  isView.value = true
  Object.assign(formData, row)
  dialogVisible.value = true
}

   
const handleEdit = (row: unknown) => {
  dialogTitle.value = '编辑社会实践'
  isView.value = false
  Object.assign(formData, row)
  dialogVisible.value = true
}

   
const handleReport = (row: unknown) => {
  ElMessage.info(`查看 ${row.employeeName} 的实践报告`)
}

   
const handleCommand = (command: string, row: unknown) => {
  switch (command) {
    case 'start':
      ElMessage.success(`${row.employeeName} 的社会实践已开始`)
      break
    case 'complete':
      ElMessage.success(`${row.employeeName} 的社会实践已完成`)
      break
    case 'evaluate':
      ElMessage.info(`查看 ${row.employeeName} 的实践评价`)
      break
    case 'certificate':
      ElMessage.info(`查看 ${row.employeeName} 的实践证明`)
      break
    case 'delete':
      ElMessageBox.confirm(`确定要删除 ${row.employeeName} 的社会实践记录吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ElMessage.success('删除成功')
      })
      break
  }
}

const handleImport = () => {
  ElMessage.info('批量导入功能开发中')
}

const handleExport = () => {
  ElMessage.info('导出数据功能开发中')
}

const handleSubmit = () => {
  ElMessage.success('保存成功')
  dialogVisible.value = false
}

const handleDialogClose = () => {
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    practiceId: '',
    employeeName: '',
    practiceType: '',
    organization: '',
    position: '',
    contact: '',
    startDate: '',
    endDate: '',
    duration: 1,
    location: '',
    objectives: '',
    content: '',
    expectedResults: '',
    notes: ''
  })
}

const loadData = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params = {
      page: pagination.currentPage - 1, // API使用0基索引
      size: pagination.pageSize,
      employeeId: searchForm.employeeName ? undefined : undefined, // 此处应该传员工ID
      practiceType: searchForm.practiceType,
      year: new Date().getFullYear() // 当前年份
    }
    
    // 过滤掉undefined的参数
    const filteredParams = Object.fromEntries(
      Object.entries(params).filter(([_, v]) => v !== undefined && v !== '')
    )
    
    // 获取社会实践记录列表
    const response = await socialPracticeApi.getPage(filteredParams)
    
    if (response.data && response.data.content) {
      // 转换数据格式
      tableData.value = response.data.content.map(item => ({
        id: item.id,
        practiceId: item.id, // 使用ID作为实践编号
        employeeName: item.employeeName,
        employeeId: item.employeeId,
        department: '-', // API可能不返回部门信息
        practiceType: item.practiceType,
        organization: item.organizationName,
        activity: item.practiceContent,
        location: item.location,
        startDate: item.startDate,
        endDate: item.endDate,
        duration: item.practiceHours,
        achievement: item.achievements || '实践成果',
        status: item.approvalStatus === '已审批' ? 'completed' : 
                item.approvalStatus === '待审批' ? 'pending' : 'ongoing',
        evaluation: item.evaluation || '',
        notes: ''
      }))
      
      pagination.total = response.data.totalElements
      
      // 更新统计信息
      stats.total = response.data.totalElements
      stats.ongoing = response.data.content.filter(item => 
        item.approvalStatus !== '已审批').length
      stats.participants = response.data.totalElements // 参与人数
      stats.days = response.data.content.reduce((sum, item) => 
        sum + (item.practiceHours / 8), 0) // 假设每天8小时
    }
  } catch (__error) {
    console.error('加载社会实践记录失败:', error)
    ElMessage.error('加载社会实践记录失败')
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadData()
}

const getTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    enterprise: 'primary',
    research: 'success',
    volunteer: 'warning',
    technical: 'info'
  }
  return colors[type] || ''
}

const getTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    enterprise: '企业挂职',
    research: '社会调研',
    volunteer: '志愿服务',
    technical: '技术服务'
  }
  return labels[type] || type
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    planning: 'info',
    ongoing: 'warning',
    completed: 'success',
    cancelled: 'danger'
  }
  return types[status] || ''
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    planning: '计划中',
    ongoing: '进行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return labels[status] || status
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.social-practice-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.ongoing {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.participants {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.days {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
