<template>
  <div class="training-plan-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon><Calendar /></el-icon>
        培训计划管理
      </h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增计划
        </el-button>
        <el-button @click="handleImport">
          <el-icon><Upload /></el-icon>
          批量导入
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出计划
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="计划名称">
          <el-input
            v-model="searchForm.planName"
            placeholder="请输入计划名称"
            clearable
            style="width: 200px"
            />
        </el-form-item>
        <el-form-item label="培训类型">
          <el-select
            v-model="searchForm.type"
            placeholder="请选择培训类型"
            clearable
            style="width: 150px"
          >
            <el-option label="技能培训" value="skill"  />
            <el-option label="管理培训" value="management"  />
            <el-option label="安全培训" value="safety"  />
            <el-option label="新员工培训" value="newcomer"  />
          </el-select>
        </el-form-item>
        <el-form-item label="计划状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="计划中" value="planning"  />
            <el-option label="进行中" value="ongoing"  />
            <el-option label="已完成" value="completed"  />
            <el-option label="已取消" value="cancelled"  />
          </el-select>
        </el-form-item>
        <el-form-item label="培训时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
           />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.total }}</div>
              <div class="stat-label">总计划数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon ongoing">
              <el-icon><Timer /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.ongoing }}</div>
              <div class="stat-label">进行中</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon participants">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.participants }}</div>
              <div class="stat-label">参训人数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon hours">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.hours }}</div>
              <div class="stat-label">总学时</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 培训计划列表 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="planCode" label="计划编号" width="120"  />
        <el-table-column prop="planName" label="计划名称" min-width="200"  />
        <el-table-column prop="type" label="培训类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.type)">
              {{ getTypeLabel(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="instructor" label="培训讲师" width="120"  />
        <el-table-column prop="startDate" label="开始时间" width="120"  />
        <el-table-column prop="endDate" label="结束时间" width="120"  />
        <el-table-column prop="duration" label="培训时长" width="100">
          <template #default="{ row }">
            {{ row.duration }}小时
          </template>
        </el-table-column>
        <el-table-column prop="maxParticipants" label="计划人数" width="100"  />
        <el-table-column prop="currentParticipants" label="已报名" width="100"  />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button type="text" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="text" size="small" @click="handleParticipants(row)">
              参训管理
            </el-button>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
            <el-dropdown @command="(command: unknown) => handleCommand(command, row)">
              <el-button type="text" size="small">
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="start" v-if="row.status === 'planning'">
                    开始培训
                  </el-dropdown-item>
                  <el-dropdown-item command="complete" v-if="row.status === 'ongoing'">
                    完成培训
                  </el-dropdown-item>
                  <el-dropdown-item command="cancel" v-if="row.status !== 'completed'">
                    取消培训
                  </el-dropdown-item>
                  <el-dropdown-item command="copy">复制计划</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 培训计划详情/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="计划编号" prop="planCode">
              <el-input v-model="formData.planCode" :disabled="isView"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计划名称" prop="planName">
              <el-input v-model="formData.planName" :disabled="isView"   />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="培训类型" prop="type">
              <el-select v-model="formData.type" :disabled="isView" style="width: 100%">
                <el-option label="技能培训" value="skill"  />
                <el-option label="管理培训" value="management"  />
                <el-option label="安全培训" value="safety"  />
                <el-option label="新员工培训" value="newcomer"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="培训讲师" prop="instructor">
              <el-input v-model="formData.instructor" :disabled="isView"   />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startDate">
              <el-date-picker
                v-model="formData.startDate"
                type="datetime"
                :disabled="isView"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endDate">
              <el-date-picker
                v-model="formData.endDate"
                type="datetime"
                :disabled="isView"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="培训时长" prop="duration">
              <el-input-number
                v-model="formData.duration"
                :min="1"
                :disabled="isView"
                style="width: 100%"
                />
              <span style="margin-left: 8px;">小时</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计划人数" prop="maxParticipants">
              <el-input-number
                v-model="formData.maxParticipants"
                :min="1"
                :disabled="isView"
                style="width: 100%"
                />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="培训地点" prop="location">
          <el-input v-model="formData.location" :disabled="isView"   />
        </el-form-item>
        <el-form-item label="培训内容" prop="content">
          <el-input
            v-model="formData.content"
            type="textarea"
            :rows="4"
            :disabled="isView"
            placeholder="请输入培训内容"
            />
        </el-form-item>
        <el-form-item label="培训目标" prop="objectives">
          <el-input
            v-model="formData.objectives"
            type="textarea"
            :rows="3"
            :disabled="isView"
            placeholder="请输入培训目标"
            />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="formData.notes"
            type="textarea"
            :rows="2"
            :disabled="isView"
            placeholder="请输入备注信息"
            />
        </el-form-item>
      </el-form>
      <template #footer v-if="!isView">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'TrainingPlanManagement'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { trainingPlanApi } from '@/api/facultyDevelopment'
import {
  Calendar,
  Plus,
  Upload,
  Download,
  Search,
  Refresh,
  Timer,
  User,
  Clock,
  ArrowDown
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isView = ref(false)

const searchForm = reactive({
  planName: '',
  type: '',
  status: '',
  dateRange: []
})

const stats = reactive({
  total: 45,
  ongoing: 12,
  participants: 320,
  hours: 1280
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

const tableData = ref([
  {
    id: '1',
    planCode: '*********',
    planName: 'Vue.js前端开发培训',
    type: 'skill',
    instructor: '张老师',
    startDate: '2024-07-01',
    endDate: '2024-07-05',
    duration: 40,
    maxParticipants: 30,
    currentParticipants: 25,
    status: 'planning',
    location: '培训中心A101',
    content: 'Vue.js基础知识、组件开发、状态管理等',
    objectives: '掌握Vue.js前端开发技能，能够独立开发前端应用',
    notes: '需要准备开发环境'
  }
])

const formData = reactive({
  planCode: '',
  planName: '',
  type: '',
  instructor: '',
  startDate: '',
  endDate: '',
  duration: 1,
  maxParticipants: 1,
  location: '',
  content: '',
  objectives: '',
  notes: ''
})

const formRules = {
  planCode: [{ required: true, message: '请输入计划编号', trigger: 'blur' }],
  planName: [{ required: true, message: '请输入计划名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择培训类型', trigger: 'change' }],
  instructor: [{ required: true, message: '请输入培训讲师', trigger: 'blur' }],
  startDate: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
  endDate: [{ required: true, message: '请选择结束时间', trigger: 'change' }]
}

// 方法
const handleSearch = () => {
  console.log('搜索:', searchForm)
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    planName: '',
    type: '',
    status: '',
    dateRange: []
  })
  loadData()
}

const handleAdd = () => {
  dialogTitle.value = '新增培训计划'
  isView.value = false
  resetForm()
  dialogVisible.value = true
}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleView = (row: unknown) => {
  dialogTitle.value = '查看培训计划'
  isView.value = true
  Object.assign(formData, row)
  dialogVisible.value = true
}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleEdit = (row: unknown) => {
  dialogTitle.value = '编辑培训计划'
  isView.value = false
  Object.assign(formData, row)
  dialogVisible.value = true
}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleParticipants = (row: unknown) => {
  ElMessage.info(`查看培训计划 ${row.planName} 的参训管理`)
}

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleCommand = (command: string, row: unknown) => {
  switch (command) {
    case 'start':
      ElMessage.success(`培训计划 ${row.planName} 已开始`)
      break
    case 'complete':
      ElMessage.success(`培训计划 ${row.planName} 已完成`)
      break
    case 'cancel':
      ElMessageBox.confirm(`确定要取消培训计划 ${row.planName} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ElMessage.success('培训计划已取消')
      })
      break
    case 'copy':
      ElMessage.success(`已复制培训计划 ${row.planName}`)
      break
    case 'delete':
      ElMessageBox.confirm(`确定要删除培训计划 ${row.planName} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ElMessage.success('删除成功')
      })
      break
  }
}

const handleImport = () => {
  ElMessage.info('批量导入功能开发中')
}

const handleExport = () => {
  ElMessage.info('导出计划功能开发中')
}

const handleSubmit = () => {
  ElMessage.success('保存成功')
  dialogVisible.value = false
}

const handleDialogClose = () => {
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    planCode: '',
    planName: '',
    type: '',
    instructor: '',
    startDate: '',
    endDate: '',
    duration: 1,
    maxParticipants: 1,
    location: '',
    content: '',
    objectives: '',
    notes: ''
  })
}

const loadData = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.currentPage - 1,
      size: pagination.pageSize,
      keyword: searchForm.planName || undefined,
      status: searchForm.status || undefined,
      category: searchForm.type || undefined
    }
    
    const {data: _data} =  await trainingPlanApi.getPage(params)
    if (data.success) {
      tableData.value 
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.ongoing {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.participants {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.hours {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
