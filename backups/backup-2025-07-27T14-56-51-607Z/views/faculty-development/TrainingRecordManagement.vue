<template>
  <div class="training-record-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon><Document /></el-icon>
        培训记录管理
      </h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增记录
        </el-button>
        <el-button @click="handleImport">
          <el-icon><Upload /></el-icon>
          批量导入
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出记录
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="员工姓名">
          <el-input
            v-model="searchForm.employeeName"
            placeholder="请输入员工姓名"
            clearable
            style="width: 150px"
            />
        </el-form-item>
        <el-form-item label="培训计划">
          <el-select
            v-model="searchForm.planId"
            placeholder="请选择培训计划"
            clearable
            style="width: 200px"
          >
            <el-option label="Vue.js前端开发培训" value="1"  />
            <el-option label="Java后端开发培训" value="2"  />
            <el-option label="项目管理培训" value="3"  />
          </el-select>
        </el-form-item>
        <el-form-item label="培训状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="进行中" value="ongoing"  />
            <el-option label="已完成" value="completed"  />
            <el-option label="未通过" value="failed"  />
            <el-option label="缺席" value="absent"  />
          </el-select>
        </el-form-item>
        <el-form-item label="培训时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
           />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.total }}</div>
              <div class="stat-label">总记录数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon completed">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.completed }}</div>
              <div class="stat-label">已完成</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon ongoing">
              <el-icon><Timer /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.ongoing }}</div>
              <div class="stat-label">进行中</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon hours">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.hours }}</div>
              <div class="stat-label">累计学时</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 培训记录列表 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="recordId" label="记录编号" width="120"  />
        <el-table-column prop="employeeName" label="员工姓名" width="100"  />
        <el-table-column prop="employeeId" label="员工工号" width="120"  />
        <el-table-column prop="department" label="所属部门" width="120"  />
        <el-table-column prop="planName" label="培训计划" min-width="180"  />
        <el-table-column prop="trainingType" label="培训类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.trainingType)">
              {{ getTypeLabel(row.trainingType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="startDate" label="开始时间" width="120"  />
        <el-table-column prop="endDate" label="结束时间" width="120"  />
        <el-table-column prop="duration" label="学时" width="80">
          <template #default="{ row }">
            {{ row.duration }}h
          </template>
        </el-table-column>
        <el-table-column prop="score" label="成绩" width="80">
          <template #default="{ row }">
            <span :class="getScoreClass(row.score)">{{ row.score || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button type="text" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="text" size="small" @click="handleCertificate(row)">
              证书
            </el-button>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
            <el-dropdown @command="(command: unknown) => handleCommand(command, row)">
              <el-button type="text" size="small">
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="complete" v-if="row.status === 'ongoing'">
                    标记完成
                  </el-dropdown-item>
                  <el-dropdown-item command="score" v-if="row.status === 'completed'">
                    录入成绩
                  </el-dropdown-item>
                  <el-dropdown-item command="feedback">培训反馈</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 培训记录详情/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="记录编号" prop="recordId">
              <el-input v-model="formData.recordId" :disabled="isView"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="员工姓名" prop="employeeName">
              <el-select v-model="formData.employeeName" :disabled="isView" style="width: 100%">
                <el-option label="张三" value="张三"  />
                <el-option label="李四" value="李四"  />
                <el-option label="王五" value="王五"  />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="培训计划" prop="planName">
              <el-select v-model="formData.planName" :disabled="isView" style="width: 100%">
                <el-option label="Vue.js前端开发培训" value="Vue.js前端开发培训"  />
                <el-option label="Java后端开发培训" value="Java后端开发培训"  />
                <el-option label="项目管理培训" value="项目管理培训"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="培训类型" prop="trainingType">
              <el-select v-model="formData.trainingType" :disabled="isView" style="width: 100%">
                <el-option label="技能培训" value="skill"  />
                <el-option label="管理培训" value="management"  />
                <el-option label="安全培训" value="safety"  />
                <el-option label="新员工培训" value="newcomer"  />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startDate">
              <el-date-picker
                v-model="formData.startDate"
                type="datetime"
                :disabled="isView"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endDate">
              <el-date-picker
                v-model="formData.endDate"
                type="datetime"
                :disabled="isView"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="培训学时" prop="duration">
              <el-input-number
                v-model="formData.duration"
                :min="0"
                :disabled="isView"
                style="width: 100%"
                />
              <span style="margin-left: 8px;">小时</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="培训成绩" prop="score">
              <el-input-number
                v-model="formData.score"
                :min="0"
                :max="100"
                :disabled="isView"
                style="width: 100%"
                />
              <span style="margin-left: 8px;">分</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="培训内容" prop="content">
          <el-input
            v-model="formData.content"
            type="textarea"
            :rows="3"
            :disabled="isView"
            placeholder="请输入培训内容"
            />
        </el-form-item>
        <el-form-item label="培训效果" prop="effect">
          <el-input
            v-model="formData.effect"
            type="textarea"
            :rows="3"
            :disabled="isView"
            placeholder="请输入培训效果评价"
            />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="formData.notes"
            type="textarea"
            :rows="2"
            :disabled="isView"
            placeholder="请输入备注信息"
            />
        </el-form-item>
      </el-form>
      <template #footer v-if="!isView">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'TrainingRecordManagement'
})
 
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Document,
  Plus,
  Upload,
  Download,
  Search,
  Refresh,
  CircleCheck,
  Timer,
  Clock,
  ArrowDown
} from '@element-plus/icons-vue'
import { trainingRecordApi } from '@/api/facultyDevelopment'

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isView = ref(false)

const searchForm = reactive({
  employeeName: '',
  planId: '',
  status: '',
  dateRange: []
})

const stats = reactive({
  total: 256,
  completed: 180,
  ongoing: 45,
  hours: 2048
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

const tableData = ref([
  {
    id: '1',
    recordId: 'TR2024001',
    employeeName: '张三',
    employeeId: 'EMP001',
    department: '计算机学院',
    planName: 'Vue.js前端开发培训',
    trainingType: 'skill',
    startDate: '2024-07-01',
    endDate: '2024-07-05',
    duration: 40,
    score: 85,
    status: 'completed',
    content: 'Vue.js基础知识、组件开发、状态管理等',
    effect: '掌握了Vue.js开发技能，能够独立开发前端应用',
    notes: '表现优秀，积极参与讨论'
  }
])

const formData = reactive({
  recordId: '',
  employeeName: '',
  planName: '',
  trainingType: '',
  startDate: '',
  endDate: '',
  duration: 0,
  score: 0,
  content: '',
  effect: '',
  notes: ''
})

const formRules = {
  recordId: [{ required: true, message: '请输入记录编号', trigger: 'blur' }],
  employeeName: [{ required: true, message: '请选择员工', trigger: 'change' }],
  planName: [{ required: true, message: '请选择培训计划', trigger: 'change' }],
  trainingType: [{ required: true, message: '请选择培训类型', trigger: 'change' }],
  startDate: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
  endDate: [{ required: true, message: '请选择结束时间', trigger: 'change' }]
}

// 方法
const handleSearch = () => {
  console.log('搜索:', searchForm)
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    employeeName: '',
    planId: '',
    status: '',
    dateRange: []
  })
  loadData()
}

const handleAdd = () => {
  dialogTitle.value = '新增培训记录'
  isView.value = false
  resetForm()
  dialogVisible.value = true
}

   
const handleView = (row: unknown) => {
  dialogTitle.value = '查看培训记录'
  isView.value = true
  Object.assign(formData, row)
  dialogVisible.value = true
}

   
const handleEdit = (row: unknown) => {
  dialogTitle.value = '编辑培训记录'
  isView.value = false
  Object.assign(formData, row)
  dialogVisible.value = true
}

   
const handleCertificate = (row: unknown) => {
  ElMessage.info(`查看 ${row.employeeName} 的培训证书`)
}

   
const handleCommand = (command: string, row: unknown) => {
  switch (command) {
    case 'complete':
      ElMessage.success(`${row.employeeName} 的培训已标记为完成`)
      break
    case 'score':
      ElMessage.info(`为 ${row.employeeName} 录入培训成绩`)
      break
    case 'feedback':
      ElMessage.info(`查看 ${row.employeeName} 的培训反馈`)
      break
    case 'delete':
      ElMessageBox.confirm(`确定要删除 ${row.employeeName} 的培训记录吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ElMessage.success('删除成功')
      })
      break
  }
}

const handleImport = () => {
  ElMessage.info('批量导入功能开发中')
}

const handleExport = () => {
  ElMessage.info('导出记录功能开发中')
}

const handleSubmit = () => {
  ElMessage.success('保存成功')
  dialogVisible.value = false
}

const handleDialogClose = () => {
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    recordId: '',
    employeeName: '',
    planName: '',
    trainingType: '',
    startDate: '',
    endDate: '',
    duration: 0,
    score: 0,
    content: '',
    effect: '',
    notes: ''
  })
}

const loadData = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params = {
      page: pagination.currentPage - 1, // API使用0基索引
      size: pagination.pageSize,
      employeeId: searchForm.employeeName ? undefined : undefined, // 此处应该传员工ID
      trainingType: searchForm.status === 'completed' ? undefined : undefined, // 状态映射需要调整
      year: searchForm.dateRange && searchForm.dateRange.length > 0 ? 
        new Date(searchForm.dateRange[0]).getFullYear() : undefined
    }
    
    // 过滤掉undefined的参数
    const filteredParams = Object.fromEntries(
      Object.entries(params).filter(([_, v]) => v !== undefined)
    )
    
    // 获取培训记录列表
    const response = await trainingRecordApi.getPage(filteredParams)
    
    if (response.data && response.data.content) {
      // 转换数据格式
      tableData.value = response.data.content.map(item => ({
        id: item.id,
        recordId: item.id, // 使用ID作为记录编号
        employeeName: item.employeeName,
        employeeId: item.employeeId,
        department: '-', // API可能不返回部门信息
        planName: item.planName,
        trainingType: item.trainingType,
        startDate: item.startTime.split(' ')[0], // 取日期部分
        endDate: item.endTime.split(' ')[0],
        duration: item.creditHours,
        score: item.score,
        status: item.result === '合格' ? 'completed' : item.result === '不合格' ? 'failed' : 'ongoing',
        content: item.evaluation || '培训内容',
        effect: item.evaluation || '培训效果',
        notes: item.certificateNumber ? `证书编号: ${item.certificateNumber}` : ''
      }))
      
      pagination.total = response.data.totalElements
      
      // 更新统计信息
      stats.total = response.data.totalElements
      stats.completed = response.data.content.filter(item => item.result === '合格').length
      stats.ongoing = response.data.content.filter(item => !item.result).length
      stats.hours = response.data.content.reduce((sum, item) => sum + (item.creditHours || 0), 0)
    }
  } catch (__error) {
    console.error('加载培训记录失败:', error)
    ElMessage.error('加载培训记录失败')
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadData()
}

const getTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    skill: 'primary',
    management: 'success',
    safety: 'warning',
    newcomer: 'info'
  }
  return colors[type] || ''
}

const getTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    skill: '技能培训',
    management: '管理培训',
    safety: '安全培训',
    newcomer: '新员工培训'
  }
  return labels[type] || type
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    ongoing: 'warning',
    completed: 'success',
    failed: 'danger',
    absent: 'info'
  }
  return types[status] || ''
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    ongoing: '进行中',
    completed: '已完成',
    failed: '未通过',
    absent: '缺席'
  }
  return labels[status] || status
}

const getScoreClass = (score: number) => {
  if (score >= 90) return 'score-excellent'
  if (score >= 80) return 'score-good'
  if (score >= 60) return 'score-pass'
  return 'score-fail'
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.training-record-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-icon.ongoing {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.hours {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.score-excellent {
  color: #67c23a;
  font-weight: 600;
}

.score-good {
  color: #409eff;
  font-weight: 600;
}

.score-pass {
  color: #e6a23c;
  font-weight: 600;
}

.score-fail {
  color: #f56c6c;
  font-weight: 600;
}
</style>
