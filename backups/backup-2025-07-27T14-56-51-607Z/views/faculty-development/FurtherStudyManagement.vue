<template>
  <div class="further-study-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon><Reading /></el-icon>
        在职学历进修管理
      </h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增申请
        </el-button>
        <el-button @click="handleImport">
          <el-icon><Upload /></el-icon>
          批量导入
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="员工姓名">
          <el-input
            v-model="searchForm.employeeName"
            placeholder="请输入员工姓名"
            clearable
            style="width: 150px"
            />
        </el-form-item>
        <el-form-item label="进修类型">
          <el-select
            v-model="searchForm.studyType"
            placeholder="请选择进修类型"
            clearable
            style="width: 150px"
          >
            <el-option label="学历提升" value="degree"  />
            <el-option label="专业进修" value="professional"  />
            <el-option label="访问学者" value="visiting"  />
            <el-option label="博士后" value="postdoc"  />
          </el-select>
        </el-form-item>
        <el-form-item label="申请状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="申请中" value="applying"  />
            <el-option label="已批准" value="approved"  />
            <el-option label="进修中" value="studying"  />
            <el-option label="已完成" value="completed"  />
            <el-option label="已拒绝" value="rejected"  />
          </el-select>
        </el-form-item>
        <el-form-item label="进修院校">
          <el-input
            v-model="searchForm.university"
            placeholder="请输入院校名称"
            clearable
            style="width: 200px"
            />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><Reading /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.total }}</div>
              <div class="stat-label">总申请数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon studying">
              <el-icon><Timer /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.studying }}</div>
              <div class="stat-label">进修中</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon completed">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.completed }}</div>
              <div class="stat-label">已完成</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon funding">
              <el-icon><Money /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.funding }}</div>
              <div class="stat-label">资助金额(万)</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 进修申请列表 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="applicationId" label="申请编号" width="120"  />
        <el-table-column prop="employeeName" label="员工姓名" width="100"  />
        <el-table-column prop="employeeId" label="员工工号" width="120"  />
        <el-table-column prop="department" label="所属部门" width="120"  />
        <el-table-column prop="studyType" label="进修类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.studyType)">
              {{ getTypeLabel(row.studyType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="university" label="进修院校" min-width="150"  />
        <el-table-column prop="major" label="专业方向" min-width="120"  />
        <el-table-column prop="startDate" label="开始时间" width="120"  />
        <el-table-column prop="endDate" label="结束时间" width="120"  />
        <el-table-column prop="duration" label="进修期限" width="100">
          <template #default="{ row }">
            {{ row.duration }}个月
          </template>
        </el-table-column>
        <el-table-column prop="funding" label="资助金额" width="100">
          <template #default="{ row }">
            ¥{{ row.funding?.toLocaleString() || 0 }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button type="text" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="text" size="small" @click="handleProgress(row)">
              进度
            </el-button>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
            <el-dropdown @command="(command: unknown) => handleCommand(command, row)">
              <el-button type="text" size="small">
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="approve" v-if="row.status === 'applying'">
                    批准申请
                  </el-dropdown-item>
                  <el-dropdown-item command="reject" v-if="row.status === 'applying'">
                    拒绝申请
                  </el-dropdown-item>
                  <el-dropdown-item command="complete" v-if="row.status === 'studying'">
                    标记完成
                  </el-dropdown-item>
                  <el-dropdown-item command="certificate">查看证书</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 进修申请详情/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="900px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="申请编号" prop="applicationId">
              <el-input v-model="formData.applicationId" :disabled="isView"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="员工姓名" prop="employeeName">
              <el-select v-model="formData.employeeName" :disabled="isView" style="width: 100%">
                <el-option label="张三" value="张三"  />
                <el-option label="李四" value="李四"  />
                <el-option label="王五" value="王五"  />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="进修类型" prop="studyType">
              <el-select v-model="formData.studyType" :disabled="isView" style="width: 100%">
                <el-option label="学历提升" value="degree"  />
                <el-option label="专业进修" value="professional"  />
                <el-option label="访问学者" value="visiting"  />
                <el-option label="博士后" value="postdoc"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="进修院校" prop="university">
              <el-input v-model="formData.university" :disabled="isView"   />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="专业方向" prop="major">
              <el-input v-model="formData.major" :disabled="isView"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="导师姓名" prop="supervisor">
              <el-input v-model="formData.supervisor" :disabled="isView"   />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startDate">
              <el-date-picker
                v-model="formData.startDate"
                type="date"
                :disabled="isView"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endDate">
              <el-date-picker
                v-model="formData.endDate"
                type="date"
                :disabled="isView"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="进修期限" prop="duration">
              <el-input-number
                v-model="formData.duration"
                :min="1"
                :disabled="isView"
                style="width: 100%"
                />
              <span style="margin-left: 8px;">个月</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资助金额" prop="funding">
              <el-input-number
                v-model="formData.funding"
                :min="0"
                :disabled="isView"
                style="width: 100%"
                />
              <span style="margin-left: 8px;">元</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="进修目标" prop="objectives">
          <el-input
            v-model="formData.objectives"
            type="textarea"
            :rows="3"
            :disabled="isView"
            placeholder="请输入进修目标"
            />
        </el-form-item>
        <el-form-item label="进修计划" prop="plan">
          <el-input
            v-model="formData.plan"
            type="textarea"
            :rows="4"
            :disabled="isView"
            placeholder="请输入详细的进修计划"
            />
        </el-form-item>
        <el-form-item label="预期成果" prop="expectedResults">
          <el-input
            v-model="formData.expectedResults"
            type="textarea"
            :rows="3"
            :disabled="isView"
            placeholder="请输入预期成果"
            />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="formData.notes"
            type="textarea"
            :rows="2"
            :disabled="isView"
            placeholder="请输入备注信息"
            />
        </el-form-item>
      </el-form>
      <template #footer v-if="!isView">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'FurtherStudyManagement'
})
 
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Reading,
  Plus,
  Upload,
  Download,
  Search,
  Refresh,
  Timer,
  CircleCheck,
  Money,
  ArrowDown
} from '@element-plus/icons-vue'
import { furtherStudyApi } from '@/api/facultyDevelopment'

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isView = ref(false)

const searchForm = reactive({
  employeeName: '',
  studyType: '',
  status: '',
  university: ''
})

const stats = reactive({
  total: 68,
  studying: 15,
  completed: 42,
  funding: 285.6
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

const tableData = ref([
  {
    id: '1',
    applicationId: '*********',
    employeeName: '张三',
    employeeId: 'EMP001',
    department: '计算机学院',
    studyType: 'degree',
    university: '清华大学',
    major: '计算机科学与技术',
    supervisor: '李教授',
    startDate: '2024-09-01',
    endDate: '2027-06-30',
    duration: 36,
    funding: 150000,
    status: 'studying',
    objectives: '攻读计算机科学与技术博士学位，提升学术水平',
    plan: '第一年完成课程学习，第二年开始研究工作，第三年完成论文',
    expectedResults: '获得博士学位，发表高质量学术论文',
    notes: '优秀青年教师培养计划'
  }
])

const formData = reactive({
  applicationId: '',
  employeeName: '',
  studyType: '',
  university: '',
  major: '',
  supervisor: '',
  startDate: '',
  endDate: '',
  duration: 1,
  funding: 0,
  objectives: '',
  plan: '',
  expectedResults: '',
  notes: ''
})

const formRules = {
  applicationId: [{ required: true, message: '请输入申请编号', trigger: 'blur' }],
  employeeName: [{ required: true, message: '请选择员工', trigger: 'change' }],
  studyType: [{ required: true, message: '请选择进修类型', trigger: 'change' }],
  university: [{ required: true, message: '请输入进修院校', trigger: 'blur' }],
  major: [{ required: true, message: '请输入专业方向', trigger: 'blur' }],
  startDate: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
  endDate: [{ required: true, message: '请选择结束时间', trigger: 'change' }]
}

// 方法
const handleSearch = () => {
  console.log('搜索:', searchForm)
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    employeeName: '',
    studyType: '',
    status: '',
    university: ''
  })
  loadData()
}

const handleAdd = () => {
  dialogTitle.value = '新增进修申请'
  isView.value = false
  resetForm()
  dialogVisible.value = true
}

   
const handleView = (row: unknown) => {
  dialogTitle.value = '查看进修申请'
  isView.value = true
  Object.assign(formData, row)
  dialogVisible.value = true
}

   
const handleEdit = (row: unknown) => {
  dialogTitle.value = '编辑进修申请'
  isView.value = false
  Object.assign(formData, row)
  dialogVisible.value = true
}

   
const handleProgress = (row: unknown) => {
  ElMessage.info(`查看 ${row.employeeName} 的进修进度`)
}

   
const handleCommand = (command: string, row: unknown) => {
  switch (command) {
    case 'approve':
      ElMessage.success(`${row.employeeName} 的进修申请已批准`)
      break
    case 'reject':
      ElMessageBox.confirm(`确定要拒绝 ${row.employeeName} 的进修申请吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ElMessage.success('申请已拒绝')
      })
      break
    case 'complete':
      ElMessage.success(`${row.employeeName} 的进修已标记为完成`)
      break
    case 'certificate':
      ElMessage.info(`查看 ${row.employeeName} 的进修证书`)
      break
    case 'delete':
      ElMessageBox.confirm(`确定要删除 ${row.employeeName} 的进修申请吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ElMessage.success('删除成功')
      })
      break
  }
}

const handleImport = () => {
  ElMessage.info('批量导入功能开发中')
}

const handleExport = () => {
  ElMessage.info('导出数据功能开发中')
}

const handleSubmit = () => {
  ElMessage.success('保存成功')
  dialogVisible.value = false
}

const handleDialogClose = () => {
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    applicationId: '',
    employeeName: '',
    studyType: '',
    university: '',
    major: '',
    supervisor: '',
    startDate: '',
    endDate: '',
    duration: 1,
    funding: 0,
    objectives: '',
    plan: '',
    expectedResults: '',
    notes: ''
  })
}

const loadData = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params = {
      page: pagination.currentPage - 1, // API使用0基索引
      size: pagination.pageSize,
      employeeId: searchForm.employeeName ? undefined : undefined, // 此处应该传员工ID
      status: searchForm.status,
      studyType: searchForm.studyType
    }
    
    // 过滤掉undefined的参数
    const filteredParams = Object.fromEntries(
      Object.entries(params).filter(([_, v]) => v !== undefined && v !== '')
    )
    
    // 获取进修记录列表
    const response = await furtherStudyApi.getPage(filteredParams)
    
    if (response.data && response.data.content) {
      // 转换数据格式
      tableData.value = response.data.content.map(item => ({
        id: item.id,
        studyId: item.id, // 使用ID作为进修编号
        employeeName: item.employeeName,
        employeeId: item.employeeId,
        department: '-', // API可能不返回部门信息
        studyType: item.studyType,
        university: item.institutionName,
        major: item.major,
        degree: item.degree,
        startDate: item.startDate,
        endDate: item.endDate || '-',
        status: item.status,
        funding: item.subsidyAmount || 0,
        progress: item.status === '已完成' ? 100 : 
                 item.status === '进修中' ? 50 : 0,
        graduationDate: item.graduationDate || '',
        certificateNumber: item.certificateNumber || '',
        notes: item.approvalComment || ''
      }))
      
      pagination.total = response.data.totalElements
      
      // 更新统计信息
      stats.total = response.data.totalElements
      stats.studying = response.data.content.filter(item => item.status === '进修中').length
      stats.completed = response.data.content.filter(item => item.status === '已完成').length
      stats.funding = response.data.content.reduce((sum, item) => 
        sum + (item.subsidyAmount || 0), 0) / 10000 // 转换为万元
    }
  } catch (__error) {
    console.error('加载进修记录失败:', error)
    ElMessage.error('加载进修记录失败')
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadData()
}

const getTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    degree: 'primary',
    professional: 'success',
    visiting: 'warning',
    postdoc: 'info'
  }
  return colors[type] || ''
}

const getTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    degree: '学历提升',
    professional: '专业进修',
    visiting: '访问学者',
    postdoc: '博士后'
  }
  return labels[type] || type
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    applying: 'info',
    approved: 'success',
    studying: 'warning',
    completed: 'success',
    rejected: 'danger'
  }
  return types[status] || ''
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    applying: '申请中',
    approved: '已批准',
    studying: '进修中',
    completed: '已完成',
    rejected: '已拒绝'
  }
  return labels[status] || status
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.further-study-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.studying {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-icon.funding {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
