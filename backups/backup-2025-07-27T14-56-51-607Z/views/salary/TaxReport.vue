<template>
  <div class="tax-report">
    <!-- 查询条件 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" inline>
        <el-form-item label="申报期间">
          <el-date-picker
            v-model="searchForm.period"
            type="month"
            placeholder="选择月份"
            value-format="YYYY-MM"
           />
        </el-form-item>
        
        <el-form-item label="申报类型">
          <el-select v-model="searchForm.reportType" placeholder="请选择">
            <el-option label="月度申报" value="monthly"  />
            <el-option label="年度汇算" value="annual"  />
            <el-option label="专项申报" value="special"  />
          </el-select>
        </el-form-item>

        <el-form-item label="部门">
          <el-tree-select
            v-model="searchForm.departmentId"
            :data="departmentTree"
            :props="{ label: 'name', value: 'id' }"
            placeholder="全部部门"
            clearable
           />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleGenerate">
            <el-icon><Document /></el-icon>
            生成申报表
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 申报概览 -->
    <el-card v-if="showReport" class="overview-card" shadow="never">
      <template #header>
        <span>申报概览</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="overview-item">
            <div class="label">申报人数</div>
            <div class="value">{{ overview.totalCount }}</div>
            <div class="sub">需申报 {{ overview.taxableCount }} 人</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="overview-item">
            <div class="label">应发工资总额</div>
            <div class="value">¥{{ formatMoney(overview.totalIncome) }}</div>
            <div class="sub">月均 ¥{{ formatMoney(overview.avgIncome) }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="overview-item">
            <div class="label">应纳税所得额</div>
            <div class="value">¥{{ formatMoney(overview.taxableIncome) }}</div>
            <div class="sub">免税额 ¥{{ formatMoney(overview.exemptAmount) }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="overview-item">
            <div class="label">应纳税额</div>
            <div class="value primary">¥{{ formatMoney(overview.totalTax) }}</div>
            <div class="sub">已预缴 ¥{{ formatMoney(overview.prepaidTax) }}</div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 申报表内容 -->
    <el-card v-if="showReport" shadow="never">
      <template #header>
        <div class="report-header">
          <div>
            <span class="title">个人所得税申报表</span>
            <span class="period">（{{ searchForm.period }}）</span>
          </div>
          <div>
            <el-button @click="handlePrint">
              <el-icon><Printer /></el-icon>
              打印
            </el-button>
            <el-button type="primary" @click="handleExport">
              <el-icon><Download /></el-icon>
              导出申报文件
            </el-button>
            <el-button type="success" @click="handleSubmit">
              <el-icon><Upload /></el-icon>
              提交申报
            </el-button>
          </div>
        </div>
      </template>

      <!-- 申报明细 -->
      <el-tabs v-model="activeTab">
        <el-tab-pane label="工资薪金所得" name="salary">
          <el-table :data="salaryData" stripe show-summary :summary-method="getSalarySummary">
            <el-table-column type="index" label="序号" width="60"  />
            <el-table-column prop="employeeNo" label="工号" width="100"  />
            <el-table-column prop="name" label="姓名" width="100"  />
            <el-table-column prop="idNumber" label="身份证号" width="180"  />
            <el-table-column prop="income" label="本期收入" align="right">
              <template #default="{ row }">
                {{ formatMoney(row.income) }}
              </template>
            </el-table-column>
            <el-table-column prop="exemption" label="免税收入" align="right">
              <template #default="{ row }">
                {{ formatMoney(row.exemption) }}
              </template>
            </el-table-column>
            <el-table-column prop="insurance" label="三险一金" align="right">
              <template #default="{ row }">
                {{ formatMoney(row.insurance) }}
              </template>
            </el-table-column>
            <el-table-column prop="specialDeduction" label="专项扣除" align="right">
              <template #default="{ row }">
                {{ formatMoney(row.specialDeduction) }}
              </template>
            </el-table-column>
            <el-table-column prop="otherDeduction" label="其他扣除" align="right">
              <template #default="{ row }">
                {{ formatMoney(row.otherDeduction) }}
              </template>
            </el-table-column>
            <el-table-column prop="taxableIncome" label="应纳税所得额" align="right" width="120">
              <template #default="{ row }">
                <strong>{{ formatMoney(row.taxableIncome) }}</strong>
              </template>
            </el-table-column>
            <el-table-column prop="taxRate" label="税率" align="center" width="80">
              <template #default="{ row }">
                {{ row.taxRate }}%
              </template>
            </el-table-column>
            <el-table-column prop="tax" label="应纳税额" align="right" width="100">
              <template #default="{ row }">
                <span class="tax-amount">{{ formatMoney(row.tax) }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <el-tab-pane label="专项附加扣除" name="special">
          <el-table :data="specialDeductionData" stripe>
            <el-table-column prop="employeeName" label="员工姓名" width="100"  />
            <el-table-column prop="childEducation" label="子女教育" align="right">
              <template #default="{ row }">
                {{ formatMoney(row.childEducation) }}
              </template>
            </el-table-column>
            <el-table-column prop="continuingEducation" label="继续教育" align="right">
              <template #default="{ row }">
                {{ formatMoney(row.continuingEducation) }}
              </template>
            </el-table-column>
            <el-table-column prop="housing" label="住房贷款利息" align="right">
              <template #default="{ row }">
                {{ formatMoney(row.housing) }}
              </template>
            </el-table-column>
            <el-table-column prop="rent" label="住房租金" align="right">
              <template #default="{ row }">
                {{ formatMoney(row.rent) }}
              </template>
            </el-table-column>
            <el-table-column prop="elderCare" label="赡养老人" align="right">
              <template #default="{ row }">
                {{ formatMoney(row.elderCare) }}
              </template>
            </el-table-column>
            <el-table-column prop="total" label="合计" align="right" width="100">
              <template #default="{ row }">
                <strong>{{ formatMoney(row.total) }}</strong>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <el-tab-pane label="税率表" name="rate">
          <div class="rate-table-container">
            <h4>个人所得税税率表（综合所得适用）</h4>
            <el-table :data="taxRateTable" stripe>
              <el-table-column prop="level" label="级数" width="80" align="center"  />
              <el-table-column prop="range" label="应纳税所得额"  />
              <el-table-column prop="rate" label="税率(%)" width="100" align="center"  />
              <el-table-column prop="deduction" label="速算扣除数" width="120" align="right">
                <template #default="{ row }">
                  {{ formatMoney(row.deduction) }}
                </template>
              </el-table-column>
            </el-table>
            <div class="rate-note">
              <p>* 应纳税所得额 = 工资薪金所得 - 免税收入 - 减除费用 - 专项扣除 - 专项附加扣除 - 依法确定的其他扣除</p>
              <p>* 减除费用：5000元/月</p>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>

      <!-- 申报信息 -->
      <div class="report-footer">
        <el-row>
          <el-col :span="8">
            <div class="footer-item">
              <span class="label">纳税人识别号：</span>
              <span>{{ companyInfo.taxId }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="footer-item">
              <span class="label">扣缴义务人：</span>
              <span>{{ companyInfo.name }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="footer-item">
              <span class="label">申报日期：</span>
              <span>{{ reportDate }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 历史申报记录 -->
    <el-card shadow="never" style="margin-top: 20px">
      <template #header>
        <span>历史申报记录</span>
      </template>
      <el-table :data="historyData" stripe>
        <el-table-column prop="period" label="申报期间" width="100"  />
        <el-table-column prop="type" label="申报类型" width="100">
          <template #default="{ row }">
            <el-tag size="small">{{ getReportTypeText(row.type) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="submitTime" label="申报时间" width="160"  />
        <el-table-column prop="submitter" label="申报人" width="100"  />
        <el-table-column prop="employeeCount" label="申报人数" width="100" align="center"  />
        <el-table-column prop="taxAmount" label="申报税额" align="right">
          <template #default="{ row }">
            ¥{{ formatMoney(row.taxAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleView(row)">查看</el-button>
            <el-button link type="primary" @click="handleDownloadRecord(row)">下载</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document, Printer, Download, Upload } from '@element-plus/icons-vue'

// 搜索表单
const searchForm = reactive({
  period: new Date().toISOString().slice(0, 7),
  reportType: 'monthly',
  departmentId: ''
})

// 部门树
const departmentTree = ref([
  { id: '1', name: 'HrHr计算机系' },
  { id: '2', name: '数学系' },
  { id: '3', name: '物理系' }
])

// 显示报表
const showReport = ref(false)
const activeTab = ref('salary')

// 概览数据
const overview = reactive({
  totalCount: 0,
  taxableCount: 0,
  totalIncome: 0,
  avgIncome: 0,
  taxableIncome: 0,
  exemptAmount: 0,
  totalTax: 0,
  prepaidTax: 0
})

// 工资数据
const salaryData = ref<unknown[]>([])

// 专项扣除数据
const specialDeductionData = ref<unknown[]>([])

// 税率表
const taxRateTable = ref([
  { level: 1, range: '不超过36,000元', rate: 3, deduction: 0 },
  { level: 2, range: '超过36,000元至144,000元', rate: 10, deduction: 2520 },
  { level: 3, range: '超过144,000元至300,000元', rate: 20, deduction: 16920 },
  { level: 4, range: '超过300,000元至420,000元', rate: 25, deduction: 31920 },
  { level: 5, range: '超过420,000元至660,000元', rate: 30, deduction: 52920 },
  { level: 6, range: '超过660,000元至960,000元', rate: 35, deduction: 85920 },
  { level: 7, range: '超过960,000元', rate: 45, deduction: 181920 }
])

// 公司信息
const companyInfo = ref({
  name: '杭州科技学院',
  taxId: '91330106MA2B1234567'
})

// 申报日期
const reportDate = computed(() => new Date().toLocaleDateString())

// 历史记录
const historyData = ref([
  {
    period: '2024-12',
    type: 'monthly',
    submitTime: '2025-01-10 10:30:00',
    submitter: '张三',
    employeeCount: 580,
    taxAmount: 285000,
    status: 'success'
  },
  {
    period: '2024-11',
    type: 'monthly',
    submitTime: '2024-12-10 09:45:00',
    submitter: '张三',
    employeeCount: 575,
    taxAmount: 276000,
    status: 'success'
  }
])

// 生成申报表
const handleGenerate = async () => {
  ElMessage.info('正在生成个税申报表...')
  
  // 模拟数据生成
  setTimeout(() => {
    // 生成模拟数据
    const mockSalaryData = []
    let totalIncome = 0
    let totalTax = 0
    let taxableCount = 0
    
    for (let i = 1; i <= 30; i++) {
      const income = 8000 + Math.random() * 22000
      const exemption = 0
      const insurance = income * 0.225 // 22.5%的三险一金
      const specialDeduction = Math.random() > 0.5 ? 1000 + Math.random() * 3000 : 0
      const otherDeduction = 0
      const monthlyExempt = 5000 // 月度免征额
      
      const taxableIncome = Math.max(income - exemption - insurance - specialDeduction - otherDeduction - monthlyExempt, 0)
      
      // 计算税率和税额
      let taxRate = 0
      let tax = 0
      if (taxableIncome > 0) {
        taxableCount++
        if (taxableIncome <= 3000) {
          taxRate = 3
          tax = taxableIncome * 0.03
        } else if (taxableIncome <= 12000) {
          taxRate = 10
          tax = taxableIncome * 0.1 - 210
        } else if (taxableIncome <= 25000) {
          taxRate = 20
          tax = taxableIncome * 0.2 - 1410
        } else if (taxableIncome <= 35000) {
          taxRate = 25
          tax = taxableIncome * 0.25 - 2660
        } else if (taxableIncome <= 55000) {
          taxRate = 30
          tax = taxableIncome * 0.3 - 4410
        } else if (taxableIncome <= 80000) {
          taxRate = 35
          tax = taxableIncome * 0.35 - 7160
        } else {
          taxRate = 45
          tax = taxableIncome * 0.45 - 15160
        }
      }
      
      mockSalaryData.push({
        id: i,
        employeeNo: `T2024${String(i).padStart(3, '0')}`,
        name: `员工${i}`,
        idNumber: `33010619900101${String(1000 + i).slice(-4)}`,
        income,
        exemption,
        insurance,
        specialDeduction,
        otherDeduction,
        taxableIncome,
        taxRate,
        tax
      })
      
      totalIncome += income
      totalTax += tax
    }
    
    salaryData.value = mockSalaryData
    
    // 生成专项扣除数据
    const mockSpecialData = mockSalaryData.filter(item => item.specialDeduction > 0).map(item => ({
      employeeName: item.name,
      childEducation: Math.random() > 0.7 ? 1000 : 0,
      continuingEducation: Math.random() > 0.8 ? 400 : 0,
      housing: Math.random() > 0.6 ? 1000 : 0,
      rent: Math.random() > 0.4 ? 1500 : 0,
      elderCare: Math.random() > 0.5 ? 2000 : 0,
      total: 0
    }))
    
    mockSpecialData.forEach(item => {
      item.total = item.childEducation + item.continuingEducation + item.housing + item.rent + item.elderCare
    })
    
    specialDeductionData.value = mockSpecialData
    
    // 更新概览
    overview.totalCount = mockSalaryData.length
    overview.taxableCount = taxableCount
    overview.totalIncome = totalIncome
    overview.avgIncome = totalIncome / mockSalaryData.length
    overview.taxableIncome = mockSalaryData.reduce((sum, item) => sum + item.taxableIncome, 0)
    overview.exemptAmount = mockSalaryData.length * 5000
    overview.totalTax = totalTax
    overview.prepaidTax = totalTax * 0.9 // 假设已预缴90%
    
    showReport.value = true
    ElMessage.success('个税申报表生成成功')
  }, 1500)
}

// 重置
const handleReset = () => {
  searchForm.period = new Date().toISOString().slice(0, 7)
  searchForm.reportType = 'monthly'
  searchForm.departmentId = ''
  showReport.value = false
}

// 打印
const handlePrint = () => {
  window.print()
}

// 导出
const handleExport = () => {
  ElMessage.success('正在导出个税申报文件...')
}

// 提交申报
const handleSubmit = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要提交${searchForm.period}的个税申报吗？\n申报人数：${overview.taxableCount}人\n应纳税额：¥${formatMoney(overview.totalTax)}`,
      '提交确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }
    )
    
    ElMessage.success('个税申报提交成功')
  } catch {
    // 用户取消
  }
}

// 获取汇总
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const getSalarySummary = (param: unknown) => {
  const {columns: _columns, data: _data} =  param
  const sums: string[] 
  }

  .overview-card {
    margin-bottom: 20px;

    .overview-item {
      text-align: center;
      padding: 20px 0;

      .label {
        font-size: 14px;
        color: #909399;
        margin-bottom: 10px;
      }

      .value {
        font-size: 24px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 5px;

        &.primary {
          color: #409eff;
        }
      }

      .sub {
        font-size: 12px;
        color: #909399;
      }
    }
  }

  .report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      font-size: 18px;
      font-weight: bold;
    }

    .period {
      color: #606266;
      margin-left: 10px;
    }
  }

  .tax-amount {
    color: #f56c6c;
    font-weight: bold;
  }

  .rate-table-container {
    padding: 20px;

    h4 {
      margin: 0 0 20px 0;
      color: #303133;
    }

    .rate-note {
      margin-top: 20px;
      padding: 15px;
      background-color: #f5f7fa;
      border-radius: 4px;

      p {
        margin: 5px 0;
        font-size: 12px;
        color: #606266;
      }
    }
  }

  .report-footer {
    margin-top: 30px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;

    .footer-item {
      .label {
        color: #606266;
        margin-right: 10px;
      }
    }
  }
}

// 打印样式
@media print {
  .search-card,
  .el-card__header button,
  .el-tabs__nav {
    display: none !important;
  }

  .el-table {
    font-size: 10px;
  }
}
</style>