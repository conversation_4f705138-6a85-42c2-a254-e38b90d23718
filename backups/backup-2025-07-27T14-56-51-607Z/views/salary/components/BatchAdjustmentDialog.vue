<template>
  <el-dialog
    v-model="dialogVisible"
    title="批量调薪"
    width="700px"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
    >
      <!-- 调薪员工 -->
      <el-form-item label="调薪员工">
        <div class="selected-info">
          已选择 <span class="count">{{ selectedEmployees.length }}</span> 名员工
        </div>
        <el-table
          :data="selectedEmployees"
          max-height="200"
          size="small"
          style="margin-top: 10px"
        >
          <el-table-column prop="applicantName" label="姓名" width="100"  />
          <el-table-column prop="employeeNo" label="工号" width="100"  />
          <el-table-column prop="department" label="部门"  />
          <el-table-column prop="currentSalary" label="当前薪资" align="right">
            <template #default="{ row }">
              ¥{{ row.currentSalary.toLocaleString() }}
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>

      <!-- 调薪方案 -->
      <el-divider content-position="left">调薪方案</el-divider>
      
      <el-form-item label="调薪类型" prop="adjustType">
        <el-select v-model="formData.adjustType" placeholder="请选择">
          <el-option label="年度调薪" value="annual"  />
          <el-option label="特殊调薪" value="special"  />
        </el-select>
      </el-form-item>

      <el-form-item label="调整方式" prop="adjustMethod">
        <el-radio-group v-model="formData.adjustMethod">
          <el-radio label="unified">统一调整</el-radio>
          <el-radio label="tiered">分档调整</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 统一调整 -->
      <template v-if="formData.adjustMethod === 'unified'">
        <el-form-item label="调整类型">
          <el-radio-group v-model="formData.unifiedType">
            <el-radio label="amount">固定金额</el-radio>
            <el-radio label="percentage">固定比例</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item 
          :label="formData.unifiedType === 'amount' ? '调整金额' : '调整比例'" 
          prop="unifiedValue"
        >
          <el-input-number
            v-model="formData.unifiedValue"
            :min="0"
            :max="formData.unifiedType === 'amount' ? 10000 : 50"
            :step="formData.unifiedType === 'amount' ? 100 : 1"
            />
          <span v-if="formData.unifiedType === 'percentage'" style="margin-left: 5px">%</span>
        </el-form-item>
      </template>

      <!-- 分档调整 -->
      <template v-if="formData.adjustMethod === 'tiered'">
        <el-form-item label="调整档位">
          <el-button type="primary" size="small" @click="addTier">添加档位</el-button>
        </el-form-item>
        
        <div v-for="(tier, index) in formData.tiers" :key="index" class="tier-item">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="薪资范围">
                <el-input v-model="tier.minSalary" placeholder="最低薪资">
                  <template #prepend>¥</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label-width="20px" label="~">
                <el-input v-model="tier.maxSalary" placeholder="最高薪资">
                  <template #prepend>¥</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label-width="0">
                <el-input v-model="tier.adjustValue" placeholder="调整比例">
                  <template #append>%</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="2">
              <el-button type="danger" icon="Delete" circle @click="removeTier(index)"   />
            </el-col>
          </el-row>
        </div>
      </template>

      <el-form-item label="生效日期" prop="effectiveDate">
        <el-date-picker
          v-model="formData.effectiveDate"
          type="date"
          placeholder="选择生效日期"
          value-format="YYYY-MM-DD"
         />
      </el-form-item>

      <el-form-item label="调薪说明" prop="reason">
        <el-input
          v-model="formData.reason"
          type="textarea"
          :rows="3"
          placeholder="请输入批量调薪说明"
          />
      </el-form-item>

      <!-- 预览结果 -->
      <el-divider content-position="left">调整预览</el-divider>
      <div class="preview-info">
        <el-row>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">平均调整金额：</span>
              <span class="value">¥{{ avgAdjustAmount }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">平均调整幅度：</span>
              <span class="value">{{ avgAdjustPercentage }}%</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handlePreview">预览结果</el-button>
        <el-button type="primary" @click="handleSubmit">确认调整</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'BatchAdjustmentDialog'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import salaryAnalysisApi from '@/api/salary/analysis'

// Props & Emits
interface Props {
  modelValue: boolean
   
  selectedEmployees: unknown[]
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue', 'success'])

// 对话框
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 表单
const formRef = ref<FormInstance>()
const formData = ref({
  adjustType: '',
  adjustMethod: 'unified',
  unifiedType: 'percentage',
  unifiedValue: 0,
  tiers: [
    { minSalary: '', maxSalary: '', adjustValue: '' }
  ],
  effectiveDate: '',
  reason: ''
})

// 表单规则
const rules: FormRules = {
  adjustType: [{ required: true, message: '请选择调薪类型', trigger: 'change' }],
  effectiveDate: [{ required: true, message: '请选择生效日期', trigger: 'change' }],
  reason: [{ required: true, message: '请输入调薪说明', trigger: 'blur' }]
}

// 计算结果缓存
const calculationResult = ref<any[]>([])

// 计算平均调整
const avgAdjustAmount = computed(() => {
  // 根据调整方案计算
  if (formData.value.adjustMethod === 'unified' && formData.value.unifiedType === 'amount') {
    return formData.value.unifiedValue
  }
  // 如果有计算结果，使用实际计算值
  if (calculationResult.value.length > 0) {
    const totalAdjust = calculationResult.value.reduce((sum, item) => sum + item.adjustAmount, 0)
    return Math.round(totalAdjust / calculationResult.value.length)
  }
  return 0
})

const avgAdjustPercentage = computed(() => {
  if (formData.value.adjustMethod === 'unified' && formData.value.unifiedType === 'percentage') {
    return formData.value.unifiedValue
  }
  // 如果有计算结果，使用实际计算值
  if (calculationResult.value.length > 0) {
    const totalPercentage = calculationResult.value.reduce((sum, item) => sum + item.adjustPercentage, 0)
    return Math.round(totalPercentage / calculationResult.value.length * 100) / 100
  }
  return 0
})

// 监听调整参数变化，重新计算
watch(
  () => ({
    method: formData.value.adjustMethod,
    employees: formData.value.employees,
    config: {
      unified: {
        type: formData.value.unifiedType,
        value: formData.value.unifiedValue
      },
      tiers: formData.value.tiers,
      performance: formData.value.performanceConfig
    }
  }),
  async (newVal) => {
    if (newVal.employees.length > 0 && newVal.method) {
      await calculateAdjustment()
    }
  },
  { deep: true }
)

// 计算薪资调整
const calculateAdjustment = async () => {
  try {
    const params = {
      employeeIds: formData.value.employees,
      adjustMethod: formData.value.adjustMethod,
      adjustConfig: {
        unified: {
          type: formData.value.unifiedType,
          value: formData.value.unifiedValue
        },
        tiers: formData.value.tiers,
        performance: formData.value.performanceConfig
      }
    }
    
    const result = await salaryAnalysisApi.calculateBatchAdjustment(params)
    calculationResult.value = result
  } catch (__error) {
    console.error('计算薪资调整失败:', error)
    // 如果API调用失败，使用本地计算
    calculationResult.value = []
  }
}

// 添加档位
const addTier = () => {
  formData.value.tiers.push({ minSalary: '', maxSalary: '', adjustValue: '' })
}

// 删除档位
const removeTier = (index: number) => {
  if (formData.value.tiers.length > 1) {
    formData.value.tiers.splice(index, 1)
  }
}

// 预览结果
const handlePreview = () => {
  ElMessage.info('预览功能开发中...')
}

// 提交
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    await ElMessageBox.confirm(
      `确定要对选中的 ${props.selectedEmployees.length} 名员工进行批量调薪吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('批量调薪申请已提交')
    emit('success')
  } catch {
    // 用户取消或验证失败
  }
}
</script>

<style lang="scss" scoped>
.selected-info {
  color: #606266;
  
  .count {
    color: #409eff;
    font-weight: bold;
    font-size: 16px;
  }
}

.tier-item {
  margin-bottom: 10px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.preview-info {
  padding: 15px;
  background-color: #ecf5ff;
  border-radius: 4px;
  
  .info-item {
    margin-bottom: 10px;
    
    .label {
      color: #606266;
    }
    
    .value {
      color: #409eff;
      font-weight: bold;
      font-size: 16px;
    }
  }
}

.dialog-footer {
  display: flex;
  gap: 10px;
}
</style>