<template>
  <el-dialog
    :model-value="modelValue"
    :title="title"
    width="800px"
    @update:model-value="$emit('update:modelValue', $event)"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="结构编码" prop="code">
            <el-input 
              v-model="formData.code" 
              placeholder="请输入结构编码"
              :disabled="!!data?.id"
              />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结构名称" prop="name">
            <el-input 
              v-model="formData.name" 
              placeholder="请输入结构名称"
              />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="适用范围" prop="scope">
            <el-select 
              v-model="formData.scope" 
              placeholder="请选择适用范围"
              style="width: 100%"
            >
              <el-option label="全体员工" value="all"  />
              <el-option label="教师岗位" value="teacher"  />
              <el-option label="行政岗位" value="admin"  />
              <el-option label="后勤岗位" value="support"  />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="版本号" prop="version">
            <el-input 
              v-model="formData.version" 
              placeholder="如：1.0"
              />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="薪资项目" prop="items">
        <div class="salary-items-container">
          <el-button 
            type="primary" 
            size="small" 
            @click="handleAddItem"
          >
            <el-icon><Plus /></el-icon>
            添加项目
          </el-button>
          
          <div class="items-list">
            <div 
              v-for="(item, index) in formData.items" 
              :key="item.tempId || item.id"
              class="salary-item"
            >
              <el-card shadow="never">
                <div class="item-header">
                  <span class="item-index">{{ index + 1 }}</span>
                  <el-button 
                    link 
                    type="danger" 
                    size="small"
                    @click="handleRemoveItem(index)"
                  >
                    删除
                  </el-button>
                </div>
                
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item 
                      :label="`项目名称`" 
                      :prop="`items.${index}.name`"
                      :rules="itemRules.name"
                    >
                      <el-input 
                        v-model="item.name" 
                        placeholder="如：基本工资"
                        />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item 
                      :label="`项目编码`" 
                      :prop="`items.${index}.code`"
                      :rules="itemRules.code"
                    >
                      <el-input 
                        v-model="item.code" 
                        placeholder="如：BASE_SALARY"
                        />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item 
                      :label="`项目类型`" 
                      :prop="`items.${index}.type`"
                      :rules="itemRules.type"
                    >
                      <el-select 
                        v-model="item.type" 
                        placeholder="请选择"
                        style="width: 100%"
                      >
                        <el-option label="固定项" value="fixed"  />
                        <el-option label="计算项" value="calculated"  />
                        <el-option label="扣除项" value="deduction"  />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20" v-if="item.type === 'calculated'">
                  <el-col :span="24">
                    <el-form-item 
                      label="计算公式" 
                      :prop="`items.${index}.formula`"
                    >
                      <el-input 
                        v-model="item.formula" 
                        type="textarea"
                        :rows="2"
                        placeholder="如：BASE_SALARY * 0.2 + ALLOWANCE"
                        />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="是否显示">
                      <el-switch 
                        v-model="item.visible"
                        active-text="显示"
                        inactive-text="隐藏"
                       />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="排序号">
                      <el-input-number 
                        v-model="item.sort" 
                        :min="1"
                        style="width: 100%"
                        />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-card>
            </div>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="备注说明" prop="remark">
        <el-input 
          v-model="formData.remark" 
          type="textarea"
          :rows="3"
          placeholder="请输入备注说明"
          />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="loading">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
 
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import type { SalaryStructure, SalaryItem } from '@/types/salary'

interface Props {
  modelValue: boolean
  title: string
  data?: SalaryStructure | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', data: SalaryStructure): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const loading = ref(false)

// 表单数据
const formData = reactive<SalaryStructure>({
  id: '',
  code: '',
  name: '',
  scope: '',
  version: '1.0',
  items: [],
  remark: '',
  status: 'active'
})

// 表单验证规则
const rules: FormRules = {
  code: [
    { required: true, message: '请输入结构编码', trigger: 'blur' },
    { pattern: /^[A-Z_]+$/, message: '编码只能包含大写字母和下划线', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入结构名称', trigger: 'blur' }
  ],
  scope: [
    { required: true, message: '请选择适用范围', trigger: 'change' }
  ],
  version: [
    { required: true, message: '请输入版本号', trigger: 'blur' }
  ],
  items: [
    { required: true, message: '至少添加一个薪资项目', trigger: 'change' }
  ]
}

// 薪资项目验证规则
const itemRules = {
  name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入项目编码', trigger: 'blur' },
    { pattern: /^[A-Z_]+$/, message: '编码只能包含大写字母和下划线', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择项目类型', trigger: 'change' }
  ]
}

// 监听数据变化
watch(() => props.data, (newData) => {
  if (newData) {
    Object.assign(formData, {
      ...newData,
      items: newData.items?.map(item => ({ ...item })) || []
    })
  } else {
    // 重置表单
    formData.id = ''
    formData.code = ''
    formData.name = ''
    formData.scope = ''
    formData.version = '1.0'
    formData.items = []
    formData.remark = ''
    formData.status = 'active'
  }
}, { immediate: true })

// 添加薪资项目
const handleAddItem = () => {
  const newItem: SalaryItem = {
    tempId: Date.now().toString(),
    name: '',
    code: '',
    type: 'fixed',
    visible: true,
    sort: formData.items.length + 1,
    formula: ''
  }
  formData.items.push(newItem)
}

// 删除薪资项目
const handleRemoveItem = (index: number) => {
  formData.items.splice(index, 1)
  // 重新排序
  formData.items.forEach((item, i) => {
    item.sort = i + 1
  })
}

// 关闭对话框
const handleClose = () => {
  formRef.value?.resetFields()
  emit('update:modelValue', false)
}

// 确认提交
const handleConfirm = async () => {
  try {
    await formRef.value?.validate()
    
    // 验证薪资项目
    if (formData.items.length === 0) {
      ElMessage.error('请至少添加一个薪资项目')
      return
    }
    
    // 验证计算项的公式
    const calculatedItems = formData.items.filter(item => item.type === 'calculated')
    for (const item of calculatedItems) {
      if (!item.formula) {
        ElMessage.error(`计算项"${item.name}"的公式不能为空`)
        return
      }
    }
    
    loading.value = true
    // 提交数据
    emit('confirm', { ...formData })
    
    setTimeout(() => {
      loading.value = false
      handleClose()
    }, 1000)
  } catch (__error) {
    console.error('表单验证失败', error)
  }
}
</script>

<style lang="scss" scoped>
.salary-items-container {
  width: 100%;

  .items-list {
    margin-top: 10px;
    max-height: 400px;
    overflow-y: auto;
  }

  .salary-item {
    margin-bottom: 10px;

    .item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      .item-index {
        font-weight: bold;
        color: #409eff;
      }
    }

    :deep(.el-card__body) {
      padding: 15px;
    }

    :deep(.el-form-item) {
      margin-bottom: 15px;
    }
  }
}
</style>