 
/* eslint-disable @typescript-eslint/no-unused-vars */
/**
 * SalaryCharts 组件测试
 * @description 自动生成的组件测试文件
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { mount, flushPromises } from '@vue/test-utils'
import SalaryCharts from '../SalaryCharts.vue'
describe('SalaryCharts', () => {
  let wrapper

  beforeEach(() => {
    wrapper = null
  })

  afterEach(() => {
    wrapper?.unmount()
  })

  it('应该正确渲染', async () => {
    const wrapper = mount(SalaryCharts)
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.hr-salary-charts').exists()).toBe(true)
  })

  it('应该匹配快照', () => {
    const wrapper = mount(SalaryCharts)
    expect(wrapper.html()).toMatchSnapshot()
  })
})
