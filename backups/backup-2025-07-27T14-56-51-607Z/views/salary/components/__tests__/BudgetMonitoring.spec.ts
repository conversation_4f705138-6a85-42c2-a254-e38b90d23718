 
/* eslint-disable @typescript-eslint/no-unused-vars */
/**
 * BudgetMonitoring 组件测试
 * @description 自动生成的组件测试文件
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { mount, flushPromises } from '@vue/test-utils'
import BudgetMonitoring from '../BudgetMonitoring.vue'
describe('BudgetMonitoring', () => {
  let wrapper

  beforeEach(() => {
    wrapper = null
  })

  afterEach(() => {
    wrapper?.unmount()
  })

  it('应该正确渲染', async () => {
    const wrapper = mount(BudgetMonitoring)
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.hr-budget-monitoring').exists()).toBe(true)
  })

  it('应该匹配快照', () => {
    const wrapper = mount(BudgetMonitoring)
    expect(wrapper.html()).toMatchSnapshot()
  })
})
