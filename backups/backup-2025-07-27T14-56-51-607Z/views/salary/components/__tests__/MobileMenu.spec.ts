 
/* eslint-disable @typescript-eslint/no-unused-vars */
/**
 * MobileMenu 组件测试
 * @description 自动生成的组件测试文件
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { mount, flushPromises } from '@vue/test-utils'
import HrMobileMenu from '../HrMobileMenu.vue'
describe('MobileMenu', () => {
  let wrapper

  beforeEach(() => {
    wrapper = null
  })

  afterEach(() => {
    wrapper?.unmount()
  })

  it('应该正确渲染', async () => {
    const wrapper = mount(MobileMenu)
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.hr-mobile-menu').exists()).toBe(true)
  })

  it('应该匹配快照', () => {
    const wrapper = mount(MobileMenu)
    expect(wrapper.html()).toMatchSnapshot()
  })
})
