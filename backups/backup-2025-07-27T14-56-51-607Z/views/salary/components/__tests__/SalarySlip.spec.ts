 
/* eslint-disable @typescript-eslint/no-unused-vars */
/**
 * SalarySlip 组件测试
 * @description 自动生成的组件测试文件
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { mount, flushPromises } from '@vue/test-utils'
import SalarySlip from '../SalarySlip.vue'
describe('SalarySlip', () => {
  let wrapper

  beforeEach(() => {
    wrapper = null
  })

  afterEach(() => {
    wrapper?.unmount()
  })

  it('应该正确渲染', async () => {
    const wrapper = mount(SalarySlip)
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.hr-salary-slip').exists()).toBe(true)
  })

  it('应该匹配快照', () => {
    const wrapper = mount(SalarySlip)
    expect(wrapper.html()).toMatchSnapshot()
  })
})
