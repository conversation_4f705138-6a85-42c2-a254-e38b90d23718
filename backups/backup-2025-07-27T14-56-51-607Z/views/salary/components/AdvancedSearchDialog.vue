<template>
  <el-dialog
    :model-value="modelValue"
    title="高级查询"
    width="800px"
    @update:model-value="$emit('update:modelValue', $event)"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="formData" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="员工信息">
            <el-input 
              v-model="formData.keyword" 
              placeholder="姓名/工号"
              clearable
              />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="部门">
            <el-cascader
              v-model="formData.departmentId"
              :options="departmentTree"
              :props="{ 
                checkStrictly: true, 
                value: 'id', 
                label: 'name',
                children: 'children'
              }"
              placeholder="请选择部门"
              clearable
              style="width: 100%"
             />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="岗位">
            <el-select v-model="formData.positionId" placeholder="请选择岗位" clearable style="width: 100%">
              <el-option label="教授" value="POS001"  />
              <el-option label="副教授" value="POS002"  />
              <el-option label="讲师" value="POS003"  />
              <el-option label="助教" value="POS004"  />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="级别">
            <el-select v-model="formData.levelId" placeholder="请选择级别" clearable style="width: 100%">
              <el-option label="初级" value="L1"  />
              <el-option label="中级" value="L2"  />
              <el-option label="高级" value="L3"  />
              <el-option label="资深" value="L4"  />
              <el-option label="专家" value="L5"  />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="薪资月份">
            <el-date-picker
              v-model="formData.monthRange"
              type="monthrange"
              range-separator="至"
              start-placeholder="开始月份"
              end-placeholder="结束月份"
              value-format="YYYY-MM"
              style="width: 100%"
             />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发放状态">
            <el-select v-model="formData.paymentStatus" placeholder="请选择" clearable style="width: 100%">
              <el-option label="已发放" value="paid"  />
              <el-option label="待发放" value="pending"  />
              <el-option label="发放失败" value="failed"  />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="实发工资范围">
            <div style="display: flex; align-items: center;">
              <el-input-number
                v-model="formData.salaryMin"
                :precision="2"
                :min="0"
                placeholder="最低"
                style="width: 45%"
                />
              <span style="margin: 0 10px">-</span>
              <el-input-number
                v-model="formData.salaryMax"
                :precision="2"
                :min="0"
                placeholder="最高"
                style="width: 45%"
                />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="基本工资范围">
            <div style="display: flex; align-items: center;">
              <el-input-number
                v-model="formData.baseSalaryMin"
                :precision="2"
                :min="0"
                placeholder="最低"
                style="width: 45%"
                />
              <span style="margin: 0 10px">-</span>
              <el-input-number
                v-model="formData.baseSalaryMax"
                :precision="2"
                :min="0"
                placeholder="最高"
                style="width: 45%"
                />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="个税范围">
            <div style="display: flex; align-items: center;">
              <el-input-number
                v-model="formData.taxMin"
                :precision="2"
                :min="0"
                placeholder="最低"
                style="width: 45%"
                />
              <span style="margin: 0 10px">-</span>
              <el-input-number
                v-model="formData.taxMax"
                :precision="2"
                :min="0"
                placeholder="最高"
                style="width: 45%"
                />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="包含项目">
            <el-checkbox-group v-model="formData.includeItems">
              <el-checkbox label="bonus">有奖金</el-checkbox>
              <el-checkbox label="overtime">有加班费</el-checkbox>
              <el-checkbox label="subsidy">有补贴</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <el-button @click="handleReset">重置</el-button>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSearch">查询</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive } from 'vue'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
   
  (e: 'search', params: unknown): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref()

const formData = reactive({
  keyword: '',
  departmentId: [],
  positionId: '',
  levelId: '',
  monthRange: [],
  paymentStatus: '',
  salaryMin: undefined,
  salaryMax: undefined,
  baseSalaryMin: undefined,
  baseSalaryMax: undefined,
  taxMin: undefined,
  taxMax: undefined,
  includeItems: []
})

// 部门树数据
const departmentTree = ref([
  {
    id: 'DEPT001',
    name: 'HrHr教学部',
    children: [
      { id: 'DEPT001-1', name: '计算机系' },
      { id: 'DEPT001-2', name: '数学系' },
      { id: 'DEPT001-3', name: '物理系' }
    ]
  },
  {
    id: 'DEPT002',
    name: '行政部',
    children: [
      { id: 'DEPT002-1', name: '人事处' },
      { id: 'DEPT002-2', name: '财务处' },
      { id: 'DEPT002-3', name: '教务处' }
    ]
  }
])

// 查询
const handleSearch = () => {
  emit('search', { ...formData })
  handleClose()
}

// 重置
const handleReset = () => {
  formRef.value?.resetFields()
  Object.keys(formData).forEach(key => {
    if (Array.isArray(formData[key as keyof typeof formData])) {
      (formData as unknown)[key] = []
    } else {
      (formData as unknown)[key] = undefined
    }
  })
}

// 关闭对话框
const handleClose = () => {
  emit('update:modelValue', false)
}
</script>

<style lang="scss" scoped>
</style>