<template>
  <el-dialog
    v-model="visible"
    :title="title"
    width="90%"
    :fullscreen="isMobile"
    append-to-body
  >
    <div ref="chartRef" :style="{ height: chartHeight }"></div>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'ChartFullscreenDialog'
})
 
import { ref, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

interface Props {
  modelValue: boolean
  title?: string
   
  chartOption?: unknown
  isMobile?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  title: '图表详情',
  isMobile: false
})

const emit = defineEmits<Emits>()

// 控制对话框显示
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 图表引用
const chartRef = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

// 图表高度
const chartHeight = computed(() => props.isMobile ? '400px' : '500px')

// 初始化图表
const initChart = () => {
  if (!chartRef.value || !props.chartOption) return
  
  chartInstance = echarts.init(chartRef.value)
  chartInstance.setOption(props.chartOption)
}

// 更新图表
const updateChart = () => {
  if (!chartInstance || !props.chartOption) return
  chartInstance.setOption(props.chartOption)
}

// 响应式调整
const handleResize = () => {
  chartInstance?.resize()
}

// 监听对话框显示
watch(visible, (newVal) => {
  if (newVal) {
    nextTick(() => {
      initChart()
    })
  } else {
    chartInstance?.dispose()
    chartInstance = null
  }
})

// 监听图表配置变化
watch(() => props.chartOption, () => {
  if (visible.value) {
    updateChart()
  }
}, { deep: true })

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  chartInstance?.dispose()
})
</script>

<style scoped>
/* 对话框内容样式可以根据需要调整 */
</style>