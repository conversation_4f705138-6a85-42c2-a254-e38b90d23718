<template>
  <el-card class="monitoring-card" shadow="never">
    <template #header>
      <div class="card-header">
        <span>预算监控</span>
        <el-button size="small" type="primary" link @click="$emit('view-details')">
          查看详情
        </el-button>
      </div>
    </template>
    <div class="budget-monitoring">
      <div
        v-for="item in budgetData"
        :key="item.organizationId"
        class="budget-item"
        :class="getBudgetItemClass(item.alertLevel)"
      >
        <div class="budget-info">
          <div class="budget-org">{{ item.organizationName }}</div>
          <div class="budget-progress">
            <el-progress
              :percentage="item.utilizationRate"
              :status="getBudgetProgressStatus(item.alertLevel)"
              :stroke-width="8"
             />
          </div>
          <div class="budget-details">
            <span>已用：{{ formatCurrency(item.actualAmount) }}</span>
            <span>预算：{{ formatCurrency(item.budgetAmount) }}</span>
          </div>
        </div>
        <div class="budget-alert">
          <el-tag :type="getBudgetAlertTagType(item.alertLevel)" size="small">
            {{ item.alertLevelName }}
          </el-tag>
        </div>
      </div>
      <div v-if="budgetData.length === 0" class="empty-state">
        暂无预算监控数据
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">

defineOptions({
  name: 'BudgetMonitoring'
})
 
import type { BudgetMonitoring } from '@/api/salary'
import { BUDGET_ALERT_CLASSES, BUDGET_PROGRESS_STATUS } from '../types'

interface Props {
  budgetData: BudgetMonitoring[]
}

interface Emits {
  (e: 'view-details'): void
}

defineProps<Props>()
defineEmits<Emits>()

// 格式化货币
const formatCurrency = (amount: number) => {
  if (!amount) return '¥0'
  return `¥${amount.toLocaleString()}`
}

// 获取预算项目样式类
const getBudgetItemClass = (alertLevel: string) => {
  return BUDGET_ALERT_CLASSES[alertLevel as keyof typeof BUDGET_ALERT_CLASSES] || BUDGET_ALERT_CLASSES.NORMAL
}

// 获取预算进度状态
const getBudgetProgressStatus = (alertLevel: string) => {
  return BUDGET_PROGRESS_STATUS[alertLevel as keyof typeof BUDGET_PROGRESS_STATUS] || BUDGET_PROGRESS_STATUS.NORMAL
}

// 获取预算警告标签类型
const getBudgetAlertTagType = (alertLevel: string) => {
  const typeMap = {
    WARNING: 'warning',
    CRITICAL: 'danger',
    NORMAL: 'success'
  }
  return typeMap[alertLevel as keyof typeof typeMap] || 'success'
}
</script>

<style scoped>
.monitoring-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.budget-monitoring {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.budget-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  background: #f5f7fa;
  transition: all 0.3s ease;
}

.budget-item.budget-warning {
  background: #fdf6ec;
  border: 1px solid #e6a23c;
}

.budget-item.budget-critical {
  background: #fef0f0;
  border: 1px solid #f56c6c;
}

.budget-item.budget-normal {
  background: #f0f9ff;
  border: 1px solid #67c23a;
}

.budget-info {
  flex: 1;
  margin-right: 16px;
}

.budget-org {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.budget-progress {
  margin-bottom: 8px;
}

.budget-details {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #606266;
}

.budget-alert {
  flex-shrink: 0;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
  color: #909399;
  font-size: 14px;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .monitoring-card {
    background: rgba(31, 31, 31, 0.95);
  }

  .budget-item {
    background: rgba(45, 45, 45, 0.8);
  }

  .budget-item.budget-warning {
    background: rgba(230, 162, 60, 0.1);
  }

  .budget-item.budget-critical {
    background: rgba(245, 108, 108, 0.1);
  }

  .budget-item.budget-normal {
    background: rgba(103, 194, 58, 0.1);
  }

  .budget-org {
    color: #ffffff;
  }

  .budget-details {
    color: #a0a0a0;
  }
}
</style>