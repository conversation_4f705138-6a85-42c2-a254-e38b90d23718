<template>
  <el-dialog
    :model-value="modelValue"
    title="批量调整薪资"
    width="700px"
    @update:model-value="$emit('update:modelValue', $event)"
    @close="handleClose"
  >
    <el-alert 
      :title="`已选择 ${selectedRows.length} 条记录进行批量调整`" 
      type="info" 
      :closable="false"
      style="margin-bottom: 20px"
     />

    <el-form ref="formRef" :model="formData" label-width="120px">
      <el-form-item label="调整方式" prop="adjustType">
        <el-radio-group v-model="formData.adjustType">
          <el-radio label="fixed">固定金额调整</el-radio>
          <el-radio label="percentage">按比例调整</el-radio>
          <el-radio label="formula">公式计算</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="调整项目" prop="adjustItems">
        <el-checkbox-group v-model="formData.adjustItems">
          <el-checkbox label="baseSalary">基本工资</el-checkbox>
          <el-checkbox label="positionSalary">岗位工资</el-checkbox>
          <el-checkbox label="performanceSalary">绩效工资</el-checkbox>
          <el-checkbox label="allowance">津贴</el-checkbox>
          <el-checkbox label="subsidy">补贴</el-checkbox>
          <el-checkbox label="bonus">奖金</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <template v-if="formData.adjustType === 'fixed'">
        <el-form-item label="调整金额" prop="fixedAmount">
          <el-input-number
            v-model="formData.fixedAmount"
            :precision="2"
            :step="100"
            style="width: 200px"
            />
          <span style="margin-left: 10px">元</span>
        </el-form-item>
      </template>

      <template v-if="formData.adjustType === 'percentage'">
        <el-form-item label="调整比例" prop="percentage">
          <el-input-number
            v-model="formData.percentage"
            :precision="1"
            :min="-100"
            :max="100"
            style="width: 200px"
            />
          <span style="margin-left: 10px">%</span>
        </el-form-item>
      </template>

      <template v-if="formData.adjustType === 'formula'">
        <el-form-item label="计算公式" prop="formula">
          <el-input
            v-model="formData.formula"
            type="textarea"
            :rows="3"
            placeholder="例如：基本工资 * 1.1 + 500"
            />
        </el-form-item>
      </template>

      <el-form-item label="生效月份" prop="effectiveMonth">
        <el-date-picker
          v-model="formData.effectiveMonth"
          type="month"
          placeholder="选择月份"
          value-format="YYYY-MM"
          style="width: 200px"
         />
      </el-form-item>

      <el-form-item label="调整原因" prop="reason">
        <el-input
          v-model="formData.reason"
          type="textarea"
          :rows="3"
          placeholder="请输入调整原因"
          />
      </el-form-item>
    </el-form>

    <el-divider   />

    <div class="preview-section">
      <h4>调整预览</h4>
      <el-table :data="previewData" max-height="300">
        <el-table-column prop="employeeName" label="姓名" width="100"  />
        <el-table-column prop="itemName" label="调整项目" width="120"  />
        <el-table-column prop="originalValue" label="原值" width="100" align="right">
          <template #default="{ row }">
            ¥{{ formatNumber(row.originalValue) }}
          </template>
        </el-table-column>
        <el-table-column prop="adjustedValue" label="调整后" width="100" align="right">
          <template #default="{ row }">
            ¥{{ formatNumber(row.adjustedValue) }}
          </template>
        </el-table-column>
        <el-table-column prop="difference" label="差额" width="100" align="right">
          <template #default="{ row }">
            <span :style="{ color: row.difference > 0 ? '#67c23a' : '#f56c6c' }">
              {{ row.difference > 0 ? '+' : '' }}¥{{ formatNumber(Math.abs(row.difference)) }}
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="loading">
        确认调整
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'BatchAdjustDialog'
})
 
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { PersonalSalary } from '@/types/salary'

interface Props {
  modelValue: boolean
  selectedRows: PersonalSalary[]
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref()
const loading = ref(false)

const formData = reactive({
  adjustType: 'fixed',
  adjustItems: [] as string[],
  fixedAmount: 0,
  percentage: 0,
  formula: '',
  effectiveMonth: '',
  reason: ''
})

// 预览数据
const previewData = computed(() => {
   
  const data: unknown[] = []
  
  if (formData.adjustItems.length === 0) return data
  
  // 只显示前3条记录的预览
  const previewRows = props.selectedRows.slice(0, 3)
  
  previewRows.forEach(row => {
    formData.adjustItems.forEach(item => {
      const itemNameMap: Record<string, string> = {
        baseSalary: '基本工资',
        positionSalary: '岗位工资',
        performanceSalary: '绩效工资',
        allowance: '津贴',
        subsidy: '补贴',
        bonus: '奖金'
      }
      
      const originalValue = row[item as keyof PersonalSalary] as number || 0
      let adjustedValue = originalValue
      
      if (formData.adjustType === 'fixed') {
        adjustedValue = originalValue + formData.fixedAmount
      } else if (formData.adjustType === 'percentage') {
        adjustedValue = originalValue * (1 + formData.percentage / 100)
      }
      
      data.push({
        employeeName: row.employeeName,
        itemName: itemNameMap[item],
        originalValue,
        adjustedValue,
        difference: adjustedValue - originalValue
      })
    })
  })
  
  return data
})

// 设置默认值
watch(() => props.modelValue, (val) => {
  if (val) {
    const now = new Date()
    formData.effectiveMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`
  }
})

// 确认调整
const handleConfirm = async () => {
  if (formData.adjustItems.length === 0) {
    ElMessage.error('请选择要调整的项目')
    return
  }

  if (!formData.effectiveMonth) {
    ElMessage.error('请选择生效月份')
    return
  }

  if (!formData.reason) {
    ElMessage.error('请输入调整原因')
    return
  }

  loading.value = true
  try {
    // 模拟调整过程
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    ElMessage.success(`成功调整 ${props.selectedRows.length} 条记录`)
    emit('confirm')
    handleClose()
  } catch (__error) {
    ElMessage.error('调整失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  formRef.value?.resetFields()
  formData.adjustItems = []
  emit('update:modelValue', false)
}

// 格式化数字
const formatNumber = (num: number) => {
  return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}
</script>

<style lang="scss" scoped>
.preview-section {
  margin-top: 20px;

  h4 {
    margin-bottom: 10px;
    color: #303133;
  }
}
</style>