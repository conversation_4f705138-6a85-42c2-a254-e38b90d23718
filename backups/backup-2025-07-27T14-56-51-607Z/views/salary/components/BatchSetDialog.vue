<template>
  <el-dialog
    :model-value="modelValue"
    title="批量设置薪资标准"
    width="700px"
    @update:model-value="$emit('update:modelValue', $event)"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
    >
      <el-form-item label="薪资结构" prop="structureId">
        <el-select 
          v-model="formData.structureId" 
          placeholder="请选择薪资结构"
          style="width: 100%"
          @change="handleStructureChange"
        >
          <el-option 
            v-for="item in structureList" 
            :key="item.id"
            :label="item.name"
            :value="item.id"
           />
        </el-select>
      </el-form-item>

      <el-form-item label="岗位范围" prop="positionIds">
        <el-select 
          v-model="formData.positionIds" 
          placeholder="请选择岗位（可多选）"
          multiple
          collapse-tags
          collapse-tags-tooltip
          style="width: 100%"
        >
          <el-option 
            v-for="item in positionList" 
            :key="item.id"
            :label="item.name"
            :value="item.id"
           />
        </el-select>
      </el-form-item>

      <el-form-item label="级别范围" prop="levelIds">
        <el-checkbox-group v-model="formData.levelIds">
          <el-checkbox v-for="level in levelList" :key="level.id" :label="level.id">
            {{ level.name }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item label="生效日期" prop="effectiveDate">
        <el-date-picker
          v-model="formData.effectiveDate"
          type="date"
          placeholder="选择生效日期"
          style="width: 100%"
          value-format="YYYY-MM-DD"
          :disabled-date="disabledDate"
         />
      </el-form-item>

      <el-divider>薪资项目设置</el-divider>

      <el-form-item label="设置方式">
        <el-radio-group v-model="formData.setType">
          <el-radio label="fixed">统一标准</el-radio>
          <el-radio label="percentage">按比例调整</el-radio>
          <el-radio label="range">按级别递增</el-radio>
        </el-radio-group>
      </el-form-item>

      <div v-if="structureItems.length" class="items-setting">
        <el-table :data="formData.items" stripe>
          <el-table-column prop="itemName" label="薪资项目" width="150"  />
          <el-table-column label="标准值" min-width="150">
            <template #default="{ row }">
              <el-input-number
                v-if="formData.setType === 'fixed'"
                v-model="row.standardValue"
                :precision="2"
                :min="0"
                size="small"
                style="width: 100%"
                />
              <div v-else-if="formData.setType === 'percentage'" class="percentage-input">
                <el-input-number
                  v-model="row.percentage"
                  :precision="1"
                  :min="-100"
                  :max="100"
                  size="small"
                  style="width: 120px"
                  />
                <span>%</span>
              </div>
              <div v-else class="range-input">
                <el-input-number
                  v-model="row.baseValue"
                  :precision="2"
                  :min="0"
                  size="small"
                  style="width: 100px"
                  placeholder="基础值"
                  />
                <span>+</span>
                <el-input-number
                  v-model="row.increment"
                  :precision="2"
                  :min="0"
                  size="small"
                  style="width: 80px"
                  placeholder="递增"
                  />
                <span>/级</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <el-form-item label="备注说明" prop="remark">
        <el-input 
          v-model="formData.remark" 
          type="textarea"
          :rows="3"
          placeholder="请输入备注说明"
          />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="loading">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import type { SalaryStructure } from '@/types/salary'

interface Props {
  modelValue: boolean
  structureList: SalaryStructure[]
   
  positionList: unknown[]
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
   
  (e: 'confirm', data: unknown): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const loading = ref(false)

// 表单数据
const formData = reactive({
  structureId: '',
  positionIds: [] as string[],
  levelIds: [] as string[],
  effectiveDate: new Date().toISOString().split('T')[0],
  setType: 'fixed',
  items: [] as unknown[],
  remark: ''
})

// 岗位级别列表
const levelList = ref([
  { id: 'L1', name: 'HrHr初级' },
  { id: 'L2', name: '中级' },
  { id: 'L3', name: '高级' },
  { id: 'L4', name: '资深' },
  { id: 'L5', name: '专家' }
])

// 当前选中薪资结构的项目
const structureItems = computed(() => {
  if (!formData.structureId) return []
  const structure = props.structureList.find(s => s.id === formData.structureId)
  return structure?.items || []
})

// 表单验证规则
const rules: FormRules = {
  structureId: [
    { required: true, message: '请选择薪资结构', trigger: 'change' }
  ],
  positionIds: [
    { required: true, message: '请选择岗位范围', trigger: 'change' }
  ],
  levelIds: [
    { required: true, message: '请选择级别范围', trigger: 'change' }
  ],
  effectiveDate: [
    { required: true, message: '请选择生效日期', trigger: 'change' }
  ]
}

// 薪资结构变化处理
const handleStructureChange = (structureId: string) => {
  const structure = props.structureList.find(s => s.id === structureId)
  if (structure) {
    // 初始化薪资项目
    formData.items = structure.items.map(item => ({
      itemId: item.id || item.tempId || '',
      itemName: item.name,
      itemCode: item.code,
      standardValue: 0,
      percentage: 0,
      baseValue: 0,
      increment: 0
    }))
  }
}

// 禁用过去的日期
const disabledDate = (date: Date) => {
  return date < new Date(new Date().setHours(0, 0, 0, 0))
}

// 关闭对话框
const handleClose = () => {
  formRef.value?.resetFields()
  formData.items = []
  emit('update:modelValue', false)
}

// 确认提交
const handleConfirm = async () => {
  try {
    await formRef.value?.validate()
    
    // 验证薪资项目设置
    if (formData.items.length === 0) {
      ElMessage.error('请先选择薪资结构')
      return
    }
    
    // 根据设置方式验证数据
    if (formData.setType === 'fixed') {
      const invalidItems = formData.items.filter(item => !item.standardValue || item.standardValue <= 0)
      if (invalidItems.length > 0) {
        ElMessage.error('请填写所有项目的标准值')
        return
      }
    } else if (formData.setType === 'range') {
      const invalidItems = formData.items.filter(item => !item.baseValue || item.baseValue <= 0)
      if (invalidItems.length > 0) {
        ElMessage.error('请填写所有项目的基础值')
        return
      }
    }
    
    loading.value = true
    // 提交数据
    emit('confirm', { ...formData })
    
    setTimeout(() => {
      loading.value = false
      handleClose()
    }, 1000)
  } catch (__error) {
    console.error('表单验证失败', error)
  }
}
</script>

<style lang="scss" scoped>
.items-setting {
  margin-bottom: 20px;

  .percentage-input,
  .range-input {
    display: flex;
    align-items: center;
    gap: 8px;

    span {
      color: #606266;
    }
  }
}
</style>