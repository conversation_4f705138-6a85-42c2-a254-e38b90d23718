<template>
  <el-dialog
    v-model="dialogVisible"
    title="编辑工资数据"
    width="700px"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
    >
      <!-- 员工信息 -->
      <el-divider content-position="left">员工信息</el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="工号">
            <el-input v-model="formData.employeeNo" disabled   />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="姓名">
            <el-input v-model="formData.name" disabled   />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 收入项目 -->
      <el-divider content-position="left">收入项目</el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="基本工资" prop="baseSalary">
            <el-input-number
              v-model="formData.baseSalary"
              :min="0"
              :precision="2"
              @change="calculateTotal"
              />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="岗位工资" prop="positionSalary">
            <el-input-number
              v-model="formData.positionSalary"
              :min="0"
              :precision="2"
              @change="calculateTotal"
              />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="绩效工资" prop="performanceSalary">
            <el-input-number
              v-model="formData.performanceSalary"
              :min="0"
              :precision="2"
              @change="calculateTotal"
              />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="津贴补贴" prop="allowance">
            <el-input-number
              v-model="formData.allowance"
              :min="0"
              :precision="2"
              @change="calculateTotal"
              />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 扣除项目 -->
      <el-divider content-position="left">扣除项目</el-divider>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="社保" prop="socialInsurance">
            <el-input-number
              v-model="formData.socialInsurance"
              :min="0"
              :precision="2"
              @change="calculateTotal"
              />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="公积金" prop="housingFund">
            <el-input-number
              v-model="formData.housingFund"
              :min="0"
              :precision="2"
              @change="calculateTotal"
              />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="个税" prop="tax">
            <el-input-number
              v-model="formData.tax"
              :min="0"
              :precision="2"
              @change="calculateTotal"
              />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 汇总信息 -->
      <el-divider content-position="left">汇总信息</el-divider>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="应发工资">
            <el-input v-model="formData.gross" readonly>
              <template #prefix>¥</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="扣除合计">
            <el-input v-model="formData.deduction" readonly>
              <template #prefix>¥</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="实发工资">
            <el-input v-model="formData.net" readonly class="net-salary">
              <template #prefix>¥</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 备注 -->
      <el-form-item label="备注">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="2"
          placeholder="请输入备注信息"
          />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
 
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

// Props & Emits
interface Props {
  modelValue: boolean
   
  data?: unknown
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue', 'success'])

// 对话框
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 表单
const formRef = ref<FormInstance>()
const formData = ref({
  employeeNo: '',
  name: '',
  baseSalary: 0,
  positionSalary: 0,
  performanceSalary: 0,
  allowance: 0,
  socialInsurance: 0,
  housingFund: 0,
  tax: 0,
  gross: 0,
  deduction: 0,
  net: 0,
  remark: ''
})

// 表单规则
const rules: FormRules = {
  baseSalary: [{ required: true, message: '请输入基本工资', trigger: 'blur' }],
  positionSalary: [{ required: true, message: '请输入岗位工资', trigger: 'blur' }]
}

// 监听数据变化
watch(() => props.data, (newData) => {
  if (newData) {
    Object.assign(formData.value, newData)
  }
}, { immediate: true })

// 计算总额
const calculateTotal = () => {
  // 计算应发工资
  formData.value.gross = 
    formData.value.baseSalary +
    formData.value.positionSalary +
    formData.value.performanceSalary +
    formData.value.allowance

  // 计算扣除合计
  formData.value.deduction = 
    formData.value.socialInsurance +
    formData.value.housingFund +
    formData.value.tax

  // 计算实发工资
  formData.value.net = formData.value.gross - formData.value.deduction
}

// 保存
const handleSave = async () => {
  try {
    await formRef.value?.validate()
    
    ElMessage.success('保存成功')
    emit('success')
  } catch {
    ElMessage.warning('请检查表单填写')
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  gap: 10px;
}

:deep(.net-salary) {
  .el-input__inner {
    color: #409eff;
    font-weight: bold;
    font-size: 16px;
  }
}
</style>