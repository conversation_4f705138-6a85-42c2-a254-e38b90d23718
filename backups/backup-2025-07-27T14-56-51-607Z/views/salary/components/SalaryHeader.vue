<template>
  <div class="page-header" role="banner">
    <div class="header-content">
      <div class="header-title">
        <h1>薪酬福利管理</h1>
        <p class="subtitle">智能化薪酬数据管理、分析和报表系统</p>
      </div>

      <!-- 快速操作按钮 -->
      <div class="header-actions" v-if="!isMobile">
        <el-button type="primary" size="large" @click="$emit('quick-query')">
          <el-icon><Search /></el-icon>
          快速查询
        </el-button>

        <el-dropdown trigger="click" placement="bottom-end">
          <el-button size="large">
            <el-icon><MoreFilled /></el-icon>
            更多操作
          </el-button>

          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="$emit('quick-import')">
                <el-icon><Upload /></el-icon>
                数据导入
              </el-dropdown-item>
              <el-dropdown-item @click="$emit('quick-export')">
                <el-icon><Download /></el-icon>
                数据导出
              </el-dropdown-item>
              <el-dropdown-item @click="$emit('quick-analysis')">
                <el-icon><TrendCharts /></el-icon>
                薪酬分析
              </el-dropdown-item>
              <el-dropdown-item @click="$emit('system-config')" divided>
                <el-icon><Setting /></el-icon>
                系统配置
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>

      <!-- 移动端操作按钮 -->
      <div class="mobile-actions" v-if="isMobile">
        <el-button type="primary" circle @click="$emit('quick-query')">
          <el-icon><Search /></el-icon>
        </el-button>
        <el-button circle @click="$emit('show-mobile-menu')">
          <el-icon><MoreFilled /></el-icon>
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'SalaryHeader'
})
 
import {
  Search,
  MoreFilled,
  Upload,
  Download,
  TrendCharts,
  Setting
} from '@element-plus/icons-vue'

interface Props {
  isMobile?: boolean
}

interface Emits {
  (e: 'quick-query'): void
  (e: 'quick-import'): void
  (e: 'quick-export'): void
  (e: 'quick-analysis'): void
  (e: 'system-config'): void
  (e: 'show-mobile-menu'): void
}

withDefaults(defineProps<Props>(), {
  isMobile: false
})

defineEmits<Emits>()
</script>

<style scoped>
/* 页面头部 */
.page-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title h1 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  margin: 0;
  color: #7f8c8d;
  font-size: 16px;
  font-weight: 400;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.mobile-actions {
  display: flex;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .header-title {
    text-align: center;
  }

  .header-title h1 {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: 16px;
  }

  .header-title h1 {
    font-size: 20px;
  }

  .subtitle {
    font-size: 14px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .page-header {
    background: rgba(31, 31, 31, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .header-title h1 {
    color: #ffffff;
  }

  .subtitle {
    color: #a0a0a0;
  }
}
</style>