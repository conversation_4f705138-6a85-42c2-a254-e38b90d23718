<template>
  <el-card class="exceptions-card" shadow="never">
    <template #header>
      <div class="card-header">
        <span>异常提醒</span>
        <el-button size="small" type="primary" link @click="$emit('view-all')">
          查看全部
        </el-button>
      </div>
    </template>
    <div class="exceptions-list">
      <div
        v-for="item in exceptions"
        :key="item.id"
        class="exception-item"
        @click="$emit('view-exception', item)"
      >
        <div class="exception-info">
          <div class="exception-title">{{ item.employeeName }} - {{ item.exceptionTypeName }}</div>
          <div class="exception-desc">{{ item.description }}</div>
          <div class="exception-time">{{ formatDateTime(item.detectedTime || '') }}</div>
        </div>
        <div class="exception-status">
          <el-tag :type="getExceptionStatusTagType(item.status)" size="small">
            {{ item.statusName }}
          </el-tag>
        </div>
      </div>
      <div v-if="exceptions.length === 0" class="empty-state">
        暂无异常数据
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">

defineOptions({
  name: 'ExceptionAlert'
})
 
import type { SalaryException } from '@/api/salary'
import { formatDateTime } from '@/utils/date'
import { EXCEPTION_STATUS_TYPES } from '../types'

interface Props {
  exceptions: SalaryException[]
}

interface Emits {
  (e: 'view-all'): void
  (e: 'view-exception', exception: SalaryException): void
}

defineProps<Props>()
defineEmits<Emits>()

// 获取异常状态标签类型
const getExceptionStatusTagType = (status: string) => {
  return EXCEPTION_STATUS_TYPES[status as keyof typeof EXCEPTION_STATUS_TYPES] || ''
}
</script>

<style scoped>
.exceptions-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.exceptions-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.exception-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px;
  border-radius: 8px;
  background: #f5f7fa;
  cursor: pointer;
  transition: all 0.3s ease;
}

.exception-item:hover {
  background: #e9ecef;
  transform: translateX(4px);
}

.exception-info {
  flex: 1;
  margin-right: 12px;
}

.exception-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.exception-desc {
  font-size: 13px;
  color: #606266;
  margin-bottom: 4px;
  line-height: 1.4;
}

.exception-time {
  font-size: 12px;
  color: #909399;
}

.exception-status {
  flex-shrink: 0;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
  color: #909399;
  font-size: 14px;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .exceptions-card {
    background: rgba(31, 31, 31, 0.95);
  }

  .exception-item {
    background: rgba(45, 45, 45, 0.8);
  }

  .exception-item:hover {
    background: rgba(60, 60, 60, 0.8);
  }

  .exception-title {
    color: #ffffff;
  }

  .exception-desc {
    color: #a0a0a0;
  }

  .exception-time {
    color: #808080;
  }
}
</style>