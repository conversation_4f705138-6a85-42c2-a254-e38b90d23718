<template>
  <el-dialog
    :model-value="modelValue"
    :title="`${employeeName} - 薪资历史记录`"
    width="1200px"
    @update:model-value="$emit('update:modelValue', $event)"
    @close="handleClose"
  >
    <!-- 时间范围选择 -->
    <div class="filter-bar">
      <el-date-picker
        v-model="dateRange"
        type="monthrange"
        range-separator="至"
        start-placeholder="开始月份"
        end-placeholder="结束月份"
        value-format="YYYY-MM"
        @change="fetchHistory"
       />
      <el-button type="primary" @click="exportHistory">
        <el-icon><Download /></el-icon>
        导出历史
      </el-button>
    </div>

    <!-- 统计信息 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-statistic title="记录数" :value="historyData.length"  />
      </el-col>
      <el-col :span="6">
        <el-statistic title="平均薪资" :value="avgSalary" :precision="2" prefix="¥"  />
      </el-col>
      <el-col :span="6">
        <el-statistic title="最高薪资" :value="maxSalary" :precision="2" prefix="¥"  />
      </el-col>
      <el-col :span="6">
        <el-statistic title="薪资总额" :value="totalSalary" :precision="2" prefix="¥"  />
      </el-col>
    </el-row>

    <!-- 薪资趋势图 -->
    <el-card class="chart-card" shadow="never">
      <template #header>
        <span>薪资趋势</span>
      </template>
      <div ref="chartRef" style="height: 300px"></div>
    </el-card>

    <!-- 历史记录表格 -->
    <el-table :data="historyData" stripe max-height="400">
      <el-table-column prop="salaryMonth" label="月份" width="100" fixed  />
      <el-table-column label="收入" align="center">
        <el-table-column prop="baseSalary" label="基本工资" width="100" align="right">
          <template #default="{ row }">
            {{ formatNumber(row.baseSalary) }}
          </template>
        </el-table-column>
        <el-table-column prop="positionSalary" label="岗位工资" width="100" align="right">
          <template #default="{ row }">
            {{ formatNumber(row.positionSalary) }}
          </template>
        </el-table-column>
        <el-table-column prop="performanceSalary" label="绩效工资" width="100" align="right">
          <template #default="{ row }">
            {{ formatNumber(row.performanceSalary) }}
          </template>
        </el-table-column>
        <el-table-column prop="otherIncome" label="其他收入" width="100" align="right">
          <template #default="{ row }">
            {{ formatNumber(row.allowance + row.subsidy + row.bonus + row.otherIncome) }}
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="扣除" align="center">
        <el-table-column prop="socialInsurance" label="社保" width="100" align="right">
          <template #default="{ row }">
            {{ formatNumber(row.pensionInsurance + row.medicalInsurance + row.unemploymentInsurance + row.injuryInsurance + row.maternityInsurance) }}
          </template>
        </el-table-column>
        <el-table-column prop="housingFund" label="公积金" width="100" align="right">
          <template #default="{ row }">
            {{ formatNumber(row.housingFund) }}
          </template>
        </el-table-column>
        <el-table-column prop="incomeTax" label="个税" width="100" align="right">
          <template #default="{ row }">
            {{ formatNumber(row.incomeTax) }}
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column prop="grossSalary" label="应发工资" width="120" align="right">
        <template #default="{ row }">
          <span style="color: #409eff">{{ formatNumber(row.grossSalary) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="netSalary" label="实发工资" width="120" align="right">
        <template #default="{ row }">
          <span style="color: #67c23a; font-weight: bold">{{ formatNumber(row.netSalary) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100" fixed="right">
        <template #default="{ row }">
          <el-button link type="primary" @click="viewDetail(row)">
            查看详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
 
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { Download } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import type { PersonalSalary } from '@/types/salary'

interface Props {
  modelValue: boolean
  employeeId: string
  employeeName: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const chartRef = ref<HTMLElement>()
const chartInstance = ref<echarts.ECharts>()
const historyData = ref<PersonalSalary[]>([])
const dateRange = ref<string[]>([])

// 计算统计数据
const avgSalary = computed(() => {
  if (historyData.value.length === 0) return 0
  const sum = historyData.value.reduce((acc, item) => acc + item.netSalary, 0)
  return sum / historyData.value.length
})

const maxSalary = computed(() => {
  if (historyData.value.length === 0) return 0
  return Math.max(...historyData.value.map(item => item.netSalary))
})

const totalSalary = computed(() => {
  return historyData.value.reduce((acc, item) => acc + item.netSalary, 0)
})

// 监听对话框显示状态
watch(() => props.modelValue, async (val) => {
  if (val) {
    // 设置默认时间范围为最近12个月
    const now = new Date()
    const start = new Date(now.getFullYear(), now.getMonth() - 11, 1)
    dateRange.value = [
      `${start.getFullYear()}-${String(start.getMonth() + 1).padStart(2, '0')}`,
      `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`
    ]
    
    await fetchHistory()
    await nextTick()
    initChart()
  }
})

// 获取历史记录
const fetchHistory = async () => {
  // 模拟获取历史数据
  const mockData: PersonalSalary[] = []
  const [startStr, endStr] = dateRange.value
  
  if (startStr && endStr) {
    const start = new Date(startStr)
    const end = new Date(endStr)
    
    const current = new Date(start)
    while (current <= end) {
      const month = `${current.getFullYear()}-${String(current.getMonth() + 1).padStart(2, '0')}`
      
      // 生成模拟数据
      const baseSalary = 8000 + Math.random() * 2000
      const positionSalary = 5000 + Math.random() * 1000
      const performanceSalary = 3000 + Math.random() * 2000
      const allowance = 1000 + Math.random() * 500
      const subsidy = 800 + Math.random() * 200
      const bonus = Math.random() > 0.7 ? Math.random() * 5000 : 0
      
      const grossSalary = baseSalary + positionSalary + performanceSalary + allowance + subsidy + bonus
      
      const pensionInsurance = baseSalary * 0.08
      const medicalInsurance = baseSalary * 0.02
      const unemploymentInsurance = baseSalary * 0.005
      const injuryInsurance = baseSalary * 0.002
      const maternityInsurance = baseSalary * 0.005
      const housingFund = baseSalary * 0.12
      const incomeTax = calculateIncomeTax(grossSalary)
      
      const totalDeduction = pensionInsurance + medicalInsurance + unemploymentInsurance + 
        injuryInsurance + maternityInsurance + housingFund + incomeTax
      
      mockData.push({
        id: `SALARY_${month}_${props.employeeId}`,
        employeeId: props.employeeId,
        employeeNo: 'EMP001',
        employeeName: props.employeeName,
        departmentId: 'DEPT001',
        departmentName: '教学部',
        positionId: 'POS001',
        positionName: '讲师',
        levelId: 'L3',
        levelName: '高级',
        salaryMonth: month,
        baseSalary,
        positionSalary,
        rankSalary: 0,
        performanceSalary,
        allowance,
        subsidy,
        bonus,
        otherIncome: 0,
        pensionInsurance,
        medicalInsurance,
        unemploymentInsurance,
        injuryInsurance,
        maternityInsurance,
        housingFund,
        incomeTax,
        otherDeduction: 0,
        grossSalary,
        totalDeduction,
        netSalary: grossSalary - totalDeduction,
        paymentStatus: 'paid',
        paymentDate: `${month}-25`,
        remark: ''
      })
      
      // 下一个月
      current.setMonth(current.getMonth() + 1)
    }
  }
  
  historyData.value = mockData
  updateChart()
}

// 计算个人所得税（简化计算）
const calculateIncomeTax = (salary: number) => {
  const taxableIncome = salary - 5000 // 起征点5000
  if (taxableIncome <= 0) return 0
  
  if (taxableIncome <= 3000) return taxableIncome * 0.03
  if (taxableIncome <= 12000) return taxableIncome * 0.1 - 210
  if (taxableIncome <= 25000) return taxableIncome * 0.2 - 1410
  if (taxableIncome <= 35000) return taxableIncome * 0.25 - 2660
  if (taxableIncome <= 55000) return taxableIncome * 0.3 - 4410
  if (taxableIncome <= 80000) return taxableIncome * 0.35 - 7160
  return taxableIncome * 0.45 - 15160
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance.value = echarts.init(chartRef.value)
  updateChart()
}

// 更新图表
const updateChart = () => {
  if (!chartInstance.value) return
  
  const months = historyData.value.map(item => item.salaryMonth)
  const grossSalaries = historyData.value.map(item => item.grossSalary)
  const netSalaries = historyData.value.map(item => item.netSalary)
  
  const option = {
    tooltip: {
      trigger: 'axis',
   
      formatter: (params: unknown) => {
        const month = params[0].axisValue
        const gross = params[0].value
        const net = params[1].value
        return `${month}<br/>应发工资: ¥${gross.toFixed(2)}<br/>实发工资: ¥${net.toFixed(2)}`
      }
    },
    legend: {
      data: ['应发工资', '实发工资']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: months,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '¥{value}'
      }
    },
    series: [
      {
        name: 'HrHr应发工资',
        type: 'line',
        data: grossSalaries,
        itemStyle: {
          color: '#409eff'
        }
      },
      {
        name: '实发工资',
        type: 'line',
        data: netSalaries,
        itemStyle: {
          color: '#67c23a'
        }
      }
    ]
  }
  
  chartInstance.value.setOption(option)
}

// 导出历史
const exportHistory = () => {
  console.log('导出薪资历史')
}

// 查看详情
const viewDetail = (row: PersonalSalary) => {
  console.log('查看薪资详情', row)
}

// 关闭对话框
const handleClose = () => {
  emit('update:modelValue', false)
}

// 格式化数字
const formatNumber = (num: number) => {
  return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

// 组件卸载时销毁图表
onMounted(() => {
  window.addEventListener('resize', () => {
    chartInstance.value?.resize()
  })
})
</script>

<style lang="scss" scoped>
.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.chart-card {
  margin-bottom: 20px;
}
</style>