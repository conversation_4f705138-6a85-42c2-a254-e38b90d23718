<template>
  <el-table
    v-loading="loading"
    :data="data"
    stripe
    @selection-change="handleSelectionChange"
  >
    <el-table-column type="selection" width="55" v-if="showActions"  />
    <el-table-column prop="employeeNo" label="工号" width="100" fixed  />
    <el-table-column prop="name" label="姓名" width="80" fixed  />
    <el-table-column prop="department" label="部门" width="120"  />
    <el-table-column prop="position" label="岗位" width="100"  />
    
    <!-- 收入项目 -->
    <el-table-column label="收入项目" align="center">
      <el-table-column prop="baseSalary" label="基本工资" width="100" align="right">
        <template #default="{ row }">
          {{ formatMoney(row.baseSalary) }}
        </template>
      </el-table-column>
      <el-table-column prop="positionSalary" label="岗位工资" width="100" align="right">
        <template #default="{ row }">
          {{ formatMoney(row.positionSalary) }}
        </template>
      </el-table-column>
      <el-table-column prop="performanceSalary" label="绩效工资" width="100" align="right">
        <template #default="{ row }">
          {{ formatMoney(row.performanceSalary) }}
        </template>
      </el-table-column>
      <el-table-column prop="allowance" label="津贴补贴" width="100" align="right">
        <template #default="{ row }">
          {{ formatMoney(row.allowance) }}
        </template>
      </el-table-column>
    </el-table-column>
    
    <!-- 扣除项目 -->
    <el-table-column label="扣除项目" align="center">
      <el-table-column prop="socialInsurance" label="社保" width="80" align="right">
        <template #default="{ row }">
          {{ formatMoney(row.socialInsurance || 0) }}
        </template>
      </el-table-column>
      <el-table-column prop="housingFund" label="公积金" width="80" align="right">
        <template #default="{ row }">
          {{ formatMoney(row.housingFund || 0) }}
        </template>
      </el-table-column>
      <el-table-column prop="tax" label="个税" width="80" align="right">
        <template #default="{ row }">
          {{ formatMoney(row.tax || 0) }}
        </template>
      </el-table-column>
    </el-table-column>

    <!-- 汇总 -->
    <el-table-column prop="gross" label="应发工资" width="110" align="right" fixed="right">
      <template #default="{ row }">
        <span class="gross">{{ formatMoney(row.gross) }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="deduction" label="扣除合计" width="100" align="right" fixed="right">
      <template #default="{ row }">
        <span class="deduction">{{ formatMoney(row.deduction) }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="net" label="实发工资" width="110" align="right" fixed="right">
      <template #default="{ row }">
        <span class="net">{{ formatMoney(row.net) }}</span>
      </template>
    </el-table-column>

    <el-table-column label="操作" width="120" fixed="right" v-if="showActions">
      <template #default="{ row }">
        <el-button link type="primary" @click="handleEdit(row)">编辑</el-button>
        <el-button link type="danger" @click="handleDelete(row)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup lang="ts">

defineOptions({
  name: 'PayrollTable'
})
 
import { defineProps, defineEmits } from 'vue'

// Props
interface Props {
   
  data: unknown[]
  loading?: boolean
  showActions?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  showActions: true
})

// Emits
const emit = defineEmits(['selection-change', 'edit', 'delete'])

// 格式化金额
const formatMoney = (value: number) => {
  return value ? `¥${value.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}` : '¥0.00'
}

// 选择变化
   
const handleSelectionChange = (selection: unknown[]) => {
  emit('selection-change', selection)
}

// 编辑
   
const handleEdit = (row: unknown) => {
  emit('edit', row)
}

// 删除
   
const handleDelete = (row: unknown) => {
  emit('delete', row)
}
</script>

<style lang="scss" scoped>
.gross {
  color: #67c23a;
  font-weight: bold;
}

.deduction {
  color: #f56c6c;
}

.net {
  color: #409eff;
  font-weight: bold;
  font-size: 14px;
}

:deep(.el-table__header-wrapper) {
  .el-table__header {
    th {
      background-color: #f5f7fa;
    }
  }
}
</style>