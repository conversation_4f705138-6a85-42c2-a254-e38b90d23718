<template>
  <el-dialog
    :model-value="modelValue"
    :title="title"
    width="1000px"
    @update:model-value="$emit('update:modelValue', $event)"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="员工" prop="employeeId">
            <el-select 
              v-model="formData.employeeId" 
              placeholder="请选择员工"
              filterable
              remote
              :remote-method="searchEmployee"
              style="width: 100%"
              @change="handleEmployeeChange"
            >
              <el-option 
                v-for="item in filteredEmployeeList" 
                :key="item.id"
                :label="`${item.name} (${item.employeeNo})`"
                :value="item.id"
               />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="薪资月份" prop="salaryMonth">
            <el-date-picker
              v-model="formData.salaryMonth"
              type="month"
              placeholder="选择月份"
              value-format="YYYY-MM"
              style="width: 100%"
             />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="部门">
            <el-input v-model="formData.departmentName" readonly   />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="岗位">
            <el-input v-model="formData.positionName" readonly   />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="级别">
            <el-input v-model="formData.levelName" readonly   />
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider>收入项目</el-divider>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="基本工资" prop="baseSalary">
            <el-input-number
              v-model="formData.baseSalary"
              :precision="2"
              :min="0"
              style="width: 100%"
              @change="calculateSalary"
              />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="岗位工资" prop="positionSalary">
            <el-input-number
              v-model="formData.positionSalary"
              :precision="2"
              :min="0"
              style="width: 100%"
              @change="calculateSalary"
              />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="薪级工资" prop="rankSalary">
            <el-input-number
              v-model="formData.rankSalary"
              :precision="2"
              :min="0"
              style="width: 100%"
              @change="calculateSalary"
              />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="绩效工资" prop="performanceSalary">
            <el-input-number
              v-model="formData.performanceSalary"
              :precision="2"
              :min="0"
              style="width: 100%"
              @change="calculateSalary"
              />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="津贴" prop="allowance">
            <el-input-number
              v-model="formData.allowance"
              :precision="2"
              :min="0"
              style="width: 100%"
              @change="calculateSalary"
              />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="补贴" prop="subsidy">
            <el-input-number
              v-model="formData.subsidy"
              :precision="2"
              :min="0"
              style="width: 100%"
              @change="calculateSalary"
              />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="奖金" prop="bonus">
            <el-input-number
              v-model="formData.bonus"
              :precision="2"
              :min="0"
              style="width: 100%"
              @change="calculateSalary"
              />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="其他收入" prop="otherIncome">
            <el-input-number
              v-model="formData.otherIncome"
              :precision="2"
              :min="0"
              style="width: 100%"
              @change="calculateSalary"
              />
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider>扣除项目</el-divider>

      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="养老保险" prop="pensionInsurance">
            <el-input-number
              v-model="formData.pensionInsurance"
              :precision="2"
              :min="0"
              style="width: 100%"
              @change="calculateSalary"
              />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="医疗保险" prop="medicalInsurance">
            <el-input-number
              v-model="formData.medicalInsurance"
              :precision="2"
              :min="0"
              style="width: 100%"
              @change="calculateSalary"
              />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="失业保险" prop="unemploymentInsurance">
            <el-input-number
              v-model="formData.unemploymentInsurance"
              :precision="2"
              :min="0"
              style="width: 100%"
              @change="calculateSalary"
              />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="工伤保险" prop="injuryInsurance">
            <el-input-number
              v-model="formData.injuryInsurance"
              :precision="2"
              :min="0"
              style="width: 100%"
              @change="calculateSalary"
              />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="生育保险" prop="maternityInsurance">
            <el-input-number
              v-model="formData.maternityInsurance"
              :precision="2"
              :min="0"
              style="width: 100%"
              @change="calculateSalary"
              />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="公积金" prop="housingFund">
            <el-input-number
              v-model="formData.housingFund"
              :precision="2"
              :min="0"
              style="width: 100%"
              @change="calculateSalary"
              />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="个人所得税" prop="incomeTax">
            <el-input-number
              v-model="formData.incomeTax"
              :precision="2"
              :min="0"
              style="width: 100%"
              @change="calculateSalary"
              />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="其他扣除" prop="otherDeduction">
            <el-input-number
              v-model="formData.otherDeduction"
              :precision="2"
              :min="0"
              style="width: 100%"
              @change="calculateSalary"
              />
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider>汇总</el-divider>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="应发工资">
            <el-input v-model="formData.grossSalary" readonly>
              <template #prefix>¥</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="扣款合计">
            <el-input v-model="formData.totalDeduction" readonly style="color: #f56c6c">
              <template #prefix>¥</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="实发工资">
            <el-input v-model="formData.netSalary" readonly style="color: #67c23a; font-weight: bold">
              <template #prefix>¥</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="发放状态" prop="paymentStatus">
        <el-radio-group v-model="formData.paymentStatus">
          <el-radio label="pending">待发放</el-radio>
          <el-radio label="paid">已发放</el-radio>
          <el-radio label="failed">发放失败</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input 
          v-model="formData.remark" 
          type="textarea"
          :rows="3"
          placeholder="请输入备注"
          />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="loading">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'PersonalSalaryDialog'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import type { PersonalSalary } from '@/types/salary'
import { salaryApi } from '@/api/salary'

interface Props {
  modelValue: boolean
  title: string
  data?: PersonalSalary | null
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  employeeList: unknown[]
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', data: PersonalSalary): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const loading = ref(false)
const filteredEmployeeList = ref<any[]>([])

// 表单数据
const formData = reactive<PersonalSalary>({
  id: '',
  employeeId: '',
  employeeNo: '',
  employeeName: '',
  departmentId: '',
  departmentName: '',
  positionId: '',
  positionName: '',
  levelId: '',
  levelName: '',
  salaryMonth: '',
  baseSalary: 0,
  positionSalary: 0,
  rankSalary: 0,
  performanceSalary: 0,
  allowance: 0,
  subsidy: 0,
  bonus: 0,
  otherIncome: 0,
  pensionInsurance: 0,
  medicalInsurance: 0,
  unemploymentInsurance: 0,
  injuryInsurance: 0,
  maternityInsurance: 0,
  housingFund: 0,
  incomeTax: 0,
  otherDeduction: 0,
  grossSalary: 0,
  totalDeduction: 0,
  netSalary: 0,
  paymentStatus: 'pending',
  paymentDate: '',
  remark: ''
})

// 表单验证规则
const rules: FormRules = {
  employeeId: [
    { required: true, message: '请选择员工', trigger: 'change' }
  ],
  salaryMonth: [
    { required: true, message: '请选择薪资月份', trigger: 'change' }
  ],
  baseSalary: [
    { required: true, message: '请输入基本工资', trigger: 'blur' },
    { type: 'number', min: 0, message: '基本工资不能小于0', trigger: 'blur' }
  ]
}

// 监听数据变化
watch(() => props.data, (newData) => {
  if (newData) {
    Object.assign(formData, newData)
  } else {
    // 重置表单
    Object.keys(formData).forEach(key => {
      if (key === 'paymentStatus') {
        formData[key] = 'pending'
      } else if (typeof formData[key] === 'number') {
        formData[key] = 0
      } else {
        formData[key] = ''
      }
    })
    
    // 设置默认薪资月份为当前月份
    const now = new Date()
    formData.salaryMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`
  }
}, { immediate: true })

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  if (val) {
    filteredEmployeeList.value = props.employeeList
  }
})

// 搜索员工
const searchEmployee = (query: string) => {
  if (query) {
    filteredEmployeeList.value = props.employeeList.filter(item => 
      item.name.includes(query) || item.employeeNo.includes(query)
    )
  } else {
    filteredEmployeeList.value = props.employeeList
  }
}

// 员工选择变化
const handleEmployeeChange = async (employeeId: string) => {
  const employee = props.employeeList.find(e => e.id === employeeId)
  if (employee) {
    formData.employeeNo = employee.employeeNo
    formData.employeeName = employee.name
    formData.departmentId = employee.departmentId
    formData.departmentName = employee.departmentName
    formData.positionId = employee.positionId
    formData.positionName = employee.positionName
    formData.levelId = employee.levelId || ''
    formData.levelName = employee.levelName || ''
    
    // 根据员工的薪资标准自动填充薪资项目
    try {
      // 获取员工的薪资标准
      const {data: _data} =  await salaryApi.getEmployeeSalaryStandard(employee.employeeId)
      
      if (salaryStandard) {
        // 填充基本工资
        formData.baseSalary 
    }
  }
}

:deep(.el-divider__text) {
  background-color: #fff;
  font-weight: bold;
}
</style>