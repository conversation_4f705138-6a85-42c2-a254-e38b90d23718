<template>
  <div class="salary-comparison">
    <!-- 对比配置 -->
    <el-card class="config-card" shadow="never">
      <el-form :model="config" inline>
        <el-form-item label="对比类型">
          <el-radio-group v-model="config.type" @change="handleTypeChange">
            <el-radio-button label="monthly">月度对比</el-radio-button>
            <el-radio-button label="yearly">年度对比</el-radio-button>
            <el-radio-button label="employee">员工对比</el-radio-button>
            <el-radio-button label="department">部门对比</el-radio-button>
          </el-radio-group>
        </el-form-item>
        
        <template v-if="config.type === 'monthly'">
          <el-form-item label="选择月份">
            <el-date-picker
              v-model="config.months"
              type="monthrange"
              range-separator="至"
              start-placeholder="开始月份"
              end-placeholder="结束月份"
              value-format="YYYY-MM"
              :clearable="false"
             />
          </el-form-item>
        </template>
        
        <template v-if="config.type === 'yearly'">
          <el-form-item label="选择年份">
            <el-date-picker
              v-model="config.year"
              type="year"
              placeholder="选择年份"
              value-format="YYYY"
             />
          </el-form-item>
        </template>
        
        <template v-if="config.type === 'employee'">
          <el-form-item label="选择员工">
            <el-select
              v-model="config.employees"
              multiple
              placeholder="请选择员工（最多5个）"
              style="width: 300px"
              :multiple-limit="5"
            >
              <el-option
                v-for="emp in employeeList"
                :key="emp.id"
                :label="`${emp.name} (${emp.employeeNo})`"
                :value="emp.id"
               />
            </el-select>
          </el-form-item>
        </template>
        
        <template v-if="config.type === 'department'">
          <el-form-item label="选择部门">
            <el-select
              v-model="config.departments"
              multiple
              placeholder="请选择部门"
              style="width: 300px"
            >
              <el-option
                v-for="dept in departmentList"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
               />
            </el-select>
          </el-form-item>
        </template>
        
        <el-form-item>
          <el-button type="primary" @click="handleCompare" :loading="loading">
            开始对比
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 对比结果 -->
    <div v-if="showResult" class="comparison-result">
      <!-- 关键指标 -->
      <el-row :gutter="20" class="metrics-row">
        <el-col :span="6" v-for="metric in keyMetrics" :key="metric.name">
          <el-card shadow="hover" class="metric-card">
            <div class="metric-value">{{ formatValue(metric.value, metric.type) }}</div>
            <div class="metric-name">{{ metric.name }}</div>
            <div class="metric-change" :class="getChangeClass(metric.change)">
              <el-icon v-if="metric.change > 0"><Top /></el-icon>
              <el-icon v-else-if="metric.change < 0"><Bottom /></el-icon>
              <span>{{ Math.abs(metric.change) }}%</span>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 趋势图表 -->
      <el-card class="chart-card" shadow="never">
        <template #header>
          <div class="chart-header">
            <span>薪资趋势对比</span>
            <el-radio-group v-model="chartType" size="small">
              <el-radio-button label="line">折线图</el-radio-button>
              <el-radio-button label="bar">柱状图</el-radio-button>
            </el-radio-group>
          </div>
        </template>
        <div ref="trendChartRef" class="chart-container"></div>
      </el-card>

      <!-- 构成分析 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="chart-card" shadow="never">
            <template #header>
              <span>薪资构成对比</span>
            </template>
            <div ref="structureChartRef" class="chart-container"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="chart-card" shadow="never">
            <template #header>
              <span>扣除项目对比</span>
            </template>
            <div ref="deductionChartRef" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 详细数据表格 -->
      <el-card class="table-card" shadow="never">
        <template #header>
          <span>详细数据对比</span>
        </template>
        <el-table :data="tableData" stripe>
          <el-table-column prop="name" label="对比项" width="150" fixed  />
          <el-table-column
            v-for="col in dynamicColumns"
            :key="col.prop"
            :prop="col.prop"
            :label="col.label"
            min-width="120"
            align="right"
          >
            <template #default="{ row }">
              {{ formatNumber(row[col.prop]) }}
            </template>
          </el-table-column>
          <el-table-column label="平均值" width="120" align="right">
            <template #default="{ row }">
              <strong>{{ formatNumber(row.average) }}</strong>
            </template>
          </el-table-column>
          <el-table-column label="变化率" width="100" align="center">
            <template #default="{ row }">
              <span :class="getChangeClass(row.changeRate)">
                {{ row.changeRate > 0 ? '+' : '' }}{{ row.changeRate }}%
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 空状态 -->
    <el-empty v-if="!showResult && !loading" description="请选择对比条件"  />
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Top, Bottom } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

const loading = ref(false)
const showResult = ref(false)
const chartType = ref('line')

// 图表实例
const trendChartRef = ref<HTMLElement>()
const structureChartRef = ref<HTMLElement>()
const deductionChartRef = ref<HTMLElement>()
let trendChart: echarts.ECharts
let structureChart: echarts.ECharts
let deductionChart: echarts.ECharts

// 配置
const config = reactive({
  type: 'monthly',
  months: [],
  year: new Date().getFullYear().toString(),
  employees: [],
  departments: []
})

// 模拟数据
const employeeList = ref([
  { id: 'EMP001', employeeNo: 'T2024001', name: 'HrHr张三' },
  { id: 'EMP002', employeeNo: 'T2024002', name: '李四' },
  { id: 'EMP003', employeeNo: 'T2024003', name: '王五' }
])

const departmentList = ref([
  { id: 'DEPT001', name: '计算机系' },
  { id: 'DEPT002', name: '数学系' },
  { id: 'DEPT003', name: '物理系' }
])

// 关键指标
const keyMetrics = ref([
  { name: '平均薪资', value: 18500, change: 5.2, type: 'currency' },
  { name: '薪资总额', value: 925000, change: 3.8, type: 'currency' },
  { name: '最高薪资', value: 35000, change: 8.5, type: 'currency' },
  { name: '人均增幅', value: 1200, change: 6.7, type: 'currency' }
])

// 动态列
const dynamicColumns = ref<any[]>([])

// 表格数据
const tableData = ref<any[]>([])

// 类型变化处理
const handleTypeChange = () => {
  showResult.value = false
  // 重置配置
  config.months = []
  config.employees = []
  config.departments = []
}

// 开始对比
const handleCompare = async () => {
  // 验证
  if (config.type === 'monthly' && config.months.length === 0) {
    ElMessage.warning('请选择对比月份')
    return
  }
  if (config.type === 'employee' && config.employees.length < 2) {
    ElMessage.warning('请至少选择2个员工进行对比')
    return
  }
  if (config.type === 'department' && config.departments.length < 2) {
    ElMessage.warning('请至少选择2个部门进行对比')
    return
  }

  loading.value = true
  try {
    // 模拟数据加载
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 生成对比数据
    generateComparisonData()
    
    showResult.value = true
    
    // 等待DOM更新后初始化图表
    await nextTick()
    initCharts()
    
    ElMessage.success('对比分析完成')
  } catch (__error) {
    ElMessage.error('对比分析失败')
  } finally {
    loading.value = false
  }
}

// 生成对比数据
const generateComparisonData = () => {
  if (config.type === 'monthly') {
    // 月度对比数据
    const months = getMonthsBetween(config.months[0], config.months[1])
    dynamicColumns.value = months.map(month => ({
      prop: month,
      label: month
    }))
    
    // 生成表格数据
    const items = ['基本工资', '岗位工资', '绩效工资', '津贴补贴', '实发工资']
    tableData.value = items.map(item => {
   
      const row: unknown = { name: item }
      let sum = 0
      months.forEach(month => {
        const value = 10000 + Math.random() * 5000
        row[month] = value
        sum += value
      })
      row.average = sum / months.length
      row.changeRate = ((row[months[months.length - 1]] - row[months[0]]) / row[months[0]] * 100).toFixed(1)
      return row
    })
  } else if (config.type === 'employee') {
    // 员工对比数据
    const selectedEmployees = employeeList.value.filter(emp => config.employees.includes(emp.id))
    dynamicColumns.value = selectedEmployees.map(emp => ({
      prop: emp.id,
      label: emp.name
    }))
    
    // 生成表格数据
    const items = ['基本工资', '岗位工资', '绩效工资', '津贴补贴', '扣除合计', '实发工资']
    tableData.value = items.map(item => {
   
      const row: unknown = { name: item }
      let sum = 0
      selectedEmployees.forEach(emp => {
        const value = 8000 + Math.random() * 10000
        row[emp.id] = value
        sum += value
      })
      row.average = sum / selectedEmployees.length
      row.changeRate = 0
      return row
    })
  }
}

// 获取两个月份之间的所有月份
const getMonthsBetween = (start: string, end: string) => {
  const months = []
  const startDate = new Date(start)
  const endDate = new Date(end)
  
  while (startDate <= endDate) {
    months.push(startDate.toISOString().slice(0, 7))
    startDate.setMonth(startDate.getMonth() + 1)
  }
  
  return months
}

// 初始化图表
const initCharts = () => {
  // 趋势图
  if (trendChartRef.value) {
    trendChart = echarts.init(trendChartRef.value)
    updateTrendChart()
  }
  
  // 构成图
  if (structureChartRef.value) {
    structureChart = echarts.init(structureChartRef.value)
    updateStructureChart()
  }
  
  // 扣除图
  if (deductionChartRef.value) {
    deductionChart = echarts.init(deductionChartRef.value)
    updateDeductionChart()
  }
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    trendChart?.resize()
    structureChart?.resize()
    deductionChart?.resize()
  })
}

// 更新趋势图
const updateTrendChart = () => {
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['实发工资', '应发工资']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dynamicColumns.value.map(col => col.label)
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '¥{value}'
      }
    },
    series: [
      {
        name: '实发工资',
        type: chartType.value,
        data: dynamicColumns.value.map(() => 15000 + Math.random() * 5000),
        itemStyle: { color: '#67c23a' }
      },
      {
        name: '应发工资',
        type: chartType.value,
        data: dynamicColumns.value.map(() => 20000 + Math.random() * 5000),
        itemStyle: { color: '#409eff' }
      }
    ]
  }
  
  trendChart.setOption(option)
}

// 更新构成图
const updateStructureChart = () => {
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: ¥{c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 10000, name: '基本工资' },
          { value: 6000, name: '岗位工资' },
          { value: 4000, name: '绩效工资' },
          { value: 2700, name: '津贴补贴' }
        ]
      }
    ]
  }
  
  structureChart.setOption(option)
}

// 更新扣除图
const updateDeductionChart = () => {
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      axisLabel: {
        formatter: '¥{value}'
      }
    },
    yAxis: {
      type: 'category',
      data: ['公积金', '养老保险', '医疗保险', '个税', '其他']
    },
    series: [
      {
        type: 'bar',
        data: [1200, 800, 200, 2800, 150],
        itemStyle: {
          color: '#f56c6c'
        }
      }
    ]
  }
  
  deductionChart.setOption(option)
}

// 格式化数值
const formatValue = (value: number, type: string) => {
  if (type === 'currency') {
    return '¥' + value.toLocaleString('zh-CN')
  }
  return value.toLocaleString('zh-CN')
}

// 格式化数字
const formatNumber = (num: number) => {
  return num?.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || '0.00'
}

// 获取变化样式
const getChangeClass = (change: number) => {
  if (change > 0) return 'positive'
  if (change < 0) return 'negative'
  return 'neutral'
}

// 监听图表类型变化
watch(() => chartType.value, () => {
  if (trendChart) {
    updateTrendChart()
  }
})

// 组件卸载时销毁图表
onUnmounted(() => {
  trendChart?.dispose()
  structureChart?.dispose()
  deductionChart?.dispose()
})
</script>

<style lang="scss" scoped>
.salary-comparison {
  .config-card {
    margin-bottom: 20px;
  }

  .comparison-result {
    .metrics-row {
      margin-bottom: 20px;

      .metric-card {
        text-align: center;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          transform: translateY(-4px);
        }

        .metric-value {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 8px;
        }

        .metric-name {
          font-size: 14px;
          color: #909399;
          margin-bottom: 8px;
        }

        .metric-change {
          font-size: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 4px;

          &.positive {
            color: #67c23a;
          }

          &.negative {
            color: #f56c6c;
          }

          &.neutral {
            color: #909399;
          }
        }
      }
    }

    .chart-card {
      margin-bottom: 20px;

      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .chart-container {
        height: 300px;
      }
    }

    .table-card {
      .positive {
        color: #67c23a;
      }

      .negative {
        color: #f56c6c;
      }

      .neutral {
        color: #909399;
      }
    }
  }
}
</style>