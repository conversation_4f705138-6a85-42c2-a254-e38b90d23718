<template>
  <el-drawer
    v-model="visible"
    title="操作菜单"
    direction="btt"
    size="auto"
  >
    <div class="mobile-menu-content">
      <div class="menu-item" @click="handleAction('quick-query')">
        <el-icon><Search /></el-icon>
        <span>快速查询</span>
      </div>
      <div class="menu-item" @click="handleAction('quick-import')">
        <el-icon><Upload /></el-icon>
        <span>数据导入</span>
      </div>
      <div class="menu-item" @click="handleAction('quick-export')">
        <el-icon><Download /></el-icon>
        <span>数据导出</span>
      </div>
      <div class="menu-item" @click="handleAction('quick-analysis')">
        <el-icon><TrendCharts /></el-icon>
        <span>薪酬分析</span>
      </div>
      <div class="menu-item" @click="handleAction('system-config')">
        <el-icon><Setting /></el-icon>
        <span>系统配置</span>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">

defineOptions({
  name: 'MobileMenu'
})
 
import {
  Search,
  Upload,
  Download,
  TrendCharts,
  Setting
} from '@element-plus/icons-vue'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'action', action: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 控制抽屉显示
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 处理操作
const handleAction = (action: string) => {
  emit('action', action)
  visible.value = false
}
</script>

<style scoped>
/* 移动端菜单 */
.mobile-menu-content {
  padding: 20px 0;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  cursor: pointer;
  transition: background 0.3s ease;
  border-radius: 8px;
  margin: 0 20px 8px 20px;
}

.menu-item:hover {
  background: #f5f7fa;
}

.menu-item span {
  font-size: 16px;
  color: #2c3e50;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .menu-item span {
    color: #ffffff;
  }

  .menu-item:hover {
    background: rgba(255, 255, 255, 0.1);
  }
}
</style>