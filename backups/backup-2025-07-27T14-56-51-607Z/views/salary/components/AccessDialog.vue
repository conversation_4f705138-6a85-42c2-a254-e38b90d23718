<template>
  <el-dialog
    v-model="visible"
    title="申请敏感数据访问权限"
    width="500px"
    :close-on-click-modal="false"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="访问原因" prop="reason">
        <el-input
          v-model="form.reason"
          type="textarea"
          :rows="4"
          placeholder="请详细说明访问敏感数据的原因"
          maxlength="200"
          show-word-limit
          />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          申请权限
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'AccessDialog'
})
 
import { ref, reactive, watch } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import type { AccessForm } from '../types'

interface Props {
  modelValue: boolean
  loading?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'submit', form: AccessForm): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

// 控制对话框显示
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单引用
const formRef = ref<FormInstance>()

// 表单数据
const form = reactive<AccessForm>({
  reason: ''
})

// 表单验证规则
const rules: FormRules = {
  reason: [
    { required: true, message: '请填写访问原因', trigger: 'blur' },
    { min: 10, max: 200, message: '访问原因长度在 10 到 200 个字符', trigger: 'blur' }
  ]
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    emit('submit', { ...form })
  } catch (__error) {
    console.error('表单验证失败:', error)
  }
}

// 监听对话框关闭，重置表单
watch(() => props.modelValue, (newVal) => {
  if (!newVal) {
    formRef.value?.resetFields()
  }
})
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>