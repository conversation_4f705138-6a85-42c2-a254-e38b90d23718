<template>
  <el-dialog
    :model-value="modelValue"
    title="批量导入薪资"
    width="700px"
    @update:model-value="$emit('update:modelValue', $event)"
    @close="handleClose"
  >
    <el-alert 
      title="请按照模板格式准备数据，确保数据准确性" 
      type="info" 
      :closable="false"
      style="margin-bottom: 20px"
     />

    <el-form ref="formRef" :model="formData" label-width="100px">
      <el-form-item label="薪资月份" prop="salaryMonth" required>
        <el-date-picker
          v-model="formData.salaryMonth"
          type="month"
          placeholder="选择月份"
          value-format="YYYY-MM"
          style="width: 100%"
         />
      </el-form-item>

      <el-form-item label="导入文件" required>
        <el-upload
          ref="uploadRef"
          class="upload-demo"
          :auto-upload="false"
          :on-change="handleFileChange"
          :limit="1"
          accept=".xlsx,.xls"
          drag
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              只能上传 xlsx/xls 文件，文件大小不超过 10MB
            </div>
          </template>
        </el-upload>
      </el-form-item>

      <el-form-item label="导入选项">
        <el-checkbox v-model="formData.skipExisting">跳过已存在的记录</el-checkbox>
        <el-checkbox v-model="formData.updateExisting" style="margin-left: 20px">更新已存在的记录</el-checkbox>
      </el-form-item>
    </el-form>

    <div class="template-download">
      <el-link type="primary" @click="downloadTemplate">
        <el-icon><download /></el-icon>
        下载导入模板
      </el-link>
    </div>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleImport" :loading="loading">
        开始导入
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'BatchImportDialog'
})
 
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled, Download } from '@element-plus/icons-vue'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref()
const uploadRef = ref()
const loading = ref(false)

const formData = reactive({
  salaryMonth: '',
  file: null as File | null,
  skipExisting: true,
  updateExisting: false
})

// 文件选择
   
const handleFileChange = (file: unknown) => {
  formData.file = file.raw
}

// 下载模板
const downloadTemplate = () => {
  ElMessage.success('下载薪资导入模板')
}

// 导入处理
const handleImport = async () => {
  if (!formData.salaryMonth) {
    ElMessage.error('请选择薪资月份')
    return
  }

  if (!formData.file) {
    ElMessage.error('请选择要导入的文件')
    return
  }

  loading.value = true
  try {
    // 模拟导入过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('导入成功！共导入 50 条记录')
    emit('success')
    handleClose()
  } catch (__error) {
    ElMessage.error('导入失败，请检查文件格式')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  formRef.value?.resetFields()
  uploadRef.value?.clearFiles()
  formData.file = null
  emit('update:modelValue', false)
}
</script>

<style lang="scss" scoped>
.upload-demo {
  width: 100%;
}

.template-download {
  margin-top: 20px;
  text-align: center;
}
</style>