<template>
  <div class="stats-overview">
    <div class="stats-grid" :class="{ 'mobile-grid': isMobile }">
      <div
        v-for="stat in cards"
        :key="stat.id"
        class="modern-stats-card"
        :class="[`stats-${stat.type}`, { 'clickable': stat.clickable }]"
        @click="stat.clickable && $emit('click', stat.id)"
        role="button"
        :tabindex="stat.clickable ? 0 : -1"
        :aria-label="`${stat.title}: ${stat.value}${stat.unit || ''}`"
        @keydown.enter="stat.clickable && $emit('click', stat.id)"
        @keydown.space.prevent="stat.clickable && $emit('click', stat.id)"
      >
        <div class="stats-header">
          <div class="stats-icon" :style="{ background: stat.gradient }">
            <el-icon :size="24">
              <component :is="stat.icon" />
            </el-icon>
          </div>
          <div class="stats-trend" v-if="stat.trend !== undefined">
            <el-icon :class="stat.trend >= 0 ? 'trend-up' : 'trend-down'">
              <component :is="stat.trend >= 0 ? ArrowUp : ArrowDown" />
            </el-icon>
            <span class="trend-value">{{ Math.abs(stat.trend) }}%</span>
          </div>
        </div>

        <div class="stats-content">
          <div class="stats-value">
            <span class="stats-number">{{ formatValue(stat.value, stat.type) }}</span>
            <span class="stats-unit" v-if="stat.unit">{{ stat.unit }}</span>
          </div>
          <div class="stats-title">{{ stat.title }}</div>
          <div class="stats-subtitle" v-if="stat.subtitle">{{ stat.subtitle }}</div>
        </div>

        <div class="stats-chart" v-if="stat.chartData">
          <div class="mini-chart">
            <div
              v-for="(point, index) in stat.chartData"
              :key="index"
              class="chart-bar"
              :style="{
                height: `${point}%`,
                background: stat.gradient
              }"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'StatisticsCards'
})
 
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import type { StatsCard } from '../types'

interface Props {
  cards: StatsCard[]
  isMobile?: boolean
}

interface Emits {
  (e: 'click', id: string): void
}

withDefaults(defineProps<Props>(), {
  isMobile: false
})

defineEmits<Emits>()

// 格式化统计值
const formatValue = (value: number, type: string) => {
  if (type === 'amount' || type === 'average') {
    return formatCurrency(value)
  }
  if (value >= 1000) {
    return (value / 1000).toFixed(1) + 'K'
  }
  return value.toString()
}

// 格式化货币
const formatCurrency = (amount: number) => {
  if (!amount) return '¥0'
  return `¥${amount.toLocaleString()}`
}
</script>

<style scoped>
/* 统计概览 */
.stats-overview {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.mobile-grid {
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 16px;
}

.modern-stats-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.modern-stats-card.clickable {
  cursor: pointer;
}

.modern-stats-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.stats-icon {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.stats-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
}

.trend-up {
  color: #27ae60;
}

.trend-down {
  color: #e74c3c;
}

.trend-value {
  color: inherit;
}

.stats-content {
  margin-bottom: 16px;
}

.stats-value {
  display: flex;
  align-items: baseline;
  gap: 4px;
  margin-bottom: 8px;
}

.stats-number {
  font-size: 36px;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
}

.stats-unit {
  font-size: 16px;
  color: #7f8c8d;
  font-weight: 500;
}

.stats-title {
  font-size: 16px;
  font-weight: 600;
  color: #34495e;
  margin-bottom: 4px;
}

.stats-subtitle {
  font-size: 12px;
  color: #95a5a6;
}

.stats-chart {
  margin-top: 16px;
}

.mini-chart {
  display: flex;
  align-items: end;
  gap: 2px;
  height: 40px;
}

.chart-bar {
  flex: 1;
  border-radius: 2px;
  min-height: 4px;
  transition: all 0.3s ease;
}

.modern-stats-card:hover .chart-bar {
  transform: scaleY(1.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (max-width: 480px) {
  .modern-stats-card {
    padding: 16px;
  }

  .stats-number {
    font-size: 28px;
  }

  .stats-icon {
    width: 48px;
    height: 48px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .modern-stats-card {
    background: rgba(31, 31, 31, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .stats-number,
  .stats-title {
    color: #ffffff;
  }

  .stats-subtitle {
    color: #a0a0a0;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modern-stats-card {
  animation: fadeInUp 0.6s ease-out;
}

.modern-stats-card:nth-child(1) { animation-delay: 0.1s; }
.modern-stats-card:nth-child(2) { animation-delay: 0.2s; }
.modern-stats-card:nth-child(3) { animation-delay: 0.3s; }
.modern-stats-card:nth-child(4) { animation-delay: 0.4s; }
</style>