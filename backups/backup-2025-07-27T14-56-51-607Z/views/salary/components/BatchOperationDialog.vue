<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    :close-on-click-modal="false"
  >
    <!-- 导入数据 -->
    <template v-if="type === 'import'">
      <el-upload
        ref="uploadRef"
        drag
        action="#"
        :auto-upload="false"
        :limit="1"
        accept=".xlsx,.xls"
        :on-change="handleFileChange"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            只支持 xlsx/xls 文件，文件大小不超过 10MB
            <el-button link type="primary" @click="downloadTemplate">下载模板</el-button>
          </div>
        </template>
      </el-upload>

      <el-divider   />

      <div class="import-tips">
        <h4>导入说明：</h4>
        <ol>
          <li>请使用系统提供的模板文件</li>
          <li>工号必须与系统中的员工工号一致</li>
          <li>金额字段支持数字格式，最多两位小数</li>
          <li>导入会覆盖当月已有数据，请谨慎操作</li>
        </ol>
      </div>
    </template>

    <!-- 批量计算 -->
    <template v-if="type === 'calculate'">
      <div class="selected-info">
        已选择 <span class="count">{{ selectedRows.length }}</span> 条记录进行批量计算
      </div>

      <el-form :model="calcForm" label-width="100px">
        <el-form-item label="计算项目">
          <el-checkbox-group v-model="calcForm.items">
            <el-checkbox label="tax">个人所得税</el-checkbox>
            <el-checkbox label="social">社保费用</el-checkbox>
            <el-checkbox label="fund">住房公积金</el-checkbox>
            <el-checkbox label="total">汇总金额</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="计算规则">
          <el-radio-group v-model="calcForm.rule">
            <el-radio label="standard">标准规则</el-radio>
            <el-radio label="custom">自定义规则</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="覆盖原值">
          <el-switch v-model="calcForm.override"  />
          <span class="tips">开启后将覆盖已有计算结果</span>
        </el-form-item>
      </el-form>

      <el-alert
        title="计算提示"
        type="info"
        show-icon
        :closable="false"
      >
        批量计算将根据最新的薪资标准和计算规则重新计算选中记录的相关项目
      </el-alert>
    </template>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          {{ confirmText }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'BatchOperationDialog'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, computed, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import { processBatchWithProgress } from '@/utils/batchProcessor'
import * as XLSX from 'xlsx'

// Props & Emits
interface Props {
  modelValue: boolean
  type: string
   
  selectedRows?: unknown[]
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue', 'success'])

// 对话框
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const dialogTitle = computed(() => {
  return props.type === 'import' ? '导入工资数据' : '批量计算'
})

const confirmText = computed(() => {
  return props.type === 'import' ? '开始导入' : '开始计算'
})

// 状态
const loading = ref(false)
const uploadRef = ref()
const uploadFile = ref<unknown>(null)

// 计算表单
const calcForm = reactive({
  items: ['tax', 'social', 'fund', 'total'],
  rule: 'standard',
  override: true
})

// 文件变化
   
const handleFileChange = (file: unknown) => {
  uploadFile.value = file
}

// 下载模板
const downloadTemplate = () => {
  // 创建模板数据
  const templateData = [
    ['工号', '姓名', '基本工资', '绩效奖金', '津贴', '扣款', '备注'],
    ['EMP001', '张三', '8000', '2000', '500', '0', ''],
    ['EMP002', '李四', '10000', '3000', '800', '200', '迟到扣款']
  ]
  
  // 创建工作簿
  const ws = XLSX.utils.aoa_to_sheet(templateData)
  const wb = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(wb, ws, '工资导入模板')
  
  // 下载文件
  XLSX.writeFile(wb, '工资导入模板.xlsx')
  ElMessage.success('模板下载成功')
}

// 读取Excel文件
   
const readExcelFile = (file: unknown): Promise<any[]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
   
    reader.onload = (e: unknown) => {
      try {
        const data = new Uint8Array(e.target.result)
        const workbook = XLSX.read(data, { type: 'array' })
        const worksheet = workbook.Sheets[workbook.SheetNames[0]]
        const jsonData = XLSX.utils.sheet_to_json(worksheet)
        resolve(jsonData)
      } catch (__error) {
        reject(error)
      }
    }
    reader.onerror = reject
    reader.readAsArrayBuffer(file.raw)
  })
}

// 确认操作
const handleConfirm = async () => {
  if (props.type === 'import') {
    if (!uploadFile.value) {
      ElMessage.warning('请选择要导入的文件')
      return
    }
    
    loading.value = true
    try {
      // 读取文件数据
      const fileData = await readExcelFile(uploadFile.value)
      
      // 使用批量处理器处理数据，避免超时
      const result = await processBatchWithProgress(
        fileData,
   
        async (item: unknown) => {
          // 模拟API调用
          await new Promise(resolve => setTimeout(resolve, 50))
          // 这里应该调用实际的导入API
          return { success: true, data: item }
        },
        '正在导入工资数据'
      )
      
      emit('success')
    } catch (__error) {
      ElMessage.error('导入失败，请检查文件格式')
    } finally {
      loading.value = false
    }
  } else {
    loading.value = true
    try {
      // 使用批量处理器进行计算，避免超时
      const result = await processBatchWithProgress(
        props.selectedRows || [],
   
        async (item: unknown) => {
          // 模拟复杂计算
          await new Promise(resolve => setTimeout(resolve, 100))
          
          // 根据选择的计算项进行计算
   
          const calculations: unknown = {}
          if (calcForm.items.includes('tax')) {
            calculations.tax = item.salary * 0.1 // 简化的税率计算
          }
          if (calcForm.items.includes('social')) {
            calculations.social = item.salary * 0.08 // 社保
          }
          if (calcForm.items.includes('fund')) {
            calculations.fund = item.salary * 0.05 // 公积金
          }
          if (calcForm.items.includes('total')) {
            calculations.total = item.salary - (calculations.tax || 0) - (calculations.social || 0) - (calculations.fund || 0)
          }
          
          return calculations
        },
        '正在批量计算'
      )
      
      emit('success')
    } catch (__error) {
      ElMessage.error('计算失败')
    } finally {
      loading.value = false
    }
  }
}
</script>

<style lang="scss" scoped>
.import-tips {
  h4 {
    margin-bottom: 10px;
    color: #303133;
  }
  
  ol {
    margin: 0;
    padding-left: 20px;
    
    li {
      margin-bottom: 5px;
      color: #606266;
    }
  }
}

.selected-info {
  margin-bottom: 20px;
  color: #606266;
  
  .count {
    color: #409eff;
    font-weight: bold;
    font-size: 18px;
  }
}

.tips {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

.dialog-footer {
  display: flex;
  gap: 10px;
}
</style>