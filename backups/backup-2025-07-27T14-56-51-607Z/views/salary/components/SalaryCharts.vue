<template>
  <div class="charts-section">
    <el-row :gutter="24">
      <el-col :span="isMobile ? 24 : 16">
        <div class="chart-container">
          <div class="chart-header">
            <h3>薪酬趋势分析</h3>
            <div class="chart-controls">
              <el-button-group size="small">
                <el-button
                  v-for="period in chartPeriods"
                  :key="period.key"
                  :type="activeChartPeriod === period.key ? 'primary' : 'default'"
                  @click="$emit('period-change', period.key)"
                >
                  {{ period.label }}
                </el-button>
              </el-button-group>
              <el-button size="small" @click="$emit('fullscreen', 'trend')">
                <el-icon><FullScreen /></el-icon>
              </el-button>
            </div>
          </div>
          <div class="chart-content" ref="salaryTrendChartRef" style="height: 300px;"></div>
        </div>
      </el-col>

      <el-col :span="isMobile ? 24 : 8">
        <div class="chart-container">
          <div class="chart-header">
            <h3>薪酬结构分布</h3>
            <el-button size="small" @click="$emit('structure-analysis')">
              <el-icon><DataAnalysis /></el-icon>
              详细分析
            </el-button>
          </div>
          <div class="chart-content" ref="salaryStructureChartRef" style="height: 300px;"></div>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="24" style="margin-top: 24px;">
      <el-col :span="isMobile ? 24 : 12">
        <div class="chart-container">
          <div class="chart-header">
            <h3>部门薪酬对比</h3>
            <el-select 
              :model-value="selectedDepartmentType" 
              @update:model-value="$emit('update:selectedDepartmentType', $event)"
              size="small" 
              style="width: 120px;"
            >
              <el-option label="学院" value="college"  />
              <el-option label="部门" value="department"  />
              <el-option label="岗位" value="position"  />
            </el-select>
          </div>
          <div class="chart-content" ref="departmentComparisonChartRef" style="height: 250px;"></div>
        </div>
      </el-col>

      <el-col :span="isMobile ? 24 : 12">
        <div class="chart-container">
          <div class="chart-header">
            <h3>预算执行监控</h3>
            <el-tag :type="budgetStatusType" size="small">
              {{ budgetStatusText }}
            </el-tag>
          </div>
          <div class="chart-content" ref="budgetMonitoringChartRef" style="height: 250px;"></div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
 
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import { FullScreen, DataAnalysis } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import type { ChartPeriod, SalaryStatistics } from '../types'

interface Props {
  chartPeriods: ChartPeriod[]
  activeChartPeriod: string
  selectedDepartmentType: string
  statistics: SalaryStatistics
  isMobile?: boolean
}

interface Emits {
  (e: 'period-change', period: string): void
  (e: 'fullscreen', chartType: string): void
  (e: 'structure-analysis'): void
  (e: 'update:selectedDepartmentType', type: string): void
}

const props = withDefaults(defineProps<Props>(), {
  isMobile: false
})

const emit = defineEmits<Emits>()

// 图表引用
const salaryTrendChartRef = ref<HTMLElement>()
const salaryStructureChartRef = ref<HTMLElement>()
const departmentComparisonChartRef = ref<HTMLElement>()
const budgetMonitoringChartRef = ref<HTMLElement>()

// 图表实例
let salaryTrendChart: echarts.ECharts | null = null
let salaryStructureChart: echarts.ECharts | null = null
let departmentComparisonChart: echarts.ECharts | null = null
let budgetMonitoringChart: echarts.ECharts | null = null

// 预算状态
const budgetStatusType = computed(() => {
  const rate = props.statistics.budgetUtilization
  if (rate >= 90) return 'danger'
  if (rate >= 80) return 'warning'
  return 'success'
})

const budgetStatusText = computed(() => {
  const rate = props.statistics.budgetUtilization
  if (rate >= 90) return '预算紧张'
  if (rate >= 80) return '预算正常'
  return '预算充足'
})

// 初始化薪酬趋势图表
const initSalaryTrendChart = () => {
  if (!salaryTrendChartRef.value) return

  salaryTrendChart = echarts.init(salaryTrendChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' }
    },
    legend: {
      data: ['平均薪酬', '总薪酬']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: [
      {
        type: 'value',
        name: 'HrHr平均薪酬(元)',
        position: 'left'
      },
      {
        type: 'value',
        name: '总薪酬(万元)',
        position: 'right'
      }
    ],
    series: [
      {
        name: '平均薪酬',
        type: 'line',
        data: [6800, 6900, 7100, 7000, 7200, 7300],
        smooth: true,
        itemStyle: { color: '#667eea' }
      },
      {
        name: '总薪酬',
        type: 'bar',
        yAxisIndex: 1,
        data: [850, 870, 890, 875, 900, 920],
        itemStyle: { color: '#43e97b' }
      }
    ]
  }
  salaryTrendChart.setOption(option)
}

// 初始化薪酬结构图表
const initSalaryStructureChart = () => {
  if (!salaryStructureChartRef.value) return

  salaryStructureChart = echarts.init(salaryStructureChartRef.value)
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '薪酬结构',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        data: [
          { value: 40, name: '基本工资', itemStyle: { color: '#667eea' } },
          { value: 25, name: '岗位工资', itemStyle: { color: '#43e97b' } },
          { value: 20, name: '绩效工资', itemStyle: { color: '#feca57' } },
          { value: 15, name: '津贴补贴', itemStyle: { color: '#ff6b6b' } }
        ]
      }
    ]
  }
  salaryStructureChart.setOption(option)
}

// 初始化部门对比图表
const initDepartmentComparisonChart = () => {
  if (!departmentComparisonChartRef.value) return

  departmentComparisonChart = echarts.init(departmentComparisonChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: ['计算机学院', '机械学院', '电气学院', '管理学院', '外语学院']
    },
    series: [
      {
        name: '平均薪酬',
        type: 'bar',
        data: [7500, 7200, 6800, 6500, 6200],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#667eea' },
            { offset: 1, color: '#764ba2' }
          ])
        }
      }
    ]
  }
  departmentComparisonChart.setOption(option)
}

// 初始化预算监控图表
const initBudgetMonitoringChart = () => {
  if (!budgetMonitoringChartRef.value) return

  budgetMonitoringChart = echarts.init(budgetMonitoringChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value',
      max: 100
    },
    series: [
      {
        name: '预算执行率',
        type: 'line',
        data: [75, 78, 82, 85, 88, 85],
        smooth: true,
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(255, 107, 107, 0.3)' },
            { offset: 1, color: 'rgba(255, 107, 107, 0.1)' }
          ])
        },
        itemStyle: { color: '#ff6b6b' }
      }
    ]
  }
  budgetMonitoringChart.setOption(option)
}

// 更新图表
const updateCharts = () => {
  nextTick(() => {
    salaryTrendChart?.resize()
    salaryStructureChart?.resize()
    departmentComparisonChart?.resize()
    budgetMonitoringChart?.resize()
  })
}

// 响应式调整
const handleResize = () => {
  salaryTrendChart?.resize()
  salaryStructureChart?.resize()
  departmentComparisonChart?.resize()
  budgetMonitoringChart?.resize()
}

// 监听变化
watch(() => props.activeChartPeriod, updateCharts)
watch(() => props.selectedDepartmentType, updateCharts)

onMounted(() => {
  nextTick(() => {
    initSalaryTrendChart()
    initSalaryStructureChart()
    initDepartmentComparisonChart()
    initBudgetMonitoringChart()
  })
  
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  salaryTrendChart?.dispose()
  salaryStructureChart?.dispose()
  departmentComparisonChart?.dispose()
  budgetMonitoringChart?.dispose()
})
</script>

<style scoped>
/* 图表区域 */
.charts-section {
  margin-bottom: 24px;
}

.chart-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  height: 100%;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.chart-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
}

.chart-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.chart-content {
  position: relative;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .chart-controls {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .chart-container {
    padding: 16px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .chart-container {
    background: rgba(31, 31, 31, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .chart-header h3 {
    color: #ffffff;
  }

  .chart-header {
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }
}
</style>