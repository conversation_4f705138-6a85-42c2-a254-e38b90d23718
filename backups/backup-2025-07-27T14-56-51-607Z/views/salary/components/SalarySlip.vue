<template>
  <div class="salary-slip">
    <!-- 工资条头部 -->
    <div class="slip-header">
      <div class="header-left">
        <img src="https://via.placeholder.com/120x40.png?text=Logo" alt="Logo" class="logo" />
        <div class="company-info">
          <h2>杭州科技职业技术学院</h2>
          <p>工资条</p>
        </div>
      </div>
      <div class="header-right">
        <div class="period">{{ period }} 月工资</div>
        <el-button type="primary" @click="handlePrint" :icon="Printer">打印</el-button>
        <el-button @click="handleDownload" :icon="Download">下载</el-button>
      </div>
    </div>

    <!-- 员工信息 -->
    <div class="employee-info">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="info-item">
            <span class="label">姓名：</span>
            <span class="value">{{ data.employeeName }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <span class="label">工号：</span>
            <span class="value">{{ data.employeeNo }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <span class="label">部门：</span>
            <span class="value">{{ data.departmentName }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <span class="label">岗位：</span>
            <span class="value">{{ data.positionName }}</span>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 薪资明细 -->
    <div class="salary-details">
      <el-row :gutter="20">
        <!-- 收入项目 -->
        <el-col :span="12">
          <div class="detail-section">
            <h3 class="section-title">
              <el-icon><CirclePlus /></el-icon>
              收入项目
            </h3>
            <div class="detail-items">
              <div class="detail-item" v-for="item in incomeItems" :key="item.code">
                <span class="item-name">{{ item.name }}</span>
                <span class="item-value income">¥{{ formatNumber(item.value) }}</span>
              </div>
              <div class="detail-item total">
                <span class="item-name">收入合计</span>
                <span class="item-value income">¥{{ formatNumber(totalIncome) }}</span>
              </div>
            </div>
          </div>
        </el-col>

        <!-- 扣除项目 -->
        <el-col :span="12">
          <div class="detail-section">
            <h3 class="section-title">
              <el-icon><Remove /></el-icon>
              扣除项目
            </h3>
            <div class="detail-items">
              <div class="detail-item" v-for="item in deductionItems" :key="item.code">
                <span class="item-name">{{ item.name }}</span>
                <span class="item-value deduction">¥{{ formatNumber(item.value) }}</span>
              </div>
              <div class="detail-item total">
                <span class="item-name">扣除合计</span>
                <span class="item-value deduction">¥{{ formatNumber(totalDeduction) }}</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 实发工资 -->
    <div class="net-salary-section">
      <div class="net-salary-box">
        <span class="label">实发工资</span>
        <span class="value">¥{{ formatNumber(data.netSalary) }}</span>
        <span class="value-cn">{{ numberToChinese(data.netSalary) }}</span>
      </div>
    </div>

    <!-- 考勤信息（可选） -->
    <div class="attendance-info" v-if="showAttendance">
      <h3 class="section-title">
        <el-icon><Calendar /></el-icon>
        考勤信息
      </h3>
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="info-item">
            <span class="label">应出勤天数：</span>
            <span class="value">{{ attendanceData.workDays }} 天</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <span class="label">实际出勤天数：</span>
            <span class="value">{{ attendanceData.attendanceDays }} 天</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <span class="label">请假天数：</span>
            <span class="value">{{ attendanceData.leaveDays }} 天</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <span class="label">加班时数：</span>
            <span class="value">{{ attendanceData.overtimeHours }} 小时</span>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 备注信息 -->
    <div class="remark-section" v-if="data.remark">
      <h3 class="section-title">
        <el-icon><InfoFilled /></el-icon>
        备注说明
      </h3>
      <p class="remark-content">{{ data.remark }}</p>
    </div>

    <!-- 底部信息 -->
    <div class="slip-footer">
      <p class="footer-text">
        本工资条由人事管理系统自动生成，如有疑问请联系人事处。
      </p>
      <p class="footer-text">
        生成时间：{{ generateTime }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { computed } from 'vue'
import { Printer, Download, CirclePlus, Remove, Calendar, InfoFilled } from '@element-plus/icons-vue'
import type { PersonalSalary } from '@/types/salary'

interface Props {
  data: PersonalSalary
  period: string
  showAttendance?: boolean
  attendanceData?: {
    workDays: number
    attendanceDays: number
    leaveDays: number
    overtimeHours: number
  }
}

const props = withDefaults(defineProps<Props>(), {
  showAttendance: false
})

// 收入项目
const incomeItems = computed(() => {
  const items = []
  const {data: _data} =  props
  
  if (data.baseSalary > 0) items.push({ name: 'HrHr基本工资', code: 'baseSalary', value: data.baseSalary })
  if (data.positionSalary > 0) items.push({ name: '岗位工资', code: 'positionSalary', value: data.positionSalary })
  if (data.rankSalary > 0) items.push({ name: '薪级工资', code: 'rankSalary', value: data.rankSalary })
  if (data.performanceSalary > 0) items.push({ name: '绩效工资', code: 'performanceSalary', value: data.performanceSalary })
  if (data.allowance > 0) items.push({ name: '津贴', code: 'allowance', value: data.allowance })
  if (data.subsidy > 0) items.push({ name: '补贴', code: 'subsidy', value: data.subsidy })
  if (data.bonus > 0) items.push({ name: '奖金', code: 'bonus', value: data.bonus })
  if (data.otherIncome > 0) items.push({ name: '其他收入', code: 'otherIncome', value: data.otherIncome })
  
  return items
})

// 扣除项目
const deductionItems  i < intStr.length; i++) {
      const digit = parseInt(intStr[i])
      const unit = units[intStr.length - 1 - i]
      if (digit !== 0) {
        result += digits[digit] + unit
      } else if (result[result.length - 1] !== '零') {
        result += '零'
      }
    }
  }
  
  result += '元'
  
  // 处理小数部分
  if (decimal === 0) {
    result += '整'
  } else {
    const jiao = Math.floor(decimal / 10)
    const fen = decimal % 10
    if (jiao > 0) result += digits[jiao] + '角'
    if (fen > 0) result += digits[fen] + '分'
  }
  
  return result
}

// 打印
const handlePrint = () => {
  window.print()
}

// 下载
const handleDownload = () => {
  // 实际实现时可以生成PDF或图片
  console.log('下载工资条')
}
</script>

<style lang="scss" scoped>
.salary-slip {
  background: #fff;
  padding: 30px;
  max-width: 800px;
  margin: 0 auto;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .slip-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid #409eff;
    padding-bottom: 20px;
    margin-bottom: 20px;

    .header-left {
      display: flex;
      align-items: center;

      .logo {
        width: 60px;
        height: 60px;
        margin-right: 20px;
      }

      .company-info {
        h2 {
          margin: 0 0 5px 0;
          color: #303133;
        }

        p {
          margin: 0;
          color: #606266;
          font-size: 16px;
        }
      }
    }

    .header-right {
      display: flex;
      align-items: center;
      gap: 20px;

      .period {
        font-size: 18px;
        font-weight: bold;
        color: #409eff;
      }
    }
  }

  .employee-info {
    background: #f5f7fa;
    padding: 15px 20px;
    border-radius: 4px;
    margin-bottom: 20px;

    .info-item {
      .label {
        color: #909399;
      }

      .value {
        color: #303133;
        font-weight: 500;
      }
    }
  }

  .salary-details {
    margin-bottom: 20px;

    .detail-section {
      .section-title {
        display: flex;
        align-items: center;
        gap: 5px;
        margin: 0 0 15px 0;
        color: #303133;
        font-size: 16px;
      }

      .detail-items {
        .detail-item {
          display: flex;
          justify-content: space-between;
          padding: 8px 0;
          border-bottom: 1px dashed #ebeef5;

          &:last-child {
            border-bottom: none;
          }

          &.total {
            border-top: 2px solid #e4e7ed;
            margin-top: 10px;
            padding-top: 15px;
            font-weight: bold;
          }

          .item-name {
            color: #606266;
          }

          .item-value {
            font-weight: 500;

            &.income {
              color: #67c23a;
            }

            &.deduction {
              color: #f56c6c;
            }
          }
        }
      }
    }
  }

  .net-salary-section {
    background: linear-gradient(135deg, #409eff 0%, #53a8ff 100%);
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;

    .net-salary-box {
      text-align: center;
      color: #fff;

      .label {
        display: block;
        font-size: 16px;
        margin-bottom: 10px;
      }

      .value {
        display: block;
        font-size: 36px;
        font-weight: bold;
        margin-bottom: 5px;
      }

      .value-cn {
        display: block;
        font-size: 14px;
        opacity: 0.9;
      }
    }
  }

  .attendance-info,
  .remark-section {
    margin-bottom: 20px;

    .section-title {
      display: flex;
      align-items: center;
      gap: 5px;
      margin: 0 0 15px 0;
      color: #303133;
      font-size: 16px;
    }

    .info-item {
      .label {
        color: #909399;
      }

      .value {
        color: #303133;
        font-weight: 500;
      }
    }

    .remark-content {
      color: #606266;
      line-height: 1.6;
      margin: 0;
    }
  }

  .slip-footer {
    text-align: center;
    border-top: 1px solid #ebeef5;
    padding-top: 20px;
    margin-top: 30px;

    .footer-text {
      color: #909399;
      font-size: 12px;
      margin: 5px 0;
    }
  }
}

// 打印样式
@media print {
  .salary-slip {
    box-shadow: none;

    .slip-header {
      .header-right {
        .el-button {
          display: none;
        }
      }
    }
  }
}
</style>