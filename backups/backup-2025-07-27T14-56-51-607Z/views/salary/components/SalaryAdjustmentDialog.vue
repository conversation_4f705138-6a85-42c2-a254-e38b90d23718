<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      :disabled="readonly"
      label-width="120px"
    >
      <!-- 基本信息 -->
      <el-divider content-position="left">基本信息</el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="申请人" prop="employeeId">
            <el-select
              v-model="formData.employeeId"
              placeholder="请选择申请人"
              filterable
              @change="handleEmployeeChange"
            >
              <el-option
                v-for="emp in employeeList"
                :key="emp.id"
                :label="`${emp.name} (${emp.employeeNo})`"
                :value="emp.id"
               />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="部门">
            <el-input v-model="formData.department" readonly   />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="当前薪资">
            <el-input v-model="formData.currentSalary" readonly>
              <template #prefix>¥</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="当前岗位">
            <el-input v-model="formData.currentPosition" readonly   />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 调薪信息 -->
      <el-divider content-position="left">调薪信息</el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="调薪类型" prop="adjustType">
            <el-select v-model="formData.adjustType" placeholder="请选择">
              <el-option label="晋升调薪" value="promotion"  />
              <el-option label="年度调薪" value="annual"  />
              <el-option label="特殊调薪" value="special"  />
              <el-option label="转正调薪" value="probation"  />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="生效日期" prop="effectiveDate">
            <el-date-picker
              v-model="formData.effectiveDate"
              type="date"
              placeholder="选择生效日期"
              value-format="YYYY-MM-DD"
              :disabled-date="disabledDate"
             />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="调整方式" prop="adjustMethod">
            <el-radio-group v-model="formData.adjustMethod" @change="handleMethodChange">
              <el-radio label="amount">按金额</el-radio>
              <el-radio label="percentage">按比例</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item 
            :label="formData.adjustMethod === 'amount' ? '调整金额' : '调整比例'" 
            prop="adjustValue"
          >
            <el-input-number
              v-model="formData.adjustValue"
              :min="formData.adjustMethod === 'amount' ? -formData.currentSalary : -100"
              :max="formData.adjustMethod === 'amount' ? 100000 : 100"
              :step="formData.adjustMethod === 'amount' ? 100 : 1"
              @change="calculateAdjustedSalary"
              />
            <span v-if="formData.adjustMethod === 'percentage'" style="margin-left: 5px">%</span>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="调整后薪资">
            <el-input v-model="formData.adjustedSalary" readonly>
              <template #prefix>¥</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="调整幅度">
            <el-input v-model="adjustInfo" readonly   />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 调薪原因 -->
      <el-divider content-position="left">调薪原因</el-divider>
      <el-form-item label="调薪原因" prop="reason">
        <el-input
          v-model="formData.reason"
          type="textarea"
          :rows="4"
          placeholder="请详细说明调薪原因"
          maxlength="500"
          show-word-limit
          />
      </el-form-item>

      <!-- 附件 -->
      <el-form-item label="附件材料">
        <el-upload
          v-model:file-list="formData.attachments"
          action="#"
          :auto-upload="false"
          :disabled="readonly"
          multiple
        >
          <el-button type="primary">选择文件</el-button>
          <template #tip>
            <div class="el-upload__tip">支持上传PDF、Word、图片等格式文件</div>
          </template>
        </el-upload>
      </el-form-item>

      <!-- 审批流程预览 -->
      <el-divider content-position="left">审批流程</el-divider>
      <div class="process-preview">
        <el-steps :active="0" align-center>
          <el-step title="申请人" description="发起申请"  />
          <el-step title="部门负责人" description="部门审批"  />
          <el-step title="人事部门" description="人事审核"  />
          <el-step title="财务部门" description="财务审核"  />
          <el-step title="校领导" description="最终审批"  />
        </el-steps>
      </div>
    </el-form>

    <template #footer>
      <span class="dialog-footer" v-if="!readonly">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSaveDraft">保存草稿</el-button>
        <el-button type="primary" @click="handleSubmit">提交申请</el-button>
      </span>
      <span class="dialog-footer" v-else>
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

// Props & Emits
interface Props {
  modelValue: boolean
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data?: unknown
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue', 'success'])

// 对话框
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const dialogTitle = computed(() => {
  if (props.data?.readonly) return '查看调薪申请'
  return props.data?.id ? '编辑调薪申请' : '新建调薪申请'
})

const readonly = computed(() => props.data?.readonly || false)

// 表单
const formRef = ref<FormInstance>()
const formData = ref({
  employeeId: '',
  department: '',
  currentSalary: 0,
  currentPosition: '',
  adjustType: '',
  effectiveDate: '',
  adjustMethod: 'amount',
  adjustValue: 0,
  adjustedSalary: 0,
  reason: '',
  attachments: []
})

// 计算调整信息
const adjustInfo = computed(() => {
  const amount = formData.value.adjustedSalary - formData.value.currentSalary
  const percentage = formData.value.currentSalary > 0 
    ? ((amount / formData.value.currentSalary) * 100).toFixed(2)
    : 0
  
  const sign = amount > 0 ? '+' : ''
  return `${sign}¥${Math.abs(amount).toLocaleString()} (${sign}${percentage}%)`
})

// 员工列表
const employeeList = ref([
  { id: 'EMP001', name: 'HrHr张三', employeeNo: 'T2024001', department: '计算机系', position: '副教授', salary: 15000 },
  { id: 'EMP002', name: '李四', employeeNo: 'T2024002', department: '数学系', position: '讲师', salary: 12000 },
  { id: 'EMP003', name: '王五', employeeNo: 'T2024003', department: '物理系', position: '教授', salary: 20000 }
])

// 表单规则
const rules: FormRules = {
  employeeId: [{ required: true, message: '请选择申请人', trigger: 'change' }],
  adjustType: [{ required: true, message: '请选择调薪类型', trigger: 'change' }],
  effectiveDate: [{ required: true, message: '请选择生效日期', trigger: 'change' }],
  adjustValue: [{ required: true, message: '请输入调整值', trigger: 'blur' }],
  reason: [{ required: true, message: '请输入调薪原因', trigger: 'blur' }]
}

// 监听数据变化
watch(() => props.data, (newData) => {
  if (newData) {
    Object.assign(formData.value, newData)
  }
}, { immediate: true })

// 选择员工
const handleEmployeeChange = (employeeId: string) => {
  const employee = employeeList.value.find(emp => emp.id === employeeId)
  if (employee) {
    formData.value.department = employee.department
    formData.value.currentSalary = employee.salary
    formData.value.currentPosition = employee.position
    calculateAdjustedSalary()
  }
}

// 调整方式变化
const handleMethodChange = () => {
  formData.value.adjustValue = 0
  calculateAdjustedSalary()
}

// 计算调整后薪资
const calculateAdjustedSalary = () => {
  const {currentSalary: _currentSalary, adjustMethod: _adjustMethod, adjustValue: _adjustValue} =  formData.value
  
  if (adjustMethod 
  background-color: #f5f7fa;
  border-radius: 4px;
}

.dialog-footer {
  display: flex;
  gap: 10px;
}

:deep(.el-upload__tip) {
  color: #909399;
  font-size: 12px;
}
</style>