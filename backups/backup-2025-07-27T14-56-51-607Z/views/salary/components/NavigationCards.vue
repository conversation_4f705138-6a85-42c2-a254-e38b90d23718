<template>
  <div class="function-navigation">
    <div class="nav-grid" :class="{ 'mobile-grid': isMobile }">
      <div
        v-for="nav in items"
        :key="nav.id"
        class="modern-nav-card"
        :class="[`nav-${nav.type}`, { 'clickable': nav.clickable }]"
        @click="nav.clickable && $emit('navigate', nav.id)"
        role="button"
        :tabindex="nav.clickable ? 0 : -1"
        :aria-label="`${nav.title}: ${nav.description}`"
        @keydown.enter="nav.clickable && $emit('navigate', nav.id)"
        @keydown.space.prevent="nav.clickable && $emit('navigate', nav.id)"
      >
        <div class="nav-header">
          <div class="nav-icon" :style="{ background: nav.gradient }">
            <el-icon :size="28">
              <component :is="nav.icon" />
            </el-icon>
          </div>
          <div class="nav-badge" v-if="nav.badge">
            <span class="badge-text">{{ nav.badge }}</span>
          </div>
        </div>

        <div class="nav-content">
          <h3 class="nav-title">{{ nav.title }}</h3>
          <p class="nav-description">{{ nav.description }}</p>
          <div class="nav-stats" v-if="nav.stats">
            <span class="stats-value">{{ nav.stats.value }}</span>
            <span class="stats-label">{{ nav.stats.label }}</span>
          </div>
        </div>

        <div class="nav-footer">
          <el-icon class="nav-arrow">
            <ArrowRight />
          </el-icon>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'NavigationCards'
})
 
import { ArrowRight } from '@element-plus/icons-vue'
import type { NavigationItem } from '../types'

interface Props {
  items: NavigationItem[]
  isMobile?: boolean
}

interface Emits {
  (e: 'navigate', id: string): void
}

withDefaults(defineProps<Props>(), {
  isMobile: false
})

defineEmits<Emits>()
</script>

<style scoped>
/* 功能导航 */
.function-navigation {
  margin-bottom: 24px;
}

.nav-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.mobile-grid {
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 16px;
}

.modern-nav-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.modern-nav-card.clickable {
  cursor: pointer;
}

.modern-nav-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.nav-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.nav-icon {
  width: 64px;
  height: 64px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.nav-badge {
  background: #ff4757;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
}

.nav-content h3 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.nav-description {
  margin: 0 0 12px 0;
  color: #7f8c8d;
  font-size: 14px;
  line-height: 1.5;
}

.nav-stats {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.stats-value {
  font-size: 20px;
  font-weight: 700;
  color: #2c3e50;
}

.stats-label {
  font-size: 12px;
  color: #95a5a6;
}

.nav-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

.nav-arrow {
  color: #bdc3c7;
  transition: all 0.3s ease;
}

.modern-nav-card:hover .nav-arrow {
  color: #3498db;
  transform: translateX(4px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .modern-nav-card {
    padding: 16px;
  }

  .nav-icon {
    width: 48px;
    height: 48px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .modern-nav-card {
    background: rgba(31, 31, 31, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .nav-content h3 {
    color: #ffffff;
  }

  .nav-description {
    color: #a0a0a0;
  }

  .stats-value {
    color: #ffffff;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modern-nav-card {
  animation: fadeInUp 0.6s ease-out;
}

.modern-nav-card:nth-child(1) { animation-delay: 0.1s; }
.modern-nav-card:nth-child(2) { animation-delay: 0.2s; }
.modern-nav-card:nth-child(3) { animation-delay: 0.3s; }
.modern-nav-card:nth-child(4) { animation-delay: 0.4s; }
</style>