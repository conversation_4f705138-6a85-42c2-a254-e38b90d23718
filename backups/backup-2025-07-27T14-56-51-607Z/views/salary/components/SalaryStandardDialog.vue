<template>
  <el-dialog
    :model-value="modelValue"
    :title="title"
    width="900px"
    @update:model-value="$emit('update:modelValue', $event)"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="薪资结构" prop="structureId">
            <el-select 
              v-model="formData.structureId" 
              placeholder="请选择薪资结构"
              style="width: 100%"
              @change="handleStructureChange"
            >
              <el-option 
                v-for="item in structureList" 
                :key="item.id"
                :label="item.name"
                :value="item.id"
               />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="适用岗位" prop="positionId">
            <el-select 
              v-model="formData.positionId" 
              placeholder="请选择岗位"
              style="width: 100%"
              @change="handlePositionChange"
            >
              <el-option 
                v-for="item in positionList" 
                :key="item.id"
                :label="item.name"
                :value="item.id"
               />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="岗位级别" prop="levelId">
            <el-select 
              v-model="formData.levelId" 
              placeholder="请选择岗位级别"
              style="width: 100%"
            >
              <el-option 
                v-for="item in levelList" 
                :key="item.id"
                :label="item.name"
                :value="item.id"
               />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="生效日期" prop="effectiveDate">
            <el-date-picker
              v-model="formData.effectiveDate"
              type="date"
              placeholder="选择生效日期"
              style="width: 100%"
              value-format="YYYY-MM-DD"
              :disabled-date="disabledDate"
             />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="薪资项目" prop="items">
        <div class="salary-items-setting">
          <el-alert 
            v-if="!structureItems.length && formData.structureId"
            title="该薪资结构暂无配置项目"
            type="warning"
            :closable="false"
           />
          
          <div v-else class="items-list">
            <div 
              v-for="(item, index) in formData.items" 
              :key="item.itemId"
              class="salary-item-setting"
            >
              <el-card shadow="never">
                <div class="item-header">
                  <div class="item-info">
                    <el-tag type="primary">{{ item.itemName }}</el-tag>
                    <el-tag size="small">{{ item.itemCode }}</el-tag>
                  </div>
                </div>
                
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item 
                      :label="`标准值`" 
                      :prop="`items.${index}.standardValue`"
                      :rules="[
                        { required: true, message: '请输入标准值', trigger: 'blur' },
                        { type: 'number', message: '请输入数字', trigger: 'blur' }
                      ]"
                    >
                      <el-input-number
                        v-model="item.standardValue"
                        :precision="2"
                        :min="0"
                        :max="999999"
                        style="width: 100%"
                        />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="最小值">
                      <el-input-number
                        v-model="item.minValue"
                        :precision="2"
                        :min="0"
                        :max="item.standardValue"
                        style="width: 100%"
                        placeholder="可选"
                        />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="最大值">
                      <el-input-number
                        v-model="item.maxValue"
                        :precision="2"
                        :min="item.standardValue"
                        :max="999999"
                        style="width: 100%"
                        placeholder="可选"
                        />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-card>
            </div>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="备注说明" prop="remark">
        <el-input 
          v-model="formData.remark" 
          type="textarea"
          :rows="3"
          placeholder="请输入备注说明"
          />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="loading">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
 
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import type { SalaryStandard, SalaryStandardItem, SalaryStructure } from '@/types/salary'

interface Props {
  modelValue: boolean
  title: string
  data?: SalaryStandard | null
  structureList: SalaryStructure[]
   
  positionList: unknown[]
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', data: SalaryStandard): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const loading = ref(false)

// 表单数据
const formData = reactive<SalaryStandard>({
  id: '',
  structureId: '',
  structureName: '',
  positionId: '',
  positionName: '',
  levelId: '',
  levelName: '',
  items: [],
  effectiveDate: '',
  status: 'pending',
  remark: ''
})

// 岗位级别列表（模拟数据）
const levelList = ref([
  { id: 'L1', name: 'HrHr初级' },
  { id: 'L2', name: '中级' },
  { id: 'L3', name: '高级' },
  { id: 'L4', name: '资深' },
  { id: 'L5', name: '专家' }
])

// 当前选中薪资结构的项目
const structureItems = computed(() => {
  if (!formData.structureId) return []
  const structure = props.structureList.find(s => s.id === formData.structureId)
  return structure?.items || []
})

// 表单验证规则
const rules: FormRules = {
  structureId: [
    { required: true, message: '请选择薪资结构', trigger: 'change' }
  ],
  positionId: [
    { required: true, message: '请选择岗位', trigger: 'change' }
  ],
  levelId: [
    { required: true, message: '请选择岗位级别', trigger: 'change' }
  ],
  effectiveDate: [
    { required: true, message: '请选择生效日期', trigger: 'change' }
  ]
}

// 监听数据变化
watch(() => props.data, (newData) => {
  if (newData) {
    Object.assign(formData, {
      ...newData,
      items: newData.items?.map(item => ({ ...item })) || []
    })
  } else {
    // 重置表单
    formData.id = ''
    formData.structureId = ''
    formData.structureName = ''
    formData.positionId = ''
    formData.positionName = ''
    formData.levelId = ''
    formData.levelName = ''
    formData.items = []
    formData.effectiveDate = new Date().toISOString().split('T')[0]
    formData.status = 'pending'
    formData.remark = ''
  }
}, { immediate: true })

// 薪资结构变化处理
const handleStructureChange = (structureId: string) => {
  const structure = props.structureList.find(s => s.id === structureId)
  if (structure) {
    formData.structureName = structure.name
    // 初始化薪资项目
    formData.items = structure.items.map(item => ({
      itemId: item.id || item.tempId || '',
      itemName: item.name,
      itemCode: item.code,
      standardValue: 0,
      minValue: undefined,
      maxValue: undefined
    }))
  }
}

// 岗位变化处理
const handlePositionChange = (positionId: string) => {
  const position = props.positionList.find(p => p.id === positionId)
  if (position) {
    formData.positionName = position.name
  }
}

// 禁用过去的日期
const disabledDate = (date: Date) => {
  return date < new Date(new Date().setHours(0, 0, 0, 0))
}

// 关闭对话框
const handleClose = () => {
  formRef.value?.resetFields()
  emit('update:modelValue', false)
}

// 确认提交
const handleConfirm = async () => {
  try {
    await formRef.value?.validate()
    
    // 验证薪资项目
    if (formData.items.length === 0) {
      ElMessage.error('请先选择薪资结构')
      return
    }
    
    // 验证标准值
    const invalidItems = formData.items.filter(item => !item.standardValue || item.standardValue <= 0)
    if (invalidItems.length > 0) {
      ElMessage.error('请填写所有项目的标准值')
      return
    }
    
    // 更新级别名称
    const level = levelList.value.find(l => l.id === formData.levelId)
    if (level) {
      formData.levelName = level.name
    }
    
    loading.value = true
    // 提交数据
    emit('confirm', { ...formData })
    
    setTimeout(() => {
      loading.value = false
      handleClose()
    }, 1000)
  } catch (__error) {
    console.error('表单验证失败', error)
  }
}
</script>

<style lang="scss" scoped>
.salary-items-setting {
  width: 100%;

  .items-list {
    max-height: 400px;
    overflow-y: auto;
  }

  .salary-item-setting {
    margin-bottom: 10px;

    .item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      .item-info {
        display: flex;
        gap: 8px;
        align-items: center;
      }
    }

    :deep(.el-card__body) {
      padding: 15px;
    }

    :deep(.el-form-item) {
      margin-bottom: 12px;
    }
  }
}
</style>