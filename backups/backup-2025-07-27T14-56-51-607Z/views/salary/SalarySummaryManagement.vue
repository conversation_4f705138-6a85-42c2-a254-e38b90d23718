<template>
  <div class="salary-summary-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>薪酬汇总导出</h2>
      <p>按组织和时间维度汇总薪酬数据并导出</p>
    </div>

    <!-- 汇总条件设置 -->
    <el-card class="summary-config-card" shadow="never">
      <template #header>
        <span>汇总条件设置</span>
      </template>
      <el-form ref="summaryFormRef" :model="summaryForm" :rules="summaryRules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="汇总维度" prop="summaryDimension">
              <el-select v-model="summaryForm.summaryDimension" placeholder="选择汇总维度" style="width: 100%">
                <el-option label="按组织汇总" value="ORGANIZATION"  />
                <el-option label="按月份汇总" value="MONTH"  />
                <el-option label="按组织+月份" value="ORGANIZATION_MONTH"  />
                <el-option label="按岗位汇总" value="POSITION"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="组织范围" prop="organizationId">
              <el-tree-select
                v-model="summaryForm.organizationId"
                :data="organizationTree"
                :props="{ label: 'name', value: 'id' }"
                placeholder="选择组织范围"
                check-strictly
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="时间范围" prop="dateRange">
              <el-date-picker
                v-model="summaryForm.dateRange"
                type="monthrange"
                range-separator="至"
                start-placeholder="开始月份"
                end-placeholder="结束月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item>
              <el-button type="primary" @click="handleGenerateSummary" :loading="generateLoading">
                <el-icon><Document /></el-icon>
                生成汇总
              </el-button>
              <el-button @click="handleReset">
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
              <el-button type="success" @click="handleExportSummary" :disabled="!summaryData.length">
                <el-icon><Download /></el-icon>
                导出Excel
              </el-button>
              <el-button type="warning" @click="handleExportDetailed" :disabled="!summaryData.length">
                <el-icon><Download /></el-icon>
                导出明细
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 汇总结果展示 -->
    <el-card v-if="summaryData.length > 0" class="summary-result-card" shadow="never">
      <template #header>
        <div class="result-header">
          <span>汇总结果</span>
          <div class="result-actions">
            <el-button size="small" @click="handleRefreshSummary">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button size="small" @click="handlePreviewExport">
              <el-icon><View /></el-icon>
              预览
            </el-button>
          </div>
        </div>
      </template>

      <!-- 汇总统计 -->
      <div class="summary-stats">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-number">{{ summaryStats.totalOrganizations }}</div>
              <div class="stat-label">汇总组织数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-number">{{ summaryStats.totalEmployees }}</div>
              <div class="stat-label">总人数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-number">{{ formatCurrency(summaryStats.totalGrossSalary) }}</div>
              <div class="stat-label">应发总额</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-number">{{ formatCurrency(summaryStats.totalNetSalary) }}</div>
              <div class="stat-label">实发总额</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 汇总数据表格 -->
      <el-table
        :data="summaryData"
        stripe
        border
        style="width: 100%"
        :summary-method="getSummaries"
        show-summary
      >
        <el-table-column prop="organizationName" label="组织名称" width="200" show-overflow-tooltip  />
        <el-table-column prop="salaryMonth" label="月份" width="100"  />
        <el-table-column prop="employeeCount" label="人数" width="80"  />
        <el-table-column prop="totalGrossSalary" label="应发总额" width="120">
          <template #default="scope">
            <span class="amount-text gross">{{ formatCurrency(scope.row.totalGrossSalary) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="totalNetSalary" label="实发总额" width="120">
          <template #default="scope">
            <span class="amount-text net">{{ formatCurrency(scope.row.totalNetSalary) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="totalDeductions" label="扣款总额" width="120">
          <template #default="scope">
            <span class="amount-text deduction">{{ formatCurrency(scope.row.totalDeductions) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="averageGrossSalary" label="平均应发" width="120">
          <template #default="scope">
            {{ formatCurrency(scope.row.averageGrossSalary) }}
          </template>
        </el-table-column>
        <el-table-column prop="averageNetSalary" label="平均实发" width="120">
          <template #default="scope">
            {{ formatCurrency(scope.row.averageNetSalary) }}
          </template>
        </el-table-column>
        <el-table-column prop="maxSalary" label="最高薪酬" width="120">
          <template #default="scope">
            {{ formatCurrency(scope.row.maxSalary) }}
          </template>
        </el-table-column>
        <el-table-column prop="minSalary" label="最低薪酬" width="120">
          <template #default="scope">
            {{ formatCurrency(scope.row.minSalary) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" link @click="handleViewDetails(scope.row)">
              查看明细
            </el-button>
            <el-button size="small" type="success" link @click="handleExportRow(scope.row)">
              导出
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 图表分析 -->
    <el-card v-if="summaryData.length > 0" class="chart-card" shadow="never">
      <template #header>
        <span>图表分析</span>
      </template>
      <el-tabs v-model="activeChartTab">
        <el-tab-pane label="薪酬分布" name="distribution">
          <div ref="distributionChartRef" class="chart" style="height: 400px;"></div>
        </el-tab-pane>
        <el-tab-pane label="组织对比" name="comparison">
          <div ref="comparisonChartRef" class="chart" style="height: 400px;"></div>
        </el-tab-pane>
        <el-tab-pane label="趋势分析" name="trend">
          <div ref="trendChartRef" class="chart" style="height: 400px;"></div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 导出历史 -->
    <el-card class="export-history-card" shadow="never">
      <template #header>
        <span>导出历史</span>
      </template>
      <el-table :data="exportHistory" stripe style="width: 100%">
        <el-table-column prop="exportTime" label="导出时间" width="180"  />
        <el-table-column prop="exportType" label="导出类型" width="120"  />
        <el-table-column prop="organizationName" label="组织范围" width="200"  />
        <el-table-column prop="dateRange" label="时间范围" width="200"  />
        <el-table-column prop="recordCount" label="记录数" width="100"  />
        <el-table-column prop="fileSize" label="文件大小" width="100"  />
        <el-table-column prop="exportBy" label="导出人" width="100"  />
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button size="small" type="primary" link @click="handleDownloadHistory(scope.row)">
              下载
            </el-button>
            <el-button size="small" type="danger" link @click="handleDeleteHistory(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Document,
  Refresh,
  Download,
  View
} from '@element-plus/icons-vue'
import { salaryApi, type SalarySummary } from '@/api/salary'
// 响应式数据
const summaryFormRef = ref()
const generateLoading = ref(false)
const summaryData = ref<SalarySummary[]>([])
const organizationTree = ref<unknown[]>([])
const activeChartTab = ref('distribution')
const exportHistory = ref<unknown[]>([])

// 图表引用
const distributionChartRef = ref()
const comparisonChartRef = ref()
const trendChartRef = ref()

// 汇总表单
const summaryForm = reactive({
  summaryDimension: 'ORGANIZATION',
  organizationId: undefined,
  dateRange: []
})

// 表单验证规则
const summaryRules = {
  summaryDimension: [
    { required: true, message: '请选择汇总维度', trigger: 'change' }
  ],
  dateRange: [
    { required: true, message: '请选择时间范围', trigger: 'change' }
  ]
}

// 汇总统计
const summaryStats = computed(() => {
  if (!summaryData.value.length) {
    return {
      totalOrganizations: 0,
      totalEmployees: 0,
      totalGrossSalary: 0,
      totalNetSalary: 0
    }
  }

  return {
    totalOrganizations: summaryData.value.length,
    totalEmployees: summaryData.value.reduce((sum, item) => sum + item.employeeCount, 0),
    totalGrossSalary: summaryData.value.reduce((sum, item) => sum + item.totalGrossSalary, 0),
    totalNetSalary: summaryData.value.reduce((sum, item) => sum + item.totalNetSalary, 0)
  }
})

// 生成汇总
const handleGenerateSummary = async () => {
  try {
    await summaryFormRef.value.validate()
    
    generateLoading.value = true
    
    const params = {
      organizationId: summaryForm.organizationId,
      salaryMonthStart: summaryForm.dateRange[0],
      salaryMonthEnd: summaryForm.dateRange[1]
    }
    
    const result = await salaryApi.getSalarySummary(params)
    summaryData.value = result
    
    // 渲染图表
    nextTick(() => {
      renderCharts()
    })
    
    ElMessage.success('汇总生成成功')
  } catch (__error) {
    ElMessage.error('生成汇总失败')
  } finally {
    generateLoading.value = false
  }
}

// 重置表单
const handleReset = () => {
  Object.assign(summaryForm, {
    summaryDimension: 'ORGANIZATION',
    organizationId: undefined,
    dateRange: []
  })
  summaryData.value = []
}

// 导出汇总
const handleExportSummary = async () => {
  try {
    const params = {
      organizationId: summaryForm.organizationId,
      salaryMonth: summaryForm.dateRange[0],
      format: 'SUMMARY' as unknown
    }
    
    const blob = await salaryApi.exportSalarySummaryExcel(params)
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `薪酬汇总_${new Date().toISOString().slice(0, 10)}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('导出成功')
  } catch (__error) {
    ElMessage.error('导出失败')
  }
}

// 导出明细
const handleExportDetailed = async () => {
  try {
    const params = {
      organizationId: summaryForm.organizationId,
      salaryMonth: summaryForm.dateRange[0],
      format: 'DETAILED' as unknown
    }
    
    const blob = await salaryApi.exportSalarySummaryExcel(params)
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `薪酬明细_${new Date().toISOString().slice(0, 10)}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('导出成功')
  } catch (__error) {
    ElMessage.error('导出失败')
  }
}

// 刷新汇总
const handleRefreshSummary = () => {
  handleGenerateSummary()
}

// 预览导出
const handlePreviewExport = () => {
  ElMessage.info('预览功能开发中...')
}

// 查看明细
const handleViewDetails = (summary: SalarySummary) => {
  ElMessage.info('查看明细功能开发中...')
}

// 导出单行
const handleExportRow = (summary: SalarySummary) => {
  ElMessage.info('单行导出功能开发中...')
}

// 下载历史文件
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleDownloadHistory = (history: unknown) => {
  ElMessage.info('下载历史文件功能开发中...')
}

// 删除历史记录
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleDeleteHistory = (history: unknown) => {
  ElMessage.info('删除历史记录功能开发中...')
}

// 表格合计行
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const getSummaries = (param: unknown) => {
  const {columns: _columns, data: _data} =  param
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const sums: unknown[] 
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.summary-config-card {
  margin-bottom: 20px;
}

.summary-result-card {
  margin-bottom: 20px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-actions {
  display: flex;
  gap: 8px;
}

.summary-stats {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 20px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.amount-text {
  font-weight: 600;
}

.amount-text.gross {
  color: #409eff;
}

.amount-text.net {
  color: #67c23a;
}

.amount-text.deduction {
  color: #f56c6c;
}

.chart-card {
  margin-bottom: 20px;
}

.chart {
  width: 100%;
}

.export-history-card {
  margin-bottom: 20px;
}
</style>
