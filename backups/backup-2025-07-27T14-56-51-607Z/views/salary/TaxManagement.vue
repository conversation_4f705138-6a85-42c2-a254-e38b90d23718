<template>
  <div class="tax-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon><Notebook /></el-icon>
        税务管理
      </h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleCalculate">
          <el-icon><Money /></el-icon>
          税额计算
        </el-button>
        <el-button @click="handleBatchProcess">
          <el-icon><Operation /></el-icon>
          批量处理
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出报表
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="员工姓名">
          <el-input
            v-model="searchForm.employeeName"
            placeholder="请输入员工姓名"
            clearable
            style="width: 150px"
            />
        </el-form-item>
        <el-form-item label="税务期间">
          <el-date-picker
            v-model="searchForm.taxPeriod"
            type="month"
            placeholder="请选择税务期间"
            style="width: 200px"
           />
        </el-form-item>
        <el-form-item label="税务类型">
          <el-select
            v-model="searchForm.taxType"
            placeholder="请选择税务类型"
            clearable
            style="width: 150px"
          >
            <el-option label="个人所得税" value="personal"  />
            <el-option label="专项附加扣除" value="deduction"  />
            <el-option label="年终奖税" value="bonus"  />
            <el-option label="其他税费" value="other"  />
          </el-select>
        </el-form-item>
        <el-form-item label="处理状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="待计算" value="pending"  />
            <el-option label="已计算" value="calculated"  />
            <el-option label="已申报" value="declared"  />
            <el-option label="已缴纳" value="paid"  />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><Notebook /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.total }}</div>
              <div class="stat-label">税务记录</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon employees">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.employees }}</div>
              <div class="stat-label">纳税人数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon amount">
              <el-icon><Money /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.totalTax }}</div>
              <div class="stat-label">总税额(万)</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon rate">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.averageRate }}%</div>
              <div class="stat-label">平均税率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 税务记录列表 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="taxId" label="税务编号" width="120"  />
        <el-table-column prop="employeeName" label="员工姓名" width="100"  />
        <el-table-column prop="employeeId" label="员工工号" width="120"  />
        <el-table-column prop="taxPeriod" label="税务期间" width="120"  />
        <el-table-column prop="taxType" label="税务类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.taxType)">
              {{ getTypeLabel(row.taxType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="taxableIncome" label="应税收入" width="120">
          <template #default="{ row }">
            ¥{{ row.taxableIncome?.toLocaleString() || 0 }}
          </template>
        </el-table-column>
        <el-table-column prop="deductions" label="扣除金额" width="120">
          <template #default="{ row }">
            ¥{{ row.deductions?.toLocaleString() || 0 }}
          </template>
        </el-table-column>
        <el-table-column prop="taxAmount" label="应纳税额" width="120">
          <template #default="{ row }">
            <span class="tax-amount">
              ¥{{ row.taxAmount?.toLocaleString() || 0 }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="taxRate" label="税率" width="100">
          <template #default="{ row }">
            {{ row.taxRate }}%
          </template>
        </el-table-column>
        <el-table-column prop="afterTaxIncome" label="税后收入" width="120">
          <template #default="{ row }">
            <span class="after-tax-income">
              ¥{{ row.afterTaxIncome?.toLocaleString() || 0 }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button type="text" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="text" size="small" @click="handleRecalculate(row)">
              重算
            </el-button>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
            <el-dropdown @command="(command: unknown) => handleCommand(command, row)">
              <el-button type="text" size="small">
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="details">税务明细</el-dropdown-item>
                  <el-dropdown-item command="declare" v-if="row.status === 'calculated'">
                    申报税务
                  </el-dropdown-item>
                  <el-dropdown-item command="payment" v-if="row.status === 'declared'">
                    缴纳税款
                  </el-dropdown-item>
                  <el-dropdown-item command="certificate">完税证明</el-dropdown-item>
                  <el-dropdown-item command="history">历史记录</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 税务详情/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="900px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="税务编号" prop="taxId">
              <el-input v-model="formData.taxId" :disabled="isView"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="员工姓名" prop="employeeName">
              <el-select v-model="formData.employeeName" :disabled="isView" style="width: 100%">
                <el-option label="张三" value="张三"  />
                <el-option label="李四" value="李四"  />
                <el-option label="王五" value="王五"  />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="税务期间" prop="taxPeriod">
              <el-date-picker
                v-model="formData.taxPeriod"
                type="month"
                :disabled="isView"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="税务类型" prop="taxType">
              <el-select v-model="formData.taxType" :disabled="isView" style="width: 100%">
                <el-option label="个人所得税" value="personal"  />
                <el-option label="专项附加扣除" value="deduction"  />
                <el-option label="年终奖税" value="bonus"  />
                <el-option label="其他税费" value="other"  />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="应税收入" prop="taxableIncome">
              <el-input-number
                v-model="formData.taxableIncome"
                :min="0"
                :disabled="isView"
                style="width: 100%"
                />
              <span style="margin-left: 8px;">元</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="基本扣除" prop="basicDeduction">
              <el-input-number
                v-model="formData.basicDeduction"
                :min="0"
                :disabled="isView"
                style="width: 100%"
                />
              <span style="margin-left: 8px;">元</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="专项扣除" prop="specialDeduction">
              <el-input-number
                v-model="formData.specialDeduction"
                :min="0"
                :disabled="isView"
                style="width: 100%"
                />
              <span style="margin-left: 8px;">元</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="专项附加扣除" prop="additionalDeduction">
              <el-input-number
                v-model="formData.additionalDeduction"
                :min="0"
                :disabled="isView"
                style="width: 100%"
                />
              <span style="margin-left: 8px;">元</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="应纳税额" prop="taxAmount">
              <el-input-number
                v-model="formData.taxAmount"
                :min="0"
                :disabled="true"
                style="width: 100%"
                />
              <span style="margin-left: 8px;">元</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="税率" prop="taxRate">
              <el-input-number
                v-model="formData.taxRate"
                :min="0"
                :max="100"
                :precision="2"
                :disabled="true"
                style="width: 100%"
                />
              <span style="margin-left: 8px;">%</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="扣除项目明细" prop="deductionDetails">
          <el-input
            v-model="formData.deductionDetails"
            type="textarea"
            :rows="4"
            :disabled="isView"
            placeholder="请输入扣除项目明细"
            />
        </el-form-item>
        <el-form-item label="税务备注" prop="taxNotes">
          <el-input
            v-model="formData.taxNotes"
            type="textarea"
            :rows="3"
            :disabled="isView"
            placeholder="请输入税务备注"
            />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="formData.notes"
            type="textarea"
            :rows="2"
            :disabled="isView"
            placeholder="请输入备注信息"
            />
        </el-form-item>
      </el-form>
      <template #footer v-if="!isView">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Notebook,
  Operation,
  Download,
  Search,
  Refresh,
  User,
  Money,
  TrendCharts,
  ArrowDown
} from '@element-plus/icons-vue'
import { salaryApi } from '@/api/salary'

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isView = ref(false)
const selectedTaxRecords = ref<unknown[]>([])

const searchForm = reactive({
  employeeName: '',
  taxPeriod: '',
  taxType: '',
  status: ''
})

const stats = reactive({
  total: 456,
  employees: 456,
  totalTax: 125.8,
  averageRate: 12.5
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

const tableData = ref([
  {
    id: '1',
    taxId: '*********',
    employeeName: '张三',
    employeeId: 'EMP001',
    taxPeriod: '2024-06',
    taxType: 'personal',
    taxableIncome: 15000,
    deductions: 8000,
    taxAmount: 840,
    taxRate: 12.0,
    afterTaxIncome: 14160,
    status: 'calculated',
    basicDeduction: 5000,
    specialDeduction: 2000,
    additionalDeduction: 1000,
    deductionDetails: '基本扣除5000元，专项扣除2000元，专项附加扣除1000元',
    taxNotes: '按照个人所得税法计算',
    notes: '正常计税'
  }
])

const formData = reactive({
  taxId: '',
  employeeName: '',
  taxPeriod: '',
  taxType: '',
  taxableIncome: 0,
  basicDeduction: 5000,
  specialDeduction: 0,
  additionalDeduction: 0,
  taxAmount: 0,
  taxRate: 0,
  deductionDetails: '',
  taxNotes: '',
  notes: ''
})

const formRules = {
  taxId: [{ required: true, message: '请输入税务编号', trigger: 'blur' }],
  employeeName: [{ required: true, message: '请选择员工', trigger: 'change' }],
  taxPeriod: [{ required: true, message: '请选择税务期间', trigger: 'change' }],
  taxType: [{ required: true, message: '请选择税务类型', trigger: 'change' }],
  taxableIncome: [{ required: true, message: '请输入应税收入', trigger: 'blur' }]
}

// 方法
const handleSearch = () => {
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    employeeName: '',
    taxPeriod: '',
    taxType: '',
    status: ''
  })
  loadData()
}

   
const handleSelectionChange = (selection: unknown[]) => {
  selectedTaxRecords.value = selection
}

const handleCalculate = async () => {
  if (selectedTaxRecords.value.length === 0) {
    ElMessage.warning('请先选择要计算税额的记录')
    return
  }
  
  try {
    ElMessage.success('正在计算税额...')
    loading.value = true
    
    // 批量重新计算税额
    for (const record of selectedTaxRecords.value) {
      const taxableIncome = record.taxableIncome - record.basicDeduction - record.specialDeduction - record.additionalDeduction
      record.taxAmount = Math.max(0, taxableIncome * (record.taxRate / 100))
      record.afterTaxIncome = record.taxableIncome - record.taxAmount
      record.status = 'calculated'
    }
    
    ElMessage.success(`已完成 ${selectedTaxRecords.value.length} 条记录的税额计算`)
  } catch (__error) {
    ElMessage.error('税额计算失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

   
const handleView = (row: unknown) => {
  dialogTitle.value = '查看税务记录'
  isView.value = true
  Object.assign(formData, row)
  dialogVisible.value = true
}

   
const handleEdit = (row: unknown) => {
  dialogTitle.value = '编辑税务记录'
  isView.value = false
  Object.assign(formData, row)
  dialogVisible.value = true
}

   
const handleRecalculate = (row: unknown) => {
  try {
    // 重新计算个人所得税
    const taxableIncome = row.taxableIncome - row.basicDeduction - row.specialDeduction - row.additionalDeduction
    const newTaxRate = calculateTaxRate(row.taxableIncome)
    const newTaxAmount = Math.max(0, taxableIncome * (newTaxRate / 100))
    const newAfterTaxIncome = row.taxableIncome - newTaxAmount
    
    // 更新数据
    row.taxRate = newTaxRate
    row.taxAmount = Math.round(newTaxAmount * 100) / 100
    row.afterTaxIncome = Math.round(newAfterTaxIncome * 100) / 100
    row.status = 'calculated'
    
    ElMessage.success(`${row.employeeName} 的税额重新计算完成：应纳税额 ¥${row.taxAmount.toLocaleString()}，税率 ${row.taxRate}%`)
  } catch (__error) {
    ElMessage.error(`${row.employeeName} 的税额重新计算失败`)
  }
}

const handleBatchProcess = () => {
  if (selectedTaxRecords.value.length === 0) {
    ElMessage.warning('请先选择要批量处理的税务记录')
    return
  }
  ElMessage.info('批量处理功能开发中')
}

   
const handleCommand = (command: string, row: unknown) => {
  switch (command) {
    case 'details':
      ElMessage.info(`查看 ${row.employeeName} 的税务明细`)
      break
    case 'declare':
      ElMessage.success(`${row.employeeName} 的税务已申报`)
      break
    case 'payment':
      ElMessage.success(`${row.employeeName} 的税款已缴纳`)
      break
    case 'certificate':
      ElMessage.info(`查看 ${row.employeeName} 的完税证明`)
      break
    case 'history':
      ElMessage.info(`查看 ${row.employeeName} 的历史记录`)
      break
    case 'delete':
      ElMessageBox.confirm(`确定要删除 ${row.employeeName} 的税务记录吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ElMessage.success('删除成功')
      })
      break
  }
}

const handleExport = async () => {
  try {
    ElMessage.success('正在导出税务报表...')
    
    const exportParams = {
      organizationId: undefined, // 可根据需要设置
      salaryMonth: searchForm.taxPeriod || undefined,
      format: 'DETAILED' as const
    }
    
    const blob = await salaryApi.exportSalarySummaryExcel(exportParams)
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `税务管理报表_${searchForm.taxPeriod || new Date().toISOString().slice(0, 7)}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('税务报表导出成功')
  } catch (__error) {
    ElMessage.error('报表导出失败，请稍后重试')
  }
}

const handleSubmit = () => {
  ElMessage.success('保存成功')
  dialogVisible.value = false
}

const handleDialogClose = () => {
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    taxId: '',
    employeeName: '',
    taxPeriod: '',
    taxType: '',
    taxableIncome: 0,
    basicDeduction: 5000,
    specialDeduction: 0,
    additionalDeduction: 0,
    taxAmount: 0,
    taxRate: 0,
    deductionDetails: '',
    taxNotes: '',
    notes: ''
  })
}

const loadData = async () => {
  loading.value = true
  try {
    ElMessage.success('正在加载税务数据...')
    
    // 构建查询参数
    const queryParams = {
      page: pagination.currentPage - 1,
      size: pagination.pageSize,
      employeeName: searchForm.employeeName || undefined,
      salaryMonth: searchForm.taxPeriod || undefined,
      processStatus: searchForm.status || undefined
    }
    
    // 调用薪资详情API获取税务相关数据
    const response = await salaryApi.querySalaryDetails(queryParams)
    
    if (response?.content) {
      // 转换API数据为税务管理格式
      tableData.value = response.content.map((item, index) => ({
        id: item.id?.toString() || `temp-${index}`,
        taxId: `TX${item.salaryMonth?.replace('-', '')}${String(item.id || index).padStart(3, '0')}`,
        employeeName: item.employeeName || '未知员工',
        employeeId: item.employeeCode || `EMP${String(item.employeeId).padStart(3, '0')}`,
        taxPeriod: item.salaryMonth || '2024-06',
        taxType: 'personal', // 默认个人所得税
        taxableIncome: item.grossSalary || 0,
        deductions: (item.deductions?.reduce((sum, deduction) => sum + deduction.amount, 0) || 0) +
                   (item.taxDeductions?.reduce((sum, taxDeduction) => sum + taxDeduction.amount, 0) || 0),
        taxAmount: item.taxDeductions?.find(td => td.type === 'BASIC_DEDUCTION')?.amount || 0,
        taxRate: calculateTaxRate(item.grossSalary || 0),
        afterTaxIncome: item.netSalary || 0,
        status: mapProcessStatusToTaxStatus(item.processStatus || 'PENDING'),
        basicDeduction: item.taxDeductions?.find(td => td.type === 'BASIC_DEDUCTION')?.amount || 5000,
        specialDeduction: item.taxDeductions?.find(td => td.type === 'SPECIAL_DEDUCTION')?.amount || 0,
        additionalDeduction: item.taxDeductions?.find(td => td.type === 'SPECIAL_ADDITIONAL_DEDUCTION')?.amount || 0,
        deductionDetails: generateDeductionDetails(item.taxDeductions || []),
        taxNotes: '按照个人所得税法计算',
        notes: item.remarks || '正常计税'
      }))
      
      // 更新分页信息
      pagination.total = response.totalElements || 0
      
      // 更新统计数据
      updateStats(response.content)
      
      ElMessage.success('税务数据加载完成')
    } else {
      ElMessage.warning('暂无税务数据')
      tableData.value = []
      pagination.total = 0
    }
  } catch (__error) {
    ElMessage.error('税务数据加载失败，请稍后重试')
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadData()
}

const getTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    personal: 'primary',
    deduction: 'success',
    bonus: 'warning',
    other: 'info'
  }
  return colors[type] || ''
}

const getTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    personal: '个人所得税',
    deduction: '专项附加扣除',
    bonus: '年终奖税',
    other: '其他税费'
  }
  return labels[type] || type
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'info',
    calculated: 'warning',
    declared: 'primary',
    paid: 'success'
  }
  return types[status] || ''
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    pending: '待计算',
    calculated: '已计算',
    declared: '已申报',
    paid: '已缴纳'
  }
  return labels[status] || status
}

// 计算税率的辅助函数
const calculateTaxRate = (grossSalary: number): number => {
  const taxableIncome = grossSalary - 5000 // 基本扣除额
  if (taxableIncome <= 0) return 0
  if (taxableIncome <= 3000) return 3
  if (taxableIncome <= 12000) return 10
  if (taxableIncome <= 25000) return 20
  if (taxableIncome <= 35000) return 25
  if (taxableIncome <= 55000) return 30
  if (taxableIncome <= 80000) return 35
  return 45
}

// 将处理状态映射为税务状态
const mapProcessStatusToTaxStatus = (processStatus: string): string => {
  const statusMap: Record<string, string> = {
    'PENDING': 'pending',
    'PROCESSED': 'calculated',
    'VERIFIED': 'declared',
    'FINALIZED': 'paid'
  }
  return statusMap[processStatus] || 'pending'
}

// 生成扣除项目明细
   
const generateDeductionDetails = (taxDeductions: unknown[]): string => {
  if (!taxDeductions || taxDeductions.length === 0) {
    return '基本扣除5000元'
  }
  
  return taxDeductions.map(deduction => {
    const typeName = deduction.typeName || deduction.type
    return `${typeName}${deduction.amount}元`
  }).join('，')
}

// 更新统计数据
   
const updateStats = (salaryData: unknown[]) => {
  const totalRecords = salaryData.length
  const uniqueEmployees = new Set(salaryData.map(item => item.employeeId)).size
  const totalTaxAmount = salaryData.reduce((sum, item) => {
   
    const taxAmount = item.taxDeductions?.find((td: unknown) => td.type === 'BASIC_DEDUCTION')?.amount || 0
    return sum + taxAmount
  }, 0)
  const averageTaxRate = totalRecords > 0 ? 
    salaryData.reduce((sum, item) => sum + calculateTaxRate(item.grossSalary || 0), 0) / totalRecords : 0
  
  stats.total = totalRecords
  stats.employees = uniqueEmployees
  stats.totalTax = Math.round(totalTaxAmount / 10000 * 100) / 100 // 转换为万元，保留2位小数
  stats.averageRate = Math.round(averageTaxRate * 100) / 100
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.tax-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.employees {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.amount {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.rate {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.tax-amount {
  font-weight: 600;
  color: #e6a23c;
}

.after-tax-income {
  font-weight: 600;
  color: #67c23a;
}
</style>
