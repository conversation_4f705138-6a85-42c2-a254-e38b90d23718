<template>
  <div class="salary-trend-analysis">
    <!-- 查询条件 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" inline>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM"
            :shortcuts="dateShortcuts"
           />
        </el-form-item>
        
        <el-form-item label="分析粒度">
          <el-radio-group v-model="searchForm.granularity">
            <el-radio-button label="month">按月</el-radio-button>
            <el-radio-button label="quarter">按季度</el-radio-button>
            <el-radio-button label="year">按年</el-radio-button>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="对比维度">
          <el-select v-model="searchForm.compareType" placeholder="请选择">
            <el-option label="不对比" value="none"  />
            <el-option label="同比" value="yoy"  />
            <el-option label="环比" value="qoq"  />
            <el-option label="部门对比" value="department"  />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            分析
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 趋势指标 -->
    <el-row :gutter="20" class="trend-metrics">
      <el-col :span="6">
        <el-card shadow="hover" class="metric-card">
          <div class="metric-title">平均增长率</div>
          <div class="metric-value">
            <hr-count-to :end-val="metrics.avgGrowth" :decimals="1" suffix="%" />
          </div>
          <div class="metric-trend">
            <span>较上期</span>
            <span :class="metrics.growthTrend > 0 ? 'up' : 'down'">
              {{ metrics.growthTrend > 0 ? '↑' : '↓' }} {{ Math.abs(metrics.growthTrend) }}%
            </span>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="metric-card">
          <div class="metric-title">最高增幅</div>
          <div class="metric-value">
            <hr-count-to :end-val="metrics.maxGrowth" :decimals="1" suffix="%" />
          </div>
          <div class="metric-sub">{{ metrics.maxGrowthPeriod }}</div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="metric-card">
          <div class="metric-title">波动系数</div>
          <div class="metric-value">
            <hr-count-to :end-val="metrics.volatility" :decimals="2" />
          </div>
          <div class="metric-sub">
            <el-tag :type="getVolatilityType(metrics.volatility)" size="small">
              {{ getVolatilityText(metrics.volatility) }}
            </el-tag>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="metric-card">
          <div class="metric-title">预测准确率</div>
          <div class="metric-value">
            <hr-count-to :end-val="metrics.accuracy" :decimals="1" suffix="%" />
          </div>
          <div class="metric-sub">基于历史数据</div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 趋势图表 -->
    <el-card shadow="never" class="chart-card">
      <template #header>
        <div class="chart-header">
          <span>薪资趋势分析图</span>
          <div class="chart-actions">
            <el-checkbox v-model="showPrediction">显示预测</el-checkbox>
            <el-checkbox v-model="showAverage">显示均线</el-checkbox>
            <el-button type="text" @click="handleZoomReset">重置缩放</el-button>
          </div>
        </div>
      </template>
      <div ref="mainChartRef" class="main-chart"></div>
    </el-card>

    <!-- 分项趋势 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card shadow="never">
          <template #header>
            <span>薪资构成项趋势</span>
          </template>
          <div ref="componentChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card shadow="never">
          <template #header>
            <span>增长贡献度分析</span>
          </template>
          <div ref="contributionChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 预测分析 -->
    <el-card shadow="never" style="margin-top: 20px">
      <template #header>
        <div class="chart-header">
          <span>薪资预测分析</span>
          <el-select v-model="predictionModel" size="small" style="width: 150px">
            <el-option label="线性回归" value="linear"  />
            <el-option label="移动平均" value="ma"  />
            <el-option label="指数平滑" value="es"  />
          </el-select>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="16">
          <div ref="predictionChartRef" class="chart-container"></div>
        </el-col>
        <el-col :span="8">
          <div class="prediction-info">
            <h4>预测参数</h4>
            <el-form label-width="80px" size="small">
              <el-form-item label="预测期数">
                <el-input-number v-model="predictionPeriods" :min="1" :max="12"   />
              </el-form-item>
              <el-form-item label="置信区间">
                <el-slider v-model="confidenceLevel" :min="80" :max="99"  />
              </el-form-item>
            </el-form>
            
            <h4>预测结果</h4>
            <div class="prediction-result">
              <div class="result-item">
                <span>下期预测值：</span>
                <strong>¥{{ predictionResult.nextValue }}</strong>
              </div>
              <div class="result-item">
                <span>预测增长率：</span>
                <strong>{{ predictionResult.growthRate }}%</strong>
              </div>
              <div class="result-item">
                <span>置信区间：</span>
                <span>¥{{ predictionResult.lowerBound }} ~ ¥{{ predictionResult.upperBound }}</span>
              </div>
            </div>
            
            <el-button type="primary" size="small" @click="handlePredict">
              重新预测
            </el-button>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 异常检测 -->
    <el-card shadow="never" style="margin-top: 20px">
      <template #header>
        <span>异常点检测</span>
      </template>
      <el-table :data="anomalyData" stripe>
        <el-table-column prop="period" label="时期" width="120"  />
        <el-table-column prop="department" label="部门" width="120"  />
        <el-table-column prop="actualValue" label="实际值" align="right">
          <template #default="{ row }">
            ¥{{ row.actualValue.toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="expectedValue" label="预期值" align="right">
          <template #default="{ row }">
            ¥{{ row.expectedValue.toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="deviation" label="偏差率" align="center" width="100">
          <template #default="{ row }">
            <el-tag :type="Math.abs(row.deviation) > 10 ? 'danger' : 'warning'">
              {{ row.deviation }}%
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="reason" label="可能原因"  />
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleAnalyze(row)">分析</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import echarts, { createChart } from '@/utils/echarts'
import HrCountTo from '@/components/common/HrCountTo.vue'
import { salaryApi } from '@/api/salary'

// 搜索表单
const searchForm = reactive({
  dateRange: [],
  granularity: 'month',
  compareType: 'yoy'
})

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近3个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 3)
      return [start, end]
    }
  },
  {
    text: '最近6个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 6)
      return [start, end]
    }
  },
  {
    text: '最近1年',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setFullYear(start.getFullYear() - 1)
      return [start, end]
    }
  }
]

// 指标数据
const metrics = reactive({
  avgGrowth: 8.5,
  growthTrend: 2.3,
  maxGrowth: 15.2,
  maxGrowthPeriod: '2024年12月',
  volatility: 0.23,
  accuracy: 92.5
})

// 图表配置
const showPrediction = ref(true)
const showAverage = ref(true)
const predictionModel = ref('linear')
const predictionPeriods = ref(3)
const confidenceLevel = ref(95)

// 图表实例
const mainChartRef = ref<HTMLElement>()
const componentChartRef = ref<HTMLElement>()
const contributionChartRef = ref<HTMLElement>()
const predictionChartRef = ref<HTMLElement>()

let mainChart: echarts.ECharts
let componentChart: echarts.ECharts
let contributionChart: echarts.ECharts
let predictionChart: echarts.ECharts

// 预测结果
const predictionResult = reactive({
  nextValue: 1980500,
  growthRate: 5.2,
  lowerBound: 1850000,
  upperBound: 2100000
})

// 异常数据
const anomalyData = ref([
  {
    period: '2024年11月',
    department: '计算机系',
    actualValue: 280000,
    expectedValue: 250000,
    deviation: 12,
    reason: '年终奖发放'
  },
  {
    period: '2024年9月',
    department: '数学系',
    actualValue: 180000,
    expectedValue: 200000,
    deviation: -10,
    reason: '人员调整'
  }
])

// 初始化
onMounted(() => {
  // 设置默认日期范围
  const end = new Date()
  const start = new Date()
  start.setMonth(start.getMonth() - 12)
  searchForm.dateRange = [start, end]

  nextTick(() => {
    initCharts()
  })
})

// 初始化图表
const initCharts = () => {
  // 主趋势图
  if (mainChartRef.value) {
    mainChart = createChart(mainChartRef.value)
    updateMainChart()
  }

  // 构成项趋势图
  if (componentChartRef.value) {
    componentChart = createChart(componentChartRef.value)
    updateComponentChart()
  }

  // 贡献度图
  if (contributionChartRef.value) {
    contributionChart = createChart(contributionChartRef.value)
    updateContributionChart()
  }

  // 预测图
  if (predictionChartRef.value) {
    predictionChart = createChart(predictionChartRef.value)
    updatePredictionChart()
  }

  // 监听窗口变化
  window.addEventListener('resize', () => {
    mainChart?.resize()
    componentChart?.resize()
    contributionChart?.resize()
    predictionChart?.resize()
  })
}

// 更新主趋势图
const updateMainChart = () => {
  const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
  const actualData = [1750, 1780, 1820, 1850, 1880, 1920, 1950, 1980, 2020, 2050, 2100, 2150]
  const predictData = [null, null, null, null, null, null, null, null, null, null, 2100, 2150, 2200, 2250, 2300]
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['实际值', '预测值', '移动平均']
    },
    toolbox: {
      feature: {
        dataZoom: {
          yAxisIndex: 'none'
        },
        restore: {},
        saveAsImage: {}
      }
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100
      },
      {
        start: 0,
        end: 100
      }
    ],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: [...months, '次年1月', '次年2月', '次年3月']
    },
    yAxis: {
      type: 'value',
      name: 'HrHr薪资总额(万元)',
      axisLabel: {
        formatter: '{value}'
      }
    },
    series: [
      {
        name: '实际值',
        type: 'line',
        data: actualData,
        itemStyle: {
          color: '#409eff'
        },
        markPoint: {
          data: [
            { type: 'max', name: '最大值' },
            { type: 'min', name: '最小值' }
          ]
        }
      },
      {
        name: '预测值',
        type: 'line',
        data: predictData,
        itemStyle: {
          color: '#67c23a'
        },
        lineStyle: {
          type: 'dashed'
        }
      },
      {
        name: '移动平均',
        type: 'line',
        data: actualData.map((val, idx) => {
          if (idx < 2) return null
          return (actualData[idx] + actualData[idx-1] + actualData[idx-2]) / 3
        }),
        itemStyle: {
          color: '#e6a23c'
        },
        smooth: true
      }
    ]
  }
  
  mainChart.setOption(option)
}

// 更新构成项趋势图
const updateComponentChart = () => {
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['基本工资', '岗位工资', '绩效工资', '津贴补贴']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value',
      name: '金额(万元)'
    },
    series: [
      {
        name: '基本工资',
        type: 'line',
        data: [820, 832, 901, 934, 1000, 1050],
        smooth: true
      },
      {
        name: '岗位工资',
        type: 'line',
        data: [400, 410, 420, 430, 450, 480],
        smooth: true
      },
      {
        name: '绩效工资',
        type: 'line',
        data: [280, 300, 320, 340, 360, 390],
        smooth: true
      },
      {
        name: '津贴补贴',
        type: 'line',
        data: [150, 160, 170, 180, 190, 200],
        smooth: true
      }
    ]
  }
  
  componentChart.setOption(option)
}

// 更新贡献度图
const updateContributionChart = () => {
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['基本工资', '岗位工资', '绩效工资', '津贴补贴']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '贡献度(%)'
    },
    yAxis: {
      type: 'category',
      data: ['1-2月', '2-3月', '3-4月', '4-5月', '5-6月']
    },
    series: [
      {
        name: '基本工资',
        type: 'bar',
        stack: '总量',
        data: [40, 42, 38, 45, 43]
      },
      {
        name: '岗位工资',
        type: 'bar',
        stack: '总量',
        data: [25, 23, 27, 22, 24]
      },
      {
        name: '绩效工资',
        type: 'bar',
        stack: '总量',
        data: [20, 22, 18, 20, 21]
      },
      {
        name: '津贴补贴',
        type: 'bar',
        stack: '总量',
        data: [15, 13, 17, 13, 12]
      }
    ]
  }
  
  contributionChart.setOption(option)
}

// 更新预测图
const updatePredictionChart = () => {
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['历史数据', '预测值', '置信上限', '置信下限']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['10月', '11月', '12月', '1月', '2月', '3月']
    },
    yAxis: {
      type: 'value',
      name: '薪资总额(万元)'
    },
    series: [
      {
        name: '历史数据',
        type: 'line',
        data: [2050, 2100, 2150, null, null, null],
        itemStyle: {
          color: '#409eff'
        }
      },
      {
        name: '预测值',
        type: 'line',
        data: [null, null, 2150, 2200, 2250, 2300],
        itemStyle: {
          color: '#67c23a'
        },
        lineStyle: {
          type: 'dashed'
        }
      },
      {
        name: '置信上限',
        type: 'line',
        data: [null, null, 2150, 2280, 2340, 2400],
        lineStyle: {
          opacity: 0
        },
        stack: 'confidence',
        symbol: 'none'
      },
      {
        name: '置信下限',
        type: 'line',
        data: [null, null, 2150, 2120, 2160, 2200],
        lineStyle: {
          opacity: 0
        },
        areaStyle: {
          color: 'rgba(103, 194, 58, 0.2)'
        },
        stack: 'confidence',
        symbol: 'none'
      }
    ]
  }
  
  predictionChart.setOption(option)
}

// 获取波动性类型
const getVolatilityType = (value: number) => {
  if (value < 0.2) return 'success'
  if (value < 0.4) return 'warning'
  return 'danger'
}

// 获取波动性文本
const getVolatilityText = (value: number) => {
  if (value < 0.2) return '稳定'
  if (value < 0.4) return '正常'
  return '波动较大'
}

// 查询
const handleSearch = async () => {
  try {
      message: '正在分析薪资趋势...',
      duration: 0
    })
    
    // 构建查询参数
    const params = {
      dateRange: searchForm.dateRange,
      granularity: searchForm.granularity,
      compareType: searchForm.compareType,
      departmentId: searchForm.departmentId || undefined,
      metricType: 'salary' // 默认分析薪资指标
    }
    
    // 调用API获取趋势分析数据
    const {data: _data} =  await salaryApi.getSalaryTrendAnalysis(params)
    
    // 更新核心指标
    Object.assign(metrics, data.metrics)
    
    // 更新图表数据
    if (mainChart && data.mainData) {
      const option 
  }

  .trend-metrics {
    margin-bottom: 20px;

    .metric-card {
      text-align: center;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-4px);
      }

      .metric-title {
        font-size: 14px;
        color: #909399;
        margin-bottom: 10px;
      }

      .metric-value {
        font-size: 28px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 10px;
      }

      .metric-trend, .metric-sub {
        font-size: 12px;
        color: #909399;

        .up {
          color: #67c23a;
        }

        .down {
          color: #f56c6c;
        }
      }
    }
  }

  .chart-card {
    margin-bottom: 20px;

    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .chart-actions {
        display: flex;
        align-items: center;
        gap: 15px;
      }
    }
  }

  .main-chart {
    height: 400px;
  }

  .chart-container {
    height: 300px;
  }

  .prediction-info {
    padding: 20px;

    h4 {
      margin: 0 0 15px 0;
      color: #303133;
      font-size: 16px;
    }

    .prediction-result {
      margin: 20px 0;
      padding: 15px;
      background-color: #f5f7fa;
      border-radius: 4px;

      .result-item {
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        &:last-child {
          margin-bottom: 0;
        }

        strong {
          color: #409eff;
        }
      }
    }
  }
}
</style>