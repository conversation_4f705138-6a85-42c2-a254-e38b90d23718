<template>
  <div class="subsidy-application">
    <!-- 申请列表 -->
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span>补贴申请管理</span>
          <el-button type="primary" @click="handleNewApplication">
            <el-icon><Plus /></el-icon>
            新建申请
          </el-button>
        </div>
      </template>

      <!-- 搜索条件 -->
      <el-form :model="searchForm" inline>
        <el-form-item label="申请状态">
          <el-select v-model="searchForm.status" placeholder="全部" clearable>
            <el-option label="草稿" value="draft"  />
            <el-option label="审批中" value="pending"  />
            <el-option label="已通过" value="approved"  />
            <el-option label="已驳回" value="rejected"  />
            <el-option label="已发放" value="paid"  />
          </el-select>
        </el-form-item>
        <el-form-item label="补贴类型">
          <el-select v-model="searchForm.subsidyType" placeholder="全部" clearable>
            <el-option label="交通补贴" value="transport"  />
            <el-option label="餐饮补贴" value="meal"  />
            <el-option label="通讯补贴" value="communication"  />
            <el-option label="住房补贴" value="housing"  />
            <el-option label="其他补贴" value="other"  />
          </el-select>
        </el-form-item>
        <el-form-item label="申请时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
           />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table :data="applicationList" stripe v-loading="loading">
        <el-table-column prop="applicationNo" label="申请单号" width="150"  />
        <el-table-column prop="applicantName" label="申请人" width="100"  />
        <el-table-column prop="department" label="部门" width="120"  />
        <el-table-column prop="subsidyType" label="补贴类型" width="100">
          <template #default="{ row }">
            <el-tag>{{ getSubsidyTypeText(row.subsidyType) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="申请金额" width="100" align="right">
          <template #default="{ row }">
            ¥{{ row.amount.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="period" label="补贴期间" width="120"  />
        <el-table-column prop="createTime" label="申请时间" width="160"  />
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="currentApprover" label="当前审批人" width="100"  />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button
              v-if="row.status === 'draft'"
              link
              type="primary"
              size="small"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="row.status === 'draft'"
              link
              type="primary"
              size="small"
              @click="handleSubmit(row)"
            >
              提交
            </el-button>
            <el-button
              v-if="row.status === 'pending' && row.canApprove"
              link
              type="success"
              size="small"
              @click="handleApprove(row)"
            >
              审批
            </el-button>
            <el-button
              v-if="row.status === 'pending' && row.isApplicant"
              link
              type="warning"
              size="small"
              @click="handleWithdraw(row)"
            >
              撤回
            </el-button>
            <el-button
              v-if="row.status === 'draft'"
              link
              type="danger"
              size="small"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pageInfo.currentPage"
        v-model:page-size="pageInfo.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pageInfo.total"
        layout="total, sizes, prev, pager, next, jumper"
        style="margin-top: 20px"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
    </el-card>

    <!-- 申请对话框 -->
    <el-dialog
      v-model="applicationDialog"
      :title="dialogTitle"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="applicationFormRef"
        :model="applicationForm"
        :rules="rules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="补贴类型" prop="subsidyType">
              <el-select
                v-model="applicationForm.subsidyType"
                placeholder="请选择"
                @change="handleTypeChange"
                style="width: 100%"
              >
                <el-option label="交通补贴" value="transport"  />
                <el-option label="餐饮补贴" value="meal"  />
                <el-option label="通讯补贴" value="communication"  />
                <el-option label="住房补贴" value="housing"  />
                <el-option label="其他补贴" value="other"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="补贴期间" prop="period">
              <el-date-picker
                v-model="applicationForm.period"
                type="month"
                placeholder="选择月份"
                value-format="YYYY-MM"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="申请金额" prop="amount">
              <el-input-number
                v-model="applicationForm.amount"
                :min="0"
                :max="10000"
                :precision="2"
                style="width: 100%"
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="补贴标准">
              <el-input
                v-model="subsidyStandard"
                readonly
                placeholder="根据类型自动显示"
                />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="申请事由" prop="reason">
          <el-input
            v-model="applicationForm.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入申请事由"
            />
        </el-form-item>

        <el-form-item label="附件上传">
          <el-upload
            ref="uploadRef"
            v-model:file-list="applicationForm.attachments"
            :action="uploadUrl"
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :limit="5"
            accept=".jpg,.jpeg,.png,.pdf,.doc,.docx"
          >
            <el-button type="primary">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持 jpg/png/pdf/doc 文件，单个文件不超过 5MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="applicationDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSaveApplication">保存</el-button>
      </template>
    </el-dialog>

    <!-- 审批对话框 -->
    <el-dialog
      v-model="approvalDialog"
      title="补贴审批"
      width="600px"
    >
      <el-descriptions :column="2" border style="margin-bottom: 20px">
        <el-descriptions-item label="申请单号">
          {{ currentApplication.applicationNo }}
        </el-descriptions-item>
        <el-descriptions-item label="申请人">
          {{ currentApplication.applicantName }}
        </el-descriptions-item>
        <el-descriptions-item label="补贴类型">
          {{ getSubsidyTypeText(currentApplication.subsidyType) }}
        </el-descriptions-item>
        <el-descriptions-item label="申请金额">
          ¥{{ currentApplication.amount?.toFixed(2) }}
        </el-descriptions-item>
        <el-descriptions-item label="补贴期间" :span="2">
          {{ currentApplication.period }}
        </el-descriptions-item>
        <el-descriptions-item label="申请事由" :span="2">
          {{ currentApplication.reason }}
        </el-descriptions-item>
      </el-descriptions>

      <el-form :model="approvalForm" label-width="100px">
        <el-form-item label="审批意见" required>
          <el-radio-group v-model="approvalForm.action">
            <el-radio label="approve">同意</el-radio>
            <el-radio label="reject">驳回</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审批备注">
          <el-input
            v-model="approvalForm.comment"
            type="textarea"
            :rows="3"
            placeholder="请输入审批意见"
            />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="approvalDialog = false">取消</el-button>
        <el-button type="primary" @click="handleConfirmApproval">确定</el-button>
      </template>
    </el-dialog>

    <!-- 详情抽屉 -->
    <el-drawer
      v-model="detailDrawer"
      title="补贴申请详情"
      size="600px"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="申请单号">
          {{ currentApplication.applicationNo }}
        </el-descriptions-item>
        <el-descriptions-item label="申请状态">
          <el-tag :type="getStatusType(currentApplication.status)">
            {{ getStatusText(currentApplication.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="申请人">
          {{ currentApplication.applicantName }}
        </el-descriptions-item>
        <el-descriptions-item label="部门">
          {{ currentApplication.department }}
        </el-descriptions-item>
        <el-descriptions-item label="补贴类型">
          {{ getSubsidyTypeText(currentApplication.subsidyType) }}
        </el-descriptions-item>
        <el-descriptions-item label="申请金额">
          ¥{{ currentApplication.amount?.toFixed(2) }}
        </el-descriptions-item>
        <el-descriptions-item label="补贴期间">
          {{ currentApplication.period }}
        </el-descriptions-item>
        <el-descriptions-item label="申请时间">
          {{ currentApplication.createTime }}
        </el-descriptions-item>
        <el-descriptions-item label="申请事由" :span="2">
          {{ currentApplication.reason }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 审批流程 -->
      <el-divider>审批流程</el-divider>
      <el-timeline>
        <el-timeline-item
          v-for="(item, index) in approvalHistory"
          :key="index"
          :timestamp="item.time"
          :type="item.type"
        >
          <div>
            <strong>{{ item.nodeName }}</strong>
            <span style="margin-left: 10px">{{ item.approverName }}</span>
          </div>
          <div style="color: #909399; font-size: 12px">
            {{ item.action }} - {{ item.comment }}
          </div>
        </el-timeline-item>
      </el-timeline>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

// 搜索表单
const searchForm = reactive({
  status: '',
  subsidyType: '',
  dateRange: []
})

// 分页信息
const pageInfo = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 加载状态
const loading = ref(false)

// 申请列表
const applicationList = ref([
  {
    id: 1,
    applicationNo: 'SUB202501200001',
    applicantName: '张三',
    department: '计算机系',
    subsidyType: 'transport',
    amount: 500,
    period: '2025-01',
    createTime: '2025-01-20 09:00:00',
    status: 'pending',
    currentApprover: '李主任',
    canApprove: false,
    isApplicant: true
  },
  {
    id: 2,
    applicationNo: 'SUB202501190001',
    applicantName: '李四',
    department: '数学系',
    subsidyType: 'meal',
    amount: 300,
    period: '2025-01',
    createTime: '2025-01-19 14:30:00',
    status: 'approved',
    currentApprover: '-',
    canApprove: false,
    isApplicant: false
  },
  {
    id: 3,
    applicationNo: 'SUB202501180001',
    applicantName: '王五',
    department: '物理系',
    subsidyType: 'communication',
    amount: 200,
    period: '2025-01',
    createTime: '2025-01-18 10:00:00',
    status: 'draft',
    currentApprover: '-',
    canApprove: false,
    isApplicant: true
  }
])

// 对话框
const applicationDialog = ref(false)
const approvalDialog = ref(false)
const detailDrawer = ref(false)
const dialogTitle = ref('新建补贴申请')

// 表单
const applicationFormRef = ref()
const applicationForm = reactive({
  subsidyType: '',
  period: '',
  amount: 0,
  reason: '',
  attachments: []
})

const approvalForm = reactive({
  action: 'approve',
  comment: ''
})

// 当前申请
const currentApplication = ref<unknown>({})

// 审批历史
const approvalHistory = ref([
  {
    nodeName: '申请提交',
    approverName: '张三',
    time: '2025-01-20 09:00:00',
    action: '提交申请',
    comment: '申请1月份交通补贴',
    type: 'success'
  },
  {
    nodeName: '部门审批',
    approverName: '李主任',
    time: '2025-01-20 10:30:00',
    action: '审批中',
    comment: '',
    type: 'primary'
  }
])

// 上传地址
const uploadUrl = ref('/api/upload')

// 补贴标准
const subsidyStandard = computed(() => {
  const standards: Record<string, string> = {
    transport: '每月上限500元',
    meal: '每月上限300元',
    communication: '每月上限200元',
    housing: '每月上限1000元',
    other: '根据实际情况'
  }
  return standards[applicationForm.subsidyType] || ''
})

// 表单规则
const rules = reactive({
  subsidyType: [
    { required: true, message: '请选择补贴类型', trigger: 'change' }
  ],
  period: [
    { required: true, message: '请选择补贴期间', trigger: 'change' }
  ],
  amount: [
    { required: true, message: '请输入申请金额', trigger: 'blur' }
  ],
  reason: [
    { required: true, message: '请输入申请事由', trigger: 'blur' }
  ]
})

// 查询
const handleSearch = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('查询成功')
  }, 500)
}

// 重置
const handleReset = () => {
  searchForm.status = ''
  searchForm.subsidyType = ''
  searchForm.dateRange = []
  handleSearch()
}

// 新建申请
const handleNewApplication = () => {
  dialogTitle.value = '新建补贴申请'
  applicationDialog.value = true
  applicationFormRef.value?.resetFields()
}

// 查看
   
const handleView = (row: unknown) => {
  currentApplication.value = row
  detailDrawer.value = true
}

// 编辑
   
const handleEdit = (row: unknown) => {
  dialogTitle.value = '编辑补贴申请'
  Object.assign(applicationForm, row)
  applicationDialog.value = true
}

// 提交
   
const handleSubmit = async (row: unknown) => {
  try {
    await ElMessageBox.confirm(
      '确定要提交该补贴申请吗？提交后将进入审批流程。',
      '提交确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    row.status = 'pending'
    ElMessage.success('提交成功')
  } catch {
    // 用户取消
  }
}

// 审批
   
const handleApprove = (row: unknown) => {
  currentApplication.value = row
  approvalForm.action = 'approve'
  approvalForm.comment = ''
  approvalDialog.value = true
}

// 撤回
   
const handleWithdraw = async (row: unknown) => {
  try {
    await ElMessageBox.confirm(
      '确定要撤回该补贴申请吗？',
      '撤回确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    row.status = 'draft'
    ElMessage.success('撤回成功')
  } catch {
    // 用户取消
  }
}

// 删除
   
const handleDelete = async (row: unknown) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除该补贴申请吗？',
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    const index = applicationList.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      applicationList.value.splice(index, 1)
    }
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

// 保存申请
const handleSaveApplication = async () => {
  const valid = await applicationFormRef.value.validate()
  if (!valid) return

  ElMessage.success('保存成功')
  applicationDialog.value = false
}

// 确认审批
const handleConfirmApproval = () => {
  if (!approvalForm.comment) {
    ElMessage.warning('请输入审批意见')
    return
  }

  if (approvalForm.action === 'approve') {
    currentApplication.value.status = 'approved'
    ElMessage.success('审批通过')
  } else {
    currentApplication.value.status = 'rejected'
    ElMessage.success('已驳回')
  }
  approvalDialog.value = false
}

// 类型改变
const handleTypeChange = () => {
  // 根据类型设置默认金额
  const defaultAmounts: Record<string, number> = {
    transport: 500,
    meal: 300,
    communication: 200,
    housing: 1000,
    other: 0
  }
  applicationForm.amount = defaultAmounts[applicationForm.subsidyType] || 0
}

// 文件预览
   
const handlePreview = (file: unknown) => {
  }

// 文件移除
   
const handleRemove = (file: unknown, fileList: unknown) => {
  }

// 分页
const handleSizeChange = (val: number) => {
  pageInfo.pageSize = val
  handleSearch()
}

const handleCurrentChange = (val: number) => {
  pageInfo.currentPage = val
  handleSearch()
}

// 获取补贴类型文本
const getSubsidyTypeText = (type: string) => {
  const map: Record<string, string> = {
    transport: '交通补贴',
    meal: '餐饮补贴',
    communication: '通讯补贴',
    housing: '住房补贴',
    other: '其他补贴'
  }
  return map[type] || type
}

// 获取状态类型
const getStatusType = (status: string) => {
  const map: Record<string, string> = {
    draft: 'info',
    pending: 'warning',
    approved: 'success',
    rejected: 'danger',
    paid: 'success'
  }
  return map[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const map: Record<string, string> = {
    draft: '草稿',
    pending: '审批中',
    approved: '已通过',
    rejected: '已驳回',
    paid: '已发放'
  }
  return map[status] || status
}
</script>

<style lang="scss" scoped>
.subsidy-application {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>