/**
 * 薪资管理模块类型定义
 */

// 导航项类型
export interface NavigationItem {
  id: string
  title: string
  description: string

  icon: unknown
  type: 'primary' | 'success' | 'warning' | 'info' | 'danger'
  gradient: string
  clickable: boolean
  stats?: {
    value: string
    label: string
  }
  badge?: string
}

// 统计卡片类型
export interface StatsCard {
  id: string
  title: string
  value: number
  unit?: string

  icon: unknown
  type: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  gradient: string
  trend?: number
  subtitle?: string
  clickable: boolean
  chartData?: number[]
}

// 图表周期类型
export interface ChartPeriod {
  key: string
  label: string
}

// 操作记录类型
export interface OperationRecord {
  id: number
  title: string
  description: string
  type: 'import' | 'export' | 'analysis' | 'alert'
  status: 'success' | 'processing' | 'pending' | 'failed'
  statusText: string
  operatorName: string
  createTime: string
}

// 数据访问表单类型
export interface AccessForm {
  reason: string
}

// 薪资统计数据类型
export interface SalaryStatistics {
  totalEmployees: number
  totalAmount: number
  averageAmount: number
  budgetUtilization: number
}

// 图表配置类型
export interface ChartConfig {
  tooltip?: unknown

  legend?: unknown

  grid?: unknown

  xAxis?: unknown

  yAxis?: unknown

  series?: unknown[]
}

// 部门类型枚举
export enum DepartmentType {
  COLLEGE = 'college',
  DEPARTMENT = 'department',
  POSITION = 'position'
}

// 预算状态类型
export enum BudgetStatus {
  ADEQUATE = 'success',
  NORMAL = 'warning',
  TIGHT = 'danger'
}

// 操作类型图标映射
export const OPERATION_ICONS = {
  import: 'Upload',
  export: 'Download',
  analysis: 'TrendCharts',
  alert: 'Warning'
} as const

// 操作状态类型映射
export const OPERATION_STATUS_TYPES = {
  success: 'success',
  processing: 'primary',
  pending: 'warning',
  failed: 'danger'
} as const

// 预算警告等级样式映射
export const BUDGET_ALERT_CLASSES = {
  WARNING: 'budget-warning',
  CRITICAL: 'budget-critical',
  NORMAL: 'budget-normal'
} as const

// 预算进度状态映射
export const BUDGET_PROGRESS_STATUS = {
  WARNING: 'warning',
  CRITICAL: 'exception',
  NORMAL: 'success'
} as const

// 异常状态标签类型映射
export const EXCEPTION_STATUS_TYPES = {
  PENDING: 'warning',
  INVESTIGATING: 'primary',
  RESOLVED: 'success',
  DISMISSED: 'info'
} as const
