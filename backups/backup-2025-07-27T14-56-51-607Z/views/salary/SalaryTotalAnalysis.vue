<template>
  <div class="salary-total-analysis">
    <!-- 查询条件 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" inline>
        <el-form-item label="统计期间">
          <el-radio-group v-model="searchForm.periodType" @change="handlePeriodChange">
            <el-radio-button label="month">按月</el-radio-button>
            <el-radio-button label="quarter">按季度</el-radio-button>
            <el-radio-button label="year">按年</el-radio-button>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="时间范围">
          <el-date-picker
            v-if="searchForm.periodType === 'month'"
            v-model="searchForm.dateRange"
            type="monthrange"
            range-separator="至"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            value-format="YYYY-MM"
           />
          <el-date-picker
            v-else-if="searchForm.periodType === 'year'"
            v-model="searchForm.year"
            type="year"
            placeholder="选择年份"
            value-format="YYYY"
           />
          <el-select
            v-else
            v-model="searchForm.quarter"
            placeholder="选择季度"
          >
            <el-option label="第一季度" value="Q1"  />
            <el-option label="第二季度" value="Q2"  />
            <el-option label="第三季度" value="Q3"  />
            <el-option label="第四季度" value="Q4"  />
          </el-select>
        </el-form-item>

        <el-form-item label="部门">
          <el-tree-select
            v-model="searchForm.departmentId"
            :data="departmentTree"
            :props="{ label: 'name', value: 'id' }"
            placeholder="全部部门"
            clearable
            check-strictly
           />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            导出报表
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 核心指标 -->
    <el-row :gutter="20" class="metrics-row">
      <el-col :span="6">
        <el-card shadow="hover" class="metric-card">
          <el-statistic
            title="薪资总额"
            :value="metrics.total"
            :precision="2"
            prefix="¥"
          >
            <template #suffix>
              <span class="unit">万元</span>
            </template>
          </el-statistic>
          <div class="metric-footer">
            <span>同比</span>
            <span :class="metrics.totalYoY > 0 ? 'up' : 'down'">
              {{ metrics.totalYoY > 0 ? '+' : '' }}{{ metrics.totalYoY }}%
            </span>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="metric-card">
          <el-statistic
            title="人均薪资"
            :value="metrics.average"
            prefix="¥"
           />
          <div class="metric-footer">
            <span>环比</span>
            <span :class="metrics.averageQoQ > 0 ? 'up' : 'down'">
              {{ metrics.averageQoQ > 0 ? '+' : '' }}{{ metrics.averageQoQ }}%
            </span>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="metric-card">
          <el-statistic
            title="发放人数"
            :value="metrics.count"
            suffix="人"
           />
          <div class="metric-footer">
            <span>占比</span>
            <span>{{ metrics.countRatio }}%</span>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="metric-card">
          <el-statistic
            title="预算执行率"
            :value="metrics.budgetRate"
            suffix="%"
          >
            <template #title>
              <span>
                预算执行率
                <el-tooltip content="实际发放/预算金额×100%" placement="top">
                  <el-icon><InfoFilled /></el-icon>
                </el-tooltip>
              </span>
            </template>
          </el-statistic>
          <div class="metric-footer">
            <el-progress
              :percentage="metrics.budgetRate"
              :color="getBudgetColor(metrics.budgetRate)"
              :show-text="false"
             />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表分析 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card shadow="never">
          <template #header>
            <div class="chart-header">
              <span>薪资总额趋势</span>
              <el-radio-group v-model="trendType" size="small">
                <el-radio-button label="amount">金额</el-radio-button>
                <el-radio-button label="growth">增长率</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div ref="trendChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card shadow="never">
          <template #header>
            <span>部门薪资分布</span>
          </template>
          <div ref="distributionChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px">
      <el-col :span="12">
        <el-card shadow="never">
          <template #header>
            <span>薪资构成分析</span>
          </template>
          <div ref="compositionChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card shadow="never">
          <template #header>
            <div class="chart-header">
              <span>成本占比分析</span>
              <el-select v-model="costType" size="small" style="width: 120px">
                <el-option label="占收入比" value="income"  />
                <el-option label="占支出比" value="expense"  />
                <el-option label="占预算比" value="budget"  />
              </el-select>
            </div>
          </template>
          <div ref="costChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细数据表格 -->
    <el-card shadow="never" style="margin-top: 20px">
      <template #header>
        <span>部门薪资明细</span>
      </template>
      <el-table
        :data="tableData"
        stripe
        show-summary
        :summary-method="getSummaries"
      >
        <el-table-column prop="department" label="部门" width="150" fixed  />
        <el-table-column prop="count" label="人数" width="80" align="center"  />
        <el-table-column prop="baseSalary" label="基本工资" align="right">
          <template #default="{ row }">
            {{ formatMoney(row.baseSalary) }}
          </template>
        </el-table-column>
        <el-table-column prop="positionSalary" label="岗位工资" align="right">
          <template #default="{ row }">
            {{ formatMoney(row.positionSalary) }}
          </template>
        </el-table-column>
        <el-table-column prop="performanceSalary" label="绩效工资" align="right">
          <template #default="{ row }">
            {{ formatMoney(row.performanceSalary) }}
          </template>
        </el-table-column>
        <el-table-column prop="allowance" label="津贴补贴" align="right">
          <template #default="{ row }">
            {{ formatMoney(row.allowance) }}
          </template>
        </el-table-column>
        <el-table-column prop="total" label="合计" align="right" width="120">
          <template #default="{ row }">
            <strong>{{ formatMoney(row.total) }}</strong>
          </template>
        </el-table-column>
        <el-table-column prop="average" label="人均" align="right" width="100">
          <template #default="{ row }">
            {{ formatMoney(row.average) }}
          </template>
        </el-table-column>
        <el-table-column prop="ratio" label="占比" align="center" width="80">
          <template #default="{ row }">
            {{ row.ratio }}%
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Download, InfoFilled } from '@element-plus/icons-vue'
import echarts, { createChart } from '@/utils/echarts'
import { salaryApi } from '@/api/salary'

// 搜索表单
const searchForm = reactive({
  periodType: 'month',
  dateRange: [],
  year: new Date().getFullYear().toString(),
  quarter: 'Q1',
  departmentId: ''
})

// 部门树
const departmentTree = ref([
  {
    id: 'all',
    name: 'HrHr全部部门',
    children: [
      { id: 'cs', name: '计算机系' },
      { id: 'math', name: '数学系' },
      { id: 'phy', name: '物理系' }
    ]
  }
])

// 核心指标
const metrics = reactive({
  total: 1850.50,
  totalYoY: 8.5,
  average: 18500,
  averageQoQ: 2.3,
  count: 1000,
  countRatio: 95.2,
  budgetRate: 87.5
})

// 图表
const trendType = ref('amount')
const costType = ref('income')
const trendChartRef = ref<HTMLElement>()
const distributionChartRef = ref<HTMLElement>()
const compositionChartRef = ref<HTMLElement>()
const costChartRef = ref<HTMLElement>()

let trendChart: echarts.ECharts
let distributionChart: echarts.ECharts
let compositionChart: echarts.ECharts
let costChart: echarts.ECharts

// 表格数据
const tableData = ref([
  {
    department: '计算机系',
    count: 120,
    baseSalary: 1200000,
    positionSalary: 600000,
    performanceSalary: 360000,
    allowance: 240000,
    total: 2400000,
    average: 20000,
    ratio: 25.8
  },
  {
    department: '数学系',
    count: 100,
    baseSalary: 950000,
    positionSalary: 450000,
    performanceSalary: 280000,
    allowance: 180000,
    total: 1860000,
    average: 18600,
    ratio: 20.0
  },
  {
    department: '物理系',
    count: 90,
    baseSalary: 850000,
    positionSalary: 400000,
    performanceSalary: 250000,
    allowance: 160000,
    total: 1660000,
    average: 18444,
    ratio: 17.8
  }
])

// 初始化
onMounted(() => {
  nextTick(() => {
    initCharts()
    // 初始加载数据
    handleSearch()
  })
})

// 监听趋势类型变化
watch(trendType, () => {
  if (trendChart) {
    updateTrendChart()
  }
})

// 监听成本类型变化
watch(costType, () => {
  if (costChart) {
    updateCostChart()
  }
})

// 初始化图表
const initCharts = () => {
  // 趋势图
  if (trendChartRef.value) {
    trendChart = createChart(trendChartRef.value)
    updateTrendChart()
  }

  // 分布图
  if (distributionChartRef.value) {
    distributionChart = createChart(distributionChartRef.value)
    updateDistributionChart()
  }

  // 构成图
  if (compositionChartRef.value) {
    compositionChart = createChart(compositionChartRef.value)
    updateCompositionChart()
  }

  // 成本图
  if (costChartRef.value) {
    costChart = createChart(costChartRef.value)
    updateCostChart()
  }

  // 监听窗口变化
  window.addEventListener('resize', () => {
    trendChart?.resize()
    distributionChart?.resize()
    compositionChart?.resize()
    costChart?.resize()
  })
}

// 更新图表数据
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  // 更新趋势图数据
  if (trendChart && data.trend) {
    const trendOption = trendChart.getOption()
    trendOption.xAxis[0].data = data.trend.labels
    trendOption.series[0].data = trendType.value === 'amount' ? data.trend.amounts : data.trend.amounts
    trendOption.series[1].data = data.trend.growthRates
    trendChart.setOption(trendOption)
  }
  
  // 更新分布图数据
  if (distributionChart && data.distribution) {
    const distributionOption = distributionChart.getOption()
    distributionOption.series[0].data = data.distribution
    distributionChart.setOption(distributionOption)
  }
  
  // 更新构成图数据
  if (compositionChart && data.composition) {
    const compositionOption = compositionChart.getOption()
    compositionOption.xAxis[0].data = data.composition.labels
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
    data.composition.series.forEach((series: unknown, index: number) => {
      if (compositionOption.series[index]) {
        compositionOption.series[index].data = series.data
      }
    })
    compositionChart.setOption(compositionOption)
  }
  
  // 更新成本占比图
  if (costChart && data.costRatio) {
    const costOption = costChart.getOption()
    costOption.series[0].data[0].value = data.costRatio.value
    costChart.setOption(costOption)
  }
}

// 更新趋势图
const updateTrendChart = () => {
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['薪资总额', '同比增长']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: [
      {
        type: 'value',
        name: '金额(万元)',
        axisLabel: {
          formatter: '{value}'
        }
      },
      {
        type: 'value',
        name: '增长率(%)',
        axisLabel: {
          formatter: '{value}%'
        }
      }
    ],
    series: [
      {
        name: '薪资总额',
        type: 'bar',
        data: [1820, 1850, 1880, 1920, 1950, 1980],
        itemStyle: {
          color: '#409eff'
        }
      },
      {
        name: '同比增长',
        type: 'line',
        yAxisIndex: 1,
        data: [5.2, 6.8, 7.5, 8.2, 8.5, 9.1],
        itemStyle: {
          color: '#67c23a'
        }
      }
    ]
  }
  
  trendChart.setOption(option)
}

// 更新分布图
const updateDistributionChart = () => {
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}万元 ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        type: 'pie',
        radius: '50%',
        data: [
          { value: 240, name: '计算机系' },
          { value: 186, name: '数学系' },
          { value: 166, name: '物理系' },
          { value: 145, name: '化学系' },
          { value: 122, name: '生物系' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  distributionChart.setOption(option)
}

// 更新构成图
const updateCompositionChart = () => {
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['基本工资', '岗位工资', '绩效工资', '津贴补贴']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value',
      name: '金额(万元)'
    },
    series: [
      {
        name: '基本工资',
        type: 'bar',
        stack: '总量',
        data: [820, 832, 901, 934, 1290, 1330]
      },
      {
        name: '岗位工资',
        type: 'bar',
        stack: '总量',
        data: [220, 182, 191, 234, 290, 330]
      },
      {
        name: '绩效工资',
        type: 'bar',
        stack: '总量',
        data: [150, 232, 201, 154, 190, 330]
      },
      {
        name: '津贴补贴',
        type: 'bar',
        stack: '总量',
        data: [62, 82, 91, 74, 90, 130]
      }
    ]
  }
  
  compositionChart.setOption(option)
}

// 更新成本图
const updateCostChart = () => {
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}%'
    },
    series: [
      {
        name: '成本占比',
        type: 'gauge',
        startAngle: 180,
        endAngle: 0,
        min: 0,
        max: 100,
        splitNumber: 10,
        radius: '90%',
        axisLine: {
          lineStyle: {
            width: 20,
            color: [
              [0.6, '#67c23a'],
              [0.8, '#e6a23c'],
              [1, '#f56c6c']
            ]
          }
        },
        pointer: {
          width: 5
        },
        axisTick: {
          length: 10,
          lineStyle: {
            color: 'auto'
          }
        },
        splitLine: {
          length: 15,
          lineStyle: {
            color: 'auto'
          }
        },
        title: {
          show: true,
          offsetCenter: [0, '-20%'],
          textStyle: {
            fontSize: 14
          }
        },
        detail: {
          formatter: '{value}%',
          offsetCenter: [0, '10%'],
          textStyle: {
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        data: [
          {
            value: 35.2,
            name: '薪资成本占比'
          }
        ]
      }
    ]
  }
  
  costChart.setOption(option)
}

// 期间类型变化
const handlePeriodChange = () => {
  searchForm.dateRange = []
}

// 查询
const handleSearch = async () => {
  try {
      message: '正在查询数据...',
      duration: 0
    })
    
    // 构建查询参数
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const params: unknown = {
      periodType: searchForm.periodType,
      departmentId: searchForm.departmentId
    }
    
    // 根据期间类型设置不同的时间参数
    if (searchForm.periodType === 'month' && searchForm.dateRange?.length === 2) {
      params.dateRange = searchForm.dateRange
    } else if (searchForm.periodType === 'year') {
      params.year = searchForm.year
    } else if (searchForm.periodType === 'quarter') {
      params.year = new Date().getFullYear().toString()
      params.quarter = searchForm.quarter
    }
    
    // 调用API获取统计数据
    const {data: _data} =  await salaryApi.getSalaryTotalStatistics(params)
    
    // 更新核心指标
    Object.assign(metrics, data.metrics)
    
    // 更新表格数据
    tableData.value 
  }

  .metrics-row {
    margin-bottom: 20px;

    .metric-card {
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-4px);
      }

      .unit {
        font-size: 14px;
        color: #909399;
      }

      .metric-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;
        font-size: 14px;
        color: #909399;

        .up {
          color: #67c23a;
        }

        .down {
          color: #f56c6c;
        }

        :deep(.el-progress) {
          flex: 1;
          margin-left: 10px;
        }
      }
    }
  }

  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .chart-container {
    height: 300px;
  }
}
</style>