<template>
  <div class="salary-calculation-test">
    <el-card class="header-card" shadow="never">
      <h2>薪资计算引擎测试</h2>
      <p>测试公式解析、变量计算、薪资计算等功能</p>
    </el-card>

    <!-- 公式测试 -->
    <el-card class="test-card" shadow="never">
      <template #header>
        <span>公式解析测试</span>
      </template>
      
      <el-form label-width="100px">
        <el-form-item label="测试公式">
          <el-input
            v-model="formulaTest.formula"
            type="textarea"
            :rows="3"
            placeholder="输入公式，如: BASE_SALARY * 1.2 + 1000"
            />
        </el-form-item>
        
        <el-form-item label="变量设置">
          <div class="variable-list">
            <div v-for="(value, key) in formulaTest.variables" :key="key" class="variable-item">
              <span class="variable-name">{{ key }}:</span>
              <el-input-number v-model="formulaTest.variables[key]" :precision="2"   />
              <el-button link type="danger" @click="removeVariable(key)">删除</el-button>
            </div>
            <div class="variable-add">
              <el-input v-model="newVariable.name" placeholder="变量名" style="width: 150px"   />
              <el-input-number v-model="newVariable.value" placeholder="值" :precision="2" style="width: 150px"   />
              <el-button type="primary" @click="addVariable">添加变量</el-button>
            </div>
          </div>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="testFormula">测试公式</el-button>
          <el-button @click="clearFormulaTest">清空</el-button>
        </el-form-item>
        
        <el-form-item label="解析结果" v-if="formulaTest.result">
          <el-alert
            :title="`计算结果: ${formulaTest.result.value}`"
            :type="formulaTest.result.success ? 'success' : 'error'"
            :description="formulaTest.result.message"
            :closable="false"
           />
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 薪资计算测试 -->
    <el-card class="test-card" shadow="never">
      <template #header>
        <span>薪资计算测试</span>
      </template>
      
      <el-form :model="salaryTest" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="员工ID">
              <el-input v-model="salaryTest.employeeId" placeholder="测试员工ID"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="薪资月份">
              <el-date-picker
                v-model="salaryTest.period"
                type="month"
                placeholder="选择月份"
                value-format="YYYY-MM"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="出勤天数">
              <el-input-number v-model="salaryTest.baseData.attendanceDays" :min="0" :max="31"   />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="应出勤天数">
              <el-input-number v-model="salaryTest.baseData.workDays" :min="0" :max="31"   />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="加班时数">
              <el-input-number v-model="salaryTest.baseData.overtimeHours" :min="0"   />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="绩效系数">
              <el-input-number v-model="salaryTest.baseData.performanceRate" :min="0" :max="2" :step="0.1"   />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="子女数量">
              <el-input-number v-model="salaryTest.baseData.childrenCount" :min="0" :max="10"   />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="赡养老人">
              <el-input-number v-model="salaryTest.baseData.elderlyCount" :min="0" :max="4"   />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item>
          <el-button type="primary" @click="calculateSalary" :loading="calculating">
            计算薪资
          </el-button>
          <el-button @click="resetSalaryTest">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 计算结果 -->
      <div v-if="salaryTest.result" class="calculation-result">
        <el-divider>计算结果</el-divider>
        
        <el-descriptions :column="3" border>
          <el-descriptions-item label="应发工资">
            <span class="income">¥{{ formatNumber(salaryTest.result.totalIncome) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="应扣金额">
            <span class="deduction">¥{{ formatNumber(salaryTest.result.totalDeduction) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="实发工资">
            <span class="net-salary">¥{{ formatNumber(salaryTest.result.netSalary) }}</span>
          </el-descriptions-item>
        </el-descriptions>
        
        <el-table :data="salaryTest.result.items" style="margin-top: 20px" max-height="400">
          <el-table-column prop="itemName" label="项目" width="150"  />
          <el-table-column prop="itemCode" label="代码" width="150"  />
          <el-table-column prop="itemType" label="类型" width="100">
            <template #default="{ row }">
              <el-tag :type="row.itemType === 'deduction' ? 'danger' : 'success'">
                {{ row.itemType === 'deduction' ? '扣除' : '收入' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="value" label="金额" width="120" align="right">
            <template #default="{ row }">
              <span :class="row.itemType === 'deduction' ? 'deduction' : 'income'">
                {{ row.itemType === 'deduction' ? '-' : '' }}¥{{ formatNumber(row.value) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="formula" label="计算公式" min-width="200" show-overflow-tooltip  />
        </el-table>
        
        <div v-if="salaryTest.result.warnings?.length" class="warnings">
          <el-alert
            v-for="(warning, index) in salaryTest.result.warnings"
            :key="index"
            :title="warning"
            type="warning"
            :closable="false"
            style="margin-top: 10px"
           />
        </div>
      </div>
    </el-card>

    <!-- 函数测试 -->
    <el-card class="test-card" shadow="never">
      <template #header>
        <span>内置函数测试</span>
      </template>
      
      <div class="function-list">
        <el-row :gutter="20">
          <el-col :span="8" v-for="func in builtInFunctions" :key="func.name">
            <div class="function-item">
              <h4>{{ func.name }}</h4>
              <p>{{ func.description }}</p>
              <el-input
                v-model="func.testInput"
                placeholder="输入测试参数"
                size="small"
                />
              <el-button size="small" @click="testFunction(func)">测试</el-button>
              <div v-if="func.result" class="function-result">
                结果: {{ func.result }}
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { FormulaParser } from '@/utils/salary/formulaParser'
import { CalculationEngine } from '@/utils/salary/calculationEngine'
import { SalaryCalculationService } from '@/services/salaryCalculationService'

// 公式测试
const formulaTest = reactive({
  formula: 'BASE_SALARY * 1.2 + BONUS',
  variables: {
    BASE_SALARY: 10000,
    BONUS: 2000
  } as Record<string, number>,
  result: null as unknown
})

const newVariable = reactive({
  name: '',
  value: 0
})

// 薪资计算测试
const salaryTest = reactive({
  employeeId: 'EMP001',
  period: new Date().toISOString().slice(0, 7),
  structureId: 'STR001',
  baseData: {
    attendanceDays: 22,
    workDays: 22,
    overtimeHours: 0,
    leaveDays: 0,
    performanceRate: 1,
    childrenCount: 0,
    elderlyCount: 0,
    hasHousingLoan: 0
  },
  result: null as unknown
})

const calculating = ref(false)

// 内置函数列表
const builtInFunctions = reactive([
  {
    name: 'MAX',
    description: '返回最大值',
    testInput: '100, 200, 150',
    result: ''
  },
  {
    name: 'MIN',
    description: '返回最小值',
    testInput: '100, 200, 150',
    result: ''
  },
  {
    name: 'ROUND',
    description: '四舍五入',
    testInput: '123.456, 2',
    result: ''
  },
  {
    name: 'IF',
    description: '条件判断',
    testInput: '1 > 0, 100, 200',
    result: ''
  },
  {
    name: 'SUM',
    description: '求和',
    testInput: '100, 200, 300',
    result: ''
  },
  {
    name: 'AVG',
    description: '平均值',
    testInput: '100, 200, 300',
    result: ''
  }
])

// 测试公式
const testFormula = () => {
  try {
    const parser = new FormulaParser()
    const engine = new CalculationEngine()
    
    // 设置变量
    Object.entries(formulaTest.variables).forEach(([name, value]) => {
      engine.setVariable(name, value)
    })
    
    // 解析公式
    const ast = parser.parse(formulaTest.formula)
    
    // 执行计算
    const result = engine.evaluate(ast)
    
    formulaTest.result = {
      success: true,
      value: result,
      message: 'AST: ' + JSON.stringify(ast, null, 2)
    }
    
    ElMessage.success('公式计算成功')
   
  } catch (error: unknown) {
    formulaTest.result = {
      success: false,
      value: 'ERROR',
      message: error.message
    }
    ElMessage.error('公式计算失败: ' + error.message)
  }
}

// 添加变量
const addVariable = () => {
  if (!newVariable.name) {
    ElMessage.warning('请输入变量名')
    return
  }
  
  formulaTest.variables[newVariable.name] = newVariable.value
  newVariable.name = ''
  newVariable.value = 0
}

// 删除变量
const removeVariable = (key: string) => {
  delete formulaTest.variables[key]
}

// 清空公式测试
const clearFormulaTest = () => {
  formulaTest.formula = ''
  formulaTest.variables = {}
  formulaTest.result = null
}

// 计算薪资
const calculateSalary = async () => {
  calculating.value = true
  try {
    const service = new SalaryCalculationService()
    const result = await service.calculateSalary({
      employeeId: salaryTest.employeeId,
      period: salaryTest.period,
      structureId: salaryTest.structureId,
      baseData: salaryTest.baseData
    })
    
    salaryTest.result = result
    ElMessage.success('薪资计算完成')
   
  } catch (error: unknown) {
    ElMessage.error('薪资计算失败: ' + error.message)
  } finally {
    calculating.value = false
  }
}

// 重置薪资测试
const resetSalaryTest = () => {
  salaryTest.baseData = {
    attendanceDays: 22,
    workDays: 22,
    overtimeHours: 0,
    leaveDays: 0,
    performanceRate: 1,
    childrenCount: 0,
    elderlyCount: 0,
    hasHousingLoan: 0
  }
  salaryTest.result = null
}

// 测试函数
   
const testFunction = (func: unknown) => {
  try {
    const engine = new CalculationEngine()
    const formula = `${func.name}(${func.testInput})`
    const parser = new FormulaParser()
    const ast = parser.parse(formula)
    const result = engine.evaluate(ast)
    func.result = result
   
  } catch (error: unknown) {
    func.result = 'ERROR: ' + error.message
  }
}

// 格式化数字
const formatNumber = (num: number) => {
  return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}
</script>

<style lang="scss" scoped>
.salary-calculation-test {
  padding: 20px;

  .header-card {
    margin-bottom: 20px;

    h2 {
      margin: 0 0 10px 0;
      color: #303133;
    }

    p {
      margin: 0;
      color: #606266;
    }
  }

  .test-card {
    margin-bottom: 20px;
  }

  .variable-list {
    .variable-item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      gap: 10px;

      .variable-name {
        width: 150px;
        font-weight: bold;
      }
    }

    .variable-add {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-top: 10px;
      padding-top: 10px;
      border-top: 1px dashed #e4e7ed;
    }
  }

  .calculation-result {
    .income {
      color: #67c23a;
      font-weight: bold;
    }

    .deduction {
      color: #f56c6c;
      font-weight: bold;
    }

    .net-salary {
      color: #409eff;
      font-weight: bold;
      font-size: 18px;
    }
  }

  .function-list {
    .function-item {
      padding: 15px;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      margin-bottom: 20px;

      h4 {
        margin: 0 0 5px 0;
        color: #303133;
      }

      p {
        margin: 0 0 10px 0;
        color: #909399;
        font-size: 12px;
      }

      .el-input {
        margin-bottom: 10px;
      }

      .function-result {
        margin-top: 10px;
        padding: 10px;
        background-color: #f5f7fa;
        border-radius: 4px;
        font-family: monospace;
      }
    }
  }

  .warnings {
    margin-top: 20px;
  }
}
</style>