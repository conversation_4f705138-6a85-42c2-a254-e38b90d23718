<template>
  <div class="modern-personnel-change" :class="{ 'mobile-layout': isMobile }">
    <!-- 现代化页面头部 -->
    <div class="page-header" role="banner">
      <div class="header-content">
        <div class="header-title">
          <h1>人事变动管理</h1>
          <p class="subtitle">智能化人事变动流程管理与数据分析平台</p>
        </div>

        <!-- 快速操作按钮 -->
        <div class="header-actions" v-if="!isMobile">
          <el-button type="primary" size="large" @click="handleQuickApply">
            <el-icon><Plus /></el-icon>
            快速申请
          </el-button>

          <el-dropdown trigger="click" placement="bottom-end">
            <el-button size="large">
              <el-icon><MoreFilled /></el-icon>
              更多操作
            </el-button>

            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="handleBatchProcess">
                  <el-icon><Operation /></el-icon>
                  批量处理
                </el-dropdown-item>
                <el-dropdown-item @click="handleExportReport">
                  <el-icon><Download /></el-icon>
                  导出报告
                </el-dropdown-item>
                <el-dropdown-item @click="handleSystemConfig" divided>
                  <el-icon><Setting /></el-icon>
                  系统配置
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>

        <!-- 移动端操作按钮 -->
        <div class="mobile-actions" v-if="isMobile">
          <el-button type="primary" circle @click="handleQuickApply">
            <el-icon><Plus /></el-icon>
          </el-button>
          <el-button circle @click="showMobileMenu = true">
            <el-icon><MoreFilled /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 现代化统计概览 -->
    <div class="stats-overview">
      <div class="stats-grid" :class="{ 'mobile-grid': isMobile }">
        <div
          v-for="stat in statsCards"
          :key="stat.id"
          class="modern-stats-card"
          :class="[`stats-${stat.type}`, { 'clickable': stat.clickable }]"
          @click="stat.clickable && handleStatsClick(stat.id)"
          role="button"
          :tabindex="stat.clickable ? 0 : -1"
          :aria-label="`${stat.title}: ${stat.value}${stat.unit || ''}`"
          @keydown.enter="stat.clickable && handleStatsClick(stat.id)"
          @keydown.space.prevent="stat.clickable && handleStatsClick(stat.id)"
        >
          <div class="stats-header">
            <div class="stats-icon" :style="{ background: stat.gradient }">
              <el-icon :size="24">
                <component :is="stat.icon" />
              </el-icon>
            </div>
            <div class="stats-trend" v-if="stat.trend !== undefined">
              <el-icon :class="stat.trend >= 0 ? 'trend-up' : 'trend-down'">
                <component :is="stat.trend >= 0 ? ArrowUp : ArrowDown" />
              </el-icon>
              <span class="trend-value">{{ Math.abs(stat.trend) }}%</span>
            </div>
          </div>

          <div class="stats-content">
            <div class="stats-value">
              <span class="stats-number">{{ stat.value }}</span>
              <span class="stats-unit" v-if="stat.unit">{{ stat.unit }}</span>
            </div>
            <div class="stats-title">{{ stat.title }}</div>
            <div class="stats-subtitle" v-if="stat.subtitle">{{ stat.subtitle }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 现代化功能导航 -->
    <div class="function-navigation">
      <div class="nav-grid" :class="{ 'mobile-grid': isMobile }">
        <div
          v-for="nav in navigationItems"
          :key="nav.id"
          class="modern-nav-card"
          :class="[`nav-${nav.type}`, { 'clickable': nav.clickable }]"
          @click="nav.clickable && navigateTo(nav.route)"
          role="button"
          :tabindex="nav.clickable ? 0 : -1"
          :aria-label="`${nav.title}: ${nav.description}`"
          @keydown.enter="nav.clickable && navigateTo(nav.route)"
          @keydown.space.prevent="nav.clickable && navigateTo(nav.route)"
        >
          <div class="nav-header">
            <div class="nav-icon" :style="{ background: nav.gradient }">
              <el-icon :size="28">
                <component :is="nav.icon" />
              </el-icon>
            </div>
            <div class="nav-badge" v-if="nav.badge">
              <span class="badge-text">{{ nav.badge }}</span>
            </div>
          </div>

          <div class="nav-content">
            <h3 class="nav-title">{{ nav.title }}</h3>
            <p class="nav-description">{{ nav.description }}</p>
            <div class="nav-stats" v-if="nav.stats">
              <span class="stats-value">{{ nav.stats.value }}</span>
              <span class="stats-label">{{ nav.stats.label }}</span>
            </div>
          </div>

          <div class="nav-footer">
            <el-icon class="nav-arrow">
              <ArrowRight />
            </el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近变动记录 -->
    <div class="recent-changes">
      <div class="changes-header">
        <h3>最近变动记录</h3>
        <div class="header-controls">
          <el-button-group size="small">
            <el-button
              v-for="filter in changeFilters"
              :key="filter.key"
              :type="activeChangeFilter === filter.key ? 'primary' : 'default'"
              @click="handleChangeFilter(filter.key)"
            >
              {{ filter.label }}
            </el-button>
          </el-button-group>
          <el-button type="primary" text @click="viewAllChanges">
            查看全部
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
      </div>

      <div class="changes-timeline">
        <div
          v-for="change in recentChanges"
          :key="change.id"
          class="timeline-item"
          @click="viewDetail(change)"
        >
          <div class="timeline-dot" :class="`dot-${change.changeType.toLowerCase()}`">
            <el-icon>
              <component :is="getChangeIcon(change.changeType)" />
            </el-icon>
          </div>
          <div class="timeline-content">
            <div class="change-header">
              <div class="change-info">
                <span class="employee-name">{{ change.employeeName }}</span>
                <el-tag
                  :type="getChangeTypeTag(change.changeType)"
                  size="small"
                  class="change-type-tag"
                >
                  {{ change.changeTypeName }}
                </el-tag>
              </div>
              <div class="change-meta">
                <span class="change-date">{{ change.changeDate }}</span>
                <el-tag :type="getStatusTag(change.status)" size="small">
                  {{ change.statusName }}
                </el-tag>
              </div>
            </div>

            <div class="change-details">
              <div class="department-change" v-if="change.fromDepartment !== '-' && change.toDepartment !== '-'">
                <span class="from-dept">{{ change.fromDepartment }}</span>
                <el-icon class="arrow-icon"><ArrowRight /></el-icon>
                <span class="to-dept">{{ change.toDepartment }}</span>
              </div>
              <div class="single-dept" v-else>
                {{ change.fromDepartment !== '-' ? change.fromDepartment : change.toDepartment }}
              </div>
            </div>

            <div class="change-footer">
              <span class="operator">操作人: {{ change.operator }}</span>
              <el-button size="small" type="primary" text @click.stop="viewDetail(change)">
                查看详情
                <el-icon><View /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 移动端操作菜单 -->
    <el-drawer
      v-model="showMobileMenu"
      title="操作菜单"
      direction="btt"
      size="auto"
    >
      <div class="mobile-menu-content">
        <div class="menu-item" @click="handleQuickApply">
          <el-icon><Plus /></el-icon>
          <span>快速申请</span>
        </div>
        <div class="menu-item" @click="handleBatchProcess">
          <el-icon><Operation /></el-icon>
          <span>批量处理</span>
        </div>
        <div class="menu-item" @click="handleExportReport">
          <el-icon><Download /></el-icon>
          <span>导出报告</span>
        </div>
        <div class="menu-item" @click="handleSystemConfig">
          <el-icon><Setting /></el-icon>
          <span>系统配置</span>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'PersonnelChangeManagement'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  UserFilled,
  Switch,
  Clock,
  Right,
  RefreshRight,
  DataAnalysis,
  Plus,
  MoreFilled,
  Operation,
  Download,
  Setting,
  ArrowRight,
  ArrowUp,
  ArrowDown,
  View
} from '@element-plus/icons-vue'
import { useMobile } from '@/composables/useMobile'
import { personnelChangeStatsApi } from '@/api/personnelChange'

const router = useRouter()

// 移动端适配
const {isMobile: _isMobile} =  useMobile()

// UI状态
const showMobileMenu 
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 24px;
}

.mobile-layout {
  padding: 16px;
}

/* 页面头部 */
.page-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title h1 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  margin: 0;
  color: #7f8c8d;
  font-size: 16px;
  font-weight: 400;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.mobile-actions {
  display: flex;
  gap: 8px;
}

/* 统计概览 */
.stats-overview {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.mobile-grid {
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 16px;
}

.modern-stats-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.modern-stats-card.clickable {
  cursor: pointer;
}

.modern-stats-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.stats-icon {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.stats-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
}

.trend-up {
  color: #27ae60;
}

.trend-down {
  color: #e74c3c;
}

.stats-content {
  margin-bottom: 16px;
}

.stats-value {
  display: flex;
  align-items: baseline;
  gap: 4px;
  margin-bottom: 8px;
}

.stats-number {
  font-size: 36px;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
}

.stats-unit {
  font-size: 16px;
  color: #7f8c8d;
  font-weight: 500;
}

.stats-title {
  font-size: 16px;
  font-weight: 600;
  color: #34495e;
  margin-bottom: 4px;
}

.stats-subtitle {
  font-size: 12px;
  color: #95a5a6;
}

/* 功能导航 */
.function-navigation {
  margin-bottom: 24px;
}

.nav-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 20px;
}

.modern-nav-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.modern-nav-card.clickable {
  cursor: pointer;
}

.modern-nav-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.nav-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.nav-icon {
  width: 64px;
  height: 64px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.nav-badge {
  background: #ff4757;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
}

.nav-content h3 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.nav-description {
  margin: 0 0 12px 0;
  color: #7f8c8d;
  font-size: 14px;
  line-height: 1.5;
}

.nav-stats {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.stats-value {
  font-size: 20px;
  font-weight: 700;
  color: #2c3e50;
}

.stats-label {
  font-size: 12px;
  color: #95a5a6;
}

.nav-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

.nav-arrow {
  color: #bdc3c7;
  transition: all 0.3s ease;
}

.modern-nav-card:hover .nav-arrow {
  color: #3498db;
  transform: translateX(4px);
}

/* 最近变动记录 */
.recent-changes {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.changes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.changes-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.header-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.changes-timeline {
  position: relative;
}

.timeline-item {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.timeline-item:hover {
  transform: translateX(4px);
}

.timeline-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 15px;
  top: 32px;
  bottom: -20px;
  width: 2px;
  background: #e1e8ed;
}

.timeline-dot {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  flex-shrink: 0;
  z-index: 1;
  position: relative;
}

.dot-new_employee {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.dot-position_adjustment {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.dot-retirement {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.dot-leave_school {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.dot-reemployment {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.timeline-content {
  flex: 1;
  background: #f8fafc;
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.timeline-item:hover .timeline-content {
  background: #ffffff;
  border-color: #3498db;
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.1);
}

.change-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.change-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.employee-name {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.change-type-tag {
  margin-left: 8px;
}

.change-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.change-date {
  font-size: 12px;
  color: #94a3b8;
}

.change-details {
  margin-bottom: 12px;
}

.department-change {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #64748b;
}

.from-dept {
  color: #94a3b8;
}

.arrow-icon {
  color: #cbd5e1;
}

.to-dept {
  color: #2c3e50;
  font-weight: 500;
}

.single-dept {
  font-size: 14px;
  color: #64748b;
}

.change-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.operator {
  font-size: 12px;
  color: #94a3b8;
}

/* 移动端菜单 */
.mobile-menu-content {
  padding: 20px 0;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  cursor: pointer;
  transition: background 0.3s ease;
  border-radius: 8px;
  margin: 0 20px 8px 20px;
}

.menu-item:hover {
  background: #f5f7fa;
}

.menu-item span {
  font-size: 16px;
  color: #2c3e50;
}
</style>
