<template>
  <div class="personnel-change-report">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>人事异动报表</h2>
      <p>生成和查看人事异动月度、季度、年度统计报表</p>
    </div>

    <!-- 报表筛选条件 -->
    <el-card class="filter-card" shadow="never">
      <el-form :model="filterForm" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="报表类型">
              <el-select v-model="filterForm.reportType" @change="handleReportTypeChange">
                <el-option label="月度报表" value="MONTHLY"  />
                <el-option label="季度报表" value="QUARTERLY"  />
                <el-option label="年度报表" value="YEARLY"  />
                <el-option label="自定义时间" value="CUSTOM"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item v-if="filterForm.reportType === 'MONTHLY'" label="选择月份">
              <el-date-picker
                v-model="filterForm.month"
                type="month"
                placeholder="选择月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
               />
            </el-form-item>
            <el-form-item v-else-if="filterForm.reportType === 'QUARTERLY'" label="选择季度">
              <el-select v-model="filterForm.quarter">
                <el-option label="第一季度" value="Q1"  />
                <el-option label="第二季度" value="Q2"  />
                <el-option label="第三季度" value="Q3"  />
                <el-option label="第四季度" value="Q4"  />
              </el-select>
            </el-form-item>
            <el-form-item v-else-if="filterForm.reportType === 'YEARLY'" label="选择年份">
              <el-date-picker
                v-model="filterForm.year"
                type="year"
                placeholder="选择年份"
                format="YYYY"
                value-format="YYYY"
               />
            </el-form-item>
            <el-form-item v-else-if="filterForm.reportType === 'CUSTOM'" label="时间范围">
              <el-date-picker
                v-model="filterForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
               />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="部门筛选">
              <el-select v-model="filterForm.department" clearable>
                <el-option label="全部部门" value=""  />
                <el-option
                  v-for="dept in departmentOptions"
                  :key="dept.id"
                  :label="dept.name"
                  :value="dept.id"
                 />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item>
              <el-button type="primary" @click="handleGenerate">
                <el-icon><DataAnalysis /></el-icon>
                生成报表
              </el-button>
              <el-button @click="handleExport">
                <el-icon><Download /></el-icon>
                导出报表
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 报表概览 -->
    <el-row :gutter="20" class="overview-row">
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon onboarding">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-number">{{ overview.onboarding }}</div>
              <div class="overview-label">入职人数</div>
              <div class="overview-trend" :class="getTrendClass(overview.onboardingTrend)">
                <el-icon><TrendCharts /></el-icon>
                {{ overview.onboardingTrend }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon resignation">
              <el-icon><User /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-number">{{ overview.resignation }}</div>
              <div class="overview-label">离职人数</div>
              <div class="overview-trend" :class="getTrendClass(-overview.resignationTrend)">
                <el-icon><TrendCharts /></el-icon>
                {{ overview.resignationTrend }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon transfer">
              <el-icon><Switch /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-number">{{ overview.transfer }}</div>
              <div class="overview-label">调动人数</div>
              <div class="overview-trend" :class="getTrendClass(overview.transferTrend)">
                <el-icon><TrendCharts /></el-icon>
                {{ overview.transferTrend }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon promotion">
              <el-icon><Trophy /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-number">{{ overview.promotion }}</div>
              <div class="overview-label">晋升人数</div>
              <div class="overview-trend" :class="getTrendClass(overview.promotionTrend)">
                <el-icon><TrendCharts /></el-icon>
                {{ overview.promotionTrend }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表展示 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <span class="chart-title">人事异动趋势图</span>
          </template>
          <div id="trendChart" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <span class="chart-title">部门异动分布</span>
          </template>
          <div id="distributionChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细数据表格 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="table-header">
          <span class="table-title">人事异动明细</span>
          <el-radio-group v-model="currentTab" @change="handleTabChange">
            <el-radio-button value="ALL">全部</el-radio-button>
            <el-radio-button value="ONBOARDING">入职</el-radio-button>
            <el-radio-button value="RESIGNATION">离职</el-radio-button>
            <el-radio-button value="TRANSFER">调动</el-radio-button>
            <el-radio-button value="PROMOTION">晋升</el-radio-button>
          </el-radio-group>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
        :summary-method="getSummaries"
        show-summary
      >
        <el-table-column prop="date" label="日期" width="100"  />
        <el-table-column prop="type" label="类型" width="80">
          <template #default="scope">
            <el-tag :type="getTypeTagType(scope.row.type)" size="small">
              {{ getTypeText(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="employeeName" label="姓名" width="100"  />
        <el-table-column prop="employeeNo" label="工号" width="100"  />
        <el-table-column prop="department" label="部门" width="150" show-overflow-tooltip  />
        <el-table-column prop="position" label="岗位" width="120" show-overflow-tooltip  />
        <el-table-column prop="description" label="异动说明" min-width="200" show-overflow-tooltip  />
        <el-table-column prop="effectiveDate" label="生效日期" width="100"  />
        <el-table-column prop="approver" label="审批人" width="100"  />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 导出对话框 -->
    <el-dialog
      v-model="exportDialogVisible"
      title="导出报表"
      width="500px"
    >
      <el-form :model="exportForm" label-width="100px">
        <el-form-item label="导出格式">
          <el-radio-group v-model="exportForm.format">
            <el-radio value="EXCEL">Excel格式</el-radio>
            <el-radio value="PDF">PDF格式</el-radio>
            <el-radio value="WORD">Word格式</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="包含内容">
          <el-checkbox-group v-model="exportForm.content">
            <el-checkbox label="overview">统计概览</el-checkbox>
            <el-checkbox label="charts">图表分析</el-checkbox>
            <el-checkbox label="detail">明细数据</el-checkbox>
            <el-checkbox label="summary">汇总统计</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="exportDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleExportConfirm">
          确定导出
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  DataAnalysis,
  Download,
  UserFilled,
  User,
  Switch,
  Trophy,
  TrendCharts
} from '@element-plus/icons-vue'
import { organizationApi } from '@/api/organization'
import * as echarts from 'echarts'

// 响应式数据
const loading = ref(false)
const tableData = ref<any[]>([])
const departmentOptions = ref<any[]>([])
const currentTab = ref('ALL')
const exportDialogVisible = ref(false)

// 筛选表单
const filterForm = reactive({
  reportType: 'MONTHLY',
  month: new Date().toISOString().substring(0, 7),
  quarter: 'Q1',
  year: new Date().getFullYear().toString(),
  dateRange: [],
  department: ''
})

// 统计概览
const overview = reactive({
  onboarding: 0,
  onboardingTrend: 0,
  resignation: 0,
  resignationTrend: 0,
  transfer: 0,
  transferTrend: 0,
  promotion: 0,
  promotionTrend: 0
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 导出表单
const exportForm = reactive({
  format: 'EXCEL',
  content: ['overview', 'charts', 'detail', 'summary']
})

// 图表实例
let trendChart: echarts.ECharts | null = null
let distributionChart: echarts.ECharts | null = null

// 处理报表类型变化
const handleReportTypeChange = () => {
  // 重置相关数据
  if (filterForm.reportType === 'CUSTOM') {
    filterForm.dateRange = []
  }
}

// 生成报表
const handleGenerate = async () => {
  try {
    loading.value = true
    
    // 构建查询参数
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const params: unknown = {
      reportType: filterForm.reportType,
      department: filterForm.department
    }
    
    // 根据报表类型添加时间参数
    switch (filterForm.reportType) {
      case 'MONTHLY':
        params.month = filterForm.month
        break
      case 'QUARTERLY':
        params.quarter = filterForm.quarter
        params.year = filterForm.year
        break
      case 'YEARLY':
        params.year = filterForm.year
        break
      case 'CUSTOM':
        if (filterForm.dateRange && filterForm.dateRange.length === 2) {
          params.startDate = filterForm.dateRange[0]
          params.endDate = filterForm.dateRange[1]
        }
        break
    }
    
    // 调用API获取报表数据（暂时使用模拟数据）
    await fetchReportData(params)
    
    // 更新图表
    await nextTick()
    updateCharts()
    
    ElMessage.success('报表生成成功')
  } catch (__error) {
    console.error('生成报表失败:', error)
    ElMessage.error('生成报表失败')
  } finally {
    loading.value = false
  }
}

// 获取报表数据
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const fetchReportData = async (params: unknown) => {
  // 模拟数据
  overview.onboarding = 15
  overview.onboardingTrend = 25.5
  overview.resignation = 8
  overview.resignationTrend = -10.2
  overview.transfer = 12
  overview.transferTrend = 15.8
  overview.promotion = 6
  overview.promotionTrend = 20.0
  
  // 模拟明细数据
  const mockData = [
    {
      date: '2025-06-01',
      type: 'ONBOARDING',
      employeeName: '张三',
      employeeNo: 'EMP001',
      department: '计算机学院',
      position: '讲师',
      description: '新员工入职',
      effectiveDate: '2025-06-01',
      approver: '李主任'
    },
    {
      date: '2025-06-05',
      type: 'TRANSFER',
      employeeName: '李四',
      employeeNo: 'EMP002',
      department: '机械工程学院→电气工程学院',
      position: '副教授',
      description: '部门调动',
      effectiveDate: '2025-06-15',
      approver: '王院长'
    },
    {
      date: '2025-06-10',
      type: 'PROMOTION',
      employeeName: '王五',
      employeeNo: 'EMP003',
      department: '人工智能学院',
      position: '讲师→副教授',
      description: '职级晋升',
      effectiveDate: '2025-07-01',
      approver: '赵处长'
    },
    {
      date: '2025-06-15',
      type: 'RESIGNATION',
      employeeName: '赵六',
      employeeNo: 'EMP004',
      department: '外语学院',
      position: '讲师',
      description: '个人原因离职',
      effectiveDate: '2025-06-30',
      approver: '钱主任'
    }
  ]
  
  // 根据tab筛选
  if (currentTab.value !== 'ALL') {
    tableData.value = mockData.filter(item => item.type === currentTab.value)
  } else {
    tableData.value = mockData
  }
  
  pagination.total = tableData.value.length
}

// 更新图表
const updateCharts = () => {
  // 趋势图
  if (trendChart) {
    const option = {
      title: {
        text: ''
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['入职', '离职', '调动', '晋升']
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: ['1月', '2月', '3月', '4月', '5月', '6月']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: 'HrHr入职',
          type: 'line',
          data: [12, 15, 10, 18, 20, 15],
          smooth: true
        },
        {
          name: '离职',
          type: 'line',
          data: [5, 8, 6, 10, 7, 8],
          smooth: true
        },
        {
          name: '调动',
          type: 'line',
          data: [8, 10, 12, 15, 10, 12],
          smooth: true
        },
        {
          name: '晋升',
          type: 'line',
          data: [3, 5, 4, 6, 8, 6],
          smooth: true
        }
      ]
    }
    trendChart.setOption(option)
  }
  
  // 分布图
  if (distributionChart) {
    const option = {
      title: {
        text: ''
      },
      tooltip: {
        trigger: 'item'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: '部门异动分布',
          type: 'pie',
          radius: '50%',
          data: [
            { value: 25, name: '计算机学院' },
            { value: 20, name: '机械工程学院' },
            { value: 18, name: '电气工程学院' },
            { value: 15, name: '人工智能学院' },
            { value: 12, name: '外语学院' },
            { value: 10, name: '其他学院' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
    distributionChart.setOption(option)
  }
}

// 导出报表
const handleExport = () => {
  exportDialogVisible.value = true
}

// 确认导出
const handleExportConfirm = () => {
  ElMessage.success(`正在生成${exportForm.format}格式报表...`)
  exportDialogVisible.value = false
  
  // 实际导出逻辑
  setTimeout(() => {
    ElMessage.success('报表导出成功')
  }, 2000)
}

// 处理tab切换
const handleTabChange = () => {
  pagination.page = 1
  handleGenerate()
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
}

// 获取趋势样式
const getTrendClass = (trend: number) => {
  return trend > 0 ? 'trend-up' : trend < 0 ? 'trend-down' : ''
}

// 获取类型标签类型
const getTypeTagType = (type: string) => {
  switch (type) {
    case 'ONBOARDING': return 'success'
    case 'RESIGNATION': return 'danger'
    case 'TRANSFER': return 'primary'
    case 'PROMOTION': return 'warning'
    default: return ''
  }
}

// 获取类型文本
const getTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'ONBOARDING': '入职',
    'RESIGNATION': '离职',
    'TRANSFER': '调动',
    'PROMOTION': '晋升'
  }
  return typeMap[type] || type
}

// 表格汇总
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const getSummaries = (param: unknown) => {
  const {columns: _columns, data: _data} =  param
  const sums: string[] 
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.overview-row {
  margin-bottom: 20px;
}

.overview-card {
  cursor: pointer;
  transition: all 0.3s;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.overview-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.overview-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.overview-icon.onboarding {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.overview-icon.resignation {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.overview-icon.transfer {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.overview-icon.promotion {
  background: linear-gradient(135deg, #ffd93d 0%, #ffb347 100%);
}

.overview-info {
  flex: 1;
}

.overview-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.overview-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 4px;
}

.overview-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  color: #606266;
}

.overview-trend.trend-up {
  color: #67c23a;
}

.overview-trend.trend-down {
  color: #f56c6c;
}

.charts-row {
  margin-bottom: 20px;
}

.chart-card {
  height: 400px;
}

.chart-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.chart-container {
  width: 100%;
  height: 340px;
}

.table-card {
  margin-top: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>