<template>
  <div class="personnel-change-approval">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>人事异动审批</h2>
      <p>统一处理所有人事异动申请的审批流程</p>
    </div>

    <!-- 审批类型筛选 -->
    <el-card class="filter-card" shadow="never">
      <div class="filter-tabs">
        <el-radio-group v-model="currentType" @change="handleTypeChange">
          <el-radio-button value="ALL">
            全部申请
            <el-badge :value="stats.total" :max="99" class="badge-item"  />
          </el-radio-button>
          <el-radio-button value="ONBOARDING">
            入职申请
            <el-badge :value="stats.onboarding" :max="99" class="badge-item"  />
          </el-radio-button>
          <el-radio-button value="RESIGNATION">
            离职申请
            <el-badge :value="stats.resignation" :max="99" class="badge-item"  />
          </el-radio-button>
          <el-radio-button value="TRANSFER">
            调动申请
            <el-badge :value="stats.transfer" :max="99" class="badge-item"  />
          </el-radio-button>
          <el-radio-button value="PROMOTION">
            晋升申请
            <el-badge :value="stats.promotion" :max="99" class="badge-item"  />
          </el-radio-button>
          <el-radio-button value="POSITION">
            岗位调整
            <el-badge :value="stats.position" :max="99" class="badge-item"  />
          </el-radio-button>
        </el-radio-group>
      </div>

      <!-- 搜索条件 -->
      <div class="search-form">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索申请人、申请编号"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.status" placeholder="审批状态" clearable>
              <el-option label="待我审批" value="PENDING_MY_APPROVAL"  />
              <el-option label="我已审批" value="MY_APPROVED"  />
              <el-option label="审批中" value="IN_PROGRESS"  />
              <el-option label="已完成" value="COMPLETED"  />
              <el-option label="已驳回" value="REJECTED"  />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
             />
          </el-col>
          <el-col :span="10">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
            <el-button @click="handleBatchApprove" :disabled="selectedRows.length === 0">
              <el-icon><Stamp /></el-icon>
              批量审批
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 审批列表 -->
    <el-card class="table-card" shadow="never">
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="applicationNo" label="申请编号" width="140">
          <template #default="scope">
            <el-link type="primary" @click="handleView(scope.row)">
              {{ scope.row.applicationNo }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="申请类型" width="100">
          <template #default="scope">
            <el-tag :type="getTypeTagType(scope.row.type)" size="small">
              {{ getTypeText(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="applicant" label="申请人" width="100"  />
        <el-table-column prop="department" label="部门" width="150" show-overflow-tooltip  />
        <el-table-column prop="description" label="申请事项" min-width="200" show-overflow-tooltip  />
        <el-table-column prop="applicationDate" label="申请时间" width="160"  />
        <el-table-column prop="currentStage" label="当前环节" width="120">
          <template #default="scope">
            <el-tag size="small">{{ scope.row.currentStage }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="urgency" label="紧急程度" width="100">
          <template #default="scope">
            <el-tag :type="getUrgencyType(scope.row.urgency)" size="small">
              {{ getUrgencyText(scope.row.urgency) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" link @click="handleView(scope.row)">
              查看
            </el-button>
            <el-button 
              v-if="canApprove(scope.row)"
              size="small" 
              type="success" 
              link 
              @click="handleApprove(scope.row)"
            >
              审批
            </el-button>
            <el-button 
              v-if="canUrge(scope.row)"
              size="small" 
              type="warning" 
              link 
              @click="handleUrge(scope.row)"
            >
              催办
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 统一审批对话框 -->
    <UnifiedApprovalDialog
      v-model:visible="approvalDialogVisible"
      :application="currentApplication"
      @success="handleApprovalSuccess"
    />

    <!-- 批量审批对话框 -->
    <BatchApprovalDialog
      v-model:visible="batchDialogVisible"
      :applications="selectedRows"
      @success="handleBatchSuccess"
    />
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'PersonnelChangeApproval'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Stamp
} from '@element-plus/icons-vue'
import UnifiedApprovalDialog from './components/UnifiedApprovalDialog.vue'
import BatchApprovalDialog from './components/BatchApprovalDialog.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref<any[]>([])
const selectedRows = ref<any[]>([])
const currentType = ref('ALL')

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: '',
  dateRange: []
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 统计信息
const stats = reactive({
  total: 15,
  onboarding: 3,
  resignation: 2,
  transfer: 4,
  promotion: 3,
  position: 3
})

// 对话框相关
const approvalDialogVisible = ref(false)
const batchDialogVisible = ref(false)
const currentApplication = ref(null)

// 模拟数据
const mockData = [
  {
    id: '1',
    applicationNo: 'HR202506001',
    type: 'ONBOARDING',
    applicant: '张三',
    department: '计算机学院',
    description: '新员工入职申请 - 张三入职计算机学院',
    applicationDate: '2025-06-15 10:30:00',
    currentStage: '部门审批',
    urgency: 'HIGH',
    status: 'PENDING_MY_APPROVAL'
  },
  {
    id: '2',
    applicationNo: 'HR202506002',
    type: 'TRANSFER',
    applicant: '李四',
    department: '机械工程学院',
    description: '部门调动申请 - 从机械工程学院调至电气工程学院',
    applicationDate: '2025-06-14 14:20:00',
    currentStage: '人事审批',
    urgency: 'NORMAL',
    status: 'IN_PROGRESS'
  },
  {
    id: '3',
    applicationNo: 'HR202506003',
    type: 'PROMOTION',
    applicant: '王五',
    department: '人工智能学院',
    description: '职级晋升申请 - 从讲师晋升为副教授',
    applicationDate: '2025-06-13 09:15:00',
    currentStage: '学院评审',
    urgency: 'NORMAL',
    status: 'PENDING_MY_APPROVAL'
  },
  {
    id: '4',
    applicationNo: 'HR202506004',
    type: 'RESIGNATION',
    applicant: '赵六',
    department: '外语学院',
    description: '离职申请 - 个人原因申请离职',
    applicationDate: '2025-06-12 16:45:00',
    currentStage: '已完成',
    urgency: 'LOW',
    status: 'COMPLETED'
  }
]

// 获取审批列表
const fetchApprovalList = async () => {
  try {
    loading.value = true
    // 构建查询参数
   
    const params: unknown = {
      page: pagination.page,
      size: pagination.size,
      type: currentType.value === 'ALL' ? '' : currentType.value
    }
    
    // 添加搜索条件
    if (searchForm.keyword) {
      params.keyword = searchForm.keyword
    }
    if (searchForm.status) {
      params.status = searchForm.status
    }
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.startDate = searchForm.dateRange[0]
      params.endDate = searchForm.dateRange[1]
    }
    
    // 调用实际API（暂时使用模拟数据）
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 根据类型筛选
    let filteredData = [...mockData]
    if (currentType.value !== 'ALL') {
      filteredData = filteredData.filter(item => item.type === currentType.value)
    }
    
    tableData.value = filteredData
    pagination.total = filteredData.length
  } catch (__error) {
    console.error('获取审批列表失败:', error)
    ElMessage.error('获取审批列表失败')
  } finally {
    loading.value = false
  }
}

// 处理类型切换
const handleTypeChange = () => {
  pagination.page = 1
  fetchApprovalList()
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchApprovalList()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: '',
    dateRange: []
  })
  pagination.page = 1
  fetchApprovalList()
}

// 查看详情
   
const handleView = (application: unknown) => {
  currentApplication.value = application
  approvalDialogVisible.value = true
}

// 审批
   
const handleApprove = (application: unknown) => {
  currentApplication.value = application
  approvalDialogVisible.value = true
}

// 催办
   
const handleUrge = async (application: unknown) => {
  try {
    await ElMessageBox.confirm(
      `确定要催办该审批吗？将向当前审批人发送催办通知。`,
      '催办确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 调用催办API
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('催办通知已发送')
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('催办失败:', error)
      ElMessage.error('催办失败')
    }
  }
}

// 批量审批
const handleBatchApprove = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要审批的申请')
    return
  }
  
  // 检查是否都是待审批状态
  const canBatchApprove = selectedRows.value.every(row => 
    row.status === 'PENDING_MY_APPROVAL'
  )
  
  if (!canBatchApprove) {
    ElMessage.warning('只能批量审批待我审批的申请')
    return
  }
  
  batchDialogVisible.value = true
}

// 表格选择变化
   
const handleSelectionChange = (selection: unknown[]) => {
  selectedRows.value = selection
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchApprovalList()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchApprovalList()
}

// 审批成功回调
const handleApprovalSuccess = () => {
  fetchApprovalList()
}

// 批量审批成功回调
const handleBatchSuccess = () => {
  selectedRows.value = []
  fetchApprovalList()
}

// 获取类型标签类型
const getTypeTagType = (type: string) => {
  switch (type) {
    case 'ONBOARDING': return 'success'
    case 'RESIGNATION': return 'danger'
    case 'TRANSFER': return 'primary'
    case 'PROMOTION': return 'warning'
    case 'POSITION': return 'info'
    default: return ''
  }
}

// 获取类型文本
const getTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'ONBOARDING': '入职',
    'RESIGNATION': '离职',
    'TRANSFER': '调动',
    'PROMOTION': '晋升',
    'POSITION': '岗位'
  }
  return typeMap[type] || type
}

// 获取紧急程度类型
const getUrgencyType = (urgency: string) => {
  switch (urgency) {
    case 'HIGH': return 'danger'
    case 'NORMAL': return 'warning'
    case 'LOW': return 'info'
    default: return ''
  }
}

// 获取紧急程度文本
const getUrgencyText = (urgency: string) => {
  const urgencyMap: Record<string, string> = {
    'HIGH': '紧急',
    'NORMAL': '普通',
    'LOW': '不急'
  }
  return urgencyMap[urgency] || urgency
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  switch (status) {
    case 'PENDING_MY_APPROVAL': return 'danger'
    case 'MY_APPROVED': return 'success'
    case 'IN_PROGRESS': return 'warning'
    case 'COMPLETED': return 'success'
    case 'REJECTED': return 'info'
    default: return ''
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'PENDING_MY_APPROVAL': '待我审批',
    'MY_APPROVED': '我已审批',
    'IN_PROGRESS': '审批中',
    'COMPLETED': '已完成',
    'REJECTED': '已驳回'
  }
  return statusMap[status] || status
}

// 判断是否可以审批
   
const canApprove = (application: unknown) => {
  return application.status === 'PENDING_MY_APPROVAL'
}

// 判断是否可以催办
   
const canUrge = (application: unknown) => {
  return application.status === 'IN_PROGRESS' && application.urgency !== 'LOW'
}

// 初始化
onMounted(() => {
  fetchApprovalList()
})
</script>

<style scoped>
.personnel-change-approval {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-tabs {
  margin-bottom: 20px;
}

.badge-item {
  margin-left: 4px;
}

:deep(.el-radio-button__inner) {
  padding: 12px 20px;
}

.search-form {
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.table-card {
  margin-top: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>