<template>
  <div class="position-adjustment">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>校内岗位调整</h2>
      <p>管理教职工校内部门或岗位调整申请和审批流程</p>
    </div>

    <!-- 搜索和操作区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-form">
        <el-row :gutter="20">
          <el-col :span="5">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索姓名、工号"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.status" placeholder="申请状态" clearable>
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.fromDepartment" placeholder="调出部门" clearable>
              <el-option label="全部部门" value=""  />
              <el-option
                v-for="dept in departmentOptions"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
               />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.toDepartment" placeholder="调入部门" clearable>
              <el-option label="全部部门" value=""  />
              <el-option
                v-for="dept in departmentOptions"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
               />
            </el-select>
          </el-col>
          <el-col :span="7">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
            <el-button type="success" @click="handleAdd">
              <el-icon><Plus /></el-icon>
              发起调整
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total">
              <el-icon><Switch /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.total }}</div>
              <div class="stats-label">调整总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon pending">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.pending }}</div>
              <div class="stats-label">待审核</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon completed">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.completed }}</div>
              <div class="stats-label">已完成</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon thisMonth">
              <el-icon><Star /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.thisMonth }}</div>
              <div class="stats-label">本月调整</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 调整申请列表 -->
    <el-card class="table-card" shadow="never">
      <div class="table-header">
        <span class="table-title">岗位调整申请列表</span>
        <div class="table-actions">
          <el-button size="small" @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
          <el-button size="small" @click="handleBatchApprove" :disabled="selectedRows.length === 0">
            <el-icon><Check /></el-icon>
            批量审核
          </el-button>
        </div>
      </div>

      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="adjustmentId" label="调整编号" width="120"  />
        <el-table-column prop="employeeName" label="姓名" width="100"  />
        <el-table-column prop="employeeNumber" label="工号" width="100"  />
        <el-table-column prop="fromDepartment" label="调出部门" width="150" show-overflow-tooltip  />
        <el-table-column prop="fromPosition" label="原岗位" width="120" show-overflow-tooltip  />
        <el-table-column prop="toDepartment" label="调入部门" width="150" show-overflow-tooltip  />
        <el-table-column prop="toPosition" label="新岗位" width="120" show-overflow-tooltip  />
        <el-table-column prop="applicationDate" label="申请日期" width="100"  />
        <el-table-column prop="currentApprover" label="当前审批人" width="100"  />
        <el-table-column prop="status" label="状态" width="120">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" link @click="handleView(scope.row)">
              查看
            </el-button>
            <el-button 
              v-if="canEdit(scope.row.status)"
              size="small" 
              type="success" 
              link 
              @click="handleEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button 
              v-if="canApprove(scope.row.status)"
              size="small" 
              type="warning" 
              link 
              @click="handleApprove(scope.row)"
            >
              审核
            </el-button>
            <el-button 
              v-if="scope.row.status === 'APPROVED'"
              size="small" 
              type="success" 
              link 
              @click="handleConfirmAdjustment(scope.row)"
            >
              确认调整
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 调整申请详情/编辑对话框 -->
    <AdjustmentDialog
      v-model:visible="dialogVisible"
      :adjustment="currentAdjustment"
      :mode="dialogMode"
      @success="handleDialogSuccess"
    />

    <!-- 审核对话框 -->
    <ApprovalDialog
      v-model:visible="approvalDialogVisible"
      :adjustment="currentAdjustment"
      @success="handleApprovalSuccess"
    />

    <!-- 影响分析对话框 -->
    <ImpactAnalysisDialog
      v-model:visible="impactAnalysisVisible"
      :adjustment="currentAdjustment"
    />
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  Switch,
  Clock,
  Check,
  Star,
  Download
} from '@element-plus/icons-vue'
import { organizationApi } from '@/api/organization'
import { positionAdjustmentApi, personnelChangeOptions, type PositionAdjustmentApplication, type PersonnelChangeStats } from '@/api/personnelChange'

// 组件引入（需要创建）
// import AdjustmentDialog from './components/AdjustmentDialog.vue'
// import HrApprovalDialog from './components/HrApprovalDialog.vue'
// import ImpactAnalysisDialog from './components/ImpactAnalysisDialog.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref<any[]>([])
const selectedRows = ref<any[]>([])
const departmentOptions = ref<any[]>([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: '',
  fromDepartment: '',
  toDepartment: ''
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 统计信息
const stats = reactive({
  total: 0,
  pending: 0,
  completed: 0,
  thisMonth: 0
})

// 对话框相关
const dialogVisible = ref(false)
const approvalDialogVisible = ref(false)
const impactAnalysisVisible = ref(false)
const dialogMode = ref<'view' | 'add' | 'edit'>('view')
const currentAdjustment = ref(null)

// 状态选项
const statusOptions = personnelChangeOptions.positionAdjustmentStatus

// 模拟数据
const mockData = [
  {
    id: '1',
    adjustmentId: 'ADJ202506001',
    employeeName: '张三',
    employeeNumber: 'EMP001',
    fromDepartment: '计算机学院',
    fromPosition: '讲师',
    toDepartment: '信息工程学院',
    toPosition: '副教授',
    applicationDate: '2025-06-15',
    currentApprover: '李四',
    status: 'PENDING_OUTGOING_REVIEW'
  },
  {
    id: '2',
    adjustmentId: 'ADJ202506002',
    employeeName: '王五',
    employeeNumber: 'EMP002',
    fromDepartment: '机械工程学院',
    fromPosition: '副教授',
    toDepartment: '管理学院',
    toPosition: '教授',
    applicationDate: '2025-06-14',
    currentApprover: '赵六',
    status: 'PENDING_HR_REVIEW'
  }
]

// 获取调整申请列表
const fetchAdjustments = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.page,
      size: pagination.size,
      keyword: searchForm.keyword,
      status: searchForm.status,
      department: searchForm.fromDepartment || searchForm.toDepartment || undefined
    }
    
    const result = await positionAdjustmentApi.queryApplications(params)
    tableData.value = result.content
    pagination.total = result.totalElements
  } catch (__error) {
    console.error('获取调整申请列表失败:', error)
    ElMessage.error('获取调整申请列表失败')
  } finally {
    loading.value = false
  }
}

// 获取统计信息
const fetchStats = async () => {
  try {
    const result = await positionAdjustmentApi.getStats()
    stats.total = result.total
    stats.pending = result.pending
    stats.completed = result.approved
    stats.thisMonth = result.thisMonth
  } catch (__error) {
    console.error('获取统计信息失败:', error)
    ElMessage.error('获取统计信息失败')
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchAdjustments()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: '',
    fromDepartment: '',
    toDepartment: ''
  })
  pagination.page = 1
  fetchAdjustments()
}

// 发起调整
const handleAdd = () => {
  currentAdjustment.value = null
  dialogMode.value = 'add'
  dialogVisible.value = true
}

// 查看调整
   
const handleView = (adjustment: unknown) => {
  currentAdjustment.value = adjustment
  dialogMode.value = 'view'
  dialogVisible.value = true
}

// 编辑调整
   
const handleEdit = (adjustment: unknown) => {
  currentAdjustment.value = adjustment
  dialogMode.value = 'edit'
  dialogVisible.value = true
}

// 审核调整
   
const handleApprove = (adjustment: unknown) => {
  currentAdjustment.value = adjustment
  approvalDialogVisible.value = true
}

// 确认调整
   
const handleConfirmAdjustment = async (adjustment: unknown) => {
  try {
    await ElMessageBox.confirm(
      `确定要确认 "${adjustment.employeeName}" 的岗位调整吗？`,
      '确认调整',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await positionAdjustmentApi.confirmAdjustment(adjustment.id)
    ElMessage.success('确认调整成功')
    fetchAdjustments()
    fetchStats()
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('确认调整失败:', error)
      ElMessage.error('确认调整失败')
    }
  }
}

// 表格选择变化
   
const handleSelectionChange = (selection: unknown[]) => {
  selectedRows.value = selection
}

// 批量审核
const handleBatchApprove = () => {
  ElMessage.info('批量审核功能开发中...')
}

// 导出
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchAdjustments()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchAdjustments()
}

// 对话框成功回调
const handleDialogSuccess = () => {
  fetchAdjustments()
  fetchStats()
}

// 审核成功回调
const handleApprovalSuccess = () => {
  fetchAdjustments()
  fetchStats()
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  switch (status) {
    case 'PENDING_OUTGOING_REVIEW':
    case 'PENDING_INCOMING_REVIEW':
    case 'PENDING_HR_REVIEW':
      return 'warning'
    case 'APPROVED':
      return 'success'
    case 'REJECTED':
      return 'danger'
    case 'COMPLETED':
      return 'success'
    default:
      return ''
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  const option = statusOptions.find(item => item.value === status)
  return option ? option.label : status
}

// 判断是否可以编辑
const canEdit = (status: string) => {
  return ['PENDING_OUTGOING_REVIEW'].includes(status)
}

// 判断是否可以审核
const canApprove = (status: string) => {
  return ['PENDING_OUTGOING_REVIEW', 'PENDING_INCOMING_REVIEW', 'PENDING_HR_REVIEW'].includes(status)
}

// 获取部门列表
const fetchDepartments = async () => {
  try {
    const response = await organizationApi.getTree()
    if (response && Array.isArray(response)) {
      departmentOptions.value = response.map(dept => ({
        id: dept.id,
        name: dept.name,
        orgCode: dept.orgCode
      }))
    }
  } catch (__error) {
    console.error('获取部门列表失败:', error)
    ElMessage.error('获取部门列表失败')
  }
}

// 初始化
onMounted(() => {
  fetchAdjustments()
  fetchStats()
  fetchDepartments()
})
</script>

<style scoped>
.position-adjustment {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  padding: 10px 0;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.pending {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.completed {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.thisMonth {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.table-card {
  margin-top: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
