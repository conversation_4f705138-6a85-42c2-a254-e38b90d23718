<template>
  <div class="department-transfer">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>部门调动管理</h2>
      <p>管理教职工跨部门调动申请、审批流程和调动记录</p>
    </div>

    <!-- 搜索和操作区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-form">
        <el-row :gutter="20">
          <el-col :span="5">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索姓名、工号"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.fromDept" placeholder="原部门" clearable>
              <el-option label="全部部门" value=""  />
              <el-option
                v-for="dept in departmentOptions"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
               />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.toDept" placeholder="目标部门" clearable>
              <el-option label="全部部门" value=""  />
              <el-option
                v-for="dept in departmentOptions"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
               />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.status" placeholder="审批状态" clearable>
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-select>
          </el-col>
          <el-col :span="7">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
            <el-button type="success" @click="handleAdd">
              <el-icon><Plus /></el-icon>
              发起调动
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total">
              <el-icon><Promotion /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.total }}</div>
              <div class="stats-label">调动总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon pending">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.pending }}</div>
              <div class="stats-label">待审批</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon approved">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.approved }}</div>
              <div class="stats-label">已通过</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon thisMonth">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.thisMonth }}</div>
              <div class="stats-label">本月调动</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 调动记录列表 -->
    <el-card class="table-card" shadow="never">
      <div class="table-header">
        <span class="table-title">部门调动记录</span>
        <div class="table-actions">
          <el-button size="small" @click="handleExport">
            <el-icon><Download /></el-icon>
            导出记录
          </el-button>
          <el-button size="small" @click="handleBatchApprove" :disabled="selectedRows.length === 0">
            <el-icon><Check /></el-icon>
            批量审批
          </el-button>
        </div>
      </div>

      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="applicationNo" label="申请编号" width="120"  />
        <el-table-column prop="employeeName" label="姓名" width="100"  />
        <el-table-column prop="employeeNo" label="工号" width="100"  />
        <el-table-column prop="fromDepartment" label="原部门" width="150" show-overflow-tooltip  />
        <el-table-column prop="toDepartment" label="目标部门" width="150" show-overflow-tooltip  />
        <el-table-column prop="transferReason" label="调动原因" width="150" show-overflow-tooltip  />
        <el-table-column prop="applicationDate" label="申请日期" width="100"  />
        <el-table-column prop="effectiveDate" label="生效日期" width="100"  />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" link @click="handleView(scope.row)">
              查看
            </el-button>
            <el-button 
              v-if="canApprove(scope.row)"
              size="small" 
              type="success" 
              link 
              @click="handleApprove(scope.row)"
            >
              审批
            </el-button>
            <el-button 
              v-if="canCancel(scope.row)"
              size="small" 
              type="danger" 
              link 
              @click="handleCancel(scope.row)"
            >
              撤销
            </el-button>
            <el-button size="small" link @click="handlePrint(scope.row)">
              打印
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 调动申请对话框 -->
    <TransferApplicationDialog
      v-model:visible="applicationDialogVisible"
      :transfer="currentTransfer"
      :mode="dialogMode"
      :departments="departmentOptions"
      @success="handleApplicationSuccess"
    />

    <!-- 审批对话框 -->
    <TransferApprovalDialog
      v-model:visible="approvalDialogVisible"
      :transfer="currentTransfer"
      @success="handleApprovalSuccess"
    />
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  Promotion,
  Clock,
  Check,
  Calendar,
  Download
} from '@element-plus/icons-vue'
import { organizationApi } from '@/api/organization'
import TransferApplicationDialog from './components/TransferApplicationDialog.vue'
import TransferApprovalDialog from './components/TransferApprovalDialog.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref<any[]>([])
const selectedRows = ref<any[]>([])
const departmentOptions = ref<any[]>([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  fromDept: '',
  toDept: '',
  status: ''
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 统计信息
const stats = reactive({
  total: 0,
  pending: 0,
  approved: 0,
  thisMonth: 0
})

// 对话框相关
const applicationDialogVisible = ref(false)
const approvalDialogVisible = ref(false)
const dialogMode = ref<'view' | 'add' | 'edit'>('view')
const currentTransfer = ref(null)

// 状态选项
const statusOptions = [
  { label: '待部门主管审批', value: 'PENDING_DEPT_MANAGER' },
  { label: '待原部门审批', value: 'PENDING_FROM_DEPT' },
  { label: '待目标部门审批', value: 'PENDING_TO_DEPT' },
  { label: '待人事处审批', value: 'PENDING_HR' },
  { label: '已通过', value: 'APPROVED' },
  { label: '已驳回', value: 'REJECTED' },
  { label: '已撤销', value: 'CANCELLED' }
]

// 模拟数据
const mockData = [
  {
    id: '1',
    applicationNo: 'TRANS202506001',
    employeeName: '张三',
    employeeNo: 'EMP001',
    fromDepartment: '计算机学院',
    toDepartment: '人工智能学院',
    transferReason: '专业发展需要',
    applicationDate: '2025-06-15',
    effectiveDate: '2025-07-01',
    status: 'PENDING_FROM_DEPT',
    currentApprover: '计算机学院院长'
  },
  {
    id: '2',
    applicationNo: 'TRANS202506002',
    employeeName: '李四',
    employeeNo: 'EMP002',
    fromDepartment: '机械工程学院',
    toDepartment: '电气工程学院',
    transferReason: '工作调整',
    applicationDate: '2025-06-10',
    effectiveDate: '2025-07-15',
    status: 'APPROVED',
    currentApprover: ''
  }
]

// 获取调动记录列表
const fetchTransferList = async () => {
  try {
    loading.value = true
    // 构建查询参数
   
    const params: unknown = {
      page: pagination.page,
      size: pagination.size
    }
    
    // 添加搜索条件
    if (searchForm.keyword) {
      params.keyword = searchForm.keyword
    }
    if (searchForm.fromDept) {
      params.fromDept = searchForm.fromDept
    }
    if (searchForm.toDept) {
      params.toDept = searchForm.toDept
    }
    if (searchForm.status) {
      params.status = searchForm.status
    }
    
    // 调用实际API（暂时使用模拟数据）
    await new Promise(resolve => setTimeout(resolve, 500))
    tableData.value = mockData
    pagination.total = mockData.length
    
    // 更新统计信息
    stats.total = 25
    stats.pending = 8
    stats.approved = 15
    stats.thisMonth = 5
  } catch (__error) {
    console.error('获取调动记录失败:', error)
    ElMessage.error('获取调动记录失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchTransferList()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    fromDept: '',
    toDept: '',
    status: ''
  })
  pagination.page = 1
  fetchTransferList()
}

// 发起调动
const handleAdd = () => {
  currentTransfer.value = null
  dialogMode.value = 'add'
  applicationDialogVisible.value = true
}

// 查看详情
   
const handleView = (transfer: unknown) => {
  currentTransfer.value = transfer
  dialogMode.value = 'view'
  applicationDialogVisible.value = true
}

// 审批
   
const handleApprove = (transfer: unknown) => {
  currentTransfer.value = transfer
  approvalDialogVisible.value = true
}

// 撤销申请
   
const handleCancel = async (transfer: unknown) => {
  try {
    await ElMessageBox.confirm(
      `确定要撤销该调动申请吗？撤销后将无法恢复。`,
      '撤销确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 调用撤销API
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('撤销成功')
    fetchTransferList()
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('撤销失败:', error)
      ElMessage.error('撤销失败')
    }
  }
}

// 打印
   
const handlePrint = (transfer: unknown) => {
  ElMessage.info('打印功能开发中...')
}

// 导出记录
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 批量审批
const handleBatchApprove = () => {
  ElMessage.info('批量审批功能开发中...')
}

// 表格选择变化
   
const handleSelectionChange = (selection: unknown[]) => {
  selectedRows.value = selection
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchTransferList()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchTransferList()
}

// 申请成功回调
const handleApplicationSuccess = () => {
  fetchTransferList()
}

// 审批成功回调
const handleApprovalSuccess = () => {
  fetchTransferList()
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  switch (status) {
    case 'APPROVED':
      return 'success'
    case 'REJECTED':
      return 'danger'
    case 'CANCELLED':
      return 'info'
    default:
      return 'warning'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  const option = statusOptions.find(item => item.value === status)
  return option ? option.label : status
}

// 判断是否可以审批
   
const canApprove = (transfer: unknown) => {
  return ['PENDING_DEPT_MANAGER', 'PENDING_FROM_DEPT', 'PENDING_TO_DEPT', 'PENDING_HR'].includes(transfer.status)
}

// 判断是否可以撤销
   
const canCancel = (transfer: unknown) => {
  return ['PENDING_DEPT_MANAGER', 'PENDING_FROM_DEPT', 'PENDING_TO_DEPT', 'PENDING_HR'].includes(transfer.status)
}

// 获取部门列表
const fetchDepartments = async () => {
  try {
    const response = await organizationApi.getTree()
    if (response && Array.isArray(response)) {
      departmentOptions.value = response.map(dept => ({
        id: dept.id,
        name: dept.name,
        orgCode: dept.orgCode
      }))
    }
  } catch (__error) {
    console.error('获取部门列表失败:', error)
    ElMessage.error('获取部门列表失败')
  }
}

// 初始化
onMounted(() => {
  fetchTransferList()
  fetchDepartments()
})
</script>

<style scoped>
.department-transfer {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  padding: 10px 0;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.pending {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.approved {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.thisMonth {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.table-card {
  margin-top: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>