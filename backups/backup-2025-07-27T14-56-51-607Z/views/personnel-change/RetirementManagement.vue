<template>
  <div class="retirement-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>退休管理</h2>
      <p>管理退休规则配置、退休提醒和退休登记等业务</p>
    </div>

    <!-- 功能标签页 -->
    <el-tabs v-model="activeTab" class="function-tabs">
      <!-- 退休提醒 -->
      <el-tab-pane label="退休提醒" name="reminders">
        <el-card shadow="never">
          <div class="tab-header">
            <span class="tab-title">即将退休人员提醒</span>
            <el-button type="primary" @click="handleRefreshReminders">
              <el-icon><Refresh /></el-icon>
              刷新提醒
            </el-button>
          </div>

          <!-- 提醒统计 -->
          <el-row :gutter="20" class="reminder-stats">
            <el-col :span="6">
              <el-card class="stats-card">
                <div class="stats-content">
                  <div class="stats-icon urgent">
                    <el-icon><Warning /></el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ reminderStats.urgent }}</div>
                    <div class="stats-label">1个月内退休</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stats-card">
                <div class="stats-content">
                  <div class="stats-icon warning">
                    <el-icon><Clock /></el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ reminderStats.warning }}</div>
                    <div class="stats-label">3个月内退休</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stats-card">
                <div class="stats-content">
                  <div class="stats-icon info">
                    <el-icon><InfoFilled /></el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ reminderStats.info }}</div>
                    <div class="stats-label">6个月内退休</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stats-card">
                <div class="stats-content">
                  <div class="stats-icon total">
                    <el-icon><User /></el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ reminderStats.total }}</div>
                    <div class="stats-label">提醒总数</div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 提醒列表 -->
          <el-table :data="reminderList" style="width: 100%">
            <el-table-column prop="employeeName" label="姓名" width="100"  />
            <el-table-column prop="employeeNumber" label="工号" width="100"  />
            <el-table-column prop="department" label="部门" width="150" show-overflow-tooltip  />
            <el-table-column prop="position" label="岗位" width="120"  />
            <el-table-column prop="birthDate" label="出生日期" width="100"  />
            <el-table-column prop="retirementDate" label="预计退休日期" width="120"  />
            <el-table-column prop="daysToRetirement" label="距离退休" width="100">
              <template #default="scope">
                <el-tag :type="getDaysToRetirementTag(scope.row.daysToRetirement)" size="small">
                  {{ scope.row.daysToRetirement }}天
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="reminderLevel" label="提醒级别" width="100">
              <template #default="scope">
                <el-tag :type="getReminderLevelTag(scope.row.reminderLevel)" size="small">
                  {{ scope.row.reminderLevelName }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button size="small" type="primary" link @click="handleSendReminder(scope.row)">
                  发送提醒
                </el-button>
                <el-button size="small" type="success" link @click="handleRetirementRegistration(scope.row)">
                  办理退休
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <!-- 退休登记 -->
      <el-tab-pane label="退休登记" name="registration">
        <el-card shadow="never">
          <div class="tab-header">
            <span class="tab-title">退休登记记录</span>
            <div class="tab-actions">
              <el-button type="success" @click="handleAddRegistration">
                <el-icon><Plus /></el-icon>
                新增登记
              </el-button>
              <el-button @click="handleExportRegistrations">
                <el-icon><Download /></el-icon>
                导出记录
              </el-button>
            </div>
          </div>

          <!-- 搜索区域 -->
          <div class="search-form">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-input
                  v-model="registrationSearch.keyword"
                  placeholder="搜索姓名、工号"
                  clearable
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </el-col>
              <el-col :span="4">
                <el-select v-model="registrationSearch.retirementType" placeholder="退休类型" clearable>
                  <el-option label="正常退休" value="NORMAL"  />
                  <el-option label="提前退休" value="EARLY"  />
                  <el-option label="丧失劳动能力退休" value="DISABILITY"  />
                </el-select>
              </el-col>
              <el-col :span="6">
                <el-date-picker
                  v-model="registrationSearch.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                 />
              </el-col>
              <el-col :span="8">
                <el-button type="primary" @click="handleSearchRegistrations">
                  <el-icon><Search /></el-icon>
                  搜索
                </el-button>
                <el-button @click="handleResetRegistrationSearch">
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
              </el-col>
            </el-row>
          </div>

          <!-- 登记列表 -->
          <el-table :data="registrationList" style="width: 100%">
            <el-table-column prop="recordId" label="登记编号" width="120"  />
            <el-table-column prop="employeeName" label="姓名" width="100"  />
            <el-table-column prop="employeeNumber" label="工号" width="100"  />
            <el-table-column prop="department" label="部门" width="150" show-overflow-tooltip  />
            <el-table-column prop="retirementType" label="退休类型" width="120">
              <template #default="scope">
                <el-tag :type="getRetirementTypeTag(scope.row.retirementType)" size="small">
                  {{ getRetirementTypeText(scope.row.retirementType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="retirementDate" label="退休日期" width="100"  />
            <el-table-column prop="preRetirementPosition" label="退休前岗位" width="120"  />
            <el-table-column prop="operator" label="办理人" width="100"  />
            <el-table-column prop="operationTime" label="办理时间" width="120"  />
            <el-table-column label="操作" width="150">
              <template #default="scope">
                <el-button size="small" type="primary" link @click="handleViewRegistration(scope.row)">
                  查看详情
                </el-button>
                <el-button size="small" type="danger" link @click="handleDeleteRegistration(scope.row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <!-- 提前退休申请 -->
      <el-tab-pane label="提前退休申请" name="early-retirement">
        <el-card shadow="never">
          <div class="tab-header">
            <span class="tab-title">提前退休申请</span>
            <el-button type="success" @click="handleAddEarlyRetirement">
              <el-icon><Plus /></el-icon>
              新增申请
            </el-button>
          </div>

          <!-- 申请列表 -->
          <el-table :data="earlyRetirementList" style="width: 100%">
            <el-table-column prop="applicationId" label="申请编号" width="120"  />
            <el-table-column prop="employeeName" label="姓名" width="100"  />
            <el-table-column prop="employeeNumber" label="工号" width="100"  />
            <el-table-column prop="department" label="部门" width="150" show-overflow-tooltip  />
            <el-table-column prop="proposedRetirementDate" label="申请退休日期" width="120"  />
            <el-table-column prop="applicationReason" label="申请理由" width="200" show-overflow-tooltip  />
            <el-table-column prop="status" label="状态" width="120">
              <template #default="scope">
                <el-tag :type="getApplicationStatusTag(scope.row.status)" size="small">
                  {{ getApplicationStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button size="small" type="primary" link @click="handleViewEarlyRetirement(scope.row)">
                  查看
                </el-button>
                <el-button 
                  v-if="canApproveEarlyRetirement(scope.row.status)"
                  size="small" 
                  type="warning" 
                  link 
                  @click="handleApproveEarlyRetirement(scope.row)"
                >
                  审核
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <!-- 退休规则配置 -->
      <el-tab-pane label="规则配置" name="rules">
        <el-card shadow="never">
          <div class="tab-header">
            <span class="tab-title">退休规则配置</span>
            <el-button type="success" @click="handleAddRule">
              <el-icon><Plus /></el-icon>
              新增规则
            </el-button>
          </div>

          <!-- 规则列表 -->
          <el-table :data="rulesList" style="width: 100%">
            <el-table-column prop="ruleName" label="规则名称" width="200"  />
            <el-table-column prop="ruleType" label="规则类型" width="120">
              <template #default="scope">
                <el-tag :type="getRuleTypeTag(scope.row.ruleType)" size="small">
                  {{ getRuleTypeText(scope.row.ruleType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="minAge" label="最小年龄" width="100"  />
            <el-table-column prop="minWorkYears" label="最小工龄" width="100"  />
            <el-table-column prop="reminderDaysBefore" label="提前提醒天数" width="120"  />
            <el-table-column prop="isEnabled" label="状态" width="100">
              <template #default="scope">
                <el-switch
                  v-model="scope.row.isEnabled"
                  @change="handleToggleRule(scope.row)"
                 />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template #default="scope">
                <el-button size="small" type="primary" link @click="handleEditRule(scope.row)">
                  编辑
                </el-button>
                <el-button size="small" type="danger" link @click="handleDeleteRule(scope.row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <!-- 退休登记对话框 -->
    <RetirementRegistrationDialog
      v-model:visible="registrationDialogVisible"
      :employee="currentEmployee"
      :mode="registrationDialogMode"
      @success="handleRegistrationSuccess"
    />

    <!-- 提前退休申请对话框 -->
    <EarlyRetirementDialog
      v-model:visible="earlyRetirementDialogVisible"
      :application="currentEarlyRetirement"
      :mode="earlyRetirementDialogMode"
      @success="handleEarlyRetirementSuccess"
    />

    <!-- 规则配置对话框 -->
    <RuleConfigDialog
      v-model:visible="ruleDialogVisible"
      :rule="currentRule"
      :mode="ruleDialogMode"
      @success="handleRuleSuccess"
    />
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'RetirementManagement'
})
 
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  Warning,
  Clock,
  InfoFilled,
  User,
  Plus,
  Download,
  Search
} from '@element-plus/icons-vue'

// 组件引入（需要创建）
// import RetirementRegistrationDialog from './components/RetirementRegistrationDialog.vue'
// import EarlyRetirementDialog from './components/EarlyRetirementDialog.vue'
// import RuleConfigDialog from './components/RuleConfigDialog.vue'

// 响应式数据
const activeTab = ref('reminders')

// 提醒相关数据
const reminderStats = reactive({
  urgent: 3,
  warning: 8,
  info: 15,
  total: 26
})

const reminderList = ref([
  {
    id: '1',
    employeeName: '张三',
    employeeNumber: 'EMP001',
    department: '计算机学院',
    position: '教授',
    birthDate: '1965-03-15',
    retirementDate: '2025-07-15',
    daysToRetirement: 26,
    reminderLevel: 'URGENT',
    reminderLevelName: '紧急'
  },
  {
    id: '2',
    employeeName: '李四',
    employeeNumber: 'EMP002',
    department: '机械工程学院',
    position: '副教授',
    birthDate: '1965-09-20',
    retirementDate: '2025-09-20',
    daysToRetirement: 93,
    reminderLevel: 'WARNING',
    reminderLevelName: '警告'
  }
])

// 登记相关数据
const registrationSearch = reactive({
  keyword: '',
  retirementType: '',
  dateRange: []
})

const registrationList = ref([
  {
    id: '1',
    recordId: 'RET202506001',
    employeeName: '王五',
    employeeNumber: 'EMP003',
    department: '管理学院',
    retirementType: 'NORMAL',
    retirementDate: '2025-06-01',
    preRetirementPosition: '教授',
    operator: '人事处',
    operationTime: '2025-06-01 10:30:00'
  }
])

// 提前退休申请数据
const earlyRetirementList = ref([
  {
    id: '1',
    applicationId: 'EARLY202506001',
    employeeName: '赵六',
    employeeNumber: 'EMP004',
    department: '外语学院',
    proposedRetirementDate: '2025-12-31',
    applicationReason: '身体健康原因',
    status: 'PENDING_DEPARTMENT_REVIEW'
  }
])

// 规则配置数据
const rulesList = ref([
  {
    id: '1',
    ruleName: '法定退休年龄规则',
    ruleType: 'LEGAL',
    minAge: 60,
    minWorkYears: 0,
    reminderDaysBefore: 90,
    isEnabled: true
  },
  {
    id: '2',
    ruleName: '弹性提前退休规则',
    ruleType: 'EARLY',
    minAge: 55,
    minWorkYears: 10,
    reminderDaysBefore: 180,
    isEnabled: true
  }
])

// 对话框相关
const registrationDialogVisible = ref(false)
const earlyRetirementDialogVisible = ref(false)
const ruleDialogVisible = ref(false)
const registrationDialogMode = ref<'view' | 'add' | 'edit'>('add')
const earlyRetirementDialogMode = ref<'view' | 'add' | 'edit'>('add')
const ruleDialogMode = ref<'view' | 'add' | 'edit'>('add')
const currentEmployee = ref(null)
const currentEarlyRetirement = ref(null)
const currentRule = ref(null)

// 刷新提醒
const handleRefreshReminders = () => {
  ElMessage.success('提醒数据已刷新')
}

// 发送提醒
   
const handleSendReminder = (employee: unknown) => {
  ElMessage.success(`已向 ${employee.employeeName} 发送退休提醒`)
}

// 办理退休
   
const handleRetirementRegistration = (employee: unknown) => {
  currentEmployee.value = employee
  registrationDialogMode.value = 'add'
  registrationDialogVisible.value = true
}

// 新增退休登记
const handleAddRegistration = () => {
  currentEmployee.value = null
  registrationDialogMode.value = 'add'
  registrationDialogVisible.value = true
}

// 查看退休登记
   
const handleViewRegistration = (registration: unknown) => {
  currentEmployee.value = registration
  registrationDialogMode.value = 'view'
  registrationDialogVisible.value = true
}

// 删除退休登记
   
const handleDeleteRegistration = async (registration: unknown) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除 "${registration.employeeName}" 的退休登记记录吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('删除成功')
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 导出登记记录
const handleExportRegistrations = () => {
  ElMessage.info('导出功能开发中...')
}

// 搜索登记记录
const handleSearchRegistrations = () => {
  ElMessage.info('搜索功能开发中...')
}

// 重置搜索
const handleResetRegistrationSearch = () => {
  Object.assign(registrationSearch, {
    keyword: '',
    retirementType: '',
    dateRange: []
  })
}

// 新增提前退休申请
const handleAddEarlyRetirement = () => {
  currentEarlyRetirement.value = null
  earlyRetirementDialogMode.value = 'add'
  earlyRetirementDialogVisible.value = true
}

// 查看提前退休申请
   
const handleViewEarlyRetirement = (application: unknown) => {
  currentEarlyRetirement.value = application
  earlyRetirementDialogMode.value = 'view'
  earlyRetirementDialogVisible.value = true
}

// 审核提前退休申请
   
const handleApproveEarlyRetirement = (application: unknown) => {
  ElMessage.info('审核功能开发中...')
}

// 新增规则
const handleAddRule = () => {
  currentRule.value = null
  ruleDialogMode.value = 'add'
  ruleDialogVisible.value = true
}

// 编辑规则
   
const handleEditRule = (rule: unknown) => {
  currentRule.value = rule
  ruleDialogMode.value = 'edit'
  ruleDialogVisible.value = true
}

// 删除规则
   
const handleDeleteRule = async (rule: unknown) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除规则 "${rule.ruleName}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('删除成功')
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 切换规则状态
   
const handleToggleRule = (rule: unknown) => {
  const status = rule.isEnabled ? '启用' : '禁用'
  ElMessage.success(`规则 "${rule.ruleName}" 已${status}`)
}

// 对话框成功回调
const handleRegistrationSuccess = () => {
  ElMessage.success('操作成功')
}

const handleEarlyRetirementSuccess = () => {
  ElMessage.success('操作成功')
}

const handleRuleSuccess = () => {
  ElMessage.success('操作成功')
}

// 获取距离退休天数标签
const getDaysToRetirementTag = (days: number) => {
  if (days <= 30) return 'danger'
  if (days <= 90) return 'warning'
  return 'info'
}

// 获取提醒级别标签
const getReminderLevelTag = (level: string) => {
  switch (level) {
    case 'URGENT': return 'danger'
    case 'WARNING': return 'warning'
    case 'INFO': return 'info'
    default: return ''
  }
}

// 获取退休类型标签
const getRetirementTypeTag = (type: string) => {
  switch (type) {
    case 'NORMAL': return 'success'
    case 'EARLY': return 'warning'
    case 'DISABILITY': return 'info'
    default: return ''
  }
}

// 获取退休类型文本
const getRetirementTypeText = (type: string) => {
  switch (type) {
    case 'NORMAL': return '正常退休'
    case 'EARLY': return '提前退休'
    case 'DISABILITY': return '丧失劳动能力退休'
    default: return type
  }
}

// 获取申请状态标签
const getApplicationStatusTag = (status: string) => {
  switch (status) {
    case 'PENDING_DEPARTMENT_REVIEW': return 'warning'
    case 'PENDING_SCHOOL_REVIEW': return 'warning'
    case 'APPROVED': return 'success'
    case 'REJECTED': return 'danger'
    default: return ''
  }
}

// 获取申请状态文本
const getApplicationStatusText = (status: string) => {
  switch (status) {
    case 'PENDING_DEPARTMENT_REVIEW': return '待部门审核'
    case 'PENDING_SCHOOL_REVIEW': return '待学校审核'
    case 'APPROVED': return '已通过'
    case 'REJECTED': return '已驳回'
    default: return status
  }
}

// 获取规则类型标签
const getRuleTypeTag = (type: string) => {
  switch (type) {
    case 'LEGAL': return 'primary'
    case 'EARLY': return 'warning'
    case 'DISABILITY': return 'info'
    default: return ''
  }
}

// 获取规则类型文本
const getRuleTypeText = (type: string) => {
  switch (type) {
    case 'LEGAL': return '法定'
    case 'EARLY': return '弹性提前'
    case 'DISABILITY': return '丧失劳动能力'
    default: return type
  }
}

// 判断是否可以审核提前退休申请
const canApproveEarlyRetirement = (status: string) => {
  return ['PENDING_DEPARTMENT_REVIEW', 'PENDING_SCHOOL_REVIEW'].includes(status)
}

// 初始化
onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.retirement-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.function-tabs {
  margin-top: 20px;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.tab-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.tab-actions {
  display: flex;
  gap: 8px;
}

.reminder-stats {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stats-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 20px;
  color: white;
}

.stats-icon.urgent {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.stats-icon.warning {
  background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
}

.stats-icon.info {
  background: linear-gradient(135deg, #48dbfb 0%, #0abde3 100%);
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 12px;
  color: #909399;
}

.search-form {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}
</style>
