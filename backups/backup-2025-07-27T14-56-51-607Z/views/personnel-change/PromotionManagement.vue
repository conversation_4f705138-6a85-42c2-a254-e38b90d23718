<template>
  <div class="promotion-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>职级晋升管理</h2>
      <p>管理教职工职级晋升申请、评审流程和晋升记录</p>
    </div>

    <!-- 搜索和操作区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-form">
        <el-row :gutter="20">
          <el-col :span="5">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索姓名、工号"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.department" placeholder="申请部门" clearable>
              <el-option label="全部部门" value=""  />
              <el-option
                v-for="dept in departmentOptions"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
               />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.promotionType" placeholder="晋升类型" clearable>
              <el-option
                v-for="item in promotionTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.status" placeholder="审批状态" clearable>
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-select>
          </el-col>
          <el-col :span="7">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
            <el-button type="success" @click="handleAdd">
              <el-icon><Plus /></el-icon>
              申请晋升
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.total }}</div>
              <div class="stats-label">申请总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon pending">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.pending }}</div>
              <div class="stats-label">评审中</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon approved">
              <el-icon><Trophy /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.approved }}</div>
              <div class="stats-label">已晋升</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon thisYear">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.thisYear }}</div>
              <div class="stats-label">本年晋升</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 晋升记录列表 -->
    <el-card class="table-card" shadow="never">
      <div class="table-header">
        <span class="table-title">职级晋升记录</span>
        <div class="table-actions">
          <el-button size="small" @click="handleExport">
            <el-icon><Download /></el-icon>
            导出记录
          </el-button>
          <el-button size="small" @click="handleBatchReview" :disabled="selectedRows.length === 0">
            <el-icon><Stamp /></el-icon>
            批量评审
          </el-button>
        </div>
      </div>

      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="applicationNo" label="申请编号" width="120"  />
        <el-table-column prop="employeeName" label="姓名" width="100"  />
        <el-table-column prop="employeeNo" label="工号" width="100"  />
        <el-table-column prop="department" label="部门" width="150" show-overflow-tooltip  />
        <el-table-column label="晋升信息" width="200" show-overflow-tooltip>
          <template #default="scope">
            <div class="promotion-info">
              <span class="current-level">{{ scope.row.currentLevel }}</span>
              <el-icon><Right /></el-icon>
              <span class="target-level">{{ scope.row.targetLevel }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="promotionType" label="晋升类型" width="100">
          <template #default="scope">
            <el-tag size="small">{{ getPromotionTypeText(scope.row.promotionType) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="applicationDate" label="申请日期" width="100"  />
        <el-table-column prop="reviewProgress" label="评审进度" width="120">
          <template #default="scope">
            <el-progress
              :percentage="scope.row.reviewProgress"
              :color="getProgressColor(scope.row.reviewProgress)"
             />
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" link @click="handleView(scope.row)">
              查看
            </el-button>
            <el-button 
              v-if="canReview(scope.row)"
              size="small" 
              type="success" 
              link 
              @click="handleReview(scope.row)"
            >
              评审
            </el-button>
            <el-button 
              v-if="canWithdraw(scope.row)"
              size="small" 
              type="warning" 
              link 
              @click="handleWithdraw(scope.row)"
            >
              撤回
            </el-button>
            <el-button size="small" link @click="handlePrint(scope.row)">
              打印
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 晋升申请对话框 -->
    <PromotionApplicationDialog
      v-model:visible="applicationDialogVisible"
      :promotion="currentPromotion"
      :mode="dialogMode"
      :departments="departmentOptions"
      @success="handleApplicationSuccess"
    />

    <!-- 评审对话框 -->
    <PromotionReviewDialog
      v-model:visible="reviewDialogVisible"
      :promotion="currentPromotion"
      @success="handleReviewSuccess"
    />
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  TrendCharts,
  Clock,
  Trophy,
  Calendar,
  Download,
  Stamp,
  Right
} from '@element-plus/icons-vue'
import { organizationApi } from '@/api/organization'
import PromotionApplicationDialog from './components/PromotionApplicationDialog.vue'
import PromotionReviewDialog from './components/PromotionReviewDialog.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref<any[]>([])
const selectedRows = ref<any[]>([])
const departmentOptions = ref<any[]>([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  department: '',
  promotionType: '',
  status: ''
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 统计信息
const stats = reactive({
  total: 0,
  pending: 0,
  approved: 0,
  thisYear: 0
})

// 对话框相关
const applicationDialogVisible = ref(false)
const reviewDialogVisible = ref(false)
const dialogMode = ref<'view' | 'add' | 'edit'>('view')
const currentPromotion = ref(null)

// 晋升类型选项
const promotionTypeOptions = [
  { label: '正常晋升', value: 'NORMAL' },
  { label: '破格晋升', value: 'EXCEPTIONAL' },
  { label: '职称晋升', value: 'TITLE' },
  { label: '管理晋升', value: 'MANAGEMENT' }
]

// 状态选项
const statusOptions = [
  { label: '待部门评审', value: 'PENDING_DEPT_REVIEW' },
  { label: '待学院评审', value: 'PENDING_COLLEGE_REVIEW' },
  { label: '待人事评审', value: 'PENDING_HR_REVIEW' },
  { label: '待校级评审', value: 'PENDING_SCHOOL_REVIEW' },
  { label: '已通过', value: 'APPROVED' },
  { label: '未通过', value: 'REJECTED' },
  { label: '已撤回', value: 'WITHDRAWN' }
]

// 模拟数据
const mockData = [
  {
    id: '1',
    applicationNo: 'PROM202506001',
    employeeName: '张三',
    employeeNo: 'EMP001',
    department: '计算机学院',
    currentLevel: '讲师',
    targetLevel: '副教授',
    promotionType: 'NORMAL',
    applicationDate: '2025-06-15',
    reviewProgress: 60,
    status: 'PENDING_COLLEGE_REVIEW',
    currentReviewer: '学院评审委员会'
  },
  {
    id: '2',
    applicationNo: 'PROM202506002',
    employeeName: '李四',
    employeeNo: 'EMP002',
    department: '机械工程学院',
    currentLevel: '副教授',
    targetLevel: '教授',
    promotionType: 'EXCEPTIONAL',
    applicationDate: '2025-06-10',
    reviewProgress: 100,
    status: 'APPROVED',
    effectiveDate: '2025-07-01'
  }
]

// 获取晋升记录列表
const fetchPromotionList = async () => {
  try {
    loading.value = true
    // 构建查询参数
   
    const params: unknown = {
      page: pagination.page,
      size: pagination.size
    }
    
    // 添加搜索条件
    if (searchForm.keyword) {
      params.keyword = searchForm.keyword
    }
    if (searchForm.department) {
      params.department = searchForm.department
    }
    if (searchForm.promotionType) {
      params.promotionType = searchForm.promotionType
    }
    if (searchForm.status) {
      params.status = searchForm.status
    }
    
    // 调用实际API（暂时使用模拟数据）
    await new Promise(resolve => setTimeout(resolve, 500))
    tableData.value = mockData
    pagination.total = mockData.length
    
    // 更新统计信息
    stats.total = 45
    stats.pending = 12
    stats.approved = 28
    stats.thisYear = 15
  } catch (__error) {
    console.error('获取晋升记录失败:', error)
    ElMessage.error('获取晋升记录失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchPromotionList()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    department: '',
    promotionType: '',
    status: ''
  })
  pagination.page = 1
  fetchPromotionList()
}

// 申请晋升
const handleAdd = () => {
  currentPromotion.value = null
  dialogMode.value = 'add'
  applicationDialogVisible.value = true
}

// 查看详情
   
const handleView = (promotion: unknown) => {
  currentPromotion.value = promotion
  dialogMode.value = 'view'
  applicationDialogVisible.value = true
}

// 评审
   
const handleReview = (promotion: unknown) => {
  currentPromotion.value = promotion
  reviewDialogVisible.value = true
}

// 撤回申请
   
const handleWithdraw = async (promotion: unknown) => {
  try {
    await ElMessageBox.confirm(
      `确定要撤回该晋升申请吗？撤回后需要重新提交申请。`,
      '撤回确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 调用撤回API
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('撤回成功')
    fetchPromotionList()
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('撤回失败:', error)
      ElMessage.error('撤回失败')
    }
  }
}

// 打印
   
const handlePrint = (promotion: unknown) => {
  ElMessage.info('打印功能开发中...')
}

// 导出记录
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 批量评审
const handleBatchReview = () => {
  ElMessage.info('批量评审功能开发中...')
}

// 表格选择变化
   
const handleSelectionChange = (selection: unknown[]) => {
  selectedRows.value = selection
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchPromotionList()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchPromotionList()
}

// 申请成功回调
const handleApplicationSuccess = () => {
  fetchPromotionList()
}

// 评审成功回调
const handleReviewSuccess = () => {
  fetchPromotionList()
}

// 获取晋升类型文本
const getPromotionTypeText = (type: string) => {
  const option = promotionTypeOptions.find(item => item.value === type)
  return option ? option.label : type
}

// 获取进度颜色
const getProgressColor = (progress: number) => {
  if (progress < 30) return '#f56c6c'
  if (progress < 60) return '#e6a23c'
  if (progress < 100) return '#409eff'
  return '#67c23a'
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  switch (status) {
    case 'APPROVED':
      return 'success'
    case 'REJECTED':
      return 'danger'
    case 'WITHDRAWN':
      return 'info'
    default:
      return 'warning'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  const option = statusOptions.find(item => item.value === status)
  return option ? option.label : status
}

// 判断是否可以评审
   
const canReview = (promotion: unknown) => {
  return ['PENDING_DEPT_REVIEW', 'PENDING_COLLEGE_REVIEW', 'PENDING_HR_REVIEW', 'PENDING_SCHOOL_REVIEW'].includes(promotion.status)
}

// 判断是否可以撤回
   
const canWithdraw = (promotion: unknown) => {
  return ['PENDING_DEPT_REVIEW', 'PENDING_COLLEGE_REVIEW'].includes(promotion.status)
}

// 获取部门列表
const fetchDepartments = async () => {
  try {
    const response = await organizationApi.getTree()
    if (response && Array.isArray(response)) {
      departmentOptions.value = response.map(dept => ({
        id: dept.id,
        name: dept.name,
        orgCode: dept.orgCode
      }))
    }
  } catch (__error) {
    console.error('获取部门列表失败:', error)
    ElMessage.error('获取部门列表失败')
  }
}

// 初始化
onMounted(() => {
  fetchPromotionList()
  fetchDepartments()
})
</script>

<style scoped>
.promotion-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  padding: 10px 0;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.pending {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.approved {
  background: linear-gradient(135deg, #ffd93d 0%, #ffb347 100%);
}

.stats-icon.thisYear {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.table-card {
  margin-top: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.promotion-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.current-level {
  color: #606266;
}

.target-level {
  color: #409eff;
  font-weight: 600;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>