<template>
  <el-dialog
    :model-value="visible" @update:model-value="$emit('update:visible', $event)"
    :title="dialogTitle"
    width="700px"
    :close-on-click-modal="false"
    destroy-on-close
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      :disabled="mode === 'view'"
      label-width="120px"
    >
      <el-form-item label="员工信息" prop="employeeId">
        <el-select
          v-model="formData.employeeId"
          placeholder="请选择员工"
          filterable
          remote
          :remote-method="searchEmployee"
          :loading="searchLoading"
          style="width: 100%"
          @change="handleEmployeeChange"
        >
          <el-option
            v-for="emp in employeeOptions"
            :key="emp.id"
            :label="`${emp.name} (${emp.employeeNo})`"
            :value="emp.id"
          >
            <span style="float: left">{{ emp.name }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">
              {{ emp.employeeNo }} - {{ emp.department }}
            </span>
          </el-option>
        </el-select>
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="原部门">
            <el-input v-model="formData.fromDepartment" readonly   />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="原岗位">
            <el-input v-model="formData.fromPosition" readonly   />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="目标部门" prop="toDepartmentId">
            <el-select
              v-model="formData.toDepartmentId"
              placeholder="请选择目标部门"
              style="width: 100%"
              @change="handleDepartmentChange"
            >
              <el-option
                v-for="dept in availableDepartments"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
                :disabled="dept.id === formData.fromDepartmentId"
               />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="目标岗位" prop="toPosition">
            <el-input v-model="formData.toPosition" placeholder="请输入目标岗位"   />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="调动类型" prop="transferType">
        <el-radio-group v-model="formData.transferType">
          <el-radio value="NORMAL">正常调动</el-radio>
          <el-radio value="PROMOTION">晋升调动</el-radio>
          <el-radio value="ROTATION">轮岗调动</el-radio>
          <el-radio value="TEMPORARY">临时借调</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="生效日期" prop="effectiveDate">
        <el-date-picker
          v-model="formData.effectiveDate"
          type="date"
          placeholder="选择生效日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          :disabled-date="disabledDate"
          style="width: 100%"
         />
      </el-form-item>

      <el-form-item label="调动原因" prop="transferReason">
        <el-input
          v-model="formData.transferReason"
          type="textarea"
          :rows="3"
          placeholder="请详细说明调动原因"
          maxlength="200"
          show-word-limit
          />
      </el-form-item>

      <el-form-item label="工作交接" prop="handoverPlan">
        <el-input
          v-model="formData.handoverPlan"
          type="textarea"
          :rows="3"
          placeholder="请说明工作交接计划"
          maxlength="200"
          show-word-limit
          />
      </el-form-item>

      <el-form-item label="相关附件">
        <el-upload
          ref="uploadRef"
          v-model:file-list="fileList"
          action="#"
          :auto-upload="false"
          :limit="3"
          accept=".pdf,.doc,.docx,.jpg,.png"
        >
          <el-button type="primary" plain>
            <el-icon><Upload /></el-icon>
            选择文件
          </el-button>
          <template #tip>
            <div class="el-upload__tip">
              支持上传 PDF、Word、图片格式，单个文件不超过 10MB
            </div>
          </template>
        </el-upload>
      </el-form-item>

      <!-- 审批流程预览 -->
      <el-form-item v-if="mode === 'add'" label="审批流程">
        <div class="approval-flow">
          <el-steps :active="0" align-center>
            <el-step title="申请人" description="发起申请"  />
            <el-step title="部门主管" description="部门主管审批"  />
            <el-step title="原部门" description="原部门审批"  />
            <el-step title="目标部门" description="目标部门审批"  />
            <el-step title="人事处" description="人事处审批"  />
            <el-step title="完成" description="调动生效"  />
          </el-steps>
        </div>
      </el-form-item>

      <!-- 审批记录 -->
      <el-form-item v-if="mode === 'view' && formData.approvalRecords" label="审批记录">
        <el-timeline>
          <el-timeline-item
            v-for="(record, index) in formData.approvalRecords"
            :key="index"
            :timestamp="record.time"
            :type="getTimelineType(record.action)"
            placement="top"
          >
            <div class="approval-content">
              <div class="approval-info">
                <span class="approver">{{ record.approver }}</span>
                <el-tag size="small" :type="getApprovalTagType(record.action)">
                  {{ record.action }}
                </el-tag>
              </div>
              <div v-if="record.comment" class="approval-comment">
                {{ record.comment }}
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">{{ mode === 'view' ? '关闭' : '取消' }}</el-button>
      <el-button v-if="mode !== 'view'" type="primary" @click="handleSubmit">
        提交申请
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Upload } from '@element-plus/icons-vue'

// Props定义
interface Props {
  visible: boolean
   
  transfer?: unknown
  mode: 'view' | 'add' | 'edit'
   
  departments: unknown[]
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  mode: 'add',
  departments: () => []
})

// Emits定义
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}>()

// 响应式数据
const formRef = ref()
const uploadRef = ref()
const searchLoading = ref(false)
const employeeOptions = ref<any[]>([])
const fileList = ref<any[]>([])

// 表单数据
const formData = ref({
  employeeId: '',
  employeeName: '',
  employeeNo: '',
  fromDepartmentId: '',
  fromDepartment: '',
  fromPosition: '',
  toDepartmentId: '',
  toDepartment: '',
  toPosition: '',
  transferType: 'NORMAL',
  effectiveDate: '',
  transferReason: '',
  handoverPlan: '',
  attachments: [],
  approvalRecords: []
})

// 表单验证规则
const rules = {
  employeeId: [
    { required: true, message: '请选择员工', trigger: 'change' }
  ],
  toDepartmentId: [
    { required: true, message: '请选择目标部门', trigger: 'change' }
  ],
  toPosition: [
    { required: true, message: '请输入目标岗位', trigger: 'blur' }
  ],
  transferType: [
    { required: true, message: '请选择调动类型', trigger: 'change' }
  ],
  effectiveDate: [
    { required: true, message: '请选择生效日期', trigger: 'change' }
  ],
  transferReason: [
    { required: true, message: '请输入调动原因', trigger: 'blur' },
    { min: 10, message: '调动原因至少10个字符', trigger: 'blur' }
  ],
  handoverPlan: [
    { required: true, message: '请输入工作交接计划', trigger: 'blur' }
  ]
}

// 计算属性
const dialogTitle = computed(() => {
  switch (props.mode) {
    case 'add':
      return '发起部门调动申请'
    case 'edit':
      return '编辑调动申请'
    case 'view':
      return '调动申请详情'
    default:
      return ''
  }
})

const availableDepartments = computed(() => {
  return props.departments.filter(dept => dept.id !== formData.value.fromDepartmentId)
})

// 监听props变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    if (props.transfer) {
      formData.value = { ...props.transfer }
    } else {
      resetForm()
    }
  }
})

// 重置表单
const resetForm = () => {
  formData.value = {
    employeeId: '',
    employeeName: '',
    employeeNo: '',
    fromDepartmentId: '',
    fromDepartment: '',
    fromPosition: '',
    toDepartmentId: '',
    toDepartment: '',
    toPosition: '',
    transferType: 'NORMAL',
    effectiveDate: '',
    transferReason: '',
    handoverPlan: '',
    attachments: [],
    approvalRecords: []
  }
  fileList.value = []
}

// 搜索员工
const searchEmployee = async (query: string) => {
  if (query) {
    searchLoading.value = true
    try {
      // 模拟搜索员工API
      await new Promise(resolve => setTimeout(resolve, 500))
      employeeOptions.value = [
        { id: '1', name: 'HrHr张三', employeeNo: 'EMP001', department: '计算机学院', position: '讲师' },
        { id: '2', name: '李四', employeeNo: 'EMP002', department: '机械工程学院', position: '副教授' },
        { id: '3', name: '王五', employeeNo: 'EMP003', department: '电气工程学院', position: '教授' }
      ].filter(emp => 
        emp.name.includes(query) || emp.employeeNo.includes(query)
      )
    } catch (__error) {
      console.error('搜索员工失败:', error)
    } finally {
      searchLoading.value = false
    }
  } else {
    employeeOptions.value = []
  }
}

// 员工选择变化
const handleEmployeeChange = (employeeId: string) => {
  const employee = employeeOptions.value.find(emp => emp.id === employeeId)
  if (employee) {
    formData.value.employeeName = employee.name
    formData.value.employeeNo = employee.employeeNo
    formData.value.fromDepartment = employee.department
    formData.value.fromPosition = employee.position
    // 设置原部门ID（实际应该从员工信息中获取）
    const fromDept = props.departments.find(dept => dept.name === employee.department)
    if (fromDept) {
      formData.value.fromDepartmentId = fromDept.id
    }
  }
}

// 部门选择变化
const handleDepartmentChange = (deptId: string) => {
  const department = props.departments.find(dept => dept.id === deptId)
  if (department) {
    formData.value.toDepartment = department.name
  }
}

// 禁用日期
const disabledDate = (time: Date) => {
  return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
}

// 获取时间线类型
const getTimelineType = (action: string) => {
  if (action.includes('通过')) return 'success'
  if (action.includes('驳回')) return 'danger'
  return 'primary'
}

// 获取审批标签类型
const getApprovalTagType = (action: string) => {
  if (action.includes('通过')) return 'success'
  if (action.includes('驳回')) return 'danger'
  if (action.includes('提交')) return 'primary'
  return 'info'
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    // 构建提交数据
    const submitData = {
      ...formData.value,
      attachments: fileList.value.map(file => ({
        name: file.name,
        url: file.url || file.response?.url
      }))
    }
    
    // 调用提交API（暂时模拟）
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('调动申请提交成功')
    emit('success')
    handleClose()
  } catch (__error) {
    console.error('提交失败:', error)
  }
}
</script>

<style scoped>
.approval-flow {
  width: 100%;
  padding: 20px 0;
}

.approval-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.approval-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.approver {
  font-weight: 600;
  color: #303133;
}

.approval-comment {
  font-size: 13px;
  color: #909399;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
}
</style>