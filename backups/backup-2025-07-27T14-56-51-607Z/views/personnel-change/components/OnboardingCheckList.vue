<template>
  <div class="onboarding-checklist">
    <el-card class="checklist-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span class="title">入职材料清单</span>
          <el-tag :type="getProgressType()" size="small">
            {{ completedCount }}/{{ materialList.length }} 已收集
          </el-tag>
        </div>
      </template>

      <!-- 材料清单 -->
      <div class="material-list">
        <div
          v-for="(category, index) in materialCategories"
          :key="index"
          class="material-category"
        >
          <h4 class="category-title">
            <el-icon><FolderOpened /></el-icon>
            {{ category.name }}
          </h4>
          
          <div class="material-items">
            <div
              v-for="item in category.items"
              :key="item.id"
              class="material-item"
              :class="{ completed: item.status === 'COLLECTED' }"
            >
              <div class="item-content">
                <el-checkbox
                  v-model="item.checked"
                  :disabled="readonly"
                  @change="handleCheckChange(item)"
                >
                  <span class="item-name">{{ item.name }}</span>
                  <el-tag v-if="item.required" type="danger" size="small" class="required-tag">
                    必需
                  </el-tag>
                </el-checkbox>
                
                <div class="item-info">
                  <span v-if="item.copies" class="copies-info">
                    <el-icon><CopyDocument /></el-icon>
                    {{ item.copies }}份
                  </span>
                  <span v-if="item.format" class="format-info">
                    <el-icon><Document /></el-icon>
                    {{ item.format }}
                  </span>
                </div>
              </div>
              
              <div class="item-actions">
                <el-upload
                  v-if="!readonly && item.checked"
                  :ref="`upload-${item.id}`"
                  action="#"
                  :auto-upload="false"
                  :limit="1"
                  :on-change="(file) => handleFileChange(file, item)"
                  :on-remove="() => handleFileRemove(item)"
                  :show-file-list="false"
                >
                  <el-button size="small" type="primary" text>
                    <el-icon><Upload /></el-icon>
                    上传
                  </el-button>
                </el-upload>
                
                <el-button
                  v-if="item.fileUrl"
                  size="small"
                  type="success"
                  text
                  @click="previewFile(item)"
                >
                  <el-icon><View /></el-icon>
                  查看
                </el-button>
                
                <el-button
                  v-if="item.remark && !readonly"
                  size="small"
                  type="warning"
                  text
                  @click="handleRemark(item)"
                >
                  <el-icon><Edit /></el-icon>
                  备注
                </el-button>
              </div>
              
              <!-- 收集状态 -->
              <div v-if="item.checked" class="item-status">
                <template v-if="item.status === 'COLLECTED'">
                  <el-icon class="status-icon success"><CircleCheck /></el-icon>
                  <span class="status-text">已收集</span>
                  <span class="collect-date">{{ item.collectDate }}</span>
                </template>
                <template v-else-if="item.status === 'PENDING'">
                  <el-icon class="status-icon warning"><Clock /></el-icon>
                  <span class="status-text">待上传</span>
                </template>
                <template v-else-if="item.status === 'REJECTED'">
                  <el-icon class="status-icon danger"><CircleClose /></el-icon>
                  <span class="status-text">需重新提交</span>
                  <span class="reject-reason">{{ item.rejectReason }}</span>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div v-if="!readonly" class="action-buttons">
        <el-button type="primary" @click="handleSave">
          <el-icon><DocumentChecked /></el-icon>
          保存清单
        </el-button>
        <el-button @click="handleExportList">
          <el-icon><Download /></el-icon>
          导出清单
        </el-button>
        <el-button type="success" :disabled="!isAllRequired" @click="handleSubmit">
          <el-icon><Check /></el-icon>
          提交审核
        </el-button>
      </div>

      <!-- 进度统计 -->
      <div class="progress-section">
        <div class="progress-header">
          <span>收集进度</span>
          <span class="progress-percentage">{{ progressPercentage }}%</span>
        </div>
        <el-progress
          :percentage="progressPercentage"
          :stroke-width="10"
          :color="progressColors"
          show-text
         />
        <div class="progress-detail">
          <div class="detail-item">
            <span class="label">必需材料:</span>
            <span class="value">{{ requiredCompleted }}/{{ requiredTotal }}</span>
          </div>
          <div class="detail-item">
            <span class="label">可选材料:</span>
            <span class="value">{{ optionalCompleted }}/{{ optionalTotal }}</span>
          </div>
          <div class="detail-item">
            <span class="label">最后更新:</span>
            <span class="value">{{ lastUpdateTime || '无' }}</span>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 备注对话框 -->
    <el-dialog
      v-model="remarkDialogVisible"
      title="添加备注"
      width="500px"
    >
      <el-input
        v-model="currentRemark"
        type="textarea"
        :rows="4"
        placeholder="请输入备注信息..."
        />
      <template #footer>
        <el-button @click="remarkDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleRemarkConfirm">确定</el-button>
      </template>
    </el-dialog>

    <!-- 文件预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      :title="previewTitle"
      width="80%"
      class="preview-dialog"
    >
      <div class="file-preview">
        <iframe
          v-if="previewUrl && isPreviewable"
          :src="previewUrl"
          frameborder="0"
          width="100%"
          height="600"
        />
        <div v-else class="preview-unavailable">
          <el-icon size="48"><Document /></el-icon>
          <p>该文件无法预览</p>
          <el-button type="primary" @click="downloadFile">
            <el-icon><Download /></el-icon>
            下载文件
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  FolderOpened,
  CopyDocument,
  Document,
  Upload,
  View,
  Edit,
  CircleCheck,
  Clock,
  CircleClose,
  DocumentChecked,
  Download,
  Check
} from '@element-plus/icons-vue'

// Props定义
interface Props {
  applicationId: string
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false
})

// Emits定义
const emit = defineEmits<{
   
  (e: 'update', data: unknown): void
   
  (e: 'submit', data: unknown): void
}>()

// 响应式数据
const materialList = ref<any[]>([])
const remarkDialogVisible = ref(false)
const currentRemark = ref('')
const currentItem = ref<unknown>(null)
const previewDialogVisible = ref(false)
const previewTitle = ref('')
const previewUrl = ref('')
const lastUpdateTime = ref('')

// 材料分类
const materialCategories = computed(() => {
  const categories = [
    {
      name: 'HrHr基本证件',
      items: [] as unknown[]
    },
    {
      name: '学历学位',
      items: [] as unknown[]
    },
    {
      name: '职称证书',
      items: [] as unknown[]
    },
    {
      name: '其他材料',
      items: [] as unknown[]
    }
  ]

  materialList.value.forEach(item => {
    if (item.category === 'BASIC') {
      categories[0].items.push(item)
    } else if (item.category === 'EDUCATION') {
      categories[1].items.push(item)
    } else if (item.category === 'TITLE') {
      categories[2].items.push(item)
    } else {
      categories[3].items.push(item)
    }
  })

  return categories.filter(cat => cat.items.length > 0)
})

// 计算属性
const completedCount = computed(() => {
  return materialList.value.filter(item => item.status === 'COLLECTED').length
})

const requiredTotal = computed(() => {
  return materialList.value.filter(item => item.required).length
})

const requiredCompleted = computed(() => {
  return materialList.value.filter(item => item.required && item.status === 'COLLECTED').length
})

const optionalTotal = computed(() => {
  return materialList.value.filter(item => !item.required).length
})

const optionalCompleted = computed(() => {
  return materialList.value.filter(item => !item.required && item.status === 'COLLECTED').length
})

const progressPercentage = computed(() => {
  if (materialList.value.length === 0) return 0
  return Math.round((completedCount.value / materialList.value.length) * 100)
})

const isAllRequired = computed(() => {
  return requiredCompleted.value === requiredTotal.value
})

const isPreviewable = computed(() => {
  if (!previewUrl.value) return false
  const ext = previewUrl.value.split('.').pop()?.toLowerCase()
  return ['pdf', 'jpg', 'jpeg', 'png', 'gif'].includes(ext || '')
})

// 进度条颜色
const progressColors = [
  { color: '#f56c6c', percentage: 20 },
  { color: '#e6a23c', percentage: 40 },
  { color: '#5cb87a', percentage: 60 },
  { color: '#1989fa', percentage: 80 },
  { color: '#6f7ad3', percentage: 100 }
]

// 获取进度类型
const getProgressType = () => {
  const percentage = progressPercentage.value
  if (percentage === 100) return 'success'
  if (percentage >= 80) return 'primary'
  if (percentage >= 60) return 'warning'
  return 'danger'
}

// 初始化材料清单
const initMaterialList = async () => {
  try {
    // 调用API获取材料清单（暂时使用模拟数据）
    const mockData = [
      { id: '1', name: '身份证', category: 'BASIC', required: true, copies: 2, format: '复印件', checked: false, status: 'PENDING' },
      { id: '2', name: '户口本', category: 'BASIC', required: true, copies: 1, format: '复印件', checked: false, status: 'PENDING' },
      { id: '3', name: '学历证书', category: 'EDUCATION', required: true, copies: 1, format: '原件+复印件', checked: false, status: 'PENDING' },
      { id: '4', name: '学位证书', category: 'EDUCATION', required: true, copies: 1, format: '原件+复印件', checked: false, status: 'PENDING' },
      { id: '5', name: '教师资格证', category: 'TITLE', required: false, copies: 1, format: '复印件', checked: false, status: 'PENDING' },
      { id: '6', name: '职称证书', category: 'TITLE', required: false, copies: 1, format: '复印件', checked: false, status: 'PENDING' },
      { id: '7', name: '体检报告', category: 'OTHER', required: true, copies: 1, format: '原件', checked: false, status: 'PENDING' },
      { id: '8', name: '个人简历', category: 'OTHER', required: true, copies: 1, format: '电子版', checked: false, status: 'PENDING' }
    ]
    
    materialList.value = mockData
  } catch (__error) {
    console.error('获取材料清单失败:', error)
    ElMessage.error('获取材料清单失败')
  }
}

// 处理勾选变化
   
const handleCheckChange = (item: unknown) => {
  if (!item.checked) {
    item.status = 'PENDING'
    item.fileUrl = null
    item.collectDate = null
  }
}

// 处理文件上传
   
const handleFileChange = async (file: unknown, item: unknown) => {
  try {
    // 文件验证
    const isLt10M = file.size / 1024 / 1024 < 10
    if (!isLt10M) {
      ElMessage.error('文件大小不能超过 10MB!')
      return
    }

    // 构建上传数据
    const formData = new FormData()
    formData.append('file', file.raw)
    formData.append('materialId', item.id)
    formData.append('applicationId', props.applicationId)

    // 调用上传API（暂时模拟）
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 更新状态
    item.fileUrl = URL.createObjectURL(file.raw)
    item.fileName = file.name
    item.status = 'COLLECTED'
    item.collectDate = new Date().toLocaleDateString()
    
    ElMessage.success('文件上传成功')
    updateLastTime()
  } catch (__error) {
    console.error('文件上传失败:', error)
    ElMessage.error('文件上传失败')
  }
}

// 处理文件移除
   
const handleFileRemove = (item: unknown) => {
  item.fileUrl = null
  item.fileName = null
  item.status = 'PENDING'
  item.collectDate = null
  updateLastTime()
}

// 预览文件
   
const previewFile = (item: unknown) => {
  previewTitle.value = item.name
  previewUrl.value = item.fileUrl
  previewDialogVisible.value = true
}

// 下载文件
const downloadFile = () => {
  if (previewUrl.value) {
    const link = document.createElement('a')
    link.href = previewUrl.value
    link.download = previewTitle.value
    link.click()
  }
}

// 处理备注
   
const handleRemark = (item: unknown) => {
  currentItem.value = item
  currentRemark.value = item.remark || ''
  remarkDialogVisible.value = true
}

// 确认备注
const handleRemarkConfirm = () => {
  if (currentItem.value) {
    currentItem.value.remark = currentRemark.value
    updateLastTime()
  }
  remarkDialogVisible.value = false
}

// 保存清单
const handleSave = async () => {
  try {
    const saveData = {
      applicationId: props.applicationId,
      materials: materialList.value.map(item => ({
        id: item.id,
        checked: item.checked,
        status: item.status,
        fileUrl: item.fileUrl,
        fileName: item.fileName,
        remark: item.remark,
        collectDate: item.collectDate
      }))
    }
    
    // 调用保存API（暂时模拟）
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('保存成功')
    emit('update', saveData)
  } catch (__error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
}

// 导出清单
const handleExportList = () => {
  // 生成清单内容
  let content = '入职材料清单\n\n'
  materialCategories.value.forEach(category => {
    content += `【${category.name}】\n`
    category.items.forEach((item, index) => {
      const status = item.status === 'COLLECTED' ? '✓' : '□'
      const required = item.required ? '(必需)' : ''
      content += `${status} ${index + 1}. ${item.name} ${required}\n`
      if (item.copies) content += `   份数: ${item.copies}份\n`
      if (item.format) content += `   格式: ${item.format}\n`
      if (item.remark) content += `   备注: ${item.remark}\n`
      content += '\n'
    })
  })
  
  // 下载文件
  const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `入职材料清单_${props.applicationId}.txt`
  link.click()
}

// 提交审核
const handleSubmit = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要提交审核吗？提交后将无法修改材料信息。',
      '提交确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const submitData = {
      applicationId: props.applicationId,
      materials: materialList.value.filter(item => item.checked)
    }
    
    // 调用提交API（暂时模拟）
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('提交成功')
    emit('submit', submitData)
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('提交失败:', error)
      ElMessage.error('提交失败')
    }
  }
}

// 更新最后更新时间
const updateLastTime = () => {
  lastUpdateTime.value = new Date().toLocaleString()
}

// 初始化
onMounted(() => {
  initMaterialList()
})

// 监听applicationId变化
watch(() => props.applicationId, () => {
  initMaterialList()
})
</script>

<style scoped>
.onboarding-checklist {
  width: 100%;
}

.checklist-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header .title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.material-list {
  padding: 20px 0;
}

.material-category {
  margin-bottom: 30px;
}

.category-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 16px 0;
  color: #606266;
  font-size: 15px;
  font-weight: 600;
}

.material-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.material-item {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s;
}

.material-item:hover {
  background: #f0f2f5;
}

.material-item.completed {
  background: #f0f9ff;
  border: 1px solid #e1f3fb;
}

.item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.item-name {
  margin-right: 8px;
  font-size: 14px;
}

.required-tag {
  margin-left: 4px;
}

.item-info {
  display: flex;
  gap: 16px;
  font-size: 13px;
  color: #909399;
}

.copies-info,
.format-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.item-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.item-status {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #ebeef5;
  font-size: 13px;
}

.status-icon {
  font-size: 16px;
}

.status-icon.success {
  color: #67c23a;
}

.status-icon.warning {
  color: #e6a23c;
}

.status-icon.danger {
  color: #f56c6c;
}

.status-text {
  color: #606266;
}

.collect-date {
  color: #909399;
  margin-left: auto;
}

.reject-reason {
  color: #f56c6c;
  font-size: 12px;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding: 20px 0;
  border-top: 1px solid #ebeef5;
}

.progress-section {
  margin-top: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
  color: #606266;
}

.progress-percentage {
  font-size: 18px;
  font-weight: 600;
  color: #409eff;
}

.progress-detail {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item .label {
  font-size: 13px;
  color: #909399;
}

.detail-item .value {
  font-size: 14px;
  font-weight: 600;
  color: #606266;
}

.preview-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.file-preview {
  width: 100%;
  height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-unavailable {
  text-align: center;
  color: #909399;
}

.preview-unavailable p {
  margin: 16px 0;
  font-size: 14px;
}
</style>