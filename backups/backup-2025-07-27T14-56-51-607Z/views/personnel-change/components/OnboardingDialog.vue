<template>
  <el-dialog
    :model-value="visible" @update:model-value="$emit('update:visible', $event)"
    :title="dialogTitle"
    :width="mode === 'view' ? '80%' : '70%'"
    :close-on-click-modal="false"
    destroy-on-close
    @close="handleClose"
  >
    <div v-if="mode === 'view'" class="view-mode">
      <!-- 查看模式 - 分步骤展示 -->
      <el-steps :active="activeStep" align-center>
        <el-step title="基本信息"  />
        <el-step title="教育经历"  />
        <el-step title="工作经历"  />
        <el-step title="入职材料"  />
        <el-step title="审批记录"  />
      </el-steps>

      <div class="step-content">
        <!-- 基本信息 -->
        <div v-if="activeStep === 0" class="info-section">
          <h3 class="section-title">基本信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="申请编号">{{ formData.applicationId }}</el-descriptions-item>
            <el-descriptions-item label="申请日期">{{ formData.applicationDate }}</el-descriptions-item>
            <el-descriptions-item label="姓名">{{ formData.fullName }}</el-descriptions-item>
            <el-descriptions-item label="性别">{{ formData.gender === 'MALE' ? '男' : '女' }}</el-descriptions-item>
            <el-descriptions-item label="身份证号">{{ formData.idNumber }}</el-descriptions-item>
            <el-descriptions-item label="出生日期">{{ formData.birthDate }}</el-descriptions-item>
            <el-descriptions-item label="手机号码">{{ formData.mobile }}</el-descriptions-item>
            <el-descriptions-item label="电子邮箱">{{ formData.email }}</el-descriptions-item>
            <el-descriptions-item label="拟入职部门">{{ formData.proposedDepartment }}</el-descriptions-item>
            <el-descriptions-item label="拟入职岗位">{{ formData.proposedPosition }}</el-descriptions-item>
            <el-descriptions-item label="拟定职级">{{ formData.proposedLevel }}</el-descriptions-item>
            <el-descriptions-item label="预计入职日期">{{ formData.expectedDate }}</el-descriptions-item>
            <el-descriptions-item label="当前状态" :span="2">
              <el-tag :type="getStatusType(formData.status)">
                {{ getStatusText(formData.status) }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 教育经历 -->
        <div v-else-if="activeStep === 1" class="info-section">
          <h3 class="section-title">教育经历</h3>
          <el-table :data="formData.educationList" stripe>
            <el-table-column prop="startDate" label="开始时间" width="100"  />
            <el-table-column prop="endDate" label="结束时间" width="100"  />
            <el-table-column prop="school" label="学校名称"  />
            <el-table-column prop="major" label="专业"  />
            <el-table-column prop="degree" label="学历" width="80"  />
            <el-table-column prop="degreeType" label="学位" width="80"  />
          </el-table>
        </div>

        <!-- 工作经历 -->
        <div v-else-if="activeStep === 2" class="info-section">
          <h3 class="section-title">工作经历</h3>
          <el-table :data="formData.workList" stripe>
            <el-table-column prop="startDate" label="开始时间" width="100"  />
            <el-table-column prop="endDate" label="结束时间" width="100"  />
            <el-table-column prop="company" label="单位名称"  />
            <el-table-column prop="position" label="职位"  />
            <el-table-column prop="description" label="工作描述" show-overflow-tooltip  />
          </el-table>
        </div>

        <!-- 入职材料 -->
        <div v-else-if="activeStep === 3" class="info-section">
          <OnboardingCheckList
            :application-id="formData.id"
            :readonly="true"
          />
        </div>

        <!-- 审批记录 -->
        <div v-else-if="activeStep === 4" class="info-section">
          <h3 class="section-title">审批记录</h3>
          <el-timeline>
            <el-timeline-item
              v-for="(record, index) in formData.approvalRecords"
              :key="index"
              :timestamp="record.time"
              :type="getApprovalType(record.action)"
              placement="top"
            >
              <div class="approval-content">
                <div class="approval-info">
                  <span class="approver">{{ record.approver }}</span>
                  <el-tag size="small" :type="getApprovalTagType(record.action)">
                    {{ record.action }}
                  </el-tag>
                </div>
                <div v-if="record.comment" class="approval-comment">
                  {{ record.comment }}
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>

      <!-- 步骤导航 -->
      <div class="step-navigation">
        <el-button @click="activeStep--" :disabled="activeStep === 0">
          上一步
        </el-button>
        <el-button type="primary" @click="activeStep++" :disabled="activeStep === 4">
          下一步
        </el-button>
      </div>
    </div>

    <el-form
      v-else
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      class="onboarding-form"
    >
      <!-- 编辑/新增模式 -->
      <el-tabs v-model="activeTab">
        <el-tab-pane label="基本信息" name="basic">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="姓名" prop="fullName">
                <el-input v-model="formData.fullName" placeholder="请输入姓名"   />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="性别" prop="gender">
                <el-radio-group v-model="formData.gender">
                  <el-radio value="MALE">男</el-radio>
                  <el-radio value="FEMALE">女</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="身份证号" prop="idNumber">
                <el-input v-model="formData.idNumber" placeholder="请输入身份证号"   />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="出生日期" prop="birthDate">
                <el-date-picker
                  v-model="formData.birthDate"
                  type="date"
                  placeholder="选择日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                 />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="手机号码" prop="mobile">
                <el-input v-model="formData.mobile" placeholder="请输入手机号码"   />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="电子邮箱" prop="email">
                <el-input v-model="formData.email" placeholder="请输入电子邮箱"   />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="拟入职部门" prop="proposedDepartment">
                <el-select v-model="formData.proposedDepartment" placeholder="请选择部门" style="width: 100%">
                  <el-option
                    v-for="dept in departmentOptions"
                    :key="dept.id"
                    :label="dept.name"
                    :value="dept.name"
                   />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="拟入职岗位" prop="proposedPosition">
                <el-input v-model="formData.proposedPosition" placeholder="请输入拟入职岗位"   />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="拟定职级" prop="proposedLevel">
                <el-select v-model="formData.proposedLevel" placeholder="请选择职级" style="width: 100%">
                  <el-option label="教授" value="教授"  />
                  <el-option label="副教授" value="副教授"  />
                  <el-option label="讲师" value="讲师"  />
                  <el-option label="助教" value="助教"  />
                  <el-option label="其他" value="其他"  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="预计入职日期" prop="expectedDate">
                <el-date-picker
                  v-model="formData.expectedDate"
                  type="date"
                  placeholder="选择日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                 />
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>

        <el-tab-pane label="教育经历" name="education">
          <div class="sub-section">
            <div class="sub-section-header">
              <span>教育经历</span>
              <el-button type="primary" size="small" @click="addEducation">
                <el-icon><Plus /></el-icon>
                添加
              </el-button>
            </div>
            <el-table :data="formData.educationList" stripe>
              <el-table-column prop="startDate" label="开始时间" width="100"  />
              <el-table-column prop="endDate" label="结束时间" width="100"  />
              <el-table-column prop="school" label="学校名称"  />
              <el-table-column prop="major" label="专业"  />
              <el-table-column prop="degree" label="学历" width="80"  />
              <el-table-column prop="degreeType" label="学位" width="80"  />
              <el-table-column label="操作" width="100">
                <template #default="scope">
                  <el-button type="primary" link size="small" @click="editEducation(scope.$index)">
                    编辑
                  </el-button>
                  <el-button type="danger" link size="small" @click="removeEducation(scope.$index)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <el-tab-pane label="工作经历" name="work">
          <div class="sub-section">
            <div class="sub-section-header">
              <span>工作经历</span>
              <el-button type="primary" size="small" @click="addWork">
                <el-icon><Plus /></el-icon>
                添加
              </el-button>
            </div>
            <el-table :data="formData.workList" stripe>
              <el-table-column prop="startDate" label="开始时间" width="100"  />
              <el-table-column prop="endDate" label="结束时间" width="100"  />
              <el-table-column prop="company" label="单位名称"  />
              <el-table-column prop="position" label="职位"  />
              <el-table-column prop="description" label="工作描述" show-overflow-tooltip  />
              <el-table-column label="操作" width="100">
                <template #default="scope">
                  <el-button type="primary" link size="small" @click="editWork(scope.$index)">
                    编辑
                  </el-button>
                  <el-button type="danger" link size="small" @click="removeWork(scope.$index)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button v-if="mode !== 'view'" type="primary" @click="handleSubmit">
        {{ mode === 'add' ? '提交申请' : '保存修改' }}
      </el-button>
    </template>

    <!-- 教育经历编辑对话框 -->
    <el-dialog
      v-model="educationDialogVisible"
      title="教育经历"
      width="600px"
      append-to-body
    >
      <el-form :model="educationForm" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间">
              <el-date-picker
                v-model="educationForm.startDate"
                type="month"
                placeholder="选择月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间">
              <el-date-picker
                v-model="educationForm.endDate"
                type="month"
                placeholder="选择月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="学校名称">
          <el-input v-model="educationForm.school" placeholder="请输入学校名称"   />
        </el-form-item>
        <el-form-item label="专业">
          <el-input v-model="educationForm.major" placeholder="请输入专业"   />
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="学历">
              <el-select v-model="educationForm.degree" placeholder="请选择" style="width: 100%">
                <el-option label="博士研究生" value="博士研究生"  />
                <el-option label="硕士研究生" value="硕士研究生"  />
                <el-option label="本科" value="本科"  />
                <el-option label="专科" value="专科"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="学位">
              <el-select v-model="educationForm.degreeType" placeholder="请选择" style="width: 100%">
                <el-option label="博士" value="博士"  />
                <el-option label="硕士" value="硕士"  />
                <el-option label="学士" value="学士"  />
                <el-option label="无" value="无"  />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button @click="educationDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveEducation">确定</el-button>
      </template>
    </el-dialog>

    <!-- 工作经历编辑对话框 -->
    <el-dialog
      v-model="workDialogVisible"
      title="工作经历"
      width="600px"
      append-to-body
    >
      <el-form :model="workForm" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间">
              <el-date-picker
                v-model="workForm.startDate"
                type="month"
                placeholder="选择月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间">
              <el-date-picker
                v-model="workForm.endDate"
                type="month"
                placeholder="选择月份或至今"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="单位名称">
          <el-input v-model="workForm.company" placeholder="请输入单位名称"   />
        </el-form-item>
        <el-form-item label="职位">
          <el-input v-model="workForm.position" placeholder="请输入职位"   />
        </el-form-item>
        <el-form-item label="工作描述">
          <el-input
            v-model="workForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入工作描述"
            />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="workDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveWork">确定</el-button>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { organizationApi } from '@/api/organization'
import OnboardingCheckList from './OnboardingCheckList.vue'

// Props定义
interface Props {
  visible: boolean
   
  application?: unknown
  mode: 'view' | 'add' | 'edit'
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  mode: 'view'
})

// Emits定义
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}>()

// 响应式数据
const formRef = ref()
const activeStep = ref(0)
const activeTab = ref('basic')
const departmentOptions = ref<any[]>([])

// 表单数据
const formData = ref<unknown>({
  fullName: '',
  gender: 'MALE',
  idNumber: '',
  birthDate: '',
  mobile: '',
  email: '',
  proposedDepartment: '',
  proposedPosition: '',
  proposedLevel: '',
  expectedDate: '',
  educationList: [],
  workList: [],
  approvalRecords: []
})

// 教育经历表单
const educationDialogVisible = ref(false)
const educationEditIndex = ref(-1)
const educationForm = ref({
  startDate: '',
  endDate: '',
  school: '',
  major: '',
  degree: '',
  degreeType: ''
})

// 工作经历表单
const workDialogVisible = ref(false)
const workEditIndex = ref(-1)
const workForm = ref({
  startDate: '',
  endDate: '',
  company: '',
  position: '',
  description: ''
})

// 表单验证规则
const rules = {
  fullName: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ],
  idNumber: [
    { required: true, message: '请输入身份证号', trigger: 'blur' },
    { pattern: /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dX]$/, message: '身份证号格式不正确', trigger: 'blur' }
  ],
  birthDate: [
    { required: true, message: '请选择出生日期', trigger: 'change' }
  ],
  mobile: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入电子邮箱', trigger: 'blur' },
    { type: 'email', message: '邮箱格式不正确', trigger: 'blur' }
  ],
  proposedDepartment: [
    { required: true, message: '请选择拟入职部门', trigger: 'change' }
  ],
  proposedPosition: [
    { required: true, message: '请输入拟入职岗位', trigger: 'blur' }
  ],
  proposedLevel: [
    { required: true, message: '请选择拟定职级', trigger: 'change' }
  ],
  expectedDate: [
    { required: true, message: '请选择预计入职日期', trigger: 'change' }
  ]
}

// 计算属性
const dialogTitle = computed(() => {
  switch (props.mode) {
    case 'add':
      return '新增入职申请'
    case 'edit':
      return '编辑入职申请'
    case 'view':
      return '入职申请详情'
    default:
      return ''
  }
})

// 监听props变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    if (props.application) {
      formData.value = { ...props.application }
    } else {
      resetForm()
    }
    fetchDepartments()
  }
})

// 获取部门列表
const fetchDepartments = async () => {
  try {
    const response = await organizationApi.getTree()
    if (response && Array.isArray(response)) {
      departmentOptions.value = response.map(dept => ({
        id: dept.id,
        name: dept.name
      }))
    }
  } catch (__error) {
    console.error('获取部门列表失败:', error)
  }
}

// 重置表单
const resetForm = () => {
  formData.value = {
    fullName: '',
    gender: 'MALE',
    idNumber: '',
    birthDate: '',
    mobile: '',
    email: '',
    proposedDepartment: '',
    proposedPosition: '',
    proposedLevel: '',
    expectedDate: '',
    educationList: [],
    workList: [],
    approvalRecords: []
  }
  activeStep.value = 0
  activeTab.value = 'basic'
}

// 添加教育经历
const addEducation = () => {
  educationEditIndex.value = -1
  educationForm.value = {
    startDate: '',
    endDate: '',
    school: '',
    major: '',
    degree: '',
    degreeType: ''
  }
  educationDialogVisible.value = true
}

// 编辑教育经历
const editEducation = (index: number) => {
  educationEditIndex.value = index
  educationForm.value = { ...formData.value.educationList[index] }
  educationDialogVisible.value = true
}

// 删除教育经历
const removeEducation = (index: number) => {
  formData.value.educationList.splice(index, 1)
}

// 保存教育经历
const saveEducation = () => {
  if (educationEditIndex.value === -1) {
    formData.value.educationList.push({ ...educationForm.value })
  } else {
    formData.value.educationList[educationEditIndex.value] = { ...educationForm.value }
  }
  educationDialogVisible.value = false
}

// 添加工作经历
const addWork = () => {
  workEditIndex.value = -1
  workForm.value = {
    startDate: '',
    endDate: '',
    company: '',
    position: '',
    description: ''
  }
  workDialogVisible.value = true
}

// 编辑工作经历
const editWork = (index: number) => {
  workEditIndex.value = index
  workForm.value = { ...formData.value.workList[index] }
  workDialogVisible.value = true
}

// 删除工作经历
const removeWork = (index: number) => {
  formData.value.workList.splice(index, 1)
}

// 保存工作经历
const saveWork = () => {
  if (workEditIndex.value === -1) {
    formData.value.workList.push({ ...workForm.value })
  } else {
    formData.value.workList[workEditIndex.value] = { ...workForm.value }
  }
  workDialogVisible.value = false
}

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'APPROVED':
    case 'ONBOARDED':
      return 'success'
    case 'REJECTED':
      return 'danger'
    case 'PENDING_PERSONAL_INFO':
      return 'info'
    default:
      return 'warning'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'PENDING_PERSONAL_INFO': '待个人完善',
    'PENDING_DEPARTMENT_REVIEW': '待二级单位审核',
    'PENDING_HR_REVIEW': '待人事处审核',
    'APPROVED': '已通过',
    'REJECTED': '已驳回',
    'ONBOARDED': '已入职'
  }
  return statusMap[status] || status
}

// 获取审批时间线类型
const getApprovalType = (action: string) => {
  switch (action) {
    case '通过':
      return 'success'
    case '驳回':
      return 'danger'
    default:
      return 'primary'
  }
}

// 获取审批标签类型
const getApprovalTagType = (action: string) => {
  switch (action) {
    case '通过':
      return 'success'
    case '驳回':
      return 'danger'
    case '提交':
      return 'primary'
    default:
      return 'info'
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    // 构建提交数据
    const submitData = {
      ...formData.value,
      applicationDate: new Date().toISOString().split('T')[0],
      status: 'PENDING_DEPARTMENT_REVIEW'
    }
    
    // 调用API（暂时模拟）
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success(props.mode === 'add' ? '申请提交成功' : '保存成功')
    emit('success')
    handleClose()
  } catch (__error) {
    console.error('提交失败:', error)
  }
}
</script>

<style scoped>
.view-mode {
  padding: 20px;
}

.step-content {
  margin: 30px 0;
  min-height: 400px;
}

.info-section {
  padding: 20px;
}

.section-title {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.step-navigation {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.onboarding-form {
  padding: 20px;
}

.sub-section {
  margin-bottom: 20px;
}

.sub-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  font-size: 14px;
  font-weight: 600;
  color: #606266;
}

.approval-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.approval-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.approver {
  font-weight: 600;
  color: #303133;
}

.approval-comment {
  font-size: 13px;
  color: #909399;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
}
</style>