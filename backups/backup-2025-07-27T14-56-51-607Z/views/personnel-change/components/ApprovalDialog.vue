<template>
  <el-dialog
    :model-value="visible" @update:model-value="$emit('update:visible', $event)"
    title="入职审核"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
    >
      <!-- 申请信息 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <span class="card-title">申请信息</span>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="申请编号">{{ application?.applicationId }}</el-descriptions-item>
          <el-descriptions-item label="申请日期">{{ application?.applicationDate }}</el-descriptions-item>
          <el-descriptions-item label="申请人">{{ application?.fullName }}</el-descriptions-item>
          <el-descriptions-item label="身份证号">{{ application?.idNumber }}</el-descriptions-item>
          <el-descriptions-item label="拟入职部门">{{ application?.proposedDepartment }}</el-descriptions-item>
          <el-descriptions-item label="拟入职岗位">{{ application?.proposedPosition }}</el-descriptions-item>
          <el-descriptions-item label="当前状态">
            <el-tag :type="getStatusType(application?.status)">
              {{ getStatusText(application?.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="当前审批人">{{ application?.currentApprover }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 审核操作 -->
      <el-card class="approval-card" shadow="never">
        <template #header>
          <span class="card-title">审核操作</span>
        </template>
        
        <el-form-item label="审核结果" prop="action">
          <el-radio-group v-model="formData.action">
            <el-radio value="APPROVE">
              <span class="radio-label">
                <el-icon class="approve-icon"><CircleCheck /></el-icon>
                通过
              </span>
            </el-radio>
            <el-radio value="REJECT">
              <span class="radio-label">
                <el-icon class="reject-icon"><CircleClose /></el-icon>
                驳回
              </span>
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 驳回原因 -->
        <el-form-item
          v-if="formData.action === 'REJECT'"
          label="驳回原因"
          prop="rejectReason"
        >
          <el-select v-model="formData.rejectReason" placeholder="请选择驳回原因" style="width: 100%">
            <el-option label="材料不完整" value="材料不完整"  />
            <el-option label="资质不符合要求" value="资质不符合要求"  />
            <el-option label="信息填写有误" value="信息填写有误"  />
            <el-option label="其他原因" value="其他原因"  />
          </el-select>
        </el-form-item>

        <!-- 审核意见 -->
        <el-form-item label="审核意见" prop="comment">
          <el-input
            v-model="formData.comment"
            type="textarea"
            :rows="4"
            placeholder="请输入审核意见..."
            maxlength="200"
            show-word-limit
            />
        </el-form-item>

        <!-- 下一步处理 -->
        <el-form-item
          v-if="formData.action === 'APPROVE' && needNextApprover"
          label="下一审批人"
          prop="nextApprover"
        >
          <el-select v-model="formData.nextApprover" placeholder="请选择下一审批人" style="width: 100%">
            <el-option
              v-for="user in approverOptions"
              :key="user.id"
              :label="user.name"
              :value="user.id"
            >
              <span style="float: left">{{ user.name }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ user.position }}</span>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 附件上传 -->
        <el-form-item label="相关附件">
          <el-upload
            ref="uploadRef"
            action="#"
            :auto-upload="false"
            :limit="3"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            accept=".pdf,.doc,.docx,.jpg,.png"
          >
            <el-button type="primary" plain>
              <el-icon><Upload /></el-icon>
              选择文件
            </el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持上传 PDF、Word、图片格式，单个文件不超过 10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-card>

      <!-- 审批历史 -->
      <el-card v-if="approvalHistory.length > 0" class="history-card" shadow="never">
        <template #header>
          <span class="card-title">审批历史</span>
        </template>
        <el-timeline>
          <el-timeline-item
            v-for="(record, index) in approvalHistory"
            :key="index"
            :timestamp="record.time"
            :type="getTimelineType(record.action)"
            placement="top"
          >
            <div class="timeline-content">
              <div class="timeline-header">
                <span class="approver-name">{{ record.approver }}</span>
                <el-tag size="small" :type="getActionTagType(record.action)">
                  {{ record.action }}
                </el-tag>
              </div>
              <div v-if="record.comment" class="timeline-comment">
                {{ record.comment }}
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">
        提交审核
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  CircleCheck,
  CircleClose,
  Upload
} from '@element-plus/icons-vue'

// Props定义
interface Props {
  visible: boolean
   
  application?: unknown
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})

// Emits定义
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}>()

// 响应式数据
const formRef = ref()
const uploadRef = ref()
const loading = ref(false)

// 表单数据
const formData = ref({
  action: 'APPROVE',
  rejectReason: '',
  comment: '',
  nextApprover: '',
  attachments: [] as unknown[]
})

// 审批历史
const approvalHistory = ref<any[]>([])

// 下一审批人选项
const approverOptions = ref([
  { id: '1', name: 'HrHr张主任', position: '人事处主任' },
  { id: '2', name: '李处长', position: '人事处处长' },
  { id: '3', name: '王院长', position: '计算机学院院长' },
  { id: '4', name: '赵主任', position: '机械学院主任' }
])

// 表单验证规则
const rules = computed(() => ({
  action: [
    { required: true, message: '请选择审核结果', trigger: 'change' }
  ],
  rejectReason: formData.value.action === 'REJECT' ? [
    { required: true, message: '请选择驳回原因', trigger: 'change' }
  ] : [],
  comment: [
    { required: true, message: '请输入审核意见', trigger: 'blur' }
  ],
  nextApprover: needNextApprover.value && formData.value.action === 'APPROVE' ? [
    { required: true, message: '请选择下一审批人', trigger: 'change' }
  ] : []
}))

// 计算属性
const needNextApprover = computed(() => {
  // 根据当前状态判断是否需要下一审批人
  return props.application?.status === 'PENDING_DEPARTMENT_REVIEW'
})

// 监听props变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm()
    fetchApprovalHistory()
  }
})

// 重置表单
const resetForm = () => {
  formData.value = {
    action: 'APPROVE',
    rejectReason: '',
    comment: '',
    nextApprover: '',
    attachments: []
  }
}

// 获取审批历史
const fetchApprovalHistory = async () => {
  try {
    // 调用API获取审批历史（暂时使用模拟数据）
    approvalHistory.value = [
      {
        time: '2025-06-15 10:30:00',
        approver: '申请人',
        action: '提交申请',
        comment: '提交入职申请'
      },
      {
        time: '2025-06-15 14:20:00',
        approver: '人事专员',
        action: '初审通过',
        comment: '材料齐全，符合要求'
      }
    ]
  } catch (__error) {
    console.error('获取审批历史失败:', error)
  }
}

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'APPROVED':
      return 'success'
    case 'REJECTED':
      return 'danger'
    default:
      return 'warning'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'PENDING_PERSONAL_INFO': '待个人完善',
    'PENDING_DEPARTMENT_REVIEW': '待二级单位审核',
    'PENDING_HR_REVIEW': '待人事处审核',
    'APPROVED': '已通过',
    'REJECTED': '已驳回',
    'ONBOARDED': '已入职'
  }
  return statusMap[status] || status
}

// 获取时间线类型
const getTimelineType = (action: string) => {
  if (action.includes('通过')) return 'success'
  if (action.includes('驳回')) return 'danger'
  return 'primary'
}

// 获取操作标签类型
const getActionTagType = (action: string) => {
  if (action.includes('通过')) return 'success'
  if (action.includes('驳回')) return 'danger'
  if (action.includes('提交')) return 'primary'
  return 'info'
}

// 处理文件上传
   
const handleFileChange = (file: unknown) => {
  formData.value.attachments.push(file)
}

// 处理文件移除
   
const handleFileRemove = (file: unknown) => {
  const index = formData.value.attachments.findIndex(f => f.uid === file.uid)
  if (index > -1) {
    formData.value.attachments.splice(index, 1)
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}

// 提交审核
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    const confirmMessage = formData.value.action === 'APPROVE' 
      ? '确定要通过该入职申请吗？' 
      : '确定要驳回该入职申请吗？'
    
    await ElMessageBox.confirm(confirmMessage, '提交确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    loading.value = true
    
    // 构建提交数据
    const submitData = {
      applicationId: props.application?.id,
      action: formData.value.action,
      comment: formData.value.comment,
      rejectReason: formData.value.rejectReason,
      nextApprover: formData.value.nextApprover,
      attachments: formData.value.attachments
    }
    
    // 调用审核API（暂时模拟）
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('审核提交成功')
    emit('success')
    handleClose()
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('审核提交失败:', error)
      ElMessage.error('审核提交失败')
    }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.info-card,
.approval-card,
.history-card {
  margin-bottom: 20px;
}

.card-title {
  font-size: 15px;
  font-weight: 600;
  color: #303133;
}

.radio-label {
  display: flex;
  align-items: center;
  gap: 4px;
}

.approve-icon {
  color: #67c23a;
  font-size: 18px;
}

.reject-icon {
  color: #f56c6c;
  font-size: 18px;
}

.timeline-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.timeline-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.approver-name {
  font-weight: 600;
  color: #303133;
}

.timeline-comment {
  font-size: 13px;
  color: #909399;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
}

:deep(.el-descriptions__label) {
  width: 120px;
}
</style>