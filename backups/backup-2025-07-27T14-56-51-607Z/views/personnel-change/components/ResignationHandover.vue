<template>
  <div class="resignation-handover">
    <el-card class="handover-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span class="title">离职交接清单</span>
          <div class="header-actions">
            <el-tag :type="getStatusType()" size="small">
              {{ completedCount }}/{{ handoverList.length }} 已完成
            </el-tag>
            <el-tag v-if="handoverDeadline" type="warning" size="small">
              截止日期: {{ handoverDeadline }}
            </el-tag>
          </div>
        </div>
      </template>

      <!-- 员工信息 -->
      <div v-if="employeeInfo" class="employee-info">
        <el-descriptions :column="3" border size="small">
          <el-descriptions-item label="姓名">{{ employeeInfo.name }}</el-descriptions-item>
          <el-descriptions-item label="工号">{{ employeeInfo.employeeNumber }}</el-descriptions-item>
          <el-descriptions-item label="部门">{{ employeeInfo.department }}</el-descriptions-item>
          <el-descriptions-item label="岗位">{{ employeeInfo.position }}</el-descriptions-item>
          <el-descriptions-item label="离职日期">{{ employeeInfo.leaveDate }}</el-descriptions-item>
          <el-descriptions-item label="交接负责人">{{ employeeInfo.handoverTo }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 交接分类选项卡 -->
      <el-tabs v-model="activeCategory" class="handover-tabs">
        <el-tab-pane
          v-for="category in handoverCategories"
          :key="category.key"
          :label="`${category.name} (${getCategoryCount(category.key)})`"
          :name="category.key"
        >
          <div class="category-content">
            <!-- 快速操作 -->
            <div v-if="!readonly" class="quick-actions">
              <el-button size="small" @click="selectAll(category.key)">
                <el-icon><Select /></el-icon>
                全选
              </el-button>
              <el-button size="small" @click="unselectAll(category.key)">
                <el-icon><CloseBold /></el-icon>
                取消全选
              </el-button>
              <el-button size="small" type="primary" @click="markCompleted(category.key)">
                <el-icon><Check /></el-icon>
                标记完成
              </el-button>
            </div>

            <!-- 交接项目列表 -->
            <div class="handover-items">
              <div
                v-for="item in getCategoryItems(category.key)"
                :key="item.id"
                class="handover-item"
                :class="{ completed: item.status === 'COMPLETED' }"
              >
                <div class="item-header">
                  <el-checkbox
                    v-model="item.selected"
                    :disabled="readonly || item.status === 'COMPLETED'"
                    @change="handleItemSelect(item)"
                  >
                    <span class="item-title">{{ item.title }}</span>
                    <el-tag v-if="item.priority === 'HIGH'" type="danger" size="small" class="priority-tag">
                      高优先级
                    </el-tag>
                  </el-checkbox>
                  
                  <div class="item-status">
                    <el-tag :type="getItemStatusType(item.status)" size="small">
                      {{ getItemStatusText(item.status) }}
                    </el-tag>
                  </div>
                </div>

                <div class="item-content">
                  <div class="item-description">{{ item.description }}</div>
                  
                  <div v-if="item.details" class="item-details">
                    <div v-for="(detail, index) in item.details" :key="index" class="detail-item">
                      <el-icon><CircleCheck /></el-icon>
                      {{ detail }}
                    </div>
                  </div>

                  <!-- 交接进度 -->
                  <div v-if="item.status === 'IN_PROGRESS'" class="item-progress">
                    <el-progress :percentage="item.progress || 0" :stroke-width="6"  />
                  </div>

                  <!-- 操作按钮 -->
                  <div v-if="!readonly" class="item-actions">
                    <el-button
                      v-if="item.status === 'PENDING'"
                      size="small"
                      type="primary"
                      @click="startHandover(item)"
                    >
                      开始交接
                    </el-button>
                    
                    <el-button
                      v-if="item.status === 'IN_PROGRESS'"
                      size="small"
                      type="success"
                      @click="completeHandover(item)"
                    >
                      完成交接
                    </el-button>
                    
                    <el-button
                      v-if="item.attachments && item.attachments.length > 0"
                      size="small"
                      @click="viewAttachments(item)"
                    >
                      <el-icon><Document /></el-icon>
                      查看附件
                    </el-button>
                    
                    <el-button
                      size="small"
                      @click="addRemark(item)"
                    >
                      <el-icon><Edit /></el-icon>
                      备注
                    </el-button>
                  </div>

                  <!-- 交接记录 -->
                  <div v-if="item.handoverRecord" class="handover-record">
                    <div class="record-header">
                      <span class="record-label">交接记录:</span>
                      <span class="record-time">{{ item.handoverRecord.time }}</span>
                    </div>
                    <div class="record-content">
                      <div class="record-person">
                        交接人: {{ item.handoverRecord.from }} → {{ item.handoverRecord.to }}
                      </div>
                      <div v-if="item.handoverRecord.remark" class="record-remark">
                        备注: {{ item.handoverRecord.remark }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>

      <!-- 操作按钮 -->
      <div v-if="!readonly" class="action-buttons">
        <el-button @click="handleSave">
          <el-icon><DocumentChecked /></el-icon>
          保存进度
        </el-button>
        <el-button type="primary" @click="handlePrint">
          <el-icon><Printer /></el-icon>
          打印清单
        </el-button>
        <el-button type="success" :disabled="!isAllCompleted" @click="handleSubmit">
          <el-icon><Check /></el-icon>
          提交确认
        </el-button>
      </div>

      <!-- 整体进度 -->
      <div class="overall-progress">
        <div class="progress-header">
          <span>整体交接进度</span>
          <span class="progress-percentage">{{ overallProgress }}%</span>
        </div>
        <el-progress
          :percentage="overallProgress"
          :stroke-width="10"
          :color="progressColors"
         />
        <div class="progress-stats">
          <div class="stat-item">
            <span class="stat-label">待交接:</span>
            <span class="stat-value">{{ pendingCount }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">交接中:</span>
            <span class="stat-value">{{ inProgressCount }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">已完成:</span>
            <span class="stat-value">{{ completedCount }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">最后更新:</span>
            <span class="stat-value">{{ lastUpdateTime || '无' }}</span>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 备注对话框 -->
    <el-dialog
      v-model="remarkDialogVisible"
      title="添加备注"
      width="500px"
    >
      <el-input
        v-model="currentRemark"
        type="textarea"
        :rows="4"
        placeholder="请输入备注信息..."
        />
      <template #footer>
        <el-button @click="remarkDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleRemarkConfirm">确定</el-button>
      </template>
    </el-dialog>

    <!-- 附件查看对话框 -->
    <el-dialog
      v-model="attachmentDialogVisible"
      title="查看附件"
      width="600px"
    >
      <el-table :data="currentAttachments" style="width: 100%">
        <el-table-column prop="name" label="文件名"  />
        <el-table-column prop="size" label="大小" width="100"  />
        <el-table-column prop="uploadTime" label="上传时间" width="150"  />
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-button type="primary" link @click="downloadAttachment(scope.row)">
              下载
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Select,
  CloseBold,
  Check,
  CircleCheck,
  Document,
  Edit,
  DocumentChecked,
  Printer
} from '@element-plus/icons-vue'

// Props定义
interface Props {
  employeeId: string
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false
})

// Emits定义
const emit = defineEmits<{
   
  (e: 'update', data: unknown): void
   
  (e: 'submit', data: unknown): void
}>()

// 响应式数据
const activeCategory = ref('work')
const handoverList = ref<any[]>([])
const employeeInfo = ref<unknown>(null)
const handoverDeadline = ref('')
const lastUpdateTime = ref('')

const remarkDialogVisible = ref(false)
const currentRemark = ref('')
const currentItem = ref<unknown>(null)

const attachmentDialogVisible = ref(false)
const currentAttachments = ref<any[]>([])

// 交接分类
const handoverCategories = [
  { key: 'work', name: 'HrHr工作交接' },
  { key: 'document', name: '文档资料' },
  { key: 'asset', name: '资产物品' },
  { key: 'system', name: '系统权限' },
  { key: 'other', name: '其他事项' }
]

// 进度条颜色
const progressColors = [
  { color: '#f56c6c', percentage: 20 },
  { color: '#e6a23c', percentage: 40 },
  { color: '#5cb87a', percentage: 60 },
  { color: '#1989fa', percentage: 80 },
  { color: '#6f7ad3', percentage: 100 }
]

// 计算属性
const completedCount = computed(() => {
  return handoverList.value.filter(item => item.status === 'COMPLETED').length
})

const pendingCount = computed(() => {
  return handoverList.value.filter(item => item.status === 'PENDING').length
})

const inProgressCount = computed(() => {
  return handoverList.value.filter(item => item.status === 'IN_PROGRESS').length
})

const overallProgress = computed(() => {
  if (handoverList.value.length === 0) return 0
  return Math.round((completedCount.value / handoverList.value.length) * 100)
})

const isAllCompleted = computed(() => {
  return completedCount.value === handoverList.value.length && handoverList.value.length > 0
})

// 获取分类项目数
const getCategoryCount = (category: string) => {
  return handoverList.value.filter(item => item.category === category).length
}

// 获取分类项目
const getCategoryItems = (category: string) => {
  return handoverList.value.filter(item => item.category === category)
}

// 获取状态类型
const getStatusType = () => {
  const percentage = overallProgress.value
  if (percentage === 100) return 'success'
  if (percentage >= 80) return 'primary'
  if (percentage >= 50) return 'warning'
  return 'danger'
}

// 获取项目状态类型
const getItemStatusType = (status: string) => {
  switch (status) {
    case 'COMPLETED': return 'success'
    case 'IN_PROGRESS': return 'primary'
    case 'PENDING': return 'info'
    default: return ''
  }
}

// 获取项目状态文本
const getItemStatusText = (status: string) => {
  switch (status) {
    case 'COMPLETED': return '已完成'
    case 'IN_PROGRESS': return '交接中'
    case 'PENDING': return '待交接'
    default: return status
  }
}

// 初始化数据
const initHandoverData = async () => {
  try {
    // 模拟获取员工信息
    employeeInfo.value = {
      name: '张三',
      employeeNumber: 'EMP001',
      department: '计算机学院',
      position: '高级工程师',
      leaveDate: '2025-06-30',
      handoverTo: '李四'
    }
    
    handoverDeadline.value = '2025-06-25'
    
    // 模拟交接清单数据
    const mockData = [
      // 工作交接
      {
        id: '1',
        category: 'work',
        title: '在研项目交接',
        description: '移交所有在研项目的相关资料和进度',
        priority: 'HIGH',
        status: 'COMPLETED',
        selected: false,
        details: [
          '项目A - 智能校园系统开发',
          '项目B - 教学质量评估平台',
          '项目C - 学生管理系统升级'
        ],
        handoverRecord: {
          time: '2025-06-15 14:30',
          from: '张三',
          to: '李四',
          remark: '所有项目文档已交接完成'
        }
      },
      {
        id: '2',
        category: 'work',
        title: '日常工作职责',
        description: '说明日常工作内容和注意事项',
        priority: 'HIGH',
        status: 'IN_PROGRESS',
        selected: false,
        progress: 60,
        details: [
          '系统日常维护流程',
          '故障处理联系人清单',
          '定期报告撰写要求'
        ]
      },
      {
        id: '3',
        category: 'work',
        title: '未完成任务清单',
        description: '列出所有未完成的任务和后续安排',
        priority: 'MEDIUM',
        status: 'PENDING',
        selected: false
      },
      
      // 文档资料
      {
        id: '4',
        category: 'document',
        title: '技术文档',
        description: '移交所有技术文档和设计资料',
        priority: 'HIGH',
        status: 'COMPLETED',
        selected: false,
        attachments: [
          { name: '系统架构设计.pdf', size: '2.5MB', uploadTime: '2025-06-14' },
          { name: '接口文档.docx', size: '1.2MB', uploadTime: '2025-06-14' }
        ]
      },
      {
        id: '5',
        category: 'document',
        title: '合同协议',
        description: '移交相关合同和协议文件',
        priority: 'HIGH',
        status: 'PENDING',
        selected: false
      },
      
      // 资产物品
      {
        id: '6',
        category: 'asset',
        title: '办公设备',
        description: '归还所有办公设备',
        priority: 'HIGH',
        status: 'COMPLETED',
        selected: false,
        details: [
          '笔记本电脑 - ThinkPad X1 (编号: NB001)',
          '显示器 - Dell 27寸 (编号: MON001)',
          '键盘鼠标套装'
        ]
      },
      {
        id: '7',
        category: 'asset',
        title: '门禁卡和钥匙',
        description: '归还所有门禁卡和办公室钥匙',
        priority: 'HIGH',
        status: 'PENDING',
        selected: false
      },
      
      // 系统权限
      {
        id: '8',
        category: 'system',
        title: '系统账号',
        description: '移交或注销所有系统账号',
        priority: 'HIGH',
        status: 'IN_PROGRESS',
        selected: false,
        progress: 30,
        details: [
          'OA系统管理员账号',
          '代码仓库权限',
          '服务器访问权限',
          '数据库操作权限'
        ]
      },
      {
        id: '9',
        category: 'system',
        title: '邮箱和通讯工具',
        description: '设置邮箱自动回复和移交',
        priority: 'MEDIUM',
        status: 'PENDING',
        selected: false
      },
      
      // 其他事项
      {
        id: '10',
        category: 'other',
        title: '财务结算',
        description: '完成所有财务相关结算',
        priority: 'HIGH',
        status: 'PENDING',
        selected: false,
        details: [
          '报销单据提交',
          '借款结清',
          '工资结算确认'
        ]
      }
    ]
    
    handoverList.value = mockData
  } catch (__error) {
    console.error('获取交接数据失败:', error)
    ElMessage.error('获取交接数据失败')
  }
}

// 全选
const selectAll = (category: string) => {
  handoverList.value.forEach(item => {
    if (item.category === category && item.status !== 'COMPLETED') {
      item.selected = true
    }
  })
}

// 取消全选
const unselectAll = (category: string) => {
  handoverList.value.forEach(item => {
    if (item.category === category) {
      item.selected = false
    }
  })
}

// 标记完成
const markCompleted = async (category: string) => {
  const selectedItems = handoverList.value.filter(
    item => item.category === category && item.selected && item.status !== 'COMPLETED'
  )
  
  if (selectedItems.length === 0) {
    ElMessage.warning('请选择要标记完成的项目')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要标记 ${selectedItems.length} 个项目为已完成吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    selectedItems.forEach(item => {
      item.status = 'COMPLETED'
      item.selected = false
      item.handoverRecord = {
        time: new Date().toLocaleString(),
        from: employeeInfo.value?.name || '',
        to: employeeInfo.value?.handoverTo || '',
        remark: '批量标记完成'
      }
    })
    
    updateLastTime()
    ElMessage.success('标记完成成功')
  } catch (__error) {
    // 用户取消
  }
}

// 处理项目选择
   
const handleItemSelect = (item: unknown) => {
  // 可以在这里添加选择后的逻辑
}

// 开始交接
   
const startHandover = (item: unknown) => {
  item.status = 'IN_PROGRESS'
  item.progress = 0
  updateLastTime()
  ElMessage.success('已开始交接')
}

// 完成交接
   
const completeHandover = async (item: unknown) => {
  try {
    await ElMessageBox.confirm(
      `确定要完成"${item.title}"的交接吗？`,
      '确认完成',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    item.status = 'COMPLETED'
    item.handoverRecord = {
      time: new Date().toLocaleString(),
      from: employeeInfo.value?.name || '',
      to: employeeInfo.value?.handoverTo || '',
      remark: currentItem.value?.remark || ''
    }
    
    updateLastTime()
    ElMessage.success('交接完成')
  } catch (__error) {
    // 用户取消
  }
}

// 查看附件
   
const viewAttachments = (item: unknown) => {
  currentAttachments.value = item.attachments || []
  attachmentDialogVisible.value = true
}

// 下载附件
   
const downloadAttachment = (attachment: unknown) => {
  ElMessage.info(`下载文件: ${attachment.name}`)
}

// 添加备注
   
const addRemark = (item: unknown) => {
  currentItem.value = item
  currentRemark.value = item.remark || ''
  remarkDialogVisible.value = true
}

// 确认备注
const handleRemarkConfirm = () => {
  if (currentItem.value) {
    currentItem.value.remark = currentRemark.value
    updateLastTime()
  }
  remarkDialogVisible.value = false
}

// 保存进度
const handleSave = async () => {
  try {
    const saveData = {
      employeeId: props.employeeId,
      handoverList: handoverList.value,
      lastUpdateTime: new Date().toLocaleString()
    }
    
    // 调用保存API（暂时模拟）
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('保存成功')
    emit('update', saveData)
  } catch (__error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
}

// 打印清单
const handlePrint = () => {
  window.print()
}

// 提交确认
const handleSubmit = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要提交交接确认吗？提交后将无法修改。',
      '提交确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const submitData = {
      employeeId: props.employeeId,
      handoverList: handoverList.value,
      submitTime: new Date().toLocaleString()
    }
    
    // 调用提交API（暂时模拟）
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('提交成功')
    emit('submit', submitData)
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('提交失败:', error)
      ElMessage.error('提交失败')
    }
  }
}

// 更新最后更新时间
const updateLastTime = () => {
  lastUpdateTime.value = new Date().toLocaleString()
}

// 初始化
onMounted(() => {
  initHandoverData()
})

// 监听employeeId变化
watch(() => props.employeeId, () => {
  initHandoverData()
})
</script>

<style scoped>
.resignation-handover {
  width: 100%;
}

.handover-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header .title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.employee-info {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.handover-tabs {
  margin-top: 20px;
}

.category-content {
  padding: 20px 0;
}

.quick-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.handover-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.handover-item {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #ebeef5;
  transition: all 0.3s;
}

.handover-item:hover {
  background: #f0f2f5;
}

.handover-item.completed {
  background: #f0f9ff;
  border-color: #b3e5fc;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.item-title {
  font-size: 14px;
  font-weight: 600;
  margin-right: 8px;
}

.priority-tag {
  margin-left: 8px;
}

.item-content {
  margin-left: 24px;
}

.item-description {
  color: #606266;
  font-size: 13px;
  margin-bottom: 8px;
}

.item-details {
  margin: 12px 0;
  padding: 12px;
  background: white;
  border-radius: 4px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 4px;
  font-size: 13px;
  color: #909399;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.item-progress {
  margin: 12px 0;
}

.item-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.handover-record {
  margin-top: 16px;
  padding: 12px;
  background: #e3f2fd;
  border-radius: 4px;
  border-left: 3px solid #2196f3;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.record-label {
  font-size: 13px;
  font-weight: 600;
  color: #606266;
}

.record-time {
  font-size: 12px;
  color: #909399;
}

.record-content {
  font-size: 13px;
  color: #606266;
}

.record-person {
  margin-bottom: 4px;
}

.record-remark {
  color: #909399;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding: 20px 0;
  border-top: 1px solid #ebeef5;
}

.overall-progress {
  margin-top: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
  color: #606266;
}

.progress-percentage {
  font-size: 18px;
  font-weight: 600;
  color: #409eff;
}

.progress-stats {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  font-size: 13px;
  color: #909399;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #606266;
}

@media print {
  .quick-actions,
  .item-actions,
  .action-buttons {
    display: none !important;
  }
}
</style>