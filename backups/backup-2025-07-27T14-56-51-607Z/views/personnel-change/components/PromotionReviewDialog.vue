<template>
  <el-dialog
    :model-value="visible" @update:model-value="$emit('update:visible', $event)"
    title="职级晋升评审"
    width="700px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
    >
      <!-- 申请信息 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <span class="card-title">晋升申请信息</span>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="申请编号">{{ promotion?.applicationNo }}</el-descriptions-item>
          <el-descriptions-item label="申请日期">{{ promotion?.applicationDate }}</el-descriptions-item>
          <el-descriptions-item label="申请人">{{ promotion?.employeeName }}</el-descriptions-item>
          <el-descriptions-item label="工号">{{ promotion?.employeeNo }}</el-descriptions-item>
          <el-descriptions-item label="所在部门">{{ promotion?.department }}</el-descriptions-item>
          <el-descriptions-item label="现职级年限">{{ promotion?.currentLevelYears }}年</el-descriptions-item>
          <el-descriptions-item label="当前职级">
            <el-tag>{{ promotion?.currentLevel }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="目标职级">
            <el-tag type="success">{{ promotion?.targetLevel }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="晋升类型">{{ getPromotionTypeText(promotion?.promotionType) }}</el-descriptions-item>
          <el-descriptions-item label="当前状态">
            <el-tag :type="getStatusType(promotion?.status)">
              {{ getStatusText(promotion?.status) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 评审操作 -->
      <el-card class="review-card" shadow="never">
        <template #header>
          <span class="card-title">评审操作</span>
        </template>
        
        <!-- 评审维度 -->
        <el-form-item label="评审维度">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="review-dimension">
                <span class="dimension-label">学术成果</span>
                <el-rate v-model="formData.academicScore"  />
                <span class="score-text">{{ formData.academicScore }}分</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="review-dimension">
                <span class="dimension-label">教学质量</span>
                <el-rate v-model="formData.teachingScore"  />
                <span class="score-text">{{ formData.teachingScore }}分</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="review-dimension">
                <span class="dimension-label">师德师风</span>
                <el-rate v-model="formData.ethicsScore"  />
                <span class="score-text">{{ formData.ethicsScore }}分</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 20px">
            <el-col :span="8">
              <div class="review-dimension">
                <span class="dimension-label">科研能力</span>
                <el-rate v-model="formData.researchScore"  />
                <span class="score-text">{{ formData.researchScore }}分</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="review-dimension">
                <span class="dimension-label">社会服务</span>
                <el-rate v-model="formData.serviceScore"  />
                <span class="score-text">{{ formData.serviceScore }}分</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="review-dimension">
                <span class="dimension-label">综合评分</span>
                <div class="total-score">{{ totalScore }}分</div>
              </div>
            </el-col>
          </el-row>
        </el-form-item>

        <el-form-item label="评审结果" prop="result">
          <el-radio-group v-model="formData.result">
            <el-radio value="PASS">
              <span class="radio-label">
                <el-icon class="pass-icon"><CircleCheck /></el-icon>
                通过
              </span>
            </el-radio>
            <el-radio value="FAIL">
              <span class="radio-label">
                <el-icon class="fail-icon"><CircleClose /></el-icon>
                不通过
              </span>
            </el-radio>
            <el-radio value="DEFER">
              <span class="radio-label">
                <el-icon class="defer-icon"><Clock /></el-icon>
                延期再议
              </span>
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 评审意见 -->
        <el-form-item label="评审意见" prop="comment">
          <el-input
            v-model="formData.comment"
            type="textarea"
            :rows="4"
            placeholder="请输入详细的评审意见..."
            maxlength="500"
            show-word-limit
            />
        </el-form-item>

        <!-- 改进建议（不通过或延期时显示） -->
        <el-form-item 
          v-if="formData.result === 'FAIL' || formData.result === 'DEFER'"
          label="改进建议"
          prop="suggestion"
        >
          <el-input
            v-model="formData.suggestion"
            type="textarea"
            :rows="3"
            placeholder="请提供具体的改进建议..."
            maxlength="300"
            show-word-limit
            />
        </el-form-item>

        <!-- 附件上传 -->
        <el-form-item label="评审材料">
          <el-upload
            ref="uploadRef"
            action="#"
            :auto-upload="false"
            :limit="5"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            accept=".pdf,.doc,.docx"
          >
            <el-button type="primary" plain>
              <el-icon><Upload /></el-icon>
              选择文件
            </el-button>
            <template #tip>
              <div class="el-upload__tip">
                可上传评审报告、会议纪要等文件，支持PDF、Word格式
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-card>

      <!-- 历史评审记录 -->
      <el-card v-if="reviewHistory.length > 0" class="history-card" shadow="never">
        <template #header>
          <span class="card-title">历史评审记录</span>
        </template>
        <el-timeline>
          <el-timeline-item
            v-for="(record, index) in reviewHistory"
            :key="index"
            :timestamp="record.time"
            :type="getTimelineType(record.result)"
            placement="top"
          >
            <div class="timeline-content">
              <div class="timeline-header">
                <span class="reviewer-name">{{ record.reviewer }}</span>
                <el-tag size="small" :type="getResultTagType(record.result)">
                  {{ record.result }}
                </el-tag>
                <span class="review-stage">{{ record.stage }}</span>
              </div>
              <div class="timeline-scores">
                <span v-if="record.score" class="score-item">
                  <el-icon><StarFilled /></el-icon>
                  综合评分：{{ record.score }}分
                </span>
              </div>
              <div v-if="record.comment" class="timeline-comment">
                {{ record.comment }}
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">
        提交评审
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'PromotionReviewDialog'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  CircleCheck,
  CircleClose,
  Clock,
  Upload,
  StarFilled
} from '@element-plus/icons-vue'

// Props定义
interface Props {
  visible: boolean
   
  promotion?: unknown
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})

// Emits定义
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}>()

// 响应式数据
const formRef = ref()
const uploadRef = ref()
const loading = ref(false)

// 表单数据
const formData = ref({
  academicScore: 4,
  teachingScore: 4,
  ethicsScore: 5,
  researchScore: 4,
  serviceScore: 3,
  result: 'PASS',
  comment: '',
  suggestion: '',
  attachments: [] as unknown[]
})

// 历史评审记录
const reviewHistory = ref<any[]>([])

// 表单验证规则
const rules = computed(() => ({
  result: [
    { required: true, message: '请选择评审结果', trigger: 'change' }
  ],
  comment: [
    { required: true, message: '请输入评审意见', trigger: 'blur' },
    { min: 10, message: '评审意见至少10个字符', trigger: 'blur' }
  ],
  suggestion: (formData.value.result === 'FAIL' || formData.value.result === 'DEFER') ? [
    { required: true, message: '请提供改进建议', trigger: 'blur' }
  ] : []
}))

// 计算属性
const totalScore = computed(() => {
  const scores = [
    formData.value.academicScore,
    formData.value.teachingScore,
    formData.value.ethicsScore,
    formData.value.researchScore,
    formData.value.serviceScore
  ]
  const avg = scores.reduce((sum, score) => sum + score, 0) / scores.length
  return avg.toFixed(1)
})

// 监听props变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm()
    fetchReviewHistory()
  }
})

// 重置表单
const resetForm = () => {
  formData.value = {
    academicScore: 4,
    teachingScore: 4,
    ethicsScore: 5,
    researchScore: 4,
    serviceScore: 3,
    result: 'PASS',
    comment: '',
    suggestion: '',
    attachments: []
  }
}

// 获取历史评审记录
const fetchReviewHistory = async () => {
  try {
    // 调用API获取历史评审记录（暂时使用模拟数据）
    reviewHistory.value = [
      {
        time: '2025-06-15 10:30:00',
        reviewer: '部门评审委员会',
        stage: '部门评审',
        result: '通过',
        score: 4.2,
        comment: '该申请人学术成果突出，教学评价优秀，符合晋升条件'
      }
    ]
  } catch (__error) {
    console.error('获取历史评审记录失败:', error)
  }
}

// 获取晋升类型文本
const getPromotionTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'NORMAL': '正常晋升',
    'EXCEPTIONAL': '破格晋升',
    'TITLE': '职称晋升',
    'MANAGEMENT': '管理晋升'
  }
  return typeMap[type] || type
}

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'APPROVED':
      return 'success'
    case 'REJECTED':
      return 'danger'
    default:
      return 'warning'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'PENDING_DEPT_REVIEW': '待部门评审',
    'PENDING_COLLEGE_REVIEW': '待学院评审',
    'PENDING_HR_REVIEW': '待人事评审',
    'PENDING_SCHOOL_REVIEW': '待校级评审',
    'APPROVED': '已通过',
    'REJECTED': '未通过',
    'WITHDRAWN': '已撤回'
  }
  return statusMap[status] || status
}

// 获取时间线类型
const getTimelineType = (result: string) => {
  if (result.includes('通过')) return 'success'
  if (result.includes('不通过')) return 'danger'
  return 'primary'
}

// 获取结果标签类型
const getResultTagType = (result: string) => {
  if (result.includes('通过')) return 'success'
  if (result.includes('不通过')) return 'danger'
  if (result.includes('延期')) return 'warning'
  return 'info'
}

// 处理文件上传
   
const handleFileChange = (file: unknown) => {
  formData.value.attachments.push(file)
}

// 处理文件移除
   
const handleFileRemove = (file: unknown) => {
  const index = formData.value.attachments.findIndex(f => f.uid === file.uid)
  if (index > -1) {
    formData.value.attachments.splice(index, 1)
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}

// 提交评审
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    const confirmMessage = formData.value.result === 'PASS' 
      ? '确定要通过该晋升申请吗？' 
      : formData.value.result === 'FAIL'
      ? '确定要驳回该晋升申请吗？'
      : '确定要将该申请延期再议吗？'
    
    await ElMessageBox.confirm(confirmMessage, '提交确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    loading.value = true
    
    // 构建提交数据
    const submitData = {
      promotionId: props.promotion?.id,
      scores: {
        academic: formData.value.academicScore,
        teaching: formData.value.teachingScore,
        ethics: formData.value.ethicsScore,
        research: formData.value.researchScore,
        service: formData.value.serviceScore,
        total: totalScore.value
      },
      result: formData.value.result,
      comment: formData.value.comment,
      suggestion: formData.value.suggestion,
      attachments: formData.value.attachments
    }
    
    // 调用评审API（暂时模拟）
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('评审提交成功')
    emit('success')
    handleClose()
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('评审提交失败:', error)
      ElMessage.error('评审提交失败')
    }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.info-card,
.review-card,
.history-card {
  margin-bottom: 20px;
}

.card-title {
  font-size: 15px;
  font-weight: 600;
  color: #303133;
}

.review-dimension {
  text-align: center;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 8px;
}

.dimension-label {
  display: block;
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.score-text {
  display: block;
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.total-score {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
  margin-top: 8px;
}

.radio-label {
  display: flex;
  align-items: center;
  gap: 4px;
}

.pass-icon {
  color: #67c23a;
  font-size: 18px;
}

.fail-icon {
  color: #f56c6c;
  font-size: 18px;
}

.defer-icon {
  color: #e6a23c;
  font-size: 18px;
}

.timeline-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.timeline-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.reviewer-name {
  font-weight: 600;
  color: #303133;
}

.review-stage {
  font-size: 12px;
  color: #909399;
  margin-left: 4px;
}

.timeline-scores {
  display: flex;
  gap: 16px;
  font-size: 13px;
  color: #606266;
}

.score-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.timeline-comment {
  font-size: 13px;
  color: #909399;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
}

:deep(.el-descriptions__label) {
  width: 100px;
}

:deep(.el-rate) {
  display: inline-block;
}
</style>