<template>
  <el-dialog
    v-model="visible"
    title="批量审批"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-alert
      title="批量审批说明"
      type="info"
      show-icon
      :closable="false"
      class="batch-info"
    >
      您已选择 <strong>{{ applications.length }}</strong> 条申请进行批量审批，
      请仔细核对后进行操作。批量审批仅支持"同意"操作。
    </el-alert>

    <!-- 选中的申请列表 -->
    <el-card class="applications-card" shadow="never">
      <template #header>
        <span class="card-title">待审批申请列表</span>
      </template>
      
      <el-table :data="applications" max-height="300">
        <el-table-column prop="applicationNo" label="申请编号" width="140"  />
        <el-table-column prop="type" label="类型" width="80">
          <template #default="scope">
            <el-tag :type="getTypeTagType(scope.row.type)" size="small">
              {{ getTypeText(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="applicant" label="申请人" width="80"  />
        <el-table-column prop="description" label="申请事项" show-overflow-tooltip  />
      </el-table>
    </el-card>

    <!-- 审批操作 -->
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      class="approval-form"
    >
      <el-form-item label="审批意见" prop="comment">
        <el-input
          v-model="formData.comment"
          type="textarea"
          :rows="4"
          placeholder="请输入统一的审批意见..."
          maxlength="200"
          show-word-limit
          />
      </el-form-item>

      <el-form-item label="快捷意见">
        <el-space wrap>
          <el-tag
            v-for="quick in quickComments"
            :key="quick"
            class="quick-comment"
            @click="formData.comment = quick"
          >
            {{ quick }}
          </el-tag>
        </el-space>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">
        批量同意
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'BatchApprovalDialog'
})
 
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// Props定义
interface Props {
  visible: boolean
   
  applications: unknown[]
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  applications: () => []
})

// Emits定义
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}>()

// 响应式数据
const formRef = ref()
const loading = ref(false)

// 表单数据
const formData = ref({
  comment: ''
})

// 快捷意见
const quickComments = [
  '同意',
  '符合要求，同意',
  '经审核，符合相关规定，同意',
  '材料齐全，同意办理',
  '符合晋升条件，同意'
]

// 表单验证规则
const rules = {
  comment: [
    { required: true, message: '请输入审批意见', trigger: 'blur' },
    { min: 2, message: '审批意见至少2个字符', trigger: 'blur' }
  ]
}

// 获取类型文本
const getTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'ONBOARDING': '入职',
    'RESIGNATION': '离职',
    'TRANSFER': '调动',
    'PROMOTION': '晋升',
    'POSITION': '岗位'
  }
  return typeMap[type] || type
}

// 获取类型标签类型
const getTypeTagType = (type: string) => {
  switch (type) {
    case 'ONBOARDING': return 'success'
    case 'RESIGNATION': return 'danger'
    case 'TRANSFER': return 'primary'
    case 'PROMOTION': return 'warning'
    case 'POSITION': return 'info'
    default: return ''
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
  // 重置表单
  formData.value.comment = ''
}

// 提交批量审批
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    await ElMessageBox.confirm(
      `确定要批量同意这 ${props.applications.length} 条申请吗？`,
      '批量审批确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    loading.value = true
    
    // 构建批量审批数据
    const submitData = {
      applicationIds: props.applications.map(app => app.id),
      result: 'APPROVE',
      comment: formData.value.comment
    }
    
    // 调用批量审批API（暂时模拟）
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    ElMessage.success(`成功审批 ${props.applications.length} 条申请`)
    emit('success')
    handleClose()
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('批量审批失败:', error)
      ElMessage.error('批量审批失败')
    }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.batch-info {
  margin-bottom: 20px;
}

.applications-card {
  margin-bottom: 20px;
}

.card-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.approval-form {
  padding-top: 10px;
}

.quick-comment {
  cursor: pointer;
  transition: all 0.3s;
}

.quick-comment:hover {
  color: #409eff;
  border-color: #409eff;
}
</style>