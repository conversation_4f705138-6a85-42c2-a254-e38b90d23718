<template>
  <el-dialog
    v-model="visible"
    title="交接清单管理"
    width="85%"
    :close-on-click-modal="false"
    destroy-on-close
    @close="handleClose"
  >
    <ResignationHandover
      :employee-id="employee?.employeeNumber || ''"
      :readonly="false"
      @update="handleUpdate"
      @submit="handleSubmit"
    />
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'HandoverDialog'
})
 
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import ResignationHandover from './ResignationHandover.vue'

// Props定义
interface Props {
  visible: boolean
   
  employee?: unknown
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})

// Emits定义
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}>()

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}

// 处理更新
   
const handleUpdate = (data: unknown) => {
  console.log('交接清单更新:', data)
}

// 处理提交
   
const handleSubmit = (data: unknown) => {
  console.log('交接清单提交:', data)
  ElMessage.success('交接清单提交成功')
  emit('success')
  handleClose()
}
</script>

<style scoped>
:deep(.el-dialog__body) {
  padding: 10px 20px;
}
</style>