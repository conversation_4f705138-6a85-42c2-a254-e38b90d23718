<template>
  <el-dialog
    :model-value="visible" @update:model-value="$emit('update:visible', $event)"
    title="部门调动审批"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
    >
      <!-- 申请信息 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <span class="card-title">调动申请信息</span>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="申请编号">{{ transfer?.applicationNo }}</el-descriptions-item>
          <el-descriptions-item label="申请日期">{{ transfer?.applicationDate }}</el-descriptions-item>
          <el-descriptions-item label="申请人">{{ transfer?.employeeName }}</el-descriptions-item>
          <el-descriptions-item label="工号">{{ transfer?.employeeNo }}</el-descriptions-item>
          <el-descriptions-item label="原部门">{{ transfer?.fromDepartment }}</el-descriptions-item>
          <el-descriptions-item label="目标部门">{{ transfer?.toDepartment }}</el-descriptions-item>
          <el-descriptions-item label="调动原因" :span="2">{{ transfer?.transferReason }}</el-descriptions-item>
          <el-descriptions-item label="生效日期">{{ transfer?.effectiveDate }}</el-descriptions-item>
          <el-descriptions-item label="当前状态">
            <el-tag :type="getStatusType(transfer?.status)">
              {{ getStatusText(transfer?.status) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 审批操作 -->
      <el-card class="approval-card" shadow="never">
        <template #header>
          <span class="card-title">审批操作</span>
        </template>
        
        <el-form-item label="审批结果" prop="action">
          <el-radio-group v-model="formData.action">
            <el-radio value="APPROVE">
              <span class="radio-label">
                <el-icon class="approve-icon"><CircleCheck /></el-icon>
                同意
              </span>
            </el-radio>
            <el-radio value="REJECT">
              <span class="radio-label">
                <el-icon class="reject-icon"><CircleClose /></el-icon>
                驳回
              </span>
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 审批意见 -->
        <el-form-item label="审批意见" prop="comment">
          <el-input
            v-model="formData.comment"
            type="textarea"
            :rows="4"
            placeholder="请输入审批意见..."
            maxlength="200"
            show-word-limit
            />
        </el-form-item>

        <!-- 特殊要求（目标部门审批时） -->
        <el-form-item v-if="isTargetDeptApproval" label="岗位安排">
          <el-input
            v-model="formData.positionArrangement"
            placeholder="请说明具体的岗位安排"
            />
        </el-form-item>

        <!-- 附件上传 -->
        <el-form-item label="相关附件">
          <el-upload
            ref="uploadRef"
            action="#"
            :auto-upload="false"
            :limit="3"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            accept=".pdf,.doc,.docx,.jpg,.png"
          >
            <el-button type="primary" plain>
              <el-icon><Upload /></el-icon>
              选择文件
            </el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持上传 PDF、Word、图片格式，单个文件不超过 10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-card>

      <!-- 审批历史 -->
      <el-card v-if="approvalHistory.length > 0" class="history-card" shadow="never">
        <template #header>
          <span class="card-title">审批历史</span>
        </template>
        <el-timeline>
          <el-timeline-item
            v-for="(record, index) in approvalHistory"
            :key="index"
            :timestamp="record.time"
            :type="getTimelineType(record.action)"
            placement="top"
          >
            <div class="timeline-content">
              <div class="timeline-header">
                <span class="approver-name">{{ record.approver }}</span>
                <el-tag size="small" :type="getActionTagType(record.action)">
                  {{ record.action }}
                </el-tag>
                <span class="approval-role">{{ record.role }}</span>
              </div>
              <div v-if="record.comment" class="timeline-comment">
                {{ record.comment }}
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">
        提交审批
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'TransferApprovalDialog'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  CircleCheck,
  CircleClose,
  Upload
} from '@element-plus/icons-vue'

// Props定义
interface Props {
  visible: boolean
   
  transfer?: unknown
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})

// Emits定义
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}>()

// 响应式数据
const formRef = ref()
const uploadRef = ref()
const loading = ref(false)

// 表单数据
const formData = ref({
  action: 'APPROVE',
  comment: '',
  positionArrangement: '',
  attachments: [] as unknown[]
})

// 审批历史
const approvalHistory = ref<any[]>([])

// 表单验证规则
const rules = computed(() => ({
  action: [
    { required: true, message: '请选择审批结果', trigger: 'change' }
  ],
  comment: [
    { required: true, message: '请输入审批意见', trigger: 'blur' },
    { min: 5, message: '审批意见至少5个字符', trigger: 'blur' }
  ],
  positionArrangement: isTargetDeptApproval.value ? [
    { required: true, message: '请说明岗位安排', trigger: 'blur' }
  ] : []
}))

// 计算属性
const isTargetDeptApproval = computed(() => {
  // 判断是否为目标部门审批
  return props.transfer?.status === 'PENDING_TO_DEPT'
})

// 监听props变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm()
    fetchApprovalHistory()
  }
})

// 重置表单
const resetForm = () => {
  formData.value = {
    action: 'APPROVE',
    comment: '',
    positionArrangement: '',
    attachments: []
  }
}

// 获取审批历史
const fetchApprovalHistory = async () => {
  try {
    // 调用API获取审批历史（暂时使用模拟数据）
    approvalHistory.value = [
      {
        time: '2025-06-15 10:30:00',
        approver: '张三',
        role: '申请人',
        action: '提交申请',
        comment: '申请从计算机学院调动到人工智能学院'
      },
      {
        time: '2025-06-15 14:20:00',
        approver: '李四',
        role: '部门主管',
        action: '审批通过',
        comment: '同意该员工的调动申请'
      }
    ]
  } catch (__error) {
    console.error('获取审批历史失败:', error)
  }
}

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'APPROVED':
      return 'success'
    case 'REJECTED':
      return 'danger'
    default:
      return 'warning'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'PENDING_DEPT_MANAGER': '待部门主管审批',
    'PENDING_FROM_DEPT': '待原部门审批',
    'PENDING_TO_DEPT': '待目标部门审批',
    'PENDING_HR': '待人事处审批',
    'APPROVED': '已通过',
    'REJECTED': '已驳回',
    'CANCELLED': '已撤销'
  }
  return statusMap[status] || status
}

// 获取时间线类型
const getTimelineType = (action: string) => {
  if (action.includes('通过')) return 'success'
  if (action.includes('驳回')) return 'danger'
  return 'primary'
}

// 获取操作标签类型
const getActionTagType = (action: string) => {
  if (action.includes('通过')) return 'success'
  if (action.includes('驳回')) return 'danger'
  if (action.includes('提交')) return 'primary'
  return 'info'
}

// 处理文件上传
   
const handleFileChange = (file: unknown) => {
  formData.value.attachments.push(file)
}

// 处理文件移除
   
const handleFileRemove = (file: unknown) => {
  const index = formData.value.attachments.findIndex(f => f.uid === file.uid)
  if (index > -1) {
    formData.value.attachments.splice(index, 1)
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}

// 提交审批
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    const confirmMessage = formData.value.action === 'APPROVE' 
      ? '确定要同意该调动申请吗？' 
      : '确定要驳回该调动申请吗？'
    
    await ElMessageBox.confirm(confirmMessage, '提交确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    loading.value = true
    
    // 构建提交数据
    const submitData = {
      transferId: props.transfer?.id,
      action: formData.value.action,
      comment: formData.value.comment,
      positionArrangement: formData.value.positionArrangement,
      attachments: formData.value.attachments
    }
    
    // 调用审批API（暂时模拟）
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('审批提交成功')
    emit('success')
    handleClose()
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('审批提交失败:', error)
      ElMessage.error('审批提交失败')
    }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.info-card,
.approval-card,
.history-card {
  margin-bottom: 20px;
}

.card-title {
  font-size: 15px;
  font-weight: 600;
  color: #303133;
}

.radio-label {
  display: flex;
  align-items: center;
  gap: 4px;
}

.approve-icon {
  color: #67c23a;
  font-size: 18px;
}

.reject-icon {
  color: #f56c6c;
  font-size: 18px;
}

.timeline-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.timeline-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.approver-name {
  font-weight: 600;
  color: #303133;
}

.approval-role {
  font-size: 12px;
  color: #909399;
  margin-left: 4px;
}

.timeline-comment {
  font-size: 13px;
  color: #909399;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
}

:deep(.el-descriptions__label) {
  width: 100px;
}
</style>