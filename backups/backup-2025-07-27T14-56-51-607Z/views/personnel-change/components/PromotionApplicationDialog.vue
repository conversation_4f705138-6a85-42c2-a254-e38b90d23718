<template>
  <el-dialog
    :model-value="visible" @update:model-value="$emit('update:visible', $event)"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    destroy-on-close
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      :disabled="mode === 'view'"
      label-width="120px"
    >
      <!-- 基本信息 -->
      <el-divider content-position="left">基本信息</el-divider>
      
      <el-form-item label="申请人" prop="employeeId">
        <el-select
          v-model="formData.employeeId"
          placeholder="请选择申请人"
          filterable
          remote
          :remote-method="searchEmployee"
          :loading="searchLoading"
          style="width: 100%"
          @change="handleEmployeeChange"
        >
          <el-option
            v-for="emp in employeeOptions"
            :key="emp.id"
            :label="`${emp.name} (${emp.employeeNo})`"
            :value="emp.id"
          >
            <span style="float: left">{{ emp.name }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">
              {{ emp.employeeNo }} - {{ emp.department }}
            </span>
          </el-option>
        </el-select>
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所在部门">
            <el-input v-model="formData.department" readonly   />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="当前职级">
            <el-input v-model="formData.currentLevel" readonly   />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="入职日期">
            <el-input v-model="formData.joinDate" readonly   />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="现职级年限">
            <el-input v-model="formData.currentLevelYears" readonly>
              <template #append>年</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 晋升信息 -->
      <el-divider content-position="left">晋升信息</el-divider>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="目标职级" prop="targetLevel">
            <el-select v-model="formData.targetLevel" placeholder="请选择目标职级" style="width: 100%">
              <el-option label="助教" value="助教"  />
              <el-option label="讲师" value="讲师"  />
              <el-option label="副教授" value="副教授"  />
              <el-option label="教授" value="教授"  />
              <el-option label="其他" value="其他"  />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="晋升类型" prop="promotionType">
            <el-select v-model="formData.promotionType" placeholder="请选择晋升类型" style="width: 100%">
              <el-option label="正常晋升" value="NORMAL"  />
              <el-option label="破格晋升" value="EXCEPTIONAL"  />
              <el-option label="职称晋升" value="TITLE"  />
              <el-option label="管理晋升" value="MANAGEMENT"  />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 评审条件 -->
      <el-divider content-position="left">评审条件</el-divider>

      <el-form-item label="学术成果">
        <el-checkbox-group v-model="formData.academicAchievements">
          <el-checkbox label="SCI">发表SCI论文</el-checkbox>
          <el-checkbox label="EI">发表EI论文</el-checkbox>
          <el-checkbox label="CORE">发表核心期刊论文</el-checkbox>
          <el-checkbox label="PATENT">获得发明专利</el-checkbox>
          <el-checkbox label="PROJECT">主持科研项目</el-checkbox>
          <el-checkbox label="AWARD">获得科研奖项</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item label="成果说明" prop="achievementDescription">
        <el-input
          v-model="formData.achievementDescription"
          type="textarea"
          :rows="3"
          placeholder="请详细说明学术成果情况，包括论文数量、项目级别、奖项等级等"
          maxlength="500"
          show-word-limit
          />
      </el-form-item>

      <el-form-item label="教学评价" prop="teachingEvaluation">
        <el-rate
          v-model="formData.teachingEvaluation"
          :texts="['不合格', '合格', '良好', '优秀', '卓越']"
          show-text
         />
      </el-form-item>

      <el-form-item label="申请理由" prop="applicationReason">
        <el-input
          v-model="formData.applicationReason"
          type="textarea"
          :rows="4"
          placeholder="请详细说明晋升理由"
          maxlength="500"
          show-word-limit
          />
      </el-form-item>

      <!-- 附件材料 -->
      <el-divider content-position="left">附件材料</el-divider>

      <el-form-item label="证明材料">
        <el-upload
          ref="uploadRef"
          v-model:file-list="fileList"
          action="#"
          :auto-upload="false"
          :limit="10"
          accept=".pdf,.doc,.docx,.jpg,.png"
          list-type="picture-card"
        >
          <el-icon><Plus /></el-icon>
          <template #file="{ file }">
            <div>
              <img v-if="isImage(file.name)" class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
              <el-icon v-else class="file-icon"><Document /></el-icon>
              <span class="el-upload-list__item-actions">
                <span
                  class="el-upload-list__item-preview"
                  @click="handlePreview(file)"
                >
                  <el-icon><ZoomIn /></el-icon>
                </span>
                <span
                  class="el-upload-list__item-delete"
                  @click="handleRemove(file)"
                >
                  <el-icon><Delete /></el-icon>
                </span>
              </span>
            </div>
          </template>
          <template #tip>
            <div class="el-upload__tip">
              请上传相关证明材料，如论文、证书、奖状等，支持PDF、Word、图片格式
            </div>
          </template>
        </el-upload>
      </el-form-item>

      <!-- 评审流程 -->
      <el-divider v-if="mode === 'view'" content-position="left">评审流程</el-divider>
      
      <el-form-item v-if="mode === 'view' && formData.reviewRecords" label="评审记录">
        <el-timeline>
          <el-timeline-item
            v-for="(record, index) in formData.reviewRecords"
            :key="index"
            :timestamp="record.time"
            :type="getTimelineType(record.result)"
            placement="top"
          >
            <div class="review-content">
              <div class="review-header">
                <span class="reviewer">{{ record.reviewer }}</span>
                <el-tag size="small" :type="getReviewTagType(record.result)">
                  {{ record.result }}
                </el-tag>
                <span class="review-stage">{{ record.stage }}</span>
              </div>
              <div v-if="record.comment" class="review-comment">
                评审意见：{{ record.comment }}
              </div>
              <div v-if="record.score" class="review-score">
                评分：{{ record.score }}分
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">{{ mode === 'view' ? '关闭' : '取消' }}</el-button>
      <el-button v-if="mode !== 'view'" type="primary" @click="handleSubmit">
        提交申请
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Document, ZoomIn, Delete } from '@element-plus/icons-vue'

// Props定义
interface Props {
  visible: boolean
   
  promotion?: unknown
  mode: 'view' | 'add' | 'edit'
   
  departments: unknown[]
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  mode: 'add',
  departments: () => []
})

// Emits定义
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}>()

// 响应式数据
const formRef = ref()
const uploadRef = ref()
const searchLoading = ref(false)
const employeeOptions = ref<any[]>([])
const fileList = ref<any[]>([])

// 表单数据
const formData = ref({
  employeeId: '',
  employeeName: '',
  employeeNo: '',
  department: '',
  currentLevel: '',
  joinDate: '',
  currentLevelYears: '',
  targetLevel: '',
  promotionType: 'NORMAL',
  academicAchievements: [],
  achievementDescription: '',
  teachingEvaluation: 4,
  applicationReason: '',
  attachments: [],
  reviewRecords: []
})

// 表单验证规则
const rules = {
  employeeId: [
    { required: true, message: '请选择申请人', trigger: 'change' }
  ],
  targetLevel: [
    { required: true, message: '请选择目标职级', trigger: 'change' }
  ],
  promotionType: [
    { required: true, message: '请选择晋升类型', trigger: 'change' }
  ],
  achievementDescription: [
    { required: true, message: '请填写成果说明', trigger: 'blur' },
    { min: 20, message: '成果说明至少20个字符', trigger: 'blur' }
  ],
  teachingEvaluation: [
    { required: true, message: '请进行教学评价', trigger: 'change' }
  ],
  applicationReason: [
    { required: true, message: '请填写申请理由', trigger: 'blur' },
    { min: 20, message: '申请理由至少20个字符', trigger: 'blur' }
  ]
}

// 计算属性
const dialogTitle = computed(() => {
  switch (props.mode) {
    case 'add':
      return '申请职级晋升'
    case 'edit':
      return '编辑晋升申请'
    case 'view':
      return '晋升申请详情'
    default:
      return ''
  }
})

// 监听props变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    if (props.promotion) {
      formData.value = { ...props.promotion }
    } else {
      resetForm()
    }
  }
})

// 重置表单
const resetForm = () => {
  formData.value = {
    employeeId: '',
    employeeName: '',
    employeeNo: '',
    department: '',
    currentLevel: '',
    joinDate: '',
    currentLevelYears: '',
    targetLevel: '',
    promotionType: 'NORMAL',
    academicAchievements: [],
    achievementDescription: '',
    teachingEvaluation: 4,
    applicationReason: '',
    attachments: [],
    reviewRecords: []
  }
  fileList.value = []
}

// 搜索员工
const searchEmployee = async (query: string) => {
  if (query) {
    searchLoading.value = true
    try {
      // 模拟搜索员工API
      await new Promise(resolve => setTimeout(resolve, 500))
      employeeOptions.value = [
        { 
          id: '1', 
          name: 'HrHr张三', 
          employeeNo: 'EMP001', 
          department: '计算机学院', 
          currentLevel: '讲师',
          joinDate: '2018-09-01',
          currentLevelYears: '5'
        },
        { 
          id: '2', 
          name: '李四', 
          employeeNo: 'EMP002', 
          department: '机械工程学院', 
          currentLevel: '副教授',
          joinDate: '2015-09-01',
          currentLevelYears: '3'
        }
      ].filter(emp => 
        emp.name.includes(query) || emp.employeeNo.includes(query)
      )
    } catch (__error) {
      console.error('搜索员工失败:', error)
    } finally {
      searchLoading.value = false
    }
  } else {
    employeeOptions.value = []
  }
}

// 员工选择变化
const handleEmployeeChange = (employeeId: string) => {
  const employee = employeeOptions.value.find(emp => emp.id === employeeId)
  if (employee) {
    formData.value.employeeName = employee.name
    formData.value.employeeNo = employee.employeeNo
    formData.value.department = employee.department
    formData.value.currentLevel = employee.currentLevel
    formData.value.joinDate = employee.joinDate
    formData.value.currentLevelYears = employee.currentLevelYears
  }
}

// 判断是否为图片
const isImage = (fileName: string) => {
  const ext = fileName.split('.').pop()?.toLowerCase()
  return ['jpg', 'jpeg', 'png', 'gif'].includes(ext || '')
}

// 预览文件
   
const handlePreview = (file: unknown) => {
  console.log('预览文件:', file)
}

// 移除文件
   
const handleRemove = (file: unknown) => {
  const index = fileList.value.findIndex(f => f.uid === file.uid)
  if (index > -1) {
    fileList.value.splice(index, 1)
  }
}

// 获取时间线类型
const getTimelineType = (result: string) => {
  if (result.includes('通过')) return 'success'
  if (result.includes('不通过')) return 'danger'
  return 'primary'
}

// 获取评审标签类型
const getReviewTagType = (result: string) => {
  if (result.includes('通过')) return 'success'
  if (result.includes('不通过')) return 'danger'
  if (result.includes('待评审')) return 'warning'
  return 'info'
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    // 构建提交数据
    const submitData = {
      ...formData.value,
      attachments: fileList.value.map(file => ({
        name: file.name,
        url: file.url || file.response?.url
      }))
    }
    
    // 调用提交API（暂时模拟）
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('晋升申请提交成功')
    emit('success')
    handleClose()
  } catch (__error) {
    console.error('提交失败:', error)
  }
}
</script>

<style scoped>
.el-divider {
  margin: 24px 0 16px;
}

.review-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.review-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.reviewer {
  font-weight: 600;
  color: #303133;
}

.review-stage {
  font-size: 12px;
  color: #909399;
  margin-left: 4px;
}

.review-comment,
.review-score {
  font-size: 13px;
  color: #606266;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
}

.file-icon {
  font-size: 48px;
  color: #909399;
}

:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 100px;
  height: 100px;
}

:deep(.el-upload--picture-card) {
  width: 100px;
  height: 100px;
}
</style>