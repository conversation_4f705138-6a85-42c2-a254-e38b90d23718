<template>
  <el-dialog
    :model-value="visible" @update:model-value="$emit('update:visible', $event)"
    :title="`${getTypeText(application?.type)}审批`"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <!-- 申请详情 -->
    <el-card class="detail-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="card-title">申请详情</span>
          <el-tag :type="getUrgencyType(application?.urgency)" size="small">
            {{ getUrgencyText(application?.urgency) }}
          </el-tag>
        </div>
      </template>
      
      <!-- 基本信息 -->
      <el-descriptions :column="2" border class="basic-info">
        <el-descriptions-item label="申请编号">{{ application?.applicationNo }}</el-descriptions-item>
        <el-descriptions-item label="申请类型">
          <el-tag :type="getTypeTagType(application?.type)" size="small">
            {{ getTypeText(application?.type) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="申请人">{{ application?.applicant }}</el-descriptions-item>
        <el-descriptions-item label="所属部门">{{ application?.department }}</el-descriptions-item>
        <el-descriptions-item label="申请时间">{{ application?.applicationDate }}</el-descriptions-item>
        <el-descriptions-item label="当前环节">{{ application?.currentStage }}</el-descriptions-item>
        <el-descriptions-item label="申请事项" :span="2">
          {{ application?.description }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 根据不同类型显示不同的详情 -->
      <component
        v-if="detailComponent"
        :is="detailComponent"
        :application="application"
        class="type-detail"
      />
    </el-card>

    <!-- 审批流程 -->
    <el-card class="process-card" shadow="never">
      <template #header>
        <span class="card-title">审批流程</span>
      </template>
      
      <el-timeline>
        <el-timeline-item
          v-for="(node, index) in approvalProcess"
          :key="index"
          :timestamp="node.time"
          :type="getNodeType(node.status)"
          :hollow="node.status === 'PENDING'"
        >
          <div class="process-node">
            <div class="node-header">
              <span class="node-stage">{{ node.stage }}</span>
              <el-tag size="small" :type="getNodeTagType(node.status)">
                {{ getNodeStatusText(node.status) }}
              </el-tag>
            </div>
            <div v-if="node.approver" class="node-info">
              <span class="approver">审批人：{{ node.approver }}</span>
              <span v-if="node.result" class="result">
                结果：{{ node.result }}
              </span>
            </div>
            <div v-if="node.comment" class="node-comment">
              {{ node.comment }}
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </el-card>

    <!-- 审批操作 -->
    <el-card v-if="canApprove" class="approval-card" shadow="never">
      <template #header>
        <span class="card-title">审批操作</span>
      </template>
      
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="审批结果" prop="result">
          <el-radio-group v-model="formData.result">
            <el-radio value="APPROVE">
              <span class="radio-label">
                <el-icon class="approve-icon"><CircleCheck /></el-icon>
                同意
              </span>
            </el-radio>
            <el-radio value="REJECT">
              <span class="radio-label">
                <el-icon class="reject-icon"><CircleClose /></el-icon>
                驳回
              </span>
            </el-radio>
            <el-radio value="RETURN">
              <span class="radio-label">
                <el-icon class="return-icon"><RefreshLeft /></el-icon>
                退回修改
              </span>
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="审批意见" prop="comment">
          <el-input
            v-model="formData.comment"
            type="textarea"
            :rows="4"
            placeholder="请输入审批意见..."
            maxlength="200"
            show-word-limit
            />
        </el-form-item>

        <!-- 转审功能 -->
        <el-form-item label="转审他人">
          <el-switch v-model="formData.needTransfer"  />
        </el-form-item>

        <el-form-item v-if="formData.needTransfer" label="转审对象" prop="transferTo">
          <el-select
            v-model="formData.transferTo"
            placeholder="请选择转审对象"
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="user in userOptions"
              :key="user.id"
              :label="`${user.name} (${user.position})`"
              :value="user.id"
             />
          </el-select>
        </el-form-item>

        <!-- 附件上传 -->
        <el-form-item label="相关附件">
          <el-upload
            ref="uploadRef"
            action="#"
            :auto-upload="false"
            :limit="3"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            accept=".pdf,.doc,.docx,.jpg,.png"
          >
            <el-button type="primary" plain>
              <el-icon><Upload /></el-icon>
              选择文件
            </el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持上传 PDF、Word、图片格式，单个文件不超过 10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
    </el-card>

    <template #footer>
      <el-button @click="handleClose">{{ canApprove ? '取消' : '关闭' }}</el-button>
      <el-button v-if="canApprove" type="primary" :loading="loading" @click="handleSubmit">
        提交审批
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, computed, watch, shallowRef } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  CircleCheck,
  CircleClose,
  RefreshLeft,
  Upload
} from '@element-plus/icons-vue'

// 动态导入详情组件
const OnboardingDetail = shallowRef()
const ResignationDetail = shallowRef()
const TransferDetail = shallowRef()
const PromotionDetail = shallowRef()
const PositionDetail = shallowRef()

// Props定义
interface Props {
  visible: boolean
   
  application?: unknown
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})

// Emits定义
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}>()

// 响应式数据
const formRef = ref()
const uploadRef = ref()
const loading = ref(false)

// 表单数据
const formData = ref({
  result: 'APPROVE',
  comment: '',
  needTransfer: false,
  transferTo: '',
  attachments: [] as unknown[]
})

// 审批流程
const approvalProcess = ref<any[]>([])

// 用户选项
const userOptions = ref([
  { id: '1', name: 'HrHr王主任', position: '部门主任' },
  { id: '2', name: '李处长', position: '人事处长' },
  { id: '3', name: '张院长', position: '学院院长' }
])

// 表单验证规则
const rules = computed(() => ({
  result: [
    { required: true, message: '请选择审批结果', trigger: 'change' }
  ],
  comment: [
    { required: true, message: '请输入审批意见', trigger: 'blur' },
    { min: 5, message: '审批意见至少5个字符', trigger: 'blur' }
  ],
  transferTo: formData.value.needTransfer ? [
    { required: true, message: '请选择转审对象', trigger: 'change' }
  ] : []
}))

// 计算属性
const canApprove = computed(() => {
  return props.application?.status === 'PENDING_MY_APPROVAL'
})

const detailComponent = computed(() => {
  switch (props.application?.type) {
    case 'ONBOARDING': return OnboardingDetail.value
    case 'RESIGNATION': return ResignationDetail.value
    case 'TRANSFER': return TransferDetail.value
    case 'PROMOTION': return PromotionDetail.value
    case 'POSITION': return PositionDetail.value
    default: return null
  }
})

// 监听props变化
watch(() => props.visible, async (newVal) => {
  if (newVal) {
    resetForm()
    fetchApprovalProcess()
    // 动态加载对应的详情组件
    await loadDetailComponent()
  }
})

// 重置表单
const resetForm = () => {
  formData.value = {
    result: 'APPROVE',
    comment: '',
    needTransfer: false,
    transferTo: '',
    attachments: []
  }
}

// 获取审批流程
const fetchApprovalProcess = async () => {
  try {
    // 调用API获取审批流程（暂时使用模拟数据）
    approvalProcess.value = [
      {
        stage: '申请人提交',
        time: '2025-06-15 10:30:00',
        status: 'COMPLETED',
        approver: props.application?.applicant,
        result: '提交申请',
        comment: '申请入职'
      },
      {
        stage: '部门审批',
        time: '2025-06-15 14:20:00',
        status: 'COMPLETED',
        approver: '李主任',
        result: '同意',
        comment: '符合部门用人需求'
      },
      {
        stage: '人事审批',
        time: '',
        status: 'PENDING',
        approver: '当前用户',
        result: '',
        comment: ''
      },
      {
        stage: '校级审批',
        time: '',
        status: 'WAITING',
        approver: '',
        result: '',
        comment: ''
      }
    ]
  } catch (__error) {
    console.error('获取审批流程失败:', error)
  }
}

// 动态加载详情组件
const loadDetailComponent = async () => {
  // 这里可以根据需要动态加载不同类型的详情组件
  // 由于组件较简单，暂时不实现
}

// 获取类型文本
const getTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'ONBOARDING': '入职申请',
    'RESIGNATION': '离职申请',
    'TRANSFER': '调动申请',
    'PROMOTION': '晋升申请',
    'POSITION': '岗位调整'
  }
  return typeMap[type] || type
}

// 获取类型标签类型
const getTypeTagType = (type: string) => {
  switch (type) {
    case 'ONBOARDING': return 'success'
    case 'RESIGNATION': return 'danger'
    case 'TRANSFER': return 'primary'
    case 'PROMOTION': return 'warning'
    case 'POSITION': return 'info'
    default: return ''
  }
}

// 获取紧急程度类型
const getUrgencyType = (urgency: string) => {
  switch (urgency) {
    case 'HIGH': return 'danger'
    case 'NORMAL': return 'warning'
    case 'LOW': return 'info'
    default: return ''
  }
}

// 获取紧急程度文本
const getUrgencyText = (urgency: string) => {
  const urgencyMap: Record<string, string> = {
    'HIGH': '紧急',
    'NORMAL': '普通',
    'LOW': '不急'
  }
  return urgencyMap[urgency] || urgency
}

// 获取节点类型
const getNodeType = (status: string) => {
  switch (status) {
    case 'COMPLETED': return 'success'
    case 'PENDING': return 'primary'
    case 'REJECTED': return 'danger'
    default: return 'info'
  }
}

// 获取节点标签类型
const getNodeTagType = (status: string) => {
  switch (status) {
    case 'COMPLETED': return 'success'
    case 'PENDING': return 'warning'
    case 'WAITING': return 'info'
    case 'REJECTED': return 'danger'
    default: return ''
  }
}

// 获取节点状态文本
const getNodeStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'COMPLETED': '已完成',
    'PENDING': '待处理',
    'WAITING': '等待中',
    'REJECTED': '已驳回'
  }
  return statusMap[status] || status
}

// 处理文件上传
   
const handleFileChange = (file: unknown) => {
  formData.value.attachments.push(file)
}

// 处理文件移除
   
const handleFileRemove = (file: unknown) => {
  const index = formData.value.attachments.findIndex(f => f.uid === file.uid)
  if (index > -1) {
    formData.value.attachments.splice(index, 1)
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}

// 提交审批
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    const actionText = formData.value.result === 'APPROVE' ? '同意' : 
                      formData.value.result === 'REJECT' ? '驳回' : '退回'
    
    await ElMessageBox.confirm(
      `确定要${actionText}该申请吗？`,
      '提交确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    loading.value = true
    
    // 构建提交数据
    const submitData = {
      applicationId: props.application?.id,
      result: formData.value.result,
      comment: formData.value.comment,
      needTransfer: formData.value.needTransfer,
      transferTo: formData.value.transferTo,
      attachments: formData.value.attachments
    }
    
    // 调用审批API（暂时模拟）
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('审批提交成功')
    emit('success')
    handleClose()
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('审批提交失败:', error)
      ElMessage.error('审批提交失败')
    }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.detail-card,
.process-card,
.approval-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 15px;
  font-weight: 600;
  color: #303133;
}

.basic-info {
  margin-bottom: 20px;
}

.type-detail {
  margin-top: 20px;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 4px;
}

.process-node {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.node-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.node-stage {
  font-weight: 600;
  color: #303133;
}

.node-info {
  display: flex;
  gap: 16px;
  font-size: 13px;
  color: #606266;
}

.node-comment {
  font-size: 13px;
  color: #909399;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
}

.radio-label {
  display: flex;
  align-items: center;
  gap: 4px;
}

.approve-icon {
  color: #67c23a;
  font-size: 18px;
}

.reject-icon {
  color: #f56c6c;
  font-size: 18px;
}

.return-icon {
  color: #e6a23c;
  font-size: 18px;
}

:deep(.el-descriptions__label) {
  width: 100px;
}
</style>