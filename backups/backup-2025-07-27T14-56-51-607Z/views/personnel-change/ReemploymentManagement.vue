<template>
  <div class="reemployment-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>退休返聘管理</h2>
      <p>管理离退休人员返聘申请、审批和记录管理</p>
    </div>

    <!-- 搜索和操作区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-form">
        <el-row :gutter="20">
          <el-col :span="5">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索姓名、工号"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.status" placeholder="申请状态" clearable>
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.reemploymentType" placeholder="返聘类型" clearable>
              <el-option label="全职返聘" value="FULL_TIME"  />
              <el-option label="兼职返聘" value="PART_TIME"  />
              <el-option label="项目返聘" value="PROJECT_BASED"  />
              <el-option label="顾问返聘" value="CONSULTANT"  />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.department" placeholder="返聘部门" clearable>
              <el-option label="全部部门" value=""  />
              <el-option
                v-for="dept in departmentOptions"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
               />
            </el-select>
          </el-col>
          <el-col :span="7">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
            <el-button type="success" @click="handleAdd">
              <el-icon><Plus /></el-icon>
              新增申请
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total">
              <el-icon><RefreshRight /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.total }}</div>
              <div class="stats-label">申请总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon pending">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.pending }}</div>
              <div class="stats-label">待审核</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon approved">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.approved }}</div>
              <div class="stats-label">已通过</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon active">
              <el-icon><Star /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.active }}</div>
              <div class="stats-label">在聘人员</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 申请列表 -->
    <el-card class="table-card" shadow="never">
      <div class="table-header">
        <span class="table-title">返聘申请列表</span>
        <div class="table-actions">
          <el-button size="small" @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
          <el-button size="small" @click="handleBatchApprove" :disabled="selectedRows.length === 0">
            <el-icon><Check /></el-icon>
            批量审核
          </el-button>
        </div>
      </div>

      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="applicationId" label="申请编号" width="120"  />
        <el-table-column prop="employeeName" label="姓名" width="100"  />
        <el-table-column prop="employeeNumber" label="原工号" width="100"  />
        <el-table-column prop="retirementDate" label="退休日期" width="100"  />
        <el-table-column prop="reemploymentType" label="返聘类型" width="120">
          <template #default="scope">
            <el-tag :type="getReemploymentTypeTag(scope.row.reemploymentType)" size="small">
              {{ getReemploymentTypeText(scope.row.reemploymentType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="proposedDepartment" label="拟返聘部门" width="150" show-overflow-tooltip  />
        <el-table-column prop="proposedPosition" label="拟返聘岗位" width="120" show-overflow-tooltip  />
        <el-table-column prop="contractPeriod" label="合同期限" width="100"  />
        <el-table-column prop="applicationDate" label="申请日期" width="100"  />
        <el-table-column prop="currentApprover" label="当前审批人" width="100"  />
        <el-table-column prop="status" label="状态" width="120">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" link @click="handleView(scope.row)">
              查看
            </el-button>
            <el-button 
              v-if="canEdit(scope.row.status)"
              size="small" 
              type="success" 
              link 
              @click="handleEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button 
              v-if="canApprove(scope.row.status)"
              size="small" 
              type="warning" 
              link 
              @click="handleApprove(scope.row)"
            >
              审核
            </el-button>
            <el-button 
              v-if="scope.row.status === 'APPROVED'"
              size="small" 
              type="success" 
              link 
              @click="handleConfirmReemployment(scope.row)"
            >
              确认返聘
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 在聘人员管理 -->
    <el-card class="active-employees-card" shadow="never" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>在聘人员管理</span>
          <el-button type="primary" size="small" @click="handleViewActiveEmployees">
            查看全部在聘人员
          </el-button>
        </div>
      </template>

      <el-table :data="activeEmployeesList" style="width: 100%">
        <el-table-column prop="employeeName" label="姓名" width="100"  />
        <el-table-column prop="employeeNumber" label="工号" width="100"  />
        <el-table-column prop="department" label="部门" width="150" show-overflow-tooltip  />
        <el-table-column prop="position" label="岗位" width="120"  />
        <el-table-column prop="reemploymentType" label="返聘类型" width="120">
          <template #default="scope">
            <el-tag :type="getReemploymentTypeTag(scope.row.reemploymentType)" size="small">
              {{ getReemploymentTypeText(scope.row.reemploymentType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="startDate" label="返聘开始日期" width="120"  />
        <el-table-column prop="endDate" label="返聘结束日期" width="120"  />
        <el-table-column prop="remainingDays" label="剩余天数" width="100">
          <template #default="scope">
            <el-tag :type="getRemainingDaysTag(scope.row.remainingDays)" size="small">
              {{ scope.row.remainingDays }}天
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" type="primary" link @click="handleViewContract(scope.row)">
              查看合同
            </el-button>
            <el-button size="small" type="success" link @click="handleRenewContract(scope.row)">
              续签
            </el-button>
            <el-button size="small" type="danger" link @click="handleTerminateContract(scope.row)">
              终止
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 返聘申请详情/编辑对话框 -->
    <ReemploymentDialog
      v-model:visible="dialogVisible"
      :application="currentApplication"
      :mode="dialogMode"
      @success="handleDialogSuccess"
    />

    <!-- 审核对话框 -->
    <ApprovalDialog
      v-model:visible="approvalDialogVisible"
      :application="currentApplication"
      @success="handleApprovalSuccess"
    />

    <!-- 合同管理对话框 -->
    <ContractDialog
      v-model:visible="contractDialogVisible"
      :employee="currentEmployee"
      :mode="contractDialogMode"
      @success="handleContractSuccess"
    />
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  RefreshRight,
  Clock,
  Check,
  Star,
  Download
} from '@element-plus/icons-vue'
import { reemploymentApi, personnelChangeOptions } from '@/api/personnelChange'
import { organizationApi } from '@/api/organization'

// 组件引入（需要创建）
// import ReemploymentDialog from './components/ReemploymentDialog.vue'
// import HrApprovalDialog from './components/HrApprovalDialog.vue'
// import ContractDialog from './components/ContractDialog.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref<any[]>([])
const selectedRows = ref<any[]>([])
const activeEmployeesList = ref<any[]>([])
const departmentOptions = ref<any[]>([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: '',
  reemploymentType: '',
  department: ''
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 统计信息
const stats = reactive({
  total: 0,
  pending: 0,
  approved: 0,
  active: 0
})

// 对话框相关
const dialogVisible = ref(false)
const approvalDialogVisible = ref(false)
const contractDialogVisible = ref(false)
const dialogMode = ref<'view' | 'add' | 'edit'>('view')
const contractDialogMode = ref<'view' | 'add' | 'edit'>('view')
const currentApplication = ref(null)
const currentEmployee = ref(null)

// 状态选项
const statusOptions = [
  { label: '待部门审核', value: 'PENDING_DEPARTMENT_REVIEW' },
  { label: '待人事处审核', value: 'PENDING_HR_REVIEW' },
  { label: '待校领导审核', value: 'PENDING_LEADERSHIP_REVIEW' },
  { label: '已通过', value: 'APPROVED' },
  { label: '已驳回', value: 'REJECTED' },
  { label: '已返聘', value: 'REEMPLOYED' },
  { label: '已终止', value: 'TERMINATED' }
]

// 模拟数据
const mockData = [
  {
    id: '1',
    applicationId: 'REMP202506001',
    employeeName: '张三',
    employeeNumber: 'EMP001',
    retirementDate: '2025-03-15',
    reemploymentType: 'FULL_TIME',
    proposedDepartment: '计算机学院',
    proposedPosition: '教授',
    contractPeriod: '1年',
    applicationDate: '2025-06-15',
    currentApprover: '李四',
    status: 'PENDING_DEPARTMENT_REVIEW'
  },
  {
    id: '2',
    applicationId: 'REMP202506002',
    employeeName: '王五',
    employeeNumber: 'EMP002',
    retirementDate: '2025-02-20',
    reemploymentType: 'PART_TIME',
    proposedDepartment: '机械工程学院',
    proposedPosition: '副教授',
    contractPeriod: '2年',
    applicationDate: '2025-06-14',
    currentApprover: '赵六',
    status: 'PENDING_HR_REVIEW'
  }
]

// 在聘人员模拟数据
const mockActiveEmployees = [
  {
    id: '1',
    employeeName: '刘七',
    employeeNumber: 'REMP001',
    department: '管理学院',
    position: '教授',
    reemploymentType: 'FULL_TIME',
    startDate: '2025-01-01',
    endDate: '2025-12-31',
    remainingDays: 195
  },
  {
    id: '2',
    employeeName: '孙八',
    employeeNumber: 'REMP002',
    department: '外语学院',
    position: '副教授',
    reemploymentType: 'PART_TIME',
    startDate: '2025-03-01',
    endDate: '2025-08-31',
    remainingDays: 72
  }
]

// 获取申请列表
const fetchApplications = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.page,
      size: pagination.size,
      keyword: searchForm.keyword,
      status: searchForm.status,
      department: searchForm.department
    }
    
    const result = await reemploymentApi.queryApplications(params)
    tableData.value = result.content
    pagination.total = result.totalElements
  } catch (__error) {
    console.error('获取申请列表失败:', error)
    ElMessage.error('获取申请列表失败')
  } finally {
    loading.value = false
  }
}

// 获取在聘人员列表
const fetchActiveEmployees = async () => {
  try {
    const result = await reemploymentApi.getActiveEmployees()
    activeEmployeesList.value = result
  } catch (__error) {
    console.error('获取在聘人员列表失败:', error)
    ElMessage.error('获取在聘人员列表失败')
  }
}

// 获取统计信息
const fetchStats = async () => {
  try {
    const result = await reemploymentApi.getStats()
    stats.total = result.total
    stats.pending = result.pending
    stats.approved = result.approved
    stats.active = result.approved // 使用approved作为active的值
  } catch (__error) {
    console.error('获取统计信息失败:', error)
    ElMessage.error('获取统计信息失败')
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchApplications()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: '',
    reemploymentType: '',
    department: ''
  })
  pagination.page = 1
  fetchApplications()
}

// 新增申请
const handleAdd = () => {
  currentApplication.value = null
  dialogMode.value = 'add'
  dialogVisible.value = true
}

// 查看申请
   
const handleView = (application: unknown) => {
  currentApplication.value = application
  dialogMode.value = 'view'
  dialogVisible.value = true
}

// 编辑申请
   
const handleEdit = (application: unknown) => {
  currentApplication.value = application
  dialogMode.value = 'edit'
  dialogVisible.value = true
}

// 审核申请
   
const handleApprove = (application: unknown) => {
  currentApplication.value = application
  approvalDialogVisible.value = true
}

// 确认返聘
   
const handleConfirmReemployment = async (application: unknown) => {
  try {
    await ElMessageBox.confirm(
      `确定要确认 "${application.employeeName}" 返聘吗？`,
      '确认返聘',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 调用确认返聘API
    await reemploymentApi.confirmReemployment(row.id)
    ElMessage.success('确认返聘成功')
    
    // 刷新数据
    fetchApplications()
    fetchActiveEmployees()
    fetchStats()
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('确认返聘失败:', error)
      ElMessage.error('确认返聘失败')
    }
  }
}

// 查看全部在聘人员
const handleViewActiveEmployees = () => {
  ElMessage.info('查看全部在聘人员功能开发中...')
}

// 查看合同
   
const handleViewContract = (employee: unknown) => {
  currentEmployee.value = employee
  contractDialogMode.value = 'view'
  contractDialogVisible.value = true
}

// 续签合同
   
const handleRenewContract = (employee: unknown) => {
  currentEmployee.value = employee
  contractDialogMode.value = 'add'
  contractDialogVisible.value = true
}

// 终止合同
   
const handleTerminateContract = async (employee: unknown) => {
  try {
    await ElMessageBox.confirm(
      `确定要终止 "${employee.employeeName}" 的返聘合同吗？`,
      '终止合同',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 调用终止合同API
    await reemploymentApi.terminateContract(employee.employeeId)
    ElMessage.success('终止合同成功')
    
    // 刷新数据
    fetchActiveEmployees()
    fetchStats()
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('终止合同失败:', error)
      ElMessage.error('终止合同失败')
    }
  }
}

// 表格选择变化
   
const handleSelectionChange = (selection: unknown[]) => {
  selectedRows.value = selection
}

// 批量审核
const handleBatchApprove = () => {
  ElMessage.info('批量审核功能开发中...')
}

// 导出
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchApplications()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchApplications()
}

// 对话框成功回调
const handleDialogSuccess = () => {
  fetchApplications()
  fetchStats()
}

// 审核成功回调
const handleApprovalSuccess = () => {
  fetchApplications()
  fetchStats()
}

// 合同成功回调
const handleContractSuccess = () => {
  fetchActiveEmployees()
  fetchStats()
}

// 获取返聘类型标签
const getReemploymentTypeTag = (type: string) => {
  switch (type) {
    case 'FULL_TIME': return 'primary'
    case 'PART_TIME': return 'success'
    case 'PROJECT_BASED': return 'warning'
    case 'CONSULTANT': return 'info'
    default: return ''
  }
}

// 获取返聘类型文本
const getReemploymentTypeText = (type: string) => {
  switch (type) {
    case 'FULL_TIME': return '全职返聘'
    case 'PART_TIME': return '兼职返聘'
    case 'PROJECT_BASED': return '项目返聘'
    case 'CONSULTANT': return '顾问返聘'
    default: return type
  }
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  switch (status) {
    case 'PENDING_DEPARTMENT_REVIEW':
    case 'PENDING_HR_REVIEW':
    case 'PENDING_LEADERSHIP_REVIEW':
      return 'warning'
    case 'APPROVED':
    case 'REEMPLOYED':
      return 'success'
    case 'REJECTED':
    case 'TERMINATED':
      return 'danger'
    default:
      return ''
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  const option = statusOptions.find(item => item.value === status)
  return option ? option.label : status
}

// 获取剩余天数标签
const getRemainingDaysTag = (days: number) => {
  if (days <= 30) return 'danger'
  if (days <= 90) return 'warning'
  return 'success'
}

// 判断是否可以编辑
const canEdit = (status: string) => {
  return ['PENDING_DEPARTMENT_REVIEW'].includes(status)
}

// 判断是否可以审核
const canApprove = (status: string) => {
  return ['PENDING_DEPARTMENT_REVIEW', 'PENDING_HR_REVIEW', 'PENDING_LEADERSHIP_REVIEW'].includes(status)
}

// 获取部门列表
const fetchDepartments = async () => {
  try {
    const response = await organizationApi.getOrgTree()
    // 将树形结构扁平化
   
    const flattenDepartments = (nodes: unknown[]): unknown[] => {
   
      let result: unknown[] = []
      nodes.forEach(node => {
        if (node.type === 'department') {
          result.push({
            id: node.id,
            name: node.name
          })
        }
        if (node.children && node.children.length > 0) {
          result = result.concat(flattenDepartments(node.children))
        }
      })
      return result
    }
    departmentOptions.value = flattenDepartments(response.data || [])
  } catch (__error) {
    console.error('获取部门列表失败:', error)
  }
}

// 初始化
onMounted(() => {
  fetchApplications()
  fetchActiveEmployees()
  fetchStats()
  fetchDepartments()
})
</script>

<style scoped>
.reemployment-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  padding: 10px 0;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.pending {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.approved {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.active {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.table-card {
  margin-top: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.active-employees-card {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
