<template>
  <div class="leave-school-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>离校管理</h2>
      <p>管理教职工离校登记、离职原因分析、资产回收等事务</p>
    </div>

    <!-- 功能标签页 -->
    <el-tabs v-model="activeTab" class="function-tabs">
      <!-- 离校登记 -->
      <el-tab-pane label="离校登记" name="registration">
        <el-card shadow="never">
          <div class="tab-header">
            <span class="tab-title">离校登记记录</span>
            <div class="tab-actions">
              <el-button type="success" @click="handleAddRegistration">
                <el-icon><Plus /></el-icon>
                新增登记
              </el-button>
              <el-button @click="handleExportRegistrations">
                <el-icon><Download /></el-icon>
                导出记录
              </el-button>
            </div>
          </div>

          <!-- 搜索区域 -->
          <div class="search-form">
            <el-row :gutter="20">
              <el-col :span="5">
                <el-input
                  v-model="registrationSearch.keyword"
                  placeholder="搜索姓名、工号"
                  clearable
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </el-col>
              <el-col :span="4">
                <el-select v-model="registrationSearch.leaveWay" placeholder="离校途径" clearable>
                  <el-option label="辞职" value="RESIGNATION"  />
                  <el-option label="解聘" value="DISMISSAL"  />
                  <el-option label="调离" value="TRANSFER"  />
                  <el-option label="开除" value="EXPULSION"  />
                  <el-option label="合同到期不续签" value="CONTRACT_EXPIRY"  />
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-select v-model="registrationSearch.department" placeholder="所属部门" clearable>
                  <el-option label="全部部门" value=""  />
                  <el-option
                    v-for="dept in departmentOptions"
                    :key="dept.id"
                    :label="dept.name"
                    :value="dept.id"
                   />
                </el-select>
              </el-col>
              <el-col :span="5">
                <el-date-picker
                  v-model="registrationSearch.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                 />
              </el-col>
              <el-col :span="6">
                <el-button type="primary" @click="handleSearchRegistrations">
                  <el-icon><Search /></el-icon>
                  搜索
                </el-button>
                <el-button @click="handleResetRegistrationSearch">
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
              </el-col>
            </el-row>
          </div>

          <!-- 统计卡片 -->
          <el-row :gutter="20" class="stats-row">
            <el-col :span="6">
              <el-card class="stats-card">
                <div class="stats-content">
                  <div class="stats-icon total">
                    <el-icon><User /></el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ registrationStats.total }}</div>
                    <div class="stats-label">离校总数</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stats-card">
                <div class="stats-content">
                  <div class="stats-icon resignation">
                    <el-icon><Right /></el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ registrationStats.resignation }}</div>
                    <div class="stats-label">主动辞职</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stats-card">
                <div class="stats-content">
                  <div class="stats-icon transfer">
                    <el-icon><Switch /></el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ registrationStats.transfer }}</div>
                    <div class="stats-label">调离</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stats-card">
                <div class="stats-content">
                  <div class="stats-icon thisMonth">
                    <el-icon><Star /></el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ registrationStats.thisMonth }}</div>
                    <div class="stats-label">本月离校</div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 登记列表 -->
          <el-table :data="registrationList" style="width: 100%">
            <el-table-column prop="recordId" label="登记编号" width="120"  />
            <el-table-column prop="employeeName" label="姓名" width="100"  />
            <el-table-column prop="employeeNumber" label="工号" width="100"  />
            <el-table-column prop="department" label="部门" width="150" show-overflow-tooltip  />
            <el-table-column prop="leaveWay" label="离校途径" width="120">
              <template #default="scope">
                <el-tag :type="getLeaveWayTag(scope.row.leaveWay)" size="small">
                  {{ getLeaveWayText(scope.row.leaveWay) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="leaveDate" label="离校日期" width="100"  />
            <el-table-column prop="leaveDestination" label="去向" width="150" show-overflow-tooltip  />
            <el-table-column prop="isHandoverCompleted" label="交接状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.isHandoverCompleted ? 'success' : 'warning'" size="small">
                  {{ scope.row.isHandoverCompleted ? '已完成' : '未完成' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="operator" label="办理人" width="100"  />
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button size="small" type="primary" link @click="handleViewRegistration(scope.row)">
                  查看详情
                </el-button>
                <el-button size="small" type="success" link @click="handleViewHandover(scope.row)">
                  交接清单
                </el-button>
                <el-button size="small" type="danger" link @click="handleDeleteRegistration(scope.row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <!-- 离职原因分析 -->
      <el-tab-pane label="离职原因分析" name="reason-analysis">
        <el-card shadow="never">
          <div class="tab-header">
            <span class="tab-title">离职原因统计分析</span>
            <div class="tab-actions">
              <el-button @click="handleRefreshAnalysis">
                <el-icon><Refresh /></el-icon>
                刷新数据
              </el-button>
              <el-button @click="handleExportAnalysis">
                <el-icon><Download /></el-icon>
                导出报告
              </el-button>
            </div>
          </div>

          <!-- 分析图表区域 -->
          <el-row :gutter="20" class="analysis-charts">
            <el-col :span="12">
              <el-card class="chart-card" shadow="never">
                <template #header>
                  <span>离职原因分布</span>
                </template>
                <div class="chart-container" id="reasonPieChart">
                  <!-- 饼图容器 -->
                  <div class="chart-placeholder">离职原因分布图</div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card class="chart-card" shadow="never">
                <template #header>
                  <span>月度离职趋势</span>
                </template>
                <div class="chart-container" id="trendLineChart">
                  <!-- 折线图容器 -->
                  <div class="chart-placeholder">月度离职趋势图</div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 详细分析表格 -->
          <el-table :data="reasonAnalysisList" style="width: 100%; margin-top: 20px;">
            <el-table-column prop="mainCategory" label="主要原因" width="150"  />
            <el-table-column prop="subCategory" label="具体原因" width="200"  />
            <el-table-column prop="count" label="人数" width="100"  />
            <el-table-column prop="percentage" label="占比" width="100">
              <template #default="scope">
                {{ scope.row.percentage }}%
              </template>
            </el-table-column>
            <el-table-column prop="retainableCount" label="可挽留人数" width="120"  />
            <el-table-column prop="avgWorkYears" label="平均工龄" width="100">
              <template #default="scope">
                {{ scope.row.avgWorkYears }}年
              </template>
            </el-table-column>
            <el-table-column prop="trend" label="趋势" width="100">
              <template #default="scope">
                <el-tag :type="getTrendTag(scope.row.trend)" size="small">
                  {{ getTrendText(scope.row.trend) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template #default="scope">
                <el-button size="small" type="primary" link @click="handleViewReasonDetail(scope.row)">
                  查看详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <!-- 资产回收 -->
      <el-tab-pane label="资产回收" name="asset-recovery">
        <el-card shadow="never">
          <div class="tab-header">
            <span class="tab-title">资产回收管理</span>
            <el-button type="success" @click="handleAddAssetRecovery">
              <el-icon><Plus /></el-icon>
              新增回收清单
            </el-button>
          </div>

          <!-- 回收统计 -->
          <el-row :gutter="20" class="recovery-stats">
            <el-col :span="6">
              <el-card class="stats-card">
                <div class="stats-content">
                  <div class="stats-icon pending">
                    <el-icon><Clock /></el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ recoveryStats.pending }}</div>
                    <div class="stats-label">待回收</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stats-card">
                <div class="stats-content">
                  <div class="stats-icon completed">
                    <el-icon><Check /></el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ recoveryStats.completed }}</div>
                    <div class="stats-label">已回收</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stats-card">
                <div class="stats-content">
                  <div class="stats-icon damaged">
                    <el-icon><Warning /></el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ recoveryStats.damaged }}</div>
                    <div class="stats-label">损坏/缺失</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stats-card">
                <div class="stats-content">
                  <div class="stats-icon overdue">
                    <el-icon><Timer /></el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ recoveryStats.overdue }}</div>
                    <div class="stats-label">逾期未还</div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 回收清单 -->
          <el-table :data="assetRecoveryList" style="width: 100%">
            <el-table-column prop="employeeName" label="员工姓名" width="100"  />
            <el-table-column prop="employeeNumber" label="工号" width="100"  />
            <el-table-column prop="department" label="部门" width="150" show-overflow-tooltip  />
            <el-table-column prop="assetName" label="资产名称" width="150"  />
            <el-table-column prop="assetNumber" label="资产编号" width="120"  />
            <el-table-column prop="responsibleDepartment" label="负责部门" width="120"  />
            <el-table-column prop="isRecovered" label="回收状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.isRecovered ? 'success' : 'warning'" size="small">
                  {{ scope.row.isRecovered ? '已回收' : '待回收' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="recoveryDate" label="回收日期" width="100"  />
            <el-table-column prop="damageLossStatus" label="损坏/缺失情况" width="150" show-overflow-tooltip  />
            <el-table-column label="操作" width="150">
              <template #default="scope">
                <el-button 
                  v-if="!scope.row.isRecovered"
                  size="small" 
                  type="success" 
                  link 
                  @click="handleConfirmRecovery(scope.row)"
                >
                  确认回收
                </el-button>
                <el-button size="small" type="primary" link @click="handleViewAssetDetail(scope.row)">
                  查看详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <!-- 权限清理 -->
      <el-tab-pane label="权限清理" name="permission-cleanup">
        <el-card shadow="never">
          <div class="tab-header">
            <span class="tab-title">权限清理记录</span>
            <el-button type="success" @click="handleBatchCleanup">
              <el-icon><Delete /></el-icon>
              批量清理
            </el-button>
          </div>

          <!-- 权限清理列表 -->
          <el-table :data="permissionCleanupList" style="width: 100%">
            <el-table-column type="selection" width="55"  />
            <el-table-column prop="employeeName" label="员工姓名" width="100"  />
            <el-table-column prop="employeeNumber" label="工号" width="100"  />
            <el-table-column prop="systemName" label="系统名称" width="150"  />
            <el-table-column prop="permissionType" label="权限类型" width="150"  />
            <el-table-column prop="cleanupTime" label="清理时间" width="150"  />
            <el-table-column prop="cleanupResult" label="清理结果" width="100">
              <template #default="scope">
                <el-tag :type="getCleanupResultTag(scope.row.cleanupResult)" size="small">
                  {{ scope.row.cleanupResult }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template #default="scope">
                <el-button size="small" type="primary" link @click="handleViewCleanupDetail(scope.row)">
                  查看详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <!-- 离校登记对话框 -->
    <LeaveRegistrationDialog
      v-model:visible="registrationDialogVisible"
      :employee="currentEmployee"
      :mode="registrationDialogMode"
      @success="handleRegistrationSuccess"
    />

    <!-- 交接清单对话框 -->
    <HandoverDialog
      v-model:visible="handoverDialogVisible"
      :employee="currentEmployee"
      @success="handleHandoverSuccess"
    />

    <!-- 资产回收对话框 -->
    <AssetRecoveryDialog
      v-model:visible="assetRecoveryDialogVisible"
      :employee="currentEmployee"
      :mode="assetRecoveryDialogMode"
      @success="handleAssetRecoverySuccess"
    />
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Download,
  Search,
  Refresh,
  User,
  Right,
  Switch,
  Star,
  Clock,
  Check,
  Warning,
  Timer,
  Delete
} from '@element-plus/icons-vue'
import { organizationApi } from '@/api/organization'

// 组件引入
// import LeaveRegistrationDialog from './components/LeaveRegistrationDialog.vue'
import HandoverDialog from './components/HandoverDialog.vue'
// import AssetRecoveryDialog from './components/AssetRecoveryDialog.vue'

// 响应式数据
const activeTab = ref('registration')
const departmentOptions = ref<any[]>([])

// 离校登记相关数据
const registrationSearch = reactive({
  keyword: '',
  leaveWay: '',
  department: '',
  dateRange: []
})

const registrationStats = reactive({
  total: 45,
  resignation: 28,
  transfer: 12,
  thisMonth: 8
})

const registrationList = ref([
  {
    id: '1',
    recordId: 'LEAVE202506001',
    employeeName: '张三',
    employeeNumber: 'EMP001',
    department: '计算机学院',
    leaveWay: 'RESIGNATION',
    leaveDate: '2025-06-15',
    leaveDestination: '某科技公司',
    isHandoverCompleted: true,
    operator: '人事处'
  },
  {
    id: '2',
    recordId: 'LEAVE202506002',
    employeeName: '李四',
    employeeNumber: 'EMP002',
    department: '机械工程学院',
    leaveWay: 'TRANSFER',
    leaveDate: '2025-06-10',
    leaveDestination: '某大学',
    isHandoverCompleted: false,
    operator: '人事处'
  }
])

// 离职原因分析数据
const reasonAnalysisList = ref([
  {
    mainCategory: '个人发展',
    subCategory: '职业发展空间有限',
    count: 15,
    percentage: 33.3,
    retainableCount: 8,
    avgWorkYears: 3.5,
    trend: 'UP'
  },
  {
    mainCategory: '薪酬福利',
    subCategory: '薪资水平不满意',
    count: 12,
    percentage: 26.7,
    retainableCount: 10,
    avgWorkYears: 2.8,
    trend: 'STABLE'
  },
  {
    mainCategory: '工作环境',
    subCategory: '工作压力过大',
    count: 8,
    percentage: 17.8,
    retainableCount: 5,
    avgWorkYears: 4.2,
    trend: 'DOWN'
  }
])

// 资产回收数据
const recoveryStats = reactive({
  pending: 12,
  completed: 35,
  damaged: 3,
  overdue: 2
})

const assetRecoveryList = ref([
  {
    id: '1',
    employeeName: '张三',
    employeeNumber: 'EMP001',
    department: '计算机学院',
    assetName: '笔记本电脑',
    assetNumber: 'NB001',
    responsibleDepartment: '设备部',
    isRecovered: true,
    recoveryDate: '2025-06-15',
    damageLossStatus: '正常'
  },
  {
    id: '2',
    employeeName: '李四',
    employeeNumber: 'EMP002',
    department: '机械工程学院',
    assetName: '办公桌椅',
    assetNumber: 'DESK001',
    responsibleDepartment: '行政部',
    isRecovered: false,
    recoveryDate: '',
    damageLossStatus: ''
  }
])

// 权限清理数据
const permissionCleanupList = ref([
  {
    id: '1',
    employeeName: '张三',
    employeeNumber: 'EMP001',
    systemName: '人事管理系统',
    permissionType: '系统账号',
    cleanupTime: '2025-06-15 14:30:00',
    cleanupResult: '成功'
  },
  {
    id: '2',
    employeeName: '张三',
    employeeNumber: 'EMP001',
    systemName: '财务系统',
    permissionType: '文件夹访问',
    cleanupTime: '2025-06-15 14:35:00',
    cleanupResult: '失败'
  }
])

// 对话框相关
const registrationDialogVisible = ref(false)
const handoverDialogVisible = ref(false)
const assetRecoveryDialogVisible = ref(false)
const registrationDialogMode = ref<'view' | 'add' | 'edit'>('add')
const assetRecoveryDialogMode = ref<'view' | 'add' | 'edit'>('add')
const currentEmployee = ref(null)

// 新增离校登记
const handleAddRegistration = () => {
  currentEmployee.value = null
  registrationDialogMode.value = 'add'
  registrationDialogVisible.value = true
}

// 查看离校登记
   
const handleViewRegistration = (registration: unknown) => {
  currentEmployee.value = registration
  registrationDialogMode.value = 'view'
  registrationDialogVisible.value = true
}

// 查看交接清单
   
const handleViewHandover = (registration: unknown) => {
  currentEmployee.value = registration
  handoverDialogVisible.value = true
}

// 删除离校登记
   
const handleDeleteRegistration = async (registration: unknown) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除 "${registration.employeeName}" 的离校登记记录吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('删除成功')
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 导出登记记录
const handleExportRegistrations = () => {
  ElMessage.info('导出功能开发中...')
}

// 搜索登记记录
const handleSearchRegistrations = () => {
  ElMessage.info('搜索功能开发中...')
}

// 重置搜索
const handleResetRegistrationSearch = () => {
  Object.assign(registrationSearch, {
    keyword: '',
    leaveWay: '',
    department: '',
    dateRange: []
  })
}

// 刷新分析数据
const handleRefreshAnalysis = () => {
  ElMessage.success('分析数据已刷新')
}

// 导出分析报告
const handleExportAnalysis = () => {
  ElMessage.info('导出分析报告功能开发中...')
}

// 查看原因详情
   
const handleViewReasonDetail = (reason: unknown) => {
  ElMessage.info(`查看 ${reason.mainCategory} - ${reason.subCategory} 的详细信息`)
}

// 新增资产回收清单
const handleAddAssetRecovery = () => {
  currentEmployee.value = null
  assetRecoveryDialogMode.value = 'add'
  assetRecoveryDialogVisible.value = true
}

// 确认回收
   
const handleConfirmRecovery = async (asset: unknown) => {
  try {
    await ElMessageBox.confirm(
      `确定要确认回收 "${asset.assetName}" 吗？`,
      '确认回收',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('确认回收成功')
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('确认回收失败')
    }
  }
}

// 查看资产详情
   
const handleViewAssetDetail = (asset: unknown) => {
  ElMessage.info(`查看资产 ${asset.assetName} 的详细信息`)
}

// 批量权限清理
const handleBatchCleanup = () => {
  ElMessage.info('批量权限清理功能开发中...')
}

// 查看清理详情
   
const handleViewCleanupDetail = (cleanup: unknown) => {
  ElMessage.info(`查看 ${cleanup.systemName} 的权限清理详情`)
}

// 对话框成功回调
const handleRegistrationSuccess = () => {
  ElMessage.success('操作成功')
}

const handleHandoverSuccess = () => {
  ElMessage.success('操作成功')
}

const handleAssetRecoverySuccess = () => {
  ElMessage.success('操作成功')
}

// 获取离校途径标签
const getLeaveWayTag = (way: string) => {
  switch (way) {
    case 'RESIGNATION': return 'primary'
    case 'DISMISSAL': return 'danger'
    case 'TRANSFER': return 'success'
    case 'EXPULSION': return 'danger'
    case 'CONTRACT_EXPIRY': return 'warning'
    default: return ''
  }
}

// 获取离校途径文本
const getLeaveWayText = (way: string) => {
  switch (way) {
    case 'RESIGNATION': return '辞职'
    case 'DISMISSAL': return '解聘'
    case 'TRANSFER': return '调离'
    case 'EXPULSION': return '开除'
    case 'CONTRACT_EXPIRY': return '合同到期'
    default: return way
  }
}

// 获取趋势标签
const getTrendTag = (trend: string) => {
  switch (trend) {
    case 'UP': return 'danger'
    case 'DOWN': return 'success'
    case 'STABLE': return 'warning'
    default: return ''
  }
}

// 获取趋势文本
const getTrendText = (trend: string) => {
  switch (trend) {
    case 'UP': return '上升'
    case 'DOWN': return '下降'
    case 'STABLE': return '稳定'
    default: return trend
  }
}

// 获取清理结果标签
const getCleanupResultTag = (result: string) => {
  switch (result) {
    case '成功': return 'success'
    case '失败': return 'danger'
    case '部分成功': return 'warning'
    default: return ''
  }
}

// 获取部门列表
const fetchDepartments = async () => {
  try {
    const response = await organizationApi.getTree()
    if (response && Array.isArray(response)) {
      departmentOptions.value = response.map(dept => ({
        id: dept.id,
        name: dept.name,
        orgCode: dept.orgCode
      }))
    }
  } catch (__error) {
    console.error('获取部门列表失败:', error)
    ElMessage.error('获取部门列表失败')
  }
}

// 初始化
onMounted(() => {
  fetchDepartments()
})
</script>

<style scoped>
.leave-school-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.function-tabs {
  margin-top: 20px;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.tab-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.tab-actions {
  display: flex;
  gap: 8px;
}

.search-form {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stats-row,
.recovery-stats {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stats-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 20px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.resignation {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.transfer {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.thisMonth {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-icon.pending {
  background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
}

.stats-icon.completed {
  background: linear-gradient(135deg, #48dbfb 0%, #0abde3 100%);
}

.stats-icon.damaged {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.stats-icon.overdue {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 12px;
  color: #909399;
}

.analysis-charts {
  margin-bottom: 20px;
}

.chart-card {
  height: 300px;
}

.chart-container {
  height: 240px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  color: #909399;
  font-size: 14px;
}
</style>
