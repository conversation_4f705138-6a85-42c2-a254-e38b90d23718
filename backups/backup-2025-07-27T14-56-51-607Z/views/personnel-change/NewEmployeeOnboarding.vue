<template>
  <div class="new-employee-onboarding">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>新教工入职管理</h2>
      <p>管理新教工入职申请、审核、工号分配和入职流程</p>
    </div>

    <!-- 搜索和操作区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-form">
        <el-row :gutter="20">
          <el-col :span="5">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索姓名、身份证号"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.status" placeholder="申请状态" clearable>
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.department" placeholder="拟入职部门" clearable>
              <el-option label="全部部门" value=""  />
              <el-option
                v-for="dept in departmentOptions"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
               />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
             />
          </el-col>
          <el-col :span="7">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
            <el-button type="success" @click="handleAdd">
              <el-icon><Plus /></el-icon>
              新增申请
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total">
              <el-icon><User /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.total }}</div>
              <div class="stats-label">申请总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon pending">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.pending }}</div>
              <div class="stats-label">待审核</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon approved">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.approved }}</div>
              <div class="stats-label">已通过</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon thisMonth">
              <el-icon><Star /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.thisMonth }}</div>
              <div class="stats-label">本月新增</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 申请列表 -->
    <el-card class="table-card" shadow="never">
      <div class="table-header">
        <span class="table-title">入职申请列表</span>
        <div class="table-actions">
          <el-button size="small" @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
          <el-button size="small" @click="handleBatchApprove" :disabled="selectedRows.length === 0">
            <el-icon><Check /></el-icon>
            批量审核
          </el-button>
        </div>
      </div>

      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="applicationId" label="申请编号" width="120"  />
        <el-table-column prop="fullName" label="姓名" width="100"  />
        <el-table-column prop="idNumber" label="身份证号" width="150" show-overflow-tooltip  />
        <el-table-column prop="proposedDepartment" label="拟入职部门" width="150" show-overflow-tooltip  />
        <el-table-column prop="proposedPosition" label="拟入职岗位" width="120" show-overflow-tooltip  />
        <el-table-column prop="applicationDate" label="申请日期" width="100"  />
        <el-table-column prop="currentApprover" label="当前审批人" width="100"  />
        <el-table-column prop="status" label="状态" width="120">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" link @click="handleView(scope.row)">
              查看
            </el-button>
            <el-button 
              v-if="scope.row.status === 'PENDING_PERSONAL_INFO'"
              size="small" 
              type="success" 
              link 
              @click="handleEdit(scope.row)"
            >
              完善信息
            </el-button>
            <el-button 
              v-if="canApprove(scope.row.status)"
              size="small" 
              type="warning" 
              link 
              @click="handleApprove(scope.row)"
            >
              审核
            </el-button>
            <el-button 
              v-if="scope.row.status === 'APPROVED'"
              size="small" 
              type="success" 
              link 
              @click="handleConfirmOnboarding(scope.row)"
            >
              确认入职
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 申请详情/编辑对话框 -->
    <OnboardingDialog
      v-model:visible="dialogVisible"
      :application="currentApplication"
      :mode="dialogMode"
      @success="handleDialogSuccess"
    />

    <!-- 审核对话框 -->
    <ApprovalDialog
      v-model:visible="approvalDialogVisible"
      :application="currentApplication"
      @success="handleApprovalSuccess"
    />
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  User,
  Clock,
  Check,
  Star,
  Download
} from '@element-plus/icons-vue'
import { organizationApi } from '@/api/organization'

// 组件引入
import OnboardingDialog from './components/OnboardingDialog.vue'
import HrApprovalDialog from './components/HrApprovalDialog.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref<unknown[]>([])
const selectedRows = ref<unknown[]>([])
const departmentOptions = ref<unknown[]>([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: '',
  department: '',
  dateRange: []
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 统计信息
const stats = reactive({
  total: 0,
  pending: 0,
  approved: 0,
  thisMonth: 0
})

// 对话框相关
const dialogVisible = ref(false)
const approvalDialogVisible = ref(false)
const dialogMode = ref<'view' | 'add' | 'edit'>('view')
const currentApplication = ref(null)

// 状态选项
const statusOptions = [
  { label: '待个人完善', value: 'PENDING_PERSONAL_INFO' },
  { label: '待二级单位审核', value: 'PENDING_DEPARTMENT_REVIEW' },
  { label: '待人事处审核', value: 'PENDING_HR_REVIEW' },
  { label: '已通过', value: 'APPROVED' },
  { label: '已驳回', value: 'REJECTED' },
  { label: '已入职', value: 'ONBOARDED' }
]

// 模拟数据
const mockData = [
  {
    id: '1',
    applicationId: 'APP202506001',
    fullName: '张三',
    idNumber: '330102199001011234',
    proposedDepartment: '计算机学院',
    proposedPosition: '讲师',
    applicationDate: '2025-06-15',
    currentApprover: '李四',
    status: 'PENDING_DEPARTMENT_REVIEW'
  },
  {
    id: '2',
    applicationId: 'APP202506002',
    fullName: '王五',
    idNumber: '330102199002021234',
    proposedDepartment: '机械工程学院',
    proposedPosition: '副教授',
    applicationDate: '2025-06-14',
    currentApprover: '赵六',
    status: 'PENDING_HR_REVIEW'
  }
]

// 获取申请列表
const fetchApplications = async () => {
  try {
    loading.value = true
    // 构建查询参数
   
    const params: unknown = {
      page: pagination.page,
      size: pagination.size
    }
    
    // 添加搜索条件
    if (searchForm.keyword) {
      params.keyword = searchForm.keyword
    }
    if (searchForm.status) {
      params.status = searchForm.status
    }
    if (searchForm.department) {
      params.department = searchForm.department
    }
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.startDate = searchForm.dateRange[0]
      params.endDate = searchForm.dateRange[1]
    }
    
    // 调用实际API（暂时使用模拟数据，等待后端API完成）
    await new Promise(resolve => setTimeout(resolve, 500))
    tableData.value = mockData
    pagination.total = mockData.length
  } catch (__error) {
    ElMessage.error('获取申请列表失败')
  } finally {
    loading.value = false
  }
}

// 获取统计信息
const fetchStats = async () => {
  try {
    // 构建查询参数
   
    const params: unknown = {}
    
    // 添加筛选条件
    if (searchForm.status) {
      params.status = searchForm.status
    }
    if (searchForm.department) {
      params.department = searchForm.department
    }
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.startDate = searchForm.dateRange[0]
      params.endDate = searchForm.dateRange[1]
    }
    
    // 调用实际API（暂时使用模拟数据，等待后端API完成）
    stats.total = 25
    stats.pending = 8
    stats.approved = 15
    stats.thisMonth = 5
  } catch (__error) {
    }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchApplications()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: '',
    department: '',
    dateRange: []
  })
  pagination.page = 1
  fetchApplications()
}

// 新增申请
const handleAdd = () => {
  currentApplication.value = null
  dialogMode.value = 'add'
  dialogVisible.value = true
}

// 查看申请
   
const handleView = (application: unknown) => {
  currentApplication.value = application
  dialogMode.value = 'view'
  dialogVisible.value = true
}

// 编辑申请
   
const handleEdit = (application: unknown) => {
  currentApplication.value = application
  dialogMode.value = 'edit'
  dialogVisible.value = true
}

// 审核申请
   
const handleApprove = (application: unknown) => {
  currentApplication.value = application
  approvalDialogVisible.value = true
}

// 确认入职
   
const handleConfirmOnboarding = async (application: unknown) => {
  try {
    await ElMessageBox.confirm(
      `确定要确认 "${application.fullName}" 入职吗？`,
      '确认入职',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 构建确认入职请求数据
      applicationId: application.id,
      fullName: application.fullName,
      idNumber: application.idNumber,
      department: application.proposedDepartment,
      position: application.proposedPosition,
      onboardingDate: new Date().toISOString().split('T')[0]
    }
    
    // 调用确认入职API（暂时使用模拟，等待后端API完成）
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('确认入职成功')
    fetchApplications()
    fetchStats()
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('确认入职失败')
    }
  }
}

// 表格选择变化
   
const handleSelectionChange = (selection: unknown[]) => {
  selectedRows.value = selection
}

// 批量审核
const handleBatchApprove = () => {
  ElMessage.info('批量审核功能开发中...')
}

// 导出
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchApplications()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchApplications()
}

// 对话框成功回调
const handleDialogSuccess = () => {
  fetchApplications()
  fetchStats()
}

// 审核成功回调
const handleApprovalSuccess = () => {
  fetchApplications()
  fetchStats()
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  switch (status) {
    case 'PENDING_PERSONAL_INFO':
      return 'info'
    case 'PENDING_DEPARTMENT_REVIEW':
    case 'PENDING_HR_REVIEW':
      return 'warning'
    case 'APPROVED':
      return 'success'
    case 'REJECTED':
      return 'danger'
    case 'ONBOARDED':
      return 'success'
    default:
      return ''
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  const option = statusOptions.find(item => item.value === status)
  return option ? option.label : status
}

// 判断是否可以审核
const canApprove = (status: string) => {
  return ['PENDING_DEPARTMENT_REVIEW', 'PENDING_HR_REVIEW'].includes(status)
}

// 获取部门列表
const fetchDepartments = async () => {
  try {
    const response = await organizationApi.getTree()
    if (response && Array.isArray(response)) {
      departmentOptions.value = response.map(dept => ({
        id: dept.id,
        name: dept.name,
        orgCode: dept.orgCode
      }))
    }
  } catch (__error) {
    ElMessage.error('获取部门列表失败')
  }
}

// 初始化
onMounted(() => {
  fetchApplications()
  fetchStats()
  fetchDepartments()
})
</script>

<style scoped>
.new-employee-onboarding {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  padding: 10px 0;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.pending {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.approved {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.thisMonth {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.table-card {
  margin-top: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
