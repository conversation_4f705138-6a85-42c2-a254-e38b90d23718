<template>
  <div class="personnel-change-statistics">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>人事变动统计</h2>
      <p>查看人事变动统计分析和趋势报告</p>
    </div>

    <!-- 统计概览 -->
    <el-row :gutter="20" class="overview-stats">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total">
              <el-icon><User /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ overviewStats.total }}</div>
              <div class="stats-label">总变动人数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon onboarding">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ overviewStats.onboarding }}</div>
              <div class="stats-label">新入职人数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon leaving">
              <el-icon><Right /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ overviewStats.leaving }}</div>
              <div class="stats-label">离职人数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon adjustment">
              <el-icon><Switch /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ overviewStats.adjustment }}</div>
              <div class="stats-label">岗位调整人数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <div class="chart-header">
              <span>月度变动趋势</span>
              <el-select v-model="trendYear" size="small" style="width: 100px;">
                <el-option label="2025年" value="2025"  />
                <el-option label="2024年" value="2024"  />
              </el-select>
            </div>
          </template>
          <div class="chart-container" id="trendChart">
            <div class="chart-placeholder">月度变动趋势图</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <span>变动类型分布</span>
          </template>
          <div class="chart-container" id="typeChart">
            <div class="chart-placeholder">变动类型分布图</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <span>部门变动统计</span>
          </template>
          <div class="chart-container" id="departmentChart">
            <div class="chart-placeholder">部门变动统计图</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <span>年龄分布分析</span>
          </template>
          <div class="chart-container" id="ageChart">
            <div class="chart-placeholder">年龄分布分析图</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细统计表格 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="table-header">
          <span>详细统计数据</span>
          <div class="table-actions">
            <el-button size="small" @click="handleExport">
              <el-icon><Download /></el-icon>
              导出报告
            </el-button>
            <el-button size="small" @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
          </div>
        </div>
      </template>

      <!-- 筛选条件 -->
      <div class="filter-form">
        <el-row :gutter="20">
          <el-col :span="4">
            <el-select v-model="filterForm.changeType" placeholder="变动类型" clearable>
              <el-option label="新员工入职" value="NEW_EMPLOYEE"  />
              <el-option label="岗位调整" value="POSITION_ADJUSTMENT"  />
              <el-option label="退休" value="RETIREMENT"  />
              <el-option label="离校" value="LEAVE_SCHOOL"  />
              <el-option label="退休返聘" value="REEMPLOYMENT"  />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="filterForm.department" placeholder="部门" clearable>
              <el-option label="全部部门" value=""  />
              <el-option 
                v-for="dept in departmentOptions"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
               />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
             />
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="handleFilter">
              <el-icon><Search /></el-icon>
              筛选
            </el-button>
            <el-button @click="handleResetFilter">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 统计表格 -->
      <el-table :data="statisticsData" style="width: 100%">
        <el-table-column prop="department" label="部门" width="150"  />
        <el-table-column prop="newEmployees" label="新入职" width="100" align="center"  />
        <el-table-column prop="positionAdjustments" label="岗位调整" width="100" align="center"  />
        <el-table-column prop="retirements" label="退休" width="100" align="center"  />
        <el-table-column prop="leaves" label="离校" width="100" align="center"  />
        <el-table-column prop="reemployments" label="返聘" width="100" align="center"  />
        <el-table-column prop="totalChanges" label="总变动" width="100" align="center">
          <template #default="scope">
            <el-tag type="primary" size="small">{{ scope.row.totalChanges }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="changeRate" label="变动率" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getChangeRateTag(scope.row.changeRate)" size="small">
              {{ scope.row.changeRate }}%
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="trend" label="趋势" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getTrendTag(scope.row.trend)" size="small">
              {{ getTrendText(scope.row.trend) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button size="small" type="primary" link @click="handleViewDetail(scope.row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 月度对比分析 -->
    <el-card class="comparison-card" shadow="never" style="margin-top: 20px;">
      <template #header>
        <span>月度对比分析</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="comparison-item">
            <div class="comparison-title">本月 vs 上月</div>
            <div class="comparison-content">
              <div class="comparison-number">
                <span class="current">{{ monthComparison.current }}</span>
                <span class="vs">vs</span>
                <span class="previous">{{ monthComparison.previous }}</span>
              </div>
              <div class="comparison-change">
                <el-tag :type="monthComparison.change > 0 ? 'danger' : 'success'" size="small">
                  {{ monthComparison.change > 0 ? '+' : '' }}{{ monthComparison.change }}
                  ({{ monthComparison.changePercent }}%)
                </el-tag>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="comparison-item">
            <div class="comparison-title">本季度 vs 上季度</div>
            <div class="comparison-content">
              <div class="comparison-number">
                <span class="current">{{ quarterComparison.current }}</span>
                <span class="vs">vs</span>
                <span class="previous">{{ quarterComparison.previous }}</span>
              </div>
              <div class="comparison-change">
                <el-tag :type="quarterComparison.change > 0 ? 'danger' : 'success'" size="small">
                  {{ quarterComparison.change > 0 ? '+' : '' }}{{ quarterComparison.change }}
                  ({{ quarterComparison.changePercent }}%)
                </el-tag>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="comparison-item">
            <div class="comparison-title">本年 vs 去年</div>
            <div class="comparison-content">
              <div class="comparison-number">
                <span class="current">{{ yearComparison.current }}</span>
                <span class="vs">vs</span>
                <span class="previous">{{ yearComparison.previous }}</span>
              </div>
              <div class="comparison-change">
                <el-tag :type="yearComparison.change > 0 ? 'danger' : 'success'" size="small">
                  {{ yearComparison.change > 0 ? '+' : '' }}{{ yearComparison.change }}
                  ({{ yearComparison.changePercent }}%)
                </el-tag>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  User,
  UserFilled,
  Right,
  Switch,
  Download,
  Refresh,
  Search
} from '@element-plus/icons-vue'
import { organizationApi } from '@/api/organization'

// 响应式数据
const trendYear = ref('2025')
const departmentOptions = ref<any[]>([])

// 概览统计
const overviewStats = reactive({
  total: 156,
  onboarding: 45,
  leaving: 28,
  adjustment: 35
})

// 筛选表单
const filterForm = reactive({
  changeType: '',
  department: '',
  dateRange: []
})

// 统计数据
const statisticsData = ref([
  {
    department: '计算机学院',
    newEmployees: 8,
    positionAdjustments: 5,
    retirements: 3,
    leaves: 2,
    reemployments: 1,
    totalChanges: 19,
    changeRate: 12.5,
    trend: 'UP'
  },
  {
    department: '机械工程学院',
    newEmployees: 6,
    positionAdjustments: 3,
    retirements: 4,
    leaves: 3,
    reemployments: 2,
    totalChanges: 18,
    changeRate: 11.8,
    trend: 'STABLE'
  },
  {
    department: '管理学院',
    newEmployees: 5,
    positionAdjustments: 4,
    retirements: 2,
    leaves: 1,
    reemployments: 1,
    totalChanges: 13,
    changeRate: 8.7,
    trend: 'DOWN'
  }
])

// 月度对比数据
const monthComparison = reactive({
  current: 25,
  previous: 18,
  change: 7,
  changePercent: 38.9
})

const quarterComparison = reactive({
  current: 78,
  previous: 65,
  change: 13,
  changePercent: 20.0
})

const yearComparison = reactive({
  current: 156,
  previous: 142,
  change: 14,
  changePercent: 9.9
})

// 导出报告
const handleExport = () => {
  ElMessage.info('导出统计报告功能开发中...')
}

// 刷新数据
const handleRefresh = () => {
  ElMessage.success('数据已刷新')
}

// 筛选
const handleFilter = () => {
  ElMessage.info('筛选功能开发中...')
}

// 重置筛选
const handleResetFilter = () => {
  Object.assign(filterForm, {
    changeType: '',
    department: '',
    dateRange: []
  })
}

// 查看详情
   
const handleViewDetail = (row: unknown) => {
  ElMessage.info(`查看 ${row.department} 的详细统计信息`)
}

// 获取变动率标签
const getChangeRateTag = (rate: number) => {
  if (rate > 15) return 'danger'
  if (rate > 10) return 'warning'
  return 'success'
}

// 获取趋势标签
const getTrendTag = (trend: string) => {
  switch (trend) {
    case 'UP': return 'danger'
    case 'DOWN': return 'success'
    case 'STABLE': return 'warning'
    default: return ''
  }
}

// 获取趋势文本
const getTrendText = (trend: string) => {
  switch (trend) {
    case 'UP': return '上升'
    case 'DOWN': return '下降'
    case 'STABLE': return '稳定'
    default: return trend
  }
}

// 获取部门列表
const fetchDepartments = async () => {
  try {
    const response = await organizationApi.getTree()
    if (response && Array.isArray(response)) {
      departmentOptions.value = response.map(dept => ({
        id: dept.id,
        name: dept.name,
        orgCode: dept.orgCode
      }))
    }
  } catch (__error) {
    console.error('获取部门列表失败:', error)
    ElMessage.error('获取部门列表失败')
  }
}

// 初始化
onMounted(() => {
  fetchDepartments()
  // 初始化图表和数据
})
</script>

<style scoped>
.personnel-change-statistics {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.overview-stats {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.onboarding {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.leaving {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.adjustment {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.charts-row {
  margin-bottom: 20px;
}

.chart-card {
  height: 350px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 280px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  color: #909399;
  font-size: 14px;
}

.table-card {
  margin-top: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.filter-form {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.comparison-card {
  margin-top: 20px;
}

.comparison-item {
  text-align: center;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fafafa;
}

.comparison-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 12px;
}

.comparison-number {
  margin-bottom: 8px;
}

.comparison-number .current {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
}

.comparison-number .vs {
  margin: 0 8px;
  color: #909399;
  font-size: 14px;
}

.comparison-number .previous {
  font-size: 20px;
  color: #606266;
}

.comparison-change {
  margin-top: 8px;
}
</style>
