<template>
  <div class="training-venue">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>培训场地管理</h3>
          <div class="header-actions">
            <el-button @click="handleCalendarView">日历视图</el-button>
            <el-button type="primary" @click="handleCreate">新增场地</el-button>
          </div>
        </div>
      </template>

      <!-- 筛选区域 -->
      <div class="filter-section">
        <el-form :model="filterForm" :inline="true">
          <el-form-item label="场地类型">
            <el-select v-model="filterForm.type" @change="handleFilterChange">
              <el-option label="全部" value=""  />
              <el-option label="多媒体教室" value="multimedia"  />
              <el-option label="会议室" value="meeting"  />
              <el-option label="实训室" value="practical"  />
              <el-option label="阶梯教室" value="lecture"  />
              <el-option label="在线教室" value="online"  />
            </el-select>
          </el-form-item>
          <el-form-item label="容量范围">
            <el-select v-model="filterForm.capacityRange" @change="handleFilterChange">
              <el-option label="全部" value=""  />
              <el-option label="1-20人" value="small"  />
              <el-option label="21-50人" value="medium"  />
              <el-option label="51-100人" value="large"  />
              <el-option label="100人以上" value="extra"  />
            </el-select>
          </el-form-item>
          <el-form-item label="设备要求">
            <el-select v-model="filterForm.equipment" @change="handleFilterChange" multiple>
              <el-option label="投影仪" value="projector"  />
              <el-option label="音响设备" value="audio"  />
              <el-option label="白板" value="whiteboard"  />
              <el-option label="电脑" value="computer"  />
              <el-option label="WiFi" value="wifi"  />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="filterForm.status" @change="handleFilterChange">
              <el-option label="全部" value=""  />
              <el-option label="可用" value="available"  />
              <el-option label="占用中" value="occupied"  />
              <el-option label="维护中" value="maintenance"  />
              <el-option label="停用" value="disabled"  />
            </el-select>
          </el-form-item>
          <el-form-item label="关键词">
            <el-input
              v-model="filterForm.keyword"
              placeholder="搜索场地名称、位置"
              @change="handleFilterChange"
              clearable
              />
          </el-form-item>
        </el-form>
      </div>

      <!-- 场地列表 -->
      <div class="venue-list-section">
        <el-table
          :data="venueList"
          v-loading="loading"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55"  />
          <el-table-column prop="venueName" label="场地名称" min-width="150">
            <template #default="scope">
              <div class="venue-info">
                <div class="venue-name">
                  <el-link @click="handleViewVenue(scope.row)">
                    {{ scope.row.venueName }}
                  </el-link>
                </div>
                <div class="venue-location">
                  <el-icon><LocationOutlined /></el-icon>
                  {{ scope.row.location }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="type" label="场地类型" align="center">
            <template #default="scope">
              <el-tag :type="getTypeColor(scope.row.type)">
                {{ getTypeText(scope.row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="capacity" label="容量" align="center">
            <template #default="scope">
              {{ scope.row.capacity }}人
            </template>
          </el-table-column>
          <el-table-column prop="equipment" label="设备配置" min-width="200">
            <template #default="scope">
              <div class="equipment-tags">
                <el-tag
                  v-for="item in scope.row.equipment"
                  :key="item"
                  size="small"
                  style="margin: 2px;"
                >
                  {{ getEquipmentText(item) }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="hourlyRate" label="时租费用" align="center">
            <template #default="scope">
              <span v-if="scope.row.hourlyRate === 0">免费</span>
              <span v-else>¥{{ scope.row.hourlyRate }}/小时</span>
            </template>
          </el-table-column>
          <el-table-column prop="currentBooking" label="当前状态" align="center">
            <template #default="scope">
              <div v-if="scope.row.currentBooking">
                <el-tag type="warning">占用中</el-tag>
                <div class="booking-info">
                  {{ scope.row.currentBooking.courseName }}
                  <br>
                  {{ formatTimeRange(scope.row.currentBooking.startTime, scope.row.currentBooking.endTime) }}
                </div>
              </div>
              <el-tag v-else :type="getStatusColor(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="utilizationRate" label="使用率" align="center">
            <template #default="scope">
              <el-progress
                :percentage="scope.row.utilizationRate"
                :stroke-width="8"
                :color="getUtilizationColor(scope.row.utilizationRate)"
               />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button type="text" @click="handleViewVenue(scope.row)">
                查看
              </el-button>
              <el-button type="text" @click="handleEditVenue(scope.row)">
                编辑
              </el-button>
              <el-button type="text" @click="handleBookVenue(scope.row)">
                预订
              </el-button>
              <el-button type="text" @click="handleViewSchedule(scope.row)">
                排期
              </el-button>
              <el-button
                type="text"
                @click="handleDeleteVenue(scope.row)"
                style="color: #f56c6c"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
           />
        </div>
      </div>
    </el-card>

    <!-- 场地详情/编辑对话框 -->
    <el-dialog
      v-model="venueDialog.visible"
      :title="venueDialog.title"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="venue-form" v-if="venueDialog.data">
        <el-form
          :model="venueDialog.data"
          :rules="venueRules"
          ref="venueFormRef"
          label-width="120px"
        >
          <!-- 基本信息 -->
          <div class="form-section">
            <h4>基本信息</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="场地名称" prop="venueName">
                  <el-input
                    v-model="venueDialog.data.venueName"
                    placeholder="请输入场地名称"
                    :readonly="venueDialog.mode === 'view'"
                    />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="场地编号" prop="venueCode">
                  <el-input
                    v-model="venueDialog.data.venueCode"
                    placeholder="请输入场地编号"
                    :readonly="venueDialog.mode === 'view'"
                    />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="场地类型" prop="type">
                  <el-select
                    v-model="venueDialog.data.type"
                    :disabled="venueDialog.mode === 'view'"
                  >
                    <el-option label="多媒体教室" value="multimedia"  />
                    <el-option label="会议室" value="meeting"  />
                    <el-option label="实训室" value="practical"  />
                    <el-option label="阶梯教室" value="lecture"  />
                    <el-option label="在线教室" value="online"  />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="容量" prop="capacity">
                  <el-input-number
                    v-model="venueDialog.data.capacity"
                    :min="1"
                    :max="999"
                    :disabled="venueDialog.mode === 'view'"
                    />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="面积(㎡)" prop="area">
                  <el-input-number
                    v-model="venueDialog.data.area"
                    :min="1"
                    :max="9999"
                    :precision="1"
                    :disabled="venueDialog.mode === 'view'"
                    />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="所在位置" prop="location">
                  <el-input
                    v-model="venueDialog.data.location"
                    placeholder="请输入详细位置"
                    :readonly="venueDialog.mode === 'view'"
                    />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="楼层" prop="floor">
                  <el-input
                    v-model="venueDialog.data.floor"
                    placeholder="请输入楼层"
                    :readonly="venueDialog.mode === 'view'"
                    />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="场地描述">
              <el-input
                v-model="venueDialog.data.description"
                type="textarea"
                :rows="3"
                placeholder="请输入场地描述"
                :readonly="venueDialog.mode === 'view'"
                />
            </el-form-item>
          </div>

          <!-- 设备配置 -->
          <div class="form-section">
            <h4>设备配置</h4>
            <el-form-item label="基础设备">
              <el-checkbox-group
                v-model="venueDialog.data.equipment"
                :disabled="venueDialog.mode === 'view'"
              >
                <el-checkbox label="projector">投影仪</el-checkbox>
                <el-checkbox label="audio">音响设备</el-checkbox>
                <el-checkbox label="whiteboard">白板</el-checkbox>
                <el-checkbox label="computer">电脑</el-checkbox>
                <el-checkbox label="wifi">WiFi</el-checkbox>
                <el-checkbox label="ac">空调</el-checkbox>
                <el-checkbox label="camera">摄像设备</el-checkbox>
                <el-checkbox label="microphone">话筒</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="特殊设备">
              <el-input
                v-model="venueDialog.data.specialEquipment"
                type="textarea"
                :rows="2"
                placeholder="请输入特殊设备说明"
                :readonly="venueDialog.mode === 'view'"
                />
            </el-form-item>
          </div>

          <!-- 使用规则 -->
          <div class="form-section">
            <h4>使用规则</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="开放时间">
                  <el-time-picker
                    v-model="venueDialog.data.openTime"
                    is-range
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    format="HH:mm"
                    value-format="HH:mm"
                    :disabled="venueDialog.mode === 'view'"
                   />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="时租费用">
                  <el-input-number
                    v-model="venueDialog.data.hourlyRate"
                    :min="0"
                    :max="9999"
                    :precision="2"
                    :disabled="venueDialog.mode === 'view'"
                    />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="预订规则">
              <el-checkbox-group
                v-model="venueDialog.data.bookingRules"
                :disabled="venueDialog.mode === 'view'"
              >
                <el-checkbox label="advance">需要提前预订</el-checkbox>
                <el-checkbox label="approval">需要审批</el-checkbox>
                <el-checkbox label="deposit">需要押金</el-checkbox>
                <el-checkbox label="weekends">周末可用</el-checkbox>
                <el-checkbox label="holidays">节假日可用</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="使用须知">
              <el-input
                v-model="venueDialog.data.usageNotes"
                type="textarea"
                :rows="3"
                placeholder="请输入使用须知"
                :readonly="venueDialog.mode === 'view'"
                />
            </el-form-item>
          </div>

          <!-- 管理信息 -->
          <div class="form-section">
            <h4>管理信息</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="负责人" prop="manager">
                  <el-input
                    v-model="venueDialog.data.manager"
                    placeholder="请输入负责人姓名"
                    :readonly="venueDialog.mode === 'view'"
                    />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系电话" prop="managerPhone">
                  <el-input
                    v-model="venueDialog.data.managerPhone"
                    placeholder="请输入联系电话"
                    :readonly="venueDialog.mode === 'view'"
                    />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="状态" prop="status">
                  <el-select
                    v-model="venueDialog.data.status"
                    :disabled="venueDialog.mode === 'view'"
                  >
                    <el-option label="可用" value="available"  />
                    <el-option label="占用中" value="occupied"  />
                    <el-option label="维护中" value="maintenance"  />
                    <el-option label="停用" value="disabled"  />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="维护日期">
                  <el-date-picker
                    v-model="venueDialog.data.maintenanceDate"
                    type="date"
                    placeholder="选择维护日期"
                    :disabled="venueDialog.mode === 'view'"
                   />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 统计信息 -->
          <div v-if="venueDialog.mode === 'view'" class="form-section">
            <h4>统计信息</h4>
            <el-row :gutter="20">
              <el-col :span="6">
                <el-statistic title="本月使用次数" :value="venueDialog.data.monthlyUsage || 0"  />
              </el-col>
              <el-col :span="6">
                <el-statistic title="本月使用时长" :value="venueDialog.data.monthlyHours || 0" suffix="小时"  />
              </el-col>
              <el-col :span="6">
                <el-statistic title="使用率" :value="venueDialog.data.utilizationRate || 0" suffix="%"  />
              </el-col>
              <el-col :span="6">
                <el-statistic title="收入" :value="venueDialog.data.monthlyRevenue || 0" prefix="¥"  />
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="venueDialog.visible = false">
          {{ venueDialog.mode === 'view' ? '关闭' : '取消' }}
        </el-button>
        <el-button
          v-if="venueDialog.mode === 'edit'"
          type="primary"
          @click="handleSaveVenue"
        >
          保存
        </el-button>
      </template>
    </el-dialog>

    <!-- 预订对话框 -->
    <el-dialog
      v-model="bookingDialog.visible"
      title="场地预订"
      width="60%"
      :close-on-click-modal="false"
    >
      <el-form
        :model="bookingForm"
        :rules="bookingRules"
        ref="bookingFormRef"
        label-width="120px"
      >
        <el-form-item label="课程名称" prop="courseName">
          <el-input
            v-model="bookingForm.courseName"
            placeholder="请输入课程名称"
            />
        </el-form-item>
        <el-form-item label="使用时间" prop="timeRange">
          <el-date-picker
            v-model="bookingForm.timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
           />
        </el-form-item>
        <el-form-item label="预计人数" prop="expectedAttendees">
          <el-input-number
            v-model="bookingForm.expectedAttendees"
            :min="1"
            :max="999"
            />
        </el-form-item>
        <el-form-item label="申请人" prop="applicant">
          <el-input
            v-model="bookingForm.applicant"
            placeholder="请输入申请人姓名"
            />
        </el-form-item>
        <el-form-item label="联系方式" prop="phone">
          <el-input
            v-model="bookingForm.phone"
            placeholder="请输入联系方式"
            />
        </el-form-item>
        <el-form-item label="使用用途">
          <el-input
            v-model="bookingForm.purpose"
            type="textarea"
            :rows="2"
            placeholder="请输入使用用途"
            />
        </el-form-item>
        <el-form-item label="特殊需求">
          <el-input
            v-model="bookingForm.specialRequirements"
            type="textarea"
            :rows="2"
            placeholder="请输入特殊需求"
            />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="bookingDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmitBooking">
          提交预订
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'TrainingVenue'
})
 
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { LocationOutlined } from '@element-plus/icons-vue'

// 接口定义
interface TrainingVenue {
  id?: string
  venueName: string
  venueCode: string
  type: string
  capacity: number
  area: number
  location: string
  floor: string
  description?: string
  equipment: string[]
  specialEquipment?: string
  openTime?: string[]
  hourlyRate: number
  bookingRules: string[]
  usageNotes?: string
  manager: string
  managerPhone: string
  status: string
  maintenanceDate?: Date
  monthlyUsage?: number
  monthlyHours?: number
  utilizationRate?: number
  monthlyRevenue?: number
  currentBooking?: {
    courseName: string
    startTime: Date
    endTime: Date
  }
}

// 响应式数据
const loading = ref(false)
const selectedVenues = ref<TrainingVenue[]>([])

const filterForm = reactive({
  type: '',
  capacityRange: '',
  equipment: [],
  status: '',
  keyword: ''
})

const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

const venueList = ref<TrainingVenue[]>([])

const venueDialog = reactive({
  visible: false,
  title: '',
  mode: 'view' as 'view' | 'edit',
  data: null as TrainingVenue | null
})

const bookingDialog = reactive({
  visible: false
})

const bookingForm = reactive({
  courseName: '',
  timeRange: [],
  expectedAttendees: 1,
  applicant: '',
  phone: '',
  purpose: '',
  specialRequirements: ''
})

const venueRules = {
  venueName: [{ required: true, message: '请输入场地名称', trigger: 'blur' }],
  venueCode: [{ required: true, message: '请输入场地编号', trigger: 'blur' }],
  type: [{ required: true, message: '请选择场地类型', trigger: 'change' }],
  capacity: [{ required: true, message: '请输入容量', trigger: 'blur' }],
  location: [{ required: true, message: '请输入所在位置', trigger: 'blur' }],
  manager: [{ required: true, message: '请输入负责人', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

const bookingRules = {
  courseName: [{ required: true, message: '请输入课程名称', trigger: 'blur' }],
  timeRange: [{ required: true, message: '请选择使用时间', trigger: 'change' }],
  expectedAttendees: [{ required: true, message: '请输入预计人数', trigger: 'blur' }],
  applicant: [{ required: true, message: '请输入申请人', trigger: 'blur' }],
  phone: [{ required: true, message: '请输入联系方式', trigger: 'blur' }]
}

const venueFormRef = ref()
const bookingFormRef = ref()

// 方法
const handleFilterChange = () => {
  loadVenueList()
}

const handleSelectionChange = (selection: TrainingVenue[]) => {
  selectedVenues.value = selection
}

const handleCalendarView = () => {
  ElMessage.info('日历视图功能开发中...')
}

const handleCreate = () => {
  venueDialog.data = {
    venueName: '',
    venueCode: '',
    type: 'multimedia',
    capacity: 30,
    area: 50,
    location: '',
    floor: '',
    equipment: [],
    hourlyRate: 0,
    bookingRules: [],
    manager: '',
    managerPhone: '',
    status: 'available'
  }
  venueDialog.title = '新增培训场地'
  venueDialog.mode = 'edit'
  venueDialog.visible = true
}

const handleViewVenue = (row: TrainingVenue) => {
  venueDialog.data = {
    ...row,
    equipment: row.equipment || ['projector', 'audio', 'whiteboard'],
    bookingRules: row.bookingRules || ['advance', 'approval']
  }
  venueDialog.title = '查看培训场地'
  venueDialog.mode = 'view'
  venueDialog.visible = true
}

const handleEditVenue = (row: TrainingVenue) => {
  venueDialog.data = { ...row }
  venueDialog.title = '编辑培训场地'
  venueDialog.mode = 'edit'
  venueDialog.visible = true
}

const handleBookVenue = (row: TrainingVenue) => {
  if (row.status !== 'available') {
    ElMessage.warning('该场地当前不可预订')
    return
  }
  
  // 重置预订表单
  Object.assign(bookingForm, {
    courseName: '',
    timeRange: [],
    expectedAttendees: 1,
    applicant: '',
    phone: '',
    purpose: '',
    specialRequirements: ''
  })
  
  bookingDialog.visible = true
}

const handleViewSchedule = (row: TrainingVenue) => {
  ElMessage.info(`查看${row.venueName}的排期安排`)
}

const handleDeleteVenue = async (row: TrainingVenue) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除场地"${row.venueName}"吗？`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )
    
    loading.value = true
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('场地删除成功')
    loadVenueList()
  } catch (__error) {
    // 用户取消
  } finally {
    loading.value = false
  }
}

const handleSaveVenue = async () => {
  if (!venueFormRef.value) return
  
  try {
    await venueFormRef.value.validate()
    loading.value = true
    
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('场地信息保存成功')
    venueDialog.visible = false
    loadVenueList()
  } catch (__error) {
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

const handleSubmitBooking = async () => {
  if (!bookingFormRef.value) return
  
  try {
    await bookingFormRef.value.validate()
    loading.value = true
    
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('预订申请提交成功')
    bookingDialog.visible = false
    loadVenueList()
  } catch (__error) {
    ElMessage.error('提交失败')
  } finally {
    loading.value = false
  }
}

const loadVenueList = async () => {
  try {
    loading.value = true
    
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    venueList.value = [
      {
        id: '1',
        venueName: '多媒体教室A',
        venueCode: 'ROOM-A001',
        type: 'multimedia',
        capacity: 50,
        area: 80,
        location: '教学楼A座',
        floor: '3楼',
        description: '配备先进的多媒体设备，适合各种培训课程',
        equipment: ['projector', 'audio', 'whiteboard', 'computer', 'wifi'],
        hourlyRate: 100,
        bookingRules: ['advance', 'approval'],
        manager: '张老师',
        managerPhone: '13800138001',
        status: 'available',
        monthlyUsage: 25,
        monthlyHours: 180,
        utilizationRate: 75,
        monthlyRevenue: 18000
      },
      {
        id: '2',
        venueName: '会议室B',
        venueCode: 'ROOM-B001',
        type: 'meeting',
        capacity: 20,
        area: 40,
        location: '办公楼B座',
        floor: '5楼',
        description: '小型会议室，适合小班培训',
        equipment: ['projector', 'audio', 'whiteboard', 'wifi'],
        hourlyRate: 50,
        bookingRules: ['advance'],
        manager: '李老师',
        managerPhone: '13800138002',
        status: 'occupied',
        currentBooking: {
          courseName: '项目管理培训',
          startTime: new Date('2025-01-20 14:00'),
          endTime: new Date('2025-01-20 17:00')
        },
        monthlyUsage: 18,
        monthlyHours: 120,
        utilizationRate: 60,
        monthlyRevenue: 6000
      },
      {
        id: '3',
        venueName: '实训室C',
        venueCode: 'ROOM-C001',
        type: 'practical',
        capacity: 30,
        area: 100,
        location: '实训楼C座',
        floor: '2楼',
        description: '实操培训专用教室',
        equipment: ['computer', 'wifi', 'camera'],
        hourlyRate: 150,
        bookingRules: ['advance', 'approval', 'deposit'],
        manager: '王老师',
        managerPhone: '13800138003',
        status: 'maintenance',
        monthlyUsage: 12,
        monthlyHours: 96,
        utilizationRate: 40,
        monthlyRevenue: 14400
      }
    ]
    
    pagination.total = venueList.value.length
  } catch (__error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  loadVenueList()
}

const handleCurrentChange = (current: number) => {
  pagination.current = current
  loadVenueList()
}

// 辅助方法
const getTypeColor = (type: string) => {
  const colorMap = {
    multimedia: 'primary',
    meeting: 'success',
    practical: 'warning',
    lecture: 'info',
    online: 'danger'
  }
  return colorMap[type] || 'info'
}

const getTypeText = (type: string) => {
  const textMap = {
    multimedia: '多媒体教室',
    meeting: '会议室',
    practical: '实训室',
    lecture: '阶梯教室',
    online: '在线教室'
  }
  return textMap[type] || '未知'
}

const getEquipmentText = (equipment: string) => {
  const textMap = {
    projector: '投影仪',
    audio: '音响',
    whiteboard: '白板',
    computer: '电脑',
    wifi: 'WiFi',
    ac: '空调',
    camera: '摄像设备',
    microphone: '话筒'
  }
  return textMap[equipment] || equipment
}

const getStatusColor = (status: string) => {
  const colorMap = {
    available: 'success',
    occupied: 'warning',
    maintenance: 'danger',
    disabled: 'info'
  }
  return colorMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap = {
    available: '可用',
    occupied: '占用中',
    maintenance: '维护中',
    disabled: '停用'
  }
  return textMap[status] || '未知'
}

const getUtilizationColor = (rate: number) => {
  if (rate >= 80) return '#67c23a'
  if (rate >= 60) return '#409eff'
  if (rate >= 40) return '#e6a23c'
  return '#f56c6c'
}

const formatTimeRange = (startTime: Date, endTime: Date) => {
  if (!startTime || !endTime) return '-'
  const start = new Date(startTime).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  const end = new Date(endTime).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  return `${start}-${end}`
}

// 生命周期
onMounted(() => {
  loadVenueList()
})
</script>

<style scoped>
.training-venue {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.venue-list-section {
  margin-bottom: 20px;
}

.venue-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.venue-name {
  font-weight: 500;
}

.venue-location {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #666;
  font-size: 12px;
}

.equipment-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
}

.booking-info {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.venue-form {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.form-section h4 {
  color: #409eff;
  margin-bottom: 20px;
  border-bottom: 2px solid #409eff;
  padding-bottom: 5px;
}
</style>