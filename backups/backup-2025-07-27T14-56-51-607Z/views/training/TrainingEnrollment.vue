<template>
  <div class="training-enrollment">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>培训报名管理</h3>
          <div class="header-actions">
            <el-button @click="handleBatchApprove">批量审批</el-button>
            <el-button type="primary" @click="handleExport">导出报名表</el-button>
          </div>
        </div>
      </template>

      <!-- 筛选区域 -->
      <div class="filter-section">
        <el-form :model="filterForm" :inline="true">
          <el-form-item label="课程名称">
            <el-select v-model="filterForm.courseId" @change="handleFilterChange">
              <el-option label="全部课程" value=""  />
              <el-option
                v-for="course in courses"
                :key="course.id"
                :label="course.name"
                :value="course.id"
               />
            </el-select>
          </el-form-item>
          <el-form-item label="报名状态">
            <el-select v-model="filterForm.status" @change="handleFilterChange">
              <el-option label="全部状态" value=""  />
              <el-option label="待审批" value="pending"  />
              <el-option label="已通过" value="approved"  />
              <el-option label="已拒绝" value="rejected"  />
              <el-option label="等待中" value="waiting"  />
              <el-option label="已取消" value="cancelled"  />
            </el-select>
          </el-form-item>
          <el-form-item label="部门">
            <el-select v-model="filterForm.department" @change="handleFilterChange">
              <el-option label="全部部门" value=""  />
              <el-option
                v-for="dept in departments"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
               />
            </el-select>
          </el-form-item>
          <el-form-item label="报名时间">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handleFilterChange"
             />
          </el-form-item>
        </el-form>
      </div>

      <!-- 报名列表 -->
      <div class="enrollment-list-section">
        <el-table
          :data="enrollmentList"
          v-loading="loading"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55"  />
          <el-table-column prop="enrollmentId" label="报名编号" width="120"  />
          <el-table-column prop="courseName" label="课程名称" min-width="200">
            <template #default="scope">
              <el-link @click="handleViewCourse(scope.row.courseId)">
                {{ scope.row.courseName }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column prop="applicantName" label="报名人"  />
          <el-table-column prop="department" label="部门"  />
          <el-table-column prop="position" label="职位"  />
          <el-table-column prop="phone" label="联系方式"  />
          <el-table-column prop="enrollmentDate" label="报名时间" align="center">
            <template #default="scope">
              {{ formatDateTime(scope.row.enrollmentDate) }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" align="center">
            <template #default="scope">
              <el-tag :type="getStatusColor(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="priority" label="优先级" align="center">
            <template #default="scope">
              <el-tag :type="getPriorityColor(scope.row.priority)" size="small">
                {{ getPriorityText(scope.row.priority) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button type="text" @click="handleViewEnrollment(scope.row)">
                查看
              </el-button>
              <el-button
                v-if="scope.row.status === 'pending'"
                type="text"
                @click="handleApprove(scope.row)"
              >
                审批
              </el-button>
              <el-button
                v-if="scope.row.status === 'approved'"
                type="text"
                @click="handleNotify(scope.row)"
              >
                通知
              </el-button>
              <el-button
                type="text"
                @click="handleEditEnrollment(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                type="text"
                @click="handleDeleteEnrollment(scope.row)"
                style="color: #f56c6c"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
           />
        </div>
      </div>
    </el-card>

    <!-- 报名详情对话框 -->
    <el-dialog
      v-model="detailDialog.visible"
      :title="detailDialog.title"
      width="70%"
      :close-on-click-modal="false"
    >
      <div class="enrollment-detail" v-if="detailDialog.data">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="报名编号">
            {{ detailDialog.data.enrollmentId }}
          </el-descriptions-item>
          <el-descriptions-item label="课程名称">
            {{ detailDialog.data.courseName }}
          </el-descriptions-item>
          <el-descriptions-item label="报名人">
            {{ detailDialog.data.applicantName }}
          </el-descriptions-item>
          <el-descriptions-item label="联系方式">
            {{ detailDialog.data.phone }}
          </el-descriptions-item>
          <el-descriptions-item label="部门">
            {{ detailDialog.data.department }}
          </el-descriptions-item>
          <el-descriptions-item label="职位">
            {{ detailDialog.data.position }}
          </el-descriptions-item>
          <el-descriptions-item label="报名时间">
            {{ formatDateTime(detailDialog.data.enrollmentDate) }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusColor(detailDialog.data.status)">
              {{ getStatusText(detailDialog.data.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="报名理由" :span="2">
            {{ detailDialog.data.reason || '无' }}
          </el-descriptions-item>
        </el-descriptions>

        <div v-if="detailDialog.data.approvalHistory" class="approval-history">
          <h4>审批历史</h4>
          <el-timeline>
            <el-timeline-item
              v-for="record in detailDialog.data.approvalHistory"
              :key="record.id"
              :timestamp="formatDateTime(record.date)"
              :type="getTimelineType(record.action)"
            >
              <el-card>
                <h4>{{ record.action }}</h4>
                <p>审批人：{{ record.approver }}</p>
                <p v-if="record.comment">备注：{{ record.comment }}</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>

      <template #footer>
        <el-button @click="detailDialog.visible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 审批对话框 -->
    <el-dialog
      v-model="approvalDialog.visible"
      title="审批报名"
      width="50%"
      :close-on-click-modal="false"
    >
      <div class="approval-form" v-if="approvalDialog.data">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="课程名称">
            {{ approvalDialog.data.courseName }}
          </el-descriptions-item>
          <el-descriptions-item label="报名人">
            {{ approvalDialog.data.applicantName }}
          </el-descriptions-item>
          <el-descriptions-item label="部门">
            {{ approvalDialog.data.department }}
          </el-descriptions-item>
          <el-descriptions-item label="职位">
            {{ approvalDialog.data.position }}
          </el-descriptions-item>
        </el-descriptions>

        <div class="approval-section">
          <h4>审批决定</h4>
          <el-radio-group v-model="approvalForm.decision">
            <el-radio label="approve">通过</el-radio>
            <el-radio label="reject">拒绝</el-radio>
            <el-radio label="wait">进入等待列表</el-radio>
          </el-radio-group>
        </div>

        <div class="comment-section">
          <h4>审批意见</h4>
          <el-input
            v-model="approvalForm.comment"
            type="textarea"
            :rows="3"
            placeholder="请输入审批意见（可选）"
            />
        </div>

        <div v-if="approvalForm.decision === 'approve'" class="notification-section">
          <h4>通知设置</h4>
          <el-checkbox-group v-model="approvalForm.notifications">
            <el-checkbox label="email">邮件通知</el-checkbox>
            <el-checkbox label="sms">短信通知</el-checkbox>
            <el-checkbox label="system">系统通知</el-checkbox>
          </el-checkbox-group>
        </div>
      </div>

      <template #footer>
        <el-button @click="approvalDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirmApproval">确认审批</el-button>
      </template>
    </el-dialog>

    <!-- 等待列表对话框 -->
    <el-dialog
      v-model="waitingDialog.visible"
      title="等待列表管理"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="waiting-list">
        <div class="waiting-stats">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-statistic title="等待人数" :value="waitingList.length"  />
            </el-col>
            <el-col :span="8">
              <el-statistic title="可补录人数" :value="availableSlots"  />
            </el-col>
            <el-col :span="8">
              <el-statistic title="预计开班" :value="'2025-02-01'"  />
            </el-col>
          </el-row>
        </div>

        <el-table :data="waitingList" style="margin-top: 20px;">
          <el-table-column type="selection" width="55"  />
          <el-table-column prop="rank" label="排名" width="80" align="center"  />
          <el-table-column prop="applicantName" label="报名人"  />
          <el-table-column prop="department" label="部门"  />
          <el-table-column prop="priority" label="优先级" align="center">
            <template #default="scope">
              <el-tag :type="getPriorityColor(scope.row.priority)" size="small">
                {{ getPriorityText(scope.row.priority) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="waitingDate" label="等待时间" align="center">
            <template #default="scope">
              {{ formatDateTime(scope.row.waitingDate) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150">
            <template #default="scope">
              <el-button type="text" @click="handlePromote(scope.row)">
                提升
              </el-button>
              <el-button type="text" @click="handleRemoveFromWaiting(scope.row)">
                移除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <template #footer>
        <el-button @click="waitingDialog.visible = false">关闭</el-button>
        <el-button type="primary" @click="handleBatchPromote">批量提升</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 接口定义
interface EnrollmentRecord {
  id: string
  enrollmentId: string
  courseId: string
  courseName: string
  applicantId: string
  applicantName: string
  department: string
  position: string
  phone: string
  enrollmentDate: Date
  status: string
  priority: string
  reason?: string
  approvalHistory?: Array<{
    id: string
    action: string
    approver: string
    date: Date
    comment?: string
  }>
}

// 响应式数据
const loading = ref(false)
const selectedEnrollments = ref<EnrollmentRecord[]>([])
const availableSlots = ref(5)

const courses = ref([
  { id: '1', name: 'HrVue.js 高级开发' },
  { id: '2', name: '项目管理实战' },
  { id: '3', name: '新员工培训' }
])

const departments = ref([
  { id: '1', name: '技术部' },
  { id: '2', name: '销售部' },
  { id: '3', name: '市场部' }
])

const filterForm = reactive({
  courseId: '',
  status: '',
  department: '',
  dateRange: []
})

const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

const enrollmentList = ref<EnrollmentRecord[]>([])
const waitingList = ref([])

const detailDialog = reactive({
  visible: false,
  title: '',
  data: null as EnrollmentRecord | null
})

const approvalDialog = reactive({
  visible: false,
  data: null as EnrollmentRecord | null
})

const approvalForm = reactive({
  decision: 'approve',
  comment: '',
  notifications: ['system']
})

const waitingDialog = reactive({
  visible: false
})

// 方法
const handleFilterChange = () => {
  loadEnrollmentList()
}

const handleSelectionChange = (selection: EnrollmentRecord[]) => {
  selectedEnrollments.value = selection
}

const handleBatchApprove = () => {
  if (selectedEnrollments.value.length === 0) {
    ElMessage.warning('请先选择要审批的报名记录')
    return
  }
  ElMessage.info('批量审批功能开发中...')
}

const handleExport = () => {
  ElMessage.success('正在导出报名表...')
}

const handleViewCourse = (courseId: string) => {
  ElMessage.info(`查看课程: ${courseId}`)
}

const handleViewEnrollment = (row: EnrollmentRecord) => {
  detailDialog.data = {
    ...row,
    approvalHistory: [
      {
        id: '1',
        action: '提交报名',
        approver: row.applicantName,
        date: row.enrollmentDate,
        comment: '在线提交报名申请'
      },
      {
        id: '2',
        action: '审批通过',
        approver: '人事部',
        date: new Date(),
        comment: '符合培训要求，同意参加'
      }
    ]
  }
  detailDialog.title = '报名详情'
  detailDialog.visible = true
}

const handleApprove = (row: EnrollmentRecord) => {
  approvalDialog.data = row
  approvalForm.decision = 'approve'
  approvalForm.comment = ''
  approvalForm.notifications = ['system']
  approvalDialog.visible = true
}

const handleConfirmApproval = async () => {
  if (!approvalDialog.data) return
  
  try {
    loading.value = true
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const statusMap = {
      approve: 'approved',
      reject: 'rejected',
      wait: 'waiting'
    }
    
    const actionMap = {
      approve: '审批通过',
      reject: '审批拒绝',
      wait: '进入等待列表'
    }
    
    ElMessage.success(`${actionMap[approvalForm.decision]}成功`)
    approvalDialog.visible = false
    loadEnrollmentList()
  } catch (__error) {
    ElMessage.error('审批失败')
  } finally {
    loading.value = false
  }
}

const handleNotify = (row: EnrollmentRecord) => {
  ElMessage.success(`已发送通知给 ${row.applicantName}`)
}

const handleEditEnrollment = (row: EnrollmentRecord) => {
  ElMessage.info('编辑报名信息功能开发中...')
}

const handleDeleteEnrollment = async (row: EnrollmentRecord) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除${row.applicantName}的报名记录吗？`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )
    
    loading.value = true
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('删除成功')
    loadEnrollmentList()
  } catch (__error) {
    // 用户取消
  } finally {
    loading.value = false
  }
}

   
const handlePromote = (row: unknown) => {
  ElMessage.success(`${row.applicantName}已提升到正式名单`)
  loadWaitingList()
}

   
const handleRemoveFromWaiting = (row: unknown) => {
  ElMessage.success(`${row.applicantName}已从等待列表中移除`)
  loadWaitingList()
}

const handleBatchPromote = () => {
  ElMessage.success('批量提升成功')
  loadWaitingList()
}

const loadEnrollmentList = async () => {
  try {
    loading.value = true
    
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    enrollmentList.value = [
      {
        id: '1',
        enrollmentId: 'ENR001',
        courseId: '1',
        courseName: 'Vue.js 高级开发',
        applicantId: '1',
        applicantName: '张三',
        department: '技术部',
        position: '前端工程师',
        phone: '13800138001',
        enrollmentDate: new Date('2025-01-15'),
        status: 'pending',
        priority: 'high',
        reason: '希望提升Vue.js技术能力'
      },
      {
        id: '2',
        enrollmentId: 'ENR002',
        courseId: '1',
        courseName: 'Vue.js 高级开发',
        applicantId: '2',
        applicantName: '李四',
        department: '技术部',
        position: '全栈工程师',
        phone: '13800138002',
        enrollmentDate: new Date('2025-01-16'),
        status: 'approved',
        priority: 'medium',
        reason: '项目需要使用Vue.js框架'
      },
      {
        id: '3',
        enrollmentId: 'ENR003',
        courseId: '2',
        courseName: '项目管理实战',
        applicantId: '3',
        applicantName: '王五',
        department: '销售部',
        position: '销售经理',
        phone: '13800138003',
        enrollmentDate: new Date('2025-01-17'),
        status: 'waiting',
        priority: 'low',
        reason: '提升项目管理能力'
      }
    ]
    
    pagination.total = enrollmentList.value.length
  } catch (__error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const loadWaitingList = async () => {
  waitingList.value = [
    {
      id: '1',
      rank: 1,
      applicantName: '王五',
      department: '销售部',
      priority: 'high',
      waitingDate: new Date('2025-01-17')
    },
    {
      id: '2',
      rank: 2,
      applicantName: '赵六',
      department: '市场部',
      priority: 'medium',
      waitingDate: new Date('2025-01-18')
    }
  ]
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  loadEnrollmentList()
}

const handleCurrentChange = (current: number) => {
  pagination.current = current
  loadEnrollmentList()
}

// 辅助方法
const getStatusColor = (status: string) => {
  const colorMap = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger',
    waiting: 'info',
    cancelled: 'info'
  }
  return colorMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap = {
    pending: '待审批',
    approved: '已通过',
    rejected: '已拒绝',
    waiting: '等待中',
    cancelled: '已取消'
  }
  return textMap[status] || '未知'
}

const getPriorityColor = (priority: string) => {
  const colorMap = {
    high: 'danger',
    medium: 'warning',
    low: 'success'
  }
  return colorMap[priority] || 'info'
}

const getPriorityText = (priority: string) => {
  const textMap = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return textMap[priority] || '未知'
}

const getTimelineType = (action: string) => {
  const typeMap = {
    '提交报名': 'primary',
    '审批通过': 'success',
    '审批拒绝': 'danger',
    '进入等待列表': 'warning'
  }
  return typeMap[action] || 'primary'
}

const formatDateTime = (date: Date) => {
  return new Date(date).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadEnrollmentList()
  loadWaitingList()
})
</script>

<style scoped>
.training-enrollment {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.enrollment-list-section {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.enrollment-detail {
  padding: 20px;
}

.approval-history {
  margin-top: 30px;
}

.approval-history h4 {
  margin-bottom: 20px;
  color: #333;
}

.approval-form {
  padding: 20px;
}

.approval-section,
.comment-section,
.notification-section {
  margin-bottom: 20px;
}

.approval-section h4,
.comment-section h4,
.notification-section h4 {
  margin-bottom: 10px;
  color: #333;
}

.waiting-list {
  padding: 20px;
}

.waiting-stats {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
}
</style>