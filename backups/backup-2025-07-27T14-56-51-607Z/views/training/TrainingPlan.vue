<template>
  <div class="training-plan">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>培训计划管理</h3>
          <div class="header-actions">
            <el-button @click="handleImport">导入计划</el-button>
            <el-button type="primary" @click="handleCreate">新建计划</el-button>
          </div>
        </div>
      </template>

      <!-- 筛选区域 -->
      <div class="filter-section">
        <el-form :model="filterForm" :inline="true">
          <el-form-item label="计划类型">
            <el-select v-model="filterForm.planType" @change="handleFilterChange">
              <el-option label="全部" value=""  />
              <el-option label="年度计划" value="annual"  />
              <el-option label="季度计划" value="quarterly"  />
              <el-option label="月度计划" value="monthly"  />
              <el-option label="专项计划" value="special"  />
            </el-select>
          </el-form-item>
          <el-form-item label="执行状态">
            <el-select v-model="filterForm.status" @change="handleFilterChange">
              <el-option label="全部" value=""  />
              <el-option label="草稿" value="draft"  />
              <el-option label="待审批" value="pending"  />
              <el-option label="执行中" value="active"  />
              <el-option label="已完成" value="completed"  />
              <el-option label="已取消" value="cancelled"  />
            </el-select>
          </el-form-item>
          <el-form-item label="负责部门">
            <el-select v-model="filterForm.department" @change="handleFilterChange">
              <el-option label="全部" value=""  />
              <el-option
                v-for="dept in departments"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
               />
            </el-select>
          </el-form-item>
          <el-form-item label="计划时间">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handleFilterChange"
             />
          </el-form-item>
        </el-form>
      </div>

      <!-- 培训计划列表 -->
      <div class="plan-list-section">
        <el-table
          :data="planList"
          v-loading="loading"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55"  />
          <el-table-column prop="planName" label="计划名称" min-width="200">
            <template #default="scope">
              <el-link @click="handleViewPlan(scope.row)">
                {{ scope.row.planName }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column prop="planType" label="计划类型" align="center">
            <template #default="scope">
              <el-tag :type="getPlanTypeColor(scope.row.planType)">
                {{ getPlanTypeText(scope.row.planType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="department" label="负责部门"  />
          <el-table-column prop="planPeriod" label="计划周期" align="center">
            <template #default="scope">
              {{ formatDateRange(scope.row.startDate, scope.row.endDate) }}
            </template>
          </el-table-column>
          <el-table-column prop="totalCourses" label="课程数量" align="center"  />
          <el-table-column prop="totalHours" label="总学时" align="center">
            <template #default="scope">
              {{ scope.row.totalHours }}h
            </template>
          </el-table-column>
          <el-table-column prop="budget" label="预算" align="center">
            <template #default="scope">
              ¥{{ scope.row.budget?.toLocaleString() }}
            </template>
          </el-table-column>
          <el-table-column prop="progress" label="执行进度" align="center">
            <template #default="scope">
              <el-progress
                :percentage="scope.row.progress"
                :stroke-width="8"
                :color="getProgressColor(scope.row.progress)"
               />
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" align="center">
            <template #default="scope">
              <el-tag :type="getStatusColor(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button type="text" @click="handleViewPlan(scope.row)">
                查看
              </el-button>
              <el-button type="text" @click="handleEditPlan(scope.row)">
                编辑
              </el-button>
              <el-button
                v-if="scope.row.status === 'draft'"
                type="text"
                @click="handleSubmitPlan(scope.row)"
              >
                提交审批
              </el-button>
              <el-button
                v-if="scope.row.status === 'pending'"
                type="text"
                @click="handleApprovePlan(scope.row)"
              >
                审批
              </el-button>
              <el-button
                type="text"
                @click="handleDeletePlan(scope.row)"
                style="color: #f56c6c"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
           />
        </div>
      </div>
    </el-card>

    <!-- 计划详情/编辑对话框 -->
    <el-dialog
      v-model="planDialog.visible"
      :title="planDialog.title"
      width="90%"
      :close-on-click-modal="false"
    >
      <div class="plan-form" v-if="planDialog.data">
        <el-form
          :model="planDialog.data"
          :rules="planRules"
          ref="planFormRef"
          label-width="120px"
        >
          <!-- 基本信息 -->
          <div class="form-section">
            <h4>基本信息</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="计划名称" prop="planName">
                  <el-input
                    v-model="planDialog.data.planName"
                    placeholder="请输入计划名称"
                    :readonly="planDialog.mode === 'view'"
                    />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="计划类型" prop="planType">
                  <el-select
                    v-model="planDialog.data.planType"
                    :disabled="planDialog.mode === 'view'"
                  >
                    <el-option label="年度计划" value="annual"  />
                    <el-option label="季度计划" value="quarterly"  />
                    <el-option label="月度计划" value="monthly"  />
                    <el-option label="专项计划" value="special"  />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="负责部门" prop="department">
                  <el-select
                    v-model="planDialog.data.department"
                    :disabled="planDialog.mode === 'view'"
                  >
                    <el-option
                      v-for="dept in departments"
                      :key="dept.id"
                      :label="dept.name"
                      :value="dept.name"
                     />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="计划负责人" prop="manager">
                  <el-select
                    v-model="planDialog.data.manager"
                    :disabled="planDialog.mode === 'view'"
                  >
                    <el-option
                      v-for="manager in managers"
                      :key="manager.id"
                      :label="manager.name"
                      :value="manager.name"
                     />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="计划周期" prop="planPeriod">
                  <el-date-picker
                    v-model="planDialog.data.planPeriod"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :disabled="planDialog.mode === 'view'"
                   />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="预算金额" prop="budget">
                  <el-input-number
                    v-model="planDialog.data.budget"
                    :min="0"
                    :max="999999999"
                    :precision="2"
                    :disabled="planDialog.mode === 'view'"
                    />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="计划描述" prop="description">
              <el-input
                v-model="planDialog.data.description"
                type="textarea"
                :rows="3"
                placeholder="请输入计划描述"
                :readonly="planDialog.mode === 'view'"
                />
            </el-form-item>
          </div>

          <!-- 培训目标 -->
          <div class="form-section">
            <h4>培训目标</h4>
            <div class="objectives-list">
              <div
                v-for="(objective, index) in planDialog.data.objectives"
                :key="index"
                class="objective-item"
              >
                <el-card shadow="never">
                  <div class="objective-header">
                    <span class="objective-title">目标 {{ index + 1 }}</span>
                    <el-button
                      v-if="planDialog.mode === 'edit'"
                      type="text"
                      icon="Delete"
                      @click="removeObjective(index)"
                      />
                  </div>
                  <el-form :model="objective" label-width="100px">
                    <el-form-item label="目标描述">
                      <el-input
                        v-model="objective.description"
                        placeholder="请描述培训目标"
                        :readonly="planDialog.mode === 'view'"
                        />
                    </el-form-item>
                    <el-row :gutter="20">
                      <el-col :span="8">
                        <el-form-item label="目标分类">
                          <el-select
                            v-model="objective.category"
                            :disabled="planDialog.mode === 'view'"
                          >
                            <el-option label="技能提升" value="skill"  />
                            <el-option label="知识学习" value="knowledge"  />
                            <el-option label="素养培养" value="quality"  />
                            <el-option label="管理能力" value="management"  />
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="目标对象">
                          <el-input
                            v-model="objective.targetAudience"
                            placeholder="如：新员工、管理层"
                            :readonly="planDialog.mode === 'view'"
                            />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="完成标准">
                          <el-input
                            v-model="objective.completionCriteria"
                            placeholder="如：考试通过率85%"
                            :readonly="planDialog.mode === 'view'"
                            />
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </el-form>
                </el-card>
              </div>
              <el-button
                v-if="planDialog.mode === 'edit'"
                type="dashed"
                @click="addObjective"
                style="width: 100%; margin-top: 10px;"
              >
                + 添加目标
              </el-button>
            </div>
          </div>

          <!-- 培训课程 -->
          <div class="form-section">
            <h4>培训课程</h4>
            <div class="courses-list">
              <div
                v-for="(course, index) in planDialog.data.courses"
                :key="index"
                class="course-item"
              >
                <el-card shadow="never">
                  <div class="course-header">
                    <span class="course-title">课程 {{ index + 1 }}</span>
                    <el-button
                      v-if="planDialog.mode === 'edit'"
                      type="text"
                      icon="Delete"
                      @click="removeCourse(index)"
                      />
                  </div>
                  <el-form :model="course" label-width="100px">
                    <el-row :gutter="20">
                      <el-col :span="12">
                        <el-form-item label="课程名称">
                          <el-input
                            v-model="course.name"
                            placeholder="请输入课程名称"
                            :readonly="planDialog.mode === 'view'"
                            />
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="课程类型">
                          <el-select
                            v-model="course.type"
                            :disabled="planDialog.mode === 'view'"
                          >
                            <el-option label="线上课程" value="online"  />
                            <el-option label="线下课程" value="offline"  />
                            <el-option label="混合课程" value="hybrid"  />
                          </el-select>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row :gutter="20">
                      <el-col :span="8">
                        <el-form-item label="课程时长">
                          <el-input-number
                            v-model="course.duration"
                            :min="0"
                            :max="999"
                            :disabled="planDialog.mode === 'view'"
                            />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="预计人数">
                          <el-input-number
                            v-model="course.expectedAttendees"
                            :min="1"
                            :max="9999"
                            :disabled="planDialog.mode === 'view'"
                            />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="课程费用">
                          <el-input-number
                            v-model="course.cost"
                            :min="0"
                            :max="999999"
                            :precision="2"
                            :disabled="planDialog.mode === 'view'"
                            />
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row :gutter="20">
                      <el-col :span="12">
                        <el-form-item label="计划时间">
                          <el-date-picker
                            v-model="course.scheduledDate"
                            type="date"
                            placeholder="选择开课日期"
                            :disabled="planDialog.mode === 'view'"
                           />
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="讲师">
                          <el-input
                            v-model="course.instructor"
                            placeholder="请输入讲师姓名"
                            :readonly="planDialog.mode === 'view'"
                            />
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </el-form>
                </el-card>
              </div>
              <el-button
                v-if="planDialog.mode === 'edit'"
                type="dashed"
                @click="addCourse"
                style="width: 100%; margin-top: 10px;"
              >
                + 添加课程
              </el-button>
            </div>
          </div>

          <!-- 资源配置 -->
          <div class="form-section">
            <h4>资源配置</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="培训场地">
                  <el-input
                    v-model="planDialog.data.venue"
                    placeholder="请输入培训场地"
                    :readonly="planDialog.mode === 'view'"
                    />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="设备需求">
                  <el-input
                    v-model="planDialog.data.equipment"
                    placeholder="请输入设备需求"
                    :readonly="planDialog.mode === 'view'"
                    />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="其他资源">
              <el-input
                v-model="planDialog.data.otherResources"
                type="textarea"
                :rows="2"
                placeholder="请输入其他资源需求"
                :readonly="planDialog.mode === 'view'"
                />
            </el-form-item>
          </div>

          <!-- 评估标准 -->
          <div class="form-section">
            <h4>评估标准</h4>
            <el-form-item label="评估方式">
              <el-checkbox-group
                v-model="planDialog.data.evaluationMethods"
                :disabled="planDialog.mode === 'view'"
              >
                <el-checkbox label="exam">考试测评</el-checkbox>
                <el-checkbox label="practice">实操演练</el-checkbox>
                <el-checkbox label="feedback">反馈调研</el-checkbox>
                <el-checkbox label="observation">行为观察</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="通过标准">
              <el-input
                v-model="planDialog.data.passingCriteria"
                placeholder="请输入通过标准"
                :readonly="planDialog.mode === 'view'"
                />
            </el-form-item>
          </div>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="planDialog.visible = false">
          {{ planDialog.mode === 'view' ? '关闭' : '取消' }}
        </el-button>
        <el-button
          v-if="planDialog.mode === 'edit'"
          type="primary"
          @click="handleSavePlan"
        >
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 接口定义
interface TrainingPlan {
  id?: string
  planName: string
  planType: string
  department: string
  manager: string
  planPeriod: [Date, Date]
  budget: number
  description: string
  objectives: Array<{
    description: string
    category: string
    targetAudience: string
    completionCriteria: string
  }>
  courses: Array<{
    name: string
    type: string
    duration: number
    expectedAttendees: number
    cost: number
    scheduledDate: Date
    instructor: string
  }>
  venue: string
  equipment: string
  otherResources: string
  evaluationMethods: string[]
  passingCriteria: string
  totalCourses?: number
  totalHours?: number
  progress?: number
  status?: string
  startDate?: Date
  endDate?: Date
}

// 响应式数据
const loading = ref(false)
const selectedPlans = ref<TrainingPlan[]>([])

const departments = ref([
  { id: '1', name: 'HrHr人力资源部' },
  { id: '2', name: '技术部' },
  { id: '3', name: '销售部' },
  { id: '4', name: '市场部' }
])

const managers = ref([
  { id: '1', name: '张经理' },
  { id: '2', name: '李总监' },
  { id: '3', name: '王主管' }
])

const filterForm = reactive({
  planType: '',
  status: '',
  department: '',
  dateRange: []
})

const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

const planList = ref<TrainingPlan[]>([])

const planDialog = reactive({
  visible: false,
  title: '',
  mode: 'view' as 'view' | 'edit',
  data: null as TrainingPlan | null
})

const planRules = {
  planName: [{ required: true, message: '请输入计划名称', trigger: 'blur' }],
  planType: [{ required: true, message: '请选择计划类型', trigger: 'change' }],
  department: [{ required: true, message: '请选择负责部门', trigger: 'change' }],
  manager: [{ required: true, message: '请选择计划负责人', trigger: 'change' }],
  planPeriod: [{ required: true, message: '请选择计划周期', trigger: 'change' }],
  budget: [{ required: true, message: '请输入预算金额', trigger: 'blur' }]
}

const planFormRef = ref()

// 方法
const handleFilterChange = () => {
  loadPlanList()
}

const handleSelectionChange = (selection: TrainingPlan[]) => {
  selectedPlans.value = selection
}

const handleImport = () => {
  ElMessage.info('导入功能开发中...')
}

const handleCreate = () => {
  planDialog.data = {
    planName: '',
    planType: 'annual',
    department: '',
    manager: '',
    planPeriod: [new Date(), new Date()],
    budget: 0,
    description: '',
    objectives: [],
    courses: [],
    venue: '',
    equipment: '',
    otherResources: '',
    evaluationMethods: [],
    passingCriteria: ''
  }
  planDialog.title = '新建培训计划'
  planDialog.mode = 'edit'
  planDialog.visible = true
}

const handleViewPlan = (row: TrainingPlan) => {
  planDialog.data = {
    ...row,
    objectives: row.objectives || [
      {
        description: '提升员工专业技能水平',
        category: 'skill',
        targetAudience: '技术部员工',
        completionCriteria: '技能考核通过率90%'
      },
      {
        description: '加强团队协作能力',
        category: 'quality',
        targetAudience: '全体员工',
        completionCriteria: '团队合作评分提升20%'
      }
    ],
    courses: row.courses || [
      {
        name: 'Vue.js 高级开发',
        type: 'online',
        duration: 16,
        expectedAttendees: 25,
        cost: 5000,
        scheduledDate: new Date('2025-02-15'),
        instructor: '张老师'
      },
      {
        name: '项目管理实战',
        type: 'offline',
        duration: 24,
        expectedAttendees: 30,
        cost: 8000,
        scheduledDate: new Date('2025-03-01'),
        instructor: '李老师'
      }
    ]
  }
  planDialog.title = '查看培训计划'
  planDialog.mode = 'view'
  planDialog.visible = true
}

const handleEditPlan = (row: TrainingPlan) => {
  planDialog.data = { ...row }
  planDialog.title = '编辑培训计划'
  planDialog.mode = 'edit'
  planDialog.visible = true
}

const handleSubmitPlan = async (row: TrainingPlan) => {
  try {
    await ElMessageBox.confirm(
      `确定要提交${row.planName}的培训计划吗？`,
      '确认提交',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    loading.value = true
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('培训计划已提交审批')
    loadPlanList()
  } catch (__error) {
    // 用户取消
  } finally {
    loading.value = false
  }
}

const handleApprovePlan = async (row: TrainingPlan) => {
  try {
    await ElMessageBox.confirm(
      `确定要审批通过${row.planName}的培训计划吗？`,
      '确认审批',
      {
        confirmButtonText: '通过',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    loading.value = true
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('培训计划审批通过')
    loadPlanList()
  } catch (__error) {
    // 用户取消
  } finally {
    loading.value = false
  }
}

const handleDeletePlan = async (row: TrainingPlan) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除${row.planName}的培训计划吗？`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )
    
    loading.value = true
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('培训计划已删除')
    loadPlanList()
  } catch (__error) {
    // 用户取消
  } finally {
    loading.value = false
  }
}

const handleSavePlan = async () => {
  if (!planFormRef.value) return
  
  try {
    await planFormRef.value.validate()
    loading.value = true
    
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('培训计划保存成功')
    planDialog.visible = false
    loadPlanList()
  } catch (__error) {
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

const addObjective = () => {
  if (!planDialog.data?.objectives) {
    planDialog.data!.objectives = []
  }
  
  planDialog.data.objectives.push({
    description: '',
    category: 'skill',
    targetAudience: '',
    completionCriteria: ''
  })
}

const removeObjective = (index: number) => {
  planDialog.data?.objectives?.splice(index, 1)
}

const addCourse = () => {
  if (!planDialog.data?.courses) {
    planDialog.data!.courses = []
  }
  
  planDialog.data.courses.push({
    name: '',
    type: 'online',
    duration: 0,
    expectedAttendees: 0,
    cost: 0,
    scheduledDate: new Date(),
    instructor: ''
  })
}

const removeCourse = (index: number) => {
  planDialog.data?.courses?.splice(index, 1)
}

const loadPlanList = async () => {
  try {
    loading.value = true
    
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    planList.value = [
      {
        id: '1',
        planName: '2025年度技术培训计划',
        planType: 'annual',
        department: '技术部',
        manager: '张经理',
        startDate: new Date('2025-01-01'),
        endDate: new Date('2025-12-31'),
        budget: 50000,
        description: '面向技术部员工的年度技能提升培训',
        totalCourses: 12,
        totalHours: 240,
        progress: 25,
        status: 'active',
        objectives: [],
        courses: [],
        venue: '培训中心',
        equipment: '投影仪、电脑',
        otherResources: '培训资料、证书',
        evaluationMethods: ['exam', 'practice'],
        passingCriteria: '考试成绩85分以上',
        planPeriod: [new Date('2025-01-01'), new Date('2025-12-31')]
      },
      {
        id: '2',
        planName: '新员工入职培训计划',
        planType: 'monthly',
        department: '人力资源部',
        manager: '李总监',
        startDate: new Date('2025-02-01'),
        endDate: new Date('2025-02-28'),
        budget: 8000,
        description: '新员工入职培训和企业文化培训',
        totalCourses: 6,
        totalHours: 48,
        progress: 0,
        status: 'draft',
        objectives: [],
        courses: [],
        venue: '会议室A',
        equipment: '音响设备',
        otherResources: '员工手册、培训资料',
        evaluationMethods: ['feedback', 'observation'],
        passingCriteria: '培训出勤率100%',
        planPeriod: [new Date('2025-02-01'), new Date('2025-02-28')]
      },
      {
        id: '3',
        planName: '销售技能专项培训',
        planType: 'special',
        department: '销售部',
        manager: '王主管',
        startDate: new Date('2025-03-01'),
        endDate: new Date('2025-03-15'),
        budget: 15000,
        description: '销售技巧和客户关系管理培训',
        totalCourses: 4,
        totalHours: 32,
        progress: 100,
        status: 'completed',
        objectives: [],
        courses: [],
        venue: '培训教室B',
        equipment: '白板、话筒',
        otherResources: '案例资料、工具包',
        evaluationMethods: ['exam', 'practice'],
        passingCriteria: '销售考核通过',
        planPeriod: [new Date('2025-03-01'), new Date('2025-03-15')]
      }
    ]
    
    pagination.total = planList.value.length
  } catch (__error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  loadPlanList()
}

const handleCurrentChange = (current: number) => {
  pagination.current = current
  loadPlanList()
}

const getPlanTypeColor = (type: string) => {
  const colorMap = {
    annual: 'primary',
    quarterly: 'success',
    monthly: 'warning',
    special: 'info'
  }
  return colorMap[type] || 'info'
}

const getPlanTypeText = (type: string) => {
  const textMap = {
    annual: '年度计划',
    quarterly: '季度计划',
    monthly: '月度计划',
    special: '专项计划'
  }
  return textMap[type] || '未知'
}

const getStatusColor = (status: string) => {
  const colorMap = {
    draft: 'info',
    pending: 'warning',
    active: 'primary',
    completed: 'success',
    cancelled: 'danger'
  }
  return colorMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap = {
    draft: '草稿',
    pending: '待审批',
    active: '执行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return textMap[status] || '未知'
}

const getProgressColor = (progress: number) => {
  if (progress >= 80) return '#67c23a'
  if (progress >= 60) return '#409eff'
  if (progress >= 40) return '#e6a23c'
  return '#f56c6c'
}

const formatDateRange = (startDate: Date, endDate: Date) => {
  if (!startDate || !endDate) return '-'
  const start = new Date(startDate).toLocaleDateString()
  const end = new Date(endDate).toLocaleDateString()
  return `${start} - ${end}`
}

// 生命周期
onMounted(() => {
  loadPlanList()
})
</script>

<style scoped>
.training-plan {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.plan-list-section {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.plan-form {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.form-section h4 {
  color: #409eff;
  margin-bottom: 20px;
  border-bottom: 2px solid #409eff;
  padding-bottom: 5px;
}

.objectives-list,
.courses-list {
  margin-top: 15px;
}

.objective-item,
.course-item {
  margin-bottom: 15px;
}

.objective-header,
.course-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.objective-title,
.course-title {
  font-weight: bold;
  color: #333;
}
</style>