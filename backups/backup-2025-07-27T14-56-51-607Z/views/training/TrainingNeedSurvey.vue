<template>
  <div class="training-need-survey">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>培训需求调研</h3>
          <div class="header-actions">
            <el-button @click="handleCreateSurvey">创建调研</el-button>
            <el-button type="primary" @click="handleAnalyzeResults">需求分析</el-button>
          </div>
        </div>
      </template>

      <!-- 筛选区域 -->
      <div class="filter-section">
        <el-form :model="filterForm" :inline="true">
          <el-form-item label="调研状态">
            <el-select v-model="filterForm.status" @change="handleFilterChange">
              <el-option label="全部" value=""  />
              <el-option label="草稿" value="draft"  />
              <el-option label="进行中" value="ongoing"  />
              <el-option label="已结束" value="finished"  />
              <el-option label="已暂停" value="paused"  />
            </el-select>
          </el-form-item>
          <el-form-item label="调研类型">
            <el-select v-model="filterForm.surveyType" @change="handleFilterChange">
              <el-option label="全部" value=""  />
              <el-option label="年度需求调研" value="annual"  />
              <el-option label="岗位技能调研" value="position"  />
              <el-option label="专项培训调研" value="special"  />
              <el-option label="满意度调研" value="satisfaction"  />
            </el-select>
          </el-form-item>
          <el-form-item label="目标部门">
            <el-select v-model="filterForm.department" @change="handleFilterChange">
              <el-option label="全部" value=""  />
              <el-option
                v-for="dept in departments"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
               />
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handleFilterChange"
             />
          </el-form-item>
        </el-form>
      </div>

      <!-- 调研列表 -->
      <div class="survey-list-section">
        <el-table
          :data="surveyList"
          v-loading="loading"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55"  />
          <el-table-column prop="title" label="调研标题" min-width="200">
            <template #default="scope">
              <el-link @click="handleViewSurvey(scope.row)">
                {{ scope.row.title }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column prop="surveyType" label="调研类型" align="center">
            <template #default="scope">
              <el-tag :type="getSurveyTypeColor(scope.row.surveyType)">
                {{ getSurveyTypeText(scope.row.surveyType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="targetDepartments" label="目标部门" min-width="150">
            <template #default="scope">
              <el-tag
                v-for="dept in scope.row.targetDepartments"
                :key="dept"
                size="small"
                style="margin: 2px;"
              >
                {{ dept }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="duration" label="调研周期" align="center">
            <template #default="scope">
              {{ formatDateRange(scope.row.startDate, scope.row.endDate) }}
            </template>
          </el-table-column>
          <el-table-column prop="participants" label="参与情况" align="center">
            <template #default="scope">
              <div class="participation-info">
                <span>{{ scope.row.responseCount }}/{{ scope.row.targetCount }}</span>
                <el-progress
                  :percentage="getParticipationRate(scope.row)"
                  :stroke-width="4"
                  :show-text="false"
                 />
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" align="center">
            <template #default="scope">
              <el-tag :type="getStatusColor(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="creator" label="创建人"  />
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button type="text" @click="handleViewSurvey(scope.row)">
                查看
              </el-button>
              <el-button type="text" @click="handleEditSurvey(scope.row)">
                编辑
              </el-button>
              <el-button type="text" @click="handleViewResults(scope.row)">
                结果
              </el-button>
              <el-button
                v-if="scope.row.status === 'draft'"
                type="text"
                @click="handlePublishSurvey(scope.row)"
              >
                发布
              </el-button>
              <el-button
                type="text"
                @click="handleDeleteSurvey(scope.row)"
                style="color: #f56c6c"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
           />
        </div>
      </div>
    </el-card>

    <!-- 调研详情/编辑对话框 -->
    <el-dialog
      v-model="surveyDialog.visible"
      :title="surveyDialog.title"
      width="90%"
      :close-on-click-modal="false"
    >
      <div class="survey-form" v-if="surveyDialog.data">
        <el-form
          :model="surveyDialog.data"
          :rules="surveyRules"
          ref="surveyFormRef"
          label-width="120px"
        >
          <!-- 基本信息 -->
          <div class="form-section">
            <h4>基本信息</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="调研标题" prop="title">
                  <el-input
                    v-model="surveyDialog.data.title"
                    placeholder="请输入调研标题"
                    :readonly="surveyDialog.mode === 'view'"
                    />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="调研类型" prop="surveyType">
                  <el-select
                    v-model="surveyDialog.data.surveyType"
                    :disabled="surveyDialog.mode === 'view'"
                  >
                    <el-option label="年度需求调研" value="annual"  />
                    <el-option label="岗位技能调研" value="position"  />
                    <el-option label="专项培训调研" value="special"  />
                    <el-option label="满意度调研" value="satisfaction"  />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="调研周期" prop="duration">
                  <el-date-picker
                    v-model="surveyDialog.data.duration"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :disabled="surveyDialog.mode === 'view'"
                   />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="目标部门" prop="targetDepartments">
                  <el-select
                    v-model="surveyDialog.data.targetDepartments"
                    :disabled="surveyDialog.mode === 'view'"
                    multiple
                    placeholder="请选择目标部门"
                  >
                    <el-option
                      v-for="dept in departments"
                      :key="dept.id"
                      :label="dept.name"
                      :value="dept.name"
                     />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="调研说明">
              <el-input
                v-model="surveyDialog.data.description"
                type="textarea"
                :rows="3"
                placeholder="请输入调研说明"
                :readonly="surveyDialog.mode === 'view'"
                />
            </el-form-item>
          </div>

          <!-- 问卷题目 -->
          <div class="form-section">
            <h4>问卷题目</h4>
            <div class="questions-list">
              <div
                v-for="(question, index) in surveyDialog.data.questions"
                :key="index"
                class="question-item"
              >
                <el-card shadow="never">
                  <div class="question-header">
                    <span class="question-title">题目 {{ index + 1 }}</span>
                    <el-button
                      v-if="surveyDialog.mode === 'edit'"
                      type="text"
                      icon="Delete"
                      @click="removeQuestion(index)"
                      />
                  </div>
                  <el-form :model="question" label-width="100px">
                    <el-form-item label="题目类型">
                      <el-select
                        v-model="question.type"
                        :disabled="surveyDialog.mode === 'view'"
                      >
                        <el-option label="单选题" value="single"  />
                        <el-option label="多选题" value="multiple"  />
                        <el-option label="填空题" value="text"  />
                        <el-option label="评分题" value="rating"  />
                        <el-option label="排序题" value="ranking"  />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="题目内容">
                      <el-input
                        v-model="question.content"
                        placeholder="请输入题目内容"
                        :readonly="surveyDialog.mode === 'view'"
                        />
                    </el-form-item>
                    <el-form-item
                      v-if="['single', 'multiple', 'ranking'].includes(question.type)"
                      label="选项设置"
                    >
                      <div class="options-list">
                        <div
                          v-for="(option, optionIndex) in question.options"
                          :key="optionIndex"
                          class="option-item"
                        >
                          <el-input
                            v-model="option.text"
                            placeholder="请输入选项内容"
                            :readonly="surveyDialog.mode === 'view'"
                            />
                          <el-button
                            v-if="surveyDialog.mode === 'edit'"
                            type="text"
                            icon="Delete"
                            @click="removeOption(index, optionIndex)"
                            />
                        </div>
                        <el-button
                          v-if="surveyDialog.mode === 'edit'"
                          type="text"
                          @click="addOption(index)"
                        >
                          + 添加选项
                        </el-button>
                      </div>
                    </el-form-item>
                    <el-form-item label="是否必填">
                      <el-switch
                        v-model="question.required"
                        :disabled="surveyDialog.mode === 'view'"
                       />
                    </el-form-item>
                  </el-form>
                </el-card>
              </div>
              <el-button
                v-if="surveyDialog.mode === 'edit'"
                type="dashed"
                @click="addQuestion"
                style="width: 100%; margin-top: 10px;"
              >
                + 添加题目
              </el-button>
            </div>
          </div>

          <!-- 发布设置 -->
          <div class="form-section">
            <h4>发布设置</h4>
            <el-form-item label="参与方式">
              <el-radio-group
                v-model="surveyDialog.data.participationMode"
                :disabled="surveyDialog.mode === 'view'"
              >
                <el-radio label="open">开放参与</el-radio>
                <el-radio label="invite">邀请参与</el-radio>
                <el-radio label="mandatory">强制参与</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="匿名设置">
              <el-switch
                v-model="surveyDialog.data.anonymous"
                :disabled="surveyDialog.mode === 'view'"
               />
            </el-form-item>
            <el-form-item label="结果公开">
              <el-switch
                v-model="surveyDialog.data.publicResults"
                :disabled="surveyDialog.mode === 'view'"
               />
            </el-form-item>
          </div>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="surveyDialog.visible = false">
          {{ surveyDialog.mode === 'view' ? '关闭' : '取消' }}
        </el-button>
        <el-button
          v-if="surveyDialog.mode === 'edit'"
          type="primary"
          @click="handleSaveSurvey"
        >
          保存
        </el-button>
      </template>
    </el-dialog>

    <!-- 调研结果对话框 -->
    <el-dialog
      v-model="resultsDialog.visible"
      title="调研结果分析"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="results-content" v-if="resultsDialog.data">
        <el-tabs v-model="resultsTab">
          <el-tab-pane label="统计概览" name="overview">
            <div class="statistics-overview">
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-statistic title="总发放数" :value="resultsDialog.data.totalSent"  />
                </el-col>
                <el-col :span="6">
                  <el-statistic title="已回收数" :value="resultsDialog.data.totalReceived"  />
                </el-col>
                <el-col :span="6">
                  <el-statistic title="回收率" :value="resultsDialog.data.responseRate" suffix="%"  />
                </el-col>
                <el-col :span="6">
                  <el-statistic title="有效问卷" :value="resultsDialog.data.validResponses"  />
                </el-col>
              </el-row>
              
              <div class="department-stats">
                <h4>部门参与情况</h4>
                <el-table :data="resultsDialog.data.departmentStats">
                  <el-table-column prop="department" label="部门"  />
                  <el-table-column prop="sent" label="发放数" align="center"  />
                  <el-table-column prop="received" label="回收数" align="center"  />
                  <el-table-column prop="rate" label="回收率" align="center">
                    <template #default="scope">
                      {{ scope.row.rate }}%
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="题目分析" name="questions">
            <div class="question-analysis">
              <div
                v-for="(analysis, index) in resultsDialog.data.questionAnalysis"
                :key="index"
                class="question-result"
              >
                <h4>{{ analysis.question }}</h4>
                <div class="answer-distribution">
                  <div
                    v-for="(answer, answerIndex) in analysis.answers"
                    :key="answerIndex"
                    class="answer-item"
                  >
                    <div class="answer-info">
                      <span class="answer-text">{{ answer.text }}</span>
                      <span class="answer-count">{{ answer.count }}票 ({{ answer.percentage }}%)</span>
                    </div>
                    <el-progress
                      :percentage="answer.percentage"
                      :stroke-width="12"
                      :show-text="false"
                     />
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="需求分析" name="demands">
            <div class="demand-analysis">
              <div class="training-demands">
                <h4>培训需求排名</h4>
                <el-table :data="resultsDialog.data.trainingDemands">
                  <el-table-column prop="rank" label="排名" width="80" align="center"  />
                  <el-table-column prop="course" label="课程类型"  />
                  <el-table-column prop="votes" label="需求票数" align="center"  />
                  <el-table-column prop="percentage" label="占比" align="center">
                    <template #default="scope">
                      {{ scope.row.percentage }}%
                    </template>
                  </el-table-column>
                  <el-table-column prop="urgency" label="紧急程度" align="center">
                    <template #default="scope">
                      <el-tag :type="getUrgencyColor(scope.row.urgency)">
                        {{ scope.row.urgency }}
                      </el-tag>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              
              <div class="skill-gaps">
                <h4>技能缺口分析</h4>
                <div class="gap-items">
                  <div
                    v-for="gap in resultsDialog.data.skillGaps"
                    :key="gap.skill"
                    class="gap-item"
                  >
                    <div class="gap-header">
                      <span class="skill-name">{{ gap.skill }}</span>
                      <span class="gap-level">缺口等级: {{ gap.level }}</span>
                    </div>
                    <div class="gap-description">{{ gap.description }}</div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <template #footer>
        <el-button @click="resultsDialog.visible = false">关闭</el-button>
        <el-button type="primary" @click="handleExportResults">导出报告</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 接口定义
interface TrainingNeedSurvey {
  id?: string
  title: string
  surveyType: string
  duration: [Date, Date]
  targetDepartments: string[]
  description: string
  questions: Array<{
    type: string
    content: string
    options?: Array<{ text: string }>
    required: boolean
  }>
  participationMode: string
  anonymous: boolean
  publicResults: boolean
  status?: string
  creator?: string
  startDate?: Date
  endDate?: Date
  responseCount?: number
  targetCount?: number
}

// 响应式数据
const loading = ref(false)
const selectedSurveys = ref<TrainingNeedSurvey[]>([])
const resultsTab = ref('overview')

const departments = ref([
  { id: '1', name: 'HrHr技术部' },
  { id: '2', name: '销售部' },
  { id: '3', name: '市场部' },
  { id: '4', name: '人事部' }
])

const filterForm = reactive({
  status: '',
  surveyType: '',
  department: '',
  dateRange: []
})

const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

const surveyList = ref<TrainingNeedSurvey[]>([])

const surveyDialog = reactive({
  visible: false,
  title: '',
  mode: 'view' as 'view' | 'edit',
  data: null as TrainingNeedSurvey | null
})

const resultsDialog = reactive({
  visible: false,
  data: null as unknown
})

const surveyRules = {
  title: [{ required: true, message: '请输入调研标题', trigger: 'blur' }],
  surveyType: [{ required: true, message: '请选择调研类型', trigger: 'change' }],
  duration: [{ required: true, message: '请选择调研周期', trigger: 'change' }],
  targetDepartments: [{ required: true, message: '请选择目标部门', trigger: 'change' }]
}

const surveyFormRef = ref()

// 方法
const handleFilterChange = () => {
  loadSurveyList()
}

const handleSelectionChange = (selection: TrainingNeedSurvey[]) => {
  selectedSurveys.value = selection
}

const handleCreateSurvey = () => {
  surveyDialog.data = {
    title: '',
    surveyType: 'annual',
    duration: [new Date(), new Date()],
    targetDepartments: [],
    description: '',
    questions: [],
    participationMode: 'open',
    anonymous: false,
    publicResults: false
  }
  surveyDialog.title = '创建培训需求调研'
  surveyDialog.mode = 'edit'
  surveyDialog.visible = true
}

const handleAnalyzeResults = () => {
  ElMessage.info('需求分析功能开发中...')
}

const handleViewSurvey = (row: TrainingNeedSurvey) => {
  surveyDialog.data = {
    ...row,
    questions: row.questions || [
      {
        type: 'single',
        content: '您认为最需要加强的技能领域是？',
        options: [
          { text: '技术技能' },
          { text: '管理技能' },
          { text: '沟通技能' },
          { text: '专业知识' }
        ],
        required: true
      },
      {
        type: 'multiple',
        content: '您希望参加哪些类型的培训？',
        options: [
          { text: '线上培训' },
          { text: '线下培训' },
          { text: '混合培训' },
          { text: '外部培训' }
        ],
        required: true
      }
    ]
  }
  surveyDialog.title = '查看培训需求调研'
  surveyDialog.mode = 'view'
  surveyDialog.visible = true
}

const handleEditSurvey = (row: TrainingNeedSurvey) => {
  surveyDialog.data = { ...row }
  surveyDialog.title = '编辑培训需求调研'
  surveyDialog.mode = 'edit'
  surveyDialog.visible = true
}

const handleViewResults = (row: TrainingNeedSurvey) => {
  resultsDialog.data = {
    totalSent: 150,
    totalReceived: 120,
    responseRate: 80,
    validResponses: 115,
    departmentStats: [
      { department: '技术部', sent: 50, received: 45, rate: 90 },
      { department: '销售部', sent: 40, received: 30, rate: 75 },
      { department: '市场部', sent: 35, received: 25, rate: 71 },
      { department: '人事部', sent: 25, received: 20, rate: 80 }
    ],
    questionAnalysis: [
      {
        question: '您认为最需要加强的技能领域是？',
        answers: [
          { text: '技术技能', count: 45, percentage: 39 },
          { text: '管理技能', count: 30, percentage: 26 },
          { text: '沟通技能', count: 25, percentage: 22 },
          { text: '专业知识', count: 15, percentage: 13 }
        ]
      }
    ],
    trainingDemands: [
      { rank: 1, course: 'Vue.js高级开发', votes: 45, percentage: 39, urgency: '高' },
      { rank: 2, course: '项目管理', votes: 30, percentage: 26, urgency: '中' },
      { rank: 3, course: '沟通技巧', votes: 25, percentage: 22, urgency: '中' },
      { rank: 4, course: '数据分析', votes: 15, percentage: 13, urgency: '低' }
    ],
    skillGaps: [
      {
        skill: '前端技术',
        level: '高',
        description: '现有技术栈与最新技术存在较大差距，需要系统性培训'
      },
      {
        skill: '团队管理',
        level: '中',
        description: '中层管理者在团队管理方面需要提升'
      }
    ]
  }
  resultsDialog.visible = true
}

const handlePublishSurvey = async (row: TrainingNeedSurvey) => {
  try {
    await ElMessageBox.confirm(
      `确定要发布调研"${row.title}"吗？`,
      '确认发布',
      {
        confirmButtonText: '发布',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    loading.value = true
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('调研发布成功')
    loadSurveyList()
  } catch (__error) {
    // 用户取消
  } finally {
    loading.value = false
  }
}

const handleDeleteSurvey = async (row: TrainingNeedSurvey) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除调研"${row.title}"吗？`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )
    
    loading.value = true
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('调研删除成功')
    loadSurveyList()
  } catch (__error) {
    // 用户取消
  } finally {
    loading.value = false
  }
}

const handleSaveSurvey = async () => {
  if (!surveyFormRef.value) return
  
  try {
    await surveyFormRef.value.validate()
    loading.value = true
    
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('调研保存成功')
    surveyDialog.visible = false
    loadSurveyList()
  } catch (__error) {
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

const handleExportResults = () => {
  ElMessage.success('正在导出调研报告...')
}

const addQuestion = () => {
  if (!surveyDialog.data?.questions) {
    surveyDialog.data!.questions = []
  }
  
  surveyDialog.data.questions.push({
    type: 'single',
    content: '',
    options: [{ text: '' }],
    required: false
  })
}

const removeQuestion = (index: number) => {
  surveyDialog.data?.questions?.splice(index, 1)
}

const addOption = (questionIndex: number) => {
  const question = surveyDialog.data?.questions?.[questionIndex]
  if (question) {
    if (!question.options) {
      question.options = []
    }
    question.options.push({ text: '' })
  }
}

const removeOption = (questionIndex: number, optionIndex: number) => {
  const question = surveyDialog.data?.questions?.[questionIndex]
  if (question?.options) {
    question.options.splice(optionIndex, 1)
  }
}

const loadSurveyList = async () => {
  try {
    loading.value = true
    
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    surveyList.value = [
      {
        id: '1',
        title: '2025年度培训需求调研',
        surveyType: 'annual',
        targetDepartments: ['技术部', '市场部'],
        startDate: new Date('2025-01-01'),
        endDate: new Date('2025-01-31'),
        responseCount: 120,
        targetCount: 150,
        status: 'ongoing',
        creator: '人事部',
        duration: [new Date('2025-01-01'), new Date('2025-01-31')],
        description: '收集全体员工的培训需求',
        questions: [],
        participationMode: 'mandatory',
        anonymous: true,
        publicResults: true
      },
      {
        id: '2',
        title: '技术岗位技能需求调研',
        surveyType: 'position',
        targetDepartments: ['技术部'],
        startDate: new Date('2025-01-10'),
        endDate: new Date('2025-01-20'),
        responseCount: 45,
        targetCount: 50,
        status: 'finished',
        creator: '技术部',
        duration: [new Date('2025-01-10'), new Date('2025-01-20')],
        description: '针对技术岗位的专业技能培训需求',
        questions: [],
        participationMode: 'open',
        anonymous: false,
        publicResults: false
      }
    ]
    
    pagination.total = surveyList.value.length
  } catch (__error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  loadSurveyList()
}

const handleCurrentChange = (current: number) => {
  pagination.current = current
  loadSurveyList()
}

// 辅助方法
const getSurveyTypeColor = (type: string) => {
  const colorMap = {
    annual: 'primary',
    position: 'success',
    special: 'warning',
    satisfaction: 'info'
  }
  return colorMap[type] || 'info'
}

const getSurveyTypeText = (type: string) => {
  const textMap = {
    annual: '年度调研',
    position: '岗位调研',
    special: '专项调研',
    satisfaction: '满意度调研'
  }
  return textMap[type] || '未知'
}

const getStatusColor = (status: string) => {
  const colorMap = {
    draft: 'info',
    ongoing: 'primary',
    finished: 'success',
    paused: 'warning'
  }
  return colorMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap = {
    draft: '草稿',
    ongoing: '进行中',
    finished: '已结束',
    paused: '已暂停'
  }
  return textMap[status] || '未知'
}

const getUrgencyColor = (urgency: string) => {
  const colorMap = {
    高: 'danger',
    中: 'warning',
    低: 'success'
  }
  return colorMap[urgency] || 'info'
}

   
const getParticipationRate = (survey: unknown) => {
  if (!survey.targetCount || survey.targetCount === 0) return 0
  return Math.round((survey.responseCount / survey.targetCount) * 100)
}

const formatDateRange = (startDate: Date, endDate: Date) => {
  if (!startDate || !endDate) return '-'
  const start = new Date(startDate).toLocaleDateString()
  const end = new Date(endDate).toLocaleDateString()
  return `${start} - ${end}`
}

// 生命周期
onMounted(() => {
  loadSurveyList()
})
</script>

<style scoped>
.training-need-survey {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.survey-list-section {
  margin-bottom: 20px;
}

.participation-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.survey-form {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.form-section h4 {
  color: #409eff;
  margin-bottom: 20px;
  border-bottom: 2px solid #409eff;
  padding-bottom: 5px;
}

.questions-list {
  margin-top: 15px;
}

.question-item {
  margin-bottom: 15px;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.question-title {
  font-weight: bold;
  color: #333;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.results-content {
  padding: 20px;
}

.statistics-overview {
  margin-bottom: 30px;
}

.department-stats {
  margin-top: 30px;
}

.department-stats h4 {
  margin-bottom: 15px;
  color: #333;
}

.question-analysis {
  margin-bottom: 30px;
}

.question-result {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.question-result h4 {
  margin-bottom: 15px;
  color: #333;
}

.answer-distribution {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.answer-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.answer-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.answer-text {
  font-weight: 500;
}

.answer-count {
  color: #666;
  font-size: 14px;
}

.demand-analysis {
  margin-bottom: 30px;
}

.training-demands {
  margin-bottom: 30px;
}

.training-demands h4 {
  margin-bottom: 15px;
  color: #333;
}

.skill-gaps h4 {
  margin-bottom: 15px;
  color: #333;
}

.gap-items {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.gap-item {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.gap-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.skill-name {
  font-weight: bold;
  color: #333;
}

.gap-level {
  color: #666;
  font-size: 14px;
}

.gap-description {
  color: #666;
  line-height: 1.5;
}
</style>