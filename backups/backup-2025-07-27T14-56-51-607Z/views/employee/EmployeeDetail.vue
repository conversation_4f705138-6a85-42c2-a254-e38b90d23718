<template>
  <div class="employee-detail">
    <div class="page-header">
      <h1>员工详情</h1>
      <p>查看和编辑员工信息</p>
    </div>
    
    <el-card>
      <el-descriptions title="基本信息" :column="3" border>
        <el-descriptions-item label="员工编号">EMP001</el-descriptions-item>
        <el-descriptions-item label="姓名">张三</el-descriptions-item>
        <el-descriptions-item label="性别">男</el-descriptions-item>
        <el-descriptions-item label="部门">技术部</el-descriptions-item>
        <el-descriptions-item label="职位">高级工程师</el-descriptions-item>
        <el-descriptions-item label="入职日期">2020-01-01</el-descriptions-item>
        <el-descriptions-item label="手机号">13800138000</el-descriptions-item>
        <el-descriptions-item label="邮箱"><EMAIL></el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag type="success">在职</el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
    
    <div class="action-buttons">
      <el-button type="primary">编辑</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
 
import { useRouter } from 'vue-router'

const router = useRouter()

const goBack = () => {
  router.back()
}
</script>

<style scoped>
.employee-detail {
  width: 100%;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  font-size: 24px;
  font-weight: 500;
  margin: 0 0 8px 0;
}

.page-header p {
  color: #909399;
  margin: 0;
}

.action-buttons {
  margin-top: 20px;
}
</style>