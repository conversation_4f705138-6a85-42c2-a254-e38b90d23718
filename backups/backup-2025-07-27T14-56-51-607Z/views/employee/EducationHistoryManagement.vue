<template>
  <div class="education-history-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>教育经历管理</h2>
      <p>管理教职工的教育背景、学历学位和学习经历信息</p>
    </div>

    <!-- 搜索和操作区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-form">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索学校、专业、员工姓名"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.degree" placeholder="学历" clearable>
              <el-option
                v-for="item in degreeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.studyForm" placeholder="学习形式" clearable>
              <el-option
                v-for="item in studyFormOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-date-picker
              v-model="graduationDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="毕业开始日期"
              end-placeholder="毕业结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleDateRangeChange"
             />
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-col>
        </el-row>
        
        <!-- 高级搜索 -->
        <el-collapse v-model="activeCollapse" class="advanced-search">
          <el-collapse-item title="高级搜索" name="advanced">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="员工ID">
                  <el-input v-model="searchForm.employeeId" placeholder="请输入员工ID"   />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="毕业院校">
                  <el-input v-model="searchForm.graduationSchool" placeholder="请输入毕业院校"   />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="专业">
                  <el-input v-model="searchForm.major" placeholder="请输入专业"   />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="验证状态">
                  <el-select v-model="searchForm.verificationStatus" placeholder="请选择" clearable>
                    <el-option
                      v-for="item in verificationStatusOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                     />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="绩点范围">
                  <el-row :gutter="10">
                    <el-col :span="12">
                      <el-input-number
                        v-model="searchForm.minGpa"
                        placeholder="最小绩点"
                        :min="0"
                        :max="5"
                        :precision="2"
                        controls-position="right"
                        />
                    </el-col>
                    <el-col :span="12">
                      <el-input-number
                        v-model="searchForm.maxGpa"
                        placeholder="最大绩点"
                        :min="0"
                        :max="5"
                        :precision="2"
                        controls-position="right"
                        />
                    </el-col>
                  </el-row>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="是否有论文">
                  <el-select v-model="searchForm.hasThesis" placeholder="请选择" clearable>
                    <el-option label="有论文" :value="true"  />
                    <el-option label="无论文" :value="false"  />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="排序方式">
                  <el-row :gutter="10">
                    <el-col :span="12">
                      <el-select v-model="searchForm.sortBy" placeholder="排序字段">
                        <el-option
                          v-for="item in educationHistorySortOptions"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                         />
                      </el-select>
                    </el-col>
                    <el-col :span="12">
                      <el-select v-model="searchForm.sortDirection" placeholder="排序方向">
                        <el-option label="升序" value="ASC"  />
                        <el-option label="降序" value="DESC"  />
                      </el-select>
                    </el-col>
                  </el-row>
                </el-form-item>
              </el-col>
            </el-row>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total">
              <el-icon><School /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.total }}</div>
              <div class="stats-label">教育记录总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon doctoral">
              <el-icon><Star /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.doctoral }}</div>
              <div class="stats-label">博士学历</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon master">
              <el-icon><Medal /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.master }}</div>
              <div class="stats-label">硕士学历</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon verified">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.verified }}</div>
              <div class="stats-label">已验证记录</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 教育经历列表 -->
    <el-card class="table-card" shadow="never">
      <div class="table-header">
        <span class="table-title">教育经历列表</span>
        <div class="table-actions">
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增教育经历
          </el-button>
          <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
          <el-button @click="handleImport">
            <el-icon><Upload /></el-icon>
            导入
          </el-button>
          <el-button 
            type="danger" 
            :disabled="selectedRows.length === 0"
            @click="handleBatchDelete"
          >
            <el-icon><Delete /></el-icon>
            批量删除
          </el-button>
          <el-button 
            type="success" 
            :disabled="selectedRows.length === 0"
            @click="handleBatchVerify"
          >
            <el-icon><CircleCheck /></el-icon>
            批量验证
          </el-button>
        </div>
      </div>

      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="employeeName" label="员工姓名" width="120"  />
        <el-table-column prop="degree" label="学历" width="100"  />
        <el-table-column prop="graduationSchool" label="毕业院校" min-width="180" show-overflow-tooltip  />
        <el-table-column prop="major" label="专业" min-width="150" show-overflow-tooltip  />
        <el-table-column prop="studyFormName" label="学习形式" width="100">
          <template #default="scope">
            <el-tag :type="getStudyFormTagType(scope.row.studyForm)" size="small">
              {{ scope.row.studyFormName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="startDate" label="开始时间" width="110"  />
        <el-table-column prop="endDate" label="结束时间" width="110"  />
        <el-table-column prop="gpa" label="绩点" width="80" align="center">
          <template #default="scope">
            <span v-if="scope.row.gpa">{{ scope.row.gpa.toFixed(2) }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="verificationStatusName" label="验证状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getVerificationStatusTagType(scope.row.verificationStatus)" size="small">
              {{ scope.row.verificationStatusName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" link @click="handleView(scope.row)">
              查看
            </el-button>
            <el-button size="small" type="primary" link @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button 
              v-if="scope.row.verificationStatus === 'PENDING'"
              size="small" 
              type="success" 
              link 
              @click="handleVerify(scope.row)"
            >
              验证
            </el-button>
            <el-button size="small" type="danger" link @click="handleDelete(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 教育经历详情/编辑对话框 -->
    <EducationHistoryDialog
      v-model:visible="dialogVisible"
      :education-history="currentEducationHistory"
      :mode="dialogMode"
      @success="handleDialogSuccess"
    />

    <!-- 验证对话框 -->
    <VerificationDialog
      v-model:visible="verificationDialogVisible"
      :education-history="currentEducationHistory"
      :batch-mode="batchVerificationMode"
      :selected-rows="selectedRows"
      @success="handleVerificationSuccess"
    />

    <!-- 导入对话框 -->
    <ImportDialog
      v-model:visible="importDialogVisible"
      title="导入教育经历"
      :upload-url="'/api/v1/education-history/import'"
      :template-url="'/api/v1/education-history/template'"
      @success="handleImportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  Download,
  Upload,
  Delete,
  School,
  Star,
  Medal,
  CircleCheck
} from '@element-plus/icons-vue'
import { 
  educationHistoryApi, 
  degreeOptions, 
  studyFormOptions, 
  verificationStatusOptions,
  educationHistorySortOptions 
} from '@/api/educationHistory'
import type { 
  EducationHistory, 
  EducationHistoryQueryRequest,
  EducationHistoryStatistics 
} from '@/types/educationHistory'
import EducationHistoryDialog from './components/EducationHistoryDialog.vue'
import VerificationDialog from './components/VerificationDialog.vue'
import HrImportDialog from '@/components/HrImportDialog.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref<EducationHistory[]>([])
const selectedRows = ref<EducationHistory[]>([])
const activeCollapse = ref<string[]>([])

// 搜索表单
const searchForm = reactive<EducationHistoryQueryRequest>({
  page: 0,
  size: 20,
  keyword: '',
  degree: '',
  studyForm: undefined,
  graduationSchool: '',
  major: '',
  employeeId: '',
  endDateStart: '',
  endDateEnd: '',
  minGpa: undefined,
  maxGpa: undefined,
  verificationStatus: undefined,
  hasThesis: undefined,
  sortBy: 'endDate',
  sortDirection: 'DESC' as unknown
})

// 日期范围
const graduationDateRange = ref<[string, string] | null>(null)

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 统计信息
const stats = reactive({
  total: 0,
  doctoral: 0,
  master: 0,
  verified: 0
})

// 对话框相关
const dialogVisible = ref(false)
const dialogMode = ref<'view' | 'add' | 'edit'>('view')
const currentEducationHistory = ref<EducationHistory | null>(null)

// 验证对话框
const verificationDialogVisible = ref(false)
const batchVerificationMode = ref(false)

// 导入对话框
const importDialogVisible = ref(false)

// 获取教育经历列表
const fetchEducationHistory = async () => {
  try {
    loading.value = true
    const params = {
      ...searchForm,
      page: pagination.page - 1, // 后端从0开始
      size: pagination.size
    }
    
    const result = await educationHistoryApi.query(params)
    tableData.value = result.content as unknown // 临时修复类型不匹配
    pagination.total = result.totalElements
  } catch (__error) {
    ElMessage.error('获取教育经历列表失败')
  } finally {
    loading.value = false
  }
}

// 获取统计信息
const fetchStats = async () => {
  try {
    const statistics = await educationHistoryApi.getStatistics()
    stats.total = statistics.totalCount
    stats.doctoral = statistics.degreeDistribution['博士研究生'] || 0
    stats.master = statistics.degreeDistribution['硕士研究生'] || 0
    stats.verified = statistics.verificationStatusDistribution['VERIFIED'] || 0
  } catch (__error) {
    }
}

// 日期范围变化处理
const handleDateRangeChange = (value: [string, string] | null) => {
  if (value) {
    searchForm.endDateStart = value[0]
    searchForm.endDateEnd = value[1]
  } else {
    searchForm.endDateStart = ''
    searchForm.endDateEnd = ''
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchEducationHistory()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    page: 0,
    size: 20,
    keyword: '',
    degree: '',
    studyForm: undefined,
    graduationSchool: '',
    major: '',
    employeeId: '',
    endDateStart: '',
    endDateEnd: '',
    minGpa: undefined,
    maxGpa: undefined,
    verificationStatus: undefined,
    hasThesis: undefined,
    sortBy: 'endDate',
    sortDirection: 'DESC'
  })
  graduationDateRange.value = null
  pagination.page = 1
  fetchEducationHistory()
}

// 新增教育经历
const handleAdd = () => {
  currentEducationHistory.value = null
  dialogMode.value = 'add'
  dialogVisible.value = true
}

// 查看教育经历
const handleView = (educationHistory: EducationHistory) => {
  currentEducationHistory.value = educationHistory
  dialogMode.value = 'view'
  dialogVisible.value = true
}

// 编辑教育经历
const handleEdit = (educationHistory: EducationHistory) => {
  currentEducationHistory.value = educationHistory
  dialogMode.value = 'edit'
  dialogVisible.value = true
}

// 验证教育经历
const handleVerify = (educationHistory: EducationHistory) => {
  currentEducationHistory.value = educationHistory
  batchVerificationMode.value = false
  verificationDialogVisible.value = true
}

// 批量验证
const handleBatchVerify = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要验证的记录')
    return
  }
  batchVerificationMode.value = true
  verificationDialogVisible.value = true
}

// 删除教育经历
const handleDelete = async (educationHistory: EducationHistory) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除 "${educationHistory.graduationSchool} - ${educationHistory.degree}" 的教育经历吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await educationHistoryApi.delete(educationHistory.id!)
    ElMessage.success('删除成功')
    fetchEducationHistory()
    fetchStats()
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 条教育经历记录吗？此操作不可恢复。`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const ids = selectedRows.value.map(row => row.id!).filter(id => id)
    await educationHistoryApi.batchDelete(ids)
    ElMessage.success('批量删除成功')
    fetchEducationHistory()
    fetchStats()
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

// 表格选择变化
const handleSelectionChange = (selection: EducationHistory[]) => {
  selectedRows.value = selection
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchEducationHistory()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchEducationHistory()
}

// 对话框成功回调
const handleDialogSuccess = () => {
  fetchEducationHistory()
  fetchStats()
}

// 验证成功回调
const handleVerificationSuccess = () => {
  fetchEducationHistory()
  fetchStats()
}

// 导出
const handleExport = async () => {
  try {
    const blob = await educationHistoryApi.export(searchForm)
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `教育经历_${new Date().toISOString().slice(0, 10)}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('导出成功')
  } catch (__error) {
    ElMessage.error('导出失败')
  }
}

// 导入
const handleImport = () => {
  importDialogVisible.value = true
}

// 导入成功回调
const handleImportSuccess = () => {
  fetchEducationHistory()
  fetchStats()
}

// 获取学习形式标签类型
const getStudyFormTagType = (studyForm: string) => {
  switch (studyForm) {
    case 'FULL_TIME':
      return 'success'
    case 'PART_TIME':
      return 'warning'
    case 'CORRESPONDENCE':
      return 'info'
    case 'SELF_STUDY':
      return 'danger'
    case 'ONLINE':
      return ''
    default:
      return ''
  }
}

// 获取验证状态标签类型
const getVerificationStatusTagType = (status: string) => {
  switch (status) {
    case 'VERIFIED':
      return 'success'
    case 'PENDING':
      return 'warning'
    case 'REJECTED':
      return 'danger'
    default:
      return ''
  }
}

// 初始化
onMounted(() => {
  fetchEducationHistory()
  fetchStats()
})
</script>

<style scoped>
.education-history-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  padding: 10px 0;
}

.advanced-search {
  margin-top: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.doctoral {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.master {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.verified {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .education-history-management {
    padding: 10px;
  }

  .search-form .el-row {
    flex-direction: column;
  }

  .search-form .el-col {
    width: 100%;
    margin-bottom: 10px;
  }

  .stats-row .el-col {
    margin-bottom: 10px;
  }

  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .table-actions {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header) {
  background-color: #f8f9fa;
}

:deep(.el-table__header th) {
  background-color: #f8f9fa;
  color: #303133;
  font-weight: 600;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

/* 搜索表单样式 */
:deep(.el-form-item) {
  margin-bottom: 10px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

/* 折叠面板样式 */
:deep(.el-collapse-item__header) {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 0 16px;
  font-weight: 500;
}

:deep(.el-collapse-item__content) {
  padding: 20px 16px;
  background-color: #fafafa;
  border-radius: 0 0 4px 4px;
}

/* 标签样式 */
.el-tag {
  border-radius: 4px;
  font-weight: 500;
}

/* 按钮样式 */
.el-button {
  border-radius: 6px;
  font-weight: 500;
}

.el-button--primary {
  background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
  border: none;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #66b1ff 0%, #40a9ff 100%);
}

.el-button--success {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  border: none;
}

.el-button--success:hover {
  background: linear-gradient(135deg, #85ce61 0%, #95d475 100%);
}
</style>
