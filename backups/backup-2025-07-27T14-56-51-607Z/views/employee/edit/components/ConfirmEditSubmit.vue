<template>
  <div class="confirm-edit-submit">
    <!-- 提示信息 -->
    <el-alert
      :title="`您即将提交 ${changedFields.length} 项变更`"
      type="warning"
      show-icon
      :closable="false"
     />

    <!-- 变更概览 -->
    <div class="changes-overview">
      <h3>变更概览</h3>
      <el-table :data="changedFieldsGrouped" style="width: 100%" row-key="group">
        <el-table-column type="expand">
          <template #default="{ row }">
            <el-table :data="row.fields" style="width: 100%; margin-left: 50px;">
              <el-table-column prop="fieldLabel" label="字段" width="150"  />
              <el-table-column label="原值" min-width="200">
                <template #default="{ row: field }">
                  <span class="old-value">{{ formatFieldValue(field.oldValue) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="新值" min-width="200">
                <template #default="{ row: field }">
                  <span class="new-value">{{ formatFieldValue(field.newValue) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="需要审批" width="100" align="center">
                <template #default="{ row: field }">
                  <el-tag v-if="field.needApproval" type="warning" size="small">是</el-tag>
                  <el-tag v-else type="info" size="small">否</el-tag>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </el-table-column>
        <el-table-column prop="group" label="信息类别" width="150"  />
        <el-table-column prop="count" label="变更数量" width="100" align="center">
          <template #default="{ row }">
            <el-badge :value="row.count" class="item" type="warning"  />
          </template>
        </el-table-column>
        <el-table-column label="需要审批" width="100" align="center">
          <template #default="{ row }">
            <el-tag v-if="row.needApproval" type="warning" size="small">是</el-tag>
            <el-tag v-else type="info" size="small">否</el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 变更原因 -->
    <div class="change-reason">
      <h3>变更原因</h3>
      <el-form ref="formRef" :model="formData" :rules="rules">
        <el-form-item prop="changeReason">
          <el-input
            v-model="formData.changeReason"
            type="textarea"
            :rows="4"
            placeholder="请说明本次信息变更的原因"
            maxlength="500"
            show-word-limit
            />
        </el-form-item>
        
        <el-form-item label="附件材料" prop="attachments">
          <HrFileUpload
            v-model="formData.attachments"
            :config="uploadConfig"
            :rules="uploadRules"
            :limit="5"
            tip="如有相关证明材料，请上传（支持PDF、JPG、PNG格式）"
          />
        </el-form-item>
      </el-form>
    </div>

    <!-- 审批流程说明 -->
    <div class="approval-info" v-if="needApproval">
      <h3>审批流程</h3>
      <el-steps :active="0" align-center finish-status="success">
        <el-step title="提交申请" description="您提交变更申请"  />
        <el-step title="二级单位审核" description="所属部门负责人审核"  />
        <el-step title="人事处审批" description="人事处最终审批"  />
        <el-step title="变更生效" description="审批通过后自动更新"  />
      </el-steps>
      <el-alert
        title="需要审批的字段变更将在审批通过后生效，不需要审批的字段将立即更新。"
        type="info"
        :closable="false"
        class="mt-20"
       />
    </div>

    <!-- 确认提交 -->
    <div class="submit-confirm">
      <el-checkbox v-model="confirmed">
        我已仔细核对以上变更信息，确认无误
      </el-checkbox>
    </div>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { HrFileUpload } from '@/components'
import type { EmployeeDetail } from '@/types/employee'

interface Props {
  formData: Partial<EmployeeDetail>
  originalData: EmployeeDetail
  changedFields: Array<{
    fieldName: string
    fieldLabel: string
   
    oldValue: unknown
   
    newValue: unknown
  }>
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'validate': [valid: boolean]
}>()

// 表单实例
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive({
  changeReason: '',
  attachments: [] as unknown[]
})

// 确认状态
const confirmed = ref(false)

// 上传配置
const uploadConfig = {
  action: '/api/upload',
  headers: {
    Authorization: `Bearer ${localStorage.getItem('token')}`
  }
}

// 上传规则
const uploadRules = {
  accept: ['application/pdf', 'image/jpeg', 'image/png'],
  maxSize: 10 // 10MB
}

// 表单规则
const rules: FormRules = {
  changeReason: [
    { required: true, message: '请说明变更原因', trigger: 'blur' },
    { min: 10, message: '变更原因不少于10个字', trigger: 'blur' }
  ]
}

// 需要审批的字段列表（根据业务规则配置）
const approvalRequiredFields = [
  'fullName',
  'idNumber',
  'institutionId',
  'positionId',
  'hireDate',
  'workStartDate'
]

// 字段分组映射
const fieldGroupMap: Record<string, string> = {
  fullName: '基本信息',
  gender: '基本信息',
  dateOfBirth: '基本信息',
  ethnicity: '基本信息',
  politicalStatus: '基本信息',
  nativePlace: '基本信息',
  placeOfBirth: '基本信息',
  idType: '基本信息',
  idNumber: '基本信息',
  phoneNumber: '联系方式',
  email: '联系方式',
  institutionId: '工作信息',
  institutionName: '工作信息',
  positionId: '工作信息',
  positionName: '工作信息',
  workStartDate: '工作信息',
  hireDate: '工作信息',
  emergencyContact: '其他信息',
  emergencyContactPhone: '其他信息',
  currentAddress: '其他信息',
  postalCode: '其他信息'
}

// 按组分类的变更字段
const changedFieldsGrouped = computed(() => {
  const groups: Record<string, unknown> = {}

  props.changedFields.forEach(field => {
    const group = fieldGroupMap[field.fieldName] || '其他'
    if (!groups[group]) {
      groups[group] = {
        group,
        fields: [],
        count: 0,
        needApproval: false
      }
    }

    const fieldWithApproval = {
      ...field,
      needApproval: approvalRequiredFields.includes(field.fieldName)
    }

    groups[group].fields.push(fieldWithApproval)
    groups[group].count++
    if (fieldWithApproval.needApproval) {
      groups[group].needApproval = true
    }
  })

  return Object.values(groups)
})

// 是否需要审批
const needApproval = computed(() => {
  return props.changedFields.some(field => 
    approvalRequiredFields.includes(field.fieldName)
  )
})

// 格式化字段值
   
const formatFieldValue = (value: unknown): string => {
  if (value === null || value === undefined || value === '') {
    return '-'
  }
  if (typeof value === 'boolean') {
    return value ? '是' : '否'
  }
  if (value === 'male') return '男'
  if (value === 'female') return '女'
  return String(value)
}

// 验证
const validate = async () => {
  try {
    await formRef.value?.validate()
    
    if (!confirmed.value) {
      throw new Error('请确认变更信息')
    }
    
    emit('validate', true)
    return true
  } catch (__error) {
    emit('validate', false)
    return false
  }
}

// 获取变更原因
const changeReason = computed(() => formData.changeReason)

// 暴露方法和数据
defineExpose({
  validate,
  changeReason
})
</script>

<style lang="scss" scoped>
.confirm-edit-submit {
  .changes-overview {
    margin-top: 30px;
    
    h3 {
      margin-bottom: 15px;
      font-size: 16px;
      font-weight: 500;
    }
    
    .old-value {
      color: var(--el-color-danger);
      text-decoration: line-through;
    }
    
    .new-value {
      color: var(--el-color-success);
      font-weight: 500;
    }
  }
  
  .change-reason {
    margin-top: 30px;
    
    h3 {
      margin-bottom: 15px;
      font-size: 16px;
      font-weight: 500;
    }
  }
  
  .approval-info {
    margin-top: 30px;
    
    h3 {
      margin-bottom: 15px;
      font-size: 16px;
      font-weight: 500;
    }
  }
  
  .submit-confirm {
    margin-top: 30px;
    padding: 20px;
    text-align: center;
    background-color: var(--el-fill-color-lighter);
    border-radius: 4px;
    
    :deep(.el-checkbox) {
      .el-checkbox__label {
        font-size: 16px;
        font-weight: 500;
      }
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .confirm-edit-submit {
    :deep(.el-table) {
      font-size: 12px;
    }
    
    :deep(.el-steps) {
      .el-step__title {
        font-size: 12px;
      }
      
      .el-step__description {
        display: none;
      }
    }
  }
}
</style>