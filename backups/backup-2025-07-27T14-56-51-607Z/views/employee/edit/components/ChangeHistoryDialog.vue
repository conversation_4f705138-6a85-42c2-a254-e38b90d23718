<template>
  <el-dialog
    :model-value="modelValue"
    title="变更历史"
    width="900px"
    @update:model-value="handleClose"
  >
    <!-- 筛选条件 -->
    <div class="filter-bar">
      <el-form :inline="true">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            @change="handleSearch"
           />
        </el-form-item>
        <el-form-item label="操作人">
          <el-input
            v-model="filterForm.operator"
            placeholder="请输入操作人"
            clearable
            @clear="handleSearch"
            @keyup.enter="handleSearch"
            />
        </el-form-item>
        <el-form-item label="字段名称">
          <el-select
            v-model="filterForm.fieldName"
            placeholder="全部字段"
            clearable
            @change="handleSearch"
          >
            <el-option
              v-for="field in fieldOptions"
              :key="field.value"
              :label="field.label"
              :value="field.value"
             />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 变更历史列表 -->
    <el-table
      :data="historyList"
      v-loading="loading"
      style="width: 100%"
      max-height="400"
    >
      <el-table-column prop="operationTime" label="变更时间" width="160">
        <template #default="{ row }">
          {{ formatDateTime(row.operationTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="fieldLabel" label="字段名称" width="120"  />
      <el-table-column label="原值" min-width="150">
        <template #default="{ row }">
          <span class="old-value">{{ formatFieldValue(row.oldValue) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="新值" min-width="150">
        <template #default="{ row }">
          <span class="new-value">{{ formatFieldValue(row.newValue) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="operatorName" label="操作人" width="100"  />
      <el-table-column label="审批状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag
            v-if="row.approvalStatus"
            :type="getApprovalStatusType(row.approvalStatus)"
            size="small"
          >
            {{ getApprovalStatusText(row.approvalStatus) }}
          </el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="80" fixed="right">
        <template #default="{ row }">
          <el-button
            v-if="row.changeReason || row.attachments"
            link
            type="primary"
            size="small"
            @click="handleViewDetail(row)"
          >
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="pagination.current"
      v-model:page-size="pagination.pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :total="pagination.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      class="mt-20"
     />

    <!-- 详情对话框 -->
    <el-dialog
      v-model="showDetail"
      title="变更详情"
      width="600px"
      append-to-body
    >
      <el-descriptions :column="1" border>
        <el-descriptions-item label="变更时间">
          {{ formatDateTime(currentDetail.operationTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="操作人">
          {{ currentDetail.operatorName }}
        </el-descriptions-item>
        <el-descriptions-item label="字段名称">
          {{ currentDetail.fieldLabel }}
        </el-descriptions-item>
        <el-descriptions-item label="原值">
          <span class="old-value">{{ formatFieldValue(currentDetail.oldValue) }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="新值">
          <span class="new-value">{{ formatFieldValue(currentDetail.newValue) }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="变更原因">
          {{ currentDetail.changeReason || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="附件材料" v-if="currentDetail.attachments?.length">
          <div class="attachments">
            <el-link
              v-for="(file, index) in currentDetail.attachments"
              :key="index"
              type="primary"
              :href="file.url"
              target="_blank"
              :underline="false"
              class="attachment-link"
            >
              {{ file.name }}
            </el-link>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="审批流程" v-if="currentDetail.approvalRecords?.length">
          <el-timeline>
            <el-timeline-item
              v-for="record in currentDetail.approvalRecords"
              :key="record.id"
              :timestamp="formatDateTime(record.time)"
              placement="top"
            >
              <div class="approval-record">
                <span class="approver">{{ record.approverName }}</span>
                <el-tag :type="record.result === 'approved' ? 'success' : 'danger'" size="small">
                  {{ record.result === 'approved' ? '通过' : '驳回' }}
                </el-tag>
                <div v-if="record.comment" class="comment">
                  审批意见：{{ record.comment }}
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </el-dialog>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import employeeApi from '@/api/modules/employee'
interface Props {
  modelValue: boolean
  employeeId: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 加载状态
const loading = ref(false)

// 筛选表单
const filterForm = reactive({
  operator: '',
  fieldName: ''
})

// 日期范围
const dateRange = ref<[string, string] | null>(null)

// 分页
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

// 历史记录列表
const historyList = ref<unknown[]>([])

// 是否显示详情
const showDetail = ref(false)

// 当前详情
const currentDetail = ref<unknown>({})

// 字段选项
const fieldOptions = [
  { label: '姓名', value: 'fullName' },
  { label: '性别', value: 'gender' },
  { label: '出生日期', value: 'dateOfBirth' },
  { label: '民族', value: 'ethnicity' },
  { label: '政治面貌', value: 'politicalStatus' },
  { label: '联系电话', value: 'phoneNumber' },
  { label: '邮箱', value: 'email' },
  { label: '所属机构', value: 'institutionName' },
  { label: '当前岗位', value: 'positionName' },
  { label: '入职日期', value: 'hireDate' },
  // 添加更多字段...
]

// 字段映射
const fieldLabelMap: Record<string, string> = {
  fullName: '姓名',
  gender: '性别',
  dateOfBirth: '出生日期',
  ethnicity: '民族',
  politicalStatus: '政治面貌',
  phoneNumber: '联系电话',
  email: '邮箱',
  institutionName: '所属机构',
  positionName: '当前岗位',
  hireDate: '入职日期',
  // 添加更多映射...
}

// 组件挂载时
onMounted(() => {
  if (props.modelValue) {
    loadHistory()
  }
})

// 加载历史记录
const loadHistory = async () => {
  try {
    loading.value = true
    
    const params = {
      employeeId: props.employeeId,
      ...filterForm,
      startDate: dateRange.value?.[0],
      endDate: dateRange.value?.[1],
      page: pagination.current,
      pageSize: pagination.pageSize
    }
    
    const {data: _data} =  await employeeApi.getChangeHistory(params)
    
    // 添加字段标签
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
    historyList.value 
  padding: 15px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 4px;
}

.old-value {
  color: var(--el-color-danger);
  text-decoration: line-through;
}

.new-value {
  color: var(--el-color-success);
  font-weight: 500;
}

.attachments {
  display: flex;
  flex-direction: column;
  gap: 5px;
  
  .attachment-link {
    justify-content: flex-start;
  }
}

.approval-record {
  .approver {
    margin-right: 10px;
    font-weight: 500;
  }
  
  .comment {
    margin-top: 5px;
    color: var(--el-text-color-secondary);
    font-size: 12px;
  }
}
</style>