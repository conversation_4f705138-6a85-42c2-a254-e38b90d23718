<template>
  <div class="employee-edit">
    <!-- 页面头部 -->
    <el-page-header @back="handleBack">
      <template #content>
        <div class="header-content">
          <span class="text-lg font-600">编辑员工信息</span>
          <el-tag v-if="originalData.employeeNumber" type="info" class="ml-10">
            工号：{{ originalData.employeeNumber }}
          </el-tag>
        </div>
      </template>
      <template #extra>
        <el-button @click="handleViewHistory">
          <el-icon><Clock /></el-icon>
          变更历史
        </el-button>
      </template>
    </el-page-header>

    <!-- 变更提示 -->
    <el-alert
      v-if="hasChanges"
      :title="`您已修改了 ${changedFieldsCount} 个字段`"
      type="warning"
      show-icon
      :closable="false"
      class="change-alert"
    >
      <el-button size="small" text @click="showChanges = true">
        查看变更详情
      </el-button>
    </el-alert>

    <!-- 步骤条 -->
    <el-card class="steps-card">
      <el-steps :active="activeStep" align-center finish-status="success">
        <el-step title="基本信息" description="编辑员工基本信息"  />
        <el-step title="教育经历" description="编辑学习经历"  />
        <el-step title="工作经历" description="编辑工作经历"  />
        <el-step title="家庭信息" description="编辑家庭成员信息"  />
        <el-step title="其他信息" description="编辑其他必要信息"  />
        <el-step title="确认提交" description="确认变更并提交"  />
      </el-steps>
    </el-card>

    <!-- 表单内容区 -->
    <el-card class="form-card" v-loading="loading" :element-loading-text="loadingText">
      <div class="form-container">
        <!-- 复用新增页面的表单组件 -->
        <component
          :is="currentStepComponent"
          v-model="formData"
          v-bind="currentStepProps"
          ref="currentFormRef"
          @validate="handleStepValidate"
        />
      </div>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button @click="handleSaveDraft" :loading="savingDraft">
          <el-icon><Document /></el-icon>
          保存草稿
        </el-button>
        <div class="step-actions">
          <el-button v-if="activeStep > 0" @click="handlePrev">
            上一步
          </el-button>
          <el-button
            v-if="activeStep < 5"
            type="primary"
            @click="handleNext"
            :loading="validating"
          >
            下一步
          </el-button>
          <el-button
            v-if="activeStep === 5"
            type="primary"
            @click="handleSubmit"
            :loading="submitting"
          >
            提交变更
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 变更详情对话框 -->
    <el-dialog
      v-model="showChanges"
      title="变更详情"
      width="800px"
      class="changes-dialog"
    >
      <el-table :data="changedFields" style="width: 100%">
        <el-table-column prop="fieldName" label="字段名称" width="150"  />
        <el-table-column prop="fieldLabel" label="字段说明" width="150"  />
        <el-table-column label="原值" min-width="150">
          <template #default="{ row }">
            <span class="old-value">{{ formatFieldValue(row.oldValue) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="新值" min-width="150">
          <template #default="{ row }">
            <span class="new-value">{{ formatFieldValue(row.newValue) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 变更历史对话框 -->
    <ChangeHistoryDialog
      v-model="showHistory"
      :employee-id="employeeId"
    />
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
defineOptions({
  name: 'EditPage'
})

import { ref, reactive, computed, watch, onMounted, shallowRef } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Document, Clock } from '@element-plus/icons-vue'
import { cloneDeep, isEqual } from 'lodash-es'

// 导入表单组件
import BasicInfoForm from '../add/components/BasicInfoForm.vue'
import EducationForm from '../add/components/EducationForm.vue'
import WorkForm from '../add/components/WorkForm.vue'
import FamilyForm from '../add/components/FamilyForm.vue'
import OtherInfoForm from '../add/components/OtherInfoForm.vue'
import ConfirmEditSubmit from './components/ConfirmEditSubmit.vue'
import ChangeHistoryDialog from './components/ChangeHistoryDialog.vue'

import employeeApi from '@/api/modules/employee'
import type { EmployeeDetail } from '@/types/employee'

// 路由实例
const route = useRoute()
const router = useRouter()

// 员工ID
const employeeId = computed(() => route.params.id as string)

// 当前步骤
const activeStep = ref(0)

// 加载状态
const loading = ref(false)
const loadingText = ref('加载中...')
const validating = ref(false)
const submitting = ref(false)
const savingDraft = ref(false)

// 原始数据（用于对比变更）
const originalData = ref<EmployeeDetail>({} as EmployeeDetail)

// 表单数据
const formData = reactive<Partial<EmployeeDetail>>({})

// 步骤组件映射
const stepComponents = [
  BasicInfoForm,
  EducationForm,
  WorkForm,
  FamilyForm,
  OtherInfoForm,
  ConfirmEditSubmit
]

// 当前步骤组件
const currentStepComponent = computed(() => stepComponents[activeStep.value])

// 当前步骤属性
const currentStepProps = computed(() => {
  if (activeStep.value === 1) {
    return { educationList: formData.educationHistory || [] }
  }
  if (activeStep.value === 2) {
    return { workList: formData.workExperience || [] }
  }
  if (activeStep.value === 3) {
    return { familyList: formData.familyMembers || [] }
  }
  if (activeStep.value === 5) {
    return { 
      formData,
      originalData: originalData.value,
      changedFields: changedFields.value
    }
  }
  return {}
})

// 表单引用
const currentFormRef = ref<unknown>()

// 是否显示变更详情
const showChanges = ref(false)

// 是否显示变更历史
const showHistory = ref(false)

// 字段映射（用于显示字段中文名）
const fieldLabelMap: Record<string, string> = {
  fullName: '姓名',
  gender: '性别',
  dateOfBirth: '出生日期',
  ethnicity: '民族',
  politicalStatus: '政治面貌',
  nativePlace: '籍贯',
  placeOfBirth: '出生地',
  idType: '证件类型',
  idNumber: '证件号码',
  workStartDate: '参加工作时间',
  hireDate: '入职日期',
  phoneNumber: '联系电话',
  email: '邮箱',
  institutionName: '所属机构',
  positionName: '当前岗位',
  highLevelTalentCategory: '高层次人才类别',
  emergencyContact: '紧急联系人',
  emergencyContactPhone: '紧急联系人电话',
  currentAddress: '现住址',
  postalCode: '邮政编码',
  // 添加更多字段映射...
}

// 计算变更的字段
const changedFields = computed(() => {
  const changes: Array<{
    fieldName: string
    fieldLabel: string
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
    oldValue: unknown
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
    newValue: unknown
  }> = []

  // 比较基本字段
  Object.keys(formData).forEach(key => {
    if (key === 'educationHistory' || key === 'workExperience' || key === 'familyMembers') {
      // 特殊处理数组类型
      return
    }

    const oldVal = originalData.value[key as keyof EmployeeDetail]
    const newVal = formData[key as keyof EmployeeDetail]

    if (!isEqual(oldVal, newVal)) {
      changes.push({
        fieldName: key,
        fieldLabel: fieldLabelMap[key] || key,
        oldValue: oldVal,
        newValue: newVal
      })
    }
  })

  // 比较教育经历、工作经历、家庭成员等数组类型的变更
  // 教育经历
  if (JSON.stringify(editForm.value.educationHistory) !== JSON.stringify(originalData.value.educationHistory)) {
    changes.push({
      field: 'educationHistory',
      label: '教育经历',
      oldValue: originalData.value.educationHistory?.length + '条记录' || '无',
      newValue: editForm.value.educationHistory?.length + '条记录' || '无'
    })
  }
  
  // 工作经历
  if (JSON.stringify(editForm.value.workHistory) !== JSON.stringify(originalData.value.workHistory)) {
    changes.push({
      field: 'workHistory',
      label: '工作经历',
      oldValue: originalData.value.workHistory?.length + '条记录' || '无',
      newValue: editForm.value.workHistory?.length + '条记录' || '无'
    })
  }
  
  // 家庭成员
  if (JSON.stringify(editForm.value.familyMembers) !== JSON.stringify(originalData.value.familyMembers)) {
    changes.push({
      field: 'familyMembers',
      label: '家庭成员',
      oldValue: originalData.value.familyMembers?.length + '条记录' || '无',
      newValue: editForm.value.familyMembers?.length + '条记录' || '无'
    })
  }
  
  // 证件信息
  if (JSON.stringify(editForm.value.certificates) !== JSON.stringify(originalData.value.certificates)) {
    changes.push({
      field: 'certificates',
      label: '证件信息',
      oldValue: originalData.value.certificates?.length + '条记录' || '无',
      newValue: editForm.value.certificates?.length + '条记录' || '无'
    })
  }
  
  // 附件信息
  if (JSON.stringify(editForm.value.attachments) !== JSON.stringify(originalData.value.attachments)) {
    changes.push({
      field: 'attachments',
      label: '附件',
      oldValue: originalData.value.attachments?.length + '个文件' || '无',
      newValue: editForm.value.attachments?.length + '个文件' || '无'
    })
  }

  return changes
})

// 是否有变更
const hasChanges = computed(() => changedFields.value.length > 0)

// 变更字段数量
const changedFieldsCount = computed(() => changedFields.value.length)

// 组件挂载时
onMounted(() => {
  loadEmployeeData()
})

// 加载员工数据
const loadEmployeeData = async () => {
  try {
    loading.value = true
    loadingText.value = '正在加载员工信息...'
    
    const {data: _data} =  await employeeApi.getDetail(employeeId.value)
    
    // 保存原始数据
    originalData.value 
  
  .header-content {
    display: flex;
    align-items: center;
  }
  
  .change-alert {
    margin-top: 20px;
  }
  
  .steps-card {
    margin-top: 20px;
    
    :deep(.el-card__body) {
      padding: 30px 20px;
    }
  }
  
  .form-card {
    margin-top: 20px;
    min-height: 500px;
    
    .form-container {
      min-height: 400px;
      padding: 20px;
    }
    
    .form-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px;
      border-top: 1px solid var(--el-border-color-lighter);
      
      .step-actions {
        display: flex;
        gap: 10px;
      }
    }
  }
}

.changes-dialog {
  .old-value {
    color: var(--el-color-danger);
    text-decoration: line-through;
  }
  
  .new-value {
    color: var(--el-color-success);
    font-weight: 500;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .employee-edit {
    padding: 10px;
    
    .steps-card {
      :deep(.el-step__title) {
        font-size: 12px;
      }
      
      :deep(.el-step__description) {
        display: none;
      }
    }
    
    .form-card {
      .form-actions {
        flex-direction: column;
        gap: 10px;
        
        .step-actions {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }
  }
}
</style>