/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * 员工编辑页面单元测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
  import { mount } from '@vue/test-utils'
  import { createPinia } from 'pinia'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import EmployeeEdit from '../index.vue';
  import employeeApi from '@/api/modules/employee';
  import type { EmployeeDetail } from '@/types/employee'

// Mock API
vi.mock('@/api/modules/employee', () => ({
  default: {
    getDetail: vi.fn(),
    update: vi.fn(),
    logChanges: vi.fn(),
    getChangeHistory: vi.fn()
  }
}))

// Mock 路由
const mockPush = vi.fn()
const mockBack = vi.fn()
const mockParams = { id: '123'
  };
  vi.mock('vue-router', () => ({
  useRoute: () => ({
    params: mockParams
  }),
  useRouter: () => ({
    push: mockPush,
    back: mockBack
  })
}))

// Mock ElMessage 和 ElMessageBox
vi.mock('element-plus', async () => {
  const actual = await vi.importActual('element-plus')
  return {
    ...actual,
    ElMessage: {
      success: vi.fn(),
      error: vi.fn(),
      warning: vi.fn(),
      info: vi.fn()
    },
    ElMessageBox: {
      confirm: vi.fn().mockResolvedValue(true)
    }
  }
})

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
},
  Object.defineProperty(window, 'localStorage', { value: localStorageMock })

// Mock lodash-es
vi.mock('lodash-es', () => ({
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  cloneDeep: (obj: any) => JSON.parse(JSON.stringify(obj)),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  isEqual: (a: any, b: any) => JSON.stringify(a) === JSON.stringify(b)
}))

describe('EmployeeEdit', () => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let wrapper: any
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let pinia: any
  
  const mockEmployeeDetail: EmployeeDetail = {
    employeeId: '123',
    employeeNumber: '2024001',
    fullName: '张三',
    gender: 'male',
    dateOfBirth: '1990-01-01',
    ethnicity: '汉族',
    politicalStatus: '党员',
    nativePlace: '浙江杭州',
    placeOfBirth: '浙江杭州',
    idType: 'idCard',
    idNumber: '330100199001010001',
    workStartDate: '2020-01-01',
    hireDate: '2020-01-01',
    personnelStatus: 'active',
    institutionId: 'dept1',
    institutionName: '信息技术部',
    positionId: 'pos1',
    positionName: '前端工程师',
    phoneNumber: '13800138000',
    email: '<EMAIL>',
    educationDegree: '本科',
    professionalTitle: '中级',
    infoCompletenessPercentage: 85,
    highLevelTalentCategory: 'C',
    educationHistory: [],
    workExperience: [],
    familyMembers: []
  },
  beforeEach(() => {
    // 创建 Pinia 实例
    pinia = createPinia()
    
    // 重置 mock
    vi.clearAllMocks()
    
    // Mock API 响应
    vi.mocked(employeeApi.getDetail).mockResolvedValue({
      code: 200,
      message: 'success',
      data: mockEmployeeDetail
    })
    
    vi.mocked(employeeApi.update).mockResolvedValue({
      code: 200,
      message: 'success',
      data: mockEmployeeDetail
    })
    
    vi.mocked(employeeApi.logChanges).mockResolvedValue({
      code: 200,
      message: 'success',
      data: null
    })
  })
  
  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })
  
  it('应该正确渲染组件', async () => {
    wrapper = mount(EmployeeEdit, {
      global: {
        plugins: [pinia],
        stubs: {
          ElPageHeader: true,
          ElAlert: true,
          ElCard: true,
          ElSteps: true,
          ElStep: true,
          ElButton: true,
          ElTag: true,
          ElIcon: true,
          ElDialog: true,
          ElTable: true,
          ElTableColumn: true,
          Document: true,
          Clock: true,
          BasicInfoForm: true,
          EducationForm: true,
          WorkForm: true,
          FamilyForm: true,
          OtherInfoForm: true,
          ConfirmEditSubmit: true,
          ChangeHistoryDialog: true
        }
      }
    })
    
    await wrapper.vm.$nextTick()
    
    expect(wrapper.find('.employee-edit').exists()).toBe(true)
    expect(wrapper.find('.steps-card').exists()).toBe(true)
    expect(wrapper.find('.form-card').exists()).toBe(true)
  })
  
  it('应该在挂载时加载员工数据', async () => {
    wrapper = mount(EmployeeEdit, {
      global: {
        plugins: [pinia],
        stubs: {
          ElPageHeader: true,
          ElAlert: true,
          ElCard: true,
          ElSteps: true,
          ElStep: true,
          ElButton: true,
          ElTag: true,
          ElIcon: true,
          ElDialog: true,
          ElTable: true,
          ElTableColumn: true,
          Document: true,
          Clock: true,
          BasicInfoForm: true,
          EducationForm: true,
          WorkForm: true,
          FamilyForm: true,
          OtherInfoForm: true,
          ConfirmEditSubmit: true,
          ChangeHistoryDialog: true
        }
      }
    })
    
    await wrapper.vm.$nextTick()
    
    expect(employeeApi.getDetail).toHaveBeenCalledWith('123')
    expect(wrapper.vm.originalData).toEqual(mockEmployeeDetail)
    expect(wrapper.vm.formData.fullName).toBe('张三')
  })
  
  it('应该检测字段变更', async () => {
    wrapper = mount(EmployeeEdit, {
      global: {
        plugins: [pinia],
        stubs: {
          ElPageHeader: true,
          ElAlert: true,
          ElCard: true,
          ElSteps: true,
          ElStep: true,
          ElButton: true,
          ElTag: true,
          ElIcon: true,
          ElDialog: true,
          ElTable: true,
          ElTableColumn: true,
          Document: true,
          Clock: true,
          BasicInfoForm: true,
          EducationForm: true,
          WorkForm: true,
          FamilyForm: true,
          OtherInfoForm: true,
          ConfirmEditSubmit: true,
          ChangeHistoryDialog: true
        }
      }
    })
    
    await wrapper.vm.$nextTick()
    
    // 修改字段
    wrapper.vm.formData.phoneNumber = '13900139000',
  await wrapper.vm.$nextTick()
    
    expect(wrapper.vm.hasChanges).toBe(true)
    expect(wrapper.vm.changedFieldsCount).toBe(1)
    expect(wrapper.vm.changedFields[0]).toMatchObject({
      fieldName: 'phoneNumber',
      fieldLabel: '联系电话',
      oldValue: '13800138000',
      newValue: '13900139000'
    })
  })
  
  it('应该保存草稿', async () => {
    wrapper = mount(EmployeeEdit, {
      global: {
        plugins: [pinia],
        stubs: {
          ElPageHeader: true,
          ElAlert: true,
          ElCard: true,
          ElSteps: true,
          ElStep: true,
          ElButton: true,
          ElTag: true,
          ElIcon: true,
          ElDialog: true,
          ElTable: true,
          ElTableColumn: true,
          Document: true,
          Clock: true,
          BasicInfoForm: true,
          EducationForm: true,
          WorkForm: true,
          FamilyForm: true,
          OtherInfoForm: true,
          ConfirmEditSubmit: true,
          ChangeHistoryDialog: true
        }
      }
    })
    
    await wrapper.vm.$nextTick()
    
    await wrapper.vm.handleSaveDraft()
    
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'employee_edit_draft_123',
      expect.any(String)
    )
    expect(ElMessage.success).toHaveBeenCalledWith('草稿保存成功')
  })
  
  it('应该提交变更', async () => {
    wrapper = mount(EmployeeEdit, {
      global: {
        plugins: [pinia],
        stubs: {
          ElPageHeader: true,
          ElAlert: true,
          ElCard: true,
          ElSteps: true,
          ElStep: true,
          ElButton: true,
          ElTag: true,
          ElIcon: true,
          ElDialog: true,
          ElTable: true,
          ElTableColumn: true,
          Document: true,
          Clock: true,
          BasicInfoForm: true,
          EducationForm: true,
          WorkForm: true,
          FamilyForm: true,
          OtherInfoForm: true,
          ConfirmEditSubmit: {
            template: '<div></div>',
            methods: {
              validate: vi.fn().mockResolvedValue(true)
            },
            computed: {
              changeReason: () => '信息更新'
            }
          },
          ChangeHistoryDialog: true
        }
      }
    })
    
    await wrapper.vm.$nextTick()
    
    // 设置到最后一步
    wrapper.vm.activeStep = 5
    
    // 修改数据
    wrapper.vm.formData.phoneNumber = '13900139000',
  await wrapper.vm.$nextTick()
    
    await wrapper.vm.handleSubmit()
    
    expect(employeeApi.update).toHaveBeenCalledWith('123', wrapper.vm.formData)
    expect(employeeApi.logChanges).toHaveBeenCalled()
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('employee_edit_draft_123')
    expect(ElMessage.success).toHaveBeenCalledWith('员工信息更新成功')
    expect(mockPush).toHaveBeenCalledWith('/employee/detail/123')
  })
  
  it('应该处理无变更的情况', async () => {
    wrapper = mount(EmployeeEdit, {
      global: {
        plugins: [pinia],
        stubs: {
          ElPageHeader: true,
          ElAlert: true,
          ElCard: true,
          ElSteps: true,
          ElStep: true,
          ElButton: true,
          ElTag: true,
          ElIcon: true,
          ElDialog: true,
          ElTable: true,
          ElTableColumn: true,
          Document: true,
          Clock: true,
          BasicInfoForm: true,
          EducationForm: true,
          WorkForm: true,
          FamilyForm: true,
          OtherInfoForm: true,
          ConfirmEditSubmit: {
            template: '<div></div>',
            methods: {
              validate: vi.fn().mockResolvedValue(true)
            }
          },
          ChangeHistoryDialog: true
        }
      }
    })
    
    await wrapper.vm.$nextTick()
    
    // 设置到最后一步
    wrapper.vm.activeStep = 5
    
    // 不修改任何数据
    await wrapper.vm.handleSubmit()
    
    expect(ElMessage.warning).toHaveBeenCalledWith('没有检测到任何变更')
    expect(employeeApi.update).not.toHaveBeenCalled()
  })
  
  it('应该处理返回操作', async () => {
    wrapper = mount(EmployeeEdit, {
      global: {
        plugins: [pinia],
        stubs: {
          ElPageHeader: true,
          ElAlert: true,
          ElCard: true,
          ElSteps: true,
          ElStep: true,
          ElButton: true,
          ElTag: true,
          ElIcon: true,
          ElDialog: true,
          ElTable: true,
          ElTableColumn: true,
          Document: true,
          Clock: true,
          BasicInfoForm: true,
          EducationForm: true,
          WorkForm: true,
          FamilyForm: true,
          OtherInfoForm: true,
          ConfirmEditSubmit: true,
          ChangeHistoryDialog: true
        }
      }
    })
    
    await wrapper.vm.$nextTick()
    
    // 修改数据
    wrapper.vm.formData.phoneNumber = '13900139000',
  await wrapper.vm.$nextTick()
    
    await wrapper.vm.handleBack()
    
    expect(ElMessageBox.confirm).toHaveBeenCalled()
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('employee_edit_draft_123')
    expect(mockBack).toHaveBeenCalled()
  })
  
  it('应该打开变更历史对话框', async () => {
    wrapper = mount(EmployeeEdit, {
      global: {
        plugins: [pinia],
        stubs: {
          ElPageHeader: true,
          ElAlert: true,
          ElCard: true,
          ElSteps: true,
          ElStep: true,
          ElButton: true,
          ElTag: true,
          ElIcon: true,
          ElDialog: true,
          ElTable: true,
          ElTableColumn: true,
          Document: true,
          Clock: true,
          BasicInfoForm: true,
          EducationForm: true,
          WorkForm: true,
          FamilyForm: true,
          OtherInfoForm: true,
          ConfirmEditSubmit: true,
          ChangeHistoryDialog: true
        }
      }
    })
    
    await wrapper.vm.$nextTick()
    
    wrapper.vm.handleViewHistory()
    
    expect(wrapper.vm.showHistory).toBe(true)
  })
})