<template>
  <div class="talent-honors-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>人才荣誉管理</h2>
      <p>管理教职工的人才荣誉信息、等级认定和证书档案</p>
    </div>

    <!-- 搜索和操作区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-form">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索荣誉名称、员工姓名、颁发单位"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.level" placeholder="荣誉等级" clearable>
              <el-option
                v-for="item in talentHonorLevelOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-input
              v-model="searchForm.issuingUnit"
              placeholder="颁发单位"
              clearable
              />
          </el-col>
          <el-col :span="6">
            <el-date-picker
              v-model="acquireTimeRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleDateRangeChange"
             />
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-col>
        </el-row>
        
        <!-- 高级搜索 -->
        <el-collapse v-model="activeCollapse" class="advanced-search">
          <el-collapse-item title="高级搜索" name="advanced">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="员工ID">
                  <el-input v-model="searchForm.employeeId" placeholder="请输入员工ID"   />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="排名范围">
                  <el-row :gutter="10">
                    <el-col :span="12">
                      <el-input-number
                        v-model="searchForm.minRanking"
                        placeholder="最小排名"
                        :min="1"
                        controls-position="right"
                        />
                    </el-col>
                    <el-col :span="12">
                      <el-input-number
                        v-model="searchForm.maxRanking"
                        placeholder="最大排名"
                        :min="1"
                        controls-position="right"
                        />
                    </el-col>
                  </el-row>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="是否有证书">
                  <el-select v-model="searchForm.hasDocument" placeholder="请选择" clearable>
                    <el-option label="有证书" :value="true"  />
                    <el-option label="无证书" :value="false"  />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="排序方式">
                  <el-row :gutter="10">
                    <el-col :span="12">
                      <el-select v-model="searchForm.sortBy" placeholder="排序字段">
                        <el-option
                          v-for="item in talentHonorSortOptions"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                         />
                      </el-select>
                    </el-col>
                    <el-col :span="12">
                      <el-select v-model="searchForm.sortDirection" placeholder="排序方向">
                        <el-option label="升序" value="ASC"  />
                        <el-option label="降序" value="DESC"  />
                      </el-select>
                    </el-col>
                  </el-row>
                </el-form-item>
              </el-col>
            </el-row>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total">
              <el-icon><Trophy /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.total }}</div>
              <div class="stats-label">荣誉总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon national">
              <el-icon><Star /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.national }}</div>
              <div class="stats-label">国家级荣誉</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon provincial">
              <el-icon><Medal /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.provincial }}</div>
              <div class="stats-label">省部级荣誉</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon thisYear">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.thisYear }}</div>
              <div class="stats-label">本年新增</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 荣誉列表 -->
    <el-card class="table-card" shadow="never">
      <div class="table-header">
        <span class="table-title">荣誉列表</span>
        <div class="table-actions">
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增荣誉
          </el-button>
          <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
          <el-button @click="handleImport">
            <el-icon><Upload /></el-icon>
            导入
          </el-button>
          <el-button 
            type="danger" 
            :disabled="selectedRows.length === 0"
            @click="handleBatchDelete"
          >
            <el-icon><Delete /></el-icon>
            批量删除
          </el-button>
        </div>
      </div>

      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="employeeName" label="员工姓名" width="120"  />
        <el-table-column prop="name" label="荣誉名称" min-width="200" show-overflow-tooltip  />
        <el-table-column prop="levelName" label="荣誉等级" width="100">
          <template #default="scope">
            <el-tag :type="getLevelTagType(scope.row.level)" size="small">
              {{ scope.row.levelName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="issuingUnit" label="颁发单位" width="150" show-overflow-tooltip  />
        <el-table-column prop="acquireTime" label="获奖时间" width="120"  />
        <el-table-column prop="ranking" label="排名" width="80" align="center">
          <template #default="scope">
            <span v-if="scope.row.ranking">第{{ scope.row.ranking }}名</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="certificateNumber" label="证书编号" width="150" show-overflow-tooltip  />
        <el-table-column label="证书文档" width="100" align="center">
          <template #default="scope">
            <el-button
              v-if="scope.row.documentUrl"
              type="primary"
              link
              size="small"
              @click="handleViewDocument(scope.row.documentUrl)"
            >
              查看
            </el-button>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" link @click="handleView(scope.row)">
              查看
            </el-button>
            <el-button size="small" type="primary" link @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button size="small" type="danger" link @click="handleDelete(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 荣誉详情/编辑对话框 -->
    <TalentHonorDialog
      v-model:visible="dialogVisible"
      :talent-honor="currentTalentHonor"
      :mode="dialogMode"
      @success="handleDialogSuccess"
    />

    <!-- 导入对话框 -->
    <ImportDialog
      v-model:visible="importDialogVisible"
      title="导入人才荣誉"
      :upload-url="'/api/v1/talent-honors/import'"
      :template-url="'/api/v1/talent-honors/template'"
      @success="handleImportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  Download,
  Upload,
  Delete,
  Trophy,
  Star,
  Medal,
  Calendar
} from '@element-plus/icons-vue'
import { talentHonorsApi, talentHonorLevelOptions, talentHonorSortOptions } from '@/api/talentHonors'
import type { 
  TalentHonor, 
  TalentHonorQueryRequest,
  TalentHonorStatistics 
} from '@/types/talentHonors'
import TalentHonorDialog from './components/TalentHonorDialog.vue'
import HrImportDialog from '@/components/HrImportDialog.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref<TalentHonor[]>([])
const selectedRows = ref<TalentHonor[]>([])
const activeCollapse = ref<string[]>([])

// 搜索表单
const searchForm = reactive<TalentHonorQueryRequest>({
  page: 0,
  size: 20,
  keyword: '',
  level: undefined,
  issuingUnit: '',
  employeeId: '',
  acquireTimeStart: '',
  acquireTimeEnd: '',
  minRanking: undefined,
  maxRanking: undefined,
  hasDocument: undefined,
  sortBy: 'acquireTime',
  sortDirection: 'DESC' as unknown
})

// 日期范围
const acquireTimeRange = ref<[string, string] | null>(null)

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 统计信息
const stats = reactive({
  total: 0,
  national: 0,
  provincial: 0,
  thisYear: 0
})

// 对话框相关
const dialogVisible = ref(false)
const dialogMode = ref<'view' | 'add' | 'edit'>('view')
const currentTalentHonor = ref<TalentHonor | null>(null)

// 导入对话框
const importDialogVisible = ref(false)

// 获取荣誉列表
const fetchTalentHonors = async () => {
  try {
    loading.value = true
    const params = {
      ...searchForm,
      page: pagination.page - 1, // 后端从0开始
      size: pagination.size
    }
    
    const result = await talentHonorsApi.query(params)
    tableData.value = result.content as unknown // 临时修复类型不匹配
    pagination.total = result.totalElements
  } catch (__error) {
    ElMessage.error('获取荣誉列表失败')
  } finally {
    loading.value = false
  }
}

// 获取统计信息
const fetchStats = async () => {
  try {
    const statistics = await talentHonorsApi.getStatistics()
    stats.total = statistics.totalCount
    stats.national = statistics.levelDistribution['NATIONAL'] || 0
    stats.provincial = statistics.levelDistribution['PROVINCIAL'] || 0
    
    // 计算本年新增
    const currentYear = new Date().getFullYear()
    const thisYearData = statistics.yearlyTrend.find(item => item.year === currentYear)
    stats.thisYear = thisYearData?.count || 0
  } catch (__error) {
    }
}

// 日期范围变化处理
const handleDateRangeChange = (value: [string, string] | null) => {
  if (value) {
    searchForm.acquireTimeStart = value[0]
    searchForm.acquireTimeEnd = value[1]
  } else {
    searchForm.acquireTimeStart = ''
    searchForm.acquireTimeEnd = ''
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchTalentHonors()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    page: 0,
    size: 20,
    keyword: '',
    level: undefined,
    issuingUnit: '',
    employeeId: '',
    acquireTimeStart: '',
    acquireTimeEnd: '',
    minRanking: undefined,
    maxRanking: undefined,
    hasDocument: undefined,
    sortBy: 'acquireTime',
    sortDirection: 'DESC'
  })
  acquireTimeRange.value = null
  pagination.page = 1
  fetchTalentHonors()
}

// 新增荣誉
const handleAdd = () => {
  currentTalentHonor.value = null
  dialogMode.value = 'add'
  dialogVisible.value = true
}

// 查看荣誉
const handleView = (talentHonor: TalentHonor) => {
  currentTalentHonor.value = talentHonor
  dialogMode.value = 'view'
  dialogVisible.value = true
}

// 编辑荣誉
const handleEdit = (talentHonor: TalentHonor) => {
  currentTalentHonor.value = talentHonor
  dialogMode.value = 'edit'
  dialogVisible.value = true
}

// 删除荣誉
const handleDelete = async (talentHonor: TalentHonor) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除荣誉 "${talentHonor.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await talentHonorsApi.delete(talentHonor.id!)
    ElMessage.success('删除成功')
    fetchTalentHonors()
    fetchStats()
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 条荣誉记录吗？此操作不可恢复。`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const ids = selectedRows.value.map(row => row.id!).filter(id => id)
    await talentHonorsApi.batchDelete(ids)
    ElMessage.success('批量删除成功')
    fetchTalentHonors()
    fetchStats()
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

// 表格选择变化
const handleSelectionChange = (selection: TalentHonor[]) => {
  selectedRows.value = selection
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchTalentHonors()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchTalentHonors()
}

// 对话框成功回调
const handleDialogSuccess = () => {
  fetchTalentHonors()
  fetchStats()
}

// 导出
const handleExport = async () => {
  try {
    const blob = await talentHonorsApi.export(searchForm)
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `人才荣誉_${new Date().toISOString().slice(0, 10)}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('导出成功')
  } catch (__error) {
    ElMessage.error('导出失败')
  }
}

// 导入
const handleImport = () => {
  importDialogVisible.value = true
}

// 导入成功回调
const handleImportSuccess = () => {
  fetchTalentHonors()
  fetchStats()
}

// 查看证书文档
const handleViewDocument = (url: string) => {
  window.open(url, '_blank')
}

// 获取等级标签类型
const getLevelTagType = (level: string) => {
  switch (level) {
    case 'NATIONAL':
      return 'danger'
    case 'PROVINCIAL':
      return 'warning'
    case 'MUNICIPAL':
      return 'success'
    case 'SCHOOL':
      return 'info'
    case 'DEPARTMENT':
      return ''
    default:
      return ''
  }
}

// 初始化
onMounted(() => {
  fetchTalentHonors()
  fetchStats()
})
</script>

<style scoped>
.talent-honors-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  padding: 10px 0;
}

.advanced-search {
  margin-top: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.national {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.provincial {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.thisYear {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .talent-honors-management {
    padding: 10px;
  }

  .search-form .el-row {
    flex-direction: column;
  }

  .search-form .el-col {
    width: 100%;
    margin-bottom: 10px;
  }

  .stats-row .el-col {
    margin-bottom: 10px;
  }

  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .table-actions {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header) {
  background-color: #f8f9fa;
}

:deep(.el-table__header th) {
  background-color: #f8f9fa;
  color: #303133;
  font-weight: 600;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

/* 搜索表单样式 */
:deep(.el-form-item) {
  margin-bottom: 10px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

/* 折叠面板样式 */
:deep(.el-collapse-item__header) {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 0 16px;
  font-weight: 500;
}

:deep(.el-collapse-item__content) {
  padding: 20px 16px;
  background-color: #fafafa;
  border-radius: 0 0 4px 4px;
}

/* 标签样式 */
.el-tag {
  border-radius: 4px;
  font-weight: 500;
}

/* 按钮样式 */
.el-button {
  border-radius: 6px;
  font-weight: 500;
}

.el-button--primary {
  background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
  border: none;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #66b1ff 0%, #40a9ff 100%);
}
</style>
