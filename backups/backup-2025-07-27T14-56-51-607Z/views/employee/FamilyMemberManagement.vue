<template>
  <div class="family-member-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>家庭成员管理</h2>
      <p>管理教职工的家庭成员信息、关系类型和紧急联系人设置</p>
    </div>

    <!-- 搜索和操作区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-form">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索姓名、员工姓名、工作单位"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.relationship" placeholder="关系类型" clearable>
              <el-option
                v-for="item in relationshipOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.gender" placeholder="性别" clearable>
              <el-option
                v-for="item in genderOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-date-picker
              v-model="birthDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="出生开始日期"
              end-placeholder="出生结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleDateRangeChange"
             />
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-col>
        </el-row>
        
        <!-- 高级搜索 -->
        <el-collapse v-model="activeCollapse" class="advanced-search">
          <el-collapse-item title="高级搜索" name="advanced">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="员工ID">
                  <el-input v-model="searchForm.employeeId" placeholder="请输入员工ID"   />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="工作单位">
                  <el-input v-model="searchForm.workUnit" placeholder="请输入工作单位"   />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="职务">
                  <el-input v-model="searchForm.position" placeholder="请输入职务"   />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="年龄范围">
                  <el-row :gutter="10">
                    <el-col :span="12">
                      <el-input-number
                        v-model="searchForm.minAge"
                        placeholder="最小年龄"
                        :min="0"
                        :max="150"
                        controls-position="right"
                        />
                    </el-col>
                    <el-col :span="12">
                      <el-input-number
                        v-model="searchForm.maxAge"
                        placeholder="最大年龄"
                        :min="0"
                        :max="150"
                        controls-position="right"
                        />
                    </el-col>
                  </el-row>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="紧急联系人">
                  <el-select v-model="searchForm.isEmergencyContact" placeholder="请选择" clearable>
                    <el-option label="是" :value="true"  />
                    <el-option label="否" :value="false"  />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="被抚养人">
                  <el-select v-model="searchForm.isDependent" placeholder="请选择" clearable>
                    <el-option label="是" :value="true"  />
                    <el-option label="否" :value="false"  />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="政治面貌">
                  <el-select v-model="searchForm.politicalStatus" placeholder="请选择" clearable>
                    <el-option
                      v-for="item in politicalStatusOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                     />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="排序方式">
                  <el-row :gutter="10">
                    <el-col :span="12">
                      <el-select v-model="searchForm.sortBy" placeholder="排序字段">
                        <el-option
                          v-for="item in familyMemberSortOptions"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                         />
                      </el-select>
                    </el-col>
                    <el-col :span="12">
                      <el-select v-model="searchForm.sortDirection" placeholder="排序方向">
                        <el-option label="升序" value="ASC"  />
                        <el-option label="降序" value="DESC"  />
                      </el-select>
                    </el-col>
                  </el-row>
                </el-form-item>
              </el-col>
            </el-row>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.total }}</div>
              <div class="stats-label">家庭成员总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon emergency">
              <el-icon><Phone /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.emergencyContact }}</div>
              <div class="stats-label">紧急联系人</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon dependent">
              <el-icon><Star /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.dependent }}</div>
              <div class="stats-label">被抚养人</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon minor">
              <el-icon><Avatar /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.minor }}</div>
              <div class="stats-label">未成年人</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 家庭成员列表 -->
    <el-card class="table-card" shadow="never">
      <div class="table-header">
        <span class="table-title">家庭成员列表</span>
        <div class="table-actions">
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增家庭成员
          </el-button>
          <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
          <el-button @click="handleImport">
            <el-icon><Upload /></el-icon>
            导入
          </el-button>
          <el-button 
            type="danger" 
            :disabled="selectedRows.length === 0"
            @click="handleBatchDelete"
          >
            <el-icon><Delete /></el-icon>
            批量删除
          </el-button>
          <el-button 
            type="success" 
            :disabled="selectedRows.length === 0"
            @click="handleBatchSetEmergencyContact"
          >
            <el-icon><Phone /></el-icon>
            设为紧急联系人
          </el-button>
        </div>
      </div>

      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="employeeName" label="员工姓名" width="120"  />
        <el-table-column prop="name" label="家庭成员姓名" width="120"  />
        <el-table-column prop="relationshipName" label="关系" width="100">
          <template #default="scope">
            <el-tag :type="getRelationshipTagType(scope.row.relationship)" size="small">
              {{ scope.row.relationshipName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="genderName" label="性别" width="80" align="center">
          <template #default="scope">
            <el-icon v-if="scope.row.gender === 'MALE'" color="#409eff"><Male /></el-icon>
            <el-icon v-else-if="scope.row.gender === 'FEMALE'" color="#f56c6c"><Female /></el-icon>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="age" label="年龄" width="80" align="center">
          <template #default="scope">
            <span v-if="scope.row.age">{{ scope.row.age }}岁</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="联系电话" width="130"  />
        <el-table-column prop="workUnit" label="工作单位" min-width="150" show-overflow-tooltip  />
        <el-table-column prop="position" label="职务" width="120" show-overflow-tooltip  />
        <el-table-column label="紧急联系人" width="100" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.isEmergencyContact" type="danger" size="small">是</el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="被抚养人" width="100" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.isDependent" type="success" size="small">是</el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" link @click="handleView(scope.row)">
              查看
            </el-button>
            <el-button size="small" type="primary" link @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button 
              size="small" 
              :type="scope.row.isEmergencyContact ? 'warning' : 'success'" 
              link 
              @click="handleToggleEmergencyContact(scope.row)"
            >
              {{ scope.row.isEmergencyContact ? '取消紧急联系人' : '设为紧急联系人' }}
            </el-button>
            <el-button size="small" type="danger" link @click="handleDelete(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 家庭成员详情/编辑对话框 -->
    <FamilyMemberDialog
      v-model:visible="dialogVisible"
      :family-member="currentFamilyMember"
      :mode="dialogMode"
      @success="handleDialogSuccess"
    />

    <!-- 导入对话框 -->
    <ImportDialog
      v-model:visible="importDialogVisible"
      title="导入家庭成员"
      :upload-url="'/api/v1/family-members/import'"
      :template-url="'/api/v1/family-members/template'"
      @success="handleImportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  Download,
  Upload,
  Delete,
  UserFilled,
  Phone,
  Star,
  Avatar,
  Male,
  Female
} from '@element-plus/icons-vue'
import { 
  familyMemberApi, 
  relationshipOptions, 
  genderOptions, 
  politicalStatusOptions,
  familyMemberSortOptions 
} from '@/api/familyMember'
import type { 
  FamilyMember, 
  FamilyMemberQueryRequest,
  FamilyMemberStatistics 
} from '@/types/familyMember'
import FamilyMemberDialog from './components/FamilyMemberDialog.vue'
import HrImportDialog from '@/components/HrImportDialog.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref<FamilyMember[]>([])
const selectedRows = ref<FamilyMember[]>([])
const activeCollapse = ref<string[]>([])

// 搜索表单
const searchForm = reactive<FamilyMemberQueryRequest>({
  page: 0,
  size: 20,
  keyword: '',
  relationship: undefined,
  gender: undefined,
  workUnit: '',
  position: '',
  employeeId: '',
  birthDateStart: '',
  birthDateEnd: '',
  minAge: undefined,
  maxAge: undefined,
  isEmergencyContact: undefined,
  isDependent: undefined,
  politicalStatus: '',
  sortBy: 'name',
  sortDirection: 'ASC' as unknown
})

// 日期范围
const birthDateRange = ref<[string, string] | null>(null)

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 统计信息
const stats = reactive({
  total: 0,
  emergencyContact: 0,
  dependent: 0,
  minor: 0
})

// 对话框相关
const dialogVisible = ref(false)
const dialogMode = ref<'view' | 'add' | 'edit'>('view')
const currentFamilyMember = ref<FamilyMember | null>(null)

// 导入对话框
const importDialogVisible = ref(false)

// 获取家庭成员列表
const fetchFamilyMembers = async () => {
  try {
    loading.value = true
    const params = {
      ...searchForm,
      page: pagination.page - 1, // 后端从0开始
      size: pagination.size
    }
    
    const result = await familyMemberApi.query(params)
    tableData.value = result.content as unknown // 临时修复类型不匹配
    pagination.total = result.totalElements
  } catch (__error) {
    ElMessage.error('获取家庭成员列表失败')
  } finally {
    loading.value = false
  }
}

// 获取统计信息
const fetchStats = async () => {
  try {
    const statistics = await familyMemberApi.getStatistics()
    stats.total = statistics.totalCount
    stats.emergencyContact = statistics.emergencyContactCount
    stats.dependent = statistics.dependentCount
    stats.minor = statistics.minorCount
  } catch (__error) {
    }
}

// 日期范围变化处理
const handleDateRangeChange = (value: [string, string] | null) => {
  if (value) {
    searchForm.birthDateStart = value[0]
    searchForm.birthDateEnd = value[1]
  } else {
    searchForm.birthDateStart = ''
    searchForm.birthDateEnd = ''
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchFamilyMembers()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    page: 0,
    size: 20,
    keyword: '',
    relationship: undefined,
    gender: undefined,
    workUnit: '',
    position: '',
    employeeId: '',
    birthDateStart: '',
    birthDateEnd: '',
    minAge: undefined,
    maxAge: undefined,
    isEmergencyContact: undefined,
    isDependent: undefined,
    politicalStatus: '',
    sortBy: 'name',
    sortDirection: 'ASC'
  })
  birthDateRange.value = null
  pagination.page = 1
  fetchFamilyMembers()
}

// 新增家庭成员
const handleAdd = () => {
  currentFamilyMember.value = null
  dialogMode.value = 'add'
  dialogVisible.value = true
}

// 查看家庭成员
const handleView = (familyMember: FamilyMember) => {
  currentFamilyMember.value = familyMember
  dialogMode.value = 'view'
  dialogVisible.value = true
}

// 编辑家庭成员
const handleEdit = (familyMember: FamilyMember) => {
  currentFamilyMember.value = familyMember
  dialogMode.value = 'edit'
  dialogVisible.value = true
}

// 切换紧急联系人状态
const handleToggleEmergencyContact = async (familyMember: FamilyMember) => {
  try {
    const newStatus = !familyMember.isEmergencyContact
    await familyMemberApi.setEmergencyContact(familyMember.id!, newStatus)
    ElMessage.success(newStatus ? '已设为紧急联系人' : '已取消紧急联系人')
    fetchFamilyMembers()
    fetchStats()
  } catch (__error) {
    ElMessage.error('操作失败')
  }
}

// 批量设置紧急联系人
const handleBatchSetEmergencyContact = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要将选中的 ${selectedRows.value.length} 个家庭成员设为紧急联系人吗？`,
      '确认批量设置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    const ids = selectedRows.value.map(row => row.id!).filter(id => id)
    await familyMemberApi.batchSetEmergencyContact(ids, true)
    ElMessage.success('批量设置成功')
    fetchFamilyMembers()
    fetchStats()
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('批量设置失败')
    }
  }
}

// 删除家庭成员
const handleDelete = async (familyMember: FamilyMember) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除家庭成员 "${familyMember.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await familyMemberApi.delete(familyMember.id!)
    ElMessage.success('删除成功')
    fetchFamilyMembers()
    fetchStats()
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 条家庭成员记录吗？此操作不可恢复。`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const ids = selectedRows.value.map(row => row.id!).filter(id => id)
    await familyMemberApi.batchDelete(ids)
    ElMessage.success('批量删除成功')
    fetchFamilyMembers()
    fetchStats()
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

// 表格选择变化
const handleSelectionChange = (selection: FamilyMember[]) => {
  selectedRows.value = selection
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchFamilyMembers()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchFamilyMembers()
}

// 对话框成功回调
const handleDialogSuccess = () => {
  fetchFamilyMembers()
  fetchStats()
}

// 导出
const handleExport = async () => {
  try {
    const blob = await familyMemberApi.export(searchForm)
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `家庭成员_${new Date().toISOString().slice(0, 10)}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('导出成功')
  } catch (__error) {
    ElMessage.error('导出失败')
  }
}

// 导入
const handleImport = () => {
  importDialogVisible.value = true
}

// 导入成功回调
const handleImportSuccess = () => {
  fetchFamilyMembers()
  fetchStats()
}

// 获取关系类型标签类型
const getRelationshipTagType = (relationship: string) => {
  switch (relationship) {
    case 'SPOUSE':
      return 'danger'
    case 'CHILD':
      return 'success'
    case 'FATHER':
    case 'MOTHER':
      return 'warning'
    case 'BROTHER':
    case 'SISTER':
      return 'info'
    default:
      return ''
  }
}

// 初始化
onMounted(() => {
  fetchFamilyMembers()
  fetchStats()
})
</script>

<style scoped>
.family-member-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  padding: 10px 0;
}

.advanced-search {
  margin-top: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.emergency {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.dependent {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.minor {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .family-member-management {
    padding: 10px;
  }

  .search-form .el-row {
    flex-direction: column;
  }

  .search-form .el-col {
    width: 100%;
    margin-bottom: 10px;
  }

  .stats-row .el-col {
    margin-bottom: 10px;
  }

  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .table-actions {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header) {
  background-color: #f8f9fa;
}

:deep(.el-table__header th) {
  background-color: #f8f9fa;
  color: #303133;
  font-weight: 600;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

/* 搜索表单样式 */
:deep(.el-form-item) {
  margin-bottom: 10px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

/* 折叠面板样式 */
:deep(.el-collapse-item__header) {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 0 16px;
  font-weight: 500;
}

:deep(.el-collapse-item__content) {
  padding: 20px 16px;
  background-color: #fafafa;
  border-radius: 0 0 4px 4px;
}

/* 标签样式 */
.el-tag {
  border-radius: 4px;
  font-weight: 500;
}

/* 按钮样式 */
.el-button {
  border-radius: 6px;
  font-weight: 500;
}

.el-button--primary {
  background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
  border: none;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #66b1ff 0%, #40a9ff 100%);
}

.el-button--success {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  border: none;
}

.el-button--success:hover {
  background: linear-gradient(135deg, #85ce61 0%, #95d475 100%);
}
</style>
