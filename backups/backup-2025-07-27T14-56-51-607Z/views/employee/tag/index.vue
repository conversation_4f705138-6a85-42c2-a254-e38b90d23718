<template>
  <div class="hr-tag-management">
    <!-- 搜索栏 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="标签名称">
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入标签名称"
            clearable
            @keyup.enter="handleSearch"
            />
        </el-form-item>
        <el-form-item label="标签分类">
          <el-select
            v-model="searchForm.category"
            placeholder="请选择分类"
            clearable
          >
            <el-option
              v-for="(label, value) in categoryOptions"
              :key="value"
              :label="label"
              :value="value"
             />
          </el-select>
        </el-form-item>
        <el-form-item label="标签类型">
          <el-select
            v-model="searchForm.type"
            placeholder="请选择类型"
            clearable
          >
            <el-option
              v-for="(label, value) in typeOptions"
              :key="value"
              :label="label"
              :value="value"
             />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
          >
            <el-option label="启用" value="active"  />
            <el-option label="停用" value="inactive"  />
            <el-option label="待审核" value="pending"  />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作栏 -->
    <el-card class="action-card">
      <div class="action-bar">
        <div class="left-actions">
          <el-button type="primary" @click="handleCreate">
            <el-icon><Plus /></el-icon>
            新增标签
          </el-button>
          <el-button @click="handleBatchDelete" :disabled="!selectedRows.length">
            <el-icon><Delete /></el-icon>
            批量删除
          </el-button>
          <el-button @click="handleMerge" :disabled="selectedRows.length < 2">
            <el-icon><Connection /></el-icon>
            合并标签
          </el-button>
          <el-button @click="handleImport">
            <el-icon><Upload /></el-icon>
            导入
          </el-button>
          <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
        </div>
        <div class="right-actions">
          <el-button @click="handleTriggerCalculation">
            <el-icon><Cpu /></el-icon>
            触发系统计算
          </el-button>
          <el-button @click="showStatistics = true">
            <el-icon><DataLine /></el-icon>
            查看统计
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
        v-loading="tagLoading"
        :data="tags"
        @selection-change="handleSelectionChange"
        row-key="tagId"
        border
        stripe
      >
        <el-table-column type="selection" width="50"  />
        <el-table-column prop="tagName" label="标签名称" min-width="150">
          <template #default="{ row }">
            <div class="tag-name-cell">
              <el-tag
                :color="row.color || '#409EFF'"
                style="margin-right: 8px"
              >
                <el-icon v-if="row.icon" style="margin-right: 4px">
                  <component :is="row.icon" />
                </el-icon>
                {{ row.tagName }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="category" label="分类" width="120">
          <template #default="{ row }">
            {{ categoryOptions[row.category] }}
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.type)">
              {{ typeOptions[row.type] }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="weight" label="权重" width="80">
          <template #default="{ row }">
            <el-rate
              v-model="row.weight"
              :max="10"
              disabled
              show-score
              score-template="{value}"
             />
          </template>
        </el-table-column>
        <el-table-column prop="employeeCount" label="员工数" width="100">
          <template #default="{ row }">
            <el-link type="primary" @click="handleViewEmployees(row)">
              {{ row.employeeCount || 0 }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              active-value="active"
              inactive-value="inactive"
              @change="handleStatusChange(row)"
             />
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160"  />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button link type="primary" @click="handleViewStatistics(row)">
              统计
            </el-button>
            <el-button link type="danger" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="tagTotal"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="fetchTags"
        @current-change="fetchTags"
        style="margin-top: 20px"
       />
    </el-card>

    <!-- 标签编辑对话框 -->
    <el-dialog
      v-model="tagDialogVisible"
      :title="tagDialogTitle"
      width="600px"
      destroy-on-close
    >
      <el-form
        ref="tagFormRef"
        :model="tagForm"
        :rules="tagRules"
        label-width="100px"
      >
        <el-form-item label="标签名称" prop="tagName">
          <el-input v-model="tagForm.tagName" placeholder="请输入标签名称"   />
        </el-form-item>
        <el-form-item label="标签分类" prop="category">
          <el-select v-model="tagForm.category" placeholder="请选择分类">
            <el-option
              v-for="(label, value) in categoryOptions"
              :key="value"
              :label="label"
              :value="value"
             />
          </el-select>
        </el-form-item>
        <el-form-item label="标签类型" prop="type">
          <el-select v-model="tagForm.type" placeholder="请选择类型">
            <el-option
              v-for="(label, value) in typeOptions"
              :key="value"
              :label="label"
              :value="value"
             />
          </el-select>
        </el-form-item>
        <el-form-item label="权重" prop="weight">
          <el-slider
            v-model="tagForm.weight"
            :min="1"
            :max="10"
            show-input
           />
        </el-form-item>
        <el-form-item label="颜色">
          <el-color-picker v-model="tagForm.color"  />
        </el-form-item>
        <el-form-item label="图标">
          <el-input v-model="tagForm.icon" placeholder="Element Plus图标名称"   />
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="tagForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入标签描述"
            />
        </el-form-item>
        <el-form-item v-if="tagForm.type === 'computed'" label="计算规则">
          <el-input
            v-model="tagForm.ruleExpression"
            type="textarea"
            :rows="3"
            placeholder="请输入计算规则表达式"
            />
        </el-form-item>
        <el-form-item v-if="tagForm.type === 'system'" label="阈值">
          <el-input-number
            v-model="tagForm.threshold"
            :min="0"
            :max="100"
            placeholder="请输入阈值"
            />
        </el-form-item>
        <el-form-item label="关键词">
          <el-select
            v-model="tagForm.keywords"
            multiple
            filterable
            allow-create
            default-first-option
            placeholder="请输入关键词，按回车确认"
            style="width: 100%"
           />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="tagDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveTag">确定</el-button>
      </template>
    </el-dialog>

    <!-- 合并标签对话框 -->
    <el-dialog
      v-model="mergeDialogVisible"
      title="合并标签"
      width="500px"
      destroy-on-close
    >
      <el-form :model="mergeForm" label-width="100px">
        <el-form-item label="源标签">
          <div class="merge-source-tags">
            <el-tag
              v-for="tag in selectedRows"
              :key="tag.tagId"
              closable
              @close="removeFromMerge(tag)"
              style="margin-right: 8px; margin-bottom: 8px"
            >
              {{ tag.tagName }}
            </el-tag>
          </div>
        </el-form-item>
        <el-form-item label="目标标签" prop="targetTagId">
          <el-select
            v-model="mergeForm.targetTagId"
            placeholder="请选择目标标签"
            filterable
          >
            <el-option
              v-for="tag in tags"
              :key="tag.tagId"
              :label="tag.tagName"
              :value="tag.tagId"
              :disabled="selectedRows.some(t => t.tagId === tag.tagId)"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="mergeDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirmMerge">确定合并</el-button>
      </template>
    </el-dialog>

    <!-- 统计对话框 -->
    <el-dialog
      v-model="showStatistics"
      :title="currentTag ? `${currentTag.tagName} - 统计信息` : '标签统计'"
      width="900px"
      destroy-on-close
    >
      <TagStatistics :tag-id="currentTag?.tagId" />
    </el-dialog>

    <!-- 员工列表对话框 -->
    <el-dialog
      v-model="showEmployeeList"
      :title="`拥有标签「${currentTag?.tagName}」的员工`"
      width="1200px"
      destroy-on-close
    >
      <EmployeeTagList :tag-id="currentTag?.tagId" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
defineOptions({
  name: 'TagPage'
})

import { ref, reactive, computed, onMounted } from 'vue'
import {
  Search,
  Refresh,
  Plus,
  Delete,
  Connection,
  Upload,
  Download,
  Cpu,
  DataLine
} from '@element-plus/icons-vue'
import { useTagStore } from '@/stores/modules/tag'
import type { Tag, TagCategory, TagType, TagStatus } from '@/types/tag'
import TagStatistics from './components/TagStatistics.vue'
import EmployeeTagList from './components/EmployeeTagList.vue'

// 使用标签store
const tagStore = useTagStore()
const {tags, tagTotal: _tagTotal, tagLoading: _tagLoading, fetchTags: _fetchTags, createTag: _createTag, updateTag: _updateTag, deleteTag: _deleteTag, mergeTags: _mergeTags, triggerSystemTagCalculation: _triggerSystemTagCalculation} =  tagStore

// 搜索表单
const searchForm 
}

.search-card,
.action-card,
.table-card {
  margin-bottom: 20px;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left-actions,
.right-actions {
  display: flex;
  gap: 10px;
}

.tag-name-cell {
  display: flex;
  align-items: center;
}

.merge-source-tags {
  display: flex;
  flex-wrap: wrap;
}
</style>