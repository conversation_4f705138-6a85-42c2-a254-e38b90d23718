<template>
  <div class="tag-statistics" v-loading="loading">
    <!-- 概览卡片 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-statistic title="使用员工数" :value="statistics.employeeCount || 0"  />
      </el-col>
      <el-col :span="6">
        <el-statistic
          title="使用率"
          :value="statistics.percentage || 0"
          suffix="%"
         />
      </el-col>
      <el-col :span="6">
        <el-statistic
          title="平均得分"
          :value="statistics.avgScore || 0"
          :precision="1"
         />
      </el-col>
      <el-col :span="6">
        <el-statistic title="趋势" :value="trendText">
          <template #suffix>
            <el-icon
              :color="trendColor"
              :size="20"
              style="vertical-align: middle"
            >
              <component :is="trendIcon" />
            </el-icon>
          </template>
        </el-statistic>
      </el-col>
    </el-row>

    <!-- 分布图表 -->
    <el-divider   />
    <el-row :gutter="20">
      <el-col :span="12">
        <h4>部门分布</h4>
        <div ref="departmentChartRef" style="height: 300px"></div>
      </el-col>
      <el-col :span="12">
        <h4>岗位分布</h4>
        <div ref="positionChartRef" style="height: 300px"></div>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px">
      <el-col :span="12">
        <h4>年龄段分布</h4>
        <div ref="ageChartRef" style="height: 300px"></div>
      </el-col>
      <el-col :span="12">
        <h4>得分分布</h4>
        <div ref="scoreChartRef" style="height: 300px"></div>
      </el-col>
    </el-row>

    <!-- 详细数据表格 -->
    <el-divider   />
    <h4>部门详细统计</h4>
    <el-table
      :data="departmentTableData"
      border
      stripe
      style="margin-top: 10px"
    >
      <el-table-column prop="name" label="部门名称"  />
      <el-table-column prop="count" label="员工数" width="100"  />
      <el-table-column prop="percentage" label="占比" width="100">
        <template #default="{ row }">
          {{ row.percentage }}%
        </template>
      </el-table-column>
      <el-table-column prop="avgScore" label="平均得分" width="100">
        <template #default="{ row }">
          {{ row.avgScore.toFixed(1) }}
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, computed, onMounted, nextTick } from 'vue'
import { useTagStore } from '@/stores/modules/tag'
import type { TagStatistics } from '@/types/tag'

const props = defineProps<{
  tagId?: string
}>()

// 使用store
const tagStore = useTagStore()
const {fetchTagStatistics: _fetchTagStatistics} =  tagStore

// 状态
const loading 
}

h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-weight: 500;
}

:deep(.el-statistic__head) {
  font-size: 14px;
  color: #909399;
}

:deep(.el-statistic__content) {
  font-size: 24px;
  color: #303133;
}
</style>