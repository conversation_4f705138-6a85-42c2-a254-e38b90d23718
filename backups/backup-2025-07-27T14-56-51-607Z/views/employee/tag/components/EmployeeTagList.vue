<template>
  <div class="employee-tag-list">
    <!-- 搜索栏 -->
    <el-form :model="searchForm" inline>
      <el-form-item label="员工姓名">
        <el-input
          v-model="searchForm.keyword"
          placeholder="请输入员工姓名或工号"
          clearable
          @keyup.enter="handleSearch"
          />
      </el-form-item>
      <el-form-item label="部门">
        <el-select
          v-model="searchForm.departmentId"
          placeholder="请选择部门"
          clearable
          filterable
        >
          <el-option
            v-for="dept in departments"
            :key="dept.id"
            :label="dept.name"
            :value="dept.id"
           />
        </el-select>
      </el-form-item>
      <el-form-item label="得分范围">
        <el-slider
          v-model="scoreRange"
          range
          :max="100"
          style="width: 200px"
         />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">
          <el-icon><Search /></el-icon>
          查询
        </el-button>
        <el-button @click="handleReset">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      border
      stripe
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="50"  />
      <el-table-column prop="employeeNumber" label="工号" width="100"  />
      <el-table-column prop="employeeName" label="姓名" width="100"  />
      <el-table-column prop="departmentName" label="部门" width="150"  />
      <el-table-column prop="positionName" label="岗位" width="150"  />
      <el-table-column prop="score" label="标签得分" width="120">
        <template #default="{ row }">
          <el-progress
            :percentage="row.score"
            :color="getScoreColor(row.score)"
            :stroke-width="8"
           />
        </template>
      </el-table-column>
      <el-table-column prop="attachTime" label="添加时间" width="160"  />
      <el-table-column prop="attachedByName" label="添加人" width="100"  />
      <el-table-column prop="verified" label="是否验证" width="100">
        <template #default="{ row }">
          <el-tag :type="row.verified ? 'success' : 'info'">
            {{ row.verified ? '已验证' : '未验证' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button link type="primary" @click="handleEdit(row)">
            编辑
          </el-button>
          <el-button link type="primary" @click="handleViewEmployee(row)">
            查看员工
          </el-button>
          <el-button link type="danger" @click="handleRemove(row)">
            移除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="fetchData"
      @current-change="fetchData"
      style="margin-top: 20px"
     />

    <!-- 编辑对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑员工标签"
      width="500px"
      destroy-on-close
    >
      <el-form :model="editForm" label-width="100px">
        <el-form-item label="员工">
          <el-input :value="`${editForm.employeeName} (${editForm.employeeNumber})`" disabled   />
        </el-form-item>
        <el-form-item label="标签">
          <el-input :value="currentTagName" disabled   />
        </el-form-item>
        <el-form-item label="得分">
          <el-slider v-model="editForm.score" :max="100" show-input  />
        </el-form-item>
        <el-form-item label="添加原因">
          <el-input
            v-model="editForm.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入添加或更新原因"
            />
        </el-form-item>
        <el-form-item label="有效期">
          <el-date-picker
            v-model="editForm.validUntil"
            type="date"
            placeholder="选择有效期"
            value-format="YYYY-MM-DD"
           />
        </el-form-item>
        <el-form-item label="是否验证">
          <el-switch v-model="editForm.verified"  />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveEdit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 批量操作栏 -->
    <div v-if="selectedRows.length > 0" class="batch-action-bar">
      <el-alert
        :title="`已选择 ${selectedRows.length} 个员工`"
        type="info"
        show-icon
        :closable="false"
      >
        <template #default>
          <el-button size="small" @click="handleBatchVerify">
            批量验证
          </el-button>
          <el-button size="small" type="danger" @click="handleBatchRemove">
            批量移除
          </el-button>
        </template>
      </el-alert>
    </div>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { Search, Refresh } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { useTagStore } from '@/stores/modules/tag'
import { useEmployeeStore } from '@/stores/modules/employee'
import type { EmployeeTag } from '@/types/tag'

const props = defineProps<{
  tagId?: string
}>()

const router = useRouter()

// 使用store
const tagStore = useTagStore()
const {employeeTags: _employeeTags, employeeTagTotal: _employeeTagTotal, employeeTagLoading: _employeeTagLoading, fetchEmployeeTags: _fetchEmployeeTags, updateEmployeeTag: _updateEmployeeTag, removeEmployeeTag: _removeEmployeeTag, batchOperateEmployeeTags: _batchOperateEmployeeTags} =  tagStore

// 状态
const loading 
}

.batch-action-bar {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999;
}

:deep(.el-alert__content) {
  display: flex;
  align-items: center;
  gap: 10px;
}
</style>