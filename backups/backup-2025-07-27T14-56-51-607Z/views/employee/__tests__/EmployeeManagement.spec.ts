 
 

/**
 * EmployeeManagement 组件测试
 * @description 自动生成的组件测试文件
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { shallowMount } from '@vue/test-utils'
import EmployeeManagement from '../EmployeeManagement.vue'
describe('EmployeeManagement', () => {
  let wrapper

  beforeEach(() => {
    wrapper = null
  })

  afterEach(() => {
    wrapper?.unmount()
  })

  it('应该正确渲染', async () => {
    const wrapper = shallowMount(EmployeeManagement, {
      global: {
        stubs: {
          'el-card': true,
          'el-button': true,
          'el-row': true,
          'el-col': true,
          'el-statistic': true,
          'el-icon': true,
          'router-link': true
        }
      }
    })
    expect(wrapper.exists()).toBe(true)
  })

  it('应该显示正确的页面标题', async () => {
    const wrapper = shallowMount(EmployeeManagement, {
      global: {
        stubs: {
          'el-card': true,
          'el-button': true,
          'el-row': true,
          'el-col': true,
          'el-statistic': true,
          'el-icon': true,
          'router-link': true
        }
      }
    })
    expect(wrapper.text()).toContain('员工管理')
  })

  it('应该包含功能卡片', () => {
    const wrapper = shallowMount(EmployeeManagement, {
      global: {
        stubs: {
          'el-card': true,
          'el-button': true,
          'el-row': true,
          'el-col': true,
          'el-statistic': true,
          'el-icon': true,
          'router-link': true
        }
      }
    })
    // 验证页面结构
    expect(wrapper.find('.employee-management').exists()).toBe(true)
  })
})
