/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * 员工列表页面单元测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
  import { mount } from '@vue/test-utils'
  import { createPinia } from 'pinia'
  import { ElMessage } from 'element-plus'
  import EmployeeList from '../index.vue';
  import { useEmployeeStore } from '@/stores/modules/employee'
  import { useOrganizationStore } from '@/stores/modules/organization'
  import employeeApi from '@/api/modules/employee';
  import type { Employee } from '@/types/employee'

// Mock API
vi.mock('@/api/modules/employee', () => ({
  default: {
    getList: vi.fn(),
    delete: vi.fn(),
    batchDelete: vi.fn(),
    export: vi.fn(),
    import: vi.fn(),
    downloadTemplate: vi.fn(),
    batchUpdateStatus: vi.fn()
  }
}))

// Mock 路由
const mockPush = vi.fn()
vi.mock('vue-router', () => ({
  useRouter: () => ({
    push: mockPush
  })
}))

// Mock ElMessage
vi.mock('element-plus', async () => {
  const actual = await vi.importActual('element-plus')
  return {
    ...actual,
    ElMessage: {
      success: vi.fn(),
      error: vi.fn(),
      warning: vi.fn()
    },
    ElMessageBox: {
      confirm: vi.fn().mockResolvedValue(true)
    }
  }
})

describe('EmployeeList', () => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let wrapper: any
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let pinia: any
  
  const mockEmployeeList: Employee[] = [
    {
      employeeId: '1',
      employeeNumber: '2024001',
      fullName: '张三',
      gender: 'male',
      dateOfBirth: '1990-01-01',
      ethnicity: '汉族',
      politicalStatus: '党员',
      nativePlace: '浙江杭州',
      placeOfBirth: '浙江杭州',
      idType: 'idCard',
      idNumber: '330100199001010001',
      workStartDate: '2020-01-01',
      hireDate: '2020-01-01',
      personnelStatus: 'active',
      institutionId: 'dept1',
      institutionName: '信息技术部',
      positionId: 'pos1',
      positionName: '前端工程师',
      phoneNumber: '13800138000',
      email: '<EMAIL>',
      educationDegree: '本科',
      professionalTitle: '中级',
      infoCompletenessPercentage: 85
    },
    {
      employeeId: '2',
      employeeNumber: '2024002',
      fullName: '李四',
      gender: 'female',
      dateOfBirth: '1992-05-15',
      ethnicity: '汉族',
      politicalStatus: '群众',
      nativePlace: '浙江宁波',
      placeOfBirth: '浙江宁波',
      idType: 'idCard',
      idNumber: '330200199205150002',
      workStartDate: '2021-03-01',
      hireDate: '2021-03-01',
      personnelStatus: 'active',
      institutionId: 'dept2',
      institutionName: '人力资源部',
      positionId: 'pos2',
      positionName: 'HR专员',
      phoneNumber: '13900139000',
      email: '<EMAIL>',
      educationDegree: '硕士',
      professionalTitle: '初级',
      infoCompletenessPercentage: 92
    }
  ]
  
  beforeEach(() => {
    // 创建 Pinia 实例
    pinia = createPinia()
    
    // Mock API 响应
    vi.mocked(employeeApi.getList).mockResolvedValue({
      code: 200,
      message: 'success',
      data: {
        list: mockEmployeeList,
        total: 2,
        page: 1,
        pageSize: 20
      }
    })
    
    // 重置 mock
    mockPush.mockClear()
    vi.mocked(ElMessage.success).mockClear()
    vi.mocked(ElMessage.error).mockClear()
  })
  
  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })
  
  it('应该正确渲染组件', async () => {
    wrapper = mount(EmployeeList, {
      global: {
        plugins: [pinia],
        stubs: {
          HrAdvancedSearch: true,
          HrDataTable: true,
          ElDialog: true,
          ElIcon: true,
          ArrowDown: true,
          Plus: true,
          Upload: true,
          Download: true,
          UploadFilled: true
        }
      }
    })
    
    await wrapper.vm.$nextTick()
    
    expect(wrapper.find('.employee-list').exists()).toBe(true)
    expect(wrapper.find('.action-card').exists()).toBe(true)
  })
  
  it('应该在挂载时获取员工列表', async () => {
    wrapper = mount(EmployeeList, {
      global: {
        plugins: [pinia],
        stubs: {
          HrAdvancedSearch: true,
          HrDataTable: true,
          ElDialog: true,
          ElIcon: true
        }
      }
    })
    
    await wrapper.vm.$nextTick()
    
    expect(employeeApi.getList).toHaveBeenCalled()
    expect(wrapper.vm.tableData).toHaveLength(2)
    expect(wrapper.vm.total).toBe(2)
  })
  
  it('应该处理搜索操作', async () => {
    wrapper = mount(EmployeeList, {
      global: {
        plugins: [pinia],
        stubs: {
          HrAdvancedSearch: true,
          HrDataTable: true,
          ElDialog: true,
          ElIcon: true
        }
      }
    })
    
    await wrapper.vm.handleSearch()
    
    expect(wrapper.vm.searchForm.page).toBe(1)
    expect(employeeApi.getList).toHaveBeenCalledWith(
      expect.objectContaining({
        page: 1
      })
    )
  })
  
  it('应该处理新增员工操作', async () => {
    wrapper = mount(EmployeeList, {
      global: {
        plugins: [pinia],
        stubs: {
          HrAdvancedSearch: true,
          HrDataTable: true,
          ElDialog: true,
          ElIcon: true
        }
      }
    })
    
    await wrapper.vm.handleCreate()
    
    expect(mockPush).toHaveBeenCalledWith('/employee/create')
  })
  
  it('应该处理查看员工操作', async () => {
    wrapper = mount(EmployeeList, {
      global: {
        plugins: [pinia],
        stubs: {
          HrAdvancedSearch: true,
          HrDataTable: true,
          ElDialog: true,
          ElIcon: true
        }
      }
    })
    
    await wrapper.vm.handleView(mockEmployeeList[0])
    
    expect(mockPush).toHaveBeenCalledWith('/employee/detail/1')
  })
  
  it('应该处理编辑员工操作', async () => {
    wrapper = mount(EmployeeList, {
      global: {
        plugins: [pinia],
        stubs: {
          HrAdvancedSearch: true,
          HrDataTable: true,
          ElDialog: true,
          ElIcon: true
        }
      }
    })
    
    await wrapper.vm.handleEdit(mockEmployeeList[0])
    
    expect(mockPush).toHaveBeenCalledWith('/employee/edit/1')
  })
  
  it('应该处理删除员工操作', async () => {vi.mocked(employeeApi.delete).mockResolvedValue({
      code: 200,
      message: 'success',
      data: null
    expect(true).toBe(true); // TODO: 添加实际断言})
    
    wrapper = mount(EmployeeList, {
      global: {
        plugins: [pinia],
        stubs: {
          HrAdvancedSearch: true,
          HrDataTable: true,
          ElDialog: true,
          ElIcon: true
        }
      }
    })
    
    await wrapper.vm.handleDelete(mockEmployeeList[0])
    
    expect(employeeApi.delete).toHaveBeenCalledWith('1')
    expect(ElMessage.success).toHaveBeenCalledWith('删除成功')
  })
  
  it('应该处理导出操作', async () => {
    wrapper = mount(EmployeeList, {
      global: {
        plugins: [pinia],
        stubs: {
          HrAdvancedSearch: true,
          HrDataTable: true,
          ElDialog: true,
          ElIcon: true
        }
      }
    })
    
    await wrapper.vm.handleExport({
      format: 'xlsx',
      fields: ['fullName', 'employeeNumber'],
      filename: 'test'
    })
    
    expect(employeeApi.export).toHaveBeenCalledWith({
      format: 'xlsx',
      fields: ['fullName', 'employeeNumber'],
      filename: 'test',
      filters: wrapper.vm.searchForm
    })
    expect(ElMessage.success).toHaveBeenCalledWith('导出成功')
  })
  
  it('应该处理批量导入', async () => {const mockFile = new File(['test'], 'test.xlsx', { type: 'application/vnd.ms-excel',
  expect(true).toBe(true); // TODO: 添加实际断言})
    
    vi.mocked(employeeApi.import).mockResolvedValue({
      code: 200,
      message: 'success',
      data: {
        total: 10,
        success: 10,
        failed: 0,
        errors: []
      }
    })
    
    wrapper = mount(EmployeeList, {
      global: {
        plugins: [pinia],
        stubs: {
          HrAdvancedSearch: true,
          HrDataTable: true,
          ElDialog: true,
          ElIcon: true
        }
      }
    })
    
    wrapper.vm.importForm.file = mockFile
    await wrapper.vm.handleImportConfirm()
    
    expect(employeeApi.import).toHaveBeenCalledWith(mockFile, 'add')
    expect(ElMessage.success).toHaveBeenCalledWith('导入成功，共导入 10 条数据')
  })
  
  it('应该处理下载模板', async () => {
    wrapper = mount(EmployeeList, {
      global: {
        plugins: [pinia],
        stubs: {
          HrAdvancedSearch: true,
          HrDataTable: true,
          ElDialog: true,
          ElIcon: true
        }
      }
    })
    
    await wrapper.vm.handleDownloadTemplate()
    
    expect(employeeApi.downloadTemplate).toHaveBeenCalled()
    expect(ElMessage.success).toHaveBeenCalledWith('模板下载成功')
  })
  
  it('应该处理批量操作', async () => {
    vi.mocked(employeeApi.batchDelete).mockResolvedValue({
      code: 200,
      message: 'success',
      data: {
        success: 2,
        failed: 0
      }
    })
    
    wrapper = mount(EmployeeList, {
      global: {
        plugins: [pinia],
        stubs: {
          HrAdvancedSearch: true,
          HrDataTable: true,
          ElDialog: true,
          ElIcon: true
        }
      }
    })
    
    wrapper.vm.selectedRows = mockEmployeeList
    wrapper.vm.batchForm.operation = 'delete',
  await wrapper.vm.handleBatchConfirm()
    
    expect(employeeApi.batchDelete).toHaveBeenCalledWith(['1', '2'])
    expect(ElMessage.success).toHaveBeenCalledWith('批量删除成功')
  })
  
  it('应该正确显示人员状态标签', () => {
    wrapper = mount(EmployeeList, {
      global: {
        plugins: [pinia],
        stubs: {
          HrAdvancedSearch: true,
          HrDataTable: true,
          ElDialog: true,
          ElIcon: true
        }
      }
    })
    
    expect(wrapper.vm.getStatusText('active')).toBe('在职')
    expect(wrapper.vm.getStatusText('resigned')).toBe('离职')
    expect(wrapper.vm.getStatusText('retired')).toBe('退休')
    expect(wrapper.vm.getStatusText('rehired')).toBe('返聘')
    
    expect(wrapper.vm.getStatusTagType('active')).toBe('success')
    expect(wrapper.vm.getStatusTagType('resigned')).toBe('info')
    expect(wrapper.vm.getStatusTagType('retired')).toBe('warning')
  })
  
  it('应该正确计算进度条颜色', () => {
    wrapper = mount(EmployeeList, {
      global: {
        plugins: [pinia],
        stubs: {
          HrAdvancedSearch: true,
          HrDataTable: true,
          ElDialog: true,
          ElIcon: true
        }
      }
    })
    
    expect(wrapper.vm.getProgressColor(50)).toBe('#f56c6c')
    expect(wrapper.vm.getProgressColor(70)).toBe('#e6a23c')
    expect(wrapper.vm.getProgressColor(90)).toBe('#67c23a')
  })
})