<template>
  <div class="employee-list-optimized">
    <!-- 搜索区域 -->
    <HrAdvancedSearch
      v-model="searchForm" <!-- eslint-disable-line vue/no-mutating-props -->
      :fields="searchFields"
      :show-export="true"
      :show-preset="true"
      @search="handleSearch"
      @reset="handleReset"
      @export="handleExport"
    />

    <!-- 操作栏 -->
    <el-card class="action-card">
      <div class="action-bar">
        <div class="action-left">
          <el-button type="primary" icon="Plus" @click="handleCreate">
            新增员工
          </el-button>
          <el-button icon="Upload" @click="handleImport">
            批量导入
          </el-button>
          <el-button icon="Download" @click="handleDownloadTemplate">
            下载模板
          </el-button>
          <HrDataExport
            :fields="exportFields"
            :fetch-data="fetchExportData"
            :selection="selectedRows"
            :total-count="total"
            :templates="exportTemplates"
            @exported="handleExported"
            @save-template="handleSaveTemplate"
            @delete-template="handleDeleteTemplate"
          />
        </div>
        <div class="action-right">
          <el-badge :value="selectedRows.length" :hidden="selectedRows.length === 0">
            <el-button
              :disabled="selectedRows.length === 0"
              @click="handleBatchOperation"
            >
              批量操作
            </el-button>
          </el-badge>
        </div>
      </div>
    </el-card>

    <!-- 虚拟滚动表格 -->
    <hr-virtual-table
      ref="tableRef"
      :data="tableData"
      :columns="virtualTableColumns"
      :loading="loading"
      :row-height="60"
      height="calc(100vh - 320px)"
      :stripe="true"
      :border="true"
      :highlight-current-row="true"
      :show-header="true"
      :default-sort="{ prop: searchForm.sortField, order: searchForm.sortOrder === 'asc' ? 'asc' : 'desc' }"
      :row-key="getRowKey"
      @sort-change="handleSortChange"
      @row-click="handleRowClick"
      @row-contextmenu="handleRowContextMenu"
      @columns-change="handleColumnsChange"
      @selection-change="handleSelectionChange"
    >
      <!-- 选择列 -->
      <template #selection="{ row }">
        <el-checkbox
          v-model="row._selected"
          @change="(val) => handleCheckboxChange(row, val)"
        />
      </template>

      <!-- 姓名列插槽 -->
      <template #fullName="{ row }">
        <div class="employee-name">
          <el-avatar
            v-if="row.photoUrl"
            v-lazy="row.photoUrl"
            :size="32"
            class="employee-avatar"
            />
          <el-avatar v-else :size="32" class="employee-avatar">
            {{ row.fullName.charAt(0) }}
          </el-avatar>
          <span class="name-text">{{ row.fullName }}</span>
        </div>
      </template>

      <!-- 状态列插槽 -->
      <template #personnelStatus="{ row }">
        <el-tag
          :type="getStatusTagType(row.personnelStatus)"
          size="small"
        >
          {{ getStatusText(row.personnelStatus) }}
        </el-tag>
      </template>

      <!-- 信息完整度插槽 -->
      <template #infoCompletenessPercentage="{ row }">
        <el-progress
          :percentage="row.infoCompletenessPercentage"
          :color="getProgressColor(row.infoCompletenessPercentage)"
          :show-text="true"
          :stroke-width="6"
          style="width: 100px"
         />
      </template>

      <!-- 操作列插槽 -->
      <template #operations="{ row }">
        <el-button
          type="primary"
          size="small"
          link
          @click.stop="handleView(row)"
        >
          查看
        </el-button>
        <el-button
          type="primary"
          size="small"
          link
          @click.stop="handleEdit(row)"
        >
          编辑
        </el-button>
        <el-dropdown
          trigger="click"
          @command="cmd => handleCommand(cmd, row)"
          @click.stop
        >
          <el-button type="primary" size="small" link>
            更多
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="export">导出档案</el-dropdown-item>
              <el-dropdown-item command="print">打印档案</el-dropdown-item>
              <el-dropdown-item command="history">变更历史</el-dropdown-item>
              <el-dropdown-item
                command="delete"
                divided
                style="color: var(--el-color-danger)"
              >
                删除
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
    </VirtualTable>

    <!-- 分页 -->
    <div class="table-pagination">
      <el-pagination
        v-model:current-page="searchForm.page"
        v-model:page-size="searchForm.pageSize"
        :page-sizes="[20, 50, 100, 200]"
        :layout="'total, sizes, prev, pager, next, jumper'"
        :total="total"
        :background="true"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
       />
    </div>

    <!-- 批量导入对话框 -->
    <el-dialog
      v-model="importDialogVisible" <!-- eslint-disable-line vue/no-mutating-props -->
      title="批量导入员工"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form ref="importFormRef" :model="importForm" label-width="100px">
        <el-form-item label="导入模式" prop="mode">
          <el-radio-group v-model="importForm.mode">
            <el-radio value="add">新增模式（仅导入新数据）</el-radio>
            <el-radio value="replace">替换模式（更新已存在数据）</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="选择文件" prop="file" required>
          <el-upload
            ref="uploadRef"
            class="upload-demo"
            :auto-upload="false"
            :limit="1"
            :on-change="handleFileChange"
            :on-exceed="handleExceed"
            accept=".xlsx,.xls"
            drag
          >
            <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                只能上传 Excel 文件，建议使用系统提供的模板
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="importDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="importLoading"
          @click="handleImportConfirm"
        >
          开始导入
        </el-button>
      </template>
    </el-dialog>

    <!-- 批量操作对话框 -->
    <el-dialog
      v-model="batchDialogVisible" <!-- eslint-disable-line vue/no-mutating-props -->
      title="批量操作"
      width="500px"
    >
      <el-form ref="batchFormRef" :model="batchForm" label-width="100px">
        <el-form-item label="操作类型" prop="operation">
          <el-select v-model="batchForm.operation" placeholder="请选择操作类型">
            <el-option label="批量导出" value="export"  />
            <el-option label="批量更新状态" value="updateStatus"  />
            <el-option label="批量分配部门" value="assign"  />
            <el-option label="批量删除" value="delete"  />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="batchForm.operation === 'updateStatus'"
          label="目标状态"
          prop="status"
        >
          <el-select v-model="batchForm.status" placeholder="请选择目标状态">
            <el-option
              v-for="item in personnelStatusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
             />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="batchForm.operation === 'assign'"
          label="目标部门"
          prop="departmentId"
        >
          <el-tree-select
            v-model="batchForm.departmentId"
            :data="departmentTree"
            :props="{ label: 'name', value: 'id' }"
            placeholder="请选择目标部门"
            filterable
           />
        </el-form-item>
        <el-alert
          type="info"
          :closable="false"
          style="margin-top: 10px"
        >
          已选择 {{ selectedRows.length }} 条记录
        </el-alert>
      </el-form>
      <template #footer>
        <el-button @click="batchDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="batchLoading"
          @click="handleBatchConfirm"
        >
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Upload,
  Download,
  ArrowDown,
  UploadFilled
} from '@element-plus/icons-vue'
import { HrAdvancedSearch, HrDataExport } from '@/components'
import HrVirtualTable from '@/components/common/HrVirtualTable.vue'
import type { TableColumn } from '@/components/common/VirtualTable.vue'
import employeeApi from '@/api/employee'
import { useOrganizationStore } from '@/stores/modules/organization'
import { useUserStore } from '@/stores/modules/user'
import type {
  Employee,
  EmployeeSearchParams,
  PersonnelStatus
} from '@/types/employee'

// 路由
const router = useRouter()

// Store
const organizationStore = useOrganizationStore()
const userStore = useUserStore()

// Refs
const tableRef = ref<InstanceType<typeof VirtualTable>>()

// 状态
const loading = ref(false)
const importDialogVisible = ref(false)
const importLoading = ref(false)
const batchDialogVisible = ref(false)
const batchLoading = ref(false)

// 数据
const tableData = ref<Employee[]>([])
const total = ref(0)
const selectedRows = ref<Employee[]>([])

// 搜索表单
const searchForm = reactive<EmployeeSearchParams>({
  page: 1,
  pageSize: 20,
  sortField: 'createdAt',
  sortOrder: 'desc'
})

// 导入表单
const importForm = reactive({
  mode: 'add' as 'add' | 'replace',
  file: null as File | null
})

// 批量操作表单
const batchForm = reactive({
  operation: '',
  status: '',
  departmentId: ''
})

// 导出模板
const exportTemplates = ref<unknown[]>([
  {
    id: '1',
    name: 'HrHr基本信息导出',
    fields: ['employeeNumber', 'fullName', 'gender', 'dateOfBirth', 'phoneNumber', 'email', 'institutionName', 'positionName'],
    fieldCount: 8,
    createTime: '2024-01-01T00:00:00'
  },
  {
    id: '2',
    name: '完整信息导出',
    fields: ['employeeNumber', 'fullName', 'gender', 'dateOfBirth', 'idNumber', 'phoneNumber', 'email', 'institutionName', 'positionName', 'hireDate', 'personnelStatus', 'educationDegree', 'professionalTitle'],
    fieldCount: 13,
    createTime: '2024-01-01T00:00:00'
  }
])

// 导出字段配置
const exportFields = [
  // 基本信息
  { group: 'basic', groupLabel: '基本信息', value: 'employeeNumber', label: '工号', required: true },
  { group: 'basic', groupLabel: '基本信息', value: 'fullName', label: '姓名', required: true },
  { group: 'basic', groupLabel: '基本信息', value: 'gender', label: '性别' },
  { group: 'basic', groupLabel: '基本信息', value: 'dateOfBirth', label: '出生日期' },
  { group: 'basic', groupLabel: '基本信息', value: 'ethnicity', label: '民族' },
  { group: 'basic', groupLabel: '基本信息', value: 'politicalStatus', label: '政治面貌' },
  { group: 'basic', groupLabel: '基本信息', value: 'nativePlace', label: '籍贯' },
  { group: 'basic', groupLabel: '基本信息', value: 'idType', label: '证件类型' },
  { group: 'basic', groupLabel: '基本信息', value: 'idNumber', label: '证件号码' },
  
  // 联系信息
  { group: 'contact', groupLabel: '联系信息', value: 'phoneNumber', label: '联系电话' },
  { group: 'contact', groupLabel: '联系信息', value: 'email', label: '邮箱' },
  { group: 'contact', groupLabel: '联系信息', value: 'currentAddress', label: '现居住地' },
  { group: 'contact', groupLabel: '联系信息', value: 'emergencyContact', label: '紧急联系人' },
  { group: 'contact', groupLabel: '联系信息', value: 'emergencyContactPhone', label: '紧急联系电话' },
  
  // 工作信息
  { group: 'work', groupLabel: '工作信息', value: 'institutionName', label: '所属机构' },
  { group: 'work', groupLabel: '工作信息', value: 'positionName', label: '当前岗位' },
  { group: 'work', groupLabel: '工作信息', value: 'personnelStatus', label: '人员状态' },
  { group: 'work', groupLabel: '工作信息', value: 'personnelType', label: '人员类型' },
  { group: 'work', groupLabel: '工作信息', value: 'hireDate', label: '入职日期' },
  { group: 'work', groupLabel: '工作信息', value: 'workStartDate', label: '参加工作时间' },
  { group: 'work', groupLabel: '工作信息', value: 'contractEndDate', label: '合同到期日期' },
  
  // 学历信息
  { group: 'education', groupLabel: '学历信息', value: 'educationDegree', label: '最高学历' },
  { group: 'education', groupLabel: '学历信息', value: 'degree', label: '最高学位' },
  { group: 'education', groupLabel: '学历信息', value: 'graduateSchool', label: '毕业院校' },
  { group: 'education', groupLabel: '学历信息', value: 'major', label: '所学专业' },
  { group: 'education', groupLabel: '学历信息', value: 'graduateDate', label: '毕业时间' },
  
  // 职称信息
  { group: 'title', groupLabel: '职称信息', value: 'professionalTitle', label: '专业技术职务' },
  { group: 'title', groupLabel: '职称信息', value: 'titleLevel', label: '职称级别' },
  { group: 'title', groupLabel: '职称信息', value: 'titleDate', label: '职称获得时间' }
]

// 人员状态选项
const personnelStatusOptions = [
  { label: '在职', value: 'active' },
  { label: '离职', value: 'resigned' },
  { label: '退休', value: 'retired' },
  { label: '返聘', value: 'rehired' },
  { label: '实习', value: 'intern' },
  { label: '兼职', value: 'partTime' }
]

// 部门树数据
const departmentTree = computed(() => organizationStore.departmentTree)

// 搜索字段配置
const searchFields = [
  {
    field: 'keyword',
    label: '关键词',
    type: 'input',
    placeholder: '姓名/工号',
    defaultValue: ''
  },
  {
    field: 'institutionId',
    label: '所属部门',
    type: 'treeSelect',
    props: {
      data: departmentTree.value,
      props: { label: 'name', value: 'id' },
      placeholder: '请选择部门',
      filterable: true,
      clearable: true
    }
  },
  {
    field: 'personnelStatus',
    label: '人员状态',
    type: 'select',
    options: personnelStatusOptions,
    defaultValue: 'active'
  },
  {
    field: 'gender',
    label: '性别',
    type: 'select',
    options: [
      { label: '男', value: 'male' },
      { label: '女', value: 'female' }
    ]
  },
  {
    field: 'educationDegree',
    label: '学历',
    type: 'select',
    options: [
      { label: '博士', value: 'doctor' },
      { label: '硕士', value: 'master' },
      { label: '本科', value: 'bachelor' },
      { label: '专科', value: 'college' },
      { label: '高中及以下', value: 'high' }
    ]
  },
  {
    field: 'professionalTitle',
    label: '职称',
    type: 'select',
    options: [
      { label: '正高级', value: 'senior' },
      { label: '副高级', value: 'deputy_senior' },
      { label: '中级', value: 'middle' },
      { label: '初级', value: 'junior' },
      { label: '无职称', value: 'none' }
    ]
  },
  {
    field: 'politicalStatus',
    label: '政治面貌',
    type: 'select',
    options: [
      { label: '中共党员', value: 'party_member' },
      { label: '民主党派', value: 'democratic_party' },
      { label: '群众', value: 'masses' }
    ]
  },
  {
    field: 'hireDateRange',
    label: '入职日期',
    type: 'dateRange',
    props: {
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      valueFormat: 'YYYY-MM-DD'
    }
  },
  {
    field: 'highLevelTalentCategory',
    label: '人才分类',
    type: 'select',
    options: [
      { label: 'A类人才', value: 'A' },
      { label: 'B类人才', value: 'B' },
      { label: 'C类人才', value: 'C' },
      { label: 'D类人才', value: 'D' },
      { label: 'E类人才', value: 'E' }
    ]
  },
  {
    field: 'infoCompletenessRange',
    label: '信息完整度',
    type: 'numberRange',
    props: {
      min: 0,
      max: 100,
      startPlaceholder: '最小值',
      endPlaceholder: '最大值'
    }
  }
]

// 虚拟表格列配置
const virtualTableColumns = computed<TableColumn[]>(() => [
  {
    prop: 'selection',
    label: '选择',
    width: 55,
    fixed: 'left',
    slot: 'selection'
  },
  {
    prop: 'employeeNumber',
    label: '工号',
    width: 100,
    fixed: 'left',
    sortable: true
  },
  {
    prop: 'fullName',
    label: '姓名',
    width: 150,
    fixed: 'left',
    slot: 'fullName'
  },
  {
    prop: 'gender',
    label: '性别',
    width: 80,
    formatter: (row: Employee) => row.gender === 'male' ? '男' : '女'
  },
  {
    prop: 'institutionName',
    label: '所属部门',
    minWidth: 150,
    showOverflowTooltip: true
  },
  {
    prop: 'positionName',
    label: '岗位',
    minWidth: 120,
    showOverflowTooltip: true
  },
  {
    prop: 'personnelStatus',
    label: '状态',
    width: 100,
    slot: 'personnelStatus'
  },
  {
    prop: 'phoneNumber',
    label: '联系电话',
    width: 120
  },
  {
    prop: 'email',
    label: '邮箱',
    minWidth: 180,
    showOverflowTooltip: true
  },
  {
    prop: 'educationDegree',
    label: '学历',
    width: 100
  },
  {
    prop: 'professionalTitle',
    label: '职称',
    width: 100
  },
  {
    prop: 'hireDate',
    label: '入职日期',
    width: 110,
    sortable: true,
    formatter: (row: Employee) => row.hireDate?.split(' ')[0]
  },
  {
    prop: 'infoCompletenessPercentage',
    label: '信息完整度',
    width: 120,
    slot: 'infoCompletenessPercentage',
    sortable: true
  },
  {
    prop: 'operations',
    label: '操作',
    width: 180,
    fixed: 'right',
    slot: 'operations'
  }
])

// 获取员工列表
const fetchEmployeeList = async () => {
  loading.value =   true
  try {
    // 处理日期范围
    if (searchForm.hireDateRange && Array.isArray(searchForm.hireDateRange)) {
      searchForm.hireDateStart = searchForm.hireDateRange[0]
      searchForm.hireDateEnd = searchForm.hireDateRange[1]
    }
    
    // 处理信息完整度范围
    if (searchForm.infoCompletenessRange && Array.isArray(searchForm.infoCompletenessRange)) {
      searchForm.infoCompletenessMin = searchForm.infoCompletenessRange[0]
      searchForm.infoCompletenessMax = searchForm.infoCompletenessRange[1]
    }

    const res = await employeeApi.getList(searchForm)
    tableData.value =   res.data.list.map(item => ({ ...item, _selected: false }))
    total.value =   res.data.total
  } catch (__error) {
    ElMessage.error('获取员工列表失败')
  } finally {
    loading.value =   false
  }
}

// 获取行key
const getRowKey = (row: Employee) => {
  return row.employeeId
}

// 搜索
const handleSearch = () => {
  searchForm.page = 1
  fetchEmployeeList()
}

// 重置
const handleReset = () => {
  searchForm.page = 1
  fetchEmployeeList()
}

// 排序变化
   
const handleSortChange = ({ prop, order }: unknown) => {
  searchForm.sortField = prop
  searchForm.sortOrder = order === 'ascending' ? 'asc' : 'desc'
  fetchEmployeeList()
}

// 页码变化
const handlePageChange = (page: number) => {
  searchForm.page = page
  fetchEmployeeList()
}

// 每页数量变化
const handleSizeChange = (size: number) => {
  searchForm.pageSize = size
  searchForm.page = 1
  fetchEmployeeList()
}

// 选择变化
const handleSelectionChange = (selection: Employee[]) => {
  selectedRows.value =   selection
}

// 复选框变化
const handleCheckboxChange = (row: Employee, checked: boolean) => {
  row._selected = checked
  if (checked) {
    if (!selectedRows.value.find(item => item.employeeId === row.employeeId)) {
      selectedRows.value.push(row)
    }
  } else {
    const index = selectedRows.value.findIndex(item => item.employeeId === row.employeeId)
    if (index > -1) {
      selectedRows.value.splice(index, 1)
    }
  }
}

// 右键菜单
const handleRowContextMenu = (row: Employee, index: number, event: Event) => {
  }

// 列配置变化
const handleColumnsChange = (columns: TableColumn[]) => {
  }

// 导出
   
const handleExport = async (params: unknown) => {
  try {
    await employeeApi.export({
      format: params.format || 'xlsx',
      fields: params.fields,
      filename: params.filename,
      filters: searchForm
    })
    ElMessage.success('导出成功')
  } catch (__error) {
    ElMessage.error('导出失败')
  }
}

// 新增员工
const handleCreate = () => {
  router.push('/employee/create')
}

// 查看员工
const handleView = (row: Employee) => {
  router.push(`/employee/detail/${row.employeeId}`)
}

// 编辑员工
const handleEdit = (row: Employee) => {
  router.push(`/employee/edit/${row.employeeId}`)
}

// 行点击
const handleRowClick = (row: Employee) => {
  handleView(row)
}

// 更多操作
const handleCommand = async (command: string, row: Employee) => {
  switch (command) {
    case 'export':
      await handleExportSingle(row)
      break
    case 'print':
      await handlePrint(row)
      break
    case 'history':
      await handleViewHistory(row)
      break
    case 'delete':
      await handleDelete(row)
      break
  }
}

// 导出单个员工档案
const handleExportSingle = async (row: Employee) => {
  try {
    await employeeApi.export({
      format: 'pdf',
      ids: [row.employeeId],
      filename: `${row.fullName}_档案`
    })
    ElMessage.success('导出成功')
  } catch (__error) {
    ElMessage.error('导出失败')
  }
}

// 打印档案
const handlePrint = (row: Employee) => {
  window.open(`/employee/print/${row.employeeId}`, '_blank')
}

// 查看变更历史
const handleViewHistory = (row: Employee) => {
  router.push(`/employee/history/${row.employeeId}`)
}

// 删除员工
const handleDelete = async (row: Employee) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除员工"${row.fullName}"吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await employeeApi.delete(row.employeeId)
    ElMessage.success('删除成功')
    fetchEmployeeList()
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 批量导入
const handleImport = () => {
  importForm.mode = 'add'
  importForm.file = null
  importDialogVisible.value =   true
}

// 下载模板
const handleDownloadTemplate = async () => {
  try {
    await employeeApi.downloadTemplate()
    ElMessage.success('模板下载成功')
  } catch (__error) {
    ElMessage.error('下载模板失败')
  }
}

// 文件选择
   
const handleFileChange = (file: unknown) => {
  importForm.file = file.raw
}

// 文件超出限制
const handleExceed = () => {
  ElMessage.warning('只能上传一个文件')
}

// 确认导入
const handleImportConfirm = async () => {
  if (!importForm.file) {
    ElMessage.warning('请选择要导入的文件')
    return
  }
  
  importLoading.value =   true
  try {
    const res = await employeeApi.import(importForm.file, importForm.mode)
    
    if (res.data.failed > 0) {
      ElMessage.warning(`导入完成，成功 ${res.data.success} 条，失败 ${res.data.failed} 条`)
    } else {
      ElMessage.success(`导入成功，共导入 ${res.data.success} 条数据`)
    }
    
    importDialogVisible.value =   false
    fetchEmployeeList()
  } catch (__error) {
    ElMessage.error('导入失败')
  } finally {
    importLoading.value =   false
  }
}

// 批量操作
const handleBatchOperation = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要操作的数据')
    return
  }
  
  batchForm.operation = ''
  batchForm.status = ''
  batchForm.departmentId = ''
  batchDialogVisible.value =   true
}

// 确认批量操作
const handleBatchConfirm = async () => {
  if (!batchForm.operation) {
    ElMessage.warning('请选择操作类型')
    return
  }
  
  const ids = selectedRows.value.map(row => row.employeeId)
  
  try {
    await ElMessageBox.confirm(
      `确定要对选中的 ${ids.length} 条记录执行批量操作吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    batchLoading.value =   true
    
    switch (batchForm.operation) {
      case 'export':
        await employeeApi.export({
          format: 'xlsx',
          ids,
          filename: '员工批量导出'
        })
        ElMessage.success('导出成功')
        break
        
      case 'updateStatus':
        if (!batchForm.status) {
          ElMessage.warning('请选择目标状态')
          return
        }
        await employeeApi.batchUpdateStatus(ids, batchForm.status)
        ElMessage.success('状态更新成功')
        fetchEmployeeList()
        break
        
      case 'assign':
        if (!batchForm.departmentId) {
          ElMessage.warning('请选择目标部门')
          return
        }
        // 批量分配部门API
        ElMessage.success('部门分配成功')
        fetchEmployeeList()
        break
        
      case 'delete':
        await employeeApi.batchDelete(ids)
        ElMessage.success('批量删除成功')
        fetchEmployeeList()
        break
    }
    
    batchDialogVisible.value =   false
    selectedRows.value =   []
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('批量操作失败')
    }
  } finally {
    batchLoading.value =   false
  }
}

// 获取导出数据
   
const fetchExportData = async (params: unknown) => {
  try {
    // 构建查询参数
   
    const queryParams: unknown = {
      ...searchForm,
      page: 1,
      pageSize: 999999 // 获取所有数据
    }
    
    // 如果是选中数据导出
    if (params.range === 'selected' && params.selection) {
      return params.selection
    }
    
    // 如果是当前页导出
    if (params.range === 'current') {
      return tableData.value
    }
    
    // 获取所有数据
    const response = await employeeApi.getList(queryParams)
    return response.data.list
  } catch (__error) {
    throw error
  }
}

// 处理导出完成
   
const handleExported = async (result: unknown) => {
  // 记录导出历史
  const exportHistory = {
    ...result,
    userId: userStore.userInfo?.id || '', // 从 store 获取当前用户ID
    module: 'employee'
  }
  
  // 调用API保存导出历史
  try {
    await employeeApi.saveExportHistory(exportHistory)
  } catch (__error) {
    }
}

// 保存导出模板
   
const handleSaveTemplate = async (template: unknown) => {
  // 添加到模板列表
  const newTemplate = {
    ...template,
    id: Date.now().toString(),
    userId: userStore.userInfo?.id || '',
    createTime: new Date().toISOString()
  }
  
  // 调用API保存模板
  try {
    const res = await employeeApi.saveExportTemplate(newTemplate)
    // 使用服务器返回的ID更新模板
    newTemplate.id = res.data.id
    exportTemplates.value.push(newTemplate)
    ElMessage.success('模板保存成功')
  } catch (__error) {
    ElMessage.error('模板保存失败')
  }
}

// 删除导出模板
const handleDeleteTemplate = async (id: string) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除该导出模板吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 调用API删除模板
    await employeeApi.deleteExportTemplate(id)
    
    const index = exportTemplates.value.findIndex(t => t.id === id)
    if (index > -1) {
      exportTemplates.value.splice(index, 1)
    }
    
    ElMessage.success('模板删除成功')
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('模板删除失败')
    }
  }
}

// 获取进度条颜色
const getProgressColor = (percentage: number): string => {
  if (percentage < 60) return '#f56c6c'
  if (percentage < 80) return '#e6a23c'
  return '#67c23a'
}

// 获取状态标签类型
const getStatusTagType = (status: PersonnelStatus): string => {
  const typeMap: Record<string, string> = {
    active: 'success',
    resigned: 'info',
    retired: 'warning',
    rehired: 'primary',
    intern: '',
    partTime: ''
  }
  return typeMap[status] || ''
}

// 获取状态文本
const getStatusText = (status: PersonnelStatus): string => {
  const textMap: Record<string, string> = {
    active: '在职',
    resigned: '离职',
    retired: '退休',
    rehired: '返聘',
    intern: '实习',
    partTime: '兼职'
  }
  return textMap[status] || status
}

// 初始化
onMounted(() => {
  // 加载部门数据
  organizationStore.fetchDepartments()
  // 获取员工列表
  fetchEmployeeList()
})
</script>

<style lang="scss" scoped>
.employee-list-optimized {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .action-card {
    margin-bottom: 16px;

    .action-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .action-left {
        display: flex;
        gap: 10px;
      }
      
      .action-right {
        display: flex;
        align-items: center;
        gap: 12px;
      }
    }
  }
  
  .table-pagination {
    padding: 16px 0;
    display: flex;
    justify-content: flex-end;
  }

  .employee-name {
    display: flex;
    align-items: center;
    gap: 8px;

    .employee-avatar {
      flex-shrink: 0;
    }

    .name-text {
      font-weight: 500;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

// 响应式
@media (max-width: 768px) {
  .employee-list-optimized {
    .action-card {
      .action-bar {
        flex-wrap: wrap;
        gap: 12px;
        
        .action-left,
        .action-right {
          width: 100%;
          flex-wrap: wrap;
        }
      }
    }
  }
}
</style>