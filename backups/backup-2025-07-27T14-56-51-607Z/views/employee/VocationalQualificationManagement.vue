<template>
  <div class="vocational-qualification-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>职业资格管理</h2>
      <p>管理教职工的职业资格证书，包括有效期跟踪、续期提醒和统计分析</p>
    </div>

    <!-- 搜索和操作区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-form">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索证书名称、员工姓名、颁发机构"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.certificateType" placeholder="证书类型" clearable>
              <el-option
                v-for="item in certificateTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.validityStatus" placeholder="有效期状态" clearable>
              <el-option
                v-for="item in validityStatusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-date-picker
              v-model="expiryDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="到期开始日期"
              end-placeholder="到期结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleDateRangeChange"
             />
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-col>
        </el-row>
        
        <!-- 高级搜索 -->
        <el-collapse v-model="activeCollapse" class="advanced-search">
          <el-collapse-item title="高级搜索" name="advanced">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="员工ID">
                  <el-input v-model="searchForm.employeeId" placeholder="请输入员工ID"   />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="颁发机构">
                  <el-input v-model="searchForm.issuingAuthority" placeholder="请输入颁发机构"   />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="证书编号">
                  <el-input v-model="searchForm.certificateNumber" placeholder="请输入证书编号"   />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="验证状态">
                  <el-select v-model="searchForm.verificationStatus" placeholder="请选择" clearable>
                    <el-option
                      v-for="item in verificationStatusOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                     />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="证书等级">
                  <el-input v-model="searchForm.level" placeholder="请输入证书等级"   />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="优先级">
                  <el-select v-model="searchForm.priority" placeholder="请选择" clearable>
                    <el-option
                      v-for="item in priorityOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                     />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="是否需要续期">
                  <el-select v-model="searchForm.renewalRequired" placeholder="请选择" clearable>
                    <el-option label="需要续期" :value="true"  />
                    <el-option label="不需要续期" :value="false"  />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="排序方式">
                  <el-row :gutter="10">
                    <el-col :span="12">
                      <el-select v-model="searchForm.sortBy" placeholder="排序字段">
                        <el-option
                          v-for="item in vocationalQualificationSortOptions"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                         />
                      </el-select>
                    </el-col>
                    <el-col :span="12">
                      <el-select v-model="searchForm.sortDirection" placeholder="排序方向">
                        <el-option label="升序" value="ASC"  />
                        <el-option label="降序" value="DESC"  />
                      </el-select>
                    </el-col>
                  </el-row>
                </el-form-item>
              </el-col>
            </el-row>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.total }}</div>
              <div class="stats-label">职业资格总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon valid">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.valid }}</div>
              <div class="stats-label">有效证书</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon expiring">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.expiring }}</div>
              <div class="stats-label">即将到期</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon expired">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.expired }}</div>
              <div class="stats-label">已过期</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 职业资格列表 -->
    <el-card class="table-card" shadow="never">
      <div class="table-header">
        <span class="table-title">职业资格列表</span>
        <div class="table-actions">
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增职业资格
          </el-button>
          <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
          <el-button @click="handleImport">
            <el-icon><Upload /></el-icon>
            导入
          </el-button>
          <el-button 
            type="danger" 
            :disabled="selectedRows.length === 0"
            @click="handleBatchDelete"
          >
            <el-icon><Delete /></el-icon>
            批量删除
          </el-button>
          <el-button 
            type="success" 
            :disabled="selectedRows.length === 0"
            @click="handleBatchRenew"
          >
            <el-icon><Refresh /></el-icon>
            批量续期
          </el-button>
        </div>
      </div>

      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="employeeName" label="员工姓名" width="120"  />
        <el-table-column prop="certificateName" label="证书名称" min-width="180" show-overflow-tooltip  />
        <el-table-column prop="certificateTypeName" label="证书类型" width="120">
          <template #default="scope">
            <el-tag :type="getCertificateTypeTagType(scope.row.certificateType)" size="small">
              {{ scope.row.certificateTypeName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="certificateNumber" label="证书编号" width="140" show-overflow-tooltip  />
        <el-table-column prop="issuingAuthority" label="颁发机构" min-width="150" show-overflow-tooltip  />
        <el-table-column prop="issueDate" label="颁发日期" width="110"  />
        <el-table-column prop="expiryDate" label="到期日期" width="110">
          <template #default="scope">
            <span v-if="scope.row.expiryDate" :class="getExpiryDateClass(scope.row.expiryDate)">
              {{ scope.row.expiryDate }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="validityStatusName" label="有效期状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getValidityStatusTagType(scope.row.validityStatus)" size="small">
              {{ scope.row.validityStatusName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="level" label="等级" width="80" align="center"  />
        <el-table-column prop="priorityName" label="优先级" width="80" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.priority" :type="getPriorityTagType(scope.row.priority)" size="small">
              {{ scope.row.priorityName }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="80" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.isActive" type="success" size="small">激活</el-tag>
            <el-tag v-else type="info" size="small">停用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="240" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" link @click="handleView(scope.row)">
              查看
            </el-button>
            <el-button size="small" type="primary" link @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button 
              v-if="scope.row.renewalRequired && scope.row.validityStatus === 'EXPIRING_SOON'"
              size="small" 
              type="success" 
              link 
              @click="handleRenew(scope.row)"
            >
              续期
            </el-button>
            <el-button 
              size="small" 
              :type="scope.row.isActive ? 'warning' : 'info'" 
              link 
              @click="handleToggleActive(scope.row)"
            >
              {{ scope.row.isActive ? '停用' : '激活' }}
            </el-button>
            <el-button size="small" type="danger" link @click="handleDelete(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 职业资格详情/编辑对话框 -->
    <VocationalQualificationDialog
      v-model:visible="dialogVisible"
      :vocational-qualification="currentVocationalQualification"
      :mode="dialogMode"
      @success="handleDialogSuccess"
    />

    <!-- 续期对话框 -->
    <RenewalDialog
      v-model:visible="renewalDialogVisible"
      :vocational-qualification="currentVocationalQualification"
      :batch-mode="batchRenewalMode"
      :selected-rows="selectedRows"
      @success="handleRenewalSuccess"
    />

    <!-- 导入对话框 -->
    <ImportDialog
      v-model:visible="importDialogVisible"
      title="导入职业资格"
      :upload-url="'/api/v1/vocational-qualifications/import'"
      :template-url="'/api/v1/vocational-qualifications/template'"
      @success="handleImportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  Download,
  Upload,
  Delete,
  Document,
  CircleCheck,
  Clock,
  Warning
} from '@element-plus/icons-vue'
import { 
  vocationalQualificationApi, 
  certificateTypeOptions, 
  validityStatusOptions, 
  verificationStatusOptions,
  priorityOptions,
  vocationalQualificationSortOptions 
} from '@/api/vocationalQualification'
import type { 
  VocationalQualification, 
  VocationalQualificationQueryRequest,
  VocationalQualificationStatistics 
} from '@/types/vocationalQualification'
import VocationalQualificationDialog from './components/VocationalQualificationDialog.vue'
import RenewalDialog from './components/RenewalDialog.vue'
import HrImportDialog from '@/components/HrImportDialog.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref<VocationalQualification[]>([])
const selectedRows = ref<VocationalQualification[]>([])
const activeCollapse = ref<string[]>([])

// 搜索表单
const searchForm = reactive<VocationalQualificationQueryRequest>({
  page: 0,
  size: 20,
  keyword: '',
  certificateType: undefined,
  validityStatus: undefined,
  verificationStatus: undefined,
  issuingAuthority: '',
  certificateNumber: '',
  employeeId: '',
  level: '',
  priority: undefined,
  expiryDateStart: '',
  expiryDateEnd: '',
  renewalRequired: undefined,
  sortBy: 'expiryDate',
  sortDirection: 'ASC' as unknown
})

// 日期范围
const expiryDateRange = ref<[string, string] | null>(null)

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 统计信息
const stats = reactive({
  total: 0,
  valid: 0,
  expiring: 0,
  expired: 0
})

// 对话框相关
const dialogVisible = ref(false)
const dialogMode = ref<'view' | 'add' | 'edit'>('view')
const currentVocationalQualification = ref<VocationalQualification | null>(null)

// 续期对话框
const renewalDialogVisible = ref(false)
const batchRenewalMode = ref(false)

// 导入对话框
const importDialogVisible = ref(false)

// 获取职业资格列表
const fetchVocationalQualifications = async () => {
  try {
    loading.value = true
    const params = {
      ...searchForm,
      page: pagination.page - 1, // 后端从0开始
      size: pagination.size
    }
    
    const result = await vocationalQualificationApi.query(params)
    tableData.value = result.content as unknown // 临时修复类型不匹配
    pagination.total = result.totalElements
  } catch (__error) {
    ElMessage.error('获取职业资格列表失败')
  } finally {
    loading.value = false
  }
}

// 获取统计信息
const fetchStats = async () => {
  try {
    const statistics = await vocationalQualificationApi.getStatistics()
    stats.total = statistics.totalCount
    stats.valid = statistics.validCount
    stats.expiring = statistics.expiringSoonCount
    stats.expired = statistics.expiredCount
  } catch (__error) {
    }
}

// 日期范围变化处理
const handleDateRangeChange = (value: [string, string] | null) => {
  if (value) {
    searchForm.expiryDateStart = value[0]
    searchForm.expiryDateEnd = value[1]
  } else {
    searchForm.expiryDateStart = ''
    searchForm.expiryDateEnd = ''
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchVocationalQualifications()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    page: 0,
    size: 20,
    keyword: '',
    certificateType: undefined,
    validityStatus: undefined,
    verificationStatus: undefined,
    issuingAuthority: '',
    certificateNumber: '',
    employeeId: '',
    level: '',
    priority: undefined,
    expiryDateStart: '',
    expiryDateEnd: '',
    renewalRequired: undefined,
    sortBy: 'expiryDate',
    sortDirection: 'ASC'
  })
  expiryDateRange.value = null
  pagination.page = 1
  fetchVocationalQualifications()
}

// 新增职业资格
const handleAdd = () => {
  currentVocationalQualification.value = null
  dialogMode.value = 'add'
  dialogVisible.value = true
}

// 查看职业资格
const handleView = (vocationalQualification: VocationalQualification) => {
  currentVocationalQualification.value = vocationalQualification
  dialogMode.value = 'view'
  dialogVisible.value = true
}

// 编辑职业资格
const handleEdit = (vocationalQualification: VocationalQualification) => {
  currentVocationalQualification.value = vocationalQualification
  dialogMode.value = 'edit'
  dialogVisible.value = true
}

// 续期职业资格
const handleRenew = (vocationalQualification: VocationalQualification) => {
  currentVocationalQualification.value = vocationalQualification
  batchRenewalMode.value = false
  renewalDialogVisible.value = true
}

// 批量续期
const handleBatchRenew = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要续期的记录')
    return
  }
  batchRenewalMode.value = true
  renewalDialogVisible.value = true
}

// 切换激活状态
const handleToggleActive = async (vocationalQualification: VocationalQualification) => {
  try {
    const newStatus = !vocationalQualification.isActive
    await vocationalQualificationApi.batchSetActive([vocationalQualification.id!], newStatus)
    ElMessage.success(newStatus ? '已激活' : '已停用')
    fetchVocationalQualifications()
    fetchStats()
  } catch (__error) {
    ElMessage.error('操作失败')
  }
}

// 删除职业资格
const handleDelete = async (vocationalQualification: VocationalQualification) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除职业资格 "${vocationalQualification.certificateName}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await vocationalQualificationApi.delete(vocationalQualification.id!)
    ElMessage.success('删除成功')
    fetchVocationalQualifications()
    fetchStats()
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 条职业资格记录吗？此操作不可恢复。`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const ids = selectedRows.value.map(row => row.id!).filter(id => id)
    await vocationalQualificationApi.batchDelete(ids)
    ElMessage.success('批量删除成功')
    fetchVocationalQualifications()
    fetchStats()
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

// 表格选择变化
const handleSelectionChange = (selection: VocationalQualification[]) => {
  selectedRows.value = selection
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchVocationalQualifications()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchVocationalQualifications()
}

// 对话框成功回调
const handleDialogSuccess = () => {
  fetchVocationalQualifications()
  fetchStats()
}

// 续期成功回调
const handleRenewalSuccess = () => {
  fetchVocationalQualifications()
  fetchStats()
}

// 导出
const handleExport = async () => {
  try {
    const blob = await vocationalQualificationApi.export(searchForm)
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `职业资格_${new Date().toISOString().slice(0, 10)}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('导出成功')
  } catch (__error) {
    ElMessage.error('导出失败')
  }
}

// 导入
const handleImport = () => {
  importDialogVisible.value = true
}

// 导入成功回调
const handleImportSuccess = () => {
  fetchVocationalQualifications()
  fetchStats()
}

// 获取证书类型标签类型
const getCertificateTypeTagType = (type: string) => {
  switch (type) {
    case 'SKILL_CERTIFICATE':
      return 'success'
    case 'PROFESSIONAL_LICENSE':
      return 'warning'
    case 'TITLE_CERTIFICATE':
      return 'info'
    case 'TRAINING_CERTIFICATE':
      return ''
    default:
      return 'info'
  }
}

// 获取有效期状态标签类型
const getValidityStatusTagType = (status: string) => {
  switch (status) {
    case 'VALID':
      return 'success'
    case 'EXPIRING_SOON':
      return 'warning'
    case 'EXPIRED':
      return 'danger'
    case 'SUSPENDED':
      return 'info'
    default:
      return ''
  }
}

// 获取优先级标签类型
const getPriorityTagType = (priority: string) => {
  switch (priority) {
    case 'LOW':
      return 'info'
    case 'MEDIUM':
      return ''
    case 'HIGH':
      return 'warning'
    case 'CRITICAL':
      return 'danger'
    default:
      return ''
  }
}

// 获取到期日期样式类
const getExpiryDateClass = (expiryDate: string) => {
  const today = new Date()
  const expiry = new Date(expiryDate)
  const diffTime = expiry.getTime() - today.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) {
    return 'expired-date'
  } else if (diffDays <= 30) {
    return 'expiring-date'
  } else {
    return 'valid-date'
  }
}

// 初始化
onMounted(() => {
  fetchVocationalQualifications()
  fetchStats()
})
</script>

<style scoped>
.vocational-qualification-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  padding: 10px 0;
}

.advanced-search {
  margin-top: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.valid {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.expiring {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.expired {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 到期日期样式 */
.expired-date {
  color: #f56c6c;
  font-weight: 600;
}

.expiring-date {
  color: #e6a23c;
  font-weight: 600;
}

.valid-date {
  color: #67c23a;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .vocational-qualification-management {
    padding: 10px;
  }

  .search-form .el-row {
    flex-direction: column;
  }

  .search-form .el-col {
    width: 100%;
    margin-bottom: 10px;
  }

  .stats-row .el-col {
    margin-bottom: 10px;
  }

  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .table-actions {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header) {
  background-color: #f8f9fa;
}

:deep(.el-table__header th) {
  background-color: #f8f9fa;
  color: #303133;
  font-weight: 600;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

/* 搜索表单样式 */
:deep(.el-form-item) {
  margin-bottom: 10px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

/* 折叠面板样式 */
:deep(.el-collapse-item__header) {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 0 16px;
  font-weight: 500;
}

:deep(.el-collapse-item__content) {
  padding: 20px 16px;
  background-color: #fafafa;
  border-radius: 0 0 4px 4px;
}

/* 标签样式 */
.el-tag {
  border-radius: 4px;
  font-weight: 500;
}

/* 按钮样式 */
.el-button {
  border-radius: 6px;
  font-weight: 500;
}

.el-button--primary {
  background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
  border: none;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #66b1ff 0%, #40a9ff 100%);
}

.el-button--success {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  border: none;
}

.el-button--success:hover {
  background: linear-gradient(135deg, #85ce61 0%, #95d475 100%);
}
</style>
