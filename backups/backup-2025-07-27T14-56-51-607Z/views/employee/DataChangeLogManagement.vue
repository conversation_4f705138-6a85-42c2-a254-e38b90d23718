<template>
  <div class="data-change-log-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>数据变更日志</h2>
      <p>查看和分析系统数据变更记录，支持变更前后数据对比和操作审计</p>
    </div>

    <!-- 搜索和操作区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-form">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索表名、操作人、变更描述"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.changeType" placeholder="变更类型" clearable>
              <el-option
                v-for="item in changeTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.operationType" placeholder="操作类型" clearable>
              <el-option
                v-for="item in operationTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-date-picker
              v-model="createTimeRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="handleDateRangeChange"
             />
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-col>
        </el-row>
        
        <!-- 高级搜索 -->
        <el-collapse v-model="activeCollapse" class="advanced-search">
          <el-collapse-item title="高级搜索" name="advanced">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="表名">
                  <el-input v-model="searchForm.tableName" placeholder="请输入表名"   />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="记录ID">
                  <el-input v-model="searchForm.recordId" placeholder="请输入记录ID"   />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="操作人">
                  <el-input v-model="searchForm.operatorName" placeholder="请输入操作人姓名"   />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="操作人类型">
                  <el-select v-model="searchForm.operatorType" placeholder="请选择" clearable>
                    <el-option
                      v-for="item in operatorTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                     />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="业务模块">
                  <el-input v-model="searchForm.businessModule" placeholder="请输入业务模块"   />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="风险等级">
                  <el-select v-model="searchForm.riskLevel" placeholder="请选择" clearable>
                    <el-option
                      v-for="item in riskLevelOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                     />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="执行状态">
                  <el-select v-model="searchForm.isSuccess" placeholder="请选择" clearable>
                    <el-option label="成功" :value="true"  />
                    <el-option label="失败" :value="false"  />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="排序方式">
                  <el-row :gutter="10">
                    <el-col :span="12">
                      <el-select v-model="searchForm.sortBy" placeholder="排序字段">
                        <el-option
                          v-for="item in dataChangeLogSortOptions"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                         />
                      </el-select>
                    </el-col>
                    <el-col :span="12">
                      <el-select v-model="searchForm.sortDirection" placeholder="排序方向">
                        <el-option label="升序" value="ASC"  />
                        <el-option label="降序" value="DESC"  />
                      </el-select>
                    </el-col>
                  </el-row>
                </el-form-item>
              </el-col>
            </el-row>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.total }}</div>
              <div class="stats-label">变更记录总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon today">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.today }}</div>
              <div class="stats-label">今日变更</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon week">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.week }}</div>
              <div class="stats-label">本周变更</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon month">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.month }}</div>
              <div class="stats-label">本月变更</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据变更日志列表 -->
    <el-card class="table-card" shadow="never">
      <div class="table-header">
        <span class="table-title">变更记录列表</span>
        <div class="table-actions">
          <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
          <el-button @click="handleExportComparison" :disabled="selectedRows.length === 0">
            <el-icon><DocumentCopy /></el-icon>
            导出对比报告
          </el-button>
          <el-button 
            type="warning" 
            :disabled="selectedRows.length === 0"
            @click="handleBatchArchive"
          >
            <el-icon><Box /></el-icon>
            批量归档
          </el-button>
          <el-button 
            type="danger" 
            :disabled="selectedRows.length === 0"
            @click="handleBatchDelete"
          >
            <el-icon><Delete /></el-icon>
            批量删除
          </el-button>
          <el-button type="info" @click="handleCleanExpired">
            <el-icon><DeleteFilled /></el-icon>
            清理过期
          </el-button>
        </div>
      </div>

      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="createTime" label="操作时间" width="160" sortable  />
        <el-table-column prop="tableName" label="表名" width="150" show-overflow-tooltip  />
        <el-table-column prop="operationTypeName" label="操作类型" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getOperationTypeTagType(scope.row.operationType)" size="small">
              {{ scope.row.operationTypeName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="changeTypeName" label="变更类型" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getChangeTypeTagType(scope.row.changeType)" size="small">
              {{ scope.row.changeTypeName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="operatorName" label="操作人" width="120"  />
        <el-table-column prop="recordId" label="记录ID" width="120" show-overflow-tooltip  />
        <el-table-column prop="changeDescription" label="变更描述" min-width="200" show-overflow-tooltip  />
        <el-table-column prop="executionTime" label="执行时间" width="100" align="center">
          <template #default="scope">
            <span :class="getExecutionTimeClass(scope.row.executionTime)">
              {{ scope.row.executionTime }}ms
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="riskLevelName" label="风险等级" width="100" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.riskLevel" :type="getRiskLevelTagType(scope.row.riskLevel)" size="small">
              {{ scope.row.riskLevelName }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="80" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.isSuccess" type="success" size="small">成功</el-tag>
            <el-tag v-else type="danger" size="small">失败</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" link @click="handleView(scope.row)">
              查看
            </el-button>
            <el-button 
              v-if="scope.row.beforeData && scope.row.afterData"
              size="small" 
              type="success" 
              link 
              @click="handleCompare(scope.row)"
            >
              对比
            </el-button>
            <el-button size="small" type="info" link @click="handleViewHistory(scope.row)">
              历史
            </el-button>
            <el-button size="small" type="warning" link @click="handleArchive(scope.row)">
              归档
            </el-button>
            <el-button size="small" type="danger" link @click="handleDelete(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 数据变更日志详情对话框 -->
    <DataChangeLogDialog
      v-model:visible="dialogVisible"
      :data-change-log="currentDataChangeLog"
      :mode="dialogMode"
      @success="handleDialogSuccess"
    />

    <!-- 数据对比对话框 -->
    <DataComparisonDialog
      v-model:visible="comparisonDialogVisible"
      :data-change-log="currentDataChangeLog"
      @success="handleComparisonSuccess"
    />

    <!-- 变更历史对话框 -->
    <ChangeHistoryDialog
      v-model:visible="historyDialogVisible"
      :table-name="currentTableName"
      :record-id="currentRecordId"
      @success="handleHistorySuccess"
    />

    <!-- 清理过期日志对话框 -->
    <CleanExpiredDialog
      v-model:visible="cleanDialogVisible"
      @success="handleCleanSuccess"
    />
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Download,
  DocumentCopy,
  Delete,
  DeleteFilled,
  Box,
  Document,
  Calendar,
  Clock,
  TrendCharts
} from '@element-plus/icons-vue'
import { 
  dataChangeLogApi, 
  operationTypeOptions, 
  changeTypeOptions, 
  operatorTypeOptions,
  riskLevelOptions,
  dataChangeLogSortOptions 
} from '@/api/dataChangeLog'
import type { 
  DataChangeLog, 
  DataChangeLogQueryRequest,
  DataChangeLogStatistics 
} from '@/types/dataChangeLog'
import DataChangeLogDialog from './components/DataChangeLogDialog.vue'
import DataComparisonDialog from './components/DataComparisonDialog.vue'
import ChangeHistoryDialog from './components/ChangeHistoryDialog.vue'
import CleanExpiredDialog from './components/CleanExpiredDialog.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref<DataChangeLog[]>([])
const selectedRows = ref<DataChangeLog[]>([])
const activeCollapse = ref<string[]>([])

// 搜索表单
const searchForm = reactive<DataChangeLogQueryRequest>({
  page: 0,
  size: 20,
  keyword: '',
  changeType: undefined,
  operationType: undefined,
  tableName: '',
  recordId: '',
  operatorName: '',
  operatorType: undefined,
  businessModule: '',
  riskLevel: undefined,
  isSuccess: undefined,
  createTimeStart: '',
  createTimeEnd: '',
  sortBy: 'createTime',
  sortDirection: 'DESC' as unknown
})

// 日期范围
const createTimeRange = ref<[string, string] | null>(null)

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 统计信息
const stats = reactive({
  total: 0,
  today: 0,
  week: 0,
  month: 0
})

// 对话框相关
const dialogVisible = ref(false)
const dialogMode = ref<'view'>('view')
const currentDataChangeLog = ref<DataChangeLog | null>(null)

// 数据对比对话框
const comparisonDialogVisible = ref(false)

// 变更历史对话框
const historyDialogVisible = ref(false)
const currentTableName = ref('')
const currentRecordId = ref('')

// 清理过期对话框
const cleanDialogVisible = ref(false)

// 获取数据变更日志列表
const fetchDataChangeLogs = async () => {
  try {
    loading.value = true
    const params = {
      ...searchForm,
      page: pagination.page - 1, // 后端从0开始
      size: pagination.size
    }
    
    const result = await dataChangeLogApi.query(params)
    tableData.value = result.content as unknown // 临时修复类型不匹配
    pagination.total = result.totalElements
  } catch (__error) {
    ElMessage.error('获取数据变更日志列表失败')
  } finally {
    loading.value = false
  }
}

// 获取统计信息
const fetchStats = async () => {
  try {
    const statistics = await dataChangeLogApi.getStatistics()
    stats.total = statistics.totalCount
    stats.today = statistics.todayCount
    stats.week = statistics.weekCount
    stats.month = statistics.monthCount
  } catch (__error) {
    }
}

// 日期范围变化处理
const handleDateRangeChange = (value: [string, string] | null) => {
  if (value) {
    searchForm.createTimeStart = value[0]
    searchForm.createTimeEnd = value[1]
  } else {
    searchForm.createTimeStart = ''
    searchForm.createTimeEnd = ''
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchDataChangeLogs()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    page: 0,
    size: 20,
    keyword: '',
    changeType: undefined,
    operationType: undefined,
    tableName: '',
    recordId: '',
    operatorName: '',
    operatorType: undefined,
    businessModule: '',
    riskLevel: undefined,
    isSuccess: undefined,
    createTimeStart: '',
    createTimeEnd: '',
    sortBy: 'createTime',
    sortDirection: 'DESC'
  })
  createTimeRange.value = null
  pagination.page = 1
  fetchDataChangeLogs()
}

// 查看数据变更日志
const handleView = (dataChangeLog: DataChangeLog) => {
  currentDataChangeLog.value = dataChangeLog
  dialogMode.value = 'view'
  dialogVisible.value = true
}

// 数据对比
const handleCompare = (dataChangeLog: DataChangeLog) => {
  currentDataChangeLog.value = dataChangeLog
  comparisonDialogVisible.value = true
}

// 查看变更历史
const handleViewHistory = (dataChangeLog: DataChangeLog) => {
  currentTableName.value = dataChangeLog.tableName
  currentRecordId.value = dataChangeLog.recordId
  historyDialogVisible.value = true
}

// 归档数据变更日志
const handleArchive = async (dataChangeLog: DataChangeLog) => {
  try {
    await ElMessageBox.confirm(
      `确定要归档此变更记录吗？`,
      '确认归档',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await dataChangeLogApi.batchArchive([dataChangeLog.id!])
    ElMessage.success('归档成功')
    fetchDataChangeLogs()
    fetchStats()
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('归档失败')
    }
  }
}

// 删除数据变更日志
const handleDelete = async (dataChangeLog: DataChangeLog) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除此变更记录吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await dataChangeLogApi.batchDelete([dataChangeLog.id!])
    ElMessage.success('删除成功')
    fetchDataChangeLogs()
    fetchStats()
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 批量归档
const handleBatchArchive = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要归档选中的 ${selectedRows.value.length} 条记录吗？`,
      '确认批量归档',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const ids = selectedRows.value.map(row => row.id!).filter(id => id)
    await dataChangeLogApi.batchArchive(ids)
    ElMessage.success('批量归档成功')
    fetchDataChangeLogs()
    fetchStats()
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('批量归档失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 条记录吗？此操作不可恢复。`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const ids = selectedRows.value.map(row => row.id!).filter(id => id)
    await dataChangeLogApi.batchDelete(ids)
    ElMessage.success('批量删除成功')
    fetchDataChangeLogs()
    fetchStats()
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

// 清理过期日志
const handleCleanExpired = () => {
  cleanDialogVisible.value = true
}

// 表格选择变化
const handleSelectionChange = (selection: DataChangeLog[]) => {
  selectedRows.value = selection
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchDataChangeLogs()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchDataChangeLogs()
}

// 对话框成功回调
const handleDialogSuccess = () => {
  fetchDataChangeLogs()
  fetchStats()
}

// 对比成功回调
const handleComparisonSuccess = () => {
  // 对比不需要刷新数据
}

// 历史成功回调
const handleHistorySuccess = () => {
  // 历史不需要刷新数据
}

// 清理成功回调
const handleCleanSuccess = () => {
  fetchDataChangeLogs()
  fetchStats()
}

// 导出
const handleExport = async () => {
  try {
    const blob = await dataChangeLogApi.export(searchForm)
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `数据变更日志_${new Date().toISOString().slice(0, 10)}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('导出成功')
  } catch (__error) {
    ElMessage.error('导出失败')
  }
}

// 导出对比报告
const handleExportComparison = async () => {
  try {
    const ids = selectedRows.value.map(row => row.id!).filter(id => id)
    const blob = await dataChangeLogApi.exportComparisonReport(ids)
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `数据对比报告_${new Date().toISOString().slice(0, 10)}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('导出对比报告成功')
  } catch (__error) {
    ElMessage.error('导出对比报告失败')
  }
}

// 获取操作类型标签类型
const getOperationTypeTagType = (type: string) => {
  switch (type) {
    case 'INSERT':
    case 'BATCH_INSERT':
      return 'success'
    case 'UPDATE':
    case 'BATCH_UPDATE':
      return 'warning'
    case 'DELETE':
    case 'BATCH_DELETE':
      return 'danger'
    case 'SELECT':
      return 'info'
    default:
      return ''
  }
}

// 获取变更类型标签类型
const getChangeTypeTagType = (type: string) => {
  switch (type) {
    case 'CREATE':
      return 'success'
    case 'MODIFY':
      return 'warning'
    case 'DELETE':
      return 'danger'
    case 'QUERY':
      return 'info'
    case 'IMPORT':
    case 'EXPORT':
      return ''
    case 'BATCH_OPERATION':
      return 'warning'
    default:
      return ''
  }
}

// 获取风险等级标签类型
const getRiskLevelTagType = (level: string) => {
  switch (level) {
    case 'LOW':
      return 'info'
    case 'MEDIUM':
      return ''
    case 'HIGH':
      return 'warning'
    case 'CRITICAL':
      return 'danger'
    default:
      return ''
  }
}

// 获取执行时间样式类
const getExecutionTimeClass = (time?: number) => {
  if (!time) return ''
  
  if (time < 100) {
    return 'fast-execution'
  } else if (time < 1000) {
    return 'normal-execution'
  } else {
    return 'slow-execution'
  }
}

// 初始化
onMounted(() => {
  fetchDataChangeLogs()
  fetchStats()
})
</script>

<style scoped>
.data-change-log-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  padding: 10px 0;
}

.advanced-search {
  margin-top: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.today {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.week {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.month {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 执行时间样式 */
.fast-execution {
  color: #67c23a;
  font-weight: 600;
}

.normal-execution {
  color: #e6a23c;
  font-weight: 600;
}

.slow-execution {
  color: #f56c6c;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .data-change-log-management {
    padding: 10px;
  }

  .search-form .el-row {
    flex-direction: column;
  }

  .search-form .el-col {
    width: 100%;
    margin-bottom: 10px;
  }

  .stats-row .el-col {
    margin-bottom: 10px;
  }

  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .table-actions {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header) {
  background-color: #f8f9fa;
}

:deep(.el-table__header th) {
  background-color: #f8f9fa;
  color: #303133;
  font-weight: 600;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

/* 搜索表单样式 */
:deep(.el-form-item) {
  margin-bottom: 10px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

/* 折叠面板样式 */
:deep(.el-collapse-item__header) {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 0 16px;
  font-weight: 500;
}

:deep(.el-collapse-item__content) {
  padding: 20px 16px;
  background-color: #fafafa;
  border-radius: 0 0 4px 4px;
}

/* 标签样式 */
.el-tag {
  border-radius: 4px;
  font-weight: 500;
}

/* 按钮样式 */
.el-button {
  border-radius: 6px;
  font-weight: 500;
}

.el-button--primary {
  background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
  border: none;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #66b1ff 0%, #40a9ff 100%);
}

.el-button--success {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  border: none;
}

.el-button--success:hover {
  background: linear-gradient(135deg, #85ce61 0%, #95d475 100%);
}
</style>
