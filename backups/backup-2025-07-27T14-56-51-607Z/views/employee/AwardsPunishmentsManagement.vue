<template>
  <div class="awards-punishments-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>奖惩记录管理</h2>
      <p>管理教职工的奖励记录和惩罚记录，包括审批状态跟踪和统计分析</p>
    </div>

    <!-- 搜索和操作区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-form">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索标题、员工姓名、颁发单位"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.type" placeholder="奖惩类型" clearable>
              <el-option
                v-for="item in awardsPunishmentsTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="searchForm.category" placeholder="奖惩类别" clearable>
              <el-option
                v-for="item in awardsPunishmentsCategoryOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-date-picker
              v-model="awardDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="奖惩开始日期"
              end-placeholder="奖惩结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleDateRangeChange"
             />
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-col>
        </el-row>
        
        <!-- 高级搜索 -->
        <el-collapse v-model="activeCollapse" class="advanced-search">
          <el-collapse-item title="高级搜索" name="advanced">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="员工ID">
                  <el-input v-model="searchForm.employeeId" placeholder="请输入员工ID"   />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="颁发单位">
                  <el-input v-model="searchForm.issuingUnit" placeholder="请输入颁发单位"   />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="审批人">
                  <el-input v-model="searchForm.approver" placeholder="请输入审批人"   />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="审批状态">
                  <el-select v-model="searchForm.approvalStatus" placeholder="请选择" clearable>
                    <el-option
                      v-for="item in approvalStatusOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                     />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="严重程度">
                  <el-select v-model="searchForm.severity" placeholder="请选择" clearable>
                    <el-option
                      v-for="item in severityOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                     />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="金额范围">
                  <el-row :gutter="10">
                    <el-col :span="12">
                      <el-input-number
                        v-model="searchForm.minAmount"
                        placeholder="最小金额"
                        :min="0"
                        controls-position="right"
                        />
                    </el-col>
                    <el-col :span="12">
                      <el-input-number
                        v-model="searchForm.maxAmount"
                        placeholder="最大金额"
                        :min="0"
                        controls-position="right"
                        />
                    </el-col>
                  </el-row>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="是否公开">
                  <el-select v-model="searchForm.isPublic" placeholder="请选择" clearable>
                    <el-option label="公开" :value="true"  />
                    <el-option label="不公开" :value="false"  />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="排序方式">
                  <el-row :gutter="10">
                    <el-col :span="12">
                      <el-select v-model="searchForm.sortBy" placeholder="排序字段">
                        <el-option
                          v-for="item in awardsPunishmentsSortOptions"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                         />
                      </el-select>
                    </el-col>
                    <el-col :span="12">
                      <el-select v-model="searchForm.sortDirection" placeholder="排序方向">
                        <el-option label="升序" value="ASC"  />
                        <el-option label="降序" value="DESC"  />
                      </el-select>
                    </el-col>
                  </el-row>
                </el-form-item>
              </el-col>
            </el-row>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total">
              <el-icon><Medal /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.total }}</div>
              <div class="stats-label">奖惩记录总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon award">
              <el-icon><Trophy /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.award }}</div>
              <div class="stats-label">奖励记录</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon punishment">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.punishment }}</div>
              <div class="stats-label">惩罚记录</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon pending">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.pending }}</div>
              <div class="stats-label">待审批</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 奖惩记录列表 -->
    <el-card class="table-card" shadow="never">
      <div class="table-header">
        <span class="table-title">奖惩记录列表</span>
        <div class="table-actions">
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增奖惩记录
          </el-button>
          <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
          <el-button @click="handleImport">
            <el-icon><Upload /></el-icon>
            导入
          </el-button>
          <el-button 
            type="danger" 
            :disabled="selectedRows.length === 0"
            @click="handleBatchDelete"
          >
            <el-icon><Delete /></el-icon>
            批量删除
          </el-button>
          <el-button 
            type="success" 
            :disabled="selectedRows.length === 0"
            @click="handleBatchApprove"
          >
            <el-icon><Check /></el-icon>
            批量审批
          </el-button>
        </div>
      </div>

      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="employeeName" label="员工姓名" width="120"  />
        <el-table-column prop="typeName" label="奖惩类型" width="100">
          <template #default="scope">
            <el-tag :type="getTypeTagType(scope.row.type)" size="small">
              {{ scope.row.typeName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="categoryName" label="奖惩类别" width="100">
          <template #default="scope">
            <el-tag :type="getCategoryTagType(scope.row.category)" size="small">
              {{ scope.row.categoryName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="奖惩标题" min-width="180" show-overflow-tooltip  />
        <el-table-column prop="issuingUnit" label="颁发单位" min-width="150" show-overflow-tooltip  />
        <el-table-column prop="awardDate" label="奖惩日期" width="110"  />
        <el-table-column prop="amount" label="金额" width="100" align="right">
          <template #default="scope">
            <span v-if="scope.row.amount">
              {{ scope.row.amount.toLocaleString() }} {{ scope.row.currency || 'CNY' }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="severityName" label="严重程度" width="100" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.severity" :type="getSeverityTagType(scope.row.severity)" size="small">
              {{ scope.row.severityName }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="approvalStatusName" label="审批状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getApprovalStatusTagType(scope.row.approvalStatus)" size="small">
              {{ scope.row.approvalStatusName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="公开状态" width="80" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.isPublic" type="success" size="small">公开</el-tag>
            <el-tag v-else type="info" size="small">私有</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" link @click="handleView(scope.row)">
              查看
            </el-button>
            <el-button size="small" type="primary" link @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button 
              v-if="scope.row.approvalStatus === 'PENDING'"
              size="small" 
              type="success" 
              link 
              @click="handleApprove(scope.row)"
            >
              审批
            </el-button>
            <el-button 
              size="small" 
              :type="scope.row.isPublic ? 'warning' : 'info'" 
              link 
              @click="handleTogglePublic(scope.row)"
            >
              {{ scope.row.isPublic ? '设为私有' : '设为公开' }}
            </el-button>
            <el-button size="small" type="danger" link @click="handleDelete(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 奖惩记录详情/编辑对话框 -->
    <AwardsPunishmentsDialog
      v-model:visible="dialogVisible"
      :awards-punishments="currentAwardsPunishments"
      :mode="dialogMode"
      @success="handleDialogSuccess"
    />

    <!-- 审批对话框 -->
    <ApprovalDialog
      v-model:visible="approvalDialogVisible"
      :awards-punishments="currentAwardsPunishments"
      :batch-mode="batchApprovalMode"
      :selected-rows="selectedRows"
      @success="handleApprovalSuccess"
    />

    <!-- 导入对话框 -->
    <ImportDialog
      v-model:visible="importDialogVisible"
      title="导入奖惩记录"
      :upload-url="'/api/v1/awards-punishments/import'"
      :template-url="'/api/v1/awards-punishments/template'"
      @success="handleImportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  Download,
  Upload,
  Delete,
  Check,
  Medal,
  Trophy,
  Warning,
  Clock
} from '@element-plus/icons-vue'
import { 
  awardsPunishmentsApi, 
  awardsPunishmentsTypeOptions, 
  awardsPunishmentsCategoryOptions, 
  approvalStatusOptions,
  severityOptions,
  awardsPunishmentsSortOptions 
} from '@/api/awardsPunishments'
import type { 
  AwardsPunishments, 
  AwardsPunishmentsQueryRequest,
  AwardsPunishmentsStatistics 
} from '@/types/awardsPunishments'
import AwardsPunishmentsDialog from './components/AwardsPunishmentsDialog.vue'
import HrApprovalDialog from './components/HrApprovalDialog.vue'
import HrImportDialog from '@/components/HrImportDialog.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref<AwardsPunishments[]>([])
const selectedRows = ref<AwardsPunishments[]>([])
const activeCollapse = ref<string[]>([])

// 搜索表单
const searchForm = reactive<AwardsPunishmentsQueryRequest>({
  page: 0,
  size: 20,
  keyword: '',
  type: undefined,
  category: undefined,
  issuingUnit: '',
  approver: '',
  employeeId: '',
  awardDateStart: '',
  awardDateEnd: '',
  approvalStatus: undefined,
  severity: undefined,
  minAmount: undefined,
  maxAmount: undefined,
  isPublic: undefined,
  sortBy: 'awardDate',
  sortDirection: 'DESC' as unknown // 临时修复类型不匹配
})

// 日期范围
const awardDateRange = ref<[string, string] | null>(null)

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 统计信息
const stats = reactive({
  total: 0,
  award: 0,
  punishment: 0,
  pending: 0
})

// 对话框相关
const dialogVisible = ref(false)
const dialogMode = ref<'view' | 'add' | 'edit'>('view')
const currentAwardsPunishments = ref<AwardsPunishments | null>(null)

// 审批对话框
const approvalDialogVisible = ref(false)
const batchApprovalMode = ref(false)

// 导入对话框
const importDialogVisible = ref(false)

// 获取奖惩记录列表
const fetchAwardsPunishments = async () => {
  try {
    loading.value = true
    const params = {
      ...searchForm,
      page: pagination.page - 1, // 后端从0开始
      size: pagination.size
    }
    
    const result = await awardsPunishmentsApi.query(params)
    tableData.value = result.content as unknown // 临时修复类型不匹配
    pagination.total = result.totalElements
  } catch (__error) {
    ElMessage.error('获取奖惩记录列表失败')
  } finally {
    loading.value = false
  }
}

// 获取统计信息
const fetchStats = async () => {
  try {
    const statistics = await awardsPunishmentsApi.getStatistics()
    stats.total = statistics.totalCount
    stats.award = statistics.awardCount
    stats.punishment = statistics.punishmentCount
    stats.pending = statistics.pendingApprovalCount
  } catch (__error) {
    }
}

// 日期范围变化处理
const handleDateRangeChange = (value: [string, string] | null) => {
  if (value) {
    searchForm.awardDateStart = value[0]
    searchForm.awardDateEnd = value[1]
  } else {
    searchForm.awardDateStart = ''
    searchForm.awardDateEnd = ''
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchAwardsPunishments()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    page: 0,
    size: 20,
    keyword: '',
    type: undefined,
    category: undefined,
    issuingUnit: '',
    approver: '',
    employeeId: '',
    awardDateStart: '',
    awardDateEnd: '',
    approvalStatus: undefined,
    severity: undefined,
    minAmount: undefined,
    maxAmount: undefined,
    isPublic: undefined,
    sortBy: 'awardDate',
    sortDirection: 'DESC'
  })
  awardDateRange.value = null
  pagination.page = 1
  fetchAwardsPunishments()
}

// 新增奖惩记录
const handleAdd = () => {
  currentAwardsPunishments.value = null
  dialogMode.value = 'add'
  dialogVisible.value = true
}

// 查看奖惩记录
const handleView = (awardsPunishments: AwardsPunishments) => {
  currentAwardsPunishments.value = awardsPunishments
  dialogMode.value = 'view'
  dialogVisible.value = true
}

// 编辑奖惩记录
const handleEdit = (awardsPunishments: AwardsPunishments) => {
  currentAwardsPunishments.value = awardsPunishments
  dialogMode.value = 'edit'
  dialogVisible.value = true
}

// 审批奖惩记录
const handleApprove = (awardsPunishments: AwardsPunishments) => {
  currentAwardsPunishments.value = awardsPunishments
  batchApprovalMode.value = false
  approvalDialogVisible.value = true
}

// 批量审批
const handleBatchApprove = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要审批的记录')
    return
  }
  batchApprovalMode.value = true
  approvalDialogVisible.value = true
}

// 切换公开状态
const handleTogglePublic = async (awardsPunishments: AwardsPunishments) => {
  try {
    const newStatus = !awardsPunishments.isPublic
    await awardsPunishmentsApi.batchSetPublic([awardsPunishments.id!], newStatus)
    ElMessage.success(newStatus ? '已设为公开' : '已设为私有')
    fetchAwardsPunishments()
    fetchStats()
  } catch (__error) {
    ElMessage.error('操作失败')
  }
}

// 删除奖惩记录
const handleDelete = async (awardsPunishments: AwardsPunishments) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除奖惩记录 "${awardsPunishments.title}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await awardsPunishmentsApi.delete(awardsPunishments.id!)
    ElMessage.success('删除成功')
    fetchAwardsPunishments()
    fetchStats()
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 条奖惩记录吗？此操作不可恢复。`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const ids = selectedRows.value.map(row => row.id!).filter(id => id)
    await awardsPunishmentsApi.batchDelete(ids)
    ElMessage.success('批量删除成功')
    fetchAwardsPunishments()
    fetchStats()
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

// 表格选择变化
const handleSelectionChange = (selection: AwardsPunishments[]) => {
  selectedRows.value = selection
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchAwardsPunishments()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchAwardsPunishments()
}

// 对话框成功回调
const handleDialogSuccess = () => {
  fetchAwardsPunishments()
  fetchStats()
}

// 审批成功回调
const handleApprovalSuccess = () => {
  fetchAwardsPunishments()
  fetchStats()
}

// 导出
const handleExport = async () => {
  try {
    const blob = await awardsPunishmentsApi.export(searchForm)
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `奖惩记录_${new Date().toISOString().slice(0, 10)}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('导出成功')
  } catch (__error) {
    ElMessage.error('导出失败')
  }
}

// 导入
const handleImport = () => {
  importDialogVisible.value = true
}

// 导入成功回调
const handleImportSuccess = () => {
  fetchAwardsPunishments()
  fetchStats()
}

// 获取奖惩类型标签类型
const getTypeTagType = (type: string) => {
  switch (type) {
    case 'AWARD':
      return 'success'
    case 'PUNISHMENT':
      return 'danger'
    default:
      return ''
  }
}

// 获取奖惩类别标签类型
const getCategoryTagType = (category: string) => {
  switch (category) {
    case 'COMMENDATION':
    case 'MERIT':
    case 'MAJOR_MERIT':
      return 'success'
    case 'WARNING':
      return 'warning'
    case 'DEMERIT':
    case 'MAJOR_DEMERIT':
      return 'danger'
    case 'DISMISSAL':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取严重程度标签类型
const getSeverityTagType = (severity: string) => {
  switch (severity) {
    case 'LOW':
      return 'success'
    case 'MEDIUM':
      return 'warning'
    case 'HIGH':
      return 'danger'
    case 'CRITICAL':
      return 'danger'
    default:
      return ''
  }
}

// 获取审批状态标签类型
const getApprovalStatusTagType = (status: string) => {
  switch (status) {
    case 'APPROVED':
      return 'success'
    case 'PENDING':
      return 'warning'
    case 'REJECTED':
      return 'danger'
    default:
      return ''
  }
}

// 初始化
onMounted(() => {
  fetchAwardsPunishments()
  fetchStats()
})
</script>

<style scoped>
.awards-punishments-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  padding: 10px 0;
}

.advanced-search {
  margin-top: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.award {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.punishment {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.pending {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .awards-punishments-management {
    padding: 10px;
  }

  .search-form .el-row {
    flex-direction: column;
  }

  .search-form .el-col {
    width: 100%;
    margin-bottom: 10px;
  }

  .stats-row .el-col {
    margin-bottom: 10px;
  }

  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .table-actions {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header) {
  background-color: #f8f9fa;
}

:deep(.el-table__header th) {
  background-color: #f8f9fa;
  color: #303133;
  font-weight: 600;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

/* 搜索表单样式 */
:deep(.el-form-item) {
  margin-bottom: 10px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

/* 折叠面板样式 */
:deep(.el-collapse-item__header) {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 0 16px;
  font-weight: 500;
}

:deep(.el-collapse-item__content) {
  padding: 20px 16px;
  background-color: #fafafa;
  border-radius: 0 0 4px 4px;
}

/* 标签样式 */
.el-tag {
  border-radius: 4px;
  font-weight: 500;
}

/* 按钮样式 */
.el-button {
  border-radius: 6px;
  font-weight: 500;
}

.el-button--primary {
  background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
  border: none;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #66b1ff 0%, #40a9ff 100%);
}

.el-button--success {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  border: none;
}

.el-button--success:hover {
  background: linear-gradient(135deg, #85ce61 0%, #95d475 100%);
}
</style>
