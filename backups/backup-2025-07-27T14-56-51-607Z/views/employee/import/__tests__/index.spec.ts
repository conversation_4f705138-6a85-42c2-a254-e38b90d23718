/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * 员工批量导入页面单元测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
  import { mount } from '@vue/test-utils'
  import { createPinia } from 'pinia'
  import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
  import EmployeeImport from '../index.vue';
  import employeeApi from '@/api/modules/employee'

// Mock API
vi.mock('@/api/modules/employee', () => ({
  default: {
    validateImportData: vi.fn(),
    executeImport: vi.fn(),
    getImportFailedData: vi.fn()
  }
}))

// Mock 路由
const mockPush = vi.fn()
const mockBack = vi.fn()

vi.mock('vue-router', () => ({
  useRouter: () => ({
    push: mockPush,
    back: mockBack
  })
}))

// Mock ElMessage 和 ElMessageBox
vi.mock('element-plus', async () => {
  const actual = await vi.importActual('element-plus')
  return {
    ...actual,
    ElMessage: {
      success: vi.fn(),
      error: vi.fn(),
      warning: vi.fn()
    },
    ElMessageBox: {
      confirm: vi.fn().mockResolvedValue(true)
    },
    ElLoading: {
      service: vi.fn().mockReturnValue({ close: vi.fn() })
    }
  }
})

// Mock XLSX
vi.mock('xlsx', () => ({
  utils: {
    json_to_sheet: vi.fn().mockReturnValue({}),
    book_new: vi.fn().mockReturnValue({}),
    book_append_sheet: vi.fn()
  },
  writeFile: vi.fn()
}))

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn().mockReturnValue('test-token'),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
},
  Object.defineProperty(window, 'localStorage', { value: localStorageMock })

describe('EmployeeImport', () => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let wrapper: any
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let pinia: any
  
  const mockValidationResult = {
    total: 100,
    success: 95,
    failed: 3,
    duplicate: 2,
    errors: [
      {
        row: 2,
        field: '工号',
        value: '',
        message: '工号不能为空',
        suggestion: '请填写工号'
  },
      {
        row: 5,
        field: '身份证号',
        value: '123456',
        message: '身份证号格式错误',
        suggestion: '请填写正确的18位身份证号'
      }
    ],
    previewData: [
      {
        employeeNumber: '2024001',
        fullName: '张三',
        gender: '男',
        idNumber: '330100199001010001',
        institutionName: '信息技术部',
        positionName: '前端工程师',
        phoneNumber: '13800138000',
        email: '<EMAIL>'
      }
    ]
  },
  const mockImportResult = {
    batchId: 'BATCH-20240110-001',
    successCount: 95,
    failedCount: 0,
    allSuccess: true,
    importTime: '2024-01-10 10:00:00',
    operator: '管理员',
    duration: 5.2
  },
  beforeEach(() => {
    // 创建 Pinia 实例
    pinia = createPinia()
    
    // 重置 mock
    vi.clearAllMocks()
    
    // Mock API 响应
    vi.mocked(employeeApi.validateImportData).mockResolvedValue({
      code: 200,
      message: 'success',
      data: mockValidationResult
    })
    
    vi.mocked(employeeApi.executeImport).mockResolvedValue({
      code: 200,
      message: 'success',
      data: mockImportResult
    })
  })
  
  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })
  
  it('应该正确渲染组件', async () => {
    wrapper = mount(EmployeeImport, {
      global: {
        plugins: [pinia],
        stubs: {
          ElPageHeader: true,
          ElCard: true,
          ElSteps: true,
          ElStep: true,
          ElButton: true,
          ElIcon: true,
          ElUpload: true,
          ElForm: true,
          ElFormItem: true,
          ElRadioGroup: true,
          ElRadio: true,
          ElTable: true,
          ElTableColumn: true,
          ElRow: true,
          ElCol: true,
          ElStatistic: true,
          ElAlert: true,
          ElProgress: true,
          ElResult: true,
          ElDescriptions: true,
          ElDescriptionsItem: true,
          ElInput: true,
          ImportHistoryDialog: true,
          Clock: true,
          Download: true,
          UploadFilled: true,
          Document: true
        }
      }
    })
    
    await wrapper.vm.$nextTick()
    
    expect(wrapper.find('.employee-import').exists()).toBe(true)
    expect(wrapper.find('.steps-card').exists()).toBe(true)
    expect(wrapper.find('.content-card').exists()).toBe(true)
  })
  
  it('应该显示正确的步骤', async () => {
    wrapper = mount(EmployeeImport, {
      global: {
        plugins: [pinia],
        stubs: {
          ElPageHeader: true,
          ElCard: true,
          ElSteps: true,
          ElStep: true,
          ElButton: true,
          ElIcon: true,
          ElUpload: true,
          ElForm: true,
          ElFormItem: true,
          ElRadioGroup: true,
          ElRadio: true,
          ElTable: true,
          ElTableColumn: true,
          ElRow: true,
          ElCol: true,
          ElStatistic: true,
          ElAlert: true,
          ElProgress: true,
          ElResult: true,
          ElDescriptions: true,
          ElDescriptionsItem: true,
          ElInput: true,
          ImportHistoryDialog: true,
          Clock: true,
          Download: true,
          UploadFilled: true,
          Document: true
        }
      }
    })
    
    await wrapper.vm.$nextTick()
    
    expect(wrapper.vm.activeStep).toBe(0)
    expect(wrapper.find('.template-download').exists()).toBe(true)
  })
  
  it('应该能够下载模板', async () => {
    const XLSX = await import('xlsx')
    
    wrapper = mount(EmployeeImport, {
      global: {
        plugins: [pinia],
        stubs: {
          ElPageHeader: true,
          ElCard: true,
          ElSteps: true,
          ElStep: true,
          ElButton: true,
          ElIcon: true,
          ElUpload: true,
          ElForm: true,
          ElFormItem: true,
          ElRadioGroup: true,
          ElRadio: true,
          ElTable: true,
          ElTableColumn: true,
          ElRow: true,
          ElCol: true,
          ElStatistic: true,
          ElAlert: true,
          ElProgress: true,
          ElResult: true,
          ElDescriptions: true,
          ElDescriptionsItem: true,
          ElInput: true,
          ImportHistoryDialog: true,
          Clock: true,
          Download: true,
          UploadFilled: true,
          Document: true
        }
      }
    })
    
    await wrapper.vm.$nextTick()
    
    await wrapper.vm.handleDownloadTemplate()
    
    expect(ElLoading.service).toHaveBeenCalled()
    expect(XLSX.utils.json_to_sheet).toHaveBeenCalled()
    expect(XLSX.utils.book_new).toHaveBeenCalled()
    expect(XLSX.utils.book_append_sheet).toHaveBeenCalled()
    expect(XLSX.writeFile).toHaveBeenCalledWith(expect.any(Object), '员工信息导入模板.xlsx')
    expect(ElMessage.success).toHaveBeenCalledWith('模板下载成功')
  })
  
  it('应该验证文件类型', async () => {
    wrapper = mount(EmployeeImport, {
      global: {
        plugins: [pinia],
        stubs: {
          ElPageHeader: true,
          ElCard: true,
          ElSteps: true,
          ElStep: true,
          ElButton: true,
          ElIcon: true,
          ElUpload: true,
          ElForm: true,
          ElFormItem: true,
          ElRadioGroup: true,
          ElRadio: true,
          ElTable: true,
          ElTableColumn: true,
          ElRow: true,
          ElCol: true,
          ElStatistic: true,
          ElAlert: true,
          ElProgress: true,
          ElResult: true,
          ElDescriptions: true,
          ElDescriptionsItem: true,
          ElInput: true,
          ImportHistoryDialog: true,
          Clock: true,
          Download: true,
          UploadFilled: true,
          Document: true
        }
      }
    })
    
    await wrapper.vm.$nextTick()
    
    const invalidFile = new File([''], 'test.txt', { type: 'text/plain' })
    const result = wrapper.vm.beforeUpload(invalidFile)
    
    expect(result).toBe(false)
    expect(ElMessage.error).toHaveBeenCalledWith('只能上传 Excel 文件!')
  })
  
  it('应该验证文件大小', async () => {
    wrapper = mount(EmployeeImport, {
      global: {
        plugins: [pinia],
        stubs: {
          ElPageHeader: true,
          ElCard: true,
          ElSteps: true,
          ElStep: true,
          ElButton: true,
          ElIcon: true,
          ElUpload: true,
          ElForm: true,
          ElFormItem: true,
          ElRadioGroup: true,
          ElRadio: true,
          ElTable: true,
          ElTableColumn: true,
          ElRow: true,
          ElCol: true,
          ElStatistic: true,
          ElAlert: true,
          ElProgress: true,
          ElResult: true,
          ElDescriptions: true,
          ElDescriptionsItem: true,
          ElInput: true,
          ImportHistoryDialog: true,
          Clock: true,
          Download: true,
          UploadFilled: true,
          Document: true
        }
      }
    })
    
    await wrapper.vm.$nextTick()
    
    const largeFile = new File(['x'.repeat(11 * 1024 * 1024)], 'test.xlsx', { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    })
    const result = wrapper.vm.beforeUpload(largeFile)
    
    expect(result).toBe(false)
    expect(ElMessage.error).toHaveBeenCalledWith('文件大小不能超过 10MB!')
  })
  
  it('应该处理文件上传成功', async () => {
    wrapper = mount(EmployeeImport, {
      global: {
        plugins: [pinia],
        stubs: {
          ElPageHeader: true,
          ElCard: true,
          ElSteps: true,
          ElStep: true,
          ElButton: true,
          ElIcon: true,
          ElUpload: true,
          ElForm: true,
          ElFormItem: true,
          ElRadioGroup: true,
          ElRadio: true,
          ElTable: true,
          ElTableColumn: true,
          ElRow: true,
          ElCol: true,
          ElStatistic: true,
          ElAlert: true,
          ElProgress: true,
          ElResult: true,
          ElDescriptions: true,
          ElDescriptionsItem: true,
          ElInput: true,
          ImportHistoryDialog: true,
          Clock: true,
          Download: true,
          UploadFilled: true,
          Document: true
        }
      }
    })
    
    await wrapper.vm.$nextTick()
    
    const response = {
      code: 200,
      data: {
        fileKey: 'FILE-KEY-123'
      }
    },
  const file = {
      name: 'test.xlsx',
      size: 1024
    },
  wrapper.vm.handleUploadSuccess(response, file)
    
    expect(wrapper.vm.uploadedFile).toEqual({
      name: 'test.xlsx',
      size: 1024,
      key: 'FILE-KEY-123'
    })
    expect(ElMessage.success).toHaveBeenCalledWith('文件上传成功')
  })
  
  it('应该执行数据校验', async () => {
    wrapper = mount(EmployeeImport, {
      global: {
        plugins: [pinia],
        stubs: {
          ElPageHeader: true,
          ElCard: true,
          ElSteps: true,
          ElStep: true,
          ElButton: true,
          ElIcon: true,
          ElUpload: true,
          ElForm: true,
          ElFormItem: true,
          ElRadioGroup: true,
          ElRadio: true,
          ElTable: true,
          ElTableColumn: true,
          ElRow: true,
          ElCol: true,
          ElStatistic: true,
          ElAlert: true,
          ElProgress: true,
          ElResult: true,
          ElDescriptions: true,
          ElDescriptionsItem: true,
          ElInput: true,
          ImportHistoryDialog: true,
          Clock: true,
          Download: true,
          UploadFilled: true,
          Document: true
        }
      }
    })
    
    await wrapper.vm.$nextTick()
    
    // 设置上传的文件
    wrapper.vm.uploadedFile = {
      name: 'test.xlsx',
      size: 1024,
      key: 'FILE-KEY-123'
  };
  await wrapper.vm.handleValidate()
    
    expect(employeeApi.validateImportData).toHaveBeenCalledWith({
      fileKey: 'FILE-KEY-123',
      mode: 'add'
    })
    
    expect(wrapper.vm.validationResult).toEqual(mockValidationResult)
    expect(wrapper.vm.previewData).toEqual(mockValidationResult.previewData.slice(0, 10))
    expect(wrapper.vm.activeStep).toBe(1)
  })
  
  it('应该执行导入操作', async () => {
    wrapper = mount(EmployeeImport, {
      global: {
        plugins: [pinia],
        stubs: {
          ElPageHeader: true,
          ElCard: true,
          ElSteps: true,
          ElStep: true,
          ElButton: true,
          ElIcon: true,
          ElUpload: true,
          ElForm: true,
          ElFormItem: true,
          ElRadioGroup: true,
          ElRadio: true,
          ElTable: true,
          ElTableColumn: true,
          ElRow: true,
          ElCol: true,
          ElStatistic: true,
          ElAlert: true,
          ElProgress: true,
          ElResult: true,
          ElDescriptions: true,
          ElDescriptionsItem: true,
          ElInput: true,
          ImportHistoryDialog: true,
          Clock: true,
          Download: true,
          UploadFilled: true,
          Document: true
        }
      }
    })
    
    await wrapper.vm.$nextTick()
    
    // 设置必要的数据
    wrapper.vm.uploadedFile = {
      name: 'test.xlsx',
      size: 1024,
      key: 'FILE-KEY-123'
  };
  wrapper.vm.validationResult = mockValidationResult
    wrapper.vm.importForm.reason = '批量导入新员工',
  await wrapper.vm.handleImport()
    
    expect(ElMessageBox.confirm).toHaveBeenCalled()
    expect(employeeApi.executeImport).toHaveBeenCalledWith({
      fileKey: 'FILE-KEY-123',
      mode: 'add',
      conflictStrategy: 'skip',
      reason: '批量导入新员工'
    })
    
    // 等待异步操作完成
    await new Promise(resolve => setTimeout(resolve, 600))
    
    expect(wrapper.vm.importResult).toEqual(mockImportResult)
  })
  
  it('应该处理返回操作', async () => {
    wrapper = mount(EmployeeImport, {
      global: {
        plugins: [pinia],
        stubs: {
          ElPageHeader: true,
          ElCard: true,
          ElSteps: true,
          ElStep: true,
          ElButton: true,
          ElIcon: true,
          ElUpload: true,
          ElForm: true,
          ElFormItem: true,
          ElRadioGroup: true,
          ElRadio: true,
          ElTable: true,
          ElTableColumn: true,
          ElRow: true,
          ElCol: true,
          ElStatistic: true,
          ElAlert: true,
          ElProgress: true,
          ElResult: true,
          ElDescriptions: true,
          ElDescriptionsItem: true,
          ElInput: true,
          ImportHistoryDialog: true,
          Clock: true,
          Download: true,
          UploadFilled: true,
          Document: true
        }
      }
    })
    
    await wrapper.vm.$nextTick()
    
    wrapper.vm.handleBack()
    
    expect(mockBack).toHaveBeenCalled()
  })
  
  it('应该导出错误数据', async () => {
    const XLSX = await import('xlsx')
    
    wrapper = mount(EmployeeImport, {
      global: {
        plugins: [pinia],
        stubs: {
          ElPageHeader: true,
          ElCard: true,
          ElSteps: true,
          ElStep: true,
          ElButton: true,
          ElIcon: true,
          ElUpload: true,
          ElForm: true,
          ElFormItem: true,
          ElRadioGroup: true,
          ElRadio: true,
          ElTable: true,
          ElTableColumn: true,
          ElRow: true,
          ElCol: true,
          ElStatistic: true,
          ElAlert: true,
          ElProgress: true,
          ElResult: true,
          ElDescriptions: true,
          ElDescriptionsItem: true,
          ElInput: true,
          ImportHistoryDialog: true,
          Clock: true,
          Download: true,
          UploadFilled: true,
          Document: true
        }
      }
    })
    
    await wrapper.vm.$nextTick()
    
    wrapper.vm.validationResult = mockValidationResult
    
    await wrapper.vm.handleExportErrors()
    
    expect(ElLoading.service).toHaveBeenCalled()
    expect(XLSX.utils.json_to_sheet).toHaveBeenCalled()
    expect(XLSX.writeFile).toHaveBeenCalledWith(expect.any(Object), '导入错误数据.xlsx')
    expect(ElMessage.success).toHaveBeenCalledWith('错误数据导出成功')
  })
  
  it('应该完成导入并跳转', async () => {
    wrapper = mount(EmployeeImport, {
      global: {
        plugins: [pinia],
        stubs: {
          ElPageHeader: true,
          ElCard: true,
          ElSteps: true,
          ElStep: true,
          ElButton: true,
          ElIcon: true,
          ElUpload: true,
          ElForm: true,
          ElFormItem: true,
          ElRadioGroup: true,
          ElRadio: true,
          ElTable: true,
          ElTableColumn: true,
          ElRow: true,
          ElCol: true,
          ElStatistic: true,
          ElAlert: true,
          ElProgress: true,
          ElResult: true,
          ElDescriptions: true,
          ElDescriptionsItem: true,
          ElInput: true,
          ImportHistoryDialog: true,
          Clock: true,
          Download: true,
          UploadFilled: true,
          Document: true
        }
      }
    })
    
    await wrapper.vm.$nextTick()
    
    wrapper.vm.handleFinish()
    
    expect(mockPush).toHaveBeenCalledWith('/employee/list')
  })
})