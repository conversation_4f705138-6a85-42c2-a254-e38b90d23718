<template>
  <el-dialog
    v-model="visible"
    title="导入历史"
    width="80%"
    :before-close="handleClose"
  >
    <!-- 查询条件 -->
    <el-form :model="queryForm" inline>
      <el-form-item label="时间范围">
        <el-date-picker
          v-model="queryForm.dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
         />
      </el-form-item>
      <el-form-item label="操作人">
        <el-input v-model="queryForm.operator" placeholder="请输入操作人" clearable   />
      </el-form-item>
      <el-form-item label="导入状态">
        <el-select v-model="queryForm.status" placeholder="请选择状态" clearable>
          <el-option label="全部" value=""  />
          <el-option label="成功" value="success"  />
          <el-option label="部分成功" value="partial"  />
          <el-option label="失败" value="failed"  />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 历史列表 -->
    <el-table 
      v-loading="loading"
      :data="tableData" 
      style="width: 100%"
      @sort-change="handleSortChange"
    >
      <el-table-column prop="batchId" label="批次号" width="150"  />
      <el-table-column prop="importTime" label="导入时间" width="180" sortable="custom">
        <template #default="{ row }">
          {{ formatDateTime(row.importTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="fileName" label="文件名" min-width="200" show-overflow-tooltip  />
      <el-table-column prop="operator" label="操作人" width="100"  />
      <el-table-column prop="mode" label="导入模式" width="100">
        <template #default="{ row }">
          <el-tag :type="getModeTagType(row.mode)">
            {{ getModeText(row.mode) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="totalCount" label="总条数" width="80" align="right"  />
      <el-table-column prop="successCount" label="成功" width="80" align="right">
        <template #default="{ row }">
          <span class="text-success">{{ row.successCount }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="failedCount" label="失败" width="80" align="right">
        <template #default="{ row }">
          <span class="text-danger">{{ row.failedCount }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusTagType(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button link type="primary" @click="handleViewDetail(row)">
            详情
          </el-button>
          <el-button 
            v-if="row.failedCount > 0" 
            link 
            type="primary" 
            @click="handleDownloadFailed(row)"
          >
            下载失败数据
          </el-button>
          <el-button 
            v-if="row.status === 'failed' || row.status === 'partial'" 
            link 
            type="primary" 
            @click="handleRerun(row)"
          >
            重新导入
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
     />

    <!-- 详情对话框 -->
    <el-dialog
      v-model="showDetail"
      title="导入详情"
      width="70%"
      append-to-body
    >
      <el-descriptions v-if="currentDetail" :column="2" border>
        <el-descriptions-item label="批次号">
          {{ currentDetail.batchId }}
        </el-descriptions-item>
        <el-descriptions-item label="导入时间">
          {{ formatDateTime(currentDetail.importTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="文件名">
          {{ currentDetail.fileName }}
        </el-descriptions-item>
        <el-descriptions-item label="文件大小">
          {{ formatFileSize(currentDetail.fileSize) }}
        </el-descriptions-item>
        <el-descriptions-item label="操作人">
          {{ currentDetail.operator }}
        </el-descriptions-item>
        <el-descriptions-item label="导入模式">
          {{ getModeText(currentDetail.mode) }}
        </el-descriptions-item>
        <el-descriptions-item label="总条数">
          {{ currentDetail.totalCount }}
        </el-descriptions-item>
        <el-descriptions-item label="成功条数">
          <span class="text-success">{{ currentDetail.successCount }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="失败条数">
          <span class="text-danger">{{ currentDetail.failedCount }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="处理时长">
          {{ currentDetail.duration }}秒
        </el-descriptions-item>
        <el-descriptions-item label="导入原因" :span="2">
          {{ currentDetail.reason || '-' }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 错误详情 -->
      <div v-if="currentDetail?.errors?.length > 0" class="error-section">
        <h4>错误详情（显示前20条）</h4>
        <el-table :data="currentDetail.errors.slice(0, 20)" max-height="300">
          <el-table-column prop="row" label="行号" width="80"  />
          <el-table-column prop="employeeNumber" label="工号" width="100"  />
          <el-table-column prop="fullName" label="姓名" width="100"  />
          <el-table-column prop="errorMessage" label="错误信息" min-width="200"  />
        </el-table>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed } from 'vue'
import employeeApi from '@/api/modules/employee'
import { formatDateTime, formatFileSize } from '@/utils/format'
// Props & Emits
interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  (e: 'rerun', batch: unknown): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (val: boolean) => emit('update:modelValue', val)
})

// 查询表单
const queryForm = reactive({
  dateRange: null as unknown,
  operator: '',
  status: ''
})

// 表格数据
const loading = ref(false)
const tableData = ref<unknown[]>([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 排序信息
const sortProp = ref('importTime')
const sortOrder = ref('descending')

// 详情相关
const showDetail = ref(false)
const currentDetail = ref<unknown>(null)

// 获取列表数据
  try {
    loading.value = true
    
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const params: unknown = {
      page: currentPage.value,
      pageSize: pageSize.value,
      sortField: sortProp.value,
      sortOrder: sortOrder.value === 'ascending' ? 'asc' : 'desc'
    }
    
    // 处理查询条件
    if (queryForm.dateRange) {
      params.startTime = queryForm.dateRange[0]
      params.endTime = queryForm.dateRange[1]
    }
    
    if (queryForm.operator) {
      params.operator = queryForm.operator
    }
    
    if (queryForm.status) {
      params.status = queryForm.status
    }
    
    const {data: _data} =  await employeeApi.getImportHistory(params)
    
    tableData.value 
}

.text-danger {
  color: var(--el-color-danger);
}

.error-section {
  margin-top: 20px;
  
  h4 {
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: 500;
  }
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-pagination) {
  margin-top: 20px;
  justify-content: flex-end;
}
</style>