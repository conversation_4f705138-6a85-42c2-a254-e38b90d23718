<template>
  <div class="employee-import">
    <!-- 页面头部 -->
    <el-page-header @back="handleBack">
      <template #content>
        <span class="text-lg font-600">批量导入员工</span>
      </template>
      <template #extra>
        <el-button @click="showHistory = true">
          <el-icon><Clock /></el-icon>
          导入历史
        </el-button>
      </template>
    </el-page-header>

    <!-- 导入步骤 -->
    <el-card class="steps-card">
      <el-steps :active="activeStep" align-center finish-status="success">
        <el-step title="下载模板" description="下载标准Excel模板"  />
        <el-step title="上传文件" description="选择填写好的文件"  />
        <el-step title="数据校验" description="检查数据格式和内容"  />
        <el-step title="预览确认" description="确认导入数据"  />
        <el-step title="导入结果" description="查看导入结果"  />
      </el-steps>
    </el-card>

    <!-- 步骤内容 -->
    <el-card class="content-card">
      <!-- 步骤1：下载模板 -->
      <div v-if="activeStep === 0" class="step-content">
        <div class="template-download">
          <el-icon class="download-icon"><Download /></el-icon>
          <h3>下载导入模板</h3>
          <p>请下载标准Excel模板，按照模板格式填写员工信息</p>
          <el-button type="primary" size="large" @click="handleDownloadTemplate">
            <el-icon><Download /></el-icon>
            下载Excel模板
          </el-button>
          
          <div class="template-tips">
            <h4>模板填写说明：</h4>
            <ul>
              <li>工号、姓名、证件号码为必填项</li>
              <li>日期格式：YYYY-MM-DD（如：2024-01-01）</li>
              <li>性别：男/女</li>
              <li>人员类型：在编/编外/兼职</li>
              <li>请勿修改模板的列顺序和表头名称</li>
              <li>单次导入建议不超过1000条数据</li>
            </ul>
          </div>
        </div>
        
        <div class="step-actions">
          <el-button type="primary" @click="activeStep++">
            下一步
          </el-button>
        </div>
      </div>

      <!-- 步骤2：上传文件 -->
      <div v-if="activeStep === 1" class="step-content">
        <div class="file-upload">
          <el-upload
            ref="uploadRef"
            class="upload-area"
            drag
            :action="uploadUrl"
            :headers="uploadHeaders"
            :before-upload="beforeUpload"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :show-file-list="false"
            accept=".xlsx,.xls"
          >
            <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                仅支持 .xlsx 和 .xls 格式的Excel文件，文件大小不超过10MB
              </div>
            </template>
          </el-upload>

          <div v-if="uploadedFile" class="uploaded-file">
            <el-card>
              <div class="file-info">
                <el-icon class="file-icon"><Document /></el-icon>
                <div class="file-details">
                  <p class="file-name">{{ uploadedFile.name }}</p>
                  <p class="file-size">{{ formatFileSize(uploadedFile.size) }}</p>
                </div>
                <el-button text type="danger" @click="handleRemoveFile">
                  删除
                </el-button>
              </div>
            </el-card>
          </div>

          <div class="import-options">
            <el-form :model="importOptions" label-width="100px">
              <el-form-item label="导入模式">
                <el-radio-group v-model="importOptions.mode">
                  <el-radio value="add">
                    <span>新增模式</span>
                    <span class="radio-desc">仅导入新数据，已存在的数据将被跳过</span>
                  </el-radio>
                  <el-radio value="update">
                    <span>更新模式</span>
                    <span class="radio-desc">仅更新已存在的数据</span>
                  </el-radio>
                  <el-radio value="replace">
                    <span>替换模式</span>
                    <span class="radio-desc">根据工号更新现有数据</span>
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
          </div>
        </div>
        
        <div class="step-actions">
          <el-button @click="activeStep--">上一步</el-button>
          <el-button 
            type="primary" 
            @click="handleValidate"
            :disabled="!uploadedFile"
            :loading="validating"
          >
            开始校验
          </el-button>
        </div>
      </div>

      <!-- 步骤3：数据校验 -->
      <div v-if="activeStep === 2" class="step-content">
        <div class="validation-result" v-loading="validating">
          <div v-if="!validating && validationResult">
            <!-- 校验概览 -->
            <div class="validation-summary">
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-statistic title="总数据条数" :value="validationResult.total"  />
                </el-col>
                <el-col :span="6">
                  <el-statistic 
                    title="校验成功" 
                    :value="validationResult.success"
                    :value-style="{ color: '#67c23a' }"
                   />
                </el-col>
                <el-col :span="6">
                  <el-statistic 
                    title="校验失败" 
                    :value="validationResult.failed"
                    :value-style="{ color: '#f56c6c' }"
                   />
                </el-col>
                <el-col :span="6">
                  <el-statistic 
                    title="重复数据" 
                    :value="validationResult.duplicate"
                    :value-style="{ color: '#e6a23c' }"
                   />
                </el-col>
              </el-row>
            </div>

            <!-- 错误详情 -->
            <div v-if="validationResult.errors.length > 0" class="error-details">
              <h4>错误详情</h4>
              <el-table 
                :data="validationResult.errors" 
                style="width: 100%"
                max-height="300"
              >
                <el-table-column prop="row" label="行号" width="80"  />
                <el-table-column prop="field" label="字段" width="120"  />
                <el-table-column prop="value" label="错误值" width="150"  />
                <el-table-column prop="message" label="错误描述" min-width="200"  />
                <el-table-column label="建议" min-width="150">
                  <template #default="{ row }">
                    <span class="error-suggestion">{{ row.suggestion }}</span>
                  </template>
                </el-table-column>
              </el-table>
              
              <div class="error-actions">
                <el-button @click="handleExportErrors">
                  <el-icon><Download /></el-icon>
                  导出错误数据
                </el-button>
              </div>
            </div>

            <!-- 冲突处理 -->
            <div v-if="validationResult.duplicate > 0" class="conflict-handling">
              <h4>数据冲突处理</h4>
              <el-radio-group v-model="conflictStrategy">
                <el-radio value="skip">跳过重复数据</el-radio>
                <el-radio value="update">更新已存在的数据</el-radio>
                <el-radio value="merge">合并数据（保留原有非空字段）</el-radio>
              </el-radio-group>
            </div>
          </div>
        </div>
        
        <div class="step-actions">
          <el-button @click="handleReupload">重新上传</el-button>
          <el-button 
            type="primary" 
            @click="activeStep++"
            :disabled="!validationResult || validationResult.success === 0"
          >
            下一步
          </el-button>
        </div>
      </div>

      <!-- 步骤4：预览确认 -->
      <div v-if="activeStep === 3" class="step-content">
        <div class="preview-data">
          <h4>数据预览（显示前10条）</h4>
          <el-table 
            :data="previewData" 
            style="width: 100%"
            max-height="400"
          >
            <el-table-column prop="employeeNumber" label="工号" width="100"  />
            <el-table-column prop="fullName" label="姓名" width="100"  />
            <el-table-column prop="gender" label="性别" width="60"  />
            <el-table-column prop="idNumber" label="证件号码" width="180"  />
            <el-table-column prop="institutionName" label="所属机构" width="150"  />
            <el-table-column prop="positionName" label="岗位" width="120"  />
            <el-table-column prop="phoneNumber" label="联系电话" width="120"  />
            <el-table-column prop="email" label="邮箱" min-width="150"  />
          </el-table>

          <div class="import-summary">
            <el-alert 
              :title="`即将导入 ${validationResult.success} 条数据`"
              type="warning"
              show-icon
              :closable="false"
            >
              <template v-if="importOptions.mode === 'update'">
                将更新已存在的员工信息
              </template>
              <template v-else-if="importOptions.mode === 'replace'">
                将根据工号替换现有数据
              </template>
              <template v-else>
                将新增员工信息，跳过已存在的数据
              </template>
            </el-alert>
          </div>

          <!-- 导入原因 -->
          <div class="import-reason">
            <el-form :model="importForm" label-width="100px">
              <el-form-item label="导入原因" required>
                <el-input
                  v-model="importForm.reason"
                  type="textarea"
                  :rows="3"
                  placeholder="请说明本次批量导入的原因"
                  maxlength="200"
                  show-word-limit
                  />
              </el-form-item>
            </el-form>
          </div>
        </div>
        
        <div class="step-actions">
          <el-button @click="activeStep--">上一步</el-button>
          <el-button 
            type="primary" 
            @click="handleImport"
            :disabled="!importForm.reason"
            :loading="importing"
          >
            确认导入
          </el-button>
        </div>
      </div>

      <!-- 步骤5：导入结果 -->
      <div v-if="activeStep === 4" class="step-content">
        <div class="import-result">
          <div v-if="importing" class="importing-progress">
            <el-progress 
              :percentage="importProgress" 
              :stroke-width="20"
              :format="formatProgress"
             />
            <p class="progress-text">正在导入数据，请稍候...</p>
          </div>

          <div v-else-if="importResult" class="result-display">
            <el-result
              :icon="importResult.allSuccess ? 'success' : 'warning'"
              :title="importResult.allSuccess ? '导入成功' : '部分导入成功'"
            >
              <template #sub-title>
                <div class="result-summary">
                  成功导入 {{ importResult.successCount }} 条，
                  失败 {{ importResult.failedCount }} 条
                </div>
              </template>
              <template #extra>
                <el-button type="primary" @click="handleFinish">完成</el-button>
                <el-button v-if="importResult.failedCount > 0" @click="handleViewFailedData">
                  查看失败数据
                </el-button>
              </template>
            </el-result>

            <!-- 导入详情 -->
            <div class="import-details">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="批次号">
                  {{ importResult.batchId }}
                </el-descriptions-item>
                <el-descriptions-item label="导入时间">
                  {{ formatDateTime(importResult.importTime) }}
                </el-descriptions-item>
                <el-descriptions-item label="导入模式">
                  {{ getImportModeText(importOptions.mode) }}
                </el-descriptions-item>
                <el-descriptions-item label="操作人">
                  {{ importResult.operator }}
                </el-descriptions-item>
                <el-descriptions-item label="文件名">
                  {{ uploadedFile.name }}
                </el-descriptions-item>
                <el-descriptions-item label="处理时长">
                  {{ importResult.duration }}秒
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 导入历史对话框 -->
    <ImportHistoryDialog
      v-model="showHistory"
      @rerun="handleRerunImport"
    />
    
    <!-- 失败数据对话框 -->
    <el-dialog
      v-model="showFailedDataDialog"
      title="导入失败数据详情"
      width="80%"
      top="5vh"
    >
      <el-table
        :data="failedData"
        style="width: 100%"
        max-height="500"
      >
        <el-table-column type="index" label="序号" width="60"  />
        <el-table-column prop="row" label="行号" width="80"  />
        <el-table-column prop="employeeNumber" label="工号" width="100"  />
        <el-table-column prop="fullName" label="姓名" width="100"  />
        <el-table-column prop="error" label="错误原因" min-width="200">
          <template #default="{ row }">
            <el-tag type="danger" effect="plain">{{ row.error }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="data" label="原始数据" min-width="300">
          <template #default="{ row }">
            <el-text truncated>{{ JSON.stringify(row.data) }}</el-text>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="downloadFailedData" type="primary">
            <el-icon><Download /></el-icon>
            导出失败数据
          </el-button>
          <el-button @click="showFailedDataDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
 
defineOptions({
  name: 'ImportView'
})

import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { 
  Clock, 
  Download, 
  UploadFilled, 
  Document 
} from '@element-plus/icons-vue'
import ImportHistoryDialog from './components/ImportHistoryDialog.vue'
import employeeApi from '@/api/modules/employee'
import { formatFileSize, formatDateTime } from '@/utils/format'
import * as XLSX from 'xlsx'

// 路由实例
const router = useRouter()

// 当前步骤
const activeStep = ref(0)

// 上传相关
const uploadRef = ref<unknown>()
const uploadUrl = '/api/employee/import/parse'
const uploadHeaders = {
  Authorization: `Bearer ${localStorage.getItem('token')}`
}
const uploadedFile = ref<unknown>(null)

// 导入选项
const importOptions = reactive({
  mode: 'add' // add: 新增, update: 更新, replace: 替换
})

// 校验相关
const validating = ref(false)
const validationResult = ref<unknown>(null)

// 冲突策略
const conflictStrategy = ref('skip')

// 预览数据
const previewData = ref<unknown[]>([])

// 导入表单
const importForm = reactive({
  reason: ''
})

// 导入相关
const importing = ref(false)
const importProgress = ref(0)
const importResult = ref<unknown>(null)

// 是否显示历史
const showHistory = ref(false)

// 失败数据对话框
const showFailedDataDialog = ref(false)
const failedData = ref<unknown[]>([])

// 返回
const handleBack = () => {
  router.back()
}

// 下载模板
const handleDownloadTemplate = async () => {
  try {
    const loading = ElLoading.service({
      text: '正在下载模板...'
    })
    
    // 创建模板数据
    const templateData = [
      {
        '工号': '2024001',
        '姓名': '张三',
        '性别': '男',
        '出生年月': '1990-01-01',
        '民族': '汉族',
        '政治面貌': '党员',
        '籍贯': '浙江杭州',
        '证件类型': '身份证',
        '证件号码': '330100199001010001',
        '参加工作时间': '2020-01-01',
        '入职日期': '2020-01-01',
        '所属机构': '信息技术部',
        '当前岗位': '前端工程师',
        '职称': '中级',
        '学历': '本科',
        '学位': '学士',
        '联系电话': '13800138000',
        '邮箱': '<EMAIL>',
        '人员类型': '在编',
        '人员状态': '在职'
      }
    ]
    
    // 创建工作簿
    const ws = XLSX.utils.json_to_sheet(templateData)
    const wb = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, ws, '员工信息')
    
    // 设置列宽
    const colWidths = [
      { wch: 10 }, // 工号
      { wch: 10 }, // 姓名
      { wch: 6 },  // 性别
      { wch: 12 }, // 出生年月
      { wch: 8 },  // 民族
      { wch: 10 }, // 政治面貌
      { wch: 15 }, // 籍贯
      { wch: 10 }, // 证件类型
      { wch: 20 }, // 证件号码
      { wch: 15 }, // 参加工作时间
      { wch: 12 }, // 入职日期
      { wch: 15 }, // 所属机构
      { wch: 15 }, // 当前岗位
      { wch: 8 },  // 职称
      { wch: 8 },  // 学历
      { wch: 8 },  // 学位
      { wch: 15 }, // 联系电话
      { wch: 20 }, // 邮箱
      { wch: 10 }, // 人员类型
      { wch: 10 }  // 人员状态
    ]
    ws['!cols'] = colWidths
    
    // 下载文件
    XLSX.writeFile(wb, '员工信息导入模板.xlsx')
    
    loading.close()
    ElMessage.success('模板下载成功')
  } catch (__error) {
    ElMessage.error('下载模板失败')
  }
}

// 文件上传前验证
const beforeUpload = (file: File) => {
  const isExcel = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'].includes(file.type)
  const isLt10M = file.size / 1024 / 1024 < 10
  
  if (!isExcel) {
    ElMessage.error('只能上传 Excel 文件!')
    return false
  }
  
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!')
    return false
  }
  
  return true
}

// 上传成功
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleUploadSuccess = (response: unknown, file: unknown) => {
  if (response.code === 200) {
    uploadedFile.value = {
      name: file.name,
      size: file.size,
      key: response.data.fileKey
    }
    ElMessage.success('文件上传成功')
  } else {
    ElMessage.error(response.message || '文件上传失败')
  }
}

// 上传失败
const handleUploadError = () => {
  ElMessage.error('文件上传失败，请重试')
}

// 删除文件
const handleRemoveFile = () => {
  uploadedFile.value = null
}

// 校验数据
const handleValidate = async () => {
  try {
    validating.value = true
    
    const {data} =  await employeeApi.validateImportData({
      fileKey: uploadedFile.value.key,
      mode: importOptions.mode
    })
    
    validationResult.value 
  
  .steps-card {
    margin-top: 20px;
    
    :deep(.el-card__body) {
      padding: 30px 20px;
    }
  }
  
  .content-card {
    margin-top: 20px;
    min-height: 500px;
    
    .step-content {
      padding: 30px;
      
      .step-actions {
        margin-top: 30px;
        text-align: center;
      }
    }
  }
  
  // 步骤1：下载模板
  .template-download {
    text-align: center;
    padding: 40px;
    
    .download-icon {
      font-size: 48px;
      color: var(--el-color-primary);
      margin-bottom: 20px;
    }
    
    h3 {
      margin-bottom: 10px;
    }
    
    p {
      color: var(--el-text-color-secondary);
      margin-bottom: 30px;
    }
    
    .template-tips {
      margin-top: 40px;
      text-align: left;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
      
      h4 {
        margin-bottom: 15px;
      }
      
      ul {
        list-style: disc;
        padding-left: 20px;
        
        li {
          margin-bottom: 8px;
          color: var(--el-text-color-secondary);
        }
      }
    }
  }
  
  // 步骤2：上传文件
  .file-upload {
    .upload-area {
      margin-bottom: 30px;
      
      :deep(.el-upload-dragger) {
        padding: 40px;
      }
    }
    
    .uploaded-file {
      margin-bottom: 30px;
      
      .file-info {
        display: flex;
        align-items: center;
        gap: 15px;
        
        .file-icon {
          font-size: 32px;
          color: var(--el-color-primary);
        }
        
        .file-details {
          flex: 1;
          
          .file-name {
            font-weight: 500;
            margin-bottom: 5px;
          }
          
          .file-size {
            font-size: 12px;
            color: var(--el-text-color-secondary);
          }
        }
      }
    }
    
    .import-options {
      .radio-desc {
        display: block;
        font-size: 12px;
        color: var(--el-text-color-secondary);
        margin-left: 22px;
        margin-top: 5px;
      }
    }
  }
  
  // 步骤3：数据校验
  .validation-result {
    min-height: 400px;
    
    .validation-summary {
      margin-bottom: 30px;
    }
    
    .error-details {
      margin-top: 30px;
      
      h4 {
        margin-bottom: 15px;
      }
      
      .error-suggestion {
        color: var(--el-color-primary);
        font-size: 12px;
      }
      
      .error-actions {
        margin-top: 15px;
      }
    }
    
    .conflict-handling {
      margin-top: 30px;
      
      h4 {
        margin-bottom: 15px;
      }
    }
  }
  
  // 步骤4：预览确认
  .preview-data {
    h4 {
      margin-bottom: 15px;
    }
    
    .import-summary {
      margin: 30px 0;
    }
    
    .import-reason {
      margin-top: 30px;
    }
  }
  
  // 步骤5：导入结果
  .import-result {
    min-height: 400px;
    
    .importing-progress {
      text-align: center;
      padding: 80px;
      
      .progress-text {
        margin-top: 20px;
        color: var(--el-text-color-secondary);
      }
    }
    
    .result-display {
      .result-summary {
        font-size: 16px;
      }
      
      .import-details {
        margin-top: 40px;
        max-width: 800px;
        margin-left: auto;
        margin-right: auto;
      }
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .employee-import {
    padding: 10px;
    
    .steps-card {
      :deep(.el-step__title) {
        font-size: 12px;
      }
      
      :deep(.el-step__description) {
        display: none;
      }
    }
  }
}
</style>