<template>
  <div class="modern-employee-management" :class="{ 'mobile-layout': isMobile }">
    <!-- 现代化页面头部 -->
    <div class="page-header" role="banner">
      <div class="header-content">
        <div class="header-title">
          <h1>员工管理</h1>
          <p class="subtitle">管理员工信息、档案和组织关系</p>
        </div>

        <!-- 快速操作按钮 -->
        <div class="header-actions" v-if="!isMobile">
          <el-button type="primary" size="large" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增员工
          </el-button>

          <el-dropdown trigger="click" placement="bottom-end">
            <el-button size="large">
              <el-icon><MoreFilled /></el-icon>
              更多操作
            </el-button>

            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="handleExport">
                  <el-icon><Download /></el-icon>
                  导出数据
                </el-dropdown-item>
                <el-dropdown-item @click="handleImport">
                  <el-icon><Upload /></el-icon>
                  批量导入
                </el-dropdown-item>
                <el-dropdown-item @click="handleBatchEdit" :disabled="selectedRows.length === 0">
                  <el-icon><Edit /></el-icon>
                  批量编辑
                </el-dropdown-item>
                <el-dropdown-item @click="showAdvancedSearch = true">
                  <el-icon><Filter /></el-icon>
                  高级筛选
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>

        <!-- 移动端操作按钮 -->
        <div class="mobile-actions" v-if="isMobile">
          <el-button type="primary" circle @click="handleAdd">
            <el-icon><Plus /></el-icon>
          </el-button>
          <el-button circle @click="showMobileMenu = true">
            <el-icon><MoreFilled /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 智能搜索栏 -->
    <div class="search-section">
      <div class="search-container">
        <div class="search-input-wrapper">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索员工姓名、编号、手机号或邮箱..."
            size="large"
            clearable
            @keyup.enter="handleSearch"
            @clear="handleSearch"
            class="smart-search-input"
          >
            <template #prefix>
              <el-icon class="search-icon"><Search /></el-icon>
            </template>
            <template #suffix>
              <el-button
                type="primary"
                text
                @click="showAdvancedSearch = true"
                class="advanced-search-btn"
              >
                高级筛选
              </el-button>
            </template>
          </el-input>
        </div>

        <!-- 快速筛选标签 -->
        <div class="quick-filters" v-if="!isMobile">
          <el-button-group class="filter-group">
            <el-button
              v-for="filter in quickFilters"
              :key="filter.key"
              :type="activeQuickFilter === filter.key ? 'primary' : 'default'"
              size="small"
              @click="applyQuickFilter(filter.key)"
            >
              {{ filter.label }}
            </el-button>
          </el-button-group>
        </div>
      </div>
    </div>

    <!-- 高级筛选抽屉 -->
    <el-drawer
      v-model="showAdvancedSearch"
      title="高级筛选"
      :size="isMobile ? '100%' : '400px'"
      direction="rtl"
    >
      <div class="advanced-search-content">
        <el-form :model="searchForm" label-position="top" size="default">
          <el-form-item label="在职状态">
            <el-select v-model="searchForm.employmentStatus" placeholder="选择在职状态" clearable>
              <el-option
                v-for="item in employeeOptions.employmentStatus"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-select>
          </el-form-item>

          <el-form-item label="员工类型">
            <el-select v-model="searchForm.employeeType" placeholder="选择员工类型" clearable>
              <el-option
                v-for="item in employeeOptions.employeeType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-select>
          </el-form-item>

          <el-form-item label="所属组织">
            <el-select v-model="searchForm.organizationId" placeholder="选择所属组织" clearable>
              <el-option label="全部组织" value=""  />
              <el-option
                v-for="org in organizationList"
                :key="org.id"
                :label="org.name"
                :value="org.id"
               />
            </el-select>
          </el-form-item>

          <el-form-item label="入职时间范围">
            <el-date-picker
              v-model="hireDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
             />
          </el-form-item>

          <el-form-item label="年龄范围">
            <el-slider
              v-model="ageRange"
              range
              :min="18"
              :max="65"
              :step="1"
              show-stops
              show-input
             />
          </el-form-item>
        </el-form>

        <div class="search-actions">
          <el-button @click="resetAdvancedSearch">重置</el-button>
          <el-button type="primary" @click="applyAdvancedSearch">应用筛选</el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 现代化统计概览 -->
    <div class="stats-overview">
      <div class="stats-grid" :class="{ 'mobile-grid': isMobile }">
        <div
          v-for="stat in statsCards"
          :key="stat.id"
          class="modern-stats-card"
          :class="[`stats-${stat.type}`, { 'clickable': stat.clickable }]"
          @click="stat.clickable && handleStatsClick(stat.id)"
          role="button"
          :tabindex="stat.clickable ? 0 : -1"
          :aria-label="`${stat.title}: ${stat.value}${stat.unit || ''}`"
          @keydown.enter="stat.clickable && handleStatsClick(stat.id)"
          @keydown.space.prevent="stat.clickable && handleStatsClick(stat.id)"
        >
          <div class="stats-header">
            <div class="stats-icon" :style="{ background: stat.gradient }">
              <el-icon :size="24">
                <component :is="stat.icon" />
              </el-icon>
            </div>
            <div class="stats-trend" v-if="stat.trend !== undefined">
              <el-icon :class="stat.trend >= 0 ? 'trend-up' : 'trend-down'">
                <component :is="stat.trend >= 0 ? ArrowUp : ArrowDown" />
              </el-icon>
              <span class="trend-value">{{ Math.abs(stat.trend) }}%</span>
            </div>
          </div>

          <div class="stats-content">
            <div class="stats-value">
              <span class="stats-number">{{ formatNumber(stat.value) }}</span>
              <span class="stats-unit" v-if="stat.unit">{{ stat.unit }}</span>
            </div>
            <div class="stats-title">{{ stat.title }}</div>
            <div class="stats-subtitle" v-if="stat.subtitle">{{ stat.subtitle }}</div>
          </div>

          <div class="stats-progress" v-if="(stat as unknown).progress !== undefined">
            <div class="progress-bar">
              <div
                class="progress-fill"
                :style="{
                  width: `${(stat as unknown).progress}%`,
                  background: stat.gradient
                }"
              ></div>
            </div>
            <span class="progress-text">{{ (stat as unknown).progress }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 员工列表区域 -->
    <div class="employee-list-section">
      <!-- 列表头部控制栏 -->
      <div class="list-header">
        <div class="list-title">
          <h3>员工列表</h3>
          <span class="list-count">共 {{ pagination.total }} 名员工</span>
        </div>

        <div class="list-controls">
          <!-- 视图切换 -->
          <div class="view-toggle" v-if="!isMobile">
            <el-button-group>
              <el-button
                :type="viewMode === 'table' ? 'primary' : 'default'"
                @click="viewMode = 'table'"
                size="small"
              >
                <el-icon><Grid /></el-icon>
                表格
              </el-button>
              <el-button
                :type="viewMode === 'card' ? 'primary' : 'default'"
                @click="viewMode = 'card'"
                size="small"
              >
                <el-icon><List /></el-icon>
                卡片
              </el-button>
            </el-button-group>
          </div>

          <!-- 排序控制 -->
          <el-select v-model="sortBy" placeholder="排序方式" size="small" style="width: 120px">
            <el-option label="入职时间" value="hireDate"  />
            <el-option label="姓名" value="name"  />
            <el-option label="年龄" value="age"  />
            <el-option label="工龄" value="workYears"  />
          </el-select>

          <!-- 批量操作 -->
          <el-dropdown v-if="selectedRows.length > 0" trigger="click">
            <el-button type="primary" size="small">
              批量操作 ({{ selectedRows.length }})
              <el-icon><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="handleBatchEdit">批量编辑</el-dropdown-item>
                <el-dropdown-item @click="handleBatchExport">批量导出</el-dropdown-item>
                <el-dropdown-item @click="handleBatchDelete" divided>批量删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <!-- 表格视图 -->
      <div v-if="viewMode === 'table'" class="table-view" @contextmenu="handleTableContextMenu">
        <el-table
          v-loading="loading"
          :data="tableData"
          stripe
          style="width: 100%"
          @selection-change="handleSelectionChange"
          @row-contextmenu="handleRowContextMenu"
          class="modern-table"
        >
          <el-table-column type="selection" width="55"  />
          <el-table-column prop="employeeCode" label="员工编号" width="120"  />
          <el-table-column prop="name" label="姓名" width="100"  />
          <el-table-column prop="genderName" label="性别" width="60"  />
          <el-table-column prop="age" label="年龄" width="60"  />
          <el-table-column prop="phone" label="手机号" width="120"  />
          <el-table-column prop="email" label="邮箱" width="180" show-overflow-tooltip  />
          <el-table-column prop="organizationName" label="所属组织" width="150" show-overflow-tooltip  />
          <el-table-column prop="positionName" label="岗位" width="120" show-overflow-tooltip  />
          <el-table-column prop="employeeTypeName" label="员工类型" width="100"  />
          <el-table-column prop="employmentStatusName" label="在职状态" width="100">
            <template #default="scope">
              <el-tag
                :type="getStatusTagType(scope.row.employmentStatus)"
                size="small"
              >
                {{ scope.row.employmentStatusName }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="hireDate" label="入职日期" width="100"  />
          <el-table-column prop="workYears" label="工龄" width="60">
            <template #default="scope">
              {{ scope.row.workYears }}年
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="scope">
              <el-button size="small" type="primary" link @click="handleView(scope.row)">
                查看
              </el-button>
              <el-button size="small" type="primary" link @click="handleEdit(scope.row)">
                编辑
              </el-button>
              <el-button size="small" type="danger" link @click="handleDelete(scope.row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 卡片视图 -->
      <div v-else class="card-view">
        <div class="employee-cards" :class="{ 'mobile-cards': isMobile }">
          <div
            v-for="employee in tableData"
            :key="employee.id"
            class="employee-card"
            @click="handleView(employee)"
            @contextmenu="handleCardContextMenu($event, employee)"
          >
            <div class="card-header">
              <div class="employee-avatar">
                <el-avatar :size="48" >
            <img v-lazy="employee.avatarUrl" style="width: 48px; height: 48px; object-fit: cover;" />
          </el-avatar>
                  {{ employee.name?.charAt(0) }}
                </el-avatar>
              </div>
              <div class="employee-basic">
                <h4 class="employee-name">{{ employee.name }}</h4>
                <p class="employee-code">{{ employee.employeeCode }}</p>
              </div>
              <div class="card-actions">
                <el-checkbox
                  :model-value="selectedRows.some(row => row.id === employee.id)"
                  @change="handleCardSelection(employee, $event)"
                  @click.stop
                />
              </div>
            </div>

            <div class="card-content">
              <div class="info-row">
                <span class="info-label">组织:</span>
                <span class="info-value">{{ employee.organizationName || '-' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">岗位:</span>
                <span class="info-value">{{ employee.positionName || '-' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">状态:</span>
                <el-tag
                  :type="getStatusTagType(employee.employmentStatus)"
                  size="small"
                >
                  {{ employee.employmentStatusName }}
                </el-tag>
              </div>
              <div class="info-row">
                <span class="info-label">入职:</span>
                <span class="info-value">{{ employee.hireDate }}</span>
              </div>
            </div>

            <div class="card-footer">
              <el-button size="small" type="primary" link @click.stop="handleEdit(employee)">
                编辑
              </el-button>
              <el-button size="small" type="danger" link @click.stop="handleDelete(employee)">
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页控制 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          :layout="isMobile ? 'prev, pager, next' : 'total, sizes, prev, pager, next, jumper'"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          class="modern-pagination"
         />
      </div>
    </div>

    <!-- 移动端操作菜单 -->
    <el-drawer
      v-model="showMobileMenu"
      title="操作菜单"
      direction="btt"
      size="auto"
    >
      <div class="mobile-menu-content">
        <div class="menu-item" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          <span>新增员工</span>
        </div>
        <div class="menu-item" @click="handleExport">
          <el-icon><Download /></el-icon>
          <span>导出数据</span>
        </div>
        <div class="menu-item" @click="handleImport">
          <el-icon><Upload /></el-icon>
          <span>批量导入</span>
        </div>
        <div class="menu-item" @click="showAdvancedSearch = true">
          <el-icon><Filter /></el-icon>
          <span>高级筛选</span>
        </div>
        <div class="menu-item" v-if="selectedRows.length > 0" @click="handleBatchEdit">
          <el-icon><Edit /></el-icon>
          <span>批量编辑 ({{ selectedRows.length }})</span>
        </div>
      </div>
    </el-drawer>

    <!-- 员工详情/编辑对话框 -->
    <EmployeeDialog
      v-model:visible="dialogVisible"
      :employee="currentEmployee"
      :mode="dialogMode"
      @success="handleDialogSuccess"
    />

    <!-- 批量编辑对话框 -->
    <el-dialog
      v-model="batchEditDialogVisible"
      title="批量编辑员工"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="batchEditForm" label-width="120px">
        <el-form-item label="所属组织">
          <el-select v-model="batchEditForm.organizationId" placeholder="选择新的组织" clearable>
            <el-option
              v-for="org in organizationList"
              :key="org.id"
              :label="org.name"
              :value="org.id"
             />
          </el-select>
        </el-form-item>
        <el-form-item label="员工类型">
          <el-select v-model="batchEditForm.employeeType" placeholder="选择员工类型" clearable>
            <el-option
              v-for="item in employeeOptions.employeeType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
             />
          </el-select>
        </el-form-item>
        <el-form-item label="在职状态">
          <el-select v-model="batchEditForm.employmentStatus" placeholder="选择在职状态" clearable>
            <el-option
              v-for="item in employeeOptions.employmentStatus"
              :key="item.value"
              :label="item.label"
              :value="item.value"
             />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="batchEditDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmBatchEdit" :loading="batchEditLoading">
          确认修改
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  User,
  UserFilled,
  Clock,
  Star,
  Download,
  Upload,
  MoreFilled,
  Edit,
  Filter,
  ArrowUp,
  ArrowDown,
  Grid,
  List
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { employeeApi, employeeOptions, type Employee, type EmployeeQueryRequest } from '@/api/employee'
import { useMobile } from '@/composables/useMobile'
import { useOrganizationStore } from '@/stores/modules/organization'
import EmployeeDialog from './components/EmployeeDialog.vue'

// 路由
const router = useRouter()

// 移动端适配
const {isMobile: _isMobile} =  useMobile()

// 组织架构store
const organizationStore = useOrganizationStore()

// 应用高级搜索
const applyAdvancedSearch = () => {
  if (hireDateRange.value && Array.isArray(hireDateRange.value) && hireDateRange.value.length === 2) {
    (searchForm as unknown).startDate = hireDateRange.value[0] ? '' + hireDateRange.value[0] : '';
    (searchForm as unknown).endDate = hireDateRange.value[1] ? '' + hireDateRange.value[1] : '';
  }

  showAdvancedSearch.value = false
  pagination.page = 1
  fetchEmployees()
}

// 重置高级搜索
const resetAdvancedSearch = () => {
  Object.assign(searchForm, {
    employmentStatus: undefined,
    employeeType: undefined,
    organizationId: undefined,
    startDate: undefined,
    endDate: undefined
  })
  hireDateRange.value = null
  ageRange.value = [18, 65]
}

// 统计卡片点击处理
const handleStatsClick = (statId: string) => {
  applyQuickFilter(statId)
}

// 卡片选择处理
const handleCardSelection = (employee: Employee, checked: boolean) => {
  if (checked) {
    if (!selectedRows.value.some(row => row.id === employee.id)) {
      selectedRows.value.push(employee)
    }
  } else {
    const index = selectedRows.value.findIndex(row => row.id === employee.id)
    if (index > -1) {
      selectedRows.value.splice(index, 1)
    }
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchEmployees()
}

// 重置搜索
  Object.assign(searchForm, {
    page: 0,
    size: 20,
    keyword: '',
    employmentStatus: undefined,
    employeeType: undefined,
    organizationId: undefined
  })
  pagination.page = 1
  fetchEmployees()
}

// 新增员工
const handleAdd = () => {
  currentEmployee.value = null
  dialogMode.value = 'add'
  dialogVisible.value = true
}

// 查看员工
const handleView = (employee: Employee) => {
  currentEmployee.value = employee
  dialogMode.value = 'view'
  dialogVisible.value = true
}

// 表格行右键菜单
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleRowContextMenu = (row: Employee, column: unknown, event: MouseEvent) => {
  event.preventDefault()
  
  const contextMenuHandler = createRowContextMenu({
    row,
    onView: handleView,
    onEdit: handleEdit,
    onDelete: handleDelete,
    customItems: [
      {
        id: 'copy-info',
        label: '复制员工信息',
        icon: 'CopyDocument',
        handler: () => {
          const info = `${row.name} - ${row.employeeCode} - ${row.phone}`
          navigator.clipboard.writeText(info)
          ElMessage.success('员工信息已复制到剪贴板')
        }
      },
      {
        id: 'view-detail',
        label: '查看详细档案',
        icon: 'Document',
        handler: () => {
          // 跳转到员工档案详情页
          router.push(`/employee/profile/${row.id}`)
        }
      },
      {
        type: 'divider'
      },
      {
        id: 'print',
        label: '打印员工卡',
        icon: 'Printer',
        handler: () => {
          ElMessage.info('打印功能开发中...')
        }
      }
    ]
  })
  
  contextMenuHandler(event)
}

// 卡片右键菜单
const handleCardContextMenu = (event: MouseEvent, employee: Employee) => {
  event.preventDefault()
  
  const contextMenuHandler = createRowContextMenu({
    row: employee,
    onView: handleView,
    onEdit: handleEdit,
    onDelete: handleDelete,
    customItems: [
      {
        id: 'copy-info',
        label: '复制员工信息',
        icon: 'CopyDocument',
        handler: () => {
          const info = `${employee.name} - ${employee.employeeCode} - ${employee.phone}`
          navigator.clipboard.writeText(info)
          ElMessage.success('员工信息已复制到剪贴板')
        }
      }
    ]
  })
  
  contextMenuHandler(event)
}

// 表格区域右键菜单（用于批量操作）
const handleTableContextMenu = (event: MouseEvent) => {
  // 如果点击在表格行上，则不处理（由行右键菜单处理）
  const target = event.target as HTMLElement
  if (target.closest('tr')) return
  
  // 如果没有选中的行，不显示菜单
  if (selectedRows.value.length === 0) {
    event.preventDefault()
    ElMessage.info('请先选择要操作的员工')
    return
  }
  
  event.preventDefault()
  
  const contextMenuHandler = createSelectionContextMenu({
    selection: selectedRows.value,
    onBatchDelete: handleBatchDelete,
    onBatchExport: () => handleBatchExport(),
    customItems: [
      {
        id: 'batch-edit',
        label: '批量编辑',
        icon: 'Edit',
        handler: () => handleBatchEdit()
      },
      {
        id: 'batch-print',
        label: '批量打印员工卡',
        icon: 'Printer',
        handler: () => {
          ElMessage.info(`批量打印 ${selectedRows.value.length} 张员工卡功能开发中...`)
        }
      },
      {
        type: 'divider'
      },
      {
        id: 'batch-status',
        label: '批量更新状态',
        icon: 'Refresh',
        children: [
          {
            id: 'set-active',
            label: '设为在职',
            handler: () => {
              ElMessage.info('批量更新状态功能开发中...')
            }
          },
          {
            id: 'set-resigned',
            label: '设为离职',
            handler: () => {
              ElMessage.info('批量更新状态功能开发中...')
            }
          }
        ]
      }
    ]
  })
  
  contextMenuHandler(event)
}

// 批量删除
const handleBatchDelete = async (selection?: Employee[]) => {
  // 如果没有传入selection参数，使用selectedRows.value
  const employeesToDelete = selection || selectedRows.value
  
  if (employeesToDelete.length === 0) {
    ElMessage.warning('请先选择要删除的员工')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${employeesToDelete.length} 名员工吗？此操作不可恢复！`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    loading.value = true
    
    // 逐个删除员工
    let successCount = 0
    let failCount = 0
    
    for (const employee of employeesToDelete) {
      try {
        await employeeApi.deleteEmployee(employee.id!)
        successCount++
      } catch (__error) {
        failCount++
      }
    }
    
    if (failCount > 0) {
      ElMessage.warning(`成功删除 ${successCount} 名员工，失败 ${failCount} 个`)
    } else {
      ElMessage.success(`成功删除 ${successCount} 名员工`)
    }
    
    selectedRows.value = []
    await fetchEmployees()
    await fetchStats()
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  } finally {
    loading.value = false
  }
}

// 编辑员工
const handleEdit = (employee: Employee) => {
  currentEmployee.value = employee
  dialogMode.value = 'edit'
  dialogVisible.value = true
}

// 删除员工
const handleDelete = async (employee: Employee) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除员工 "${employee.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await employeeApi.deleteEmployee(employee.id!)
    ElMessage.success('删除成功')
    fetchEmployees()
    fetchStats()
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 表格选择变化
const handleSelectionChange = (selection: Employee[]) => {
  selectedRows.value = selection
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchEmployees()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchEmployees()
}

// 对话框成功回调
const handleDialogSuccess = () => {
  fetchEmployees()
  fetchStats()
}

// 导出
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 导入
const handleImport = () => {
  ElMessage.info('导入功能开发中...')
}

// 批量编辑
const handleBatchEdit = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要编辑的员工')
    return
  }
  batchEditDialogVisible.value = true
}

// 批量导出
const handleBatchExport = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要导出的员工')
    return
  }
  ElMessage.info('批量导出功能开发中...')
}

// 确认批量编辑
const confirmBatchEdit = async () => {
  if (!batchEditForm.organizationId && !batchEditForm.employeeType && !batchEditForm.employmentStatus) {
    ElMessage.warning('请至少选择一个要修改的字段')
    return
  }

  batchEditLoading.value = true

  try {
    // 实现批量编辑API调用
    const ids = selectedRows.value.map(row => row.id!).filter(id => id)
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const updateData: unknown = {}
    
    // 只添加有值的字段
    if (batchEditForm.organizationId) {
      updateData.organizationId = batchEditForm.organizationId
    }
    if (batchEditForm.employeeType) {
      updateData.employeeType = batchEditForm.employeeType
    }
    if (batchEditForm.employmentStatus) {
      updateData.employmentStatus = batchEditForm.employmentStatus
    }
    
    // 逐个更新员工信息
    let successCount = 0
    let failCount = 0
    
    for (const id of ids) {
      try {
        await employeeApi.updateEmployee(id, updateData)
        successCount++
      } catch (__error) {
        failCount++
      }
    }
    
    if (failCount > 0) {
      ElMessage.warning(`成功修改 ${successCount} 名员工信息，失败 ${failCount} 个`)
    } else {
      ElMessage.success(`成功修改 ${successCount} 名员工信息`)
    }
    
    batchEditDialogVisible.value = false
    selectedRows.value = []

    // 重置表单
    Object.assign(batchEditForm, {
      organizationId: undefined,
      employeeType: undefined,
      employmentStatus: undefined
    })

    fetchEmployees()
  } catch (__error) {
    ElMessage.error('批量编辑失败')
  } finally {
    batchEditLoading.value = false
  }
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  switch (status) {
    case 'ACTIVE':
      return 'success'
    case 'PROBATION':
      return 'warning'
    case 'LEAVE_WITHOUT_PAY':
      return 'info'
    case 'TERMINATED':
    case 'RETIRED':
      return 'danger'
    default:
      return ''
  }
}

// 获取组织列表
const fetchOrganizations = async () => {
  try {
    await organizationStore.fetchOrganizations()
    // 将组织树扁平化为列表
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const flattenOrgs = (orgs: unknown[], result: unknown[] = []) => {
      orgs.forEach(org => {
        result.push(org)
        if (org.children && org.children.length > 0) {
          flattenOrgs(org.children, result)
        }
      })
      return result
    }
    organizationList.value = flattenOrgs(organizationStore.organizationTree)
  } catch (__error) {
    }
}

// 初始化
onMounted(() => {
  fetchEmployees()
  fetchStats()
  fetchOrganizations()
})
</script>

<style scoped>
/* 现代化员工管理样式 */
.modern-employee-management {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 24px;
}

.mobile-layout {
  padding: 16px;
}

/* 页面头部 */
.page-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title h1 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  margin: 0;
  color: #7f8c8d;
  font-size: 16px;
  font-weight: 400;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.mobile-actions {
  display: flex;
  gap: 8px;
}

/* 搜索区域 */
.search-section {
  margin-bottom: 24px;
}

.search-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.search-input-wrapper {
  margin-bottom: 16px;
}

.smart-search-input {
  border-radius: 12px;
}

.smart-search-input :deep(.el-input__wrapper) {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
  transition: all 0.3s ease;
}

.smart-search-input :deep(.el-input__wrapper:hover) {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.search-icon {
  color: #667eea;
}

.advanced-search-btn {
  color: #667eea;
  font-weight: 500;
}

.quick-filters {
  display: flex;
  gap: 12px;
  align-items: center;
}

.filter-group {
  display: flex;
  gap: 8px;
}

/* 高级搜索 */
.advanced-search-content {
  padding: 20px 0;
}

.search-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

/* 统计概览 */
.stats-overview {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.mobile-grid {
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 16px;
}

.modern-stats-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.modern-stats-card.clickable {
  cursor: pointer;
}

.modern-stats-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.stats-icon {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.stats-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
}

.trend-up {
  color: #27ae60;
}

.trend-down {
  color: #e74c3c;
}

.trend-value {
  color: inherit;
}

.stats-content {
  margin-bottom: 16px;
}

.stats-value {
  display: flex;
  align-items: baseline;
  gap: 4px;
  margin-bottom: 8px;
}

.stats-number {
  font-size: 36px;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
}

.stats-unit {
  font-size: 16px;
  color: #7f8c8d;
  font-weight: 500;
}

.stats-title {
  font-size: 16px;
  font-weight: 600;
  color: #34495e;
  margin-bottom: 4px;
}

.stats-subtitle {
  font-size: 12px;
  color: #95a5a6;
}

.stats-progress {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: #ecf0f1;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  font-weight: 600;
  color: #7f8c8d;
}

/* 员工列表区域 */
.employee-list-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.list-title h3 {
  margin: 0 0 4px 0;
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
}

.list-count {
  color: #7f8c8d;
  font-size: 14px;
}

.list-controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

.view-toggle {
  display: flex;
  gap: 8px;
}

/* 表格视图 */
.table-view {
  margin-bottom: 24px;
}

.modern-table {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.modern-table :deep(.el-table__header) {
  background: #f8fafc;
}

.modern-table :deep(.el-table__header th) {
  background: #f8fafc;
  color: #475569;
  font-weight: 600;
  border-bottom: 2px solid #e2e8f0;
}

.modern-table :deep(.el-table__row:hover) {
  background: #f1f5f9;
}

/* 卡片视图 */
.card-view {
  margin-bottom: 24px;
}

.employee-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
}

.mobile-cards {
  grid-template-columns: 1fr;
  gap: 16px;
}

.employee-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.employee-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.employee-avatar {
  flex-shrink: 0;
}

.employee-basic {
  flex: 1;
}

.employee-name {
  margin: 0 0 4px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
}

.employee-code {
  margin: 0;
  color: #7f8c8d;
  font-size: 12px;
}

.card-actions {
  flex-shrink: 0;
}

.card-content {
  margin-bottom: 16px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-label {
  color: #7f8c8d;
  font-weight: 500;
}

.info-value {
  color: #2c3e50;
  font-weight: 500;
}

.card-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding-top: 12px;
  border-top: 1px solid #ebeef5;
}

/* 分页 */
.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.modern-pagination :deep(.el-pagination) {
  background: transparent;
}

.modern-pagination :deep(.el-pager li) {
  border-radius: 8px;
  margin: 0 2px;
  transition: all 0.3s ease;
}

.modern-pagination :deep(.el-pager li:hover) {
  background: #667eea;
  color: white;
}

.modern-pagination :deep(.el-pager li.is-active) {
  background: #667eea;
  color: white;
}

/* 移动端菜单 */
.mobile-menu-content {
  padding: 20px 0;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  cursor: pointer;
  transition: background 0.3s ease;
  border-radius: 8px;
  margin: 0 20px 8px 20px;
}

.menu-item:hover {
  background: #f5f7fa;
}

.menu-item span {
  font-size: 16px;
  color: #2c3e50;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modern-employee-management {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .header-title {
    text-align: center;
  }

  .header-title h1 {
    font-size: 24px;
  }

  .list-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .list-controls {
    justify-content: space-between;
  }

  .employee-cards {
    grid-template-columns: 1fr;
  }

  .card-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .employee-basic {
    text-align: center;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .modern-employee-management {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  }

  .page-header,
  .search-container,
  .employee-list-section,
  .modern-stats-card,
  .employee-card {
    background: rgba(31, 31, 31, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .header-title h1,
  .stats-number,
  .stats-title,
  .employee-name,
  .info-value {
    color: #ffffff;
  }

  .subtitle,
  .stats-subtitle,
  .employee-code,
  .info-label {
    color: #a0a0a0;
  }
}
</style>
