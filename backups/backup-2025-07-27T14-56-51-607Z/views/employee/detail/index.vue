<template>
  <div class="employee-detail">
    <!-- 页面头部 -->
    <el-page-header @back="handleBack">
      <template #content>
        <div class="header-content">
          <span class="title">员工详情</span>
          <el-tag v-if="employee" :type="getStatusTagType(employee.personnelStatus)" size="small">
            {{ getStatusText(employee.personnelStatus) }}
          </el-tag>
        </div>
      </template>
      <template #extra>
        <el-button v-permission="'employee:edit'" @click="handleEdit">编辑</el-button>
        <el-button @click="handlePrint">打印</el-button>
        <el-button @click="handleExport">导出</el-button>
        <el-dropdown trigger="click" @command="handleCommand">
          <el-button>
            更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="history">查看变更历史</el-dropdown-item>
              <el-dropdown-item command="roster">生成花名册</el-dropdown-item>
              <el-dropdown-item command="appointment">生成干部任免表</el-dropdown-item>
              <el-dropdown-item v-permission="'employee:delete'" command="delete" divided>
                删除员工
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
    </el-page-header>

    <!-- 加载状态 -->
    <el-skeleton v-if="loading" :rows="10" animated class="detail-skeleton"  />

    <!-- 详情内容 -->
    <div v-else-if="employee" class="detail-content">
      <!-- 基本信息卡片 -->
      <el-card class="info-card">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <el-link v-if="employee.infoCompletenessPercentage < 100" type="warning" @click="handleCompleteInfo">
              信息完整度：{{ employee.infoCompletenessPercentage }}%
            </el-link>
          </div>
        </template>
        <div class="basic-info">
          <div class="photo-section">
            <el-avatar
              v-if="employee.photoUrl"
              v-lazy="employee.photoUrl"
              :size="120"
              shape="square"
              />
            <el-avatar
              v-else
              :size="120"
              shape="square"
            >
              <span class="avatar-text">{{ employee.fullName.charAt(0) }}</span>
            </el-avatar>
            <el-button
              v-if="canEdit"
              size="small"
              @click="handleUploadPhoto"
            >
              更换照片
            </el-button>
          </div>
          <div class="info-section">
            <el-descriptions :column="3" border>
              <el-descriptions-item label="姓名">{{ employee.fullName }}</el-descriptions-item>
              <el-descriptions-item label="工号">{{ employee.employeeNumber }}</el-descriptions-item>
              <el-descriptions-item label="性别">{{ employee.gender === 'male' ? '男' : '女' }}</el-descriptions-item>
              <el-descriptions-item label="出生日期">{{ employee.dateOfBirth }}</el-descriptions-item>
              <el-descriptions-item label="民族">{{ employee.ethnicity }}</el-descriptions-item>
              <el-descriptions-item label="政治面貌">{{ employee.politicalStatus }}</el-descriptions-item>
              <el-descriptions-item label="籍贯">{{ employee.nativePlace }}</el-descriptions-item>
              <el-descriptions-item label="出生地">{{ employee.placeOfBirth }}</el-descriptions-item>
              <el-descriptions-item label="证件类型">{{ getIDTypeText(employee.idType) }}</el-descriptions-item>
              <el-descriptions-item label="证件号码">
                {{ maskIDNumber(employee.idNumber) }}
              </el-descriptions-item>
              <el-descriptions-item label="联系电话">{{ employee.phoneNumber }}</el-descriptions-item>
              <el-descriptions-item label="邮箱">{{ employee.email }}</el-descriptions-item>
              <el-descriptions-item label="联系地址" :span="2">{{ employee.contactAddress }}</el-descriptions-item>
              <el-descriptions-item label="邮编">{{ employee.postalCode }}</el-descriptions-item>
              <el-descriptions-item label="参加工作时间">{{ employee.workStartDate }}</el-descriptions-item>
              <el-descriptions-item label="入职日期">{{ employee.hireDate }}</el-descriptions-item>
              <el-descriptions-item label="健康状况">{{ employee.healthStatus || '-' }}</el-descriptions-item>
              <el-descriptions-item label="婚姻状况">{{ employee.maritalStatus || '-' }}</el-descriptions-item>
              <el-descriptions-item label="身高">{{ employee.height ? `${employee.height}cm` : '-' }}</el-descriptions-item>
              <el-descriptions-item label="高层次人才">
                <el-tag v-if="employee.highLevelTalentCategory" type="danger" size="small">
                  {{ employee.highLevelTalentCategory }}类人才
                </el-tag>
                <span v-else>-</span>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
      </el-card>

      <!-- 信息子集Tab页 -->
      <el-card class="subset-card">
        <el-tabs v-model="activeTab" type="card">
          <!-- 学习经历 -->
          <el-tab-pane label="学习经历" name="education">
            <div class="tab-header">
              <span class="tab-title">学习经历</span>
              <el-button
                v-if="canEdit"
                size="small"
                type="primary"
                @click="handleAddEducation"
              >
                新增
              </el-button>
            </div>
            <el-timeline v-if="educationHistory.length > 0">
              <el-timeline-item
                v-for="item in educationHistory"
                :key="item.recordId"
                :timestamp="`${item.startDate} 至 ${item.endDate || '今'}`"
                placement="top"
              >
                <el-card shadow="hover">
                  <div class="timeline-content">
                    <div class="timeline-header">
                      <h4>{{ item.graduationSchool }}</h4>
                      <div v-if="canEdit" class="timeline-actions">
                        <el-button
                          type="primary"
                          size="small"
                          link
                          @click="handleEditEducation(item)"
                        >
                          编辑
                        </el-button>
                        <el-button
                          type="danger"
                          size="small"
                          link
                          @click="handleDeleteEducation(item)"
                        >
                          删除
                        </el-button>
                      </div>
                    </div>
                    <el-descriptions :column="2" size="small">
                      <el-descriptions-item label="学历/学位">{{ item.degree }}</el-descriptions-item>
                      <el-descriptions-item label="专业">{{ item.major }}</el-descriptions-item>
                      <el-descriptions-item label="学习形式">{{ item.studyForm || '-' }}</el-descriptions-item>
                      <el-descriptions-item label="毕业论文">{{ item.thesisTitle || '-' }}</el-descriptions-item>
                      <el-descriptions-item v-if="item.overseasCertAgency" label="认证机构">
                        {{ item.overseasCertAgency }}
                      </el-descriptions-item>
                      <el-descriptions-item v-if="item.overseasCertNumber" label="认证编号">
                        {{ item.overseasCertNumber }}
                      </el-descriptions-item>
                    </el-descriptions>
                    <div v-if="item.certificateDocUrl" class="cert-section">
                      <el-link type="primary" @click="handleViewCertificate(item.certificateDocUrl)">
                        查看学历证书
                      </el-link>
                    </div>
                  </div>
                </el-card>
              </el-timeline-item>
            </el-timeline>
            <el-empty v-else description="暂无学习经历"  />
          </el-tab-pane>

          <!-- 工作经历 -->
          <el-tab-pane label="工作经历" name="work">
            <div class="tab-header">
              <span class="tab-title">工作经历</span>
              <el-button
                v-if="canEdit"
                size="small"
                type="primary"
                @click="handleAddWork"
              >
                新增
              </el-button>
            </div>
            <el-table
              :data="workExperience"
              style="width: 100%"
              empty-text="暂无工作经历"
            >
              <el-table-column prop="startDate" label="开始时间" width="100"  />
              <el-table-column prop="endDate" label="结束时间" width="100">
                <template #default="{ row }">
                  {{ row.endDate || '至今' }}
                </template>
              </el-table-column>
              <el-table-column prop="companyName" label="工作单位" min-width="200" show-overflow-tooltip  />
              <el-table-column prop="positionHeld" label="职位" width="150"  />
              <el-table-column prop="salaryGrade" label="薪资等级" width="100">
                <template #default="{ row }">
                  {{ row.salaryGrade || '-' }}
                </template>
              </el-table-column>
              <el-table-column label="证明材料" width="150">
                <template #default="{ row }">
                  <el-space>
                    <el-link v-if="row.workProofUrl" type="primary" @click="handleViewFile(row.workProofUrl)">
                      工作证明
                    </el-link>
                    <el-link v-if="row.resignationProofUrl" type="primary" @click="handleViewFile(row.resignationProofUrl)">
                      离职证明
                    </el-link>
                  </el-space>
                </template>
              </el-table-column>
              <el-table-column v-if="canEdit" label="操作" width="120" fixed="right">
                <template #default="{ row }">
                  <el-button
                    type="primary"
                    size="small"
                    link
                    @click="handleEditWork(row)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    link
                    @click="handleDeleteWork(row)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>

          <!-- 家庭成员 -->
          <el-tab-pane label="家庭成员" name="family">
            <div class="tab-header">
              <span class="tab-title">家庭成员</span>
              <el-button
                v-if="canEdit"
                size="small"
                type="primary"
                @click="handleAddFamily"
              >
                新增
              </el-button>
            </div>
            <el-table
              :data="familyMembers"
              style="width: 100%"
              empty-text="暂无家庭成员信息"
            >
              <el-table-column prop="fullName" label="姓名" width="120"  />
              <el-table-column prop="relationship" label="关系" width="100"  />
              <el-table-column prop="dateOfBirth" label="出生日期" width="120"  />
              <el-table-column prop="politicalStatus" label="政治面貌" width="120"  />
              <el-table-column prop="workUnitAndPosition" label="工作单位及职务" min-width="200" show-overflow-tooltip  />
              <el-table-column v-if="canEdit" label="操作" width="120" fixed="right">
                <template #default="{ row }">
                  <el-button
                    type="primary"
                    size="small"
                    link
                    @click="handleEditFamily(row)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    link
                    @click="handleDeleteFamily(row)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>

          <!-- 合同信息 -->
          <el-tab-pane label="合同信息" name="contract">
            <div class="tab-header">
              <span class="tab-title">合同信息</span>
            </div>
            <el-descriptions v-if="contractInfo" :column="2" border>
              <el-descriptions-item label="合同编号">{{ contractInfo.contractNumber }}</el-descriptions-item>
              <el-descriptions-item label="合同类型">{{ contractInfo.contractType }}</el-descriptions-item>
              <el-descriptions-item label="岗位类别">{{ contractInfo.positionCategory }}</el-descriptions-item>
              <el-descriptions-item label="聘用单位">{{ contractInfo.employmentUnit }}</el-descriptions-item>
              <el-descriptions-item label="签订日期">{{ contractInfo.signDate }}</el-descriptions-item>
              <el-descriptions-item label="合同期限">
                {{ contractInfo.startDate }} 至 {{ contractInfo.endDate }}
              </el-descriptions-item>
              <el-descriptions-item label="合同状态">
                <el-tag :type="contractInfo.status === 'active' ? 'success' : 'info'">
                  {{ contractInfo.status === 'active' ? '生效中' : '已到期' }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
            <el-empty v-else description="暂无合同信息"  />
          </el-tab-pane>

          <!-- 年度考核 -->
          <el-tab-pane label="年度考核" name="assessment">
            <div class="tab-header">
              <span class="tab-title">年度考核记录</span>
            </div>
            <el-table
              :data="assessmentRecords"
              style="width: 100%"
              empty-text="暂无考核记录"
            >
              <el-table-column prop="year" label="年度" width="100"  />
              <el-table-column prop="result" label="考核结果" width="120">
                <template #default="{ row }">
                  <el-tag :type="getAssessmentTagType(row.result)">
                    {{ row.result }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="department" label="考核部门" width="150"  />
              <el-table-column prop="remark" label="备注" min-width="200" show-overflow-tooltip  />
            </el-table>
          </el-tab-pane>

          <!-- 奖惩信息 -->
          <el-tab-pane label="奖惩信息" name="rewards">
            <div class="tab-header">
              <span class="tab-title">奖惩记录</span>
              <el-button
                v-if="canEdit"
                size="small"
                type="primary"
                @click="handleAddReward"
              >
                新增
              </el-button>
            </div>
            <el-table
              :data="rewardsPunishments"
              style="width: 100%"
              empty-text="暂无奖惩记录"
            >
              <el-table-column prop="date" label="时间" width="120"  />
              <el-table-column prop="type" label="类型" width="80">
                <template #default="{ row }">
                  <el-tag :type="row.type === 'reward' ? 'success' : 'danger'">
                    {{ row.type === 'reward' ? '奖励' : '惩罚' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="名称" min-width="200" show-overflow-tooltip  />
              <el-table-column prop="unit" label="颁发单位" width="150"  />
              <el-table-column prop="level" label="等级" width="100"  />
              <el-table-column v-if="canEdit" label="操作" width="120" fixed="right">
                <template #default="{ row }">
                  <el-button
                    type="primary"
                    size="small"
                    link
                    @click="handleEditReward(row)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    link
                    @click="handleDeleteReward(row)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>

          <!-- 人才项目 -->
          <el-tab-pane label="人才项目" name="talent">
            <div class="tab-header">
              <span class="tab-title">人才项目与荣誉</span>
              <el-button
                v-if="canEdit"
                size="small"
                type="primary"
                @click="handleAddTalent"
              >
                新增
              </el-button>
            </div>
            <el-table
              :data="talentHonors"
              style="width: 100%"
              empty-text="暂无人才项目记录"
            >
              <el-table-column prop="acquireTime" label="获取时间" width="120"  />
              <el-table-column prop="name" label="项目/荣誉名称" min-width="200" show-overflow-tooltip  />
              <el-table-column prop="level" label="级别" width="120">
                <template #default="{ row }">
                  <el-tag>{{ row.level }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="issuingUnit" label="颁发单位" width="150"  />
              <el-table-column prop="ranking" label="排名" width="80">
                <template #default="{ row }">
                  {{ row.ranking || '-' }}
                </template>
              </el-table-column>
              <el-table-column v-if="canEdit" label="操作" width="120" fixed="right">
                <template #default="{ row }">
                  <el-button
                    type="primary"
                    size="small"
                    link
                    @click="handleEditTalent(row)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    link
                    @click="handleDeleteTalent(row)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>

          <!-- 证件扫描件 -->
          <el-tab-pane label="证件扫描件" name="certificates">
            <div class="tab-header">
              <span class="tab-title">证件扫描件</span>
              <el-space>
                <el-radio-group v-model="certificateViewMode" size="small">
                  <el-radio-button label="grid">网格</el-radio-button>
                  <el-radio-button label="list">列表</el-radio-button>
                </el-radio-group>
                <el-button
                  v-if="canEdit"
                  size="small"
                  type="primary"
                  @click="handleUploadCertificate"
                >
                  上传证件
                </el-button>
              </el-space>
            </div>
            <CertificateViewer
              :certificates="employeeCertificates"
              :view-mode="certificateViewMode"
              :downloadable="true"
              :deletable="canEdit"
              :uploadable="canEdit"
              @view="handleViewCertificate"
              @download="handleDownloadCertificate"
              @delete="handleDeleteCertificate"
              @upload="handleUploadCertificates"
            />
          </el-tab-pane>

          <!-- 员工画像 -->
          <el-tab-pane label="员工画像" name="portrait">
            <HrEmployeePortrait :employee-id="employeeId" />
          </el-tab-pane>

          <!-- 其他信息 -->
          <el-tab-pane label="其他信息" name="other">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="所属部门">{{ employee.institutionName }}</el-descriptions-item>
              <el-descriptions-item label="岗位">{{ employee.positionName }}</el-descriptions-item>
              <el-descriptions-item label="学历学位">{{ employee.educationDegree || '-' }}</el-descriptions-item>
              <el-descriptions-item label="职称">{{ employee.professionalTitle || '-' }}</el-descriptions-item>
              <el-descriptions-item label="教师资格证">{{ teacherQualification || '-' }}</el-descriptions-item>
              <el-descriptions-item label="职业资格证书">{{ vocationalQualifications || '-' }}</el-descriptions-item>
              <el-descriptions-item label="社会兼职" :span="2">{{ socialPartTimeJobs || '-' }}</el-descriptions-item>
              <el-descriptions-item label="创建时间">{{ employee.createdAt }}</el-descriptions-item>
              <el-descriptions-item label="最后更新">{{ employee.updatedAt }}</el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>

    <!-- 空状态 -->
    <el-empty v-else description="暂无员工信息"  />

    <!-- 编辑对话框 -->
    <component
      :is="currentDialog.component"
      v-model="currentDialog.visible"
      :data="currentDialog.data"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
 
defineOptions({
  name: 'DetailView'
})

import { ref, computed, onMounted, defineAsyncComponent } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'
import { usePermissionStore } from '@/stores/modules/permission'
import employeeApi from '@/api/modules/employee'
import type {
  EmployeeDetail,
  EducationHistory,
  WorkExperience,
  FamilyMember,
  PersonnelStatus
} from '@/types/employee'

// 异步加载编辑组件
const EducationDialog = defineAsyncComponent(() => import('./components/EducationDialog.vue'))
const WorkDialog = defineAsyncComponent(() => import('./components/WorkDialog.vue'))
const FamilyDialog = defineAsyncComponent(() => import('./components/FamilyDialog.vue'))
const HrEmployeePortrait = defineAsyncComponent(() => import('@/components/employee/HrEmployeePortrait.vue'))
const CertificateViewer = defineAsyncComponent(() => import('.vueHrCertificateViewervue'))

const route = useRoute()
const router = useRouter()
const permissionStore = usePermissionStore()

// 员工ID
const employeeId = computed(() => route.params.id as string)

// 权限判断
const canEdit = computed(() => permissionStore.hasPermission('employee:edit'))

// 状态
const loading = ref(false)
const activeTab = ref('education')
const certificateViewMode = ref<'grid' | 'list'>('grid')

// 数据
const employee = ref<EmployeeDetail | null>(null)
const educationHistory = ref<EducationHistory[]>([])
const workExperience = ref<WorkExperience[]>([])
const familyMembers = ref<FamilyMember[]>([])
const contractInfo = ref<unknown>(null)
const assessmentRecords = ref<unknown[]>([])
const rewardsPunishments = ref<unknown[]>([])
const talentHonors = ref<unknown[]>([])
const teacherQualification = ref('')
const vocationalQualifications = ref('')
const socialPartTimeJobs = ref('')
const employeeCertificates = ref<unknown[]>([])  // 员工证件扫描件

// 当前对话框
const currentDialog = ref<{
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  component: unknown
  visible: boolean
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: unknown
}>({
  component: null,
  visible: false,
  data: null
})

// 获取员工详情
  loading.value = true
  try {
    const res = await employeeApi.getDetail(employeeId.value)
    employee.value = res.data
    
    // 提取各子集数据
    educationHistory.value = res.data.educationHistory || []
    workExperience.value = res.data.workExperience || []
    familyMembers.value = res.data.familyMembers || []
    
    // 模拟证件数据（实际应从API获取）
    employeeCertificates.value = [
      {
        id: '1',
        url: '/api/employee/certificate/1/image',
        thumbnailUrl: '/api/employee/certificate/1/thumbnail',
        name: '身份证正面',
        type: 'idCard',
        size: 512000,
        uploadTime: '2024-01-15'
      },
      {
        id: '2',
        url: '/api/employee/certificate/2/image',
        thumbnailUrl: '/api/employee/certificate/2/thumbnail',
        name: '身份证反面',
        type: 'idCard',
        size: 498000,
        uploadTime: '2024-01-15'
      }
    ]
    
    // 获取其他信息
    contractInfo.value = res.data.contractInfo || {}
    assessmentRecords.value = res.data.assessmentRecords || []
    rewardsPunishments.value = res.data.rewardsPunishments || []
    talentHonors.value = res.data.talentHonors || []
  } catch (__error) {
    ElMessage.error('获取员工详情失败')
  } finally {
    loading.value = false
  }
}

// 返回
const handleBack = () => {
  router.back()
}

// 编辑
const handleEdit = () => {
  router.push(`/employee/edit/${employeeId.value}`)
}

// 打印
const handlePrint = () => {
  window.print()
}

// 导出
const handleExport = async () => {
  try {
    await employeeApi.export({
      ids: [employeeId.value],
      format: 'pdf',
      filename: `${employee.value?.fullName}_档案`
    })
    ElMessage.success('导出成功')
  } catch (__error) {
    ElMessage.error('导出失败')
  }
}

// 更多操作
const handleCommand = (command: string) => {
  switch (command) {
    case 'history':
      router.push(`/employee/history/${employeeId.value}`)
      break
    case 'roster':
      // 生成花名册
      handleGenerateRoster()
      break
    case 'appointment':
      // 生成干部任免表
      handleGenerateAppointment()
      break
    case 'delete':
      handleDelete()
      break
  }
}

// 删除员工
const handleDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除员工"${employee.value?.fullName}"吗？此操作不可恢复。`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await employeeApi.delete(employeeId.value)
    ElMessage.success('删除成功')
    router.push('/employee/list')
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 完善信息
const handleCompleteInfo = () => {
  router.push(`/employee/edit/${employeeId.value}`)
}

// 生成花名册
const handleGenerateRoster = async () => {
  try {
    const loading = ElLoading.service({
      lock: true,
      text: '正在生成花名册...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    
    // 调用API生成花名册
    const {data: _data} =  await employeeApi.generateRoster(employeeId.value)
    
    loading.close()
    
    // 下载生成的文件
    const link 

  .header-content {
    display: flex;
    align-items: center;
    gap: 10px;

    .title {
      font-size: 18px;
      font-weight: 500;
    }
  }

  .detail-skeleton {
    margin-top: 20px;
  }

  .detail-content {
    margin-top: 20px;
  }

  .info-card {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .basic-info {
      display: flex;
      gap: 30px;

      .photo-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;

        .avatar-text {
          font-size: 48px;
          font-weight: 500;
        }
      }

      .info-section {
        flex: 1;
      }
    }
  }

  .subset-card {
    .tab-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .tab-title {
        font-size: 16px;
        font-weight: 500;
      }
    }

    .timeline-content {
      .timeline-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;

        h4 {
          margin: 0;
          font-size: 16px;
        }

        .timeline-actions {
          display: flex;
          gap: 10px;
        }
      }

      .cert-section {
        margin-top: 10px;
      }
    }
  }
}

@media print {
  .el-page-header,
  .el-button,
  .timeline-actions,
  .el-table-column--selection {
    display: none !important;
  }
}
</style>