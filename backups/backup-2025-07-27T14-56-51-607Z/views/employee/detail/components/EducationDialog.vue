<template>
  <el-dialog
    :model-value="modelValue"
    :title="isEdit ? '编辑学习经历' : '新增学习经历'"
    width="600px"
    :close-on-click-modal="false"
    @update:model-value="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="学校名称" prop="graduationSchool">
        <el-input
          v-model="formData.graduationSchool"
          placeholder="请输入学校名称"
          maxlength="100"
          />
      </el-form-item>
      
      <el-form-item label="起止时间" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          :disabled-date="disabledDate"
          @change="handleDateChange"
         />
      </el-form-item>
      
      <el-form-item label="学历/学位" prop="degree">
        <el-select v-model="formData.degree" placeholder="请选择学历/学位">
          <el-option label="博士研究生" value="博士研究生"  />
          <el-option label="硕士研究生" value="硕士研究生"  />
          <el-option label="本科" value="本科"  />
          <el-option label="专科" value="专科"  />
          <el-option label="高中" value="高中"  />
          <el-option label="初中及以下" value="初中及以下"  />
        </el-select>
      </el-form-item>
      
      <el-form-item label="专业" prop="major">
        <el-input
          v-model="formData.major"
          placeholder="请输入专业名称"
          maxlength="50"
          />
      </el-form-item>
      
      <el-form-item label="学习形式" prop="studyForm">
        <el-select v-model="formData.studyForm" placeholder="请选择学习形式" clearable>
          <el-option label="全日制" value="全日制"  />
          <el-option label="在职" value="在职"  />
          <el-option label="函授" value="函授"  />
          <el-option label="自考" value="自考"  />
          <el-option label="网络教育" value="网络教育"  />
        </el-select>
      </el-form-item>
      
      <el-form-item label="核心课程" prop="coreCourses">
        <el-input
          v-model="formData.coreCourses"
          type="textarea"
          placeholder="请输入核心课程，多个课程用逗号分隔"
          :rows="3"
          maxlength="500"
          />
      </el-form-item>
      
      <el-form-item label="毕业论文" prop="thesisTitle">
        <el-input
          v-model="formData.thesisTitle"
          placeholder="请输入毕业论文题目"
          maxlength="200"
          />
      </el-form-item>
      
      <el-divider content-position="left">海外学历认证信息（选填）</el-divider>
      
      <el-form-item label="认证机构" prop="overseasCertAgency">
        <el-input
          v-model="formData.overseasCertAgency"
          placeholder="如：教育部留学服务中心"
          maxlength="100"
          />
      </el-form-item>
      
      <el-form-item label="认证编号" prop="overseasCertNumber">
        <el-input
          v-model="formData.overseasCertNumber"
          placeholder="请输入认证编号"
          maxlength="50"
          />
      </el-form-item>
      
      <el-form-item label="认证日期" prop="overseasCertDate">
        <el-date-picker
          v-model="formData.overseasCertDate"
          type="date"
          placeholder="选择认证日期"
          value-format="YYYY-MM-DD"
         />
      </el-form-item>
      
      <el-form-item label="学历证书" prop="certificateDocUrl">
        <HrFileUpload
          v-model="certificateFiles"
          :config="uploadConfig"
          :rules="uploadRules"
          :limit="1"
          tip="支持上传PDF、JPG、PNG格式，文件大小不超过10MB"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { HrFileUpload } from '@/components'
import { educationApi } from '@/api/modules/employee'
import type { EducationHistory } from '@/types/employee'

interface Props {
  modelValue: boolean
  data?: Partial<EducationHistory> & { employeeId: string }
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  data: () => ({} as unknown)
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'success': []
}>()

// 是否编辑模式
const isEdit = computed(() => !!props.data?.recordId)

// 表单实例
const formRef = ref<FormInstance>()

// 加载状态
const loading = ref(false)

// 表单数据
const formData = reactive<Partial<EducationHistory>>({
  graduationSchool: '',
  startDate: '',
  endDate: '',
  degree: '',
  major: '',
  studyForm: '',
  coreCourses: '',
  thesisTitle: '',
  overseasCertAgency: '',
  overseasCertNumber: '',
  overseasCertDate: '',
  certificateDocUrl: ''
})

// 日期范围
const dateRange = ref<[string, string] | null>(null)

// 文件列表
const certificateFiles = ref<unknown[]>([])

// 上传配置
const uploadConfig = {
  action: '/api/upload',
  headers: {
    Authorization: `Bearer ${localStorage.getItem('token')}`
  }
}

// 上传规则
const uploadRules = {
  accept: ['application/pdf', 'image/jpeg', 'image/png'],
  maxSize: 10 // 10MB
}

// 表单规则
const rules: FormRules = {
  graduationSchool: [
    { required: true, message: '请输入学校名称', trigger: 'blur' }
  ],
  dateRange: [
    { required: true, message: '请选择起止时间', trigger: 'change' }
  ],
  degree: [
    { required: true, message: '请选择学历/学位', trigger: 'change' }
  ],
  major: [
    { required: true, message: '请输入专业名称', trigger: 'blur' }
  ]
}

// 监听props变化
watch(() => props.data, (newData) => {
  if (newData) {
    Object.assign(formData, newData)
    if (newData.startDate && newData.endDate) {
      dateRange.value = [newData.startDate, newData.endDate]
    }
    if (newData.certificateDocUrl) {
      certificateFiles.value = [{
        name: 'HrHr学历证书',
        url: newData.certificateDocUrl
      }]
    }
  }
}, { immediate: true })

// 日期禁用
const disabledDate = (date: Date) => {
  return date > new Date()
}

// 日期变化
const handleDateChange = (value: [string, string] | null) => {
  if (value) {
    formData.startDate = value[0]
    formData.endDate = value[1]
  } else {
    formData.startDate = ''
    formData.endDate = ''
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:modelValue', false)
  formRef.value?.resetFields()
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    // 处理文件上传
    if (certificateFiles.value.length > 0) {
      formData.certificateDocUrl = certificateFiles.value[0].url
    }
    
    loading.value = true
    
    if (isEdit.value) {
      // 编辑
      await educationApi.update(formData.recordId!, formData)
      ElMessage.success('编辑成功')
    } else {
      // 新增
      await educationApi.create(props.data!.employeeId, formData)
      ElMessage.success('新增成功')
    }
    
    emit('success')
    handleClose()
  } catch (__error) {
    if (error !== 'cancel') {
      ElMessage.error('提交失败')
    }
  } finally {
    loading.value = false
  }
}
</script>