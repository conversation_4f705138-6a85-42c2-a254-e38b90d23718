/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * 员工新增页面单元测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
  import { mount } from '@vue/test-utils'
  import { createPinia } from 'pinia'
  import { ElMessage } from 'element-plus'
  import EmployeeAdd from '../index.vue';
  import employeeApi from '@/api/modules/employee'

// Mock API
vi.mock('@/api/modules/employee', () => ({
  default: {
    create: vi.fn(),
    generateEmployeeNumber: vi.fn()
  }
}))

// Mock 路由
const mockPush = vi.fn()
const mockBack = vi.fn()

vi.mock('vue-router', () => ({
  useRouter: () => ({
    push: mockPush,
    back: mockBack
  })
}))

// Mock ElMessage 和 ElMessageBox
vi.mock('element-plus', async () => {
  const actual = await vi.importActual('element-plus')
  return {
    ...actual,
    ElMessage: {
      success: vi.fn(),
      error: vi.fn(),
      warning: vi.fn(),
      info: vi.fn()
    },
    ElMessageBox: {
      confirm: vi.fn().mockResolvedValue(true)
    }
  }
})

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
},
  Object.defineProperty(window, 'localStorage', { value: localStorageMock })

describe('EmployeeAdd', () => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let wrapper: any
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let pinia: any
  
  beforeEach(() => {
    // 创建 Pinia 实例
    pinia = createPinia()
    
    // 重置 mock
    vi.clearAllMocks()
    
    // Mock API 响应
    vi.mocked(employeeApi.generateEmployeeNumber).mockResolvedValue({
      code: 200,
      message: 'success',
      data: {
        employeeNumber: '2024001'
      }
    })
    
    vi.mocked(employeeApi.create).mockResolvedValue({
      code: 200,
      message: 'success',
      data: {} as any
    })
  })
  
  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })
  
  it('应该正确渲染组件', async () => {
    wrapper = mount(EmployeeAdd, {
      global: {
        plugins: [pinia],
        stubs: {
          ElPageHeader: true,
          ElCard: true,
          ElSteps: true,
          ElStep: true,
          ElButton: true,
          ElIcon: true,
          Document: true,
          BasicInfoForm: true,
          EducationForm: true,
          WorkForm: true,
          FamilyForm: true,
          OtherInfoForm: true,
          ConfirmSubmit: true
        }
      }
    })
    
    await wrapper.vm.$nextTick()
    
    expect(wrapper.find('.employee-add').exists()).toBe(true)
    expect(wrapper.find('.steps-card').exists()).toBe(true)
    expect(wrapper.find('.form-card').exists()).toBe(true)
  })
  
  it('应该显示正确的步骤条', async () => {
    wrapper = mount(EmployeeAdd, {
      global: {
        plugins: [pinia],
        stubs: {
          ElPageHeader: true,
          ElCard: true,
          ElSteps: {
            template: '<div><slot /></div>'
  },
          ElStep: {
            template: '<div>{{ title }}</div>',
            props: ['title']
          },
          ElButton: true,
          ElIcon: true,
          Document: true,
          BasicInfoForm: true,
          EducationForm: true,
          WorkForm: true,
          FamilyForm: true,
          OtherInfoForm: true,
          ConfirmSubmit: true
        }
      }
    })
    
    const steps = wrapper.findAll('.el-step')
    expect(steps).toHaveLength(0) // 因为我们用了简化的 stub
    
    // 验证步骤文本
    expect(wrapper.text()).toContain('基本信息')
    expect(wrapper.text()).toContain('教育经历')
    expect(wrapper.text()).toContain('工作经历')
    expect(wrapper.text()).toContain('家庭信息')
    expect(wrapper.text()).toContain('其他信息')
    expect(wrapper.text()).toContain('确认提交')
  })
  
  it('应该在挂载时加载草稿数据', async () => {
    const draftData = {
      data: {
        fullName: '张三',
        gender: 'male' },
  step: 2,
      timestamp: new Date().toISOString()
    },
  localStorageMock.getItem.mockReturnValue(JSON.stringify(draftData))
    
    wrapper = mount(EmployeeAdd, {
      global: {
        plugins: [pinia],
        stubs: {
          ElPageHeader: true,
          ElCard: true,
          ElSteps: true,
          ElStep: true,
          ElButton: true,
          ElIcon: true,
          Document: true,
          BasicInfoForm: true,
          EducationForm: true,
          WorkForm: true,
          FamilyForm: true,
          OtherInfoForm: true,
          ConfirmSubmit: true
        }
      }
    })
    
    await wrapper.vm.$nextTick()
    
    expect(localStorageMock.getItem).toHaveBeenCalledWith('employee_add_draft')
    expect(wrapper.vm.activeStep).toBe(2)
    expect(ElMessage.info).toHaveBeenCalledWith('已恢复上次填写的内容')
  })
  
  it('应该处理步骤切换', async () => {
    wrapper = mount(EmployeeAdd, {
      global: {
        plugins: [pinia],
        stubs: {
          ElPageHeader: true,
          ElCard: true,
          ElSteps: true,
          ElStep: true,
          ElButton: true,
          ElIcon: true,
          Document: true,
          BasicInfoForm: {
            template: '<div></div>',
            methods: {
              validate: vi.fn().mockResolvedValue(true)
            }
          },
          EducationForm: true,
          WorkForm: true,
          FamilyForm: true,
          OtherInfoForm: true,
          ConfirmSubmit: true
        }
      }
    })
    
    expect(wrapper.vm.activeStep).toBe(0)
    
    // 下一步
    await wrapper.vm.handleNext()
    await wrapper.vm.$nextTick()
    
    // 由于 validate 的异步处理，activeStep 的更新会在 handleStepValidate 中进行
    // 这里我们直接测试 handlePrev
    wrapper.vm.activeStep = 2
    await wrapper.vm.handlePrev()
    
    expect(wrapper.vm.activeStep).toBe(1)
  })
  
  it('应该保存草稿', async () => {
    wrapper = mount(EmployeeAdd, {
      global: {
        plugins: [pinia],
        stubs: {
          ElPageHeader: true,
          ElCard: true,
          ElSteps: true,
          ElStep: true,
          ElButton: true,
          ElIcon: true,
          Document: true,
          BasicInfoForm: true,
          EducationForm: true,
          WorkForm: true,
          FamilyForm: true,
          OtherInfoForm: true,
          ConfirmSubmit: true
        }
      }
    })
    
    await wrapper.vm.handleSaveDraft()
    
    expect(localStorageMock.setItem).toHaveBeenCalled()
    expect(ElMessage.success).toHaveBeenCalledWith('草稿保存成功')
  })
  
  it('应该提交表单', async () => {
    wrapper = mount(EmployeeAdd, {
      global: {
        plugins: [pinia],
        stubs: {
          ElPageHeader: true,
          ElCard: true,
          ElSteps: true,
          ElStep: true,
          ElButton: true,
          ElIcon: true,
          Document: true,
          BasicInfoForm: true,
          EducationForm: true,
          WorkForm: true,
          FamilyForm: true,
          OtherInfoForm: true,
          ConfirmSubmit: {
            template: '<div></div>',
            methods: {
              validate: vi.fn().mockResolvedValue(true)
            }
          }
        }
      }
    })
    
    // 设置到最后一步
    wrapper.vm.activeStep = 5
    await wrapper.vm.$nextTick()
    
    // 设置表单数据
    wrapper.vm.formData.fullName = '张三',
  wrapper.vm.formData.personnelType = 'regular',
  await wrapper.vm.handleSubmit()
    
    expect(employeeApi.generateEmployeeNumber).toHaveBeenCalledWith({
      year: new Date().getFullYear(),
      type: 'regular'
    })
    
    expect(employeeApi.create).toHaveBeenCalled()
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('employee_add_draft')
    expect(ElMessage.success).toHaveBeenCalledWith('员工信息创建成功')
    expect(mockPush).toHaveBeenCalledWith('/employee/list')
  })
  
  it('应该处理返回操作', async () => {
    wrapper = mount(EmployeeAdd, {
      global: {
        plugins: [pinia],
        stubs: {
          ElPageHeader: true,
          ElCard: true,
          ElSteps: true,
          ElStep: true,
          ElButton: true,
          ElIcon: true,
          Document: true,
          BasicInfoForm: true,
          EducationForm: true,
          WorkForm: true,
          FamilyForm: true,
          OtherInfoForm: true,
          ConfirmSubmit: true
        }
      }
    })
    
    await wrapper.vm.handleBack()
    
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('employee_add_draft')
    expect(mockBack).toHaveBeenCalled()
  })
  
  it('应该启动自动保存', async () => {
    vi.useFakeTimers()
    
    wrapper = mount(EmployeeAdd, {
      global: {
        plugins: [pinia],
        stubs: {
          ElPageHeader: true,
          ElCard: true,
          ElSteps: true,
          ElStep: true,
          ElButton: true,
          ElIcon: true,
          Document: true,
          BasicInfoForm: true,
          EducationForm: true,
          WorkForm: true,
          FamilyForm: true,
          OtherInfoForm: true,
          ConfirmSubmit: true
        }
      }
    })
    
    // 快进30秒
    vi.advanceTimersByTime(30000)
    
    expect(localStorageMock.setItem).toHaveBeenCalled()
    
    vi.useRealTimers()
  })
})