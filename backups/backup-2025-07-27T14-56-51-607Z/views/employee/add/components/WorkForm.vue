<template>
  <div class="work-form">
    <!-- 工具栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        添加工作经历
      </el-button>
      <div class="tips">
        <el-icon><InfoFilled /></el-icon>
        请按时间倒序填写，当前工作请填写在第一条
      </div>
    </div>

    <!-- 工作经历列表 -->
    <div class="work-list" v-if="workList.length > 0">
      <el-table :data="workList" style="width: 100%">
        <el-table-column label="起止时间" width="200">
          <template #default="{ row }">
            <div class="date-range">
              <span>{{ row.startDate }}</span>
              <span class="separator">至</span>
              <span>{{ row.endDate || '至今' }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="companyName" label="工作单位" min-width="200" show-overflow-tooltip  />
        
        <el-table-column prop="positionHeld" label="职位" width="150" show-overflow-tooltip  />
        
        <el-table-column prop="salaryGrade" label="薪资等级" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.salaryGrade" size="small">
              {{ row.salaryGrade }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        
        <el-table-column label="证明材料" width="120" align="center">
          <template #default="{ row }">
            <div class="proof-links">
              <el-link
                v-if="row.workProofUrl"
                type="primary"
                :href="row.workProofUrl"
                target="_blank"
                :underline="false"
              >
                工作证明
              </el-link>
              <el-link
                v-if="row.resignationProofUrl"
                type="primary"
                :href="row.resignationProofUrl"
                target="_blank"
                :underline="false"
              >
                离职证明
              </el-link>
              <span v-if="!row.workProofUrl && !row.resignationProofUrl">-</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="海外工作" width="100" align="center">
          <template #default="{ row }">
            <el-tag v-if="row.overseasLocation" type="info" size="small">
              {{ row.overseasLocation }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ $index }">
            <el-button link type="primary" @click="handleEdit($index)">
              编辑
            </el-button>
            <el-button link type="danger" @click="handleDelete($index)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 空状态 -->
    <el-empty
      v-else
      description="暂无工作经历，请点击上方按钮添加"
      :image-size="100"
     />

    <!-- 工作经历编辑对话框 -->
    <WorkFormDialog
      v-model="dialogVisible"
      :data="currentWork"
      @confirm="handleConfirm"
    />
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'WorkForm'
})
 
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, InfoFilled } from '@element-plus/icons-vue'
import WorkFormDialog from './WorkFormDialog.vue'
import type { WorkExperience } from '@/types/employee'

interface Props {
  workList: WorkExperience[]
}

const props = withDefaults(defineProps<Props>(), {
  workList: () => []
})

const emit = defineEmits<{
  'update:workList': [value: WorkExperience[]]
  'validate': [valid: boolean]
}>()

// 对话框状态
const dialogVisible = ref(false)
const isEdit = ref(false)
const editIndex = ref(-1)

// 当前编辑的工作经历
const currentWork = computed(() => {
  if (isEdit.value && editIndex.value >= 0) {
    return {
      ...props.workList[editIndex.value],
      employeeId: 'temp' // 临时ID
    }
  }
  return {
    employeeId: 'temp' // 临时ID
  }
})

// 添加工作经历
const handleAdd = () => {
  isEdit.value = false
  editIndex.value = -1
  dialogVisible.value = true
}

// 编辑工作经历
const handleEdit = (index: number) => {
  isEdit.value = true
  editIndex.value = index
  dialogVisible.value = true
}

// 删除工作经历
const handleDelete = async (index: number) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条工作经历吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const newList = [...props.workList]
    newList.splice(index, 1)
    emit('update:workList', newList)
    
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

// 处理确认
const handleConfirm = (workData: WorkExperience) => {
  const newList = [...props.workList]
  
  if (isEdit.value) {
    // 编辑
    newList[editIndex.value] = workData
  } else {
    // 新增
    newList.push(workData)
  }
  
  // 按开始日期倒序排序
  newList.sort((a, b) => {
    return new Date(b.startDate).getTime() - new Date(a.startDate).getTime()
  })
  
  emit('update:workList', newList)
  dialogVisible.value = false
}

// 表单验证
const validate = async () => {
  // 可以设置最少需要的工作经历数量
  // 这里暂时不强制要求
  emit('validate', true)
}

// 暴露方法
defineExpose({
  validate
})
</script>

<style lang="scss" scoped>
.work-form {
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .tips {
      display: flex;
      align-items: center;
      gap: 5px;
      color: var(--el-text-color-secondary);
      font-size: 14px;
    }
  }
  
  .work-list {
    .date-range {
      display: flex;
      align-items: center;
      gap: 5px;
      
      .separator {
        color: var(--el-text-color-secondary);
      }
    }
    
    .proof-links {
      display: flex;
      flex-direction: column;
      gap: 5px;
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .work-form {
    .toolbar {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }
  }
}
</style>