<template>
  <div class="family-form">
    <!-- 工具栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        添加家庭成员
      </el-button>
      <div class="tips">
        <el-icon><InfoFilled /></el-icon>
        请填写主要家庭成员信息
      </div>
    </div>

    <!-- 家庭成员列表 -->
    <div class="family-list" v-if="familyList.length > 0">
      <el-table :data="familyList" style="width: 100%">
        <el-table-column prop="fullName" label="姓名" width="120"  />
        
        <el-table-column prop="relationship" label="关系" width="100">
          <template #default="{ row }">
            <el-tag size="small">{{ row.relationship }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="dateOfBirth" label="出生日期" width="120"  />
        
        <el-table-column prop="politicalStatus" label="政治面貌" width="120">
          <template #default="{ row }">
            {{ row.politicalStatus || '-' }}
          </template>
        </el-table-column>
        
        <el-table-column prop="workUnitAndPosition" label="工作单位及职务" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.workUnitAndPosition || '-' }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ $index }">
            <el-button link type="primary" @click="handleEdit($index)">
              编辑
            </el-button>
            <el-button link type="danger" @click="handleDelete($index)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 空状态 -->
    <el-empty
      v-else
      description="暂无家庭成员信息，请点击上方按钮添加"
      :image-size="100"
     />

    <!-- 家庭成员编辑对话框 -->
    <FamilyFormDialog
      v-model="dialogVisible"
      :data="currentFamily"
      @confirm="handleConfirm"
    />
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'FamilyForm'
})
 
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, InfoFilled } from '@element-plus/icons-vue'
import FamilyFormDialog from './FamilyFormDialog.vue'
import type { FamilyMember } from '@/types/employee'

interface Props {
  familyList: FamilyMember[]
}

const props = withDefaults(defineProps<Props>(), {
  familyList: () => []
})

const emit = defineEmits<{
  'update:familyList': [value: FamilyMember[]]
  'validate': [valid: boolean]
}>()

// 对话框状态
const dialogVisible = ref(false)
const isEdit = ref(false)
const editIndex = ref(-1)

// 当前编辑的家庭成员
const currentFamily = computed(() => {
  if (isEdit.value && editIndex.value >= 0) {
    return {
      ...props.familyList[editIndex.value],
      employeeId: 'temp' // 临时ID
    }
  }
  return {
    employeeId: 'temp' // 临时ID
  }
})

// 添加家庭成员
const handleAdd = () => {
  isEdit.value = false
  editIndex.value = -1
  dialogVisible.value = true
}

// 编辑家庭成员
const handleEdit = (index: number) => {
  isEdit.value = true
  editIndex.value = index
  dialogVisible.value = true
}

// 删除家庭成员
const handleDelete = async (index: number) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个家庭成员吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const newList = [...props.familyList]
    newList.splice(index, 1)
    emit('update:familyList', newList)
    
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

// 处理确认
const handleConfirm = (familyData: FamilyMember) => {
  const newList = [...props.familyList]
  
  if (isEdit.value) {
    // 编辑
    newList[editIndex.value] = familyData
  } else {
    // 新增
    newList.push(familyData)
  }
  
  emit('update:familyList', newList)
  dialogVisible.value = false
}

// 表单验证
const validate = async () => {
  // 家庭成员信息不是必填项
  emit('validate', true)
}

// 暴露方法
defineExpose({
  validate
})
</script>

<style lang="scss" scoped>
.family-form {
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .tips {
      display: flex;
      align-items: center;
      gap: 5px;
      color: var(--el-text-color-secondary);
      font-size: 14px;
    }
  }
  
  .family-list {
    // 表格样式已由Element Plus提供
  }
}

// 响应式适配
@media (max-width: 768px) {
  .family-form {
    .toolbar {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }
  }
}
</style>