<template>
  <el-form
    ref="formRef"
    :model="localData"
    :rules="rules"
    label-width="120px"
    class="other-info-form"
  >
    <el-divider content-position="left">紧急联系人</el-divider>
    
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="紧急联系人" prop="emergencyContact">
          <el-input
            v-model="localData.emergencyContact"
            placeholder="请输入紧急联系人姓名"
            maxlength="50"
            />
        </el-form-item>
      </el-col>
      
      <el-col :span="12">
        <el-form-item label="联系电话" prop="emergencyContactPhone">
          <el-input
            v-model="localData.emergencyContactPhone"
            placeholder="请输入紧急联系人电话"
            maxlength="20"
            />
        </el-form-item>
      </el-col>
    </el-row>
    
    <el-divider content-position="left">住址信息</el-divider>
    
    <el-row :gutter="20">
      <el-col :span="16">
        <el-form-item label="现住址" prop="currentAddress">
          <el-input
            v-model="localData.currentAddress"
            placeholder="请输入详细地址"
            maxlength="200"
            />
        </el-form-item>
      </el-col>
      
      <el-col :span="8">
        <el-form-item label="邮政编码" prop="postalCode">
          <el-input
            v-model="localData.postalCode"
            placeholder="请输入邮政编码"
            maxlength="6"
            />
        </el-form-item>
      </el-col>
    </el-row>
    
    <el-divider content-position="left">入职材料</el-divider>
    
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form-item label="入职材料" prop="entryMaterials">
          <div class="materials-upload">
            <HrFileUpload
              v-model="entryMaterialFiles"
              :config="uploadConfig"
              :rules="uploadRules"
              :limit="10"
              tip="支持上传PDF、JPG、PNG格式，单个文件不超过10MB"
            />
            <div class="materials-checklist">
              <div class="checklist-title">
                <el-icon><InfoFilled /></el-icon>
                入职材料清单
              </div>
              <ul class="checklist-items">
                <li>身份证复印件（正反面）</li>
                <li>最高学历学位证书复印件</li>
                <li>专业技术职称证书复印件（如有）</li>
                <li>上一家单位离职证明</li>
                <li>体检报告（近三个月内）</li>
                <li>一寸免冠照片（电子版）</li>
                <li>其他相关证明材料</li>
              </ul>
            </div>
          </div>
        </el-form-item>
      </el-col>
    </el-row>
    
    <el-divider content-position="left">备注信息</el-divider>
    
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form-item label="备注" prop="remarks">
          <el-input
            v-model="localData.remarks"
            type="textarea"
            :rows="4"
            placeholder="请输入其他需要说明的信息"
            maxlength="500"
            show-word-limit
            />
        </el-form-item>
      </el-col>
    </el-row>
    
    <el-divider content-position="left">合同信息（选填）</el-divider>
    
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="合同类型" prop="contractType">
          <el-select v-model="localData.contractType" placeholder="请选择合同类型" clearable>
            <el-option label="固定期限劳动合同" value="fixed"  />
            <el-option label="无固定期限劳动合同" value="permanent"  />
            <el-option label="以完成一定工作任务为期限" value="project"  />
            <el-option label="劳务合同" value="service"  />
            <el-option label="实习协议" value="internship"  />
          </el-select>
        </el-form-item>
      </el-col>
      
      <el-col :span="12">
        <el-form-item label="合同期限" prop="contractPeriod">
          <el-select v-model="localData.contractPeriod" placeholder="请选择合同期限" clearable>
            <el-option label="1年" value="1"  />
            <el-option label="2年" value="2"  />
            <el-option label="3年" value="3"  />
            <el-option label="5年" value="5"  />
            <el-option label="无固定期限" value="0"  />
          </el-select>
        </el-form-item>
      </el-col>
      
      <el-col :span="12">
        <el-form-item label="试用期" prop="probationPeriod">
          <el-select v-model="localData.probationPeriod" placeholder="请选择试用期" clearable>
            <el-option label="无试用期" value="0"  />
            <el-option label="1个月" value="1"  />
            <el-option label="2个月" value="2"  />
            <el-option label="3个月" value="3"  />
            <el-option label="6个月" value="6"  />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, watch } from 'vue'
import { InfoFilled } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { HrFileUpload } from '@/components'
import type { EmployeeDetail } from '@/types/employee'

interface Props {
  modelValue: Partial<EmployeeDetail>
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: Partial<EmployeeDetail>]
  'validate': [valid: boolean]
}>()

// 表单实例
const formRef = ref<FormInstance>()

// 本地数据
const localData = reactive({
  // 紧急联系人
  emergencyContact: '',
  emergencyContactPhone: '',
  
  // 住址信息
  currentAddress: '',
  postalCode: '',
  
  // 备注
  remarks: '',
  
  // 合同信息
  contractType: '',
  contractPeriod: '',
  probationPeriod: '',
  
  // 入职材料
  entryMaterialUrls: [] as string[],
  
  ...props.modelValue
})

// 文件列表
const entryMaterialFiles = ref<any[]>([])

// 上传配置
const uploadConfig = {
  action: '/api/upload',
  headers: {
    Authorization: `Bearer ${localStorage.getItem('token')}`
  }
}

// 上传规则
const uploadRules = {
  accept: ['application/pdf', 'image/jpeg', 'image/png'],
  maxSize: 10 // 10MB
}

// 表单验证规则
const rules: FormRules = {
  emergencyContact: [
    { required: true, message: '请输入紧急联系人', trigger: 'blur' }
  ],
  emergencyContactPhone: [
    { required: true, message: '请输入紧急联系人电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  currentAddress: [
    { required: true, message: '请输入现住址', trigger: 'blur' }
  ],
  postalCode: [
    { pattern: /^\d{6}$/, message: '请输入6位邮政编码', trigger: 'blur' }
  ]
}

// 监听数据变化，同步到父组件
watch(localData, (_newVal) => {
  // 处理文件上传
  if (entryMaterialFiles.value.length > 0) {
    localData.entryMaterialUrls = entryMaterialFiles.value.map(file => file.url)
  }
  
  emit('update:modelValue', newVal)
}, { deep: true })

// 监听父组件数据变化
watch(() => props.modelValue, (_newVal) => {
  Object.assign(localData, newVal)
  
  // 恢复文件列表
  if (newVal.entryMaterialUrls && newVal.entryMaterialUrls.length > 0) {
    entryMaterialFiles.value = newVal.entryMaterialUrls.map((url, index) => ({
      name: `入职材料${index + 1}`,
      url
    }))
  }
}, { deep: true })

// 监听文件变化
watch(entryMaterialFiles, (newFiles) => {
  localData.entryMaterialUrls = newFiles.map(file => file.url)
}, { deep: true })

// 表单验证
const validate = async () => {
  try {
    const valid = await formRef.value?.validate()
    
    // 检查入职材料
    if (entryMaterialFiles.value.length === 0) {
      emit('validate', false)
      throw new Error('请上传入职材料')
    }
    
    emit('validate', !!valid)
  } catch (__error) {
    emit('validate', false)
    throw error
  }
}

// 暴露方法
defineExpose({
  validate
})
</script>

<style lang="scss" scoped>
.other-info-form {
  .materials-upload {
    .materials-checklist {
      margin-top: 20px;
      padding: 15px;
      background-color: var(--el-fill-color-lighter);
      border-radius: 4px;
      
      .checklist-title {
        display: flex;
        align-items: center;
        gap: 5px;
        margin-bottom: 10px;
        font-weight: 500;
        color: var(--el-text-color-primary);
      }
      
      .checklist-items {
        margin: 0;
        padding-left: 20px;
        
        li {
          margin-bottom: 5px;
          color: var(--el-text-color-regular);
          font-size: 14px;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .other-info-form {
    :deep(.el-form-item) {
      margin-bottom: 12px;
    }
    
    :deep(.el-col) {
      margin-bottom: 0 !important;
    }
  }
}
</style>