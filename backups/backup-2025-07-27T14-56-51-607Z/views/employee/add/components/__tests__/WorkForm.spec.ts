 
 

/**
 * WorkForm 组件测试
 * @description 自动生成的组件测试文件
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { mount, flushPromises } from '@vue/test-utils'
import WorkForm from '../WorkForm.vue'
describe('WorkForm', () => {
  let wrapper

  beforeEach(() => {
    wrapper = null
  })

  afterEach(() => {
    wrapper?.unmount()
  })

  it('应该正确渲染', async () => {
    const wrapper = mount(WorkForm)
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.hr-work-form').exists()).toBe(true)
  })

  it('应该处理异步操作', async () => {
    const wrapper = mount(WorkForm)

    // 等待异步操作完成
    await wrapper.vm.$nextTick()
    await flushPromises()

    // 验证异步操作结果
    expect(wrapper.find('[data-loaded="true"]').exists()).toBe(true)
  })

  it('应该进行表单验证', async () => {
    const wrapper = mount(WorkForm, {
      props: {
        rules: {
          required: true,
          message: '此项为必填项'
        }
      }
    })

    // 触发验证
    await wrapper.vm.validate()

    // 检查错误信息
    expect(wrapper.find('.error-message').exists()).toBe(true)
    expect(wrapper.find('.error-message').text()).toBe('此项为必填项')
  })

  it('应该匹配快照', () => {
    const wrapper = mount(WorkForm)
    expect(wrapper.html()).toMatchSnapshot()
  })
})
