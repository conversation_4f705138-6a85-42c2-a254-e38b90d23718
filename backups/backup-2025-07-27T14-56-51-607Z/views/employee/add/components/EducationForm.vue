<template>
  <div class="education-form">
    <!-- 工具栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        添加教育经历
      </el-button>
      <div class="tips">
        <el-icon><InfoFilled /></el-icon>
        请按时间倒序填写，最高学历请填写在第一条
      </div>
    </div>

    <!-- 教育经历列表 -->
    <div class="education-list" v-if="educationList.length > 0">
      <el-timeline>
        <el-timeline-item
          v-for="(item, index) in educationList"
          :key="item.tempId || item.recordId"
          :timestamp="`${item.startDate} 至 ${item.endDate}`"
          placement="top"
        >
          <el-card>
            <template #header>
              <div class="card-header">
                <div class="header-left">
                  <el-tag :type="getDegreeTagType(item.degree)">
                    {{ item.degree }}
                  </el-tag>
                  <span class="school-name">{{ item.graduationSchool }}</span>
                </div>
                <div class="header-actions">
                  <el-button link type="primary" @click="handleEdit(index)">
                    编辑
                  </el-button>
                  <el-button link type="danger" @click="handleDelete(index)">
                    删除
                  </el-button>
                </div>
              </div>
            </template>

            <el-descriptions :column="2" size="small">
              <el-descriptions-item label="专业">
                {{ item.major || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="学习形式">
                {{ item.studyForm || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="学位">
                {{ item.degreeType || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="学位授予单位">
                {{ item.degreeGrantingInstitution || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="毕业论文题目" :span="2">
                {{ item.dissertationTitle || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="导师姓名">
                {{ item.supervisorName || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="是否最高学历">
                <el-tag v-if="item.isHighestDegree" type="success" size="small">是</el-tag>
                <el-tag v-else type="info" size="small">否</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="海外学历认证" v-if="item.isOverseas">
                <el-tag v-if="item.overseasCertificationNumber" type="success" size="small">
                  已认证
                </el-tag>
                <el-tag v-else type="warning" size="small">未认证</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="证书文件" v-if="item.certificateUrl">
                <el-link type="primary" :href="item.certificateUrl" target="_blank">
                  查看证书
                </el-link>
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </div>

    <!-- 空状态 -->
    <el-empty
      v-else
      description="暂无教育经历，请点击上方按钮添加"
      :image-size="100"
     />

    <!-- 编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑教育经历' : '新增教育经历'"
      width="700px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="起止时间" prop="dateRange">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                @change="handleDateChange"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="学历" prop="degree">
              <el-select v-model="formData.degree" placeholder="请选择学历">
                <el-option label="博士研究生" value="博士研究生"  />
                <el-option label="硕士研究生" value="硕士研究生"  />
                <el-option label="本科" value="本科"  />
                <el-option label="专科" value="专科"  />
                <el-option label="高中" value="高中"  />
                <el-option label="初中及以下" value="初中及以下"  />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="毕业院校" prop="graduationSchool">
              <el-input
                v-model="formData.graduationSchool"
                placeholder="请输入毕业院校全称"
                maxlength="100"
                />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="所学专业" prop="major">
              <el-input
                v-model="formData.major"
                placeholder="请输入所学专业"
                maxlength="100"
                />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="学习形式" prop="studyForm">
              <el-select v-model="formData.studyForm" placeholder="请选择学习形式">
                <el-option label="全日制" value="全日制"  />
                <el-option label="在职" value="在职"  />
                <el-option label="函授" value="函授"  />
                <el-option label="自考" value="自考"  />
                <el-option label="网络教育" value="网络教育"  />
                <el-option label="其他" value="其他"  />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="学位" prop="degreeType">
              <el-select v-model="formData.degreeType" placeholder="请选择学位" clearable>
                <el-option label="博士" value="博士"  />
                <el-option label="硕士" value="硕士"  />
                <el-option label="学士" value="学士"  />
                <el-option label="无学位" value="无学位"  />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="学位授予单位" prop="degreeGrantingInstitution">
              <el-input
                v-model="formData.degreeGrantingInstitution"
                placeholder="请输入学位授予单位"
                maxlength="100"
                />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="是否最高学历" prop="isHighestDegree">
              <el-switch v-model="formData.isHighestDegree"  />
            </el-form-item>
          </el-col>
          
          <el-col :span="24">
            <el-form-item label="毕业论文题目" prop="dissertationTitle">
              <el-input
                v-model="formData.dissertationTitle"
                placeholder="请输入毕业论文题目"
                maxlength="200"
                />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="导师姓名" prop="supervisorName">
              <el-input
                v-model="formData.supervisorName"
                placeholder="请输入导师姓名"
                maxlength="50"
                />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="是否海外学历" prop="isOverseas">
              <el-switch v-model="formData.isOverseas"  />
            </el-form-item>
          </el-col>
          
          <el-col :span="12" v-if="formData.isOverseas">
            <el-form-item label="认证编号" prop="overseasCertificationNumber">
              <el-input
                v-model="formData.overseasCertificationNumber"
                placeholder="请输入教育部留学服务中心认证编号"
                maxlength="50"
                />
            </el-form-item>
          </el-col>
          
          <el-col :span="12" v-if="formData.isOverseas">
            <el-form-item label="认证日期" prop="overseasCertificationDate">
              <el-date-picker
                v-model="formData.overseasCertificationDate"
                type="date"
                placeholder="选择认证日期"
                value-format="YYYY-MM-DD"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          
          <el-col :span="24">
            <el-form-item label="学历证书" prop="certificateUrl">
              <HrFileUpload
                v-model="certificateFiles"
                :config="uploadConfig"
                :rules="uploadRules"
                :limit="1"
                tip="支持上传PDF、JPG、PNG格式，文件大小不超过10MB"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, InfoFilled } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { HrFileUpload } from '@/components'
import type { EducationHistory } from '@/types/employee'

interface Props {
  educationList: EducationHistory[]
}

const props = withDefaults(defineProps<Props>(), {
  educationList: () => []
})

const emit = defineEmits<{
  'update:educationList': [value: EducationHistory[]]
  'validate': [valid: boolean]
}>()

// 对话框状态
const dialogVisible = ref(false)
const isEdit = ref(false)
const editIndex = ref(-1)

// 表单实例
const formRef = ref<FormInstance>()

// 日期范围
const dateRange = ref<[string, string] | null>(null)

// 文件列表
const certificateFiles = ref<any[]>([])

// 表单数据
const formData = reactive<Partial<EducationHistory>>({
  startDate: '',
  endDate: '',
  degree: '',
  graduationSchool: '',
  major: '',
  studyForm: '全日制',
  degreeType: '',
  degreeGrantingInstitution: '',
  dissertationTitle: '',
  supervisorName: '',
  isHighestDegree: false,
  isOverseas: false,
  overseasCertificationNumber: '',
  overseasCertificationDate: '',
  certificateUrl: ''
})

// 上传配置
const uploadConfig = {
  action: '/api/upload',
  headers: {
    Authorization: 'Bearer ' + localStorage.getItem('token')
  }
}

// 上传规则
const uploadRules = {
  accept: ['application/pdf', 'image/jpeg', 'image/png'],
  maxSize: 10 // 10MB
}

// 表单验证规则
const rules: FormRules = {
  dateRange: [
    { required: true, message: '请选择起止时间', trigger: 'change' }
  ],
  degree: [
    { required: true, message: '请选择学历', trigger: 'change' }
  ],
  graduationSchool: [
    { required: true, message: '请输入毕业院校', trigger: 'blur' }
  ],
  major: [
    { required: true, message: '请输入所学专业', trigger: 'blur' }
  ],
  studyForm: [
    { required: true, message: '请选择学习形式', trigger: 'change' }
  ],
  overseasCertificationNumber: [
    { required: true, message: '请输入认证编号', trigger: 'blur' }
  ],
  overseasCertificationDate: [
    { required: true, message: '请选择认证日期', trigger: 'change' }
  ]
}

// 获取学历标签类型
const getDegreeTagType = (degree: string) => {
  const typeMap: Record<string, string> = {
    '博士研究生': 'danger',
    '硕士研究生': 'warning',
    '本科': 'success',
    '专科': '',
    '高中': 'info',
    '初中及以下': 'info'
  }
  return typeMap[degree] || 'info'
}

// 日期变化处理
const handleDateChange = (value: [string, string] | null) => {
  if (value) {
    formData.startDate = value[0]
    formData.endDate = value[1]
  } else {
    formData.startDate = ''
    formData.endDate = ''
  }
}

// 添加教育经历
const handleAdd = () => {
  isEdit.value = false
  editIndex.value = -1
  resetForm()
  dialogVisible.value = true
}

// 编辑教育经历
const handleEdit = (index: number) => {
  isEdit.value = true
  editIndex.value = index
  
  const item = props.educationList[index]
  Object.assign(formData, item)
  
  // 设置日期范围
  if (item.startDate && item.endDate) {
    dateRange.value = [item.startDate, item.endDate]
  }
  
  // 设置文件列表
  if (item.certificateUrl) {
    certificateFiles.value = [{
      name: 'HrHr学历证书',
      url: item.certificateUrl
    }]
  }
  
  dialogVisible.value = true
}

// 删除教育经历
const handleDelete = async (index: number) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条教育经历吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const newList = [...props.educationList]
    newList.splice(index, 1)
    emit('update:educationList', newList)
    
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

// 重置表单
const resetForm = () => {
  formData.startDate = ''
  formData.endDate = ''
  formData.degree = ''
  formData.graduationSchool = ''
  formData.major = ''
  formData.studyForm = '全日制'
  formData.degreeType = ''
  formData.degreeGrantingInstitution = ''
  formData.dissertationTitle = ''
  formData.supervisorName = ''
  formData.isHighestDegree = false
  formData.isOverseas = false
  formData.overseasCertificationNumber = ''
  formData.overseasCertificationDate = ''
  formData.certificateUrl = ''
  
  dateRange.value = null
  certificateFiles.value = []
  
  formRef.value?.resetFields()
}

// 取消
const handleCancel = () => {
  dialogVisible.value = false
  resetForm()
}

// 确认
const handleConfirm = async () => {
  try {
    await formRef.value?.validate()
    
    // 处理文件上传
    if (certificateFiles.value.length > 0) {
      formData.certificateUrl = certificateFiles.value[0].url
    }
    
    // 如果是最高学历，将其他记录的最高学历标记取消
    if (formData.isHighestDegree) {
      props.educationList.forEach(item => {
        item.isHighestDegree = false
      })
    }
    
    const newList = [...props.educationList]
    
    if (isEdit.value) {
      // 编辑
      newList[editIndex.value] = {
        ...newList[editIndex.value],
        ...formData
      } as EducationHistory
    } else {
      // 新增
      newList.push({
        tempId: Date.now().toString(),
        ...formData
      } as EducationHistory)
    }
    
    // 按开始日期倒序排序
    newList.sort((a, b) => {
      return new Date(b.startDate).getTime() - new Date(a.startDate).getTime()
    })
    
    emit('update:educationList', newList)
    
    ElMessage.success(isEdit.value ? '编辑成功' : '添加成功')
    handleCancel()
  } catch (__error) {
    console.error('表单验证失败:', error)
  }
}

// 表单验证
const validate = async () => {
  // 至少需要一条教育经历
  if (props.educationList.length === 0) {
    ElMessage.error('请至少添加一条教育经历')
    emit('validate', false)
    return
  }
  
  // 检查是否有最高学历
  const hasHighest = props.educationList.some(item => item.isHighestDegree)
  if (!hasHighest) {
    ElMessage.error('请标记一条教育经历为最高学历')
    emit('validate', false)
    return
  }
  
  emit('validate', true)
}

// 暴露方法
defineExpose({
  validate
})
</script>

<style lang="scss" scoped>
.education-form {
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .tips {
      display: flex;
      align-items: center;
      gap: 5px;
      color: var(--el-text-color-secondary);
      font-size: 14px;
    }
  }
  
  .education-list {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-left {
        display: flex;
        align-items: center;
        gap: 10px;
        
        .school-name {
          font-weight: 500;
        }
      }
      
      .header-actions {
        display: flex;
        gap: 10px;
      }
    }
    
    :deep(.el-timeline-item__wrapper) {
      padding-left: 40px;
    }
    
    :deep(.el-card) {
      margin-bottom: 20px;
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .education-form {
    .toolbar {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }
    
    .education-list {
      .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
      }
    }
  }
}
</style>