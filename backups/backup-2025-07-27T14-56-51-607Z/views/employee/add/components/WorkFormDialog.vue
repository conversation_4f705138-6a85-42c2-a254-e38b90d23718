<template>
  <el-dialog
    :model-value="modelValue"
    :title="isEdit ? '编辑工作经历' : '新增工作经历'"
    width="600px"
    :close-on-click-modal="false"
    @update:model-value="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="工作单位" prop="companyName">
        <el-input
          v-model="formData.companyName"
          placeholder="请输入工作单位名称"
          maxlength="100"
          />
      </el-form-item>
      
      <el-form-item label="起止时间" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期（留空表示至今）"
          value-format="YYYY-MM-DD"
          :disabled-date="disabledDate"
          @change="handleDateChange"
         />
      </el-form-item>
      
      <el-form-item label="职位" prop="positionHeld">
        <el-input
          v-model="formData.positionHeld"
          placeholder="请输入职位名称"
          maxlength="50"
          />
      </el-form-item>
      
      <el-form-item label="薪资等级" prop="salaryGrade">
        <el-select v-model="formData.salaryGrade" placeholder="请选择薪资等级" clearable>
          <el-option label="P1" value="P1"  />
          <el-option label="P2" value="P2"  />
          <el-option label="P3" value="P3"  />
          <el-option label="P4" value="P4"  />
          <el-option label="P5" value="P5"  />
          <el-option label="P6" value="P6"  />
          <el-option label="M1" value="M1"  />
          <el-option label="M2" value="M2"  />
          <el-option label="M3" value="M3"  />
          <el-option label="M4" value="M4"  />
          <el-option label="M5" value="M5"  />
        </el-select>
      </el-form-item>
      
      <el-divider content-position="left">证明材料（选填）</el-divider>
      
      <el-form-item label="工作证明" prop="workProofUrl">
        <HrFileUpload
          v-model="workProofFiles"
          :config="uploadConfig"
          :rules="uploadRules"
          :limit="1"
          tip="支持上传PDF、JPG、PNG格式"
        />
      </el-form-item>
      
      <el-form-item label="离职证明" prop="resignationProofUrl">
        <HrFileUpload
          v-model="resignationProofFiles"
          :config="uploadConfig"
          :rules="uploadRules"
          :limit="1"
          tip="支持上传PDF、JPG、PNG格式"
        />
      </el-form-item>
      
      <el-divider content-position="left">海外工作信息（选填）</el-divider>
      
      <el-form-item label="工作地点" prop="overseasLocation">
        <el-input
          v-model="formData.overseasLocation"
          placeholder="如：美国硅谷"
          maxlength="50"
          />
      </el-form-item>
      
      <el-form-item label="时区" prop="overseasTimeZone">
        <el-select v-model="formData.overseasTimeZone" placeholder="请选择时区" clearable filterable>
          <el-option label="UTC-12" value="UTC-12"  />
          <el-option label="UTC-11" value="UTC-11"  />
          <el-option label="UTC-10" value="UTC-10"  />
          <el-option label="UTC-8 (美国西部)" value="UTC-8"  />
          <el-option label="UTC-5 (美国东部)" value="UTC-5"  />
          <el-option label="UTC+0 (伦敦)" value="UTC+0"  />
          <el-option label="UTC+1 (巴黎)" value="UTC+1"  />
          <el-option label="UTC+8 (北京)" value="UTC+8"  />
          <el-option label="UTC+9 (东京)" value="UTC+9"  />
        </el-select>
      </el-form-item>
      
      <el-form-item label="币种" prop="overseasCurrency">
        <el-select v-model="formData.overseasCurrency" placeholder="请选择币种" clearable>
          <el-option label="USD (美元)" value="USD"  />
          <el-option label="EUR (欧元)" value="EUR"  />
          <el-option label="GBP (英镑)" value="GBP"  />
          <el-option label="JPY (日元)" value="JPY"  />
          <el-option label="HKD (港币)" value="HKD"  />
          <el-option label="CNY (人民币)" value="CNY"  />
        </el-select>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { HrFileUpload } from '@/components'
import type { WorkExperience } from '@/types/employee'

interface Props {
  modelValue: boolean
  data?: Partial<WorkExperience>
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  data: () => ({})
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': [data: WorkExperience]
}>()

// 是否编辑模式
const isEdit = computed(() => !!props.data?.recordId || !!props.data?.tempId)

// 表单实例
const formRef = ref<FormInstance>()

// 加载状态
const loading = ref(false)

// 表单数据
const formData = reactive<Partial<WorkExperience>>({
  companyName: '',
  startDate: '',
  endDate: '',
  positionHeld: '',
  salaryGrade: '',
  workProofUrl: '',
  resignationProofUrl: '',
  overseasLocation: '',
  overseasTimeZone: '',
  overseasCurrency: ''
})

// 日期范围
const dateRange = ref<[string, string | null] | null>(null)

// 文件列表
const workProofFiles = ref<any[]>([])
const resignationProofFiles = ref<any[]>([])

// 上传配置
const uploadConfig = {
  action: '/api/upload',
  headers: {
    Authorization: `Bearer ${localStorage.getItem('token')}`
  }
}

// 上传规则
const uploadRules = {
  accept: ['application/pdf', 'image/jpeg', 'image/png'],
  maxSize: 10 // 10MB
}

// 表单规则
const rules: FormRules = {
  companyName: [
    { required: true, message: '请输入工作单位名称', trigger: 'blur' }
  ],
  dateRange: [
    { required: true, message: '请选择起止时间', trigger: 'change' }
  ],
  positionHeld: [
    { required: true, message: '请输入职位名称', trigger: 'blur' }
  ]
}

// 监听props变化
watch(() => props.data, (newData) => {
  if (newData) {
    Object.assign(formData, newData)
    if (newData.startDate) {
      dateRange.value = [newData.startDate, newData.endDate || null]
    }
    if (newData.workProofUrl) {
      workProofFiles.value = [{
        name: 'HrHr工作证明',
        url: newData.workProofUrl
      }]
    }
    if (newData.resignationProofUrl) {
      resignationProofFiles.value = [{
        name: '离职证明',
        url: newData.resignationProofUrl
      }]
    }
  }
}, { immediate: true })

// 日期禁用
const disabledDate = (date: Date) => {
  return date > new Date()
}

// 日期变化
const handleDateChange = (value: [string, string | null] | null) => {
  if (value) {
    formData.startDate = value[0]
    formData.endDate = value[1] || ''
  } else {
    formData.startDate = ''
    formData.endDate = ''
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:modelValue', false)
  formRef.value?.resetFields()
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    // 处理文件上传
    if (workProofFiles.value.length > 0) {
      formData.workProofUrl = workProofFiles.value[0].url
    }
    if (resignationProofFiles.value.length > 0) {
      formData.resignationProofUrl = resignationProofFiles.value[0].url
    }
    
    loading.value = true
    
    // 构造完整的工作经历数据
    const workData: WorkExperience = {
      recordId: props.data?.recordId || '',
      tempId: props.data?.tempId || Date.now().toString(),
      employeeId: '',
      ...formData
    } as WorkExperience
    
    // 触发确认事件
    emit('confirm', workData)
    
    ElMessage.success(isEdit.value ? '编辑成功' : '新增成功')
    handleClose()
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('提交失败:', error)
    }
  } finally {
    loading.value = false
  }
}
</script>