<template>
  <el-dialog
    :model-value="modelValue"
    :title="isEdit ? '编辑家庭成员' : '新增家庭成员'"
    width="500px"
    :close-on-click-modal="false"
    @update:model-value="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="姓名" prop="fullName">
        <el-input
          v-model="formData.fullName"
          placeholder="请输入姓名"
          maxlength="50"
          />
      </el-form-item>
      
      <el-form-item label="关系" prop="relationship">
        <el-select v-model="formData.relationship" placeholder="请选择关系">
          <el-option label="配偶" value="配偶"  />
          <el-option label="父亲" value="父亲"  />
          <el-option label="母亲" value="母亲"  />
          <el-option label="儿子" value="儿子"  />
          <el-option label="女儿" value="女儿"  />
          <el-option label="兄弟" value="兄弟"  />
          <el-option label="姐妹" value="姐妹"  />
          <el-option label="其他" value="其他"  />
        </el-select>
      </el-form-item>
      
      <el-form-item label="出生日期" prop="dateOfBirth">
        <el-date-picker
          v-model="formData.dateOfBirth"
          type="date"
          placeholder="选择出生日期"
          value-format="YYYY-MM-DD"
          :disabled-date="disabledDate"
         />
      </el-form-item>
      
      <el-form-item label="政治面貌" prop="politicalStatus">
        <el-select v-model="formData.politicalStatus" placeholder="请选择政治面貌" clearable>
          <el-option label="中共党员" value="中共党员"  />
          <el-option label="中共预备党员" value="中共预备党员"  />
          <el-option label="共青团员" value="共青团员"  />
          <el-option label="民革党员" value="民革党员"  />
          <el-option label="民盟盟员" value="民盟盟员"  />
          <el-option label="民建会员" value="民建会员"  />
          <el-option label="民进会员" value="民进会员"  />
          <el-option label="农工党党员" value="农工党党员"  />
          <el-option label="致公党党员" value="致公党党员"  />
          <el-option label="九三学社社员" value="九三学社社员"  />
          <el-option label="台盟盟员" value="台盟盟员"  />
          <el-option label="无党派人士" value="无党派人士"  />
          <el-option label="群众" value="群众"  />
        </el-select>
      </el-form-item>
      
      <el-form-item label="工作单位" prop="workUnitAndPosition">
        <el-input
          v-model="formData.workUnitAndPosition"
          placeholder="请输入工作单位及职务"
          maxlength="200"
          />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'FamilyFormDialog'
})
 
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import type { FamilyMember } from '@/types/employee'

interface Props {
  modelValue: boolean
  data?: Partial<FamilyMember>
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  data: () => ({})
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': [data: FamilyMember]
}>()

// 是否编辑模式
const isEdit = computed(() => !!props.data?.recordId || !!props.data?.tempId)

// 表单实例
const formRef = ref<FormInstance>()

// 加载状态
const loading = ref(false)

// 表单数据
const formData = reactive<Partial<FamilyMember>>({
  fullName: '',
  relationship: '',
  dateOfBirth: '',
  politicalStatus: '',
  workUnitAndPosition: ''
})

// 表单规则
const rules: FormRules = {
  fullName: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  relationship: [
    { required: true, message: '请选择关系', trigger: 'change' }
  ]
}

// 监听props变化
watch(() => props.data, (newData) => {
  if (newData) {
    Object.assign(formData, newData)
  }
}, { immediate: true })

// 日期禁用
const disabledDate = (date: Date) => {
  return date > new Date()
}

// 关闭对话框
const handleClose = () => {
  emit('update:modelValue', false)
  formRef.value?.resetFields()
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    loading.value = true
    
    // 构造完整的家庭成员数据
    const familyData: FamilyMember = {
      recordId: props.data?.recordId || '',
      tempId: props.data?.tempId || Date.now().toString(),
      employeeId: '',
      ...formData
    } as FamilyMember
    
    // 触发确认事件
    emit('confirm', familyData)
    
    ElMessage.success(isEdit.value ? '编辑成功' : '新增成功')
    handleClose()
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('提交失败:', error)
    }
  } finally {
    loading.value = false
  }
}
</script>