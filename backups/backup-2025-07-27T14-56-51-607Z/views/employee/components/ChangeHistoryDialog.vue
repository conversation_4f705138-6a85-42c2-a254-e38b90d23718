<template>
  <el-dialog
    v-model="dialogVisible"
    title="变更历史记录"
    width="1000px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="change-history">
      <!-- 记录信息 -->
      <div class="record-info">
        <el-descriptions :column="2" border size="small">
          <el-descriptions-item label="表名">{{ tableName }}</el-descriptions-item>
          <el-descriptions-item label="记录ID">{{ recordId }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 历史记录列表 -->
      <div class="history-list">
        <div class="list-header">
          <h4>历史变更记录</h4>
          <el-button type="primary" size="small" @click="handleRefresh">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>

        <el-timeline v-if="historyList.length > 0">
          <el-timeline-item
            v-for="(item, index) in historyList"
            :key="item.id"
            :timestamp="item.createTime"
            :type="getTimelineType(item.operationType)"
            placement="top"
          >
            <el-card class="history-item">
              <div class="item-header">
                <div class="operation-info">
                  <el-tag :type="getOperationTypeTagType(item.operationType)" size="small">
                    {{ item.operationTypeName }}
                  </el-tag>
                  <el-tag :type="getChangeTypeTagType(item.changeType)" size="small" style="margin-left: 8px;">
                    {{ item.changeTypeName }}
                  </el-tag>
                  <span class="operator-name">{{ item.operatorName }}</span>
                </div>
                <div class="item-actions">
                  <el-button type="primary" link size="small" @click="handleViewDetail(item)">
                    查看详情
                  </el-button>
                  <el-button 
                    v-if="item.beforeData && item.afterData"
                    type="success" 
                    link 
                    size="small" 
                    @click="handleCompareData(item)"
                  >
                    数据对比
                  </el-button>
                </div>
              </div>
              
              <div v-if="item.changeDescription" class="change-description">
                {{ item.changeDescription }}
              </div>
              
              <div v-if="item.changedFields" class="changed-fields">
                <span class="label">变更字段：</span>
                <el-tag
                  v-for="field in getChangedFieldsList(item.changedFields)"
                  :key="field"
                  type="info"
                  size="small"
                  style="margin-right: 6px;"
                >
                  {{ field }}
                </el-tag>
              </div>
              
              <div class="item-meta">
                <span class="execution-time">
                  执行时间：
                  <span :class="getExecutionTimeClass(item.executionTime)">
                    {{ item.executionTime }}ms
                  </span>
                </span>
                <span class="risk-level" v-if="item.riskLevel">
                  风险等级：
                  <el-tag :type="getRiskLevelTagType(item.riskLevel)" size="small">
                    {{ item.riskLevelName }}
                  </el-tag>
                </span>
                <span class="status">
                  状态：
                  <el-tag v-if="item.isSuccess" type="success" size="small">成功</el-tag>
                  <el-tag v-else type="danger" size="small">失败</el-tag>
                </span>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>

        <!-- 空状态 -->
        <el-empty v-else-if="!loading" description="暂无变更历史记录"  />

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="3" animated  />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleExportHistory">
          <el-icon><Download /></el-icon>
          导出历史
        </el-button>
      </div>
    </template>

    <!-- 详情对话框 -->
    <DataChangeLogDialog
      v-model:visible="detailDialogVisible"
      :data-change-log="currentLog"
      mode="view"
    />

    <!-- 对比对话框 -->
    <DataComparisonDialog
      v-model:visible="comparisonDialogVisible"
      :data-change-log="currentLog"
    />
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'ChangeHistoryDialog'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Download } from '@element-plus/icons-vue'
import { dataChangeLogApi } from '@/api/dataChangeLog'
import type { DataChangeLog } from '@/types/dataChangeLog'
import DataChangeLogDialog from './DataChangeLogDialog.vue'
import DataComparisonDialog from './DataComparisonDialog.vue'

// Props
interface Props {
  visible: boolean
  tableName: string
  recordId: string
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  tableName: '',
  recordId: ''
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const loading = ref(false)
const historyList = ref<DataChangeLog[]>([])
const currentLog = ref<DataChangeLog | null>(null)
const detailDialogVisible = ref(false)
const comparisonDialogVisible = ref(false)

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 监听对话框显示状态，加载历史数据
watch(
  () => [props.visible, props.tableName, props.recordId],
  ([visible, tableName, recordId]) => {
    if (visible && tableName && recordId) {
      loadHistoryData()
    } else {
      historyList.value = []
    }
  }
)

// 加载历史数据
const loadHistoryData = async () => {
  if (!props.tableName || !props.recordId) return
  
  try {
    loading.value = true
    historyList.value = await dataChangeLogApi.getRecordHistory(props.tableName, props.recordId) as unknown // 修复API类型不匹配
  } catch (__error) {
    console.error('加载历史数据失败:', error)
    ElMessage.error('加载历史数据失败')
  } finally {
    loading.value = false
  }
}

// 刷新数据
const handleRefresh = () => {
  loadHistoryData()
}

// 查看详情
const handleViewDetail = (log: DataChangeLog) => {
  currentLog.value = log
  detailDialogVisible.value = true
}

// 数据对比
const handleCompareData = (log: DataChangeLog) => {
  currentLog.value = log
  comparisonDialogVisible.value = true
}

// 导出历史
const handleExportHistory = async () => {
  try {
    const queryParams = {
      tableName: props.tableName,
      recordId: props.recordId,
      sortBy: 'createTime',
      sortDirection: 'DESC' as const
    }
    const blob = await dataChangeLogApi.export(queryParams)
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `变更历史_${props.tableName}_${props.recordId}_${new Date().toISOString().slice(0, 10)}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('导出历史记录成功')
  } catch (__error) {
    console.error('导出历史记录失败:', error)
    ElMessage.error('导出历史记录失败')
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  historyList.value = []
  currentLog.value = null
}

// 获取变更字段列表
const getChangedFieldsList = (changedFields: string) => {
  try {
    return changedFields.split(',').map(field => field.trim()).filter(field => field)
  } catch (__error) {
    return []
  }
}

// 获取时间线类型
const getTimelineType = (operationType: string) => {
  switch (operationType) {
    case 'INSERT':
    case 'BATCH_INSERT':
      return 'success'
    case 'UPDATE':
    case 'BATCH_UPDATE':
      return 'primary'
    case 'DELETE':
    case 'BATCH_DELETE':
      return 'danger'
    case 'SELECT':
      return 'info'
    default:
      return 'primary'
  }
}

// 获取操作类型标签类型
const getOperationTypeTagType = (type: string) => {
  switch (type) {
    case 'INSERT':
    case 'BATCH_INSERT':
      return 'success'
    case 'UPDATE':
    case 'BATCH_UPDATE':
      return 'warning'
    case 'DELETE':
    case 'BATCH_DELETE':
      return 'danger'
    case 'SELECT':
      return 'info'
    default:
      return ''
  }
}

// 获取变更类型标签类型
const getChangeTypeTagType = (type: string) => {
  switch (type) {
    case 'CREATE':
      return 'success'
    case 'MODIFY':
      return 'warning'
    case 'DELETE':
      return 'danger'
    case 'QUERY':
      return 'info'
    case 'IMPORT':
    case 'EXPORT':
      return ''
    case 'BATCH_OPERATION':
      return 'warning'
    default:
      return ''
  }
}

// 获取风险等级标签类型
const getRiskLevelTagType = (level: string) => {
  switch (level) {
    case 'LOW':
      return 'info'
    case 'MEDIUM':
      return ''
    case 'HIGH':
      return 'warning'
    case 'CRITICAL':
      return 'danger'
    default:
      return ''
  }
}

// 获取执行时间样式类
const getExecutionTimeClass = (time?: number) => {
  if (!time) return ''
  
  if (time < 100) {
    return 'fast-execution'
  } else if (time < 1000) {
    return 'normal-execution'
  } else {
    return 'slow-execution'
  }
}
</script>

<style scoped>
.change-history {
  padding: 10px 0;
}

.record-info {
  margin-bottom: 20px;
}

.history-list {
  margin-bottom: 20px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.list-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-left: 4px solid #409eff;
  padding-left: 10px;
}

.history-item {
  margin-bottom: 0;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.operation-info {
  display: flex;
  align-items: center;
}

.operator-name {
  margin-left: 12px;
  font-weight: 600;
  color: #303133;
}

.item-actions {
  display: flex;
  gap: 8px;
}

.change-description {
  margin-bottom: 10px;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.changed-fields {
  margin-bottom: 10px;
}

.changed-fields .label {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
}

.item-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #909399;
}

.execution-time,
.risk-level,
.status {
  display: flex;
  align-items: center;
  gap: 4px;
}

.loading-container {
  padding: 40px 0;
}

.dialog-footer {
  text-align: right;
}

/* 执行时间样式 */
.fast-execution {
  color: #67c23a;
  font-weight: 600;
}

.normal-execution {
  color: #e6a23c;
  font-weight: 600;
}

.slow-execution {
  color: #f56c6c;
  font-weight: 600;
}

/* 描述列表样式 */
:deep(.el-descriptions__label) {
  font-weight: 600;
  color: #303133;
}

:deep(.el-descriptions__content) {
  color: #606266;
}

/* 时间线样式 */
:deep(.el-timeline-item__timestamp) {
  font-size: 12px;
  color: #909399;
}

:deep(.el-timeline-item__wrapper) {
  padding-left: 20px;
}

/* 卡片样式 */
:deep(.el-card__body) {
  padding: 16px;
}

/* 空状态样式 */
:deep(.el-empty) {
  padding: 40px 0;
}
</style>
