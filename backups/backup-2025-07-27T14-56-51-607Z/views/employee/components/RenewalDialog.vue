<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="renewal-dialog">
      <!-- 单个续期模式 -->
      <div v-if="!batchMode" class="single-renewal">
        <div class="vocational-qualification-info">
          <h4>职业资格信息</h4>
          <el-descriptions :column="1" border size="small">
            <el-descriptions-item label="员工姓名">
              {{ vocationalQualification?.employeeName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="证书名称">
              {{ vocationalQualification?.certificateName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="证书类型">
              <el-tag :type="getCertificateTypeTagType(vocationalQualification?.certificateType)" size="small">
                {{ vocationalQualification?.certificateTypeName || '-' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="当前到期日期">
              <span v-if="vocationalQualification?.expiryDate" :class="getExpiryDateClass(vocationalQualification.expiryDate)">
                {{ vocationalQualification.expiryDate }}
              </span>
              <span v-else>-</span>
            </el-descriptions-item>
            <el-descriptions-item label="有效期状态">
              <el-tag :type="getValidityStatusTagType(vocationalQualification?.validityStatus)" size="small">
                {{ vocationalQualification?.validityStatusName || '-' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="续期次数">
              {{ vocationalQualification?.renewalCount || 0 }}次
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 批量续期模式 -->
      <div v-else class="batch-renewal">
        <div class="batch-info">
          <h4>批量续期信息</h4>
          <p>您选择了 <strong>{{ selectedRows.length }}</strong> 个职业资格进行批量续期</p>
          
          <div class="selected-list">
            <el-scrollbar height="200px">
              <div v-for="item in selectedRows" :key="item.id" class="selected-item">
                <div class="item-info">
                  <span class="employee-name">{{ item.employeeName }}</span>
                  <span class="certificate-info">{{ item.certificateName }}</span>
                </div>
                <div class="item-tags">
                  <el-tag :type="getCertificateTypeTagType(item.certificateType)" size="small">
                    {{ item.certificateTypeName }}
                  </el-tag>
                  <span class="expiry-date" :class="getExpiryDateClass(item.expiryDate || '')">
                    {{ item.expiryDate || '-' }}
                  </span>
                </div>
              </div>
            </el-scrollbar>
          </div>
        </div>
      </div>

      <!-- 续期表单 -->
      <div class="renewal-form">
        <el-form
          ref="formRef"
          :model="form"
          :rules="formRules"
          label-width="120px"
          label-position="right"
        >
          <el-form-item label="新到期日期" prop="newExpiryDate">
            <el-date-picker
              v-model="form.newExpiryDate"
              type="date"
              placeholder="请选择新的到期日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
              :disabled-date="disabledDate"
             />
          </el-form-item>
          
          <el-form-item label="续期备注" prop="note">
            <el-input
              v-model="form.note"
              type="textarea"
              placeholder="请输入续期备注信息（可选）"
              :rows="4"
              maxlength="500"
              show-word-limit
              />
          </el-form-item>
        </el-form>
      </div>

      <!-- 续期提示 -->
      <div class="renewal-tips">
        <el-alert
          v-if="form.newExpiryDate"
          :title="getRenewalTipTitle()"
          type="success"
          :closable="false"
          show-icon
         />
        <el-alert
          v-else
          title="请选择新的到期日期"
          type="info"
          :closable="false"
          show-icon
         />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          :disabled="!form.newExpiryDate"
          @click="handleSubmit"
        >
          确认续期
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'RenewalDialog'
})
 
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { vocationalQualificationApi } from '@/api/vocationalQualification'
import type { VocationalQualification } from '@/types/vocationalQualification'

// Props
interface Props {
  visible: boolean
  vocationalQualification: VocationalQualification | null
  batchMode: boolean
  selectedRows: VocationalQualification[]
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  vocationalQualification: null,
  batchMode: false,
  selectedRows: () => []
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const loading = ref(false)
const formRef = ref<FormInstance>()

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 对话框标题
const dialogTitle = computed(() => {
  return props.batchMode ? '批量续期职业资格' : '续期职业资格'
})

// 表单数据
const form = reactive({
  newExpiryDate: '',
  note: ''
})

// 表单验证规则
const formRules: FormRules = {
  newExpiryDate: [
    { required: true, message: '请选择新的到期日期', trigger: 'change' }
  ],
  note: [
    { max: 500, message: '备注长度不能超过500个字符', trigger: 'blur' }
  ]
}

// 监听对话框显示状态，重置表单
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      // 重置表单
      form.newExpiryDate = ''
      form.note = ''
    }
  }
)

// 禁用日期（不能选择今天之前的日期）
const disabledDate = (time: Date) => {
  return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
}

// 获取续期提示标题
const getRenewalTipTitle = () => {
  if (!form.newExpiryDate) return ''
  
  const today = new Date()
  const newExpiry = new Date(form.newExpiryDate)
  const diffTime = newExpiry.getTime() - today.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (props.batchMode) {
    return `批量续期后，所选职业资格将在 ${diffDays} 天后到期（${form.newExpiryDate}）`
  } else {
    const currentExpiry = props.vocationalQualification?.expiryDate
    if (currentExpiry) {
      const currentExpiryDate = new Date(currentExpiry)
      const extensionDays = Math.ceil((newExpiry.getTime() - currentExpiryDate.getTime()) / (1000 * 60 * 60 * 24))
      return `续期后将延长 ${extensionDays} 天，新到期日期为 ${form.newExpiryDate}`
    } else {
      return `续期后将在 ${diffDays} 天后到期（${form.newExpiryDate}）`
    }
  }
}

// 提交续期
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    if (props.batchMode) {
      // 批量续期
      const ids = props.selectedRows.map(row => row.id!).filter(id => id)
      await vocationalQualificationApi.batchRenew(ids, form.newExpiryDate, form.note)
      ElMessage.success(`批量续期完成，共处理 ${ids.length} 个职业资格`)
    } else {
      // 单个续期
      if (props.vocationalQualification?.id) {
        await vocationalQualificationApi.renew(
          props.vocationalQualification.id, 
          form.newExpiryDate, 
          form.note
        )
        ElMessage.success('续期完成')
      }
    }

    emit('success')
    handleClose()
  } catch (__error) {
    console.error('续期失败:', error)
    if (error !== false) { // 不是表单验证错误
      ElMessage.error('续期操作失败')
    }
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  formRef.value?.resetFields()
}

// 获取证书类型标签类型
const getCertificateTypeTagType = (type?: string) => {
  switch (type) {
    case 'SKILL_CERTIFICATE':
      return 'success'
    case 'PROFESSIONAL_LICENSE':
      return 'warning'
    case 'TITLE_CERTIFICATE':
      return 'info'
    case 'TRAINING_CERTIFICATE':
      return ''
    default:
      return 'info'
  }
}

// 获取有效期状态标签类型
const getValidityStatusTagType = (status?: string) => {
  switch (status) {
    case 'VALID':
      return 'success'
    case 'EXPIRING_SOON':
      return 'warning'
    case 'EXPIRED':
      return 'danger'
    case 'SUSPENDED':
      return 'info'
    default:
      return ''
  }
}

// 获取到期日期样式类
const getExpiryDateClass = (expiryDate: string) => {
  if (!expiryDate) return ''
  
  const today = new Date()
  const expiry = new Date(expiryDate)
  const diffTime = expiry.getTime() - today.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) {
    return 'expired-date'
  } else if (diffDays <= 30) {
    return 'expiring-date'
  } else {
    return 'valid-date'
  }
}
</script>

<style scoped>
.renewal-dialog {
  padding: 10px 0;
}

.vocational-qualification-info,
.batch-info {
  margin-bottom: 20px;
}

.vocational-qualification-info h4,
.batch-info h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.batch-info p {
  margin: 0 0 12px 0;
  color: #606266;
  font-size: 14px;
}

.selected-list {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #fafafa;
}

.selected-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
}

.selected-item:last-child {
  border-bottom: none;
}

.item-info {
  flex: 1;
}

.employee-name {
  font-weight: 600;
  color: #303133;
  margin-right: 12px;
}

.certificate-info {
  color: #606266;
  font-size: 14px;
}

.item-tags {
  display: flex;
  align-items: center;
  gap: 8px;
}

.expiry-date {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 3px;
  background-color: #f0f0f0;
}

.renewal-form {
  margin: 20px 0;
}

.renewal-tips {
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}

/* 到期日期样式 */
.expired-date {
  color: #f56c6c;
  font-weight: 600;
  background-color: #fef0f0;
}

.expiring-date {
  color: #e6a23c;
  font-weight: 600;
  background-color: #fdf6ec;
}

.valid-date {
  color: #67c23a;
  background-color: #f0f9ff;
}

/* 表单样式 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-date-editor.el-input) {
  border-radius: 6px;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}

/* 描述列表样式 */
:deep(.el-descriptions__label) {
  font-weight: 600;
  color: #303133;
  width: 120px;
}

:deep(.el-descriptions__content) {
  color: #606266;
}

/* 滚动条样式 */
:deep(.el-scrollbar__view) {
  padding: 0;
}

/* 警告框样式 */
:deep(.el-alert) {
  border-radius: 6px;
}

:deep(.el-alert__title) {
  font-size: 14px;
}
</style>
