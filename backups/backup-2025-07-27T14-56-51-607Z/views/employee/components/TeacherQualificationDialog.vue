<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    :width="mode === 'view' ? '900px' : '1000px'"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="mode === 'view'" class="teacher-qualification-detail">
      <!-- 查看模式 -->
      <el-descriptions :column="2" border>
        <el-descriptions-item label="员工姓名">{{ teacherQualification?.employeeName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="员工ID">{{ teacherQualification?.employeeId || '-' }}</el-descriptions-item>
        <el-descriptions-item label="资格类型">
          <el-tag :type="getQualificationTypeTagType(teacherQualification?.qualificationType)" size="small">
            {{ teacherQualification?.qualificationTypeName || '-' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="学科类别">{{ teacherQualification?.subjectCategory || '-' }}</el-descriptions-item>
        <el-descriptions-item label="学科名称">{{ teacherQualification?.subjectName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="证书编号">{{ teacherQualification?.certificateNumber || '-' }}</el-descriptions-item>
        <el-descriptions-item label="认定机构" :span="2">{{ teacherQualification?.recognitionAuthority || '-' }}</el-descriptions-item>
        <el-descriptions-item label="认定日期">{{ teacherQualification?.recognitionDate || '-' }}</el-descriptions-item>
        <el-descriptions-item label="认定状态">
          <el-tag :type="getRecognitionStatusTagType(teacherQualification?.recognitionStatus)" size="small">
            {{ teacherQualification?.recognitionStatusName || '-' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="颁发日期">{{ teacherQualification?.issueDate || '-' }}</el-descriptions-item>
        <el-descriptions-item label="到期日期">
          <span v-if="teacherQualification?.expiryDate" :class="getExpiryDateClass(teacherQualification.expiryDate)">
            {{ teacherQualification.expiryDate }}
          </span>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="有效期">
          {{ teacherQualification?.validityPeriod ? `${teacherQualification.validityPeriod}个月` : '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="有效期状态">
          <el-tag v-if="teacherQualification?.validityStatus" :type="getValidityStatusTagType(teacherQualification.validityStatus)" size="small">
            {{ teacherQualification.validityStatusName }}
          </el-tag>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="注册编号">{{ teacherQualification?.registrationNumber || '-' }}</el-descriptions-item>
        <el-descriptions-item label="注册日期">{{ teacherQualification?.registrationDate || '-' }}</el-descriptions-item>
        <el-descriptions-item label="任教学段">{{ teacherQualification?.teachingLevel || '-' }}</el-descriptions-item>
        <el-descriptions-item label="任教范围">{{ teacherQualification?.teachingScope || '-' }}</el-descriptions-item>
        <el-descriptions-item label="续期次数">{{ teacherQualification?.renewalCount || 0 }}次</el-descriptions-item>
        <el-descriptions-item label="最后续期日期">{{ teacherQualification?.lastRenewalDate || '-' }}</el-descriptions-item>
        <el-descriptions-item label="下次续期日期">{{ teacherQualification?.nextRenewalDate || '-' }}</el-descriptions-item>
        <el-descriptions-item label="验证状态">
          <el-tag v-if="teacherQualification?.verificationStatus" :type="getVerificationStatusTagType(teacherQualification.verificationStatus)" size="small">
            {{ teacherQualification.verificationStatusName }}
          </el-tag>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="验证日期">{{ teacherQualification?.verificationDate || '-' }}</el-descriptions-item>
        <el-descriptions-item label="优先级">
          <el-tag v-if="teacherQualification?.priority" :type="getPriorityTagType(teacherQualification.priority)" size="small">
            {{ teacherQualification.priorityName }}
          </el-tag>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="激活状态">
          <el-tag v-if="teacherQualification?.isActive" type="success" size="small">激活</el-tag>
          <el-tag v-else type="info" size="small">停用</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="相关证书" :span="2">{{ teacherQualification?.relatedCertificates || '-' }}</el-descriptions-item>
        <el-descriptions-item label="验证备注" :span="2">{{ teacherQualification?.verificationNote || '-' }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ teacherQualification?.remarks || '-' }}</el-descriptions-item>
        <el-descriptions-item label="相关附件" :span="2">
          <el-button
            v-if="teacherQualification?.attachmentUrl"
            type="primary"
            link
            @click="handleViewAttachment(teacherQualification.attachmentUrl)"
          >
            查看附件
          </el-button>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ teacherQualification?.createTime || '-' }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ teacherQualification?.updateTime || '-' }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <div v-else class="teacher-qualification-form">
      <!-- 编辑/新增模式 -->
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="120px"
        label-position="right"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="员工ID" prop="employeeId">
              <el-input
                v-model="form.employeeId"
                placeholder="请输入员工ID"
                :disabled="mode === 'edit'"
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资格类型" prop="qualificationType">
              <el-select v-model="form.qualificationType" placeholder="请选择资格类型" style="width: 100%">
                <el-option
                  v-for="item in qualificationTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                 />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="学科类别" prop="subjectCategory">
              <el-select v-model="form.subjectCategory" placeholder="请选择学科类别" style="width: 100%">
                <el-option
                  v-for="item in subjectCategoryOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                 />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="学科名称" prop="subjectName">
              <el-input
                v-model="form.subjectName"
                placeholder="请输入学科名称"
                maxlength="100"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证书编号" prop="certificateNumber">
              <el-input
                v-model="form.certificateNumber"
                placeholder="请输入证书编号"
                maxlength="100"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="认定机构" prop="recognitionAuthority">
              <el-input
                v-model="form.recognitionAuthority"
                placeholder="请输入认定机构"
                maxlength="200"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="认定日期" prop="recognitionDate">
              <el-date-picker
                v-model="form.recognitionDate"
                type="date"
                placeholder="请选择认定日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="颁发日期" prop="issueDate">
              <el-date-picker
                v-model="form.issueDate"
                type="date"
                placeholder="请选择颁发日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="到期日期" prop="expiryDate">
              <el-date-picker
                v-model="form.expiryDate"
                type="date"
                placeholder="请选择到期日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="有效期" prop="validityPeriod">
              <el-input-number
                v-model="form.validityPeriod"
                placeholder="请输入有效期（月）"
                :min="1"
                :max="1200"
                controls-position="right"
                style="width: 100%"
                />
              <span style="margin-left: 8px; color: #909399;">个月</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="注册编号" prop="registrationNumber">
              <el-input
                v-model="form.registrationNumber"
                placeholder="请输入注册编号"
                maxlength="100"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="注册日期" prop="registrationDate">
              <el-date-picker
                v-model="form.registrationDate"
                type="date"
                placeholder="请选择注册日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任教学段" prop="teachingLevel">
              <el-input
                v-model="form.teachingLevel"
                placeholder="请输入任教学段"
                maxlength="100"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-select v-model="form.priority" placeholder="请选择优先级" style="width: 100%">
                <el-option
                  v-for="item in priorityOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                 />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="任教范围" prop="teachingScope">
              <el-input
                v-model="form.teachingScope"
                placeholder="请输入任教范围"
                maxlength="500"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="相关证书" prop="relatedCertificates">
              <el-input
                v-model="form.relatedCertificates"
                type="textarea"
                placeholder="请输入相关证书信息"
                :rows="3"
                maxlength="1000"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="相关附件" prop="attachmentUrl">
              <div class="attachment-upload">
                <el-input
                  v-model="form.attachmentUrl"
                  placeholder="请输入附件URL或上传文件"
                  style="margin-bottom: 10px"
                  />
                <el-upload
                  class="upload-demo"
                  :action="uploadAction"
                  :headers="uploadHeaders"
                  :on-success="handleUploadSuccess"
                  :on-error="handleUploadError"
                  :before-upload="beforeUpload"
                  :show-file-list="false"
                  accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                >
                  <el-button type="primary" size="small">
                    <el-icon><Upload /></el-icon>
                    上传附件
                  </el-button>
                  <template #tip>
                    <div class="el-upload__tip">
                      支持 PDF、图片、Word 文档，文件大小不超过 10MB
                    </div>
                  </template>
                </el-upload>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="特殊设置">
              <el-checkbox v-model="form.isActive">
                激活状态
              </el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remarks">
              <el-input
                v-model="form.remarks"
                type="textarea"
                placeholder="请输入备注信息"
                :rows="4"
                maxlength="1000"
                show-word-limit
                />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          v-if="mode !== 'view'"
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          {{ mode === 'add' ? '创建' : '更新' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'TeacherQualificationDialog'
})
/* eslint-disable @typescript-eslint/no-explicit-any, @typescript-eslint/no-unused-vars */

import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Upload } from '@element-plus/icons-vue'
import {
  teacherQualificationApi,
  qualificationTypeOptions,
  priorityOptions,
  subjectCategoryOptions
} from '@/api/teacherQualification'
import type {
  TeacherQualification,
  TeacherQualificationCreateRequest,
  TeacherQualificationUpdateRequest
} from '@/types/teacherQualification'

// Props
interface Props {
  visible: boolean
  teacherQualification: TeacherQualification | null
  mode: 'view' | 'add' | 'edit'
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  teacherQualification: null,
  mode: 'view'
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const loading = ref(false)
const formRef = ref<FormInstance>()

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 对话框标题
const dialogTitle = computed(() => {
  switch(props.mode: unknown) {
    case 'view':
      return '查看教师资格'
    case 'add':
      return '新增教师资格'
    case 'edit':
      return '编辑教师资格'
    default:
      return '教师资格'
  }
})

// 表单数据
const form = reactive<TeacherQualificationCreateRequest>({
  employeeId: '',
  qualificationType: 'KINDERGARTEN' as unknown,
  subjectCategory: '',
  subjectName: '',
  certificateNumber: '',
  recognitionAuthority: '',
  recognitionDate: '',
  issueDate: '',
  validityPeriod: undefined,
  expiryDate: '',
  registrationNumber: '',
  registrationDate: '',
  teachingLevel: '',
  teachingScope: '',
  attachmentUrl: '',
  isActive: true,
  priority: undefined,
  relatedCertificates: '',
  remarks: ''
})

// 表单验证规则
const formRules: FormRules = {
  employeeId: [
    { required: true, message: '请输入员工ID', trigger: 'blur' },
    { min: 1, max: 36, message: '员工ID长度在1到36个字符', trigger: 'blur' }
  ],
  qualificationType: [
    { required: true, message: '请选择资格类型', trigger: 'change' }
  ],
  subjectCategory: [
    { max: 50, message: '学科类别长度不能超过50个字符', trigger: 'blur' }
  ],
  subjectName: [
    { max: 100, message: '学科名称长度不能超过100个字符', trigger: 'blur' }
  ],
  certificateNumber: [
    { max: 100, message: '证书编号长度不能超过100个字符', trigger: 'blur' }
  ],
  recognitionAuthority: [
    { max: 200, message: '认定机构长度不能超过200个字符', trigger: 'blur' }
  ],
  recognitionDate: [
    {
      validator: (rule, value, callback) => {
        if (value && new Date(value) > new Date()) {
          callback(new Error('认定日期不能是未来时间'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  issueDate: [
    {
      validator: (rule, value, callback) => {
        if (value && new Date(value) > new Date()) {
          callback(new Error('颁发日期不能是未来时间'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  expiryDate: [
    {
      validator: (rule, value, callback) => {
        if(value && form.issueDate && value <= form.issueDate: unknown) {
          callback(new Error('到期日期必须晚于颁发日期'))
        } else if(value && form.recognitionDate && value <= form.recognitionDate: unknown) {
          callback(new Error('到期日期必须晚于认定日期'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  validityPeriod: [
    { type: 'number', min: 1, max: 1200, message: '有效期必须在1到1200个月之间', trigger: 'blur' }
  ],
  registrationNumber: [
    { max: 100, message: '注册编号长度不能超过100个字符', trigger: 'blur' }
  ],
  teachingLevel: [
    { max: 100, message: '任教学段长度不能超过100个字符', trigger: 'blur' }
  ],
  teachingScope: [
    { max: 500, message: '任教范围长度不能超过500个字符', trigger: 'blur' }
  ],
  relatedCertificates: [
    { max: 1000, message: '相关证书长度不能超过1000个字符', trigger: 'blur' }
  ],
  remarks: [
    { max: 1000, message: '备注长度不能超过1000个字符', trigger: 'blur' }
  ]
}

// 上传配置
const uploadAction = '/api/v1/files/upload'
const uploadHeaders = {
  'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
}

// 监听props变化，初始化表单数据
watch(
  () => [props.visible, props.teacherQualification, props.mode],
  ([visible, teacherQualification, mode]) => {
    if(visible && mode === 'edit' && teacherQualification: unknown) {
      // 编辑模式，填充表单数据
      Object.assign(form, {
        employeeId: (teacherQualification as unknown)?.employeeId,
        qualificationType: (teacherQualification as unknown)?.qualificationType,
        subjectCategory: (teacherQualification as unknown)?.subjectCategory || '',
        subjectName: (teacherQualification as unknown)?.subjectName || '',
        certificateNumber: (teacherQualification as unknown)?.certificateNumber || '',
        recognitionAuthority: (teacherQualification as unknown)?.recognitionAuthority || '',
        recognitionDate: (teacherQualification as unknown)?.recognitionDate || '',
        issueDate: (teacherQualification as unknown)?.issueDate || '',
        validityPeriod: (teacherQualification as unknown)?.validityPeriod,
        expiryDate: (teacherQualification as unknown)?.expiryDate || '',
        registrationNumber: (teacherQualification as unknown)?.registrationNumber || '',
        registrationDate: (teacherQualification as unknown)?.registrationDate || '',
        teachingLevel: (teacherQualification as unknown)?.teachingLevel || '',
        teachingScope: (teacherQualification as unknown)?.teachingScope || '',
        attachmentUrl: (teacherQualification as unknown)?.attachmentUrl || '',
        isActive: (teacherQualification as unknown)?.isActive !== false,
        priority: (teacherQualification as unknown)?.priority,
        relatedCertificates: (teacherQualification as unknown)?.relatedCertificates || '',
        remarks: (teacherQualification as unknown)?.remarks || ''
      })
    } else if(visible && mode === 'add': unknown) {
      // 新增模式，重置表单
      Object.assign(form, {
        employeeId: '',
        qualificationType: 'KINDERGARTEN',
        subjectCategory: '',
        subjectName: '',
        certificateNumber: '',
        recognitionAuthority: '',
        recognitionDate: '',
        issueDate: '',
        validityPeriod: undefined,
        expiryDate: '',
        registrationNumber: '',
        registrationDate: '',
        teachingLevel: '',
        teachingScope: '',
        attachmentUrl: '',
        isActive: true,
        priority: undefined,
        relatedCertificates: '',
        remarks: ''
      })
    }
  },
  { immediate: true }
)

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    if(props.mode === 'add': unknown) {
      await teacherQualificationApi.create(form)
      ElMessage.success('教师资格创建成功')
    } else if(props.mode === 'edit' && props.teacherQualification: unknown) {
      const updateData: TeacherQualificationUpdateRequest = {
        qualificationType: form.qualificationType,
        subjectCategory: form.subjectCategory,
        subjectName: form.subjectName,
        certificateNumber: form.certificateNumber,
        recognitionAuthority: form.recognitionAuthority,
        recognitionDate: form.recognitionDate,
        issueDate: form.issueDate,
        validityPeriod: form.validityPeriod,
        expiryDate: form.expiryDate,
        registrationNumber: form.registrationNumber,
        registrationDate: form.registrationDate,
        teachingLevel: form.teachingLevel,
        teachingScope: form.teachingScope,
        attachmentUrl: form.attachmentUrl,
        isActive: form.isActive,
        priority: form.priority,
        relatedCertificates: form.relatedCertificates,
        remarks: form.remarks
      }
      await teacherQualificationApi.update(props.teacherQualification.id!, updateData)
      ElMessage.success('教师资格更新成功')
    }

    emit('success')
    handleClose()
  } catch(_error: unknown) {
    console.error('提交失败:', error)
    if(error !== false: unknown) { // 不是表单验证错误
      ElMessage.error('操作失败')
    }
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  formRef.value?.resetFields()
}

// 查看附件
const handleViewAttachment = (url: string) => {
  window.open(url, '_blank')
}

// 文件上传成功
   
const handleUploadSuccess = (response: unknown) => {
  if(response.code === 200: unknown) {
    form.attachmentUrl = response.data.url
    ElMessage.success('文件上传成功')
  } else {
    ElMessage.error(response.message || '文件上传失败')
  }
}

// 文件上传失败
const handleUploadError = () => {
  ElMessage.error('文件上传失败')
}

// 上传前检查
const beforeUpload = (file: File) => {
  const isValidType = [
    'application/pdf',
    'image/jpeg',
    'image/jpg',
    'image/png',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ].includes(file.type)

  const isLt10M = file.size / 1024 / 1024 < 10

  if(!isValidType: unknown) {
    ElMessage.error('只能上传 PDF、图片、Word 文档格式的文件!')
    return false
  }
  if(!isLt10M: unknown) {
    ElMessage.error('上传文件大小不能超过 10MB!')
    return false
  }
  return true
}

// 获取资格类型标签类型
const getQualificationTypeTagType = (type?: string) => {
  switch(type: unknown) {
    case 'KINDERGARTEN':
      return 'success'
    case 'PRIMARY_SCHOOL':
      return 'info'
    case 'JUNIOR_HIGH_SCHOOL':
      return 'warning'
    case 'SENIOR_HIGH_SCHOOL':
      return 'danger'
    case 'VOCATIONAL_SCHOOL':
      return ''
    case 'HIGHER_EDUCATION':
      return 'success'
    default:
      return 'info'
  }
}

// 获取认定状态标签类型
const getRecognitionStatusTagType = (status?: string) => {
  switch(status: unknown) {
    case 'RECOGNIZED':
      return 'success'
    case 'PENDING':
      return 'warning'
    case 'REJECTED':
      return 'danger'
    case 'EXPIRED':
      return 'info'
    case 'SUSPENDED':
      return 'info'
    default:
      return ''
  }
}

// 获取有效期状态标签类型
const getValidityStatusTagType = (status?: string) => {
  switch(status: unknown) {
    case 'VALID':
      return 'success'
    case 'EXPIRING_SOON':
      return 'warning'
    case 'EXPIRED':
      return 'danger'
    case 'SUSPENDED':
      return 'info'
    default:
      return ''
  }
}

// 获取验证状态标签类型
const getVerificationStatusTagType = (status?: string) => {
  switch(status: unknown) {
    case 'VERIFIED':
      return 'success'
    case 'PENDING':
      return 'warning'
    case 'REJECTED':
      return 'danger'
    default:
      return ''
  }
}

// 获取优先级标签类型
const getPriorityTagType = (priority?: string) => {
  switch(priority: unknown) {
    case 'LOW':
      return 'info'
    case 'MEDIUM':
      return ''
    case 'HIGH':
      return 'warning'
    case 'CRITICAL':
      return 'danger'
    default:
      return ''
  }
}

// 获取到期日期样式类
const getExpiryDateClass = (expiryDate: string) => {
  const today = new Date()
  const expiry = new Date(expiryDate)
  const diffTime = expiry.getTime() - today.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if(diffDays < 0: unknown) {
    return 'expired-date'
  } else if(diffDays <= 30: unknown) {
    return 'expiring-date'
  } else {
    return 'valid-date'
  }
}
</script>

<style scoped>
.teacher-qualification-detail {
  padding: 20px 0;
}

.teacher-qualification-form {
  padding: 20px 0;
}

.dialog-footer {
  text-align: right;
}

.attachment-upload {
  width: 100%;
}

.upload-demo {
  width: 100%;
}

/* 到期日期样式 */
.expired-date {
  color: #f56c6c;
  font-weight: 600;
}

.expiring-date {
  color: #e6a23c;
  font-weight: 600;
}

.valid-date {
  color: #67c23a;
}

:deep(.el-upload__tip) {
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
  color: #303133;
}

:deep(.el-descriptions__content) {
  color: #606266;
}

/* 表单样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}

:deep(.el-date-editor.el-input) {
  border-radius: 6px;
}

/* 复选框样式 */
:deep(.el-checkbox) {
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .teacher-qualification-form .el-col {
    width: 100%;
    margin-bottom: 10px;
  }
}
</style>