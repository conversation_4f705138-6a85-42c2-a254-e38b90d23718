<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    :width="mode === 'view' ? '900px' : '1000px'"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="mode === 'view'" class="family-member-detail">
      <!-- 查看模式 -->
      <el-descriptions :column="2" border>
        <el-descriptions-item label="员工姓名">{{ familyMember?.employeeName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="员工ID">{{ familyMember?.employeeId || '-' }}</el-descriptions-item>
        <el-descriptions-item label="家庭成员姓名">{{ familyMember?.name || '-' }}</el-descriptions-item>
        <el-descriptions-item label="关系类型">
          <el-tag :type="getRelationshipTagType(familyMember?.relationship)" size="small">
            {{ familyMember?.relationshipName || '-' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="性别">
          <span v-if="familyMember?.gender">
            <el-icon v-if="familyMember.gender === 'MALE'" color="#409eff"><Male /></el-icon>
            <el-icon v-else color="#f56c6c"><Female /></el-icon>
            {{ familyMember.genderName }}
          </span>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="年龄">
          {{ familyMember?.age ? `${familyMember.age}岁` : '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="出生日期">{{ familyMember?.birthDate || '-' }}</el-descriptions-item>
        <el-descriptions-item label="身份证号">{{ familyMember?.idCard || '-' }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ familyMember?.phone || '-' }}</el-descriptions-item>
        <el-descriptions-item label="工作单位">{{ familyMember?.workUnit || '-' }}</el-descriptions-item>
        <el-descriptions-item label="职务">{{ familyMember?.position || '-' }}</el-descriptions-item>
        <el-descriptions-item label="政治面貌">{{ familyMember?.politicalStatus || '-' }}</el-descriptions-item>
        <el-descriptions-item label="学历">{{ familyMember?.education || '-' }}</el-descriptions-item>
        <el-descriptions-item label="健康状况">{{ familyMember?.healthStatus || '-' }}</el-descriptions-item>
        <el-descriptions-item label="紧急联系人">
          <el-tag v-if="familyMember?.isEmergencyContact" type="danger" size="small">是</el-tag>
          <span v-else>否</span>
        </el-descriptions-item>
        <el-descriptions-item label="被抚养人">
          <el-tag v-if="familyMember?.isDependent" type="success" size="small">是</el-tag>
          <span v-else>否</span>
        </el-descriptions-item>
        <el-descriptions-item label="住址" :span="2">
          {{ familyMember?.address || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">
          {{ familyMember?.remarks || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ familyMember?.createTime || '-' }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ familyMember?.updateTime || '-' }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <div v-else class="family-member-form">
      <!-- 编辑/新增模式 -->
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="120px"
        label-position="right"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="员工ID" prop="employeeId">
              <el-input
                v-model="form.employeeId"
                placeholder="请输入员工ID"
                :disabled="mode === 'edit'"
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="家庭成员姓名" prop="name">
              <el-input
                v-model="form.name"
                placeholder="请输入家庭成员姓名"
                maxlength="50"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="关系类型" prop="relationship">
              <el-select v-model="form.relationship" placeholder="请选择关系类型" style="width: 100%">
                <el-option
                  v-for="item in relationshipOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                 />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="性别" prop="gender">
              <el-radio-group v-model="form.gender">
                <el-radio value="MALE">
                  <el-icon color="#409eff"><Male /></el-icon>
                  男
                </el-radio>
                <el-radio value="FEMALE">
                  <el-icon color="#f56c6c"><Female /></el-icon>
                  女
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出生日期" prop="birthDate">
              <el-date-picker
                v-model="form.birthDate"
                type="date"
                placeholder="请选择出生日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="身份证号" prop="idCard">
              <el-input
                v-model="form.idCard"
                placeholder="请输入身份证号"
                maxlength="18"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input
                v-model="form.phone"
                placeholder="请输入联系电话"
                maxlength="20"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工作单位" prop="workUnit">
              <el-input
                v-model="form.workUnit"
                placeholder="请输入工作单位"
                maxlength="200"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职务" prop="position">
              <el-input
                v-model="form.position"
                placeholder="请输入职务"
                maxlength="100"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="政治面貌" prop="politicalStatus">
              <el-select v-model="form.politicalStatus" placeholder="请选择政治面貌" style="width: 100%">
                <el-option
                  v-for="item in politicalStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                 />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="学历" prop="education">
              <el-input
                v-model="form.education"
                placeholder="请输入学历"
                maxlength="50"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="健康状况" prop="healthStatus">
              <el-select v-model="form.healthStatus" placeholder="请选择健康状况" style="width: 100%">
                <el-option
                  v-for="item in healthStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                 />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="住址" prop="address">
              <el-input
                v-model="form.address"
                placeholder="请输入住址"
                maxlength="500"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="特殊设置">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-checkbox v-model="form.isEmergencyContact">
                    设为紧急联系人
                  </el-checkbox>
                </el-col>
                <el-col :span="12">
                  <el-checkbox v-model="form.isDependent">
                    设为被抚养人
                  </el-checkbox>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remarks">
              <el-input
                v-model="form.remarks"
                type="textarea"
                placeholder="请输入备注信息"
                :rows="4"
                maxlength="1000"
                show-word-limit
                />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          v-if="mode !== 'view'"
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          {{ mode === 'add' ? '创建' : '更新' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Male, Female } from '@element-plus/icons-vue'
import { 
  familyMemberApi, 
  relationshipOptions, 
  politicalStatusOptions, 
  healthStatusOptions 
} from '@/api/familyMember'
import type { 
  FamilyMember, 
  FamilyMemberCreateRequest,
  FamilyMemberUpdateRequest 
} from '@/types/familyMember'

// Props
interface Props {
  visible: boolean
  familyMember: FamilyMember | null
  mode: 'view' | 'add' | 'edit'
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  familyMember: null,
  mode: 'view'
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const loading = ref(false)
const formRef = ref<FormInstance>()

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 对话框标题
const dialogTitle = computed(() => {
  switch (props.mode) {
    case 'view':
      return '查看家庭成员'
    case 'add':
      return '新增家庭成员'
    case 'edit':
      return '编辑家庭成员'
    default:
      return '家庭成员'
  }
})

// 表单数据
const form = reactive<FamilyMemberCreateRequest>({
  employeeId: '',
  name: '',
  relationship: 'SPOUSE' as unknown, // 临时修复枚举类型
  gender: undefined,
  birthDate: '',
  idCard: '',
  phone: '',
  workUnit: '',
  position: '',
  address: '',
  isEmergencyContact: false,
  isDependent: false,
  politicalStatus: '',
  education: '',
  healthStatus: '',
  remarks: ''
})

// 身份证号验证
   
const validateIdCard = (rule: unknown, value: unknown, callback: unknown) => {
  if (!value) {
    callback()
    return
  }
  
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  if (!idCardRegex.test(value)) {
    callback(new Error('请输入正确的身份证号'))
  } else {
    callback()
  }
}

// 手机号验证
   
const validatePhone = (rule: unknown, value: unknown, callback: unknown) => {
  if (!value) {
    callback()
    return
  }
  
  const phoneRegex = /^1[3-9]\d{9}$/
  const landlineRegex = /^0\d{2,3}-?\d{7,8}$/
  
  if (!phoneRegex.test(value) && !landlineRegex.test(value)) {
    callback(new Error('请输入正确的手机号或座机号'))
  } else {
    callback()
  }
}

// 表单验证规则
const formRules: FormRules = {
  employeeId: [
    { required: true, message: '请输入员工ID', trigger: 'blur' },
    { min: 1, max: 36, message: '员工ID长度在1到36个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入家庭成员姓名', trigger: 'blur' },
    { min: 2, max: 50, message: '姓名长度在2到50个字符', trigger: 'blur' }
  ],
  relationship: [
    { required: true, message: '请选择关系类型', trigger: 'change' }
  ],
  birthDate: [
    {
      validator: (rule, value, callback) => {
        if (value && new Date(value) > new Date()) {
          callback(new Error('出生日期不能是未来时间'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  idCard: [
    { validator: validateIdCard, trigger: 'blur' }
  ],
  phone: [
    { validator: validatePhone, trigger: 'blur' }
  ],
  workUnit: [
    { max: 200, message: '工作单位长度不能超过200个字符', trigger: 'blur' }
  ],
  position: [
    { max: 100, message: '职务长度不能超过100个字符', trigger: 'blur' }
  ],
  address: [
    { max: 500, message: '住址长度不能超过500个字符', trigger: 'blur' }
  ],
  education: [
    { max: 50, message: '学历长度不能超过50个字符', trigger: 'blur' }
  ],
  remarks: [
    { max: 1000, message: '备注长度不能超过1000个字符', trigger: 'blur' }
  ]
}

// 监听props变化，初始化表单数据
watch(
  () => [props.visible, props.familyMember, props.mode],
  ([visible, familyMember, mode]) => {
    if (visible && mode === 'edit' && familyMember) {
      // 编辑模式，填充表单数据
      Object.assign(form, {
        employeeId: (familyMember as unknown)?.employeeId,
        name: (familyMember as unknown)?.name,
        relationship: (familyMember as unknown)?.relationship,
        gender: (familyMember as unknown)?.gender,
        birthDate: (familyMember as unknown)?.birthDate || '',
        idCard: (familyMember as unknown)?.idCard || '',
        phone: (familyMember as unknown)?.phone || '',
        workUnit: (familyMember as unknown)?.workUnit || '',
        position: (familyMember as unknown)?.position || '',
        address: (familyMember as unknown)?.address || '',
        isEmergencyContact: (familyMember as unknown)?.isEmergencyContact || false,
        isDependent: (familyMember as unknown)?.isDependent || false,
        politicalStatus: (familyMember as unknown)?.politicalStatus || '',
        education: (familyMember as unknown)?.education || '',
        healthStatus: (familyMember as unknown)?.healthStatus || '',
        remarks: (familyMember as unknown)?.remarks || ''
      })
    } else if (visible && mode === 'add') {
      // 新增模式，重置表单
      Object.assign(form, {
        employeeId: '',
        name: '',
        relationship: 'SPOUSE',
        gender: undefined,
        birthDate: '',
        idCard: '',
        phone: '',
        workUnit: '',
        position: '',
        address: '',
        isEmergencyContact: false,
        isDependent: false,
        politicalStatus: '',
        education: '',
        healthStatus: '',
        remarks: ''
      })
    }
  },
  { immediate: true }
)

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    if (props.mode === 'add') {
      await familyMemberApi.create(form)
      ElMessage.success('家庭成员创建成功')
    } else if (props.mode === 'edit' && props.familyMember) {
      const updateData: FamilyMemberUpdateRequest = {
        name: form.name,
        relationship: form.relationship,
        gender: form.gender,
        birthDate: form.birthDate,
        idCard: form.idCard,
        phone: form.phone,
        workUnit: form.workUnit,
        position: form.position,
        address: form.address,
        isEmergencyContact: form.isEmergencyContact,
        isDependent: form.isDependent,
        politicalStatus: form.politicalStatus,
        education: form.education,
        healthStatus: form.healthStatus,
        remarks: form.remarks
      }
      await familyMemberApi.update(props.familyMember.id!, updateData)
      ElMessage.success('家庭成员更新成功')
    }

    emit('success')
    handleClose()
  } catch (__error) {
    console.error('提交失败:', error)
    if (error !== false) { // 不是表单验证错误
      ElMessage.error('操作失败')
    }
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  formRef.value?.resetFields()
}

// 获取关系类型标签类型
const getRelationshipTagType = (relationship?: string) => {
  switch (relationship) {
    case 'SPOUSE':
      return 'danger'
    case 'CHILD':
      return 'success'
    case 'FATHER':
    case 'MOTHER':
      return 'warning'
    case 'BROTHER':
    case 'SISTER':
      return 'info'
    default:
      return ''
  }
}
</script>

<style scoped>
.family-member-detail {
  padding: 20px 0;
}

.family-member-form {
  padding: 20px 0;
}

.dialog-footer {
  text-align: right;
}

/* 表单样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}

:deep(.el-date-editor.el-input) {
  border-radius: 6px;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
  color: #303133;
}

:deep(.el-descriptions__content) {
  color: #606266;
}

/* 单选按钮样式 */
:deep(.el-radio) {
  margin-right: 30px;
}

:deep(.el-radio__label) {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
}

/* 复选框样式 */
:deep(.el-checkbox) {
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .family-member-form .el-col {
    width: 100%;
    margin-bottom: 10px;
  }
}
</style>
