<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="verification-dialog">
      <!-- 单个验证模式 -->
      <div v-if="!batchMode" class="single-verification">
        <div class="education-info">
          <h4>教育经历信息</h4>
          <el-descriptions :column="1" border size="small">
            <el-descriptions-item label="员工姓名">
              {{ educationHistory?.employeeName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="学历/学位">
              {{ educationHistory?.degree || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="毕业院校">
              {{ educationHistory?.graduationSchool || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="专业">
              {{ educationHistory?.major || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="毕业时间">
              {{ educationHistory?.endDate || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="当前状态">
              <el-tag :type="getVerificationStatusTagType(educationHistory?.verificationStatus)" size="small">
                {{ educationHistory?.verificationStatusName || '-' }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 批量验证模式 -->
      <div v-else class="batch-verification">
        <div class="batch-info">
          <h4>批量验证信息</h4>
          <p>您选择了 <strong>{{ selectedRows.length }}</strong> 条教育经历记录进行批量验证</p>
          
          <div class="selected-list">
            <el-scrollbar height="200px">
              <div v-for="item in selectedRows" :key="item.id" class="selected-item">
                <div class="item-info">
                  <span class="employee-name">{{ item.employeeName }}</span>
                  <span class="education-info">{{ item.graduationSchool }} - {{ item.degree }}</span>
                </div>
                <el-tag :type="getVerificationStatusTagType(item.verificationStatus)" size="small">
                  {{ item.verificationStatusName }}
                </el-tag>
              </div>
            </el-scrollbar>
          </div>
        </div>
      </div>

      <!-- 验证表单 -->
      <div class="verification-form">
        <el-form
          ref="formRef"
          :model="form"
          :rules="formRules"
          label-width="100px"
          label-position="right"
        >
          <el-form-item label="验证结果" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio value="VERIFIED">
                <el-icon color="#67c23a"><CircleCheck /></el-icon>
                验证通过
              </el-radio>
              <el-radio value="REJECTED">
                <el-icon color="#f56c6c"><CircleClose /></el-icon>
                验证失败
              </el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="验证备注" prop="note">
            <el-input
              v-model="form.note"
              type="textarea"
              :placeholder="form.status === 'VERIFIED' ? '请输入验证通过的备注信息（可选）' : '请输入验证失败的原因'"
              :rows="4"
              maxlength="500"
              show-word-limit
              />
          </el-form-item>
        </el-form>
      </div>

      <!-- 验证提示 -->
      <div class="verification-tips">
        <el-alert
          v-if="form.status === 'VERIFIED'"
          title="验证通过后，该教育经历将被标记为已验证状态"
          type="success"
          :closable="false"
          show-icon
         />
        <el-alert
          v-else-if="form.status === 'REJECTED'"
          title="验证失败后，该教育经历将被标记为验证失败状态，请填写失败原因"
          type="error"
          :closable="false"
          show-icon
         />
        <el-alert
          v-else
          title="请选择验证结果"
          type="info"
          :closable="false"
          show-icon
         />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          :disabled="!form.status"
          @click="handleSubmit"
        >
          确认验证
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
 
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { CircleCheck, CircleClose } from '@element-plus/icons-vue'
import { educationHistoryApi } from '@/api/educationHistory'
import type { EducationHistory } from '@/types/educationHistory'

// Props
interface Props {
  visible: boolean
  educationHistory: EducationHistory | null
  batchMode: boolean
  selectedRows: EducationHistory[]
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  educationHistory: null,
  batchMode: false,
  selectedRows: () => []
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const loading = ref(false)
const formRef = ref<FormInstance>()

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 对话框标题
const dialogTitle = computed(() => {
  return props.batchMode ? '批量验证教育经历' : '验证教育经历'
})

// 表单数据
const form = reactive({
  status: '' as 'VERIFIED' | 'REJECTED' | '',
  note: ''
})

// 表单验证规则
const formRules: FormRules = {
  status: [
    { required: true, message: '请选择验证结果', trigger: 'change' }
  ],
  note: [
    {
      validator: (rule, value, callback) => {
        if (form.status === 'REJECTED' && !value.trim()) {
          callback(new Error('验证失败时必须填写失败原因'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    },
    { max: 500, message: '备注长度不能超过500个字符', trigger: 'blur' }
  ]
}

// 监听对话框显示状态，重置表单
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      // 重置表单
      form.status = ''
      form.note = ''
    }
  }
)

// 提交验证
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    if (props.batchMode) {
      // 批量验证
      const ids = props.selectedRows.map(row => row.id!).filter(id => id)
      await educationHistoryApi.batchVerify(ids, form.status as 'VERIFIED' | 'REJECTED', form.note)
      ElMessage.success(`批量验证完成，共处理 ${ids.length} 条记录`)
    } else {
      // 单个验证
      if (props.educationHistory?.id) {
        await educationHistoryApi.verify(
          props.educationHistory.id, 
          form.status as 'VERIFIED' | 'REJECTED', 
          form.note
        )
        ElMessage.success('验证完成')
      }
    }

    emit('success')
    handleClose()
  } catch (__error) {
    if (error !== false) { // 不是表单验证错误
      ElMessage.error('验证操作失败')
    }
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  formRef.value?.resetFields()
}

// 获取验证状态标签类型
const getVerificationStatusTagType = (status?: string) => {
  switch (status) {
    case 'VERIFIED':
      return 'success'
    case 'PENDING':
      return 'warning'
    case 'REJECTED':
      return 'danger'
    default:
      return ''
  }
}
</script>

<style scoped>
.verification-dialog {
  padding: 10px 0;
}

.education-info,
.batch-info {
  margin-bottom: 20px;
}

.education-info h4,
.batch-info h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.batch-info p {
  margin: 0 0 12px 0;
  color: #606266;
  font-size: 14px;
}

.selected-list {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #fafafa;
}

.selected-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
}

.selected-item:last-child {
  border-bottom: none;
}

.item-info {
  flex: 1;
}

.employee-name {
  font-weight: 600;
  color: #303133;
  margin-right: 12px;
}

.education-info {
  color: #606266;
  font-size: 14px;
}

.verification-form {
  margin: 20px 0;
}

.verification-tips {
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}

/* 单选按钮样式 */
:deep(.el-radio) {
  margin-right: 30px;
  margin-bottom: 10px;
}

:deep(.el-radio__label) {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
}

/* 表单样式 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}

/* 描述列表样式 */
:deep(.el-descriptions__label) {
  font-weight: 600;
  color: #303133;
  width: 100px;
}

:deep(.el-descriptions__content) {
  color: #606266;
}

/* 滚动条样式 */
:deep(.el-scrollbar__view) {
  padding: 0;
}

/* 警告框样式 */
:deep(.el-alert) {
  border-radius: 6px;
}

:deep(.el-alert__title) {
  font-size: 14px;
}
</style>
