<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    :width="mode === 'view' ? '900px' : '1000px'"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="mode === 'view'" class="vocational-qualification-detail">
      <!-- 查看模式 -->
      <el-descriptions :column="2" border>
        <el-descriptions-item label="员工姓名">{{ vocationalQualification?.employeeName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="员工ID">{{ vocationalQualification?.employeeId || '-' }}</el-descriptions-item>
        <el-descriptions-item label="证书名称" :span="2">{{ vocationalQualification?.certificateName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="证书类型">
          <el-tag :type="getCertificateTypeTagType(vocationalQualification?.certificateType)" size="small">
            {{ vocationalQualification?.certificateTypeName || '-' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="证书编号">{{ vocationalQualification?.certificateNumber || '-' }}</el-descriptions-item>
        <el-descriptions-item label="颁发机构" :span="2">{{ vocationalQualification?.issuingAuthority || '-' }}</el-descriptions-item>
        <el-descriptions-item label="颁发日期">{{ vocationalQualification?.issueDate || '-' }}</el-descriptions-item>
        <el-descriptions-item label="到期日期">
          <span v-if="vocationalQualification?.expiryDate" :class="getExpiryDateClass(vocationalQualification.expiryDate)">
            {{ vocationalQualification.expiryDate }}
          </span>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="有效期">
          {{ vocationalQualification?.validityPeriod ? `${vocationalQualification.validityPeriod}个月` : '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="有效期状态">
          <el-tag :type="getValidityStatusTagType(vocationalQualification?.validityStatus)" size="small">
            {{ vocationalQualification?.validityStatusName || '-' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="证书等级">{{ vocationalQualification?.level || '-' }}</el-descriptions-item>
        <el-descriptions-item label="适用范围">{{ vocationalQualification?.scope || '-' }}</el-descriptions-item>
        <el-descriptions-item label="是否需要续期">
          <el-tag v-if="vocationalQualification?.renewalRequired" type="warning" size="small">需要续期</el-tag>
          <el-tag v-else type="info" size="small">无需续期</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="续期次数">{{ vocationalQualification?.renewalCount || 0 }}次</el-descriptions-item>
        <el-descriptions-item label="最后续期日期">{{ vocationalQualification?.lastRenewalDate || '-' }}</el-descriptions-item>
        <el-descriptions-item label="下次续期日期">{{ vocationalQualification?.nextRenewalDate || '-' }}</el-descriptions-item>
        <el-descriptions-item label="验证状态">
          <el-tag v-if="vocationalQualification?.verificationStatus" :type="getVerificationStatusTagType(vocationalQualification.verificationStatus)" size="small">
            {{ vocationalQualification.verificationStatusName }}
          </el-tag>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="验证日期">{{ vocationalQualification?.verificationDate || '-' }}</el-descriptions-item>
        <el-descriptions-item label="优先级">
          <el-tag v-if="vocationalQualification?.priority" :type="getPriorityTagType(vocationalQualification.priority)" size="small">
            {{ vocationalQualification.priorityName }}
          </el-tag>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="激活状态">
          <el-tag v-if="vocationalQualification?.isActive" type="success" size="small">激活</el-tag>
          <el-tag v-else type="info" size="small">停用</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="相关技能" :span="2">{{ vocationalQualification?.relatedSkills || '-' }}</el-descriptions-item>
        <el-descriptions-item label="验证备注" :span="2">{{ vocationalQualification?.verificationNote || '-' }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ vocationalQualification?.remarks || '-' }}</el-descriptions-item>
        <el-descriptions-item label="相关附件" :span="2">
          <el-button
            v-if="vocationalQualification?.attachmentUrl"
            type="primary"
            link
            @click="handleViewAttachment(vocationalQualification.attachmentUrl)"
          >
            查看附件
          </el-button>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ vocationalQualification?.createTime || '-' }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ vocationalQualification?.updateTime || '-' }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <div v-else class="vocational-qualification-form">
      <!-- 编辑/新增模式 -->
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="120px"
        label-position="right"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="员工ID" prop="employeeId">
              <el-input
                v-model="form.employeeId"
                placeholder="请输入员工ID"
                :disabled="mode === 'edit'"
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证书类型" prop="certificateType">
              <el-select v-model="form.certificateType" placeholder="请选择证书类型" style="width: 100%">
                <el-option
                  v-for="item in certificateTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                 />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="证书名称" prop="certificateName">
              <el-input
                v-model="form.certificateName"
                placeholder="请输入证书名称"
                maxlength="200"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证书编号" prop="certificateNumber">
              <el-input
                v-model="form.certificateNumber"
                placeholder="请输入证书编号"
                maxlength="100"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="颁发机构" prop="issuingAuthority">
              <el-input
                v-model="form.issuingAuthority"
                placeholder="请输入颁发机构"
                maxlength="200"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="颁发日期" prop="issueDate">
              <el-date-picker
                v-model="form.issueDate"
                type="date"
                placeholder="请选择颁发日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="到期日期" prop="expiryDate">
              <el-date-picker
                v-model="form.expiryDate"
                type="date"
                placeholder="请选择到期日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="有效期" prop="validityPeriod">
              <el-input-number
                v-model="form.validityPeriod"
                placeholder="请输入有效期（月）"
                :min="1"
                :max="1200"
                controls-position="right"
                style="width: 100%"
                />
              <span style="margin-left: 8px; color: #909399;">个月</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证书等级" prop="level">
              <el-input
                v-model="form.level"
                placeholder="请输入证书等级"
                maxlength="50"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-select v-model="form.priority" placeholder="请选择优先级" style="width: 100%">
                <el-option
                  v-for="item in priorityOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                 />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="适用范围" prop="scope">
              <el-input
                v-model="form.scope"
                placeholder="请输入适用范围"
                maxlength="500"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="相关技能" prop="relatedSkills">
              <el-input
                v-model="form.relatedSkills"
                type="textarea"
                placeholder="请输入相关技能"
                :rows="3"
                maxlength="1000"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="相关附件" prop="attachmentUrl">
              <div class="attachment-upload">
                <el-input
                  v-model="form.attachmentUrl"
                  placeholder="请输入附件URL或上传文件"
                  style="margin-bottom: 10px"
                  />
                <el-upload
                  class="upload-demo"
                  :action="uploadAction"
                  :headers="uploadHeaders"
                  :on-success="handleUploadSuccess"
                  :on-error="handleUploadError"
                  :before-upload="beforeUpload"
                  :show-file-list="false"
                  accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                >
                  <el-button type="primary" size="small">
                    <el-icon><Upload /></el-icon>
                    上传附件
                  </el-button>
                  <template #tip>
                    <div class="el-upload__tip">
                      支持 PDF、图片、Word 文档，文件大小不超过 10MB
                    </div>
                  </template>
                </el-upload>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="特殊设置">
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-checkbox v-model="form.renewalRequired">
                    需要续期
                  </el-checkbox>
                </el-col>
                <el-col :span="8">
                  <el-checkbox v-model="form.isActive">
                    激活状态
                  </el-checkbox>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remarks">
              <el-input
                v-model="form.remarks"
                type="textarea"
                placeholder="请输入备注信息"
                :rows="4"
                maxlength="1000"
                show-word-limit
                />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          v-if="mode !== 'view'"
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          {{ mode === 'add' ? '创建' : '更新' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Upload } from '@element-plus/icons-vue'
import { 
  vocationalQualificationApi, 
  certificateTypeOptions, 
  priorityOptions 
} from '@/api/vocationalQualification'
import type { 
  VocationalQualification, 
  VocationalQualificationCreateRequest,
  VocationalQualificationUpdateRequest 
} from '@/types/vocationalQualification'

// Props
interface Props {
  visible: boolean
  vocationalQualification: VocationalQualification | null
  mode: 'view' | 'add' | 'edit'
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  vocationalQualification: null,
  mode: 'view'
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const loading = ref(false)
const formRef = ref<FormInstance>()

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 对话框标题
const dialogTitle = computed(() => {
  switch (props.mode) {
    case 'view':
      return '查看职业资格'
    case 'add':
      return '新增职业资格'
    case 'edit':
      return '编辑职业资格'
    default:
      return '职业资格'
  }
})

// 表单数据
const form = reactive<VocationalQualificationCreateRequest>({
  employeeId: '',
  certificateName: '',
  certificateType: 'SKILL_CERTIFICATE' as unknown,
  certificateNumber: '',
  issuingAuthority: '',
  issueDate: '',
  expiryDate: '',
  validityPeriod: undefined,
  level: '',
  scope: '',
  renewalRequired: false,
  attachmentUrl: '',
  isActive: true,
  priority: undefined,
  relatedSkills: '',
  remarks: ''
})

// 表单验证规则
const formRules: FormRules = {
  employeeId: [
    { required: true, message: '请输入员工ID', trigger: 'blur' },
    { min: 1, max: 36, message: '员工ID长度在1到36个字符', trigger: 'blur' }
  ],
  certificateName: [
    { required: true, message: '请输入证书名称', trigger: 'blur' },
    { min: 2, max: 200, message: '证书名称长度在2到200个字符', trigger: 'blur' }
  ],
  certificateType: [
    { required: true, message: '请选择证书类型', trigger: 'change' }
  ],
  certificateNumber: [
    { max: 100, message: '证书编号长度不能超过100个字符', trigger: 'blur' }
  ],
  issuingAuthority: [
    { max: 200, message: '颁发机构长度不能超过200个字符', trigger: 'blur' }
  ],
  issueDate: [
    {
      validator: (rule, value, callback) => {
        if (value && new Date(value) > new Date()) {
          callback(new Error('颁发日期不能是未来时间'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  expiryDate: [
    {
      validator: (rule, value, callback) => {
        if (value && form.issueDate && value <= form.issueDate) {
          callback(new Error('到期日期必须晚于颁发日期'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  validityPeriod: [
    { type: 'number', min: 1, max: 1200, message: '有效期必须在1到1200个月之间', trigger: 'blur' }
  ],
  level: [
    { max: 50, message: '证书等级长度不能超过50个字符', trigger: 'blur' }
  ],
  scope: [
    { max: 500, message: '适用范围长度不能超过500个字符', trigger: 'blur' }
  ],
  relatedSkills: [
    { max: 1000, message: '相关技能长度不能超过1000个字符', trigger: 'blur' }
  ],
  remarks: [
    { max: 1000, message: '备注长度不能超过1000个字符', trigger: 'blur' }
  ]
}

// 上传配置
const uploadAction = '/api/v1/files/upload'
const uploadHeaders = {
  'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
}

// 监听props变化，初始化表单数据
watch(
  () => [props.visible, props.vocationalQualification, props.mode],
  ([visible, vocationalQualification, mode]) => {
    if (visible && mode === 'edit' && vocationalQualification) {
      // 编辑模式，填充表单数据
      Object.assign(form, {
        employeeId: (vocationalQualification as unknown)?.employeeId,
        certificateName: (vocationalQualification as unknown)?.certificateName,
        certificateType: (vocationalQualification as unknown)?.certificateType,
        certificateNumber: (vocationalQualification as unknown)?.certificateNumber || '',
        issuingAuthority: (vocationalQualification as unknown)?.issuingAuthority || '',
        issueDate: (vocationalQualification as unknown)?.issueDate || '',
        expiryDate: (vocationalQualification as unknown)?.expiryDate || '',
        validityPeriod: (vocationalQualification as unknown)?.validityPeriod,
        level: (vocationalQualification as unknown)?.level || '',
        scope: (vocationalQualification as unknown)?.scope || '',
        renewalRequired: (vocationalQualification as unknown)?.renewalRequired || false,
        attachmentUrl: (vocationalQualification as unknown)?.attachmentUrl || '',
        isActive: (vocationalQualification as unknown)?.isActive !== false,
        priority: (vocationalQualification as unknown)?.priority,
        relatedSkills: (vocationalQualification as unknown)?.relatedSkills || '',
        remarks: (vocationalQualification as unknown)?.remarks || ''
      })
    } else if (visible && mode === 'add') {
      // 新增模式，重置表单
      Object.assign(form, {
        employeeId: '',
        certificateName: '',
        certificateType: 'SKILL_CERTIFICATE',
        certificateNumber: '',
        issuingAuthority: '',
        issueDate: '',
        expiryDate: '',
        validityPeriod: undefined,
        level: '',
        scope: '',
        renewalRequired: false,
        attachmentUrl: '',
        isActive: true,
        priority: undefined,
        relatedSkills: '',
        remarks: ''
      })
    }
  },
  { immediate: true }
)

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    if (props.mode === 'add') {
      await vocationalQualificationApi.create(form)
      ElMessage.success('职业资格创建成功')
    } else if (props.mode === 'edit' && props.vocationalQualification) {
      const updateData: VocationalQualificationUpdateRequest = {
        certificateName: form.certificateName,
        certificateType: form.certificateType,
        certificateNumber: form.certificateNumber,
        issuingAuthority: form.issuingAuthority,
        issueDate: form.issueDate,
        expiryDate: form.expiryDate,
        validityPeriod: form.validityPeriod,
        level: form.level,
        scope: form.scope,
        renewalRequired: form.renewalRequired,
        attachmentUrl: form.attachmentUrl,
        isActive: form.isActive,
        priority: form.priority,
        relatedSkills: form.relatedSkills,
        remarks: form.remarks
      }
      await vocationalQualificationApi.update(props.vocationalQualification.id!, updateData)
      ElMessage.success('职业资格更新成功')
    }

    emit('success')
    handleClose()
  } catch (__error) {
    if (error !== false) { // 不是表单验证错误
      ElMessage.error('操作失败')
    }
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  formRef.value?.resetFields()
}

// 查看附件
const handleViewAttachment = (url: string) => {
  window.open(url, '_blank')
}

// 文件上传成功
   
const handleUploadSuccess = (response: unknown) => {
  if (response.code === 200) {
    form.attachmentUrl = response.data.url
    ElMessage.success('文件上传成功')
  } else {
    ElMessage.error(response.message || '文件上传失败')
  }
}

// 文件上传失败
const handleUploadError = () => {
  ElMessage.error('文件上传失败')
}

// 上传前检查
const beforeUpload = (file: File) => {
  const isValidType = [
    'application/pdf',
    'image/jpeg',
    'image/jpg',
    'image/png',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ].includes(file.type)
  
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isValidType) {
    ElMessage.error('只能上传 PDF、图片、Word 文档格式的文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过 10MB!')
    return false
  }
  return true
}

// 获取证书类型标签类型
const getCertificateTypeTagType = (type?: string) => {
  switch (type) {
    case 'SKILL_CERTIFICATE':
      return 'success'
    case 'PROFESSIONAL_LICENSE':
      return 'warning'
    case 'TITLE_CERTIFICATE':
      return 'info'
    case 'TRAINING_CERTIFICATE':
      return ''
    default:
      return 'info'
  }
}

// 获取有效期状态标签类型
const getValidityStatusTagType = (status?: string) => {
  switch (status) {
    case 'VALID':
      return 'success'
    case 'EXPIRING_SOON':
      return 'warning'
    case 'EXPIRED':
      return 'danger'
    case 'SUSPENDED':
      return 'info'
    default:
      return ''
  }
}

// 获取验证状态标签类型
const getVerificationStatusTagType = (status?: string) => {
  switch (status) {
    case 'VERIFIED':
      return 'success'
    case 'PENDING':
      return 'warning'
    case 'REJECTED':
      return 'danger'
    default:
      return ''
  }
}

// 获取优先级标签类型
const getPriorityTagType = (priority?: string) => {
  switch (priority) {
    case 'LOW':
      return 'info'
    case 'MEDIUM':
      return ''
    case 'HIGH':
      return 'warning'
    case 'CRITICAL':
      return 'danger'
    default:
      return ''
  }
}

// 获取到期日期样式类
const getExpiryDateClass = (expiryDate: string) => {
  const today = new Date()
  const expiry = new Date(expiryDate)
  const diffTime = expiry.getTime() - today.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) {
    return 'expired-date'
  } else if (diffDays <= 30) {
    return 'expiring-date'
  } else {
    return 'valid-date'
  }
}
</script>

<style scoped>
.vocational-qualification-detail {
  padding: 20px 0;
}

.vocational-qualification-form {
  padding: 20px 0;
}

.dialog-footer {
  text-align: right;
}

.attachment-upload {
  width: 100%;
}

.upload-demo {
  width: 100%;
}

/* 到期日期样式 */
.expired-date {
  color: #f56c6c;
  font-weight: 600;
}

.expiring-date {
  color: #e6a23c;
  font-weight: 600;
}

.valid-date {
  color: #67c23a;
}

:deep(.el-upload__tip) {
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
  color: #303133;
}

:deep(.el-descriptions__content) {
  color: #606266;
}

/* 表单样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}

:deep(.el-date-editor.el-input) {
  border-radius: 6px;
}

/* 复选框样式 */
:deep(.el-checkbox) {
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .vocational-qualification-form .el-col {
    width: 100%;
    margin-bottom: 10px;
  }
}
</style>
