<template>
  <el-dialog
    v-model="dialogVisible"
    title="数据变更对比"
    width="1200px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="dataChangeLog" class="data-comparison">
      <!-- 对比信息 -->
      <div class="comparison-info">
        <el-descriptions :column="3" border size="small">
          <el-descriptions-item label="表名">{{ dataChangeLog.tableName }}</el-descriptions-item>
          <el-descriptions-item label="记录ID">{{ dataChangeLog.recordId }}</el-descriptions-item>
          <el-descriptions-item label="操作时间">{{ dataChangeLog.createTime }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 对比结果 -->
      <div v-if="comparisonResult" class="comparison-result">
        <div class="comparison-summary">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-statistic title="变更字段数" :value="comparisonResult.changeCount"  />
            </el-col>
            <el-col :span="6">
              <el-statistic title="变更比例" :value="comparisonResult.changePercentage" suffix="%" :precision="2"  />
            </el-col>
            <el-col :span="6">
              <el-statistic title="总字段数" :value="getTotalFieldCount()"  />
            </el-col>
            <el-col :span="6">
              <el-statistic title="未变更字段" :value="getTotalFieldCount() - comparisonResult.changeCount"  />
            </el-col>
          </el-row>
        </div>

        <!-- 变更字段详情 -->
        <div class="changed-fields">
          <h4>变更字段详情</h4>
          <el-table :data="comparisonResult.changedFields" stripe style="width: 100%">
            <el-table-column prop="fieldName" label="字段名" width="150"  />
            <el-table-column prop="fieldComment" label="字段注释" width="150"  />
            <el-table-column prop="changeType" label="变更类型" width="100" align="center">
              <template #default="scope">
                <el-tag :type="getFieldChangeTypeTagType(scope.row.changeType)" size="small">
                  {{ getFieldChangeTypeName(scope.row.changeType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="beforeValue" label="变更前" show-overflow-tooltip>
              <template #default="scope">
                <div class="field-value before-value">
                  {{ formatFieldValue(scope.row.beforeValue) }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="afterValue" label="变更后" show-overflow-tooltip>
              <template #default="scope">
                <div class="field-value after-value">
                  {{ formatFieldValue(scope.row.afterValue) }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 完整数据对比 -->
        <div class="full-data-comparison">
          <h4>完整数据对比</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="data-panel">
                <div class="panel-header">
                  <h5>变更前数据</h5>
                  <el-button type="primary" size="small" @click="copyToClipboard(JSON.stringify(comparisonResult.beforeData, null, 2))">
                    <el-icon><DocumentCopy /></el-icon>
                    复制
                  </el-button>
                </div>
                <div class="data-content">
                  <pre class="json-data">{{ JSON.stringify(comparisonResult.beforeData, null, 2) }}</pre>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="data-panel">
                <div class="panel-header">
                  <h5>变更后数据</h5>
                  <el-button type="primary" size="small" @click="copyToClipboard(JSON.stringify(comparisonResult.afterData, null, 2))">
                    <el-icon><DocumentCopy /></el-icon>
                    复制
                  </el-button>
                </div>
                <div class="data-content">
                  <pre class="json-data">{{ JSON.stringify(comparisonResult.afterData, null, 2) }}</pre>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-else-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated  />
      </div>

      <!-- 无对比数据 -->
      <div v-else class="no-comparison">
        <el-empty description="无法进行数据对比，缺少变更前或变更后数据"  />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button v-if="comparisonResult" type="primary" @click="handleExportComparison">
          <el-icon><Download /></el-icon>
          导出对比报告
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'DataComparisonDialog'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { DocumentCopy, Download } from '@element-plus/icons-vue'
import { dataChangeLogApi } from '@/api/dataChangeLog'
import type { DataChangeLog, DataComparisonResult } from '@/types/dataChangeLog'

// Props
interface Props {
  visible: boolean
  dataChangeLog: DataChangeLog | null
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  dataChangeLog: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const loading = ref(false)
const comparisonResult = ref<DataComparisonResult | null>(null)

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 监听对话框显示状态，加载对比数据
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.dataChangeLog?.id) {
      loadComparisonData()
    } else {
      comparisonResult.value = null
    }
  }
)

// 加载对比数据
const loadComparisonData = async () => {
  if (!props.dataChangeLog?.id) return
  
  try {
    loading.value = true
    comparisonResult.value = await dataChangeLogApi.compareData(props.dataChangeLog.id) as unknown // 修复API类型不匹配
  } catch (__error) {
    console.error('加载对比数据失败:', error)
    ElMessage.error('加载对比数据失败')
  } finally {
    loading.value = false
  }
}

// 获取总字段数
const getTotalFieldCount = () => {
  if (!comparisonResult.value) return 0
  
  const beforeFields = Object.keys(comparisonResult.value.beforeData || {})
  const afterFields = Object.keys(comparisonResult.value.afterData || {})
  const allFields = new Set([...beforeFields, ...afterFields])
  return allFields.size
}

// 格式化字段值
   
const formatFieldValue = (value: unknown) => {
  if (value === null) return 'null'
  if (value === undefined) return 'undefined'
  if (typeof value === 'string') return value
  if (typeof value === 'object') return JSON.stringify(value)
  return String(value)
}

// 复制到剪贴板
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('已复制到剪贴板')
  } catch (__error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}

// 导出对比报告
const handleExportComparison = async () => {
  if (!props.dataChangeLog?.id) return
  
  try {
    const blob = await dataChangeLogApi.exportComparisonReport([props.dataChangeLog.id])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `数据对比报告_${props.dataChangeLog.tableName}_${props.dataChangeLog.recordId}_${new Date().toISOString().slice(0, 10)}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('导出对比报告成功')
  } catch (__error) {
    console.error('导出对比报告失败:', error)
    ElMessage.error('导出对比报告失败')
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  comparisonResult.value = null
}

// 获取字段变更类型标签类型
const getFieldChangeTypeTagType = (type: string) => {
  switch (type) {
    case 'ADDED':
      return 'success'
    case 'MODIFIED':
      return 'warning'
    case 'REMOVED':
      return 'danger'
    default:
      return ''
  }
}

// 获取字段变更类型名称
const getFieldChangeTypeName = (type: string) => {
  switch (type) {
    case 'ADDED':
      return '新增'
    case 'MODIFIED':
      return '修改'
    case 'REMOVED':
      return '删除'
    default:
      return type
  }
}
</script>

<style scoped>
.data-comparison {
  padding: 10px 0;
}

.comparison-info {
  margin-bottom: 20px;
}

.comparison-summary {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.changed-fields {
  margin-bottom: 30px;
}

.changed-fields h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-left: 4px solid #409eff;
  padding-left: 10px;
}

.full-data-comparison h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-left: 4px solid #409eff;
  padding-left: 10px;
}

.data-panel {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.panel-header h5 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.data-content {
  height: 400px;
  overflow-y: auto;
}

.json-data {
  margin: 0;
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  white-space: pre-wrap;
  word-break: break-all;
}

.field-value {
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
}

.before-value {
  background-color: #fef0f0;
  color: #f56c6c;
}

.after-value {
  background-color: #f0f9ff;
  color: #409eff;
}

.loading-container {
  padding: 40px 0;
}

.no-comparison {
  padding: 40px 0;
  text-align: center;
}

.dialog-footer {
  text-align: right;
}

/* 描述列表样式 */
:deep(.el-descriptions__label) {
  font-weight: 600;
  color: #303133;
}

:deep(.el-descriptions__content) {
  color: #606266;
}

/* 表格样式 */
:deep(.el-table) {
  border-radius: 6px;
  overflow: hidden;
}

:deep(.el-table__header th) {
  background-color: #f8f9fa;
  color: #303133;
  font-weight: 600;
}

/* 统计数字样式 */
:deep(.el-statistic__content) {
  font-size: 24px;
  font-weight: 600;
}

:deep(.el-statistic__title) {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

/* 滚动条样式 */
.data-content::-webkit-scrollbar {
  width: 6px;
}

.data-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.data-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.data-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
