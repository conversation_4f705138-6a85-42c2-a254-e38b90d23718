<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="approval-dialog">
      <!-- 单个审批模式 -->
      <div v-if="!batchMode" class="single-approval">
        <div class="awards-punishments-info">
          <h4>奖惩记录信息</h4>
          <el-descriptions :column="1" border size="small">
            <el-descriptions-item label="员工姓名">
              {{ awardsPunishments?.employeeName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="奖惩类型">
              <el-tag :type="getTypeTagType(awardsPunishments?.type)" size="small">
                {{ awardsPunishments?.typeName || '-' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="奖惩类别">
              <el-tag :type="getCategoryTagType(awardsPunishments?.category)" size="small">
                {{ awardsPunishments?.categoryName || '-' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="奖惩标题">
              {{ awardsPunishments?.title || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="奖惩日期">
              {{ awardsPunishments?.awardDate || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="颁发单位">
              {{ awardsPunishments?.issuingUnit || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="当前状态">
              <el-tag :type="getApprovalStatusTagType(awardsPunishments?.approvalStatus)" size="small">
                {{ awardsPunishments?.approvalStatusName || '-' }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 批量审批模式 -->
      <div v-else class="batch-approval">
        <div class="batch-info">
          <h4>批量审批信息</h4>
          <p>您选择了 <strong>{{ selectedRows.length }}</strong> 条奖惩记录进行批量审批</p>
          
          <div class="selected-list">
            <el-scrollbar height="200px">
              <div v-for="item in selectedRows" :key="item.id" class="selected-item">
                <div class="item-info">
                  <span class="employee-name">{{ item.employeeName }}</span>
                  <span class="awards-punishments-info">{{ item.title }}</span>
                </div>
                <div class="item-tags">
                  <el-tag :type="getTypeTagType(item.type)" size="small">
                    {{ item.typeName }}
                  </el-tag>
                  <el-tag :type="getApprovalStatusTagType(item.approvalStatus)" size="small" style="margin-left: 8px">
                    {{ item.approvalStatusName }}
                  </el-tag>
                </div>
              </div>
            </el-scrollbar>
          </div>
        </div>
      </div>

      <!-- 审批表单 -->
      <div class="approval-form">
        <el-form
          ref="formRef"
          :model="form"
          :rules="formRules"
          label-width="100px"
          label-position="right"
        >
          <el-form-item label="审批结果" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio value="APPROVED">
                <el-icon color="#67c23a"><CircleCheck /></el-icon>
                审批通过
              </el-radio>
              <el-radio value="REJECTED">
                <el-icon color="#f56c6c"><CircleClose /></el-icon>
                审批驳回
              </el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="审批备注" prop="note">
            <el-input
              v-model="form.note"
              type="textarea"
              :placeholder="form.status === 'APPROVED' ? '请输入审批通过的备注信息（可选）' : '请输入审批驳回的原因'"
              :rows="4"
              maxlength="500"
              show-word-limit
              />
          </el-form-item>
        </el-form>
      </div>

      <!-- 审批提示 -->
      <div class="approval-tips">
        <el-alert
          v-if="form.status === 'APPROVED'"
          title="审批通过后，该奖惩记录将被标记为已审批状态"
          type="success"
          :closable="false"
          show-icon
         />
        <el-alert
          v-else-if="form.status === 'REJECTED'"
          title="审批驳回后，该奖惩记录将被标记为已驳回状态，请填写驳回原因"
          type="error"
          :closable="false"
          show-icon
         />
        <el-alert
          v-else
          title="请选择审批结果"
          type="info"
          :closable="false"
          show-icon
         />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          :disabled="!form.status"
          @click="handleSubmit"
        >
          确认审批
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'ApprovalDialog'
})
 
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { CircleCheck, CircleClose } from '@element-plus/icons-vue'
import { awardsPunishmentsApi } from '@/api/awardsPunishments'
import type { AwardsPunishments } from '@/types/awardsPunishments'

// Props
interface Props {
  visible: boolean
  awardsPunishments: AwardsPunishments | null
  batchMode: boolean
  selectedRows: AwardsPunishments[]
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  awardsPunishments: null,
  batchMode: false,
  selectedRows: () => []
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const loading = ref(false)
const formRef = ref<FormInstance>()

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 对话框标题
const dialogTitle = computed(() => {
  return props.batchMode ? '批量审批奖惩记录' : '审批奖惩记录'
})

// 表单数据
const form = reactive({
  status: '' as 'APPROVED' | 'REJECTED' | '',
  note: ''
})

// 表单验证规则
const formRules: FormRules = {
  status: [
    { required: true, message: '请选择审批结果', trigger: 'change' }
  ],
  note: [
    {
      validator: (rule, value, callback) => {
        if (form.status === 'REJECTED' && !value.trim()) {
          callback(new Error('审批驳回时必须填写驳回原因'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    },
    { max: 500, message: '备注长度不能超过500个字符', trigger: 'blur' }
  ]
}

// 监听对话框显示状态，重置表单
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      // 重置表单
      form.status = ''
      form.note = ''
    }
  }
)

// 提交审批
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    if (props.batchMode) {
      // 批量审批
      const ids = props.selectedRows.map(row => row.id!).filter(id => id)
      await awardsPunishmentsApi.batchApprove(ids, form.status as 'APPROVED' | 'REJECTED', form.note)
      ElMessage.success(`批量审批完成，共处理 ${ids.length} 条记录`)
    } else {
      // 单个审批
      if (props.awardsPunishments?.id) {
        await awardsPunishmentsApi.approve(
          props.awardsPunishments.id, 
          form.status as 'APPROVED' | 'REJECTED', 
          form.note
        )
        ElMessage.success('审批完成')
      }
    }

    emit('success')
    handleClose()
  } catch (__error) {
    console.error('审批失败:', error)
    if (error !== false) { // 不是表单验证错误
      ElMessage.error('审批操作失败')
    }
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  formRef.value?.resetFields()
}

// 获取奖惩类型标签类型
const getTypeTagType = (type?: string) => {
  switch (type) {
    case 'AWARD':
      return 'success'
    case 'PUNISHMENT':
      return 'danger'
    default:
      return ''
  }
}

// 获取奖惩类别标签类型
const getCategoryTagType = (category?: string) => {
  switch (category) {
    case 'COMMENDATION':
    case 'MERIT':
    case 'MAJOR_MERIT':
      return 'success'
    case 'WARNING':
      return 'warning'
    case 'DEMERIT':
    case 'MAJOR_DEMERIT':
      return 'danger'
    case 'DISMISSAL':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取审批状态标签类型
const getApprovalStatusTagType = (status?: string) => {
  switch (status) {
    case 'APPROVED':
      return 'success'
    case 'PENDING':
      return 'warning'
    case 'REJECTED':
      return 'danger'
    default:
      return ''
  }
}
</script>

<style scoped>
.approval-dialog {
  padding: 10px 0;
}

.awards-punishments-info,
.batch-info {
  margin-bottom: 20px;
}

.awards-punishments-info h4,
.batch-info h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.batch-info p {
  margin: 0 0 12px 0;
  color: #606266;
  font-size: 14px;
}

.selected-list {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #fafafa;
}

.selected-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
}

.selected-item:last-child {
  border-bottom: none;
}

.item-info {
  flex: 1;
}

.employee-name {
  font-weight: 600;
  color: #303133;
  margin-right: 12px;
}

.awards-punishments-info {
  color: #606266;
  font-size: 14px;
}

.item-tags {
  display: flex;
  align-items: center;
}

.approval-form {
  margin: 20px 0;
}

.approval-tips {
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}

/* 单选按钮样式 */
:deep(.el-radio) {
  margin-right: 30px;
  margin-bottom: 10px;
}

:deep(.el-radio__label) {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
}

/* 表单样式 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}

/* 描述列表样式 */
:deep(.el-descriptions__label) {
  font-weight: 600;
  color: #303133;
  width: 100px;
}

:deep(.el-descriptions__content) {
  color: #606266;
}

/* 滚动条样式 */
:deep(.el-scrollbar__view) {
  padding: 0;
}

/* 警告框样式 */
:deep(.el-alert) {
  border-radius: 6px;
}

:deep(.el-alert__title) {
  font-size: 14px;
}
</style>
