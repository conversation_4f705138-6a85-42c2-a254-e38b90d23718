<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    :width="mode === 'view' ? '900px' : '1000px'"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="mode === 'view'" class="awards-punishments-detail">
      <!-- 查看模式 -->
      <el-descriptions :column="2" border>
        <el-descriptions-item label="员工姓名">{{ awardsPunishments?.employeeName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="员工ID">{{ awardsPunishments?.employeeId || '-' }}</el-descriptions-item>
        <el-descriptions-item label="奖惩类型">
          <el-tag :type="getTypeTagType(awardsPunishments?.type)" size="small">
            {{ awardsPunishments?.typeName || '-' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="奖惩类别">
          <el-tag :type="getCategoryTagType(awardsPunishments?.category)" size="small">
            {{ awardsPunishments?.categoryName || '-' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="奖惩标题" :span="2">{{ awardsPunishments?.title || '-' }}</el-descriptions-item>
        <el-descriptions-item label="奖惩原因" :span="2">{{ awardsPunishments?.reason || '-' }}</el-descriptions-item>
        <el-descriptions-item label="奖惩日期">{{ awardsPunishments?.awardDate || '-' }}</el-descriptions-item>
        <el-descriptions-item label="颁发单位">{{ awardsPunishments?.issuingUnit || '-' }}</el-descriptions-item>
        <el-descriptions-item label="审批人">{{ awardsPunishments?.approver || '-' }}</el-descriptions-item>
        <el-descriptions-item label="审批状态">
          <el-tag :type="getApprovalStatusTagType(awardsPunishments?.approvalStatus)" size="small">
            {{ awardsPunishments?.approvalStatusName || '-' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="审批日期">{{ awardsPunishments?.approvalDate || '-' }}</el-descriptions-item>
        <el-descriptions-item label="严重程度">
          <el-tag v-if="awardsPunishments?.severity" :type="getSeverityTagType(awardsPunishments.severity)" size="small">
            {{ awardsPunishments.severityName }}
          </el-tag>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="奖惩金额">
          <span v-if="awardsPunishments?.amount">
            {{ awardsPunishments.amount.toLocaleString() }} {{ awardsPunishments.currency || 'CNY' }}
          </span>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="公开状态">
          <el-tag v-if="awardsPunishments?.isPublic" type="success" size="small">公开</el-tag>
          <el-tag v-else type="info" size="small">私有</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="生效日期">{{ awardsPunishments?.effectiveDate || '-' }}</el-descriptions-item>
        <el-descriptions-item label="失效日期">{{ awardsPunishments?.expiryDate || '-' }}</el-descriptions-item>
        <el-descriptions-item label="相关政策" :span="2">{{ awardsPunishments?.relatedPolicy || '-' }}</el-descriptions-item>
        <el-descriptions-item label="详细描述" :span="2">{{ awardsPunishments?.description || '-' }}</el-descriptions-item>
        <el-descriptions-item label="审批备注" :span="2">{{ awardsPunishments?.approvalNote || '-' }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ awardsPunishments?.remarks || '-' }}</el-descriptions-item>
        <el-descriptions-item label="相关文档" :span="2">
          <el-button
            v-if="awardsPunishments?.documentUrl"
            type="primary"
            link
            @click="handleViewDocument(awardsPunishments.documentUrl)"
          >
            查看文档
          </el-button>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ awardsPunishments?.createTime || '-' }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ awardsPunishments?.updateTime || '-' }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <div v-else class="awards-punishments-form">
      <!-- 编辑/新增模式 -->
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="120px"
        label-position="right"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="员工ID" prop="employeeId">
              <el-input
                v-model="form.employeeId"
                placeholder="请输入员工ID"
                :disabled="mode === 'edit'"
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="奖惩类型" prop="type">
              <el-radio-group v-model="form.type">
                <el-radio value="AWARD">
                  <el-icon color="#67c23a"><Trophy /></el-icon>
                  奖励
                </el-radio>
                <el-radio value="PUNISHMENT">
                  <el-icon color="#f56c6c"><Warning /></el-icon>
                  惩罚
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="奖惩类别" prop="category">
              <el-select v-model="form.category" placeholder="请选择奖惩类别" style="width: 100%">
                <el-option
                  v-for="item in filteredCategoryOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                 />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="奖惩日期" prop="awardDate">
              <el-date-picker
                v-model="form.awardDate"
                type="date"
                placeholder="请选择奖惩日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="奖惩标题" prop="title">
              <el-input
                v-model="form.title"
                placeholder="请输入奖惩标题"
                maxlength="200"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="奖惩原因" prop="reason">
              <el-input
                v-model="form.reason"
                type="textarea"
                placeholder="请输入奖惩原因"
                :rows="3"
                maxlength="500"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="颁发单位" prop="issuingUnit">
              <el-input
                v-model="form.issuingUnit"
                placeholder="请输入颁发单位"
                maxlength="200"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="审批人" prop="approver">
              <el-input
                v-model="form.approver"
                placeholder="请输入审批人"
                maxlength="50"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="严重程度" prop="severity">
              <el-select v-model="form.severity" placeholder="请选择严重程度" style="width: 100%">
                <el-option
                  v-for="item in severityOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                 />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="奖惩金额" prop="amount">
              <el-row :gutter="10">
                <el-col :span="16">
                  <el-input-number
                    v-model="form.amount"
                    placeholder="请输入金额"
                    :min="0"
                    :precision="2"
                    controls-position="right"
                    style="width: 100%"
                    />
                </el-col>
                <el-col :span="8">
                  <el-select v-model="form.currency" placeholder="货币">
                    <el-option
                      v-for="item in currencyOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                     />
                  </el-select>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生效日期" prop="effectiveDate">
              <el-date-picker
                v-model="form.effectiveDate"
                type="date"
                placeholder="请选择生效日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="失效日期" prop="expiryDate">
              <el-date-picker
                v-model="form.expiryDate"
                type="date"
                placeholder="请选择失效日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="相关政策" prop="relatedPolicy">
              <el-input
                v-model="form.relatedPolicy"
                placeholder="请输入相关政策"
                maxlength="500"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="相关文档" prop="documentUrl">
              <div class="document-upload">
                <el-input
                  v-model="form.documentUrl"
                  placeholder="请输入文档URL或上传文件"
                  style="margin-bottom: 10px"
                  />
                <el-upload
                  class="upload-demo"
                  :action="uploadAction"
                  :headers="uploadHeaders"
                  :on-success="handleUploadSuccess"
                  :on-error="handleUploadError"
                  :before-upload="beforeUpload"
                  :show-file-list="false"
                  accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                >
                  <el-button type="primary" size="small">
                    <el-icon><Upload /></el-icon>
                    上传文档
                  </el-button>
                  <template #tip>
                    <div class="el-upload__tip">
                      支持 PDF、图片、Word 文档，文件大小不超过 10MB
                    </div>
                  </template>
                </el-upload>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="特殊设置">
              <el-checkbox v-model="form.isPublic">
                设为公开
              </el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="详细描述" prop="description">
              <el-input
                v-model="form.description"
                type="textarea"
                placeholder="请输入详细描述"
                :rows="4"
                maxlength="1000"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remarks">
              <el-input
                v-model="form.remarks"
                type="textarea"
                placeholder="请输入备注信息"
                :rows="3"
                maxlength="1000"
                show-word-limit
                />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          v-if="mode !== 'view'"
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          {{ mode === 'add' ? '创建' : '更新' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'AwardsPunishmentsDialog'
})
/* eslint-disable @typescript-eslint/no-explicit-any, @typescript-eslint/no-unused-vars */

import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Trophy, Warning, Upload } from '@element-plus/icons-vue'
import {
  awardsPunishmentsApi,
  awardsPunishmentsCategoryOptions,
  severityOptions,
  currencyOptions
} from '@/api/awardsPunishments'
import type {
  AwardsPunishments,
  AwardsPunishmentsCreateRequest,
  AwardsPunishmentsUpdateRequest
} from '@/types/awardsPunishments'

// Props
interface Props {
  visible: boolean
  awardsPunishments: AwardsPunishments | null
  mode: 'view' | 'add' | 'edit'
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  awardsPunishments: null,
  mode: 'view'
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const loading = ref(false)
const formRef = ref<FormInstance>()

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 对话框标题
const dialogTitle = computed(() => {
  switch(props.mode: unknown) {
    case 'view':
      return '查看奖惩记录'
    case 'add':
      return '新增奖惩记录'
    case 'edit':
      return '编辑奖惩记录'
    default:
      return '奖惩记录'
  }
})

// 表单数据
const form = reactive<AwardsPunishmentsCreateRequest>({
  employeeId: '',
  type: 'AWARD' as unknown, // 临时修复枚举类型
  category: 'COMMENDATION' as unknown, // 临时修复枚举类型
  title: '',
  reason: '',
  description: '',
  awardDate: '',
  issuingUnit: '',
  approver: '',
  severity: undefined,
  amount: undefined,
  currency: 'CNY',
  documentUrl: '',
  isPublic: false,
  effectiveDate: '',
  expiryDate: '',
  relatedPolicy: '',
  remarks: ''
})

// 根据奖惩类型过滤类别选项
const filteredCategoryOptions = computed(() => {
  if(form.type === 'AWARD': unknown) {
    return awardsPunishmentsCategoryOptions.filter(item =>
      ['COMMENDATION', 'MERIT', 'MAJOR_MERIT', 'OTHER'].includes(item.value)
    )
  } else {
    return awardsPunishmentsCategoryOptions.filter(item =>
      ['WARNING', 'DEMERIT', 'MAJOR_DEMERIT', 'DISMISSAL', 'OTHER'].includes(item.value)
    )
  }
})

// 表单验证规则
const formRules: FormRules = {
  employeeId: [
    { required: true, message: '请输入员工ID', trigger: 'blur' },
    { min: 1, max: 36, message: '员工ID长度在1到36个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择奖惩类型', trigger: 'change' }
  ],
  category: [
    { required: true, message: '请选择奖惩类别', trigger: 'change' }
  ],
  title: [
    { required: true, message: '请输入奖惩标题', trigger: 'blur' },
    { min: 2, max: 200, message: '奖惩标题长度在2到200个字符', trigger: 'blur' }
  ],
  awardDate: [
    { required: true, message: '请选择奖惩日期', trigger: 'change' }
  ],
  reason: [
    { max: 500, message: '奖惩原因长度不能超过500个字符', trigger: 'blur' }
  ],
  issuingUnit: [
    { max: 200, message: '颁发单位长度不能超过200个字符', trigger: 'blur' }
  ],
  approver: [
    { max: 50, message: '审批人长度不能超过50个字符', trigger: 'blur' }
  ],
  amount: [
    { type: 'number', min: 0, message: '金额必须大于等于0', trigger: 'blur' }
  ],
  effectiveDate: [
    {
      validator: (rule, value, callback) => {
        if(value && form.expiryDate && value >= form.expiryDate: unknown) {
          callback(new Error('生效日期必须早于失效日期'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  expiryDate: [
    {
      validator: (rule, value, callback) => {
        if(value && form.effectiveDate && value <= form.effectiveDate: unknown) {
          callback(new Error('失效日期必须晚于生效日期'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  relatedPolicy: [
    { max: 500, message: '相关政策长度不能超过500个字符', trigger: 'blur' }
  ],
  description: [
    { max: 1000, message: '详细描述长度不能超过1000个字符', trigger: 'blur' }
  ],
  remarks: [
    { max: 1000, message: '备注长度不能超过1000个字符', trigger: 'blur' }
  ]
}

// 上传配置
const uploadAction = '/api/v1/files/upload'
const uploadHeaders = {
  'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
}

// 监听奖惩类型变化，自动调整类别
watch(() => form.type, (newType) => {
  if(newType === 'AWARD': unknown) {
    form.category = 'COMMENDATION' as unknown // 临时修复枚举类型
  } else {
    form.category = 'WARNING' as unknown // 临时修复枚举类型
  }
})

// 监听props变化，初始化表单数据
watch(
  () => [props.visible, props.awardsPunishments, props.mode],
  ([visible, awardsPunishments, mode]) => {
    if(visible && mode === 'edit' && awardsPunishments: unknown) {
      // 编辑模式，填充表单数据
      Object.assign(form, {
        employeeId: (awardsPunishments as unknown)?.employeeId,
        type: (awardsPunishments as unknown)?.type,
        category: (awardsPunishments as unknown)?.category,
        title: (awardsPunishments as unknown)?.title,
        reason: (awardsPunishments as unknown)?.reason || '',
        description: (awardsPunishments as unknown)?.description || '',
        awardDate: (awardsPunishments as unknown)?.awardDate || '',
        issuingUnit: (awardsPunishments as unknown)?.issuingUnit || '',
        approver: (awardsPunishments as unknown)?.approver || '',
        severity: (awardsPunishments as unknown)?.severity,
        amount: (awardsPunishments as unknown)?.amount,
        currency: (awardsPunishments as unknown)?.currency || 'CNY',
        documentUrl: (awardsPunishments as unknown)?.documentUrl || '',
        isPublic: (awardsPunishments as unknown)?.isPublic || false,
        effectiveDate: (awardsPunishments as unknown)?.effectiveDate || '',
        expiryDate: (awardsPunishments as unknown)?.expiryDate || '',
        relatedPolicy: (awardsPunishments as unknown)?.relatedPolicy || '',
        remarks: (awardsPunishments as unknown)?.remarks || ''
      })
    } else if(visible && mode === 'add': unknown) {
      // 新增模式，重置表单
      Object.assign(form, {
        employeeId: '',
        type: 'AWARD',
        category: 'COMMENDATION',
        title: '',
        reason: '',
        description: '',
        awardDate: '',
        issuingUnit: '',
        approver: '',
        severity: undefined,
        amount: undefined,
        currency: 'CNY',
        documentUrl: '',
        isPublic: false,
        effectiveDate: '',
        expiryDate: '',
        relatedPolicy: '',
        remarks: ''
      })
    }
  },
  { immediate: true }
)

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    if(props.mode === 'add': unknown) {
      await awardsPunishmentsApi.create(form)
      ElMessage.success('奖惩记录创建成功')
    } else if(props.mode === 'edit' && props.awardsPunishments: unknown) {
      const updateData: AwardsPunishmentsUpdateRequest = {
        type: form.type,
        category: form.category,
        title: form.title,
        reason: form.reason,
        description: form.description,
        awardDate: form.awardDate,
        issuingUnit: form.issuingUnit,
        approver: form.approver,
        severity: form.severity,
        amount: form.amount,
        currency: form.currency,
        documentUrl: form.documentUrl,
        isPublic: form.isPublic,
        effectiveDate: form.effectiveDate,
        expiryDate: form.expiryDate,
        relatedPolicy: form.relatedPolicy,
        remarks: form.remarks
      }
      await awardsPunishmentsApi.update(props.awardsPunishments.id!, updateData)
      ElMessage.success('奖惩记录更新成功')
    }

    emit('success')
    handleClose()
  } catch(_error: unknown) {
    console.error('提交失败:', error)
    if(error !== false: unknown) { // 不是表单验证错误
      ElMessage.error('操作失败')
    }
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  formRef.value?.resetFields()
}

// 查看文档
const handleViewDocument = (url: string) => {
  window.open(url, '_blank')
}

// 文件上传成功
   
const handleUploadSuccess = (response: unknown) => {
  if(response.code === 200: unknown) {
    form.documentUrl = response.data.url
    ElMessage.success('文件上传成功')
  } else {
    ElMessage.error(response.message || '文件上传失败')
  }
}

// 文件上传失败
const handleUploadError = () => {
  ElMessage.error('文件上传失败')
}

// 上传前检查
const beforeUpload = (file: File) => {
  const isValidType = [
    'application/pdf',
    'image/jpeg',
    'image/jpg',
    'image/png',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ].includes(file.type)

  const isLt10M = file.size / 1024 / 1024 < 10

  if(!isValidType: unknown) {
    ElMessage.error('只能上传 PDF、图片、Word 文档格式的文件!')
    return false
  }
  if(!isLt10M: unknown) {
    ElMessage.error('上传文件大小不能超过 10MB!')
    return false
  }
  return true
}

// 获取奖惩类型标签类型
const getTypeTagType = (type?: string) => {
  switch(type: unknown) {
    case 'AWARD':
      return 'success'
    case 'PUNISHMENT':
      return 'danger'
    default:
      return ''
  }
}

// 获取奖惩类别标签类型
const getCategoryTagType = (category?: string) => {
  switch(category: unknown) {
    case 'COMMENDATION':
    case 'MERIT':
    case 'MAJOR_MERIT':
      return 'success'
    case 'WARNING':
      return 'warning'
    case 'DEMERIT':
    case 'MAJOR_DEMERIT':
      return 'danger'
    case 'DISMISSAL':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取严重程度标签类型
const getSeverityTagType = (severity?: string) => {
  switch(severity: unknown) {
    case 'LOW':
      return 'success'
    case 'MEDIUM':
      return 'warning'
    case 'HIGH':
      return 'danger'
    case 'CRITICAL':
      return 'danger'
    default:
      return ''
  }
}

// 获取审批状态标签类型
const getApprovalStatusTagType = (status?: string) => {
  switch(status: unknown) {
    case 'APPROVED':
      return 'success'
    case 'PENDING':
      return 'warning'
    case 'REJECTED':
      return 'danger'
    default:
      return ''
  }
}
</script>

<style scoped>
.awards-punishments-detail {
  padding: 20px 0;
}

.awards-punishments-form {
  padding: 20px 0;
}

.dialog-footer {
  text-align: right;
}

.document-upload {
  width: 100%;
}

.upload-demo {
  width: 100%;
}

:deep(.el-upload__tip) {
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
  color: #303133;
}

:deep(.el-descriptions__content) {
  color: #606266;
}

/* 表单样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}

:deep(.el-date-editor.el-input) {
  border-radius: 6px;
}

/* 单选按钮样式 */
:deep(.el-radio) {
  margin-right: 30px;
}

:deep(.el-radio__label) {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
}

/* 复选框样式 */
:deep(.el-checkbox) {
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .awards-punishments-form .el-col {
    width: 100%;
    margin-bottom: 10px;
  }
}
</style>