<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    :width="mode === 'view' ? '900px' : '1000px'"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="mode === 'view'" class="education-history-detail">
      <!-- 查看模式 -->
      <el-descriptions :column="2" border>
        <el-descriptions-item label="员工姓名">{{ educationHistory?.employeeName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="员工ID">{{ educationHistory?.employeeId || '-' }}</el-descriptions-item>
        <el-descriptions-item label="学历/学位">{{ educationHistory?.degree || '-' }}</el-descriptions-item>
        <el-descriptions-item label="毕业院校">{{ educationHistory?.graduationSchool || '-' }}</el-descriptions-item>
        <el-descriptions-item label="专业">{{ educationHistory?.major || '-' }}</el-descriptions-item>
        <el-descriptions-item label="学习形式">
          <el-tag :type="getStudyFormTagType(educationHistory?.studyForm)" size="small">
            {{ educationHistory?.studyFormName || '-' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="开始时间">{{ educationHistory?.startDate || '-' }}</el-descriptions-item>
        <el-descriptions-item label="结束时间">{{ educationHistory?.endDate || '-' }}</el-descriptions-item>
        <el-descriptions-item label="平均绩点">
          {{ educationHistory?.gpa ? educationHistory.gpa.toFixed(2) : '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="排名">
          {{ educationHistory?.ranking && educationHistory?.totalStudents 
            ? `${educationHistory.ranking}/${educationHistory.totalStudents}` 
            : (educationHistory?.ranking ? `第${educationHistory.ranking}名` : '-') }}
        </el-descriptions-item>
        <el-descriptions-item label="导师">{{ educationHistory?.advisor || '-' }}</el-descriptions-item>
        <el-descriptions-item label="证书编号">{{ educationHistory?.certificateNumber || '-' }}</el-descriptions-item>
        <el-descriptions-item label="验证状态" :span="2">
          <el-tag :type="getVerificationStatusTagType(educationHistory?.verificationStatus)" size="small">
            {{ educationHistory?.verificationStatusName || '-' }}
          </el-tag>
          <span v-if="educationHistory?.verificationDate" class="verification-date">
            （验证时间：{{ educationHistory.verificationDate }}）
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="毕业论文题目" :span="2">
          {{ educationHistory?.thesisTitle || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="核心课程" :span="2">
          {{ educationHistory?.coreCourses || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="荣誉奖励" :span="2">
          {{ educationHistory?.honors || '-' }}
        </el-descriptions-item>
        <el-descriptions-item v-if="educationHistory?.verificationNote" label="验证备注" :span="2">
          {{ educationHistory.verificationNote }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ educationHistory?.createTime || '-' }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ educationHistory?.updateTime || '-' }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <div v-else class="education-history-form">
      <!-- 编辑/新增模式 -->
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="120px"
        label-position="right"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="员工ID" prop="employeeId">
              <el-input
                v-model="form.employeeId"
                placeholder="请输入员工ID"
                :disabled="mode === 'edit'"
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="学历/学位" prop="degree">
              <el-select v-model="form.degree" placeholder="请选择学历" style="width: 100%">
                <el-option
                  v-for="item in degreeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                 />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="毕业院校" prop="graduationSchool">
              <el-input
                v-model="form.graduationSchool"
                placeholder="请输入毕业院校"
                maxlength="200"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="专业" prop="major">
              <el-input
                v-model="form.major"
                placeholder="请输入专业"
                maxlength="100"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="学习形式" prop="studyForm">
              <el-select v-model="form.studyForm" placeholder="请选择学习形式" style="width: 100%">
                <el-option
                  v-for="item in studyFormOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                 />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证书编号" prop="certificateNumber">
              <el-input
                v-model="form.certificateNumber"
                placeholder="请输入证书编号"
                maxlength="100"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startDate">
              <el-date-picker
                v-model="form.startDate"
                type="date"
                placeholder="请选择开始时间"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endDate">
              <el-date-picker
                v-model="form.endDate"
                type="date"
                placeholder="请选择结束时间"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="平均绩点" prop="gpa">
              <el-input-number
                v-model="form.gpa"
                placeholder="请输入绩点"
                :min="0"
                :max="5"
                :precision="2"
                controls-position="right"
                style="width: 100%"
                />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="排名" prop="ranking">
              <el-input-number
                v-model="form.ranking"
                placeholder="请输入排名"
                :min="1"
                :max="9999"
                controls-position="right"
                style="width: 100%"
                />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="总人数" prop="totalStudents">
              <el-input-number
                v-model="form.totalStudents"
                placeholder="请输入总人数"
                :min="1"
                :max="9999"
                controls-position="right"
                style="width: 100%"
                />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="导师" prop="advisor">
              <el-input
                v-model="form.advisor"
                placeholder="请输入导师姓名"
                maxlength="50"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="毕业论文题目" prop="thesisTitle">
              <el-input
                v-model="form.thesisTitle"
                placeholder="请输入毕业论文题目"
                maxlength="500"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="核心课程" prop="coreCourses">
              <el-input
                v-model="form.coreCourses"
                type="textarea"
                placeholder="请输入核心课程，多个课程用逗号分隔"
                :rows="3"
                maxlength="1000"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="荣誉奖励" prop="honors">
              <el-input
                v-model="form.honors"
                type="textarea"
                placeholder="请输入在校期间获得的荣誉奖励"
                :rows="3"
                maxlength="1000"
                show-word-limit
                />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          v-if="mode !== 'view'"
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          {{ mode === 'add' ? '创建' : '更新' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'EducationHistoryDialog'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { educationHistoryApi, degreeOptions, studyFormOptions } from '@/api/educationHistory'
import type { 
  EducationHistory, 
  EducationHistoryCreateRequest,
  EducationHistoryUpdateRequest 
} from '@/types/educationHistory'

// Props
interface Props {
  visible: boolean
  educationHistory: EducationHistory | null
  mode: 'view' | 'add' | 'edit'
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  educationHistory: null,
  mode: 'view'
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const loading = ref(false)
const formRef = ref<FormInstance>()

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 对话框标题
const dialogTitle = computed(() => {
  switch (props.mode) {
    case 'view':
      return '查看教育经历'
    case 'add':
      return '新增教育经历'
    case 'edit':
      return '编辑教育经历'
    default:
      return '教育经历'
  }
})

// 表单数据
const form = reactive<EducationHistoryCreateRequest>({
  employeeId: '',
  startDate: '',
  endDate: '',
  degree: '',
  graduationSchool: '',
  major: '',
  studyForm: 'FULL_TIME' as unknown, // 临时修复枚举类型
  coreCourses: '',
  thesisTitle: '',
  advisor: '',
  gpa: undefined,
  ranking: undefined,
  totalStudents: undefined,
  honors: '',
  certificateNumber: ''
})

// 表单验证规则
const formRules: FormRules = {
  employeeId: [
    { required: true, message: '请输入员工ID', trigger: 'blur' },
    { min: 1, max: 36, message: '员工ID长度在1到36个字符', trigger: 'blur' }
  ],
  degree: [
    { required: true, message: '请选择学历', trigger: 'change' }
  ],
  graduationSchool: [
    { required: true, message: '请输入毕业院校', trigger: 'blur' },
    { min: 2, max: 200, message: '毕业院校长度在2到200个字符', trigger: 'blur' }
  ],
  major: [
    { required: true, message: '请输入专业', trigger: 'blur' },
    { min: 2, max: 100, message: '专业长度在2到100个字符', trigger: 'blur' }
  ],
  studyForm: [
    { required: true, message: '请选择学习形式', trigger: 'change' }
  ],
  startDate: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  endDate: [
    { required: true, message: '请选择结束时间', trigger: 'change' },
    {
      validator: (rule, value, callback) => {
        if (value && form.startDate && value <= form.startDate) {
          callback(new Error('结束时间必须晚于开始时间'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  gpa: [
    { type: 'number', min: 0, max: 5, message: '绩点范围为0-5', trigger: 'blur' }
  ],
  ranking: [
    { type: 'number', min: 1, message: '排名必须大于0', trigger: 'blur' }
  ],
  totalStudents: [
    { type: 'number', min: 1, message: '总人数必须大于0', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value && form.ranking && value < form.ranking) {
          callback(new Error('总人数不能小于排名'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  certificateNumber: [
    { max: 100, message: '证书编号长度不能超过100个字符', trigger: 'blur' }
  ],
  advisor: [
    { max: 50, message: '导师姓名长度不能超过50个字符', trigger: 'blur' }
  ],
  thesisTitle: [
    { max: 500, message: '论文题目长度不能超过500个字符', trigger: 'blur' }
  ],
  coreCourses: [
    { max: 1000, message: '核心课程长度不能超过1000个字符', trigger: 'blur' }
  ],
  honors: [
    { max: 1000, message: '荣誉奖励长度不能超过1000个字符', trigger: 'blur' }
  ]
}

// 监听props变化，初始化表单数据
watch(
  () => [props.visible, props.educationHistory, props.mode],
  ([visible, educationHistory, mode]) => {
    if (visible && mode === 'edit' && educationHistory) {
      // 编辑模式，填充表单数据
      Object.assign(form, {
        employeeId: (educationHistory as unknown)?.employeeId,
        startDate: (educationHistory as unknown)?.startDate || '',
        endDate: (educationHistory as unknown)?.endDate || '',
        degree: (educationHistory as unknown)?.degree || '',
        graduationSchool: (educationHistory as unknown)?.graduationSchool || '',
        major: (educationHistory as unknown)?.major || '',
        studyForm: (educationHistory as unknown)?.studyForm || 'FULL_TIME',
        coreCourses: (educationHistory as unknown)?.coreCourses || '',
        thesisTitle: (educationHistory as unknown)?.thesisTitle || '',
        advisor: (educationHistory as unknown)?.advisor || '',
        gpa: (educationHistory as unknown)?.gpa,
        ranking: (educationHistory as unknown)?.ranking,
        totalStudents: (educationHistory as unknown)?.totalStudents,
        honors: (educationHistory as unknown)?.honors || '',
        certificateNumber: (educationHistory as unknown)?.certificateNumber || ''
      })
    } else if (visible && mode === 'add') {
      // 新增模式，重置表单
      Object.assign(form, {
        employeeId: '',
        startDate: '',
        endDate: '',
        degree: '',
        graduationSchool: '',
        major: '',
        studyForm: 'FULL_TIME',
        coreCourses: '',
        thesisTitle: '',
        advisor: '',
        gpa: undefined,
        ranking: undefined,
        totalStudents: undefined,
        honors: '',
        certificateNumber: ''
      })
    }
  },
  { immediate: true }
)

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    if (props.mode === 'add') {
      await educationHistoryApi.create(form)
      ElMessage.success('教育经历创建成功')
    } else if (props.mode === 'edit' && props.educationHistory) {
      const updateData: EducationHistoryUpdateRequest = {
        startDate: form.startDate,
        endDate: form.endDate,
        degree: form.degree,
        graduationSchool: form.graduationSchool,
        major: form.major,
        studyForm: form.studyForm,
        coreCourses: form.coreCourses,
        thesisTitle: form.thesisTitle,
        advisor: form.advisor,
        gpa: form.gpa,
        ranking: form.ranking,
        totalStudents: form.totalStudents,
        honors: form.honors,
        certificateNumber: form.certificateNumber
      }
      await educationHistoryApi.update(props.educationHistory.id!, updateData)
      ElMessage.success('教育经历更新成功')
    }

    emit('success')
    handleClose()
  } catch (__error) {
    console.error('提交失败:', error)
    if (error !== false) { // 不是表单验证错误
      ElMessage.error('操作失败')
    }
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  formRef.value?.resetFields()
}

// 获取学习形式标签类型
const getStudyFormTagType = (studyForm?: string) => {
  switch (studyForm) {
    case 'FULL_TIME':
      return 'success'
    case 'PART_TIME':
      return 'warning'
    case 'CORRESPONDENCE':
      return 'info'
    case 'SELF_STUDY':
      return 'danger'
    case 'ONLINE':
      return ''
    default:
      return ''
  }
}

// 获取验证状态标签类型
const getVerificationStatusTagType = (status?: string) => {
  switch (status) {
    case 'VERIFIED':
      return 'success'
    case 'PENDING':
      return 'warning'
    case 'REJECTED':
      return 'danger'
    default:
      return ''
  }
}
</script>

<style scoped>
.education-history-detail {
  padding: 20px 0;
}

.education-history-form {
  padding: 20px 0;
}

.dialog-footer {
  text-align: right;
}

.verification-date {
  margin-left: 8px;
  font-size: 12px;
  color: #909399;
}

/* 表单样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}

:deep(.el-date-editor.el-input) {
  border-radius: 6px;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
  color: #303133;
}

:deep(.el-descriptions__content) {
  color: #606266;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .education-history-form .el-col {
    width: 100%;
    margin-bottom: 10px;
  }
}
</style>
