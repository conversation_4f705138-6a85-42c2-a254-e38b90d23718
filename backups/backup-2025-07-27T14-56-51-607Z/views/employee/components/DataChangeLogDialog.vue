<template>
  <el-dialog
    v-model="dialogVisible"
    title="数据变更日志详情"
    width="1000px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="dataChangeLog" class="data-change-log-detail">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h4>基本信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="操作时间">{{ dataChangeLog.createTime || '-' }}</el-descriptions-item>
          <el-descriptions-item label="表名">{{ dataChangeLog.tableName || '-' }}</el-descriptions-item>
          <el-descriptions-item label="表注释">{{ dataChangeLog.tableComment || '-' }}</el-descriptions-item>
          <el-descriptions-item label="记录ID">{{ dataChangeLog.recordId || '-' }}</el-descriptions-item>
          <el-descriptions-item label="操作类型">
            <el-tag :type="getOperationTypeTagType(dataChangeLog.operationType)" size="small">
              {{ dataChangeLog.operationTypeName || '-' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="变更类型">
            <el-tag :type="getChangeTypeTagType(dataChangeLog.changeType)" size="small">
              {{ dataChangeLog.changeTypeName || '-' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="操作人">{{ dataChangeLog.operatorName || '-' }}</el-descriptions-item>
          <el-descriptions-item label="操作人类型">{{ dataChangeLog.operatorTypeName || '-' }}</el-descriptions-item>
          <el-descriptions-item label="业务模块">{{ dataChangeLog.businessModuleName || '-' }}</el-descriptions-item>
          <el-descriptions-item label="风险等级">
            <el-tag v-if="dataChangeLog.riskLevel" :type="getRiskLevelTagType(dataChangeLog.riskLevel)" size="small">
              {{ dataChangeLog.riskLevelName }}
            </el-tag>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="执行状态">
            <el-tag v-if="dataChangeLog.isSuccess" type="success" size="small">成功</el-tag>
            <el-tag v-else type="danger" size="small">失败</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="执行时间">
            <span :class="getExecutionTimeClass(dataChangeLog.executionTime)">
              {{ dataChangeLog.executionTime }}ms
            </span>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 请求信息 -->
      <div class="detail-section">
        <h4>请求信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="请求ID">{{ dataChangeLog.requestId || '-' }}</el-descriptions-item>
          <el-descriptions-item label="会话ID">{{ dataChangeLog.sessionId || '-' }}</el-descriptions-item>
          <el-descriptions-item label="IP地址">{{ dataChangeLog.ipAddress || '-' }}</el-descriptions-item>
          <el-descriptions-item label="请求方法">{{ dataChangeLog.requestMethod || '-' }}</el-descriptions-item>
          <el-descriptions-item label="请求URL" :span="2">{{ dataChangeLog.requestUrl || '-' }}</el-descriptions-item>
          <el-descriptions-item label="用户代理" :span="2">{{ dataChangeLog.userAgent || '-' }}</el-descriptions-item>
          <el-descriptions-item label="响应状态码">{{ dataChangeLog.responseStatus || '-' }}</el-descriptions-item>
          <el-descriptions-item label="响应消息">{{ dataChangeLog.responseMessage || '-' }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 变更数据 -->
      <div v-if="dataChangeLog.beforeData || dataChangeLog.afterData" class="detail-section">
        <h4>变更数据</h4>
        <el-tabs v-model="activeTab" type="border-card">
          <el-tab-pane v-if="dataChangeLog.beforeData" label="变更前数据" name="before">
            <div class="data-content">
              <el-button type="primary" size="small" @click="copyToClipboard(dataChangeLog.beforeData!)">
                <el-icon><DocumentCopy /></el-icon>
                复制
              </el-button>
              <pre class="json-data">{{ formatJsonData(dataChangeLog.beforeData) }}</pre>
            </div>
          </el-tab-pane>
          <el-tab-pane v-if="dataChangeLog.afterData" label="变更后数据" name="after">
            <div class="data-content">
              <el-button type="primary" size="small" @click="copyToClipboard(dataChangeLog.afterData!)">
                <el-icon><DocumentCopy /></el-icon>
                复制
              </el-button>
              <pre class="json-data">{{ formatJsonData(dataChangeLog.afterData) }}</pre>
            </div>
          </el-tab-pane>
          <el-tab-pane v-if="dataChangeLog.beforeData && dataChangeLog.afterData" label="数据对比" name="compare">
            <div class="comparison-content">
              <el-button type="success" size="small" @click="handleCompareData">
                <el-icon><View /></el-icon>
                详细对比
              </el-button>
              <div v-if="comparisonResult" class="comparison-result">
                <div class="comparison-summary">
                  <el-tag type="info">变更字段数: {{ comparisonResult.changeCount }}</el-tag>
                  <el-tag type="warning">变更比例: {{ comparisonResult.changePercentage.toFixed(2) }}%</el-tag>
                </div>
                <el-table :data="comparisonResult.changedFields" stripe style="width: 100%; margin-top: 10px;">
                  <el-table-column prop="fieldName" label="字段名" width="150"  />
                  <el-table-column prop="fieldComment" label="字段注释" width="150"  />
                  <el-table-column prop="beforeValue" label="变更前" show-overflow-tooltip  />
                  <el-table-column prop="afterValue" label="变更后" show-overflow-tooltip  />
                  <el-table-column prop="changeType" label="变更类型" width="100" align="center">
                    <template #default="scope">
                      <el-tag :type="getFieldChangeTypeTagType(scope.row.changeType)" size="small">
                        {{ getFieldChangeTypeName(scope.row.changeType) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 请求参数 -->
      <div v-if="dataChangeLog.requestParams" class="detail-section">
        <h4>请求参数</h4>
        <div class="data-content">
          <el-button type="primary" size="small" @click="copyToClipboard(dataChangeLog.requestParams!)">
            <el-icon><DocumentCopy /></el-icon>
            复制
          </el-button>
          <pre class="json-data">{{ formatJsonData(dataChangeLog.requestParams) }}</pre>
        </div>
      </div>

      <!-- 变更字段 -->
      <div v-if="dataChangeLog.changedFields" class="detail-section">
        <h4>变更字段</h4>
        <el-tag
          v-for="field in getChangedFieldsList(dataChangeLog.changedFields)"
          :key="field"
          type="info"
          size="small"
          style="margin-right: 8px; margin-bottom: 8px;"
        >
          {{ field }}
        </el-tag>
      </div>

      <!-- 其他信息 -->
      <div class="detail-section">
        <h4>其他信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="批次ID">{{ dataChangeLog.batchId || '-' }}</el-descriptions-item>
          <el-descriptions-item label="父日志ID">{{ dataChangeLog.parentLogId || '-' }}</el-descriptions-item>
          <el-descriptions-item label="回滚ID">{{ dataChangeLog.rollbackId || '-' }}</el-descriptions-item>
          <el-descriptions-item label="数据状态">{{ dataChangeLog.dataStatusName || '-' }}</el-descriptions-item>
          <el-descriptions-item label="标签" :span="2">{{ dataChangeLog.tags || '-' }}</el-descriptions-item>
          <el-descriptions-item label="变更描述" :span="2">{{ dataChangeLog.changeDescription || '-' }}</el-descriptions-item>
          <el-descriptions-item label="错误消息" :span="2">
            <span v-if="dataChangeLog.errorMessage" class="error-message">{{ dataChangeLog.errorMessage }}</span>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ dataChangeLog.remarks || '-' }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button v-if="dataChangeLog?.beforeData && dataChangeLog?.afterData" type="success" @click="handleCompareData">
          详细对比
        </el-button>
        <el-button type="primary" @click="handleViewHistory">
          查看历史
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'DataChangeLogDialog'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { DocumentCopy, View } from '@element-plus/icons-vue'
import { dataChangeLogApi } from '@/api/dataChangeLog'
import type { DataChangeLog, DataComparisonResult } from '@/types/dataChangeLog'

// Props
interface Props {
  visible: boolean
  dataChangeLog: DataChangeLog | null
  mode: 'view'
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  dataChangeLog: null,
  mode: 'view'
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const activeTab = ref('before')
const comparisonResult = ref<DataComparisonResult | null>(null)

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  comparisonResult.value = null
  activeTab.value = 'before'
}

// 格式化JSON数据
const formatJsonData = (jsonStr: string) => {
  try {
    const obj = JSON.parse(jsonStr)
    return JSON.stringify(obj, null, 2)
  } catch (__error) {
    return jsonStr
  }
}

// 复制到剪贴板
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('已复制到剪贴板')
  } catch (__error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}

// 获取变更字段列表
const getChangedFieldsList = (changedFields: string) => {
  try {
    return changedFields.split(',').map(field => field.trim()).filter(field => field)
  } catch (__error) {
    return []
  }
}

// 数据对比
const handleCompareData = async () => {
  if (!props.dataChangeLog?.id) return
  
  try {
    comparisonResult.value = await dataChangeLogApi.compareData(props.dataChangeLog.id) as unknown // 修复API类型不匹配
    activeTab.value = 'compare'
    ElMessage.success('数据对比完成')
  } catch (__error) {
    console.error('数据对比失败:', error)
    ElMessage.error('数据对比失败')
  }
}

// 查看历史
const handleViewHistory = () => {
  emit('success')
  // 这里可以触发父组件打开历史对话框
}

// 获取操作类型标签类型
   
const getOperationTypeTagType = (type: unknown) => { // 临时修复类型
  switch (type) {
    case 'INSERT':
    case 'BATCH_INSERT':
      return 'success'
    case 'UPDATE':
    case 'BATCH_UPDATE':
      return 'warning'
    case 'DELETE':
    case 'BATCH_DELETE':
      return 'danger'
    case 'SELECT':
      return 'info'
    default:
      return ''
  }
}

// 获取变更类型标签类型
   
const getChangeTypeTagType = (type: unknown) => { // 临时修复类型
  switch (type) {
    case 'CREATE':
      return 'success'
    case 'MODIFY':
      return 'warning'
    case 'DELETE':
      return 'danger'
    case 'QUERY':
      return 'info'
    case 'IMPORT':
    case 'EXPORT':
      return ''
    case 'BATCH_OPERATION':
      return 'warning'
    default:
      return ''
  }
}

// 获取风险等级标签类型
const getRiskLevelTagType = (level: string) => {
  switch (level) {
    case 'LOW':
      return 'info'
    case 'MEDIUM':
      return ''
    case 'HIGH':
      return 'warning'
    case 'CRITICAL':
      return 'danger'
    default:
      return ''
  }
}

// 获取字段变更类型标签类型
const getFieldChangeTypeTagType = (type: string) => {
  switch (type) {
    case 'ADDED':
      return 'success'
    case 'MODIFIED':
      return 'warning'
    case 'REMOVED':
      return 'danger'
    default:
      return ''
  }
}

// 获取字段变更类型名称
const getFieldChangeTypeName = (type: string) => {
  switch (type) {
    case 'ADDED':
      return '新增'
    case 'MODIFIED':
      return '修改'
    case 'REMOVED':
      return '删除'
    default:
      return type
  }
}

// 获取执行时间样式类
const getExecutionTimeClass = (time?: number) => {
  if (!time) return ''
  
  if (time < 100) {
    return 'fast-execution'
  } else if (time < 1000) {
    return 'normal-execution'
  } else {
    return 'slow-execution'
  }
}
</script>

<style scoped>
.data-change-log-detail {
  padding: 10px 0;
}

.detail-section {
  margin-bottom: 30px;
}

.detail-section h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-left: 4px solid #409eff;
  padding-left: 10px;
}

.data-content {
  position: relative;
}

.json-data {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 15px;
  margin-top: 10px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  color: #495057;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 300px;
  overflow-y: auto;
}

.comparison-content {
  padding: 10px 0;
}

.comparison-summary {
  margin-bottom: 15px;
}

.comparison-summary .el-tag {
  margin-right: 10px;
}

.error-message {
  color: #f56c6c;
  font-weight: 600;
}

.dialog-footer {
  text-align: right;
}

/* 执行时间样式 */
.fast-execution {
  color: #67c23a;
  font-weight: 600;
}

.normal-execution {
  color: #e6a23c;
  font-weight: 600;
}

.slow-execution {
  color: #f56c6c;
  font-weight: 600;
}

/* 描述列表样式 */
:deep(.el-descriptions__label) {
  font-weight: 600;
  color: #303133;
}

:deep(.el-descriptions__content) {
  color: #606266;
}

/* 标签页样式 */
:deep(.el-tabs__header) {
  margin-bottom: 15px;
}

:deep(.el-tabs__item) {
  font-weight: 500;
}

/* 表格样式 */
:deep(.el-table) {
  border-radius: 6px;
  overflow: hidden;
}

:deep(.el-table__header th) {
  background-color: #f8f9fa;
  color: #303133;
  font-weight: 600;
}

/* 滚动条样式 */
.json-data::-webkit-scrollbar {
  width: 6px;
}

.json-data::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.json-data::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.json-data::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
