<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="recognition-dialog">
      <!-- 单个认定模式 -->
      <div v-if="!batchMode" class="single-recognition">
        <div class="teacher-qualification-info">
          <h4>教师资格信息</h4>
          <el-descriptions :column="1" border size="small">
            <el-descriptions-item label="员工姓名">
              {{ teacherQualification?.employeeName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="资格类型">
              <el-tag :type="getQualificationTypeTagType(teacherQualification?.qualificationType)" size="small">
                {{ teacherQualification?.qualificationTypeName || '-' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="学科类别">
              {{ teacherQualification?.subjectCategory || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="学科名称">
              {{ teacherQualification?.subjectName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="当前认定状态">
              <el-tag :type="getRecognitionStatusTagType(teacherQualification?.recognitionStatus)" size="small">
                {{ teacherQualification?.recognitionStatusName || '-' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="认定机构">
              {{ teacherQualification?.recognitionAuthority || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="认定日期">
              {{ teacherQualification?.recognitionDate || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 批量认定模式 -->
      <div v-else class="batch-recognition">
        <div class="batch-info">
          <h4>批量认定信息</h4>
          <p>您选择了 <strong>{{ selectedRows.length }}</strong> 个教师资格进行批量认定</p>
          
          <div class="selected-list">
            <el-scrollbar height="200px">
              <div v-for="item in selectedRows" :key="item.id" class="selected-item">
                <div class="item-info">
                  <span class="employee-name">{{ item.employeeName }}</span>
                  <span class="qualification-info">{{ item.qualificationTypeName }}</span>
                </div>
                <div class="item-tags">
                  <el-tag :type="getQualificationTypeTagType(item.qualificationType)" size="small">
                    {{ item.qualificationTypeName }}
                  </el-tag>
                  <el-tag :type="getRecognitionStatusTagType(item.recognitionStatus)" size="small">
                    {{ item.recognitionStatusName }}
                  </el-tag>
                </div>
              </div>
            </el-scrollbar>
          </div>
        </div>
      </div>

      <!-- 认定表单 -->
      <div class="recognition-form">
        <el-form
          ref="formRef"
          :model="form"
          :rules="formRules"
          label-width="120px"
          label-position="right"
        >
          <el-form-item label="认定状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio value="RECOGNIZED">
                <el-icon><CircleCheck /></el-icon>
                通过认定
              </el-radio>
              <el-radio value="REJECTED">
                <el-icon><CircleClose /></el-icon>
                认定失败
              </el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="认定备注" prop="note">
            <el-input
              v-model="form.note"
              type="textarea"
              placeholder="请输入认定备注信息（可选）"
              :rows="4"
              maxlength="500"
              show-word-limit
              />
          </el-form-item>
        </el-form>
      </div>

      <!-- 认定提示 -->
      <div class="recognition-tips">
        <el-alert
          v-if="form.status === 'RECOGNIZED'"
          title="认定通过后，该教师资格将变为已认定状态，可以正常使用"
          type="success"
          :closable="false"
          show-icon
         />
        <el-alert
          v-else-if="form.status === 'REJECTED'"
          title="认定失败后，该教师资格将变为认定失败状态，需要重新申请"
          type="warning"
          :closable="false"
          show-icon
         />
        <el-alert
          v-else
          title="请选择认定状态"
          type="info"
          :closable="false"
          show-icon
         />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          :disabled="!form.status"
          @click="handleSubmit"
        >
          确认认定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'RecognitionDialog'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { CircleCheck, CircleClose } from '@element-plus/icons-vue'
import { teacherQualificationApi } from '@/api/teacherQualification'
import type { TeacherQualification } from '@/types/teacherQualification'

// Props
interface Props {
  visible: boolean
  teacherQualification: TeacherQualification | null
  batchMode: boolean
  selectedRows: TeacherQualification[]
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  teacherQualification: null,
  batchMode: false,
  selectedRows: () => []
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const loading = ref(false)
const formRef = ref<FormInstance>()

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 对话框标题
const dialogTitle = computed(() => {
  return props.batchMode ? '批量认定教师资格' : '认定教师资格'
})

// 表单数据
const form = reactive({
  status: '' as unknown, // 临时修复枚举类型
  note: ''
})

// 表单验证规则
const formRules: FormRules = {
  status: [
    { required: true, message: '请选择认定状态', trigger: 'change' }
  ],
  note: [
    { max: 500, message: '备注长度不能超过500个字符', trigger: 'blur' }
  ]
}

// 监听对话框显示状态，重置表单
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      // 重置表单
      form.status = ''
      form.note = ''
    }
  }
)

// 提交认定
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    if (props.batchMode) {
      // 批量认定
      const ids = props.selectedRows.map(row => row.id!).filter(id => id)
      await teacherQualificationApi.batchRecognize(ids, form.status, form.note)
      ElMessage.success(`批量认定完成，共处理 ${ids.length} 个教师资格`)
    } else {
      // 单个认定
      if (props.teacherQualification?.id) {
        await teacherQualificationApi.recognize(
          props.teacherQualification.id, 
          form.status, 
          form.note
        )
        ElMessage.success('认定完成')
      }
    }

    emit('success')
    handleClose()
  } catch (__error) {
    console.error('认定失败:', error)
    if (error !== false) { // 不是表单验证错误
      ElMessage.error('认定操作失败')
    }
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  formRef.value?.resetFields()
}

// 获取资格类型标签类型
const getQualificationTypeTagType = (type?: string) => {
  switch (type) {
    case 'KINDERGARTEN':
      return 'success'
    case 'PRIMARY_SCHOOL':
      return 'info'
    case 'JUNIOR_HIGH_SCHOOL':
      return 'warning'
    case 'SENIOR_HIGH_SCHOOL':
      return 'danger'
    case 'VOCATIONAL_SCHOOL':
      return ''
    case 'HIGHER_EDUCATION':
      return 'success'
    default:
      return 'info'
  }
}

// 获取认定状态标签类型
const getRecognitionStatusTagType = (status?: string) => {
  switch (status) {
    case 'RECOGNIZED':
      return 'success'
    case 'PENDING':
      return 'warning'
    case 'REJECTED':
      return 'danger'
    case 'EXPIRED':
      return 'info'
    case 'SUSPENDED':
      return 'info'
    default:
      return ''
  }
}
</script>

<style scoped>
.recognition-dialog {
  padding: 10px 0;
}

.teacher-qualification-info,
.batch-info {
  margin-bottom: 20px;
}

.teacher-qualification-info h4,
.batch-info h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.batch-info p {
  margin: 0 0 12px 0;
  color: #606266;
  font-size: 14px;
}

.selected-list {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #fafafa;
}

.selected-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
}

.selected-item:last-child {
  border-bottom: none;
}

.item-info {
  flex: 1;
}

.employee-name {
  font-weight: 600;
  color: #303133;
  margin-right: 12px;
}

.qualification-info {
  color: #606266;
  font-size: 14px;
}

.item-tags {
  display: flex;
  align-items: center;
  gap: 8px;
}

.recognition-form {
  margin: 20px 0;
}

.recognition-tips {
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}

/* 表单样式 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}

:deep(.el-radio) {
  margin-right: 20px;
  margin-bottom: 10px;
}

:deep(.el-radio__label) {
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 描述列表样式 */
:deep(.el-descriptions__label) {
  font-weight: 600;
  color: #303133;
  width: 120px;
}

:deep(.el-descriptions__content) {
  color: #606266;
}

/* 滚动条样式 */
:deep(.el-scrollbar__view) {
  padding: 0;
}

/* 警告框样式 */
:deep(.el-alert) {
  border-radius: 6px;
}

:deep(.el-alert__title) {
  font-size: 14px;
}
</style>
