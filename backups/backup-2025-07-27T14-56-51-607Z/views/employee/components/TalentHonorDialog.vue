<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    :width="mode === 'view' ? '800px' : '900px'"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="mode === 'view'" class="talent-honor-detail">
      <!-- 查看模式 -->
      <el-descriptions :column="2" border>
        <el-descriptions-item label="员工姓名">{{ talentHonor?.employeeName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="员工ID">{{ talentHonor?.employeeId || '-' }}</el-descriptions-item>
        <el-descriptions-item label="荣誉名称">{{ talentHonor?.name || '-' }}</el-descriptions-item>
        <el-descriptions-item label="荣誉等级">
          <el-tag :type="getLevelTagType(talentHonor?.level)" size="small">
            {{ talentHonor?.levelName || '-' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="颁发单位">{{ talentHonor?.issuingUnit || '-' }}</el-descriptions-item>
        <el-descriptions-item label="获奖时间">{{ talentHonor?.acquireTime || '-' }}</el-descriptions-item>
        <el-descriptions-item label="排名">
          {{ talentHonor?.ranking ? `第${talentHonor.ranking}名` : '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="证书编号">{{ talentHonor?.certificateNumber || '-' }}</el-descriptions-item>
        <el-descriptions-item label="证书文档" :span="2">
          <el-button
            v-if="talentHonor?.documentUrl"
            type="primary"
            link
            @click="handleViewDocument(talentHonor.documentUrl)"
          >
            查看证书
          </el-button>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">
          {{ talentHonor?.description || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ talentHonor?.createTime || '-' }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ talentHonor?.updateTime || '-' }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <div v-else class="talent-honor-form">
      <!-- 编辑/新增模式 -->
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="120px"
        label-position="right"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="员工ID" prop="employeeId">
              <el-input
                v-model="form.employeeId"
                placeholder="请输入员工ID"
                :disabled="mode === 'edit'"
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="荣誉名称" prop="name">
              <el-input
                v-model="form.name"
                placeholder="请输入荣誉名称"
                maxlength="200"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="荣誉等级" prop="level">
              <el-select v-model="form.level" placeholder="请选择荣誉等级" style="width: 100%">
                <el-option
                  v-for="item in talentHonorLevelOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                 />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="颁发单位" prop="issuingUnit">
              <el-input
                v-model="form.issuingUnit"
                placeholder="请输入颁发单位"
                maxlength="200"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="获奖时间" prop="acquireTime">
              <el-date-picker
                v-model="form.acquireTime"
                type="date"
                placeholder="请选择获奖时间"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排名" prop="ranking">
              <el-input-number
                v-model="form.ranking"
                placeholder="请输入排名"
                :min="1"
                :max="9999"
                controls-position="right"
                style="width: 100%"
                />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="证书编号" prop="certificateNumber">
              <el-input
                v-model="form.certificateNumber"
                placeholder="请输入证书编号"
                maxlength="100"
                show-word-limit
                />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="证书文档" prop="documentUrl">
              <div class="document-upload">
                <el-input
                  v-model="form.documentUrl"
                  placeholder="请输入证书文档URL或上传文件"
                  style="margin-bottom: 10px"
                  />
                <el-upload
                  class="upload-demo"
                  :action="uploadAction"
                  :headers="uploadHeaders"
                  :on-success="handleUploadSuccess"
                  :on-error="handleUploadError"
                  :before-upload="beforeUpload"
                  :show-file-list="false"
                  accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                >
                  <el-button type="primary" size="small">
                    <el-icon><Upload /></el-icon>
                    上传证书文档
                  </el-button>
                  <template #tip>
                    <div class="el-upload__tip">
                      支持 PDF、图片、Word 文档，文件大小不超过 10MB
                    </div>
                  </template>
                </el-upload>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="描述" prop="description">
              <el-input
                v-model="form.description"
                type="textarea"
                placeholder="请输入荣誉描述"
                :rows="4"
                maxlength="1000"
                show-word-limit
                />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          v-if="mode !== 'view'"
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          {{ mode === 'add' ? '创建' : '更新' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Upload } from '@element-plus/icons-vue'
import { talentHonorsApi, talentHonorLevelOptions } from '@/api/talentHonors'
import type { 
  TalentHonor, 
  TalentHonorCreateRequest,
  TalentHonorUpdateRequest 
} from '@/types/talentHonors'

// Props
interface Props {
  visible: boolean
  talentHonor: TalentHonor | null
  mode: 'view' | 'add' | 'edit'
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  talentHonor: null,
  mode: 'view'
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const loading = ref(false)
const formRef = ref<FormInstance>()

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 对话框标题
const dialogTitle = computed(() => {
  switch (props.mode) {
    case 'view':
      return '查看人才荣誉'
    case 'add':
      return '新增人才荣誉'
    case 'edit':
      return '编辑人才荣誉'
    default:
      return '人才荣誉'
  }
})

// 表单数据
const form = reactive<TalentHonorCreateRequest>({
  employeeId: '',
  name: '',
  level: 'SCHOOL' as unknown, // 临时修复枚举类型
  issuingUnit: '',
  acquireTime: '',
  ranking: undefined,
  description: '',
  certificateNumber: '',
  documentUrl: ''
})

// 表单验证规则
const formRules: FormRules = {
  employeeId: [
    { required: true, message: '请输入员工ID', trigger: 'blur' },
    { min: 1, max: 36, message: '员工ID长度在1到36个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入荣誉名称', trigger: 'blur' },
    { min: 2, max: 200, message: '荣誉名称长度在2到200个字符', trigger: 'blur' }
  ],
  level: [
    { required: true, message: '请选择荣誉等级', trigger: 'change' }
  ],
  acquireTime: [
    { required: true, message: '请选择获奖时间', trigger: 'change' }
  ],
  issuingUnit: [
    { max: 200, message: '颁发单位长度不能超过200个字符', trigger: 'blur' }
  ],
  certificateNumber: [
    { max: 100, message: '证书编号长度不能超过100个字符', trigger: 'blur' }
  ],
  description: [
    { max: 1000, message: '描述长度不能超过1000个字符', trigger: 'blur' }
  ]
}

// 上传配置
const uploadAction = '/api/v1/files/upload'
const uploadHeaders = {
  'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
}

// 监听props变化，初始化表单数据
watch(
  () => [props.visible, props.talentHonor, props.mode],
  ([visible, talentHonor, mode]) => {
    if (visible && mode === 'edit' && talentHonor) {
      // 编辑模式，填充表单数据
      Object.assign(form, {
        employeeId: (talentHonor as unknown)?.employeeId,
        name: (talentHonor as unknown)?.name,
        level: (talentHonor as unknown)?.level,
        issuingUnit: (talentHonor as unknown)?.issuingUnit || '',
        acquireTime: (talentHonor as unknown)?.acquireTime,
        ranking: (talentHonor as unknown)?.ranking,
        description: (talentHonor as unknown)?.description || '',
        certificateNumber: (talentHonor as unknown)?.certificateNumber || '',
        documentUrl: (talentHonor as unknown)?.documentUrl || ''
      })
    } else if (visible && mode === 'add') {
      // 新增模式，重置表单
      Object.assign(form, {
        employeeId: '',
        name: '',
        level: 'SCHOOL' as unknown, // 临时修复枚举类型
        issuingUnit: '',
        acquireTime: '',
        ranking: undefined,
        description: '',
        certificateNumber: '',
        documentUrl: ''
      })
    }
  },
  { immediate: true }
)

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    if (props.mode === 'add') {
      await talentHonorsApi.create(form)
      ElMessage.success('人才荣誉创建成功')
    } else if (props.mode === 'edit' && props.talentHonor) {
      const updateData: TalentHonorUpdateRequest = {
        name: form.name,
        level: form.level,
        issuingUnit: form.issuingUnit,
        acquireTime: form.acquireTime,
        ranking: form.ranking,
        description: form.description,
        certificateNumber: form.certificateNumber,
        documentUrl: form.documentUrl
      }
      await talentHonorsApi.update(props.talentHonor.id!, updateData)
      ElMessage.success('人才荣誉更新成功')
    }

    emit('success')
    handleClose()
  } catch (__error) {
    console.error('提交失败:', error)
    if (error !== false) { // 不是表单验证错误
      ElMessage.error('操作失败')
    }
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  formRef.value?.resetFields()
}

// 查看证书文档
const handleViewDocument = (url: string) => {
  window.open(url, '_blank')
}

// 获取等级标签类型
const getLevelTagType = (level?: string) => {
  switch (level) {
    case 'NATIONAL':
      return 'danger'
    case 'PROVINCIAL':
      return 'warning'
    case 'MUNICIPAL':
      return 'success'
    case 'SCHOOL':
      return 'info'
    case 'DEPARTMENT':
      return ''
    default:
      return ''
  }
}

// 文件上传成功
   
const handleUploadSuccess = (response: unknown) => {
  if (response.code === 200) {
    form.documentUrl = response.data.url
    ElMessage.success('文件上传成功')
  } else {
    ElMessage.error(response.message || '文件上传失败')
  }
}

// 文件上传失败
const handleUploadError = () => {
  ElMessage.error('文件上传失败')
}

// 上传前检查
const beforeUpload = (file: File) => {
  const isValidType = [
    'application/pdf',
    'image/jpeg',
    'image/jpg',
    'image/png',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ].includes(file.type)
  
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isValidType) {
    ElMessage.error('只能上传 PDF、图片、Word 文档格式的文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过 10MB!')
    return false
  }
  return true
}
</script>

<style scoped>
.talent-honor-detail {
  padding: 20px 0;
}

.talent-honor-form {
  padding: 20px 0;
}

.dialog-footer {
  text-align: right;
}

.document-upload {
  width: 100%;
}

.upload-demo {
  width: 100%;
}

:deep(.el-upload__tip) {
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
  color: #303133;
}

:deep(.el-descriptions__content) {
  color: #606266;
}

/* 表单样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}

:deep(.el-date-editor.el-input) {
  border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .talent-honor-form .el-col {
    width: 100%;
    margin-bottom: 10px;
  }
}
</style>
