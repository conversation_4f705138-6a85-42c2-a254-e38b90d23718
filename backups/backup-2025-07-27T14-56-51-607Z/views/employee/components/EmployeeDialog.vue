<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    :width="mode === 'view' ? '800px' : '1000px'"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="mode === 'view'" class="employee-detail">
      <!-- 查看模式 -->
      <el-descriptions :column="2" border>
        <el-descriptions-item label="员工编号">{{ employee?.employeeCode }}</el-descriptions-item>
        <el-descriptions-item label="姓名">{{ employee?.name }}</el-descriptions-item>
        <el-descriptions-item label="英文名">{{ employee?.englishName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="性别">{{ employee?.genderName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="出生日期">{{ employee?.birthDate || '-' }}</el-descriptions-item>
        <el-descriptions-item label="年龄">{{ employee?.age ? `${employee.age}岁` : '-' }}</el-descriptions-item>
        <el-descriptions-item label="身份证号">{{ employee?.idCard || '-' }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ employee?.phone || '-' }}</el-descriptions-item>
        <el-descriptions-item label="邮箱">{{ employee?.email || '-' }}</el-descriptions-item>
        <el-descriptions-item label="所属组织">{{ employee?.organizationName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="岗位">{{ employee?.positionName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="员工类型">{{ employee?.employeeTypeName }}</el-descriptions-item>
        <el-descriptions-item label="在职状态">
          <el-tag :type="getStatusTagType(employee?.employmentStatus)" size="small">
            {{ employee?.employmentStatusName }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="入职日期">{{ employee?.hireDate }}</el-descriptions-item>
        <el-descriptions-item label="工龄">{{ employee?.workYears ? `${employee.workYears}年` : '-' }}</el-descriptions-item>
        <el-descriptions-item label="试用期结束日期">{{ employee?.probationEndDate || '-' }}</el-descriptions-item>
        <el-descriptions-item label="合同开始日期">{{ employee?.contractStartDate || '-' }}</el-descriptions-item>
        <el-descriptions-item label="合同结束日期">{{ employee?.contractEndDate || '-' }}</el-descriptions-item>
        <el-descriptions-item label="学历">{{ employee?.educationLevelName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="专业">{{ employee?.major || '-' }}</el-descriptions-item>
        <el-descriptions-item label="毕业院校">{{ employee?.graduateSchool || '-' }}</el-descriptions-item>
        <el-descriptions-item label="毕业日期">{{ employee?.graduationDate || '-' }}</el-descriptions-item>
        <el-descriptions-item label="政治面貌">{{ employee?.politicalStatusName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="婚姻状况">{{ employee?.maritalStatusName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="家庭住址" :span="2">{{ employee?.address || '-' }}</el-descriptions-item>
        <el-descriptions-item label="紧急联系人">{{ employee?.emergencyContact || '-' }}</el-descriptions-item>
        <el-descriptions-item label="紧急联系电话">{{ employee?.emergencyPhone || '-' }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ employee?.remark || '-' }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ employee?.createTime }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ employee?.updateTime || '-' }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <div v-else class="employee-form">
      <!-- 编辑/新增模式 -->
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        @submit.prevent
      >
        <el-tabs v-model="activeTab" type="border-card">
          <!-- 基本信息 -->
          <el-tab-pane label="基本信息" name="basic">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="员工编号" prop="employeeCode">
                  <el-input v-model="form.employeeCode" placeholder="请输入员工编号"   />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="姓名" prop="name">
                  <el-input v-model="form.name" placeholder="请输入姓名"   />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="英文名">
                  <el-input v-model="form.englishName" placeholder="请输入英文名"   />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="性别">
                  <el-select v-model="form.gender" placeholder="请选择性别" clearable>
                    <el-option
                      v-for="item in employeeOptions.gender"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                     />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="出生日期">
                  <el-date-picker
                    v-model="form.birthDate"
                    type="date"
                    placeholder="请选择出生日期"
                    style="width: 100%"
                   />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="身份证号">
                  <el-input v-model="form.idCard" placeholder="请输入身份证号"   />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="手机号">
                  <el-input v-model="form.phone" placeholder="请输入手机号"   />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="邮箱">
                  <el-input v-model="form.email" placeholder="请输入邮箱"   />
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>

          <!-- 工作信息 -->
          <el-tab-pane label="工作信息" name="work">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="所属组织" prop="organizationId">
                  <el-select v-model="form.organizationId" placeholder="请选择所属组织" clearable filterable>
                    <el-option
                      v-for="org in organizations"
                      :key="org.id"
                      :label="org.name"
                      :value="org.id"
                     />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="岗位">
                  <el-select v-model="form.positionId" placeholder="请选择岗位" clearable filterable>
                    <el-option
                      v-for="position in positions"
                      :key="position.id"
                      :label="position.name"
                      :value="position.id"
                     />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="员工类型" prop="employeeType">
                  <el-select v-model="form.employeeType" placeholder="请选择员工类型">
                    <el-option
                      v-for="item in employeeOptions.employeeType"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                     />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="在职状态">
                  <el-select v-model="form.employmentStatus" placeholder="请选择在职状态">
                    <el-option
                      v-for="item in employeeOptions.employmentStatus"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                     />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="入职日期" prop="hireDate">
                  <el-date-picker
                    v-model="form.hireDate"
                    type="date"
                    placeholder="请选择入职日期"
                    style="width: 100%"
                   />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="试用期结束日期">
                  <el-date-picker
                    v-model="form.probationEndDate"
                    type="date"
                    placeholder="请选择试用期结束日期"
                    style="width: 100%"
                   />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="合同开始日期">
                  <el-date-picker
                    v-model="form.contractStartDate"
                    type="date"
                    placeholder="请选择合同开始日期"
                    style="width: 100%"
                   />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="合同结束日期">
                  <el-date-picker
                    v-model="form.contractEndDate"
                    type="date"
                    placeholder="请选择合同结束日期"
                    style="width: 100%"
                   />
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>

          <!-- 教育背景 -->
          <el-tab-pane label="教育背景" name="education">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="学历">
                  <el-select v-model="form.educationLevel" placeholder="请选择学历" clearable>
                    <el-option
                      v-for="item in employeeOptions.educationLevel"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                     />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="专业">
                  <el-input v-model="form.major" placeholder="请输入专业"   />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="毕业院校">
                  <el-input v-model="form.graduateSchool" placeholder="请输入毕业院校"   />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="毕业日期">
                  <el-date-picker
                    v-model="form.graduationDate"
                    type="date"
                    placeholder="请选择毕业日期"
                    style="width: 100%"
                   />
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>

          <!-- 个人信息 -->
          <el-tab-pane label="个人信息" name="personal">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="政治面貌">
                  <el-select v-model="form.politicalStatus" placeholder="请选择政治面貌" clearable>
                    <el-option
                      v-for="item in employeeOptions.politicalStatus"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                     />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="婚姻状况">
                  <el-select v-model="form.maritalStatus" placeholder="请选择婚姻状况" clearable>
                    <el-option
                      v-for="item in employeeOptions.maritalStatus"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                     />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="家庭住址">
                  <el-input v-model="form.address" placeholder="请输入家庭住址"   />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="紧急联系人">
                  <el-input v-model="form.emergencyContact" placeholder="请输入紧急联系人"   />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="紧急联系电话">
                  <el-input v-model="form.emergencyPhone" placeholder="请输入紧急联系电话"   />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="备注">
                  <el-input
                    v-model="form.remark"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入备注"
                    />
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button v-if="mode !== 'view'" type="primary" :loading="loading" @click="handleSubmit">
          {{ mode === 'add' ? '创建' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { employeeApi, employeeOptions, type Employee, type EmployeeCreateRequest } from '@/api/employee'
import { useOrganizationStore } from '@/stores/modules/organization'
import { positionApi } from '@/api/position'

// Props
interface Props {
  visible: boolean
  employee: Employee | null
  mode: 'view' | 'add' | 'edit'
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  employee: null,
  mode: 'view'
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const loading = ref(false)
const formRef = ref<FormInstance>()
const activeTab = ref('basic')

// 组织和岗位数据
const organizationStore = useOrganizationStore()
const organizations = ref<any[]>([])
const positions = ref<any[]>([])

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 对话框标题
const dialogTitle = computed(() => {
  switch (props.mode) {
    case 'view':
      return '员工详情'
    case 'add':
      return '新增员工'
    case 'edit':
      return '编辑员工'
    default:
      return ''
  }
})

// 表单数据
const form = reactive<EmployeeCreateRequest>({
  employeeCode: '',
  name: '',
  englishName: '',
  gender: undefined,
  birthDate: '',
  idCard: '',
  phone: '',
  email: '',
  organizationId: 0,
  positionId: undefined,
  employeeType: 'FULL_TIME' as unknown, // 临时修复枚举类型
  employmentStatus: 'ACTIVE' as unknown, // 临时修复枚举类型
  hireDate: '',
  probationEndDate: '',
  contractStartDate: '',
  contractEndDate: '',
  educationLevel: undefined,
  major: '',
  graduateSchool: '',
  graduationDate: '',
  politicalStatus: undefined,
  maritalStatus: undefined,
  address: '',
  emergencyContact: '',
  emergencyPhone: '',
  avatarUrl: '',
  remark: '',
  sortOrder: 0
})

// 表单验证规则
const rules: FormRules = {
  employeeCode: [
    { required: true, message: '请输入员工编号', trigger: 'blur' },
    { min: 2, max: 32, message: '员工编号长度必须在2-32个字符之间', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 64, message: '姓名长度必须在2-64个字符之间', trigger: 'blur' }
  ],
  organizationId: [
    { required: true, message: '请选择所属组织', trigger: 'change' }
  ],
  employeeType: [
    { required: true, message: '请选择员工类型', trigger: 'change' }
  ],
  hireDate: [
    { required: true, message: '请选择入职日期', trigger: 'change' }
  ],
  idCard: [
    {
      pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
      message: '身份证号格式不正确',
      trigger: 'blur'
    }
  ],
  phone: [
    {
      pattern: /^1[3-9]\d{9}$/,
      message: '手机号格式不正确',
      trigger: 'blur'
    }
  ],
  email: [
    {
      type: 'email',
      message: '邮箱格式不正确',
      trigger: 'blur'
    }
  ],
  emergencyPhone: [
    {
      pattern: /^1[3-9]\d{9}$/,
      message: '紧急联系电话格式不正确',
      trigger: 'blur'
    }
  ]
}

// 加载组织和岗位数据
const loadOrganizationsAndPositions = async () => {
  try {
    // 加载组织数据
    const orgTree = await organizationStore.getOrganizationTree()
    organizations.value = flattenOrganizations(orgTree)
    
    // 加载岗位数据
    const {data: _data} =  await positionApi.getPositionList({ page: 1, pageSize: 1000 })
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
    positions.value 
}

.employee-form {
  padding: 10px 0;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-tabs__content) {
  padding: 20px 0;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
}
</style>
