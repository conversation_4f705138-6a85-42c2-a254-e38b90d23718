<template>
  <el-dialog
    v-model="dialogVisible"
    title="清理过期日志"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="clean-expired">
      <!-- 清理说明 -->
      <div class="clean-info">
        <el-alert
          title="清理说明"
          type="warning"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>此操作将永久删除指定天数之前的数据变更日志，删除后无法恢复。</p>
            <p>建议在清理前先导出重要的历史数据。</p>
          </template>
        </el-alert>
      </div>

      <!-- 清理配置 -->
      <div class="clean-config">
        <el-form
          ref="formRef"
          :model="form"
          :rules="formRules"
          label-width="120px"
          label-position="right"
        >
          <el-form-item label="保留天数" prop="days">
            <el-input-number
              v-model="form.days"
              :min="1"
              :max="3650"
              controls-position="right"
              style="width: 100%"
              />
            <div class="form-tip">
              将删除 {{ form.days }} 天前的所有日志记录
            </div>
          </el-form-item>
          
          <el-form-item label="预估影响">
            <div class="impact-info">
              <el-button type="primary" size="small" @click="handlePreview" :loading="previewLoading">
                <el-icon><View /></el-icon>
                预览影响
              </el-button>
              <div v-if="previewResult" class="preview-result">
                <el-tag type="warning" size="small">
                  预计删除 {{ previewResult.estimatedCount }} 条记录
                </el-tag>
                <el-tag type="info" size="small" style="margin-left: 8px;">
                  约 {{ formatFileSize(previewResult.estimatedSize) }}
                </el-tag>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <!-- 确认选项 -->
      <div class="confirm-options">
        <el-checkbox v-model="form.confirmed">
          我已了解此操作的风险，确认执行清理
        </el-checkbox>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="danger"
          :loading="loading"
          :disabled="!form.confirmed"
          @click="handleClean"
        >
          <el-icon><DeleteFilled /></el-icon>
          确认清理
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'CleanExpiredDialog'
})
 
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { View, DeleteFilled } from '@element-plus/icons-vue'
import { dataChangeLogApi } from '@/api/dataChangeLog'

// Props
interface Props {
  visible: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const loading = ref(false)
const previewLoading = ref(false)
const formRef = ref<FormInstance>()

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 表单数据
const form = reactive({
  days: 90,
  confirmed: false
})

// 预览结果
const previewResult = ref<{
  estimatedCount: number
  estimatedSize: number
} | null>(null)

// 表单验证规则
const formRules: FormRules = {
  days: [
    { required: true, message: '请输入保留天数', trigger: 'blur' },
    { type: 'number', min: 1, max: 3650, message: '保留天数必须在1到3650天之间', trigger: 'blur' }
  ]
}

// 预览影响
const handlePreview = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validateField('days')
    previewLoading.value = true
    
    // 模拟预览API调用（实际应该调用后端API）
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟预览结果
    const estimatedCount = Math.floor(Math.random() * 10000) + 1000
    const estimatedSize = estimatedCount * 2048 // 假设每条记录约2KB
    
    previewResult.value = {
      estimatedCount,
      estimatedSize
    }
    
    ElMessage.success('预览完成')
  } catch (__error) {
    console.error('预览失败:', error)
    if (error !== false) { // 不是表单验证错误
      ElMessage.error('预览失败')
    }
  } finally {
    previewLoading.value = false
  }
}

// 执行清理
const handleClean = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    await ElMessageBox.confirm(
      `确定要删除 ${form.days} 天前的所有日志记录吗？此操作不可恢复！`,
      '确认清理',
      {
        confirmButtonText: '确定清理',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }
    )
    
    loading.value = true
    const result = await dataChangeLogApi.cleanExpiredLogs(form.days)
    
    ElMessage.success(`清理完成，共删除 ${result.deletedCount} 条记录`)
    emit('success')
    handleClose()
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('清理失败:', error)
      ElMessage.error('清理失败')
    }
  } finally {
    loading.value = false
  }
}

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  form.days = 90
  form.confirmed = false
  previewResult.value = null
  formRef.value?.resetFields()
}
</script>

<style scoped>
.clean-expired {
  padding: 10px 0;
}

.clean-info {
  margin-bottom: 20px;
}

.clean-config {
  margin-bottom: 20px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.impact-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.preview-result {
  display: flex;
  align-items: center;
}

.confirm-options {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 6px;
}

.dialog-footer {
  text-align: right;
}

/* 表单样式 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input-number) {
  width: 100%;
}

/* 警告框样式 */
:deep(.el-alert) {
  border-radius: 6px;
}

:deep(.el-alert__content) {
  line-height: 1.5;
}

:deep(.el-alert__content p) {
  margin: 0 0 8px 0;
}

:deep(.el-alert__content p:last-child) {
  margin-bottom: 0;
}

/* 复选框样式 */
:deep(.el-checkbox) {
  font-weight: 500;
}

:deep(.el-checkbox__label) {
  color: #f56c6c;
}
</style>
