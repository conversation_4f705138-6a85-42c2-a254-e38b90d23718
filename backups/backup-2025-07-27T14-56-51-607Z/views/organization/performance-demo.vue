<template>
  <div class="performance-demo">
    <el-page-header @back="$router.back()" title="性能优化演示">
      <template #content>
        <span>组织管理模块性能优化功能展示</span>
      </template>
    </el-page-header>

    <el-divider />

    <!-- 性能优化组件展示 -->
    <el-row :gutter="16">
      <!-- 虚拟滚动树形组件 -->
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>虚拟滚动组织树</span>
              <el-tag type="success" size="small">性能优化</el-tag>
            </div>
          </template>

          <div class="demo-container">
            <HrOrgTreeVirtual
              v-model="selectedOrgId"
              :data="organizationData"
              :node-height="32"
              :container-height="300"
              :show-checkbox="false"
              @node-click="handleOrgTreeClick"
            />
          </div>

          <div class="demo-info">
            <p>支持大数据量组织树渲染，使用虚拟滚动技术</p>
            <el-tag size="small">数据量: {{ organizationData.length }} 个节点</el-tag>
          </div>
        </el-card>
      </el-col>

      <!-- Canvas组织架构图 -->
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>Canvas组织架构图</span>
              <el-tag type="warning" size="small">Canvas渲染</el-tag>
            </div>
          </template>

          <div class="demo-container">
            <HrOrgChartOptimized
              :data="organizationData"
              :show-performance-info="true"
              :max-render-nodes="500"
              @node-click="handleChartNodeClick"
            />
          </div>

          <div class="demo-info">
            <p>使用Canvas渲染，支持视锥体裁剪和性能监控</p>
          </div>
        </el-card>
      </el-col>

      <!-- 虚拟滚动表格 -->
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>虚拟滚动表格</span>
              <el-tag type="info" size="small">懒加载</el-tag>
            </div>
          </template>

          <div class="demo-container">
            <HrVirtualTable
              :data="tableData"
              :columns="tableColumns"
              :height="300"
              :row-height="48"
              :lazy="true"
              @row-click="handleTableRowClick"
            />
          </div>

          <div class="demo-info">
            <p>虚拟滚动表格，支持大数据量展示</p>
            <el-tag size="small">数据量: {{ tableData.length }} 行</el-tag>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-divider />

    <!-- 高性能图表展示 -->
    <el-row :gutter="16">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>高性能趋势图表</span>
              <el-tag type="success" size="small">数据采样</el-tag>
            </div>
          </template>

          <HrHighPerformanceChart
            :option="lineChartOption"
            :height="350"
            :show-controls="true"
            :show-info="true"
            :show-performance-monitor="true"
            chart-id="trend-chart"
            @performance-update="handlePerformanceUpdate"
          />
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>组织统计图表</span>
              <el-tag type="warning" size="small">动态更新</el-tag>
            </div>
          </template>

          <HrHighPerformanceChart
            :option="pieChartOption"
            :height="350"
            :auto-update-interval="5000"
            chart-id="stats-chart"
          />
        </el-card>
      </el-col>
    </el-row>

    <el-divider />

    <!-- 缓存管理面板 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>API缓存管理</span>
          <el-tag type="info" size="small">智能缓存</el-tag>
        </div>
      </template>

      <HrCacheManager :show-controls="true" :show-monitor="true" :auto-refresh-interval="3000" />
    </el-card>

    <el-divider />

    <!-- 性能监控面板 -->
    <el-card>
      <template #header>
        <span>性能监控仪表板</span>
      </template>

      <div class="performance-dashboard">
        <el-row :gutter="16">
          <el-col :span="6">
            <div class="metric-card">
              <div class="metric-value">{{ performanceMetrics.renderTime }}ms</div>
              <div class="metric-label">平均渲染时间</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="metric-card">
              <div class="metric-value">{{ performanceMetrics.memoryUsage }}MB</div>
              <div class="metric-label">内存使用</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="metric-card">
              <div class="metric-value">{{ performanceMetrics.cacheHitRate }}%</div>
              <div class="metric-label">缓存命中率</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="metric-card">
              <div class="metric-value">{{ performanceMetrics.fps }}</div>
              <div class="metric-label">帧率 (FPS)</div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="16" style="margin-top: 16px">
          <el-col :span="24">
            <div class="performance-tips">
              <h4>性能优化建议</h4>
              <ul>
                <li v-for="tip in performanceTips" :key="tip.id" :class="tip.type">
                  {{ tip.message }}
                </li>
              </ul>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 数据生成控制 -->
    <el-card style="margin-top: 16px">
      <template #header>
        <span>测试数据控制</span>
      </template>

      <el-form :model="testConfig" :inline="true">
        <el-form-item label="组织数据量">
          <el-select v-model="testConfig.orgDataSize" @change="generateOrgData">
            <el-option label="小量 (100)" :value="100" />
            <el-option label="中量 (1000)" :value="1000" />
            <el-option label="大量 (5000)" :value="5000" />
            <el-option label="超大量 (10000)" :value="10000" />
          </el-select>
        </el-form-item>

        <el-form-item label="表格数据量">
          <el-select v-model="testConfig.tableDataSize" @change="generateTableData">
            <el-option label="小量 (500)" :value="500" />
            <el-option label="中量 (2000)" :value="2000" />
            <el-option label="大量 (10000)" :value="10000" />
            <el-option label="超大量 (50000)" :value="50000" />
          </el-select>
        </el-form-item>

        <el-form-item label="图表数据点">
          <el-select v-model="testConfig.chartDataPoints" @change="generateChartData">
            <el-option label="标准 (200)" :value="200" />
            <el-option label="密集 (1000)" :value="1000" />
            <el-option label="超密集 (5000)" :value="5000" />
            <el-option label="极限 (20000)" :value="20000" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button @click="runPerformanceTest" type="primary">运行性能测试</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/**
 * @name 性能优化演示页面
 * @description 展示组织管理模块的各种性能优化功能
 */
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import HrOrgTreeVirtual from '@/components/organization/HrOrgTreeVirtual.vue'
import HrOrgChartOptimized from '@/components/organization/HrOrgChartOptimized.vue'
import HrVirtualTable from '@/components/common/HrVirtualTable.vue'
import HrHighPerformanceChart from '@/components/common/HrHighPerformanceChart.vue'
import HrCacheManager from '@/components/common/HrCacheManager.vue'
import type { Organization } from '@/types/organization-view'
import type { TableColumn } from '@/components/common/HrVirtualTable.vue'

// ===== 响应式数据 =====
const selectedOrgId = ref('')
const organizationData = ref<Organization[]>([])
const tableData = ref<any[]>([])

const testConfig = ref({
  orgDataSize: 1000,
  tableDataSize: 2000,
  chartDataPoints: 1000
})

const performanceMetrics = ref({
  renderTime: 0,
  memoryUsage: 0,
  cacheHitRate: 0,
  fps: 60
})

// ===== 表格配置 =====
const tableColumns: TableColumn[] = [
  { prop: 'id', label: 'ID', width: 80 },
  { prop: 'name', label: '姓名', width: 120 },
  { prop: 'department', label: '部门', width: 150 },
  { prop: 'position', label: '职位', width: 120 },
  { prop: 'email', label: '邮箱', width: 200 },
  { prop: 'phone', label: '电话', width: 130 },
  { prop: 'status', label: '状态', width: 80 }
]

// ===== 图表配置 =====
const lineChartOption = computed(() => ({
  title: {
    text: '员工数量趋势',
    left: 'center'
  },
  tooltip: {
    trigger: 'axis'
  },
  xAxis: {
    type: 'category',
    data: generateTimeAxis()
  },
  yAxis: {
    type: 'value',
    name: '人数'
  },
  series: [
    {
      name: '在职员工',
      type: 'line',
      smooth: true,
      data: generateTrendData(testConfig.value.chartDataPoints)
    },
    {
      name: '新入职',
      type: 'line',
      smooth: true,
      data: generateTrendData(testConfig.value.chartDataPoints, 0.3)
    }
  ]
}))

const pieChartOption = computed(() => ({
  title: {
    text: '部门人员分布',
    left: 'center'
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  series: [
    {
      name: '部门分布',
      type: 'pie',
      radius: '65%',
      data: [
        { value: 335, name: '技术部' },
        { value: 310, name: '市场部' },
        { value: 234, name: '销售部' },
        { value: 135, name: '人事部' },
        { value: 1548, name: '其他部门' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
}))

// ===== 性能提示 =====
const performanceTips = computed(() => {
  const tips = []

  if (performanceMetrics.value.renderTime > 100) {
    tips.push({
      id: 'render-time',
      type: 'warning',
      message: '渲染时间较长，建议启用数据采样或减少数据量'
    })
  }

  if (performanceMetrics.value.memoryUsage > 100) {
    tips.push({
      id: 'memory',
      type: 'error',
      message: '内存使用过高，建议清理缓存或优化数据结构'
    })
  }

  if (performanceMetrics.value.cacheHitRate < 50) {
    tips.push({
      id: 'cache',
      type: 'info',
      message: '缓存命中率较低，建议预热常用数据'
    })
  }

  if (tips.length === 0) {
    tips.push({
      id: 'good',
      type: 'success',
      message: '性能表现良好，所有指标都在正常范围内'
    })
  }

  return tips
})

// ===== 方法 =====
/**
 * 生成组织数据
 */
const generateOrgData = () => {
  const size = testConfig.value.orgDataSize
  const data: Organization[] = []

  for (let i = 0; i < size; i++) {
    data.push({
      id: `org_${i}`,
      name: `组织单位_${i}`,
      code: `ORG${String(i).padStart(4, '0')}`,
      type: ['college', 'department', 'office', 'center'][i % 4],
      status: 'active',
      parentId: i > 0 ? `org_${Math.floor(i / 3)}` : undefined,
      establishment: {
        approved: Math.floor(Math.random() * 100) + 20,
        current: Math.floor(Math.random() * 80) + 10
      },
      children: []
    })
  }

  // 构建树形结构
  const map = new Map<string, Organization>()
  const roots: Organization[] = []

  data.forEach(item => map.set(item.id, item))

  data.forEach(item => {
    if (item.parentId && map.has(item.parentId)) {
      map.get(item.parentId)!.children!.push(item)
    } else {
      roots.push(item)
    }
  })

  organizationData.value = roots
  ElMessage.success(`已生成 ${size} 条组织数据`)
}

/**
 * 生成表格数据
 */
const generateTableData = () => {
  const size = testConfig.value.tableDataSize
  const data = []

  for (let i = 0; i < size; i++) {
    data.push({
      id: i + 1,
      name: `员工_${i + 1}`,
      department: `部门_${(i % 10) + 1}`,
      position: `职位_${(i % 5) + 1}`,
      email: `user${i}@company.com`,
      phone: `138${String(i).padStart(8, '0').slice(0, 8)}`,
      status: ['active', 'inactive', 'pending'][i % 3]
    })
  }

  tableData.value = data
  ElMessage.success(`已生成 ${size} 条表格数据`)
}

/**
 * 生成图表数据
 */
const generateChartData = () => {
  ElMessage.success(`已更新图表数据点数量: ${testConfig.value.chartDataPoints}`)
}

/**
 * 生成时间轴
 */
const generateTimeAxis = () => {
  const points = testConfig.value.chartDataPoints
  const axis = []
  const now = new Date()

  for (let i = 0; i < points; i++) {
    const date = new Date(now.getTime() - (points - i) * 24 * 60 * 60 * 1000)
    axis.push(date.toISOString().split('T')[0])
  }

  return axis
}

/**
 * 生成趋势数据
 */
const generateTrendData = (points: number, factor = 1) => {
  const data = []
  let base = Math.floor(Math.random() * 1000) + 500

  for (let i = 0; i < points; i++) {
    const variation = (Math.random() - 0.5) * 100 * factor
    base += variation
    data.push(Math.max(0, Math.floor(base)))
  }

  return data
}

/**
 * 事件处理
 */
const handleOrgTreeClick = (data: Organization) => {
  console.log('Organization tree clicked:', data)
}

const handleChartNodeClick = (node: Organization) => {
  console.log('Chart node clicked:', node)
}

const handleTableRowClick = (row: any, index: number) => {
  console.log('Table row clicked:', row, index)
}

const handlePerformanceUpdate = (stats: any) => {
  performanceMetrics.value.renderTime = stats.renderTime || 0
  performanceMetrics.value.memoryUsage = Math.round((stats.memoryUsage || 0) / (1024 * 1024))
  performanceMetrics.value.fps = stats.fps || 60
}

/**
 * 运行性能测试
 */
const runPerformanceTest = async () => {
  ElMessage.info('开始性能测试...')

  const startTime = performance.now()

  // 重新生成所有数据
  generateOrgData()
  generateTableData()
  generateChartData()

  const endTime = performance.now()
  const duration = Math.round(endTime - startTime)

  ElMessage.success(`性能测试完成，耗时: ${duration}ms`)

  // 更新性能指标
  performanceMetrics.value.renderTime = duration
}

// ===== 生命周期 =====
onMounted(() => {
  generateOrgData()
  generateTableData()
})
</script>

<style lang="scss" scoped>
.performance-demo {
  padding: 16px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .demo-container {
    height: 300px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    overflow: hidden;
  }

  .demo-info {
    margin-top: 12px;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 4px;
    font-size: 12px;
    color: #6c757d;

    p {
      margin: 0 0 4px 0;
    }
  }

  .performance-dashboard {
    .metric-card {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 20px;
      border-radius: 8px;
      text-align: center;

      .metric-value {
        font-size: 28px;
        font-weight: bold;
        margin-bottom: 4px;
      }

      .metric-label {
        font-size: 12px;
        opacity: 0.8;
      }
    }

    .performance-tips {
      background: #f8f9fa;
      padding: 16px;
      border-radius: 8px;

      h4 {
        margin: 0 0 12px 0;
        color: #495057;
      }

      ul {
        margin: 0;
        padding-left: 16px;

        li {
          margin: 8px 0;

          &.success {
            color: #28a745;
          }

          &.warning {
            color: #ffc107;
          }

          &.error {
            color: #dc3545;
          }

          &.info {
            color: #17a2b8;
          }
        }
      }
    }
  }
}
</style>
