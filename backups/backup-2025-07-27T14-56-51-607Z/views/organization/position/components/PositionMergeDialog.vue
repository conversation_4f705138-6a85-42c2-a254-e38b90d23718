<template>
  <el-dialog
    v-model="dialogVisible"
    title="岗位合并"
    width="1000px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-steps :active="currentStep" finish-status="success" class="merge-steps">
      <el-step title="选择岗位"  />
      <el-step title="合并配置"  />
      <el-step title="确认合并"  />
    </el-steps>

    <!-- 步骤1: 选择岗位 -->
    <div v-if="currentStep === 0" class="step-content">
      <el-alert
        title="请选择需要合并的岗位（至少2个）"
        type="info"
        :closable="false"
        class="mb-20"
       />
      
      <div class="position-selection">
        <div class="selected-positions">
          <h4>已选择的岗位 ({{ selectedPositions.length }})</h4>
          <el-tag
            v-for="pos in selectedPositions"
            :key="pos.positionId"
            closable
            size="large"
            class="position-tag"
            @close="handleRemovePosition(pos)"
          >
            {{ pos.positionName }} ({{ pos.positionCode }})
          </el-tag>
          
          <el-button
            v-if="selectedPositions.length < 5"
            type="primary"
            plain
            @click="showPositionSelector = true"
          >
            <el-icon><Plus /></el-icon>
            添加岗位
          </el-button>
        </div>
        
        <!-- 岗位对比 -->
        <el-table
          v-if="selectedPositions.length >= 2"
          :data="comparisonData"
          class="comparison-table"
          :show-header="false"
        >
          <el-table-column prop="attribute" label="属性" width="120" fixed>
            <template #default="{ row }">
              <span class="attribute-label">{{ row.attribute }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-for="(pos, index) in selectedPositions"
            :key="pos.positionId"
            :label="pos.positionName"
            :width="200"
          >
            <template #default="{ row }">
              <span :class="{ 'text-danger': row.values[index].highlight }">
                {{ row.values[index].value }}
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 步骤2: 合并配置 -->
    <div v-if="currentStep === 1" class="step-content">
      <el-form :model="mergeForm" :rules="mergeRules" ref="mergeFormRef" label-width="120px">
        <el-divider content-position="left">目标岗位信息</el-divider>
        
        <el-form-item label="目标岗位" prop="targetPositionId">
          <el-radio-group v-model="mergeForm.targetPositionId">
            <el-radio
              v-for="pos in selectedPositions"
              :key="pos.positionId"
              :value="pos.positionId"
            >
              {{ pos.positionName }} ({{ pos.positionCode }})
            </el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="合并后名称" prop="mergedName">
          <el-input
            v-model="mergeForm.mergedName"
            placeholder="请输入合并后的岗位名称"
            maxlength="50"
            show-word-limit
            />
        </el-form-item>
        
        <el-form-item label="合并后编码" prop="mergedCode">
          <el-input
            v-model="mergeForm.mergedCode"
            placeholder="请输入合并后的岗位编码"
            maxlength="20"
            />
        </el-form-item>
        
        <el-divider content-position="left">人员处理方案</el-divider>
        
        <el-form-item label="人员安置" prop="staffingPlan">
          <el-radio-group v-model="mergeForm.staffingPlan">
            <el-radio value="ALL_TO_TARGET">全部转移到目标岗位</el-radio>
            <el-radio value="SELECTIVE">选择性安置</el-radio>
            <el-radio value="REDISTRIBUTE">重新分配</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <!-- 选择性安置 -->
        <el-form-item
          v-if="mergeForm.staffingPlan === 'SELECTIVE'"
          label="人员安置明细"
        >
          <el-table :data="staffingDetails" border>
            <el-table-column prop="employeeName" label="员工姓名" width="120"  />
            <el-table-column prop="currentPosition" label="当前岗位" width="150"  />
            <el-table-column label="安置方案" width="200">
              <template #default="{ row }">
                <el-select v-model="row.placement" placeholder="请选择">
                  <el-option label="转移到目标岗位" value="TARGET"  />
                  <el-option label="调整到其他岗位" value="OTHER"  />
                  <el-option label="待定" value="PENDING"  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="备注">
              <template #default="{ row }">
                <el-input v-model="row.remark" placeholder="备注信息"   />
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        
        <el-divider content-position="left">其他配置</el-divider>
        
        <el-form-item label="编制处理" prop="establishmentPlan">
          <el-radio-group v-model="mergeForm.establishmentPlan">
            <el-radio value="SUM">累加所有岗位编制</el-radio>
            <el-radio value="MAX">取最大编制数</el-radio>
            <el-radio value="CUSTOM">自定义编制数</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item
          v-if="mergeForm.establishmentPlan === 'CUSTOM'"
          label="自定义编制"
          prop="customEstablishment"
        >
          <el-input-number
            v-model="mergeForm.customEstablishment"
            :min="1"
            :max="999"
            controls-position="right"
            />
        </el-form-item>
        
        <el-form-item label="合并原因" prop="reason">
          <el-input
            v-model="mergeForm.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入合并原因"
            maxlength="200"
            show-word-limit
            />
        </el-form-item>
      </el-form>
    </div>

    <!-- 步骤3: 确认合并 -->
    <div v-if="currentStep === 2" class="step-content">
      <el-alert
        title="请仔细核对合并信息，合并操作不可撤销"
        type="warning"
        :closable="false"
        class="mb-20"
       />
      
      <el-descriptions :column="2" border class="merge-summary">
        <el-descriptions-item label="合并岗位数">
          {{ selectedPositions.length }}
        </el-descriptions-item>
        <el-descriptions-item label="目标岗位">
          {{ getTargetPositionName() }}
        </el-descriptions-item>
        <el-descriptions-item label="合并后名称">
          {{ mergeForm.mergedName }}
        </el-descriptions-item>
        <el-descriptions-item label="合并后编码">
          {{ mergeForm.mergedCode }}
        </el-descriptions-item>
        <el-descriptions-item label="影响人员数">
          {{ affectedEmployeeCount }}
        </el-descriptions-item>
        <el-descriptions-item label="新编制数">
          {{ calculateNewEstablishment() }}
        </el-descriptions-item>
        <el-descriptions-item label="人员安置方案" :span="2">
          {{ getStaffingPlanText() }}
        </el-descriptions-item>
        <el-descriptions-item label="合并原因" :span="2">
          {{ mergeForm.reason }}
        </el-descriptions-item>
      </el-descriptions>
      
      <!-- 合并影响分析 -->
      <el-card shadow="never" class="impact-analysis">
        <template #header>
          <span class="card-title">合并影响分析</span>
        </template>
        <ul class="impact-list">
          <li>
            <el-icon><InfoFilled /></el-icon>
            {{ selectedPositions.length - 1 }} 个岗位将被撤销
          </li>
          <li>
            <el-icon><User /></el-icon>
            {{ affectedEmployeeCount }} 名员工需要重新安置
          </li>
          <li>
            <el-icon><Document /></el-icon>
            相关的岗位职责和要求将被整合
          </li>
          <li>
            <el-icon><Calendar /></el-icon>
            历史记录将被保留以供审计
          </li>
        </ul>
      </el-card>
    </div>

    <template #footer>
      <el-button @click="handlePrevious" v-if="currentStep > 0">
        上一步
      </el-button>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button
        type="primary"
        @click="handleNext"
        v-if="currentStep < 2"
        :disabled="!canProceed"
      >
        下一步
      </el-button>
      <el-button
        type="danger"
        @click="handleConfirmMerge"
        v-if="currentStep === 2"
        :loading="merging"
      >
        确认合并
      </el-button>
    </template>
  </el-dialog>

  <!-- 岗位选择对话框 -->
  <el-dialog
    v-model="showPositionSelector"
    title="选择岗位"
    width="800px"
    append-to-body
  >
    <el-table
      :data="availablePositions"
      @selection-change="handleSelectionChange"
      ref="selectorTable"
    >
      <el-table-column type="selection" width="55"  />
      <el-table-column prop="positionCode" label="岗位编码" width="120"  />
      <el-table-column prop="positionName" label="岗位名称"  />
      <el-table-column prop="institutionName" label="所属机构"  />
      <el-table-column prop="actualCount" label="在岗人数" width="100"  />
    </el-table>
    
    <template #footer>
      <el-button @click="showPositionSelector = false">取消</el-button>
      <el-button type="primary" @click="handleConfirmSelection">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'PositionMergeDialog'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { Plus, InfoFilled, User, Document, Calendar } from '@element-plus/icons-vue'
import { usePositionStore } from '@/stores/modules/position'
import { positionApi } from '@/api/position'
import type { Position } from '@/types/position'

interface Props {
  modelValue: boolean
  sourcePositions?: Position[]
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

interface StaffingDetail {
  employeeId: string
  employeeName: string
  currentPosition: string
  placement: 'TARGET' | 'OTHER' | 'PENDING'
  remark: string
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const positionStore = usePositionStore()
const mergeFormRef = ref<FormInstance>()
const selectorTable = ref<unknown>()

// 状态
const currentStep = ref(0)
const merging = ref(false)
const showPositionSelector = ref(false)
const selectedPositions = ref<Position[]>([])
const tempSelection = ref<Position[]>([])
const availablePositions = ref<Position[]>([])
const staffingDetails = ref<StaffingDetail[]>([])
const affectedEmployeeCount = ref(0)

// 表单数据
const mergeForm = ref({
  targetPositionId: '',
  mergedName: '',
  mergedCode: '',
  staffingPlan: 'ALL_TO_TARGET',
  establishmentPlan: 'SUM',
  customEstablishment: 1,
  reason: ''
})

// 表单验证规则
const mergeRules: FormRules = {
  targetPositionId: [
    { required: true, message: '请选择目标岗位', trigger: 'change' }
  ],
  mergedName: [
    { required: true, message: '请输入合并后的岗位名称', trigger: 'blur' }
  ],
  mergedCode: [
    { required: true, message: '请输入合并后的岗位编码', trigger: 'blur' }
  ],
  staffingPlan: [
    { required: true, message: '请选择人员安置方案', trigger: 'change' }
  ],
  establishmentPlan: [
    { required: true, message: '请选择编制处理方案', trigger: 'change' }
  ],
  customEstablishment: [
    { required: true, message: '请输入自定义编制数', trigger: 'blur', type: 'number' }
  ],
  reason: [
    { required: true, message: '请输入合并原因', trigger: 'blur' },
    { min: 10, max: 200, message: '合并原因长度在 10 到 200 个字符', trigger: 'blur' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 是否可以继续下一步
const canProceed = computed(() => {
  switch (currentStep.value) {
    case 0:
      return selectedPositions.value.length >= 2
    case 1:
      return true // 表单验证在点击时进行
    default:
      return true
  }
})

// 对比数据
const comparisonData = computed(() => {
  const attributes = [
    { key: 'positionCategory', label: '岗位类别' },
    { key: 'positionLevel', label: '岗位等级' },
    { key: 'establishmentCount', label: '编制数' },
    { key: 'actualCount', label: '实际人数' },
    { key: 'institutionName', label: '所属机构' }
  ]
  
  return attributes.map(attr => {
    const values = selectedPositions.value.map(pos => {
      const value = pos[attr.key as keyof Position] || '-'
      const allSame = selectedPositions.value.every(p =>
        p[attr.key as keyof Position] === value
      )
      return {
        value: value.toString(),
        highlight: !allSame
      }
    })
    
    return {
      attribute: attr.label,
      values
    }
  })
})

// 获取目标岗位名称
const getTargetPositionName = () => {
  const target = selectedPositions.value.find(
    pos => pos.positionId === mergeForm.value.targetPositionId
  )
  return target ? `${target.positionName} (${target.positionCode})` : '-'
}

// 获取人员安置方案文本
const getStaffingPlanText = () => {
  const planMap = {
    'ALL_TO_TARGET': '所有人员转移到目标岗位',
    'SELECTIVE': '根据具体情况选择性安置',
    'REDISTRIBUTE': '重新分配到其他岗位'
  }
  return planMap[mergeForm.value.staffingPlan] || '-'
}

// 计算新编制数
const calculateNewEstablishment = () => {
  switch (mergeForm.value.establishmentPlan) {
    case 'SUM':
      return selectedPositions.value.reduce(
        (sum, pos) => sum + (pos.establishmentCount || 0), 0
      )
    case 'MAX':
      return Math.max(
        ...selectedPositions.value.map(pos => pos.establishmentCount || 0)
      )
    case 'CUSTOM':
      return mergeForm.value.customEstablishment
    default:
      return 0
  }
}

// 初始化数据
const initializeData = async () => {
  // 如果有传入的源岗位，直接使用
  if (props.sourcePositions && props.sourcePositions.length > 0) {
    selectedPositions.value = [...props.sourcePositions]
  }
  
  // 获取可选岗位列表
  try {
    const {list} =  await positionStore.fetchPositions({ size: 100 })
    availablePositions.value 
}

.step-content {
  min-height: 400px;
  
  .mb-20 {
    margin-bottom: 20px;
  }
}

.position-selection {
  .selected-positions {
    margin-bottom: 20px;
    
    h4 {
      margin: 0 0 12px;
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
    
    .position-tag {
      margin: 0 8px 8px 0;
    }
  }
  
  .comparison-table {
    margin-top: 20px;
    
    .attribute-label {
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
    
    .text-danger {
      color: var(--el-color-danger);
      font-weight: 600;
    }
  }
}

.merge-summary {
  margin-bottom: 20px;
}

.impact-analysis {
  .card-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
  
  .impact-list {
    margin: 0;
    padding: 0;
    list-style: none;
    
    li {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
      font-size: 14px;
      color: var(--el-text-color-regular);
      
      .el-icon {
        color: var(--el-color-warning);
      }
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>