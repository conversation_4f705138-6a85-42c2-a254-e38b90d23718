<template>
  <el-drawer
    v-model="drawerVisible"
    :title="drawerTitle"
    size="800px"
    :close-on-click-modal="false"
    append-to-body
  >
    <div class="position-detail" v-loading="loading">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="岗位编码">
          {{ positionDetail.positionCode }}
        </el-descriptions-item>
        <el-descriptions-item label="岗位名称">
          {{ positionDetail.positionName }}
        </el-descriptions-item>
        <el-descriptions-item label="岗位类别">
          <el-tag :type="getCategoryType(positionDetail.positionCategory)">
            {{ getCategoryLabel(positionDetail.positionCategory) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="岗位等级">
          <span v-if="positionDetail.positionLevel">
            {{ getLevelLabel(positionDetail.positionLevel) }}
          </span>
          <span v-else class="text-muted">-</span>
        </el-descriptions-item>
        <el-descriptions-item label="岗位类型">
          {{ getTypeLabel(positionDetail.positionType) }}
        </el-descriptions-item>
        <el-descriptions-item label="所属机构">
          {{ positionDetail.institutionName }}
        </el-descriptions-item>
        <el-descriptions-item label="岗位状态">
          <el-tag :type="getStatusType(positionDetail.status)">
            {{ getStatusLabel(positionDetail.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ positionDetail.createTime }}
        </el-descriptions-item>
      </el-descriptions>

      <el-tabs v-model="activeTab" class="detail-tabs">
        <!-- 岗位职责 -->
        <el-tab-pane label="岗位职责" name="responsibility">
          <div class="tab-content">
            <h4>岗位职责</h4>
            <div class="content-text">
              {{ positionDetail.jobResponsibilities || '暂无数据' }}
            </div>

            <h4>工作内容</h4>
            <div class="content-text">
              {{ positionDetail.workContent || '暂无数据' }}
            </div>
          </div>
        </el-tab-pane>

        <!-- 任职要求 -->
        <el-tab-pane label="任职要求" name="qualification">
          <div class="tab-content">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="学历要求">
                <span v-if="positionDetail.educationRequirement">
                  {{ getEducationLabel(positionDetail.educationRequirement) }}
                </span>
                <span v-else class="text-muted">不限</span>
              </el-descriptions-item>
              <el-descriptions-item label="工作经验">
                <span v-if="positionDetail.experienceRequirement">
                  {{ positionDetail.experienceRequirement }} 年
                </span>
                <span v-else class="text-muted">不限</span>
              </el-descriptions-item>
              <el-descriptions-item label="专业要求" :span="2">
                {{ positionDetail.majorRequirements || '不限' }}
              </el-descriptions-item>
              <el-descriptions-item label="任职资格" :span="2">
                {{ positionDetail.qualifications || '暂无数据' }}
              </el-descriptions-item>
              <el-descriptions-item label="技能要求" :span="2">
                <el-tag
                  v-for="skill in positionDetail.skillRequirements"
                  :key="skill"
                  class="mr-8 mb-8"
                >
                  {{ skill }}
                </el-tag>
                <span v-if="!positionDetail.skillRequirements?.length" class="text-muted">
                  暂无数据
                </span>
              </el-descriptions-item>
              <el-descriptions-item label="证书要求" :span="2">
                <el-tag
                  v-for="cert in positionDetail.certificateRequirements"
                  :key="cert"
                  class="mr-8 mb-8"
                >
                  {{ cert }}
                </el-tag>
                <span v-if="!positionDetail.certificateRequirements?.length" class="text-muted">
                  暂无数据
                </span>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-tab-pane>

        <!-- 编制薪酬 -->
        <el-tab-pane label="编制薪酬" name="establishment">
          <div class="tab-content">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-statistic title="编制数" :value="positionDetail.establishmentCount || 0">
                  <template #suffix>人</template>
                </el-statistic>
              </el-col>
              <el-col :span="8">
                <el-statistic
                  title="实际人数"
                  :value="positionDetail.actualCount || 0"
                  :value-style="actualCountStyle"
                >
                  <template #suffix>人</template>
                </el-statistic>
              </el-col>
              <el-col :span="8">
                <el-statistic
                  title="空缺数"
                  :value="positionDetail.vacancyCount || 0"
                  :value-style="vacancyCountStyle"
                >
                  <template #suffix>人</template>
                </el-statistic>
              </el-col>
            </el-row>

            <el-divider   />

            <h4>薪酬范围</h4>
            <div class="salary-range">
              <span v-if="positionDetail.salaryMin || positionDetail.salaryMax">
                ¥{{ positionDetail.salaryMin || 0 }} - ¥{{ positionDetail.salaryMax || 0 }} / 月
              </span>
              <span v-else class="text-muted">暂未设置</span>
            </div>

            <h4>岗位待遇</h4>
            <div class="content-text">
              {{ positionDetail.benefits || '暂无数据' }}
            </div>
          </div>
        </el-tab-pane>

        <!-- 在岗人员 -->
        <el-tab-pane label="在岗人员" name="staffing">
          <div class="tab-content">
            <el-table :data="staffingList" v-loading="staffingLoading">
              <el-table-column prop="employeeCode" label="工号" width="100"  />
              <el-table-column prop="employeeName" label="姓名" width="100"  />
              <el-table-column prop="departmentName" label="部门" min-width="150"  />
              <el-table-column prop="startDate" label="任职日期" width="110"  />
              <el-table-column prop="isPrimary" label="岗位类型" width="100" align="center">
                <template #default="{ row }">
                  <el-tag :type="row.isPrimary ? 'primary' : ''">
                    {{ row.isPrimary ? '主岗' : '兼岗' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="workload" label="工作量" width="100" align="center">
                <template #default="{ row }">
                  <span v-if="row.workload">{{ row.workload }}%</span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" fixed="right">
                <template #default="{ row }">
                  <el-button link type="primary" @click="handleViewEmployee(row)">
                    查看
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <!-- 变更历史 -->
        <el-tab-pane label="变更历史" name="history">
          <div class="tab-content">
            <el-timeline>
              <el-timeline-item
                v-for="item in changeHistory"
                :key="item.changeId"
                :timestamp="item.changeTime"
                placement="top"
              >
                <div class="change-item">
                  <div class="change-type">
                    <el-tag :type="getChangeType(item.changeType)">
                      {{ getChangeLabel(item.changeType) }}
                    </el-tag>
                    <span class="operator">{{ item.operatorName }}</span>
                  </div>
                  <div class="change-reason">{{ item.changeReason }}</div>
                  <div v-if="item.changeDetails" class="change-details">
                    <div v-for="(value, key) in item.changeDetails" :key="key">
                      {{ getFieldLabel(key) }}: {{ value }}
                    </div>
                  </div>
                </div>
              </el-timeline-item>
            </el-timeline>
            <el-empty v-if="!changeHistory.length" description="暂无变更记录"  />
          </div>
        </el-tab-pane>

        <!-- 价值评估 -->
        <el-tab-pane label="价值评估" name="evaluation">
          <div class="tab-content">
            <div v-if="lastEvaluation">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="评估日期">
                  {{ lastEvaluation.valuationDate }}
                </el-descriptions-item>
                <el-descriptions-item label="总体评分">
                  <el-rate
                    v-model="lastEvaluation.totalScore"
                    disabled
                    show-score
                    :max="100"
                    score-template="{value} 分"
                   />
                </el-descriptions-item>
                <el-descriptions-item label="评估等级">
                  <el-tag :type="getGradeType(lastEvaluation.grade)">
                    {{ lastEvaluation.grade }}
                  </el-tag>
                </el-descriptions-item>
              </el-descriptions>

              <h4>评估维度</h4>
              <el-row :gutter="20">
                <el-col :span="12" v-for="(value, key) in lastEvaluation.dimensions" :key="key">
                  <div class="dimension-item">
                    <span class="dimension-label">{{ getDimensionLabel(key) }}</span>
                    <el-progress :percentage="value" :color="getProgressColor(value)"  />
                  </div>
                </el-col>
              </el-row>

              <h4>优化建议</h4>
              <ul class="suggestions">
                <li v-for="(suggestion, index) in lastEvaluation.suggestions" :key="index">
                  {{ suggestion }}
                </li>
              </ul>
            </div>
            <el-empty v-else description="暂未进行价值评估">
              <el-button type="primary" @click="handleEvaluate">立即评估</el-button>
            </el-empty>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <template #footer>
      <el-button @click="drawerVisible = false">关闭</el-button>
      <el-button type="primary" @click="handleEdit">编辑岗位</el-button>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">

defineOptions({
  name: 'PositionDetailDrawer'
})
 
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { usePositionStore } from '@/stores'
import type {
  Position,
  PositionStaffing,
  PositionChange,
  PositionValuation,
  PositionEmployee
} from '@/types/position'
import {
  positionCategoryOptions,
  positionLevelOptions,
  positionTypeOptions,
  positionStatusOptions,
  educationRequirementOptions
} from '@/types/position'
import positionApi from '@/api/modules/position'

// 组件属性
interface Props {
  modelValue: boolean
  positionId: string
}

const props = defineProps<Props>()

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'edit': []
  'evaluate': []
}>()

// 路由
const router = useRouter()

// 状态管理
const positionStore = usePositionStore()

// 响应式数据
const loading = ref(false)
const staffingLoading = ref(false)
const activeTab = ref('responsibility')
const positionDetail = ref<Position>({} as Position)
const staffingList = ref<PositionEmployee[]>([])
const changeHistory = ref<PositionChange[]>([])
const lastEvaluation = ref<PositionValuation | null>(null)

// 抽屉状态
const drawerVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 抽屉标题
const drawerTitle = computed(() => {
  return positionDetail.value.positionName || '岗位详情'
})

// 实际人数样式
const actualCountStyle = computed(() => {
  const actual = positionDetail.value.actualCount || 0
  const establishment = positionDetail.value.establishmentCount || 0
  
  if (actual > establishment) {
    return { color: '#f56c6c' } // 超编
  } else if (actual < establishment) {
    return { color: '#e6a23c' } // 缺编
  }
  return { color: '#67c23a' } // 满编
})

// 空缺数样式
const vacancyCountStyle = computed(() => {
  const vacancy = positionDetail.value.vacancyCount || 0
  return vacancy > 0 ? { color: '#f56c6c' } : { color: '#67c23a' }
})

// 获取标签方法
const getCategoryLabel = (category: string) => {
  const option = positionCategoryOptions.find(item => item.value === category)
  return option?.label || category
}

const getCategoryType = (category: string) => {
  const typeMap: Record<string, string> = {
    MANAGEMENT: 'primary',
    PROFESSIONAL: 'success',
    WORKER: 'warning',
    TEACHER: 'info',
    ADMINISTRATIVE: ''
  }
  return typeMap[category] || ''
}

const getLevelLabel = (level: string) => {
  const option = positionLevelOptions.find(item => item.value === level)
  return option?.label || level
}

const getTypeLabel = (type: string) => {
  const option = positionTypeOptions.find(item => item.value === type)
  return option?.label || type
}

const getStatusLabel = (status: string) => {
  const option = positionStatusOptions.find(item => item.value === status)
  return option?.label || status
}

const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    ACTIVE: 'success',
    FROZEN: 'warning',
    CANCELLED: 'info',
    MERGED: 'info'
  }
  return typeMap[status] || ''
}

const getEducationLabel = (education: string) => {
  const option = educationRequirementOptions.find(item => item.value === education)
  return option?.label || education
}

const getChangeType = (type: string) => {
  const typeMap: Record<string, string> = {
    CREATE: 'success',
    UPDATE: 'primary',
    DELETE: 'danger',
    MERGE: 'warning',
    FREEZE: 'warning',
    ACTIVE: 'success'
  }
  return typeMap[type] || ''
}

const getChangeLabel = (type: string) => {
  const labelMap: Record<string, string> = {
    CREATE: '创建',
    UPDATE: '更新',
    DELETE: '删除',
    MERGE: '合并',
    FREEZE: '冻结',
    ACTIVE: '激活'
  }
  return labelMap[type] || type
}

const getFieldLabel = (field: string) => {
  const labelMap: Record<string, string> = {
    positionName: '岗位名称',
    positionCategory: '岗位类别',
    positionLevel: '岗位等级',
    establishmentCount: '编制数',
    salaryMin: '薪酬下限',
    salaryMax: '薪酬上限'
  }
  return labelMap[field] || field
}

const getDimensionLabel = (dimension: string) => {
  const labelMap: Record<string, string> = {
    responsibility: '职责重要性',
    complexity: '工作复杂度',
    impact: '影响范围',
    knowledge: '知识要求',
    innovation: '创新要求'
  }
  return labelMap[dimension] || dimension
}

const getGradeType = (grade: string) => {
  const typeMap: Record<string, string> = {
    A: 'success',
    B: 'primary',
    C: 'warning',
    D: 'danger'
  }
  return typeMap[grade] || ''
}

const getProgressColor = (percentage: number) => {
  if (percentage >= 80) return '#67c23a'
  if (percentage >= 60) return '#409eff'
  if (percentage >= 40) return '#e6a23c'
  return '#f56c6c'
}

// 获取岗位详情
const fetchPositionDetail = async () => {
  if (!props.positionId) return

  loading.value = true
  try {
    positionDetail.value = await positionStore.fetchPositionDetail(props.positionId)
  } catch (__error) {
    console.error('获取岗位详情失败:', error)
    ElMessage.error('获取岗位详情失败')
  } finally {
    loading.value = false
  }
}

// 获取在岗人员
const fetchStaffing = async () => {
  if (!props.positionId) return

  staffingLoading.value = true
  try {
    const staffing = await positionStore.fetchPositionStaffing(props.positionId)
    staffingList.value = staffing.employees || []
  } catch (__error) {
    console.error('获取在岗人员失败:', error)
    staffingList.value = []
  } finally {
    staffingLoading.value = false
  }
}

// 获取变更历史
const fetchChangeHistory = async () => {
  if (!props.positionId) return

  try {
    changeHistory.value = await positionStore.fetchPositionChanges(props.positionId)
  } catch (__error) {
    console.error('获取变更历史失败:', error)
    changeHistory.value = []
  }
}

// 获取最新评估结果
const fetchLastEvaluation = async () => {
  if (!props.positionId) return

  try {
    // 获取最新评估结果
    const evaluation = await positionApi.getValueEvaluation(props.position.positionId)
    lastEvaluation.value = evaluation
  } catch (__error) {
    console.error('获取评估结果失败:', error)
    lastEvaluation.value = null
  }
}

// 查看员工
const handleViewEmployee = (employee: PositionEmployee) => {
  router.push(`/employee/detail/${employee.employeeId}`)
}

// 编辑岗位
const handleEdit = () => {
  emit('edit')
  drawerVisible.value = false
}

// 立即评估
const handleEvaluate = () => {
  emit('evaluate')
}

// 监听抽屉显示
watch(drawerVisible, (val) => {
  if (val && props.positionId) {
    fetchPositionDetail()
  }
})

// 监听标签页切换
watch(activeTab, (val) => {
  if (val === 'staffing' && !staffingList.value.length) {
    fetchStaffing()
  } else if (val === 'history' && !changeHistory.value.length) {
    fetchChangeHistory()
  } else if (val === 'evaluation' && !lastEvaluation.value) {
    fetchLastEvaluation()
  }
})
</script>

<style lang="scss" scoped>
.position-detail {
  padding: 20px;

  .detail-tabs {
    margin-top: 24px;
  }

  .tab-content {
    padding: 20px 0;

    h4 {
      margin: 20px 0 12px;
      font-size: 16px;
      font-weight: 500;

      &:first-child {
        margin-top: 0;
      }
    }

    .content-text {
      color: #606266;
      line-height: 1.8;
      white-space: pre-wrap;
    }

    .salary-range {
      font-size: 20px;
      font-weight: 500;
      color: #409eff;
    }

    .change-item {
      .change-type {
        margin-bottom: 8px;

        .operator {
          margin-left: 8px;
          color: #909399;
        }
      }

      .change-reason {
        margin-bottom: 8px;
        color: #606266;
      }

      .change-details {
        padding: 8px 12px;
        background: #f5f7fa;
        border-radius: 4px;
        font-size: 12px;
        color: #909399;
      }
    }

    .dimension-item {
      margin-bottom: 16px;

      .dimension-label {
        display: inline-block;
        margin-bottom: 8px;
        font-weight: 500;
      }
    }

    .suggestions {
      padding-left: 20px;
      color: #606266;
      line-height: 1.8;

      li {
        margin-bottom: 8px;
      }
    }
  }

  .text-muted {
    color: #909399;
  }

  .mr-8 {
    margin-right: 8px;
  }

  .mb-8 {
    margin-bottom: 8px;
  }
}
</style>