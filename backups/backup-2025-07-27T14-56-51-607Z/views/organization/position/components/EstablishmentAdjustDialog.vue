<template>
  <el-dialog
    v-model="dialogVisible"
    title="编制调整"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="establishment-adjust-form"
    >
      <!-- 岗位信息 -->
      <el-card shadow="never" class="mb-20">
        <template #header>
          <span class="card-title">岗位信息</span>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="岗位编码">
            {{ positionInfo?.positionCode || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="岗位名称">
            {{ positionInfo?.positionName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="当前编制">
            {{ positionInfo?.establishmentCount || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="实际人数">
            {{ positionInfo?.actualCount || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="空缺数">
            <el-tag v-if="positionInfo?.vacancyCount > 0" type="danger">
              {{ positionInfo?.vacancyCount || 0 }}
            </el-tag>
            <span v-else>0</span>
          </el-descriptions-item>
          <el-descriptions-item label="超编数">
            <el-tag v-if="overStaffCount > 0" type="warning">
              {{ overStaffCount }}
            </el-tag>
            <span v-else>0</span>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 调整信息 -->
      <el-form-item label="调整类型" prop="adjustType">
        <el-radio-group v-model="form.adjustType">
          <el-radio value="INCREASE">增加编制</el-radio>
          <el-radio value="DECREASE">减少编制</el-radio>
          <el-radio value="SET">设置编制</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item 
        v-if="form.adjustType === 'INCREASE' || form.adjustType === 'DECREASE'"
        label="调整数量" 
        prop="adjustCount"
      >
        <el-input-number
          v-model="form.adjustCount"
          :min="1"
          :max="form.adjustType === 'DECREASE' ? positionInfo?.establishmentCount : 999"
          :step="1"
          controls-position="right"
          placeholder="请输入调整数量"
          />
      </el-form-item>

      <el-form-item 
        v-if="form.adjustType === 'SET'"
        label="编制数量" 
        prop="newCount"
      >
        <el-input-number
          v-model="form.newCount"
          :min="0"
          :max="999"
          :step="1"
          controls-position="right"
          placeholder="请输入新的编制数量"
          />
      </el-form-item>

      <el-form-item label="调整后编制">
        <el-input
          v-model="adjustedCount"
          disabled
          placeholder="自动计算"
        >
          <template #append>
            <span v-if="adjustmentDiff !== 0" :class="adjustmentDiff > 0 ? 'text-success' : 'text-danger'">
              {{ adjustmentDiff > 0 ? '+' : '' }}{{ adjustmentDiff }}
            </span>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item label="生效日期" prop="effectiveDate">
        <el-date-picker
          v-model="form.effectiveDate"
          type="date"
          placeholder="选择生效日期"
          style="width: 100%"
          :disabled-date="disabledDate"
          value-format="YYYY-MM-DD"
         />
      </el-form-item>

      <el-form-item label="调整原因" prop="reason">
        <el-input
          v-model="form.reason"
          type="textarea"
          :rows="3"
          placeholder="请输入调整原因"
          maxlength="200"
          show-word-limit
          />
      </el-form-item>

      <el-form-item label="附件">
        <el-upload
          v-model:file-list="form.attachments"
          class="upload-demo"
          action="#"
          :auto-upload="false"
          :limit="5"
          accept=".pdf,.doc,.docx,.xls,.xlsx"
        >
          <el-button type="primary">选择文件</el-button>
          <template #tip>
            <div class="el-upload__tip">
              支持 pdf、doc、docx、xls、xlsx 格式，单个文件不超过10MB
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
 
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules, UploadUserFile } from 'element-plus'
import { usePositionStore } from '@/stores/modules/position'
import type { Position } from '@/types/position'

interface Props {
  modelValue: boolean
  positionId: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const positionStore = usePositionStore()
const formRef = ref<FormInstance>()
const loading = ref(false)

// 表单数据
const form = ref({
  adjustType: 'INCREASE' as 'INCREASE' | 'DECREASE' | 'SET',
  adjustCount: 1,
  newCount: 0,
  effectiveDate: '',
  reason: '',
  attachments: [] as UploadUserFile[]
})

// 岗位信息
const positionInfo = ref<Position | null>(null)

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 计算超编数
const overStaffCount = computed(() => {
  if (!positionInfo.value) return 0
  const over = (positionInfo.value.actualCount || 0) - (positionInfo.value.establishmentCount || 0)
  return over > 0 ? over : 0
})

// 计算调整后的编制数
const adjustedCount = computed(() => {
  if (!positionInfo.value) return 0
  const current = positionInfo.value.establishmentCount || 0
  
  switch (form.value.adjustType) {
    case 'INCREASE':
      return current + form.value.adjustCount
    case 'DECREASE':
      return Math.max(0, current - form.value.adjustCount)
    case 'SET':
      return form.value.newCount
    default:
      return current
  }
})

// 计算编制变化
const adjustmentDiff = computed(() => {
  if (!positionInfo.value) return 0
  return adjustedCount.value - (positionInfo.value.establishmentCount || 0)
})

// 表单验证规则
const rules: FormRules = {
  adjustType: [
    { required: true, message: '请选择调整类型', trigger: 'change' }
  ],
  adjustCount: [
    { required: true, message: '请输入调整数量', trigger: 'blur', type: 'number' }
  ],
  newCount: [
    { required: true, message: '请输入编制数量', trigger: 'blur', type: 'number' }
  ],
  effectiveDate: [
    { required: true, message: '请选择生效日期', trigger: 'change' }
  ],
  reason: [
    { required: true, message: '请输入调整原因', trigger: 'blur' },
    { min: 10, max: 200, message: '调整原因长度在 10 到 200 个字符', trigger: 'blur' }
  ]
}

// 禁用日期（不能选择过去的日期）
const disabledDate = (date: Date) => {
  return date < new Date(new Date().setHours(0, 0, 0, 0))
}

// 获取岗位信息
const fetchPositionInfo = async () => {
  if (!props.positionId) return
  
  try {
    positionInfo.value = await positionStore.fetchPositionDetail(props.positionId)
    
    // 设置默认值
    if (form.value.adjustType === 'SET') {
      form.value.newCount = positionInfo.value.establishmentCount || 0
    }
  } catch (__error) {
    console.error('获取岗位信息失败:', error)
  }
}

// 提交表单
const handleSubmit = async () => {
  const valid = await formRef.value?.validate()
  if (!valid) return
  
  // 确认提示
  try {
    await ElMessageBox.confirm(
      `确定要将编制从 ${positionInfo.value?.establishmentCount || 0} 调整为 ${adjustedCount.value} 吗？`,
      '确认调整',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
  } catch {
    return
  }
  
  loading.value = true
  try {
    // 构建请求数据
    const requestData = {
      positionId: props.positionId,
      adjustType: form.value.adjustType,
      adjustCount: form.value.adjustType === 'SET' ? undefined : form.value.adjustCount,
      newCount: form.value.adjustType === 'SET' ? form.value.newCount : undefined,
      effectiveDate: form.value.effectiveDate,
      reason: form.value.reason,
      attachments: form.value.attachments.map(file => ({
        name: file.name,
        url: file.url
      }))
    }
    
    // 更新岗位编制
    await positionStore.updatePosition(props.positionId, {
      establishmentCount: adjustedCount.value
    })
    
    ElMessage.success('编制调整成功')
    emit('success')
    dialogVisible.value = false
   
  } catch (error: unknown) {
    ElMessage.error(error.message || '编制调整失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  formRef.value?.resetFields()
  form.value = {
    adjustType: 'INCREASE',
    adjustCount: 1,
    newCount: 0,
    effectiveDate: '',
    reason: '',
    attachments: []
  }
  positionInfo.value = null
}

// 监听对话框打开
watch(dialogVisible, (val) => {
  if (val) {
    fetchPositionInfo()
  }
})
</script>

<style lang="scss" scoped>
.establishment-adjust-form {
  .card-title {
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
  
  .text-success {
    color: var(--el-color-success);
  }
  
  .text-danger {
    color: var(--el-color-danger);
  }
  
  .mb-20 {
    margin-bottom: 20px;
  }
}
</style>