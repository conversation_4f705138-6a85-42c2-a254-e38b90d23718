<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    append-to-body
    @closed="handleClosed"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="loading"
    >
      <el-tabs v-model="activeTab">
        <!-- 基本信息 -->
        <el-tab-pane label="基本信息" name="basic">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="岗位编码" prop="positionCode">
                <el-input
                  v-model="formData.positionCode"
                  placeholder="请输入岗位编码"
                  :disabled="mode === 'edit'"
                  />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="岗位名称" prop="positionName">
                <el-input
                  v-model="formData.positionName"
                  placeholder="请输入岗位名称"
                  />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="岗位类别" prop="positionCategory">
                <el-select
                  v-model="formData.positionCategory"
                  placeholder="请选择岗位类别"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in positionCategoryOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                   />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="岗位等级" prop="positionLevel">
                <el-select
                  v-model="formData.positionLevel"
                  placeholder="请选择岗位等级"
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in positionLevelOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                   />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="岗位类型" prop="positionType">
                <el-select
                  v-model="formData.positionType"
                  placeholder="请选择岗位类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in positionTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                   />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="所属机构" prop="institutionId">
                <HrOrgPicker
                  v-model="formData.institutionId"
                  placeholder="请选择所属机构"
                  @change="handleOrgChange"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="岗位序列" prop="sequenceId">
                <el-select
                  v-model="formData.sequenceId"
                  placeholder="请选择岗位序列"
                  clearable
                  style="width: 100%"
                  @change="handleSequenceChange"
                >
                  <el-option
                    v-for="item in sequenceOptions"
                    :key="item.sequenceId"
                    :label="item.sequenceName"
                    :value="item.sequenceId"
                   />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="岗位族群" prop="familyId">
                <el-select
                  v-model="formData.familyId"
                  placeholder="请先选择岗位序列"
                  clearable
                  style="width: 100%"
                  :disabled="!formData.sequenceId"
                >
                  <el-option
                    v-for="item in familyOptions"
                    :key="item.familyId"
                    :label="item.familyName"
                    :value="item.familyId"
                   />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="岗位标签" prop="tags">
                <el-select
                  v-model="formData.tags"
                  placeholder="请选择岗位标签"
                  multiple
                  filterable
                  allow-create
                  default-first-option
                  style="width: 100%"
                >
                  <el-option
                    v-for="tag in tagOptions"
                    :key="tag"
                    :label="tag"
                    :value="tag"
                   />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>

        <!-- 岗位职责 -->
        <el-tab-pane label="岗位职责" name="responsibility">
          <el-form-item label="岗位职责" prop="jobResponsibilities">
            <el-input
              v-model="formData.jobResponsibilities"
              type="textarea"
              :rows="8"
              placeholder="请输入岗位职责，建议分条描述"
              />
          </el-form-item>
          <el-form-item label="工作内容" prop="workContent">
            <el-input
              v-model="formData.workContent"
              type="textarea"
              :rows="6"
              placeholder="请输入主要工作内容"
              />
          </el-form-item>
        </el-tab-pane>

        <!-- 任职要求 -->
        <el-tab-pane label="任职要求" name="qualification">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="学历要求" prop="educationRequirement">
                <el-select
                  v-model="formData.educationRequirement"
                  placeholder="请选择学历要求"
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in educationRequirementOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                   />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="工作经验" prop="experienceRequirement">
                <el-input-number
                  v-model="formData.experienceRequirement"
                  :min="0"
                  :max="50"
                  placeholder="年"
                  style="width: 100%"
                  />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="专业要求" prop="majorRequirements">
            <el-input
              v-model="formData.majorRequirements"
              type="textarea"
              :rows="3"
              placeholder="请输入专业要求"
              />
          </el-form-item>

          <el-form-item label="任职资格" prop="qualifications">
            <el-input
              v-model="formData.qualifications"
              type="textarea"
              :rows="4"
              placeholder="请输入任职资格要求"
              />
          </el-form-item>

          <el-form-item label="技能要求" prop="skillRequirements">
            <el-select
              v-model="formData.skillRequirements"
              placeholder="请输入技能要求"
              multiple
              filterable
              allow-create
              default-first-option
              style="width: 100%"
            >
              <el-option
                v-for="skill in skillOptions"
                :key="skill"
                :label="skill"
                :value="skill"
               />
            </el-select>
          </el-form-item>

          <el-form-item label="证书要求" prop="certificateRequirements">
            <el-select
              v-model="formData.certificateRequirements"
              placeholder="请输入证书要求"
              multiple
              filterable
              allow-create
              default-first-option
              style="width: 100%"
            >
              <el-option
                v-for="cert in certificateOptions"
                :key="cert"
                :label="cert"
                :value="cert"
               />
            </el-select>
          </el-form-item>
        </el-tab-pane>

        <!-- 编制薪酬 -->
        <el-tab-pane label="编制薪酬" name="establishment">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="编制数" prop="establishmentCount">
                <el-input-number
                  v-model="formData.establishmentCount"
                  :min="0"
                  :max="999"
                  placeholder="人"
                  style="width: 100%"
                  />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="薪酬下限" prop="salaryMin">
                <el-input-number
                  v-model="formData.salaryMin"
                  :min="0"
                  :max="999999"
                  :precision="2"
                  placeholder="元/月"
                  style="width: 100%"
                  />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="薪酬上限" prop="salaryMax">
                <el-input-number
                  v-model="formData.salaryMax"
                  :min="0"
                  :max="999999"
                  :precision="2"
                  placeholder="元/月"
                  style="width: 100%"
                  />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="岗位待遇" prop="benefits">
            <el-input
              v-model="formData.benefits"
              type="textarea"
              :rows="4"
              placeholder="请输入岗位待遇和福利"
              />
          </el-form-item>
        </el-tab-pane>

        <!-- 其他信息 -->
        <el-tab-pane label="其他信息" name="other">
          <el-form-item label="排序号" prop="sortOrder">
            <el-input-number
              v-model="formData.sortOrder"
              :min="0"
              :max="9999"
              placeholder="数值越小越靠前"
              style="width: 200px"
              />
          </el-form-item>

          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="formData.remark"
              type="textarea"
              :rows="4"
              placeholder="请输入备注信息"
              />
          </el-form-item>
        </el-tab-pane>
      </el-tabs>
    </el-form>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'PositionEditDialog'
})
 
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { usePositionStore } from '@/stores'
import type {
  Position,
  PositionSequence,
  PositionFamily,
  PositionType,
  PositionCategory,
  EducationRequirement
} from '@/types/position'
import {
  positionCategoryOptions,
  positionLevelOptions,
  positionTypeOptions,
  educationRequirementOptions
} from '@/types/position'
import HrOrgPicker from '@/components/organization/HrOrgPicker.vue'
import positionApi from '@/api/modules/position'

// 组件属性
interface Props {
  modelValue: boolean
  positionId?: string
  mode?: 'create' | 'edit'
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  positionId: '',
  mode: 'create'
})

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'success': []
}>()

// 状态管理
const positionStore = usePositionStore()

// refs
const formRef = ref<FormInstance>()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const activeTab = ref('basic')
const sequenceOptions = ref<PositionSequence[]>([])
const familyOptions = ref<PositionFamily[]>([])

// 预设选项
const tagOptions = ref([
  '核心岗位',
  '管理岗位',
  '技术岗位',
  '教学岗位',
  '科研岗位',
  '行政岗位',
  '后勤岗位',
  '临时岗位'
])

const skillOptions = ref([
  'Office办公软件',
  '项目管理',
  '数据分析',
  '公文写作',
  '英语口语',
  '教学能力',
  '科研能力',
  '团队管理',
  '沟通协调'
])

const certificateOptions = ref([
  '教师资格证',
  '职业资格证',
  '专业技术职称',
  '英语等级证书',
  '计算机等级证书',
  '驾驶证'
])

// 对话框状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 对话框标题
const dialogTitle = computed(() => {
  return props.mode === 'create' ? '新增岗位' : '编辑岗位'
})

// 表单数据
const formData = reactive<Partial<Position>>({
  positionCode: '',
  positionName: '',
  positionCategory: undefined,
  positionLevel: undefined,
  positionType: 'FULL_TIME' as PositionType,
  institutionId: '',
  institutionName: '',
  sequenceId: '',
  familyId: '',
  jobResponsibilities: '',
  workContent: '',
  qualifications: '',
  majorRequirements: '',
  educationRequirement: undefined,
  experienceRequirement: undefined,
  skillRequirements: [],
  certificateRequirements: [],
  establishmentCount: undefined,
  salaryMin: undefined,
  salaryMax: undefined,
  benefits: '',
  sortOrder: 0,
  remark: '',
  tags: []
})

// 表单验证规则
const formRules: FormRules = {
  positionCode: [
    { required: true, message: '请输入岗位编码', trigger: 'blur' },
    { pattern: /^[A-Z0-9_-]+$/, message: '只能包含大写字母、数字、下划线和横线', trigger: 'blur' }
  ],
  positionName: [
    { required: true, message: '请输入岗位名称', trigger: 'blur' }
  ],
  positionCategory: [
    { required: true, message: '请选择岗位类别', trigger: 'change' }
  ],
  positionType: [
    { required: true, message: '请选择岗位类型', trigger: 'change' }
  ],
  institutionId: [
    { required: true, message: '请选择所属机构', trigger: 'change' }
  ],
  salaryMax: [
    {
      validator: (rule, value, callback) => {
        if (value && formData.salaryMin && value < formData.salaryMin) {
          callback(new Error('薪酬上限不能小于薪酬下限'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 获取岗位序列
const fetchSequences = async () => {
  try {
    sequenceOptions.value = await positionStore.fetchSequences()
  } catch (__error) {
    console.error('获取岗位序列失败:', error)
  }
}

// 获取岗位族群
const fetchFamilies = async (sequenceId: string) => {
  try {
    familyOptions.value = await positionStore.fetchFamilies(sequenceId)
  } catch (__error) {
    console.error('获取岗位族群失败:', error)
  }
}

// 获取岗位详情
const fetchPositionDetail = async () => {
  if (!props.positionId) return

  loading.value = true
  try {
    const position = await positionStore.fetchPositionDetail(props.positionId)
    
    // 填充表单数据
    Object.assign(formData, {
      ...position,
      skillRequirements: position.skillRequirements || [],
      certificateRequirements: position.certificateRequirements || [],
      tags: position.tags || []
    })

    // 加载族群选项
    if (position.sequenceId) {
      await fetchFamilies(position.sequenceId)
    }
  } catch (__error) {
    console.error('获取岗位详情失败:', error)
    ElMessage.error('获取岗位详情失败')
  } finally {
    loading.value = false
  }
}

// 处理机构变更
   
const handleOrgChange = (orgId: string, orgData: unknown) => {
  formData.institutionName = orgData?.name || ''
}

// 处理序列变更
const handleSequenceChange = (sequenceId: string) => {
  formData.familyId = ''
  if (sequenceId) {
    fetchFamilies(sequenceId)
  } else {
    familyOptions.value = []
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (!valid) return

    submitting.value = true
    try {
      if (props.mode === 'create') {
        await positionStore.createPosition(formData)
        ElMessage.success('岗位创建成功')
      } else {
        await positionStore.updatePosition(props.positionId, formData)
        ElMessage.success('岗位更新成功')
      }

      dialogVisible.value = false
      emit('success')
    } catch (__error) {
      console.error('保存岗位失败:', error)
    } finally {
      submitting.value = false
    }
  })
}

// 取消
const handleCancel = () => {
  dialogVisible.value = false
}

// 对话框关闭
const handleClosed = () => {
  formRef.value?.resetFields()
  activeTab.value = 'basic'
  
  // 重置表单数据
  Object.assign(formData, {
    positionCode: '',
    positionName: '',
    positionCategory: undefined,
    positionLevel: undefined,
    positionType: 'FULL_TIME' as PositionType,
    institutionId: '',
    institutionName: '',
    sequenceId: '',
    familyId: '',
    jobResponsibilities: '',
    workContent: '',
    qualifications: '',
    majorRequirements: '',
    educationRequirement: undefined,
    experienceRequirement: undefined,
    skillRequirements: [],
    certificateRequirements: [],
    establishmentCount: undefined,
    salaryMin: undefined,
    salaryMax: undefined,
    benefits: '',
    sortOrder: 0,
    remark: '',
    tags: []
  })
  
  familyOptions.value = []
}

// 监听对话框显示
watch(dialogVisible, (val) => {
  if (val) {
    fetchSequences()
    if (props.mode === 'edit') {
      fetchPositionDetail()
    }
  }
})
</script>

<style lang="scss" scoped>
.el-tabs {
  margin: -10px 0;
}

:deep(.el-tabs__content) {
  padding: 20px 0;
}
</style>