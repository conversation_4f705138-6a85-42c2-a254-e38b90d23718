<template>
  <el-dialog
    v-model="dialogVisible"
    title="岗位人员配置"
    width="1200px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="staffing-container">
      <!-- 岗位信息卡片 -->
      <el-card shadow="never" class="info-card">
        <template #header>
          <div class="card-header">
            <span class="card-title">岗位信息</span>
            <el-tag :type="getStaffingStatusType()">
              {{ getStaffingStatusText() }}
            </el-tag>
          </div>
        </template>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="岗位编码">
            {{ positionInfo?.positionCode || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="岗位名称">
            {{ positionInfo?.positionName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="所属机构">
            {{ positionInfo?.institutionName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="编制数">
            <span class="number-text">{{ staffingInfo?.establishmentCount || 0 }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="实际人数">
            <span class="number-text">{{ staffingInfo?.actualCount || 0 }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="空缺/超编">
            <el-tag v-if="staffingInfo?.vacancyCount > 0" type="danger" size="small">
              缺 {{ staffingInfo.vacancyCount }}
            </el-tag>
            <el-tag v-else-if="staffingInfo?.overStaffCount > 0" type="warning" size="small">
              超 {{ staffingInfo.overStaffCount }}
            </el-tag>
            <el-tag v-else type="success" size="small">满编</el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 在岗人员列表 -->
      <el-card shadow="never" class="staff-card">
        <template #header>
          <div class="card-header">
            <span class="card-title">在岗人员</span>
            <div class="header-actions">
              <el-button 
                v-if="staffingInfo?.vacancyCount > 0"
                type="primary" 
                size="small"
                @click="handleAddEmployee"
              >
                <el-icon><Plus /></el-icon>
                分配人员
              </el-button>
            </div>
          </div>
        </template>
        
        <el-table
          :data="staffingInfo?.employees || []"
          style="width: 100%"
          max-height="400"
        >
          <el-table-column type="index" label="序号" width="60"  />
          <el-table-column prop="employeeCode" label="工号" width="100"  />
          <el-table-column prop="employeeName" label="姓名" width="100"  />
          <el-table-column prop="isPrimary" label="岗位类型" width="100">
            <template #default="{ row }">
              <el-tag :type="row.isPrimary ? 'primary' : 'info'">
                {{ row.isPrimary ? '主岗' : '兼岗' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="startDate" label="任职日期" width="120"  />
          <el-table-column prop="workload" label="工作量占比" width="120">
            <template #default="{ row }">
              <el-progress
                :percentage="row.workload || 100"
                :stroke-width="6"
                :text-inside="true"
                style="width: 80px"
               />
            </template>
          </el-table-column>
          <el-table-column label="任职时长" width="120">
            <template #default="{ row }">
              {{ calculateTenure(row.startDate) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" width="180">
            <template #default="{ row }">
              <el-button link type="primary" @click="handleViewEmployee(row)">
                查看
              </el-button>
              <el-button link type="primary" @click="handleAdjustWorkload(row)">
                调整工作量
              </el-button>
              <el-button link type="danger" @click="handleRemoveEmployee(row)">
                移除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 匹配度分析 -->
      <el-card shadow="never" class="analysis-card" v-if="selectedEmployee">
        <template #header>
          <div class="card-header">
            <span class="card-title">岗位匹配度分析</span>
            <el-button text @click="selectedEmployee = null">
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
        </template>
        
        <div class="match-analysis">
          <div class="match-score">
            <el-progress
              type="circle"
              :percentage="matchResult?.overallScore || 0"
              :width="120"
              :stroke-width="8"
              :color="getScoreColor"
            >
              <template #default="{ percentage }">
                <span class="score-text">{{ percentage }}%</span>
                <span class="score-label">匹配度</span>
              </template>
            </el-progress>
          </div>
          
          <div class="match-details">
            <h4>评估详情</h4>
            <div 
              v-for="detail in matchResult?.details || []"
              :key="detail.criterion"
              class="detail-item"
            >
              <div class="detail-header">
                <span class="criterion">{{ detail.criterion }}</span>
                <span class="score">{{ detail.score }}/100</span>
              </div>
              <el-progress
                :percentage="detail.score"
                :stroke-width="6"
                :show-text="false"
                :color="getDetailColor(detail.score)"
               />
              <div v-if="detail.gap" class="gap-text">
                {{ detail.gap }}
              </div>
            </div>
          </div>
          
          <div class="match-suggestions" v-if="matchResult?.suggestions?.length">
            <h4>改进建议</h4>
            <ul>
              <li v-for="(suggestion, index) in matchResult.suggestions" :key="index">
                {{ suggestion }}
              </li>
            </ul>
          </div>
        </div>
      </el-card>
    </div>

    <template #footer>
      <el-button @click="dialogVisible = false">关闭</el-button>
    </template>
  </el-dialog>

  <!-- 分配人员对话框 -->
  <el-dialog
    v-model="addEmployeeVisible"
    title="分配人员"
    width="800px"
    append-to-body
  >
    <el-form :model="assignForm" label-width="100px">
      <el-form-item label="选择员工" required>
        <el-select
          v-model="assignForm.employeeId"
          placeholder="请选择员工"
          filterable
          remote
          :remote-method="searchEmployees"
          style="width: 100%"
        >
          <el-option
            v-for="emp in employeeOptions"
            :key="emp.employeeId"
            :label="`${emp.employeeName} (${emp.employeeCode})`"
            :value="emp.employeeId"
           />
        </el-select>
      </el-form-item>
      
      <el-form-item label="岗位类型" required>
        <el-radio-group v-model="assignForm.isPrimary">
          <el-radio :value="true">主岗</el-radio>
          <el-radio :value="false">兼岗</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="工作量占比" v-if="!assignForm.isPrimary">
        <el-slider
          v-model="assignForm.workload"
          :min="10"
          :max="90"
          :step="10"
          show-input
         />
      </el-form-item>
      
      <el-form-item label="任职日期" required>
        <el-date-picker
          v-model="assignForm.startDate"
          type="date"
          placeholder="选择任职日期"
          value-format="YYYY-MM-DD"
          style="width: 100%"
         />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="addEmployeeVisible = false">取消</el-button>
      <el-button type="primary" @click="handleConfirmAssign">确定</el-button>
    </template>
  </el-dialog>

  <!-- 调整工作量对话框 -->
  <el-dialog
    v-model="adjustWorkloadVisible"
    title="调整工作量"
    width="500px"
    append-to-body
  >
    <el-form :model="workloadForm" label-width="100px">
      <el-form-item label="员工姓名">
        <el-input :value="currentEmployee?.employeeName" disabled   />
      </el-form-item>
      
      <el-form-item label="当前工作量">
        <el-input :value="`${currentEmployee?.workload || 100}%`" disabled   />
      </el-form-item>
      
      <el-form-item label="新工作量">
        <el-slider
          v-model="workloadForm.workload"
          :min="10"
          :max="100"
          :step="10"
          show-input
         />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="adjustWorkloadVisible = false">取消</el-button>
      <el-button type="primary" @click="handleConfirmAdjust">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'StaffingDialog'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Close } from '@element-plus/icons-vue'
import { usePositionStore } from '@/stores/modules/position'
import { useEmployeeStore } from '@/stores/modules/employee'
import { positionApi } from '@/api/position'
import type { 
  Position, 
  PositionStaffing, 
  PositionEmployee,
  PositionMatchResult 
} from '@/types/position'
import type { Employee } from '@/types/employee'

interface Props {
  modelValue: boolean
  positionId: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const positionStore = usePositionStore()
const employeeStore = useEmployeeStore()

// 状态
const loading = ref(false)
const positionInfo = ref<Position | null>(null)
const staffingInfo = ref<PositionStaffing | null>(null)
const selectedEmployee = ref<PositionEmployee | null>(null)
const matchResult = ref<PositionMatchResult | null>(null)
const employeeOptions = ref<Employee[]>([])

// 对话框状态
const addEmployeeVisible = ref(false)
const adjustWorkloadVisible = ref(false)
const currentEmployee = ref<PositionEmployee | null>(null)

// 表单数据
const assignForm = ref({
  employeeId: '',
  isPrimary: true,
  workload: 100,
  startDate: ''
})

const workloadForm = ref({
  workload: 100
})

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 获取进度条颜色
const getScoreColor = (percentage: number) => {
  if (percentage >= 90) return '#67C23A'
  if (percentage >= 70) return '#409EFF'
  if (percentage >= 50) return '#E6A23C'
  return '#F56C6C'
}

// 获取详情颜色
const getDetailColor = (score: number) => {
  if (score >= 80) return '#67C23A'
  if (score >= 60) return '#409EFF'
  if (score >= 40) return '#E6A23C'
  return '#F56C6C'
}

// 获取人员配置状态类型
const getStaffingStatusType = () => {
  if (!staffingInfo.value) return 'info'
  if (staffingInfo.value.vacancyCount > 0) return 'danger'
  if (staffingInfo.value.overStaffCount > 0) return 'warning'
  return 'success'
}

// 获取人员配置状态文本
const getStaffingStatusText = () => {
  if (!staffingInfo.value) return '未知'
  if (staffingInfo.value.vacancyCount > 0) return '人员不足'
  if (staffingInfo.value.overStaffCount > 0) return '人员超编'
  return '满编'
}

// 计算任职时长
const calculateTenure = (startDate: string) => {
  if (!startDate) return '-'
  
  const start = new Date(startDate)
  const now = new Date()
  const years = now.getFullYear() - start.getFullYear()
  const months = now.getMonth() - start.getMonth()
  
  if (years === 0) {
    return months > 0 ? `${months}个月` : '不足1个月'
  }
  
  return months >= 0 ? `${years}年${months}个月` : `${years - 1}年${12 + months}个月`
}

// 获取岗位信息
const fetchPositionInfo = async () => {
  if (!props.positionId) return
  
  loading.value = true
  try {
    // 获取岗位基本信息
    positionInfo.value = await positionStore.fetchPositionDetail(props.positionId)
    
    // 获取人员配置信息
    staffingInfo.value = await positionStore.fetchPositionStaffing(props.positionId)
  } catch (__error) {
    console.error('获取岗位信息失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索员工
const searchEmployees = async (query: string) => {
  if (query.length < 2) return
  
  try {
    const {list: _list} =  await employeeStore.fetchEmployees({
      keyword: query,
      size: 20
    })
    employeeOptions.value 
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  .card-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
  
  .header-actions {
    display: flex;
    gap: 10px;
  }
}

.info-card {
  .number-text {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-color-primary);
  }
}

.match-analysis {
  display: flex;
  flex-direction: column;
  gap: 20px;
  
  .match-score {
    display: flex;
    justify-content: center;
    padding: 20px;
    
    :deep(.el-progress__text) {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      
      .score-text {
        font-size: 24px;
        font-weight: 600;
      }
      
      .score-label {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }
  }
  
  .match-details {
    h4 {
      margin: 0 0 16px;
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
    
    .detail-item {
      margin-bottom: 16px;
      
      .detail-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        
        .criterion {
          font-size: 14px;
          color: var(--el-text-color-primary);
        }
        
        .score {
          font-size: 14px;
          font-weight: 600;
          color: var(--el-color-primary);
        }
      }
      
      .gap-text {
        margin-top: 4px;
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }
  }
  
  .match-suggestions {
    h4 {
      margin: 0 0 12px;
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
    
    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        font-size: 13px;
        color: var(--el-text-color-regular);
      }
    }
  }
}
</style>