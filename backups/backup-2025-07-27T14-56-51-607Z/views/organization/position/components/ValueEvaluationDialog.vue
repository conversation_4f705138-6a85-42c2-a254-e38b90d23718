<template>
  <el-dialog
    v-model="dialogVisible"
    title="岗位价值评估"
    width="900px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="evaluation-container" v-loading="loading">
      <!-- 岗位信息 -->
      <el-card shadow="never" class="info-card">
        <el-descriptions :column="3" border>
          <el-descriptions-item label="岗位编码">
            {{ positionInfo?.positionCode || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="岗位名称">
            {{ positionInfo?.positionName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="岗位类别">
            {{ getCategoryLabel(positionInfo?.positionCategory) }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 评估维度 -->
      <el-card shadow="never" class="dimensions-card">
        <template #header>
          <span class="card-title">评估维度</span>
        </template>
        
        <el-form :model="evaluationForm" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="职责重要性">
                <div class="dimension-item">
                  <el-slider
                    v-model="evaluationForm.responsibility"
                    :min="0"
                    :max="100"
                    :step="5"
                    show-input
                   />
                  <div class="dimension-desc">
                    评估岗位职责对组织目标的贡献程度
                  </div>
                </div>
              </el-form-item>
            </el-col>
            
            <el-col :span="12">
              <el-form-item label="工作复杂度">
                <div class="dimension-item">
                  <el-slider
                    v-model="evaluationForm.complexity"
                    :min="0"
                    :max="100"
                    :step="5"
                    show-input
                   />
                  <div class="dimension-desc">
                    评估工作内容的技术难度和复杂程度
                  </div>
                </div>
              </el-form-item>
            </el-col>
            
            <el-col :span="12">
              <el-form-item label="影响范围">
                <div class="dimension-item">
                  <el-slider
                    v-model="evaluationForm.impact"
                    :min="0"
                    :max="100"
                    :step="5"
                    show-input
                   />
                  <div class="dimension-desc">
                    评估岗位决策对组织的影响范围和深度
                  </div>
                </div>
              </el-form-item>
            </el-col>
            
            <el-col :span="12">
              <el-form-item label="知识要求">
                <div class="dimension-item">
                  <el-slider
                    v-model="evaluationForm.knowledge"
                    :min="0"
                    :max="100"
                    :step="5"
                    show-input
                   />
                  <div class="dimension-desc">
                    评估岗位所需的专业知识和技能水平
                  </div>
                </div>
              </el-form-item>
            </el-col>
            
            <el-col :span="12">
              <el-form-item label="创新要求">
                <div class="dimension-item">
                  <el-slider
                    v-model="evaluationForm.innovation"
                    :min="0"
                    :max="100"
                    :step="5"
                    show-input
                   />
                  <div class="dimension-desc">
                    评估岗位对创新和变革的需求程度
                  </div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>

      <!-- 评估结果 -->
      <el-card shadow="never" class="result-card" v-if="evaluationResult">
        <template #header>
          <div class="card-header">
            <span class="card-title">评估结果</span>
            <el-button text @click="handleRecalculate">
              <el-icon><Refresh /></el-icon>
              重新计算
            </el-button>
          </div>
        </template>
        
        <div class="result-content">
          <div class="score-section">
            <div class="total-score">
              <el-progress
                type="circle"
                :percentage="evaluationResult.totalScore"
                :width="140"
                :stroke-width="10"
                :color="getScoreColor"
              >
                <template #default="{ percentage }">
                  <span class="score-text">{{ percentage }}</span>
                  <span class="score-label">总分</span>
                </template>
              </el-progress>
            </div>
            
            <div class="grade-info">
              <div class="grade-label">评估等级</div>
              <div class="grade-value" :class="`grade-${evaluationResult.grade}`">
                {{ getGradeLabel(evaluationResult.grade) }}
              </div>
              <div class="grade-desc">{{ getGradeDescription(evaluationResult.grade) }}</div>
            </div>
          </div>
          
          <!-- 维度雷达图 -->
          <div class="radar-section">
            <div ref="radarChart" class="radar-chart"></div>
          </div>
          
          <!-- 建议 -->
          <div class="suggestions-section" v-if="evaluationResult.suggestions?.length">
            <h4>优化建议</h4>
            <el-alert
              v-for="(suggestion, index) in evaluationResult.suggestions"
              :key="index"
              :title="suggestion"
              type="info"
              :closable="false"
              class="suggestion-item"
             />
          </div>
        </div>
      </el-card>

      <!-- 历史评估记录 -->
      <el-card shadow="never" class="history-card">
        <template #header>
          <span class="card-title">历史评估记录</span>
        </template>
        
        <el-table :data="evaluationHistory" empty-text="暂无评估记录">
          <el-table-column prop="valuationDate" label="评估时间" width="180"  />
          <el-table-column prop="totalScore" label="总分" width="100">
            <template #default="{ row }">
              <el-tag>{{ row.totalScore }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="grade" label="等级" width="100">
            <template #default="{ row }">
              <el-tag :type="getGradeType(row.grade)">
                {{ getGradeLabel(row.grade) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="各维度评分">
            <template #default="{ row }">
              <div class="dimension-scores">
                <span>职责: {{ row.dimensions.responsibility }}</span>
                <span>复杂度: {{ row.dimensions.complexity }}</span>
                <span>影响: {{ row.dimensions.impact }}</span>
                <span>知识: {{ row.dimensions.knowledge }}</span>
                <span>创新: {{ row.dimensions.innovation }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="{ row }">
              <el-button link type="primary" @click="handleViewHistory(row)">
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleSave" :loading="saving">
        保存评估
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
 
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { usePositionStore } from '@/stores/modules/position'
import { positionApi } from '@/api/position'
import type { Position, PositionValuation } from '@/types/position'
import { positionCategoryOptions } from '@/types/position'

interface Props {
  modelValue: boolean
  positionId: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const positionStore = usePositionStore()
const radarChart = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

// 状态
const loading = ref(false)
const saving = ref(false)
const positionInfo = ref<Position | null>(null)
const evaluationResult = ref<PositionValuation | null>(null)
const evaluationHistory = ref<PositionValuation[]>([])

// 评估表单
const evaluationForm = ref({
  responsibility: 50,
  complexity: 50,
  impact: 50,
  knowledge: 50,
  innovation: 50
})

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 获取评分颜色
const getScoreColor = (percentage: number) => {
  if (percentage >= 90) return '#67C23A'
  if (percentage >= 70) return '#409EFF'
  if (percentage >= 50) return '#E6A23C'
  return '#F56C6C'
}

// 获取类别标签
const getCategoryLabel = (category?: string) => {
  if (!category) return '-'
  const option = positionCategoryOptions.find(opt => opt.value === category)
  return option?.label || category
}

// 获取等级标签
const getGradeLabel = (grade: string) => {
  const gradeMap: Record<string, string> = {
    'A': '核心岗位',
    'B': '重要岗位',
    'C': '一般岗位',
    'D': '基础岗位'
  }
  return gradeMap[grade] || grade
}

// 获取等级描述
const getGradeDescription = (grade: string) => {
  const descMap: Record<string, string> = {
    'A': '对组织战略目标具有关键影响，需要高度专业能力和决策权限',
    'B': '对部门目标达成有重要作用，需要较强的专业能力和一定决策权',
    'C': '承担常规性工作，需要基本的专业知识和操作技能',
    'D': '执行简单重复性工作，培训后即可胜任'
  }
  return descMap[grade] || ''
}

// 获取等级类型
const getGradeType = (grade: string) => {
  const typeMap: Record<string, string> = {
    'A': 'danger',
    'B': 'warning',
    'C': 'primary',
    'D': 'info'
  }
  return typeMap[grade] || 'info'
}

// 初始化雷达图
const initRadarChart = () => {
  if (!radarChart.value) return
  
  chartInstance = echarts.init(radarChart.value)
  
  const option = {
    radar: {
      indicator: [
        { name: 'HrHr职责重要性', max: 100 },
        { name: '工作复杂度', max: 100 },
        { name: '影响范围', max: 100 },
        { name: '知识要求', max: 100 },
        { name: '创新要求', max: 100 }
      ],
      radius: '65%',
      splitNumber: 4
    },
    series: [{
      type: 'radar',
      data: [{
        value: [
          evaluationForm.value.responsibility,
          evaluationForm.value.complexity,
          evaluationForm.value.impact,
          evaluationForm.value.knowledge,
          evaluationForm.value.innovation
        ],
        name: '评估值',
        areaStyle: {
          opacity: 0.3
        },
        emphasis: {
          areaStyle: {
            opacity: 0.5
          }
        }
      }]
    }]
  }
  
  chartInstance.setOption(option)
}

// 更新雷达图
const updateRadarChart = () => {
  if (!chartInstance) return
  
  chartInstance.setOption({
    series: [{
      data: [{
        value: [
          evaluationForm.value.responsibility,
          evaluationForm.value.complexity,
          evaluationForm.value.impact,
          evaluationForm.value.knowledge,
          evaluationForm.value.innovation
        ]
      }]
    }]
  })
}

// 计算总分和等级
const calculateResult = () => {
  const weights = {
    responsibility: 0.25,
    complexity: 0.20,
    impact: 0.20,
    knowledge: 0.20,
    innovation: 0.15
  }
  
  const totalScore = Math.round(
    evaluationForm.value.responsibility * weights.responsibility +
    evaluationForm.value.complexity * weights.complexity +
    evaluationForm.value.impact * weights.impact +
    evaluationForm.value.knowledge * weights.knowledge +
    evaluationForm.value.innovation * weights.innovation
  )
  
  let grade = 'D'
  if (totalScore >= 85) grade = 'A'
  else if (totalScore >= 70) grade = 'B'
  else if (totalScore >= 50) grade = 'C'
  
  // 生成建议
  const suggestions: string[] = []
  
  if (evaluationForm.value.responsibility < 60 && grade !== 'D') {
    suggestions.push('建议明确和强化岗位职责，提升其对组织目标的贡献度')
  }
  
  if (evaluationForm.value.knowledge < 50) {
    suggestions.push('可以考虑降低知识要求门槛，或提供更多培训支持')
  }
  
  if (evaluationForm.value.innovation > 80 && evaluationForm.value.complexity < 60) {
    suggestions.push('创新要求较高但工作复杂度不足，建议增加挑战性任务')
  }
  
  evaluationResult.value = {
    positionId: props.positionId,
    positionName: positionInfo.value?.positionName || '',
    valuationDate: new Date().toISOString(),
    dimensions: { ...evaluationForm.value },
    totalScore,
    grade,
    suggestions
  }
  
  updateRadarChart()
}

// 获取岗位信息
const fetchPositionInfo = async () => {
  if (!props.positionId) return
  
  loading.value = true
  try {
    positionInfo.value = await positionStore.fetchPositionDetail(props.positionId)
    
    // 获取最新评估结果
    try {
      const valuation = await positionStore.evaluateValue(props.positionId)
      if (valuation) {
        evaluationForm.value = { ...valuation.dimensions }
        evaluationResult.value = valuation
        await nextTick()
        initRadarChart()
      }
    } catch {
      // 如果没有评估记录，使用默认值
      calculateResult()
      await nextTick()
      initRadarChart()
    }
  } catch (__error) {
    console.error('获取岗位信息失败:', error)
  } finally {
    loading.value = false
  }
}

// 重新计算
const handleRecalculate = () => {
  calculateResult()
}

// 保存评估
const handleSave = async () => {
  if (!evaluationResult.value) {
    calculateResult()
  }
  
  saving.value = true
  try {
    // 实现保存评估结果的API
    const evaluationData = {
      strategicScore: evaluationForm.value.strategic.score,
      strategicWeight: evaluationForm.value.strategic.weight,
      strategicComment: evaluationForm.value.strategic.comment,
      managementScore: evaluationForm.value.management.score,
      managementWeight: evaluationForm.value.management.weight,
      managementComment: evaluationForm.value.management.comment,
      professionalScore: evaluationForm.value.professional.score,
      professionalWeight: evaluationForm.value.professional.weight,
      professionalComment: evaluationForm.value.professional.comment,
      complexityScore: evaluationForm.value.complexity.score,
      complexityWeight: evaluationForm.value.complexity.weight,
      complexityComment: evaluationForm.value.complexity.comment,
      totalScore: totalScore.value,
      grade: positionGrade.value,
      evaluationDate: new Date().toISOString(),
      evaluator: '当前用户' // 需要从用户信息中获取
    }
    
    await positionApi.saveValueEvaluation(props.position.positionId, evaluationData)
    
    ElMessage.success('评估结果已保存')
    emit('success')
    dialogVisible.value = false
   
  } catch (error: unknown) {
    ElMessage.error(error.message || '保存失败')
  } finally {
    saving.value = false
  }
}

// 查看历史记录
const handleViewHistory = (record: PositionValuation) => {
  evaluationForm.value = { ...record.dimensions }
  evaluationResult.value = record
  updateRadarChart()
}

// 关闭对话框
const handleClose = () => {
  evaluationForm.value = {
    responsibility: 50,
    complexity: 50,
    impact: 50,
    knowledge: 50,
    innovation: 50
  }
  evaluationResult.value = null
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
}

// 监听表单变化
watch(evaluationForm, () => {
  calculateResult()
}, { deep: true })

// 监听对话框打开
watch(dialogVisible, (val) => {
  if (val) {
    fetchPositionInfo()
  }
})

// 组件挂载
onMounted(() => {
  window.addEventListener('resize', () => {
    chartInstance?.resize()
  })
})
</script>

<style lang="scss" scoped>
.evaluation-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dimensions-card {
  .dimension-item {
    width: 100%;
    
    .dimension-desc {
      margin-top: 8px;
      font-size: 12px;
      color: var(--el-text-color-secondary);
      line-height: 1.4;
    }
  }
}

.result-content {
  .score-section {
    display: flex;
    align-items: center;
    gap: 40px;
    padding: 20px 0;
    border-bottom: 1px solid var(--el-border-color-lighter);
    
    .total-score {
      :deep(.el-progress__text) {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;
        
        .score-text {
          font-size: 32px;
          font-weight: 600;
        }
        
        .score-label {
          font-size: 14px;
          color: var(--el-text-color-secondary);
        }
      }
    }
    
    .grade-info {
      flex: 1;
      
      .grade-label {
        font-size: 14px;
        color: var(--el-text-color-secondary);
        margin-bottom: 8px;
      }
      
      .grade-value {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 8px;
        
        &.grade-A { color: var(--el-color-danger); }
        &.grade-B { color: var(--el-color-warning); }
        &.grade-C { color: var(--el-color-primary); }
        &.grade-D { color: var(--el-color-info); }
      }
      
      .grade-desc {
        font-size: 13px;
        color: var(--el-text-color-regular);
        line-height: 1.5;
      }
    }
  }
  
  .radar-section {
    padding: 20px 0;
    
    .radar-chart {
      width: 100%;
      height: 300px;
    }
  }
  
  .suggestions-section {
    h4 {
      margin: 0 0 16px;
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
    
    .suggestion-item {
      margin-bottom: 12px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.history-card {
  .dimension-scores {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    font-size: 12px;
    color: var(--el-text-color-secondary);
    
    span {
      padding: 2px 8px;
      background: var(--el-bg-color-page);
      border-radius: 4px;
    }
  }
}
</style>