<template>
  <el-dialog
    v-model="dialogVisible"
    title="岗位分析"
    width="1200px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="analysis-container" v-loading="loading">
      <!-- 分析维度选择 -->
      <el-card shadow="never" class="dimension-card">
        <template #header>
          <div class="card-header">
            <span class="card-title">分析维度</span>
            <el-button type="primary" size="small" @click="handleAnalyze">
              <el-icon><DataAnalysis /></el-icon>
              开始分析
            </el-button>
          </div>
        </template>
        
        <el-checkbox-group v-model="selectedDimensions" class="dimension-group">
          <el-checkbox label="workload">工作量分析</el-checkbox>
          <el-checkbox label="skill">技能匹配分析</el-checkbox>
          <el-checkbox label="efficiency">效率分析</el-checkbox>
          <el-checkbox label="development">发展潜力分析</el-checkbox>
          <el-checkbox label="market">市场对比分析</el-checkbox>
          <el-checkbox label="risk">风险评估</el-checkbox>
        </el-checkbox-group>
      </el-card>

      <!-- 分析结果 -->
      <div v-if="analysisResult" class="analysis-results">
        <!-- 工作量分析 -->
        <el-card v-if="analysisResult.workload" shadow="never" class="result-card">
          <template #header>
            <span class="card-title">工作量分析</span>
          </template>
          
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="metric-item">
                <div class="metric-value">{{ analysisResult.workload.averageWorkload }}%</div>
                <div class="metric-label">平均工作负荷</div>
                <el-progress
                  :percentage="analysisResult.workload.averageWorkload"
                  :color="getWorkloadColor"
                 />
              </div>
            </el-col>
            <el-col :span="8">
              <div class="metric-item">
                <div class="metric-value">{{ analysisResult.workload.peakPeriods }}</div>
                <div class="metric-label">高峰时段</div>
                <div class="metric-desc">{{ analysisResult.workload.peakDescription }}</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="metric-item">
                <div class="metric-value">{{ analysisResult.workload.taskCount }}</div>
                <div class="metric-label">任务数量</div>
                <div class="metric-desc">日均处理任务</div>
              </div>
            </el-col>
          </el-row>
          
          <div class="workload-chart" ref="workloadChart"></div>
        </el-card>

        <!-- 技能匹配分析 -->
        <el-card v-if="analysisResult.skill" shadow="never" class="result-card">
          <template #header>
            <span class="card-title">技能匹配分析</span>
          </template>
          
          <div class="skill-analysis">
            <div class="skill-summary">
              <el-statistic title="整体匹配度" :value="analysisResult.skill.overallMatch">
                <template #suffix>%</template>
              </el-statistic>
            </div>
            
            <el-table :data="analysisResult.skill.details" style="width: 100%">
              <el-table-column prop="skillName" label="技能要求" width="200"  />
              <el-table-column prop="requiredLevel" label="要求等级" width="120">
                <template #default="{ row }">
                  <el-rate
                    v-model="row.requiredLevel"
                    disabled
                    :max="5"
                    :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
                   />
                </template>
              </el-table-column>
              <el-table-column prop="currentLevel" label="当前水平" width="120">
                <template #default="{ row }">
                  <el-rate
                    v-model="row.currentLevel"
                    disabled
                    :max="5"
                    :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
                   />
                </template>
              </el-table-column>
              <el-table-column prop="gap" label="差距" width="100">
                <template #default="{ row }">
                  <el-tag
                    :type="row.gap > 0 ? 'danger' : row.gap < 0 ? 'success' : 'info'"
                  >
                    {{ row.gap > 0 ? `缺 ${row.gap}` : row.gap < 0 ? `超 ${Math.abs(row.gap)}` : '匹配' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="suggestion" label="提升建议"  />
            </el-table>
          </div>
        </el-card>

        <!-- 效率分析 -->
        <el-card v-if="analysisResult.efficiency" shadow="never" class="result-card">
          <template #header>
            <span class="card-title">效率分析</span>
          </template>
          
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="efficiency-metric">
                <div class="metric-icon">
                  <el-icon><Timer /></el-icon>
                </div>
                <div class="metric-info">
                  <div class="metric-value">{{ analysisResult.efficiency.avgCompletionTime }}</div>
                  <div class="metric-label">平均完成时间</div>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="efficiency-metric">
                <div class="metric-icon">
                  <el-icon><TrendCharts /></el-icon>
                </div>
                <div class="metric-info">
                  <div class="metric-value">{{ analysisResult.efficiency.productivity }}%</div>
                  <div class="metric-label">生产效率</div>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="efficiency-metric">
                <div class="metric-icon">
                  <el-icon><CircleCheck /></el-icon>
                </div>
                <div class="metric-info">
                  <div class="metric-value">{{ analysisResult.efficiency.qualityScore }}</div>
                  <div class="metric-label">质量评分</div>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="efficiency-metric">
                <div class="metric-icon">
                  <el-icon><Warning /></el-icon>
                </div>
                <div class="metric-info">
                  <div class="metric-value">{{ analysisResult.efficiency.errorRate }}%</div>
                  <div class="metric-label">错误率</div>
                </div>
              </div>
            </el-col>
          </el-row>
          
          <div class="efficiency-trend" ref="efficiencyChart"></div>
        </el-card>

        <!-- 发展潜力分析 -->
        <el-card v-if="analysisResult.development" shadow="never" class="result-card">
          <template #header>
            <span class="card-title">发展潜力分析</span>
          </template>
          
          <div class="development-analysis">
            <el-row :gutter="20">
              <el-col :span="12">
                <h4>岗位发展路径</h4>
                <el-timeline>
                  <el-timeline-item
                    v-for="(path, index) in analysisResult.development.careerPaths"
                    :key="index"
                    :timestamp="path.timeframe"
                  >
                    <div class="path-item">
                      <div class="path-title">{{ path.position }}</div>
                      <div class="path-desc">{{ path.requirements }}</div>
                    </div>
                  </el-timeline-item>
                </el-timeline>
              </el-col>
              
              <el-col :span="12">
                <h4>能力提升建议</h4>
                <div class="capability-list">
                  <div
                    v-for="(cap, index) in analysisResult.development.capabilities"
                    :key="index"
                    class="capability-item"
                  >
                    <div class="cap-header">
                      <span class="cap-name">{{ cap.name }}</span>
                      <el-tag :type="cap.priority === 'high' ? 'danger' : 'info'" size="small">
                        {{ cap.priority === 'high' ? '高优先级' : '建议提升' }}
                      </el-tag>
                    </div>
                    <div class="cap-actions">
                      <el-tag
                        v-for="action in cap.actions"
                        :key="action"
                        effect="plain"
                        size="small"
                      >
                        {{ action }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 市场对比分析 -->
        <el-card v-if="analysisResult.market" shadow="never" class="result-card">
          <template #header>
            <span class="card-title">市场对比分析</span>
          </template>
          
          <div class="market-analysis">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="market-metric">
                  <div class="metric-label">薪酬竞争力</div>
                  <el-progress
                    :percentage="analysisResult.market.salaryCompetitiveness"
                    :format="(percentage) => `${percentage}%`"
                  />
                  <div class="metric-desc">
                    当前薪酬水平处于市场 {{ analysisResult.market.salaryPercentile }} 分位
                  </div>
                </div>
              </el-col>
              
              <el-col :span="8">
                <div class="market-metric">
                  <div class="metric-label">人才供需比</div>
                  <div class="supply-demand">
                    <span class="supply">供给: {{ analysisResult.market.talentSupply }}</span>
                    <span class="vs">VS</span>
                    <span class="demand">需求: {{ analysisResult.market.talentDemand }}</span>
                  </div>
                  <el-tag :type="analysisResult.market.supplyDemandRatio > 1 ? 'success' : 'danger'">
                    {{ analysisResult.market.supplyDemandRatio > 1 ? '供大于求' : '供不应求' }}
                  </el-tag>
                </div>
              </el-col>
              
              <el-col :span="8">
                <div class="market-metric">
                  <div class="metric-label">行业趋势</div>
                  <div class="trend-indicator">
                    <el-icon :class="analysisResult.market.trend === 'up' ? 'trend-up' : 'trend-down'">
                      <TrendCharts />
                    </el-icon>
                    <span>{{ analysisResult.market.trendDescription }}</span>
                  </div>
                </div>
              </el-col>
            </el-row>
            
            <div class="market-chart" ref="marketChart"></div>
          </div>
        </el-card>

        <!-- 风险评估 -->
        <el-card v-if="analysisResult.risk" shadow="never" class="result-card">
          <template #header>
            <span class="card-title">风险评估</span>
          </template>
          
          <div class="risk-assessment">
            <el-alert
              :title="`总体风险等级: ${analysisResult.risk.overallLevel}`"
              :type="getRiskAlertType(analysisResult.risk.overallLevel)"
              :closable="false"
              show-icon
              class="risk-summary"
             />
            
            <el-table :data="analysisResult.risk.details" style="width: 100%">
              <el-table-column prop="category" label="风险类别" width="150"  />
              <el-table-column prop="level" label="风险等级" width="120">
                <template #default="{ row }">
                  <el-tag :type="getRiskTagType(row.level)">
                    {{ row.level }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="description" label="风险描述"  />
              <el-table-column prop="impact" label="潜在影响" width="200"  />
              <el-table-column prop="mitigation" label="缓解措施"  />
            </el-table>
          </div>
        </el-card>
      </div>
    </div>

    <template #footer>
      <el-button @click="handleExport" :disabled="!analysisResult">
        <el-icon><Download /></el-icon>
        导出报告
      </el-button>
      <el-button @click="dialogVisible = false">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
 
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { positionApi } from '@/api/position'
import {
  DataAnalysis,
  Timer,
  TrendCharts,
  CircleCheck,
  Warning,
  Download
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { usePositionStore } from '@/stores/modules/position'
import type { Position } from '@/types/position'

interface Props {
  modelValue: boolean
  positionId: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

interface AnalysisResult {
  workload?: {
    averageWorkload: number
    peakPeriods: string
    peakDescription: string
    taskCount: number
    distribution: Array<{ time: string; value: number }>
  }
  skill?: {
    overallMatch: number
    details: Array<{
      skillName: string
      requiredLevel: number
      currentLevel: number
      gap: number
      suggestion: string
    }>
  }
  efficiency?: {
    avgCompletionTime: string
    productivity: number
    qualityScore: number
    errorRate: number
    trend: Array<{ date: string; value: number }>
  }
  development?: {
    careerPaths: Array<{
      position: string
      timeframe: string
      requirements: string
    }>
    capabilities: Array<{
      name: string
      priority: 'high' | 'medium' | 'low'
      actions: string[]
    }>
  }
  market?: {
    salaryCompetitiveness: number
    salaryPercentile: number
    talentSupply: number
    talentDemand: number
    supplyDemandRatio: number
    trend: 'up' | 'down' | 'stable'
    trendDescription: string
    comparison: Array<{ company: string; salary: number }>
  }
  risk?: {
    overallLevel: string
    details: Array<{
      category: string
      level: string
      description: string
      impact: string
      mitigation: string
    }>
  }
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const positionStore = usePositionStore()

// 图表引用
const workloadChart = ref<HTMLElement>()
const efficiencyChart = ref<HTMLElement>()
const marketChart = ref<HTMLElement>()

// 图表实例
let workloadChartInstance: echarts.ECharts | null = null
let efficiencyChartInstance: echarts.ECharts | null = null
let marketChartInstance: echarts.ECharts | null = null

// 状态
const loading = ref(false)
const selectedDimensions = ref(['workload', 'skill', 'efficiency'])
const analysisResult = ref<AnalysisResult | null>(null)
const positionInfo = ref<Position | null>(null)

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 获取工作负荷颜色
const getWorkloadColor = (percentage: number) => {
  if (percentage <= 60) return '#67C23A'
  if (percentage <= 80) return '#E6A23C'
  return '#F56C6C'
}

// 获取风险警告类型
const getRiskAlertType = (level: string) => {
  const typeMap: Record<string, string> = {
    '低': 'success',
    '中': 'warning',
    '高': 'error'
  }
  return typeMap[level] || 'info'
}

// 获取风险标签类型
const getRiskTagType = (level: string) => {
  const typeMap: Record<string, string> = {
    '低': 'success',
    '中': 'warning',
    '高': 'danger'
  }
  return typeMap[level] || 'info'
}

// 初始化工作量图表
const initWorkloadChart = (data: Array<{ time: string; value: number }>) => {
  if (!workloadChart.value) return
  
  workloadChartInstance = echarts.init(workloadChart.value)
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: data.map(item => item.time)
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [{
      name: 'HrHr工作负荷',
      type: 'line',
      data: data.map(item => item.value),
      smooth: true,
      areaStyle: {
        opacity: 0.3
      }
    }]
  }
  
  workloadChartInstance.setOption(option)
}

// 初始化效率图表
const initEfficiencyChart = (data: Array<{ date: string; value: number }>) => {
  if (!efficiencyChart.value) return
  
  efficiencyChartInstance = echarts.init(efficiencyChart.value)
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: data.map(item => item.date)
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [{
      name: '工作效率',
      type: 'bar',
      data: data.map(item => item.value),
      itemStyle: {
        borderRadius: [4, 4, 0, 0]
      }
    }]
  }
  
  efficiencyChartInstance.setOption(option)
}

// 初始化市场对比图表
const initMarketChart = (data: Array<{ company: string; salary: number }>) => {
  if (!marketChart.value) return
  
  marketChartInstance = echarts.init(marketChart.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'value',
      axisLabel: {
        formatter: '¥{value}'
      }
    },
    yAxis: {
      type: 'category',
      data: data.map(item => item.company)
    },
    series: [{
      name: '薪酬水平',
      type: 'bar',
      data: data.map(item => item.salary),
      itemStyle: {
        borderRadius: [0, 4, 4, 0]
      }
    }]
  }
  
  marketChartInstance.setOption(option)
}

// 获取岗位信息
const fetchPositionInfo = async () => {
  if (!props.positionId) return
  
  try {
    positionInfo.value = await positionStore.fetchPositionDetail(props.positionId)
  } catch (__error) {
    console.error('获取岗位信息失败:', error)
  }
}

// 执行分析
const handleAnalyze = async () => {
  if (selectedDimensions.value.length === 0) {
    ElMessage.warning('请至少选择一个分析维度')
    return
  }
  
  loading.value = true
  try {
    // 模拟分析过程
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // 生成模拟数据
    analysisResult.value = generateMockAnalysisResult()
    
    // 初始化图表
    await nextTick()
    if (analysisResult.value.workload) {
      initWorkloadChart(analysisResult.value.workload.distribution)
    }
    if (analysisResult.value.efficiency) {
      initEfficiencyChart(analysisResult.value.efficiency.trend)
    }
    if (analysisResult.value.market) {
      initMarketChart(analysisResult.value.market.comparison)
    }
    
    ElMessage.success('分析完成')
  } catch (__error) {
    ElMessage.error('分析失败，请重试')
  } finally {
    loading.value = false
  }
}

// 生成模拟分析结果
const generateMockAnalysisResult = (): AnalysisResult => {
  const result: AnalysisResult = {}
  
  if (selectedDimensions.value.includes('workload')) {
    result.workload = {
      averageWorkload: 75,
      peakPeriods: '09:00-11:00, 14:00-16:00',
      peakDescription: '上午和下午的核心工作时段',
      taskCount: 25,
      distribution: [
        { time: '08:00', value: 40 },
        { time: '09:00', value: 85 },
        { time: '10:00', value: 90 },
        { time: '11:00', value: 80 },
        { time: '12:00', value: 30 },
        { time: '13:00', value: 35 },
        { time: '14:00', value: 75 },
        { time: '15:00', value: 85 },
        { time: '16:00', value: 70 },
        { time: '17:00', value: 50 }
      ]
    }
  }
  
  if (selectedDimensions.value.includes('skill')) {
    result.skill = {
      overallMatch: 82,
      details: [
        {
          skillName: '项目管理',
          requiredLevel: 4,
          currentLevel: 3,
          gap: 1,
          suggestion: '建议参加PMP认证培训'
        },
        {
          skillName: '数据分析',
          requiredLevel: 3,
          currentLevel: 4,
          gap: -1,
          suggestion: '技能超出要求，可承担更多分析任务'
        },
        {
          skillName: '沟通协调',
          requiredLevel: 5,
          currentLevel: 5,
          gap: 0,
          suggestion: '保持当前水平'
        },
        {
          skillName: '团队领导',
          requiredLevel: 4,
          currentLevel: 3,
          gap: 1,
          suggestion: '多参与团队管理实践'
        }
      ]
    }
  }
  
  if (selectedDimensions.value.includes('efficiency')) {
    result.efficiency = {
      avgCompletionTime: '2.5小时',
      productivity: 85,
      qualityScore: 92,
      errorRate: 2.3,
      trend: [
        { date: '1月', value: 78 },
        { date: '2月', value: 80 },
        { date: '3月', value: 82 },
        { date: '4月', value: 85 },
        { date: '5月', value: 83 },
        { date: '6月', value: 85 }
      ]
    }
  }
  
  if (selectedDimensions.value.includes('development')) {
    result.development = {
      careerPaths: [
        {
          position: '高级专员',
          timeframe: '1-2年',
          requirements: '完成中级职称认证，独立负责项目'
        },
        {
          position: '部门主管',
          timeframe: '3-5年',
          requirements: '具备团队管理经验，完成管理培训'
        },
        {
          position: '部门经理',
          timeframe: '5-8年',
          requirements: '战略规划能力，跨部门协作经验'
        }
      ],
      capabilities: [
        {
          name: '战略思维',
          priority: 'high',
          actions: ['参加战略管理课程', '参与部门规划制定']
        },
        {
          name: '数据决策',
          priority: 'medium',
          actions: ['学习数据分析工具', '实践数据驱动项目']
        }
      ]
    }
  }
  
  if (selectedDimensions.value.includes('market')) {
    result.market = {
      salaryCompetitiveness: 72,
      salaryPercentile: 65,
      talentSupply: 1200,
      talentDemand: 1500,
      supplyDemandRatio: 0.8,
      trend: 'up',
      trendDescription: '行业需求持续增长',
      comparison: [
        { company: '本公司', salary: 12000 },
        { company: '行业平均', salary: 13500 },
        { company: '头部企业', salary: 18000 },
        { company: '初创公司', salary: 11000 }
      ]
    }
  }
  
  if (selectedDimensions.value.includes('risk')) {
    result.risk = {
      overallLevel: '中',
      details: [
        {
          category: '人员流失',
          level: '高',
          description: '关键岗位人员流失风险较高',
          impact: '影响业务连续性和知识传承',
          mitigation: '建立人才梯队，完善激励机制'
        },
        {
          category: '技能过时',
          level: '中',
          description: '技术快速迭代导致技能需求变化',
          impact: '降低工作效率和质量',
          mitigation: '定期技能评估和培训'
        },
        {
          category: '工作负荷',
          level: '低',
          description: '当前工作负荷在可控范围内',
          impact: '轻微影响员工满意度',
          mitigation: '优化工作流程，合理分配任务'
        }
      ]
    }
  }
  
  return result
}

// 导出报告
const handleExport = async () => {
  // 实现报告导出功能
  if (!props.position) {
    ElMessage.warning('没有可导出的岗位信息')
    return
  }
  
  try {
    const blob = await positionApi.exportAnalysisReport(props.position.positionId, 'pdf')
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `岗位分析报告_${props.position.positionName}_${new Date().getTime()}.pdf`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('报告导出成功')
  } catch (__error) {
    console.error('导出报告失败:', error)
    ElMessage.error('导出报告失败')
  }
}

// 关闭对话框
const handleClose = () => {
  analysisResult.value = null
  selectedDimensions.value = ['workload', 'skill', 'efficiency']
  
  // 销毁图表实例
  if (workloadChartInstance) {
    workloadChartInstance.dispose()
    workloadChartInstance = null
  }
  if (efficiencyChartInstance) {
    efficiencyChartInstance.dispose()
    efficiencyChartInstance = null
  }
  if (marketChartInstance) {
    marketChartInstance.dispose()
    marketChartInstance = null
  }
}

// 监听对话框打开
watch(dialogVisible, (val) => {
  if (val) {
    fetchPositionInfo()
  }
})

// 监听窗口大小变化
onMounted(() => {
  window.addEventListener('resize', () => {
    workloadChartInstance?.resize()
    efficiencyChartInstance?.resize()
    marketChartInstance?.resize()
  })
})
</script>

<style lang="scss" scoped>
.analysis-container {
  max-height: 70vh;
  overflow-y: auto;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  .card-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

.dimension-card {
  margin-bottom: 20px;
  
  .dimension-group {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
  }
}

.analysis-results {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.result-card {
  .metric-item {
    text-align: center;
    
    .metric-value {
      font-size: 32px;
      font-weight: 600;
      color: var(--el-color-primary);
      margin-bottom: 8px;
    }
    
    .metric-label {
      font-size: 14px;
      color: var(--el-text-color-regular);
      margin-bottom: 12px;
    }
    
    .metric-desc {
      font-size: 12px;
      color: var(--el-text-color-secondary);
    }
  }
  
  .workload-chart,
  .efficiency-trend,
  .market-chart {
    width: 100%;
    height: 300px;
    margin-top: 20px;
  }
}

.skill-analysis {
  .skill-summary {
    text-align: center;
    margin-bottom: 20px;
    padding: 20px;
    background: var(--el-bg-color-page);
    border-radius: 8px;
  }
}

.efficiency-metric {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  
  .metric-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--el-color-primary-light-9);
    border-radius: 8px;
    
    .el-icon {
      font-size: 24px;
      color: var(--el-color-primary);
    }
  }
  
  .metric-info {
    flex: 1;
    
    .metric-value {
      font-size: 20px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
    
    .metric-label {
      font-size: 12px;
      color: var(--el-text-color-secondary);
    }
  }
}

.development-analysis {
  h4 {
    margin: 0 0 16px;
    font-size: 14px;
    color: var(--el-text-color-regular);
  }
  
  .path-item {
    .path-title {
      font-size: 16px;
      font-weight: 500;
      color: var(--el-text-color-primary);
      margin-bottom: 4px;
    }
    
    .path-desc {
      font-size: 13px;
      color: var(--el-text-color-secondary);
    }
  }
  
  .capability-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
    
    .capability-item {
      padding: 12px;
      background: var(--el-bg-color-page);
      border-radius: 8px;
      
      .cap-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;
        
        .cap-name {
          font-weight: 500;
          color: var(--el-text-color-primary);
        }
      }
      
      .cap-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }
    }
  }
}

.market-analysis {
  .market-metric {
    .metric-label {
      font-size: 14px;
      color: var(--el-text-color-regular);
      margin-bottom: 12px;
    }
    
    .supply-demand {
      display: flex;
      align-items: center;
      gap: 12px;
      margin: 12px 0;
      font-size: 16px;
      
      .supply {
        color: var(--el-color-success);
      }
      
      .vs {
        color: var(--el-text-color-secondary);
      }
      
      .demand {
        color: var(--el-color-warning);
      }
    }
    
    .trend-indicator {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      margin-top: 12px;
      
      .trend-up {
        color: var(--el-color-success);
      }
      
      .trend-down {
        color: var(--el-color-danger);
      }
    }
  }
}

.risk-assessment {
  .risk-summary {
    margin-bottom: 20px;
  }
}
</style>