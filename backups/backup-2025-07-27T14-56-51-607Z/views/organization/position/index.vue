<template>
  <div class="position-management">
    <!-- 左侧：岗位体系树 -->
    <div class="position-tree">
      <div class="tree-header">
        <h3>岗位体系</h3>
        <div class="tree-actions">
          <el-button link @click="handleManageSequence">
            <el-icon><Setting /></el-icon>
            序列管理
          </el-button>
        </div>
      </div>
      <div class="tree-content">
        <el-input
          v-model="treeFilter"
          placeholder="搜索岗位"
          clearable
          prefix-icon="Search"
          @input="handleTreeFilter"
        />
        <el-tree
          ref="treeRef"
          :data="positionTree"
          :props="treeProps"
          :filter-node-method="filterNode"
          :expand-on-click-node="false"
          node-key="id"
          highlight-current
          @node-click="handleNodeClick"
        >
          <template #default="{ node, data }">
            <span class="tree-node">
              <el-icon v-if="data.type === 'sequence'" class="node-icon">
                <Folder />
              </el-icon>
              <el-icon v-else-if="data.type === 'family'" class="node-icon">
                <FolderOpened />
              </el-icon>
              <el-icon v-else class="node-icon">
                <Document />
              </el-icon>
              <span>{{ node.label }}</span>
              <span v-if="data.count" class="node-count">({{ data.count }})</span>
            </span>
          </template>
        </el-tree>
      </div>
    </div>

    <!-- 右侧：岗位列表 -->
    <div class="position-content">
      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-form :inline="true" :model="searchForm" @submit.prevent="handleSearch">
          <el-form-item label="关键词">
            <el-input
              v-model="searchForm.keyword"
              placeholder="岗位名称/编码"
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item label="岗位类别">
            <el-select
              v-model="searchForm.positionCategory"
              placeholder="全部"
              clearable
              style="width: 150px"
            >
              <el-option
                v-for="item in positionCategoryOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="岗位状态">
            <el-select
              v-model="searchForm.status"
              placeholder="全部"
              clearable
              style="width: 120px"
            >
              <el-option
                v-for="item in positionStatusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <el-button type="primary" @click="handleCreate">
            <el-icon><Plus /></el-icon>
            新增岗位
          </el-button>
          <el-button @click="handleBatchImport">
            <el-icon><Upload /></el-icon>
            批量导入
          </el-button>
          <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
          <el-dropdown v-if="selectedRows.length > 0" @command="handleBatchCommand">
            <el-button>
              批量操作
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="freeze">批量冻结</el-dropdown-item>
                <el-dropdown-item command="active">批量激活</el-dropdown-item>
                <el-dropdown-item command="delete" divided>批量删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
        <div class="toolbar-right">
          <el-button :icon="Refresh" circle @click="handleRefresh" />
          <el-button :icon="Setting" circle @click="handleColumnSetting" />
        </div>
      </div>

      <!-- 数据表格 -->
      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="positionList"
        :height="tableHeight"
        row-key="positionId"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <el-table-column type="selection" width="50" fixed="left" />
        <el-table-column prop="positionCode" label="岗位编码" width="120" sortable="custom" fixed="left" />
        <el-table-column prop="positionName" label="岗位名称" min-width="150" sortable="custom" />
        <el-table-column prop="positionCategory" label="岗位类别" width="100">
          <template #default="{ row }">
            <el-tag :type="getCategoryType(row.positionCategory)">
              {{ getCategoryLabel(row.positionCategory) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="positionLevel" label="岗位等级" width="100">
          <template #default="{ row }">
            <span v-if="row.positionLevel">
              {{ getLevelLabel(row.positionLevel) }}
            </span>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="institutionName" label="所属机构" min-width="150" />
        <el-table-column prop="establishmentInfo" label="编制情况" width="150">
          <template #default="{ row }">
            <div class="establishment-info">
              <span>{{ row.actualCount || 0 }}/{{ row.establishmentCount || 0 }}</span>
              <el-tag
                v-if="row.vacancyCount > 0"
                type="danger"
                size="small"
                class="ml-8"
              >
                缺{{ row.vacancyCount }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" :effect="getStatusEffect(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleView(row)">查看</el-button>
            <el-button link type="primary" @click="handleEdit(row)">编辑</el-button>
            <el-dropdown @command="(cmd) => handleCommand(cmd, row)">
              <el-button link type="primary">
                更多
                <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="adjust">调整编制</el-dropdown-item>
                  <el-dropdown-item command="staffing">人员配置</el-dropdown-item>
                  <el-dropdown-item command="evaluate">价值评估</el-dropdown-item>
                  <el-dropdown-item command="analysis">岗位分析</el-dropdown-item>
                  <el-dropdown-item divided command="freeze" v-if="row.status === 'ACTIVE'">
                    冻结岗位
                  </el-dropdown-item>
                  <el-dropdown-item command="active" v-if="row.status === 'FROZEN'">
                    激活岗位
                  </el-dropdown-item>
                  <el-dropdown-item command="merge">合并岗位</el-dropdown-item>
                  <el-dropdown-item command="cancel" divided>撤销岗位</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
    </div>

    <!-- 岗位编辑对话框 -->
    <PositionEditDialog
      v-model="editDialogVisible"
      :position-id="currentPositionId"
      :mode="editMode"
      @success="handleEditSuccess"
    />

    <!-- 岗位详情抽屉 -->
    <PositionDetailDrawer
      v-model="detailDrawerVisible"
      :position-id="currentPositionId"
    />

    <!-- 序列管理对话框 -->
    <el-dialog
      v-model="sequenceDialogVisible"
      title="岗位序列管理"
      width="1000px"
      append-to-body
    >
      <HrPositionSequence />
    </el-dialog>

    <!-- 编制调整对话框 -->
    <EstablishmentAdjustDialog
      v-model="adjustDialogVisible"
      :position-id="currentPositionId"
      @success="handleAdjustSuccess"
    />

    <!-- 人员配置对话框 -->
    <StaffingDialog
      v-model="staffingDialogVisible"
      :position-id="currentPositionId"
      @success="handleStaffingSuccess"
    />

    <!-- 价值评估对话框 -->
    <ValueEvaluationDialog
      v-model="evaluationDialogVisible"
      :position-id="currentPositionId"
      @success="handleEvaluationSuccess"
    />

    <!-- 岗位合并对话框 -->
    <PositionMergeDialog
      v-model="mergeDialogVisible"
      :source-positions="mergeSourcePositions"
      @success="handleMergeSuccess"
    />

    <!-- 岗位分析对话框 -->
    <PositionAnalysisDialog
      v-model="analysisDialogVisible"
      :position-id="currentPositionId"
    />

    <!-- 批量导入对话框 -->
    <HrImportDialog
      v-model="importDialogVisible"
      title="批量导入岗位"
      template-name="岗位信息"
      :upload-api="importApi"
      @success="handleImportSuccess"
    />

    <!-- 导出配置对话框 -->
    <HrDataExport
      v-model="exportDialogVisible"
      title="导出岗位数据"
      :fields="exportFields"
      :fetch-data="fetchExportData"
      file-name="岗位信息"
    />
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
defineOptions({
  name: 'PositionPage'
})

import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox, ElTree } from 'element-plus'
import {
  Plus,
  Search,
  Upload,
  Download,
  ArrowDown,
  Refresh,
  Setting,
  Folder,
  FolderOpened,
  Document
} from '@element-plus/icons-vue'
import { usePositionStore } from '@/stores'
import type {
  Position,
  PositionQueryParams,
  PositionStatus,
  PositionCategory,
  PositionBatchRequest
} from '@/types/position'
import {
  positionCategoryOptions,
  positionStatusOptions,
  positionLevelOptions
} from '@/types/position'
import HrPositionSequence from '@/components/position/HrPositionSequence.vue'
import PositionEditDialog from './components/PositionEditDialog.vue'
import PositionDetailDrawer from './components/PositionDetailDrawer.vue'
import EstablishmentAdjustDialog from './components/EstablishmentAdjustDialog.vue'
import StaffingDialog from './components/StaffingDialog.vue'
import ValueEvaluationDialog from './components/ValueEvaluationDialog.vue'
import PositionMergeDialog from './components/PositionMergeDialog.vue'
import PositionAnalysisDialog from './components/PositionAnalysisDialog.vue'
import HrImportDialog from '@/components/common/HrImportDialog.vue'
import HrDataExport from '@/components/common/HrDataExport.vue'
import positionApi from '@/api/modules/position'

// 状态管理
const positionStore = usePositionStore()

// refs
const tableRef = ref()
const treeRef = ref<InstanceType<typeof ElTree>>()

// 响应式数据
const loading = ref(false)
const positionList = ref<Position[]>([])
const selectedRows = ref<Position[]>([])
const currentPosition = ref<Position | null>(null)
const currentPositionId = ref('')
const editMode = ref<'create' | 'edit'>('create')
const mergeSourcePositions = ref<Position[]>([])

// 对话框状态
const editDialogVisible = ref(false)
const detailDrawerVisible = ref(false)
const sequenceDialogVisible = ref(false)
const adjustDialogVisible = ref(false)
const staffingDialogVisible = ref(false)
const evaluationDialogVisible = ref(false)
const mergeDialogVisible = ref(false)
const analysisDialogVisible = ref(false)
const importDialogVisible = ref(false)
const exportDialogVisible = ref(false)

// 搜索表单
const searchForm = reactive<PositionQueryParams>({
  keyword: '',
  positionCategory: undefined,
  status: undefined
})

// 分页信息
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

// 树形数据
const treeFilter = ref('')
const positionTree = ref<any[]>([])
const treeProps = {
  label: 'label',
  children: 'children'
}

// 表格高度
const tableHeight = computed(() => {
  return `calc(100vh - 320px)`
})

// 获取类别标签
const getCategoryLabel = (category: PositionCategory) => {
  const option = positionCategoryOptions.find(item => item.value === category)
  return option?.label || category
}

// 获取类别类型
const getCategoryType = (category: PositionCategory) => {
  const typeMap: Record<PositionCategory, string> = {
    MANAGEMENT: 'primary',
    PROFESSIONAL: 'success',
    WORKER: 'warning',
    TEACHER: 'info',
    ADMINISTRATIVE: ''
  }
  return typeMap[category] || ''
}

// 获取等级标签
const getLevelLabel = (level: string) => {
  const option = positionLevelOptions.find(item => item.value === level)
  return option?.label || level
}

// 获取状态标签
const getStatusLabel = (status: PositionStatus) => {
  const option = positionStatusOptions.find(item => item.value === status)
  return option?.label || status
}

// 获取状态类型
const getStatusType = (status: PositionStatus) => {
  const typeMap: Record<PositionStatus, string> = {
    ACTIVE: 'success',
    FROZEN: 'warning',
    CANCELLED: 'info',
    MERGED: 'info'
  }
  return typeMap[status] || ''
}

// 获取状态效果
const getStatusEffect = (status: PositionStatus) => {
  return status === 'ACTIVE' ? 'light' : 'plain'
}

// 导入API
const importApi = async (formData: FormData) => {
  return await positionApi.import(formData)
}

// 导出字段配置
const exportFields = [
  {
    group: '基本信息',
    fields: [
      { prop: 'positionCode', label: '岗位编码' },
      { prop: 'positionName', label: '岗位名称' },
      { prop: 'positionCategory', label: '岗位类别' },
      { prop: 'positionLevel', label: '岗位等级' },
      { prop: 'positionType', label: '岗位类型' },
      { prop: 'institutionName', label: '所属机构' }
    ]
  },
  {
    group: '岗位详情',
    fields: [
      { prop: 'jobResponsibilities', label: '岗位职责' },
      { prop: 'qualifications', label: '任职资格' },
      { prop: 'majorRequirements', label: '专业要求' },
      { prop: 'educationRequirement', label: '学历要求' },
      { prop: 'experienceRequirement', label: '经验要求' },
      { prop: 'skillRequirements', label: '技能要求' }
    ]
  },
  {
    group: '编制信息',
    fields: [
      { prop: 'establishmentCount', label: '编制数' },
      { prop: 'actualCount', label: '实际人数' },
      { prop: 'vacancyCount', label: '空缺数' },
      { prop: 'salaryMin', label: '最低薪酬' },
      { prop: 'salaryMax', label: '最高薪酬' }
    ]
  }
]

// 获取导出数据
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const fetchExportData = async (params: unknown) => {
  const {data: _data} =  await positionApi.getList({
    ...searchForm,
    ...params,
    page: 1,
    size: 10000
  })
  return data.list
}

// 获取岗位列表
const fetchPositions 
  height: 100%;
  background: #f5f7fa;
  gap: 16px;
  padding: 16px;

  .position-tree {
    width: 300px;
    background: white;
    border-radius: 4px;
    display: flex;
    flex-direction: column;

    .tree-header {
      padding: 16px;
      border-bottom: 1px solid #ebeef5;
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
      }
    }

    .tree-content {
      flex: 1;
      padding: 16px;
      overflow: auto;

      .el-input {
        margin-bottom: 16px;
      }

      .tree-node {
        display: flex;
        align-items: center;
        gap: 4px;

        .node-icon {
          color: #909399;
        }

        .node-count {
          color: #909399;
          font-size: 12px;
          margin-left: 4px;
        }
      }
    }
  }

  .position-content {
    flex: 1;
    background: white;
    border-radius: 4px;
    padding: 20px;
    display: flex;
    flex-direction: column;

    .search-bar {
      margin-bottom: 16px;
    }

    .toolbar {
      margin-bottom: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .toolbar-left {
        display: flex;
        gap: 8px;
      }

      .toolbar-right {
        display: flex;
        gap: 8px;
      }
    }

    .el-table {
      flex: 1;
      margin-bottom: 16px;
    }

    .establishment-info {
      display: flex;
      align-items: center;
    }

    .text-muted {
      color: #909399;
    }

    .ml-8 {
      margin-left: 8px;
    }
  }
}
</style>