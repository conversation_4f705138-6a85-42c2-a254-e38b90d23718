<template>
  <div class="organization-example">
    <h3>组织架构树示例</h3>

    <!-- 搜索栏 -->
    <el-input
      v-model="searchKeyword"
      placeholder="搜索组织名称或编码"
      prefix-icon="Search"
      clearable
      style="margin-bottom: 20px; width: 300px"
    />

    <!-- 使用简化版组织树组件 -->
    <HrOrgTreeSimple
      :data="organizationData"
      :search-keyword="searchKeyword"
      :show-checkbox="showCheckbox"
      :draggable="draggable"
      @node-click="handleNodeClick"
      @check-change="handleCheckChange"
    />

    <!-- 控制选项 -->
    <div style="margin-top: 20px">
      <el-checkbox v-model="showCheckbox">显示复选框</el-checkbox>
      <el-checkbox v-model="draggable" style="margin-left: 20px">启用拖拽</el-checkbox>
    </div>

    <!-- 选中信息 -->
    <div v-if="selectedNode" style="margin-top: 20px">
      <h4>选中的节点：</h4>
      <pre>{{ JSON.stringify(selectedNode, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * @name 组织架构树使用示例
 * @description 展示如何使用 HrOrgTreeSimple 组件
 * <AUTHOR> Assistant
 * @since 2025-07-25
 */

import { ref, onMounted } from 'vue'
import HrOrgTreeSimple from '@/components/organization/HrOrgTreeSimple.vue'
import { useOrganizationStore } from '@/stores/modules/organization'
import type { Organization } from '@/types/organization-view'
import { toViewOrganizationList } from '@/types/organization-view'

// 组织 store
const organizationStore = useOrganizationStore()

// 响应式数据
const searchKeyword = ref('')
const showCheckbox = ref(false)
const draggable = ref(false)
const organizationData = ref<Organization[]>([])
const selectedNode = ref<Organization | null>(null)

// 加载组织数据
const loadData = async () => {
  try {
    const data = await organizationStore.loadOrganizationTree()
    // 将基础类型转换为视图类型
    organizationData.value = toViewOrganizationList(data)
  } catch (error) {
    console.error('加载组织数据失败:', error)
  }
}

// 处理节点点击
const handleNodeClick = (data: Organization) => {
  selectedNode.value = data
  console.log('节点点击:', data)
}

// 处理复选框变化
const handleCheckChange = (data: Organization, checked: boolean, indeterminate: boolean) => {
  console.log('复选框变化:', data.name, checked, indeterminate)
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.organization-example {
  padding: 20px;
  height: 100%;
  overflow: auto;

  h3 {
    margin-bottom: 20px;
  }

  pre {
    background-color: #f5f7fa;
    padding: 10px;
    border-radius: 4px;
    overflow: auto;
    max-height: 200px;
  }
}
</style>
