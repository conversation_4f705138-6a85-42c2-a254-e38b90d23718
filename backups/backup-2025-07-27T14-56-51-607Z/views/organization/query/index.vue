<template>
  <div class="organization-query">
    <!-- 查询条件区域 -->
    <el-card class="search-card">
      <el-form
        ref="searchFormRef"
        :model="searchForm"
        :inline="true"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="机构名称">
              <el-input
                v-model="searchForm.institutionName"
                placeholder="请输入机构名称"
                clearable
                />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="机构类型">
              <el-select
                v-model="searchForm.institutionType"
                placeholder="请选择机构类型"
                clearable
                multiple
              >
                <el-option
                  v-for="item in organizationTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                 />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="机构状态">
              <el-select
                v-model="searchForm.status"
                placeholder="请选择机构状态"
                clearable
              >
                <el-option label="正常" value="ACTIVE"  />
                <el-option label="已撤销" value="CANCELLED"  />
                <el-option label="已合并" value="MERGED"  />
                <el-option label="已停用" value="DISABLED"  />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="成立日期">
              <el-date-picker
                v-model="searchForm.establishDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="查询类型">
              <el-select
                v-model="queryType"
                placeholder="请选择查询类型"
                @change="handleQueryTypeChange"
              >
                <el-option label="机构信息查询" value="organization"  />
                <el-option label="历史变更查询" value="history"  />
                <el-option label="人员分布查询" value="employee"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label-width="0">
              <el-button type="primary" :icon="Search" @click="handleSearch">
                查询
              </el-button>
              <el-button :icon="RefreshRight" @click="handleReset">
                重置
              </el-button>
              <el-button :icon="Download" @click="handleExport" v-if="queryResults.length > 0">
                导出
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 视图切换 -->
    <el-card class="view-switch-card" v-if="queryResults.length > 0">
      <div class="view-switch">
        <span class="switch-label">视图模式：</span>
        <el-radio-group v-model="viewMode" @change="handleViewChange">
          <el-radio-button label="tree">树形视图</el-radio-button>
          <el-radio-button label="list">列表视图</el-radio-button>
          <el-radio-button label="graph">图形视图</el-radio-button>
        </el-radio-group>
      </div>
    </el-card>

    <!-- 查询结果区域 -->
    <el-card class="result-card" v-loading="loading">
      <!-- 树形视图 -->
      <div v-if="viewMode === 'tree' && queryResults.length > 0" class="tree-view">
        <HrOrgTree
          ref="queryTreeRef"
          :data="treeData"
          :lazy="false"
          :draggable="false"
          :show-toolbar="false"
          @node-click="handleNodeClick"
        />
      </div>

      <!-- 列表视图 -->
      <div v-else-if="viewMode === 'list' && queryResults.length > 0" class="list-view">
        <el-table
          :data="queryResults"
          stripe
          border
          @row-click="handleRowClick"
        >
          <el-table-column prop="institutionCode" label="机构编码" width="120"  />
          <el-table-column prop="institutionName" label="机构名称" min-width="200">
            <template #default="{ row }">
              <span>{{ row.institutionName }}</span>
              <el-tag
                v-if="row.institutionType === 'TEMP_ORG'"
                type="info"
                size="small"
                style="margin-left: 5px"
              >
                临时
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="institutionType" label="机构类型" width="120">
            <template #default="{ row }">
              <el-tag :type="getTypeTagType(row.institutionType)">
                {{ getTypeName(row.institutionType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="parentInstitutionName" label="上级机构" width="150"  />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="establishDate" label="成立日期" width="120">
            <template #default="{ row }">
              {{ formatDate(row.establishDate) }}
            </template>
          </el-table-column>
          <el-table-column prop="leaderName" label="负责人" width="100"  />
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" text @click="handleViewDetail(row)">
                查看详情
              </el-button>
              <el-button type="primary" size="small" text @click="handleViewHistory(row)">
                变更历史
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>

      <!-- 图形视图 -->
      <div v-else-if="viewMode === 'graph' && queryResults.length > 0" class="graph-view">
        <HrOrgChart
          :data="treeData"
          @node-click="handleNodeClick"
        />
      </div>

      <!-- 空状态 -->
      <el-empty v-else description="请输入查询条件进行查询"  />
    </el-card>

    <!-- 详情对话框 -->
    <OrganizationDetailDialog
      v-model="detailVisible"
      :organization-id="currentOrgId"
    />

    <!-- 历史记录对话框 -->
    <HistoryDialog
      v-model="historyVisible"
      :organization-id="currentOrgId"
      :query-type="queryType"
    />
  </div>
</template>

<script setup lang="ts">
 
defineOptions({
  name: 'QueryPage'
})

import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, RefreshRight, Download } from '@element-plus/icons-vue'
import { useOrganizationStore } from '@/stores/modules/organization'
import HrOrgTree from '@/components/organization/HrOrgTree.vue'
import HrOrgChart from '@/components/organization/HrOrgChart.vue'
import OrganizationDetailDialog from './components/OrganizationDetailDialog.vue'
import HistoryDialog from './components/HistoryDialog.vue'
import type { Organization } from '@/types/organization'
import { organizationTypeOptions } from '@/types/organization'
import { exportToExcel } from '@/utils/export'

// store
const organizationStore = useOrganizationStore()

// refs
const searchFormRef = ref()
const queryTreeRef = ref()

// 状态
const loading = ref(false)
const queryType = ref('organization')
const viewMode = ref('list')
const queryResults = ref<Organization[]>([])
const treeData = ref<Organization[]>([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const detailVisible = ref(false)
const historyVisible = ref(false)
const currentOrgId = ref('')

// 查询表单
const searchForm = reactive({
  institutionName: '',
  institutionType: [] as string[],
  status: '',
  establishDateRange: [] as string[]
})

// 方法
const getTypeName = (type: string) => {
  const item = organizationTypeOptions.find(i => i.value === type)
  return item?.label || type
}

const getTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    SCHOOL: '',
    COLLEGE: 'success',
    DEPARTMENT: 'warning',
    TEACHING_DEPT: 'danger',
    RESEARCH_INST: 'primary',
    ADMIN_DEPT: 'info',
    DIRECT_UNIT: 'warning',
    TEMP_ORG: 'info'
  }
  return typeMap[type] || ''
}

const getStatusName = (status: string) => {
  const statusMap: Record<string, string> = {
    ACTIVE: '正常',
    CANCELLED: '已撤销',
    MERGED: '已合并',
    DISABLED: '已停用'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    ACTIVE: 'success',
    CANCELLED: 'danger',
    MERGED: 'warning',
    DISABLED: 'info'
  }
  return statusMap[status] || 'info'
}

const formatDate = (date: string | undefined) => {
  if (!date) return '-'
  return new Date(date).toLocaleDateString('zh-CN')
}

// 构建树形数据
const buildTreeData = (list: Organization[]): Organization[] => {
  const map = new Map<string, Organization>()
  const tree: Organization[] = []
  
  // 先创建所有节点的映射
  list.forEach(item => {
    map.set(item.institutionId, { ...item, children: [] })
  })
  
  // 构建树形结构
  list.forEach(item => {
    const node = map.get(item.institutionId)!
    if (item.parentInstitutionId && map.has(item.parentInstitutionId)) {
      const parent = map.get(item.parentInstitutionId)!
      if (!parent.children) parent.children = []
      parent.children.push(node)
    } else {
      tree.push(node)
    }
  })
  
  return tree
}

// 查询
const handleSearch = async () => {
  loading.value = true
  
  try {
   
    const params: unknown = {
      ...searchForm,
      queryType: queryType.value,
      page: currentPage.value,
      size: pageSize.value
    }
    
    // 处理日期范围
    if (searchForm.establishDateRange && searchForm.establishDateRange.length === 2) {
      params.establishDateStart = searchForm.establishDateRange[0]
      params.establishDateEnd = searchForm.establishDateRange[1]
    }
    delete params.establishDateRange
    
    // 根据查询类型调用不同的接口
    let result
    if (queryType.value === 'organization') {
      result = await organizationStore.queryOrganizations(params)
    } else if (queryType.value === 'history') {
      result = await organizationStore.queryOrganizationHistory(params)
    } else if (queryType.value === 'employee') {
      result = await organizationStore.queryOrganizationEmployees(params)
    }
    
    queryResults.value = result.list
    total.value = result.total
    
    // 构建树形数据
    if (viewMode.value === 'tree' || viewMode.value === 'graph') {
      treeData.value = buildTreeData(result.list)
    }
    
    ElMessage.success('查询成功')
  } catch (__error) {
    console.error('查询失败:', error)
    ElMessage.error('查询失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 重置
const handleReset = () => {
  searchFormRef.value?.resetFields()
  Object.assign(searchForm, {
    institutionName: '',
    institutionType: [],
    status: '',
    establishDateRange: []
  })
  queryResults.value = []
  treeData.value = []
  total.value = 0
  currentPage.value = 1
}

// 导出
const handleExport = async () => {
  try {
    const columns = [
      { header: '机构编码', key: 'institutionCode' },
      { header: '机构名称', key: 'institutionName' },
      { header: '机构类型', key: 'institutionType', formatter: getTypeName },
      { header: '上级机构', key: 'parentInstitutionName' },
      { header: '状态', key: 'status', formatter: getStatusName },
      { header: '成立日期', key: 'establishDate', formatter: formatDate },
      { header: '负责人', key: 'leaderName' },
      { header: '联系电话', key: 'contactPhone' },
      { header: '邮箱', key: 'email' },
      { header: '办公地址', key: 'officeAddress' }
    ]
    
    await exportToExcel(queryResults.value, columns, '组织架构查询结果')
    ElMessage.success('导出成功')
  } catch (__error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请稍后重试')
  }
}

// 查询类型变更
const handleQueryTypeChange = () => {
  // 清空查询结果
  queryResults.value = []
  treeData.value = []
  total.value = 0
  currentPage.value = 1
}

// 视图切换
const handleViewChange = () => {
  if ((viewMode.value === 'tree' || viewMode.value === 'graph') && queryResults.value.length > 0) {
    treeData.value = buildTreeData(queryResults.value)
  }
}

// 分页
const handleSizeChange = () => {
  handleSearch()
}

const handleCurrentChange = () => {
  handleSearch()
}

// 节点点击
const handleNodeClick = (node: Organization) => {
  handleViewDetail(node)
}

// 行点击
const handleRowClick = (row: Organization) => {
  handleViewDetail(row)
}

// 查看详情
const handleViewDetail = (org: Organization) => {
  currentOrgId.value = org.institutionId
  detailVisible.value = true
}

// 查看历史
const handleViewHistory = (org: Organization) => {
  currentOrgId.value = org.institutionId
  historyVisible.value = true
}
</script>

<style lang="scss" scoped>
.organization-query {
  height: 100%;
  padding: 16px;
  overflow: auto;

  .search-card {
    margin-bottom: 16px;
  }

  .view-switch-card {
    margin-bottom: 16px;

    .view-switch {
      display: flex;
      align-items: center;

      .switch-label {
        margin-right: 16px;
        font-weight: 500;
      }
    }
  }

  .result-card {
    min-height: 400px;

    .tree-view,
    .graph-view {
      height: 600px;
      overflow: auto;
    }

    .list-view {
      .el-pagination {
        margin-top: 16px;
        text-align: right;
      }
    }
  }
}
</style>