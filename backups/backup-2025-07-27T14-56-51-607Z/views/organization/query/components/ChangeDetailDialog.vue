<template>
  <el-dialog
    v-model="visible"
    title="变更详情"
    width="700px"
    :before-close="handleClose"
  >
    <div v-if="changeRecord" class="change-detail">
      <!-- 基本信息 -->
      <el-descriptions :column="2" border class="mb-4">
        <el-descriptions-item label="变更类型">
          <el-tag :type="getChangeTypeTagType(changeRecord.changeType)">
            {{ getChangeTypeName(changeRecord.changeType) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="变更日期">
          {{ formatDateTime(changeRecord.changeDate) }}
        </el-descriptions-item>
        <el-descriptions-item label="操作人">
          {{ changeRecord.operator }}
        </el-descriptions-item>
        <el-descriptions-item label="审批人">
          {{ changeRecord.approver || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="变更说明" :span="2">
          {{ changeRecord.changeDescription }}
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">
          {{ changeRecord.remark || '-' }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 变更前后对比 -->
      <div v-if="changeRecord.beforeData || changeRecord.afterData" class="comparison">
        <div class="section-title">变更前后对比</div>
        <el-row :gutter="20">
          <!-- 变更前 -->
          <el-col :span="12">
            <div class="comparison-card before">
              <div class="card-header">变更前</div>
              <div class="card-content">
                <template v-if="changeRecord.beforeData">
                  <div
                    v-for="(value, key) in changeRecord.beforeData"
                    :key="key"
                    class="data-item"
                  >
                    <span class="label">{{ getFieldLabel(key) }}：</span>
                    <span class="value">{{ formatFieldValue(key, value) }}</span>
                  </div>
                </template>
                <el-empty v-else description="无数据" :image-size="60"  />
              </div>
            </div>
          </el-col>

          <!-- 变更后 -->
          <el-col :span="12">
            <div class="comparison-card after">
              <div class="card-header">变更后</div>
              <div class="card-content">
                <template v-if="changeRecord.afterData">
                  <div
                    v-for="(value, key) in changeRecord.afterData"
                    :key="key"
                    class="data-item"
                    :class="{ changed: hasChanged(key) }"
                  >
                    <span class="label">{{ getFieldLabel(key) }}：</span>
                    <span class="value">{{ formatFieldValue(key, value) }}</span>
                  </div>
                </template>
                <el-empty v-else description="无数据" :image-size="60"  />
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 附件信息 -->
      <div v-if="changeRecord.attachments && changeRecord.attachments.length > 0" class="attachments">
        <div class="section-title">相关附件</div>
        <el-table :data="changeRecord.attachments" stripe size="small">
          <el-table-column prop="fileName" label="文件名"  />
          <el-table-column prop="fileSize" label="文件大小" width="100">
            <template #default="{ row }">
              {{ formatFileSize(row.fileSize) }}
            </template>
          </el-table-column>
          <el-table-column prop="uploadTime" label="上传时间" width="150">
            <template #default="{ row }">
              {{ formatDateTime(row.uploadTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="{ row }">
              <el-button type="primary" size="small" text @click="handleDownload(row)">
                下载
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'ChangeDetailDialog'
})
 
import { computed } from 'vue'
import type { OrganizationChange } from '@/types/organization'

// props & emits
const props = defineProps<{
  modelValue: boolean
  changeRecord: OrganizationChange | null
}>()

const emits = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emits('update:modelValue', value)
})

// 字段映射
const fieldLabelMap: Record<string, string> = {
  institutionName: '机构名称',
  institutionCode: '机构编码',
  institutionType: '机构类型',
  parentInstitutionId: '上级机构',
  establishDate: '成立日期',
  effectiveDate: '生效日期',
  status: '机构状态',
  leaderName: '负责人',
  contactPhone: '联系电话',
  email: '邮箱',
  officeAddress: '办公地址',
  description: '职责描述'
}

// 方法
const getChangeTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    CREATE: '新建',
    UPDATE: '修改',
    DELETE: '删除',
    MOVE: '移动',
    MERGE: '合并',
    SPLIT: '拆分'
  }
  return typeMap[type] || type
}

const getChangeTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    CREATE: 'success',
    UPDATE: 'primary',
    DELETE: 'danger',
    MOVE: 'warning',
    MERGE: 'info',
    SPLIT: 'warning'
  }
  return typeMap[type] || ''
}

const formatDateTime = (date: string | undefined) => {
  if (!date) return '-'
  return new Date(date).toLocaleString('zh-CN')
}

const getFieldLabel = (field: string) => {
  return fieldLabelMap[field] || field
}

   
const formatFieldValue = (field: string, value: unknown) => {
  if (value === null || value === undefined || value === '') {
    return '-'
  }
  
  // 特殊字段处理
  if (field === 'establishDate' || field === 'effectiveDate') {
    return new Date(value).toLocaleDateString('zh-CN')
  }
  
  if (field === 'institutionType') {
    const typeMap: Record<string, string> = {
      SCHOOL: '学校',
      COLLEGE: '学院',
      DEPARTMENT: '部门',
      TEACHING_DEPT: '教学部',
      RESEARCH_INST: '科研机构',
      ADMIN_DEPT: '行政机构',
      DIRECT_UNIT: '直属单位',
      TEMP_ORG: '临时组织'
    }
    return typeMap[value] || value
  }
  
  if (field === 'status') {
    const statusMap: Record<string, string> = {
      ACTIVE: '正常',
      CANCELLED: '已撤销',
      MERGED: '已合并',
      DISABLED: '已停用'
    }
    return statusMap[value] || value
  }
  
  return value
}

const hasChanged = (field: string) => {
  if (!props.changeRecord?.beforeData || !props.changeRecord?.afterData) {
    return false
  }
  
  const before = props.changeRecord.beforeData[field]
  const after = props.changeRecord.afterData[field]
  
  return before !== after
}

const formatFileSize = (size: number) => {
  if (size < 1024) {
    return size + ' B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB'
  } else {
    return (size / (1024 * 1024)).toFixed(2) + ' MB'
  }
}

   
const handleDownload = (file: unknown) => {
  // 实际项目中需要调用下载接口
  console.log('下载文件:', file)
}

const handleClose = () => {
  visible.value = false
}
</script>

<style lang="scss" scoped>
.change-detail {
  .mb-4 {
    margin-bottom: 16px;
  }

  .section-title {
    margin: 20px 0 10px;
    font-size: 16px;
    font-weight: 500;
    color: #303133;
  }

  .comparison {
    margin-top: 20px;

    .comparison-card {
      border: 1px solid #e4e7ed;
      border-radius: 4px;

      .card-header {
        padding: 10px 16px;
        font-weight: 500;
        border-bottom: 1px solid #e4e7ed;
      }

      .card-content {
        padding: 16px;
        min-height: 200px;

        .data-item {
          margin-bottom: 8px;
          line-height: 1.5;

          .label {
            color: #909399;
          }

          .value {
            color: #303133;
          }

          &.changed {
            .value {
              color: #f56c6c;
              font-weight: 500;
            }
          }
        }
      }

      &.before {
        .card-header {
          background-color: #f5f7fa;
        }
      }

      &.after {
        .card-header {
          background-color: #ecf5ff;
          color: #409eff;
        }
      }
    }
  }

  .attachments {
    margin-top: 20px;
  }
}
</style>