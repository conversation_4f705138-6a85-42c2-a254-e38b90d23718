<template>
  <el-dialog
    v-model="visible"
    title="机构详情"
    width="800px"
    :before-close="handleClose"
  >
    <div v-loading="loading" class="detail-content">
      <template v-if="organization">
        <!-- 基本信息 -->
        <el-descriptions :column="2" border class="mb-4">
          <el-descriptions-item label="机构名称" :span="2">
            {{ organization.institutionName }}
          </el-descriptions-item>
          <el-descriptions-item label="机构编码">
            {{ organization.institutionCode }}
          </el-descriptions-item>
          <el-descriptions-item label="机构类型">
            <el-tag :type="getTypeTagType(organization.institutionType)">
              {{ getTypeName(organization.institutionType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="上级机构">
            {{ organization.parentInstitutionName || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="机构状态">
            <el-tag :type="getStatusTagType(organization.status)">
              {{ getStatusName(organization.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="成立日期">
            {{ formatDate(organization.establishDate) }}
          </el-descriptions-item>
          <el-descriptions-item label="生效日期">
            {{ formatDate(organization.effectiveDate) }}
          </el-descriptions-item>
          <el-descriptions-item label="负责人">
            {{ organization.leaderName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="联系电话">
            {{ organization.contactPhone || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="邮箱" :span="2">
            {{ organization.email || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="办公地址" :span="2">
            {{ organization.officeAddress || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="职责描述" :span="2">
            {{ organization.description || '-' }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 编制信息 -->
        <div class="section-title">编制信息</div>
        <el-descriptions :column="3" border class="mb-4">
          <el-descriptions-item label="编制数">
            {{ establishment.establishmentCount || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="实际人数">
            {{ establishment.actualCount || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="空缺数">
            {{ (establishment.establishmentCount || 0) - (establishment.actualCount || 0) }}
          </el-descriptions-item>
          <el-descriptions-item label="管理岗位">
            {{ establishment.managementCount || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="专技岗位">
            {{ establishment.professionalCount || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="工勤岗位">
            {{ establishment.supportCount || 0 }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 统计信息 -->
        <div class="section-title">统计信息</div>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="下级机构数" :value="statistics.childrenCount || 0"  />
          </el-col>
          <el-col :span="6">
            <el-statistic title="员工总数" :value="statistics.employeeCount || 0"  />
          </el-col>
          <el-col :span="6">
            <el-statistic title="在职人数" :value="statistics.activeEmployeeCount || 0"  />
          </el-col>
          <el-col :span="6">
            <el-statistic title="岗位数" :value="statistics.positionCount || 0"  />
          </el-col>
        </el-row>

        <!-- 下属机构 -->
        <div class="section-title mt-4" v-if="children.length > 0">
          下属机构 ({{ children.length }})
        </div>
        <el-table
          v-if="children.length > 0"
          :data="children"
          stripe
          size="small"
          max-height="200"
        >
          <el-table-column prop="institutionCode" label="机构编码" width="120"  />
          <el-table-column prop="institutionName" label="机构名称"  />
          <el-table-column prop="institutionType" label="机构类型" width="120">
            <template #default="{ row }">
              {{ getTypeName(row.institutionType) }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              {{ getStatusName(row.status) }}
            </template>
          </el-table-column>
        </el-table>
      </template>
    </div>

    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" :icon="Download" @click="handleExport">
        导出详情
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'OrganizationDetailDialog'
})
 
import { ref, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Download } from '@element-plus/icons-vue'
import { useOrganizationStore } from '@/stores/modules/organization'
import type { Organization, OrganizationStatistics, Establishment } from '@/types/organization'
import { organizationTypeOptions } from '@/types/organization'
import { exportToExcel } from '@/utils/export'

// props & emits
const props = defineProps<{
  modelValue: boolean
  organizationId: string
}>()

const emits = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// store
const organizationStore = useOrganizationStore()

// 状态
const loading = ref(false)
const organization = ref<Organization | null>(null)
const establishment = ref<Partial<Establishment>>({})
const statistics = ref<Partial<OrganizationStatistics>>({})
const children = ref<Organization[]>([])

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emits('update:modelValue', value)
})

// 方法
const getTypeName = (type: string) => {
  const item = organizationTypeOptions.find(i => i.value === type)
  return item?.label || type
}

const getTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    SCHOOL: '',
    COLLEGE: 'success',
    DEPARTMENT: 'warning',
    TEACHING_DEPT: 'danger',
    RESEARCH_INST: 'primary',
    ADMIN_DEPT: 'info',
    DIRECT_UNIT: 'warning',
    TEMP_ORG: 'info'
  }
  return typeMap[type] || ''
}

const getStatusName = (status: string) => {
  const statusMap: Record<string, string> = {
    ACTIVE: '正常',
    CANCELLED: '已撤销',
    MERGED: '已合并',
    DISABLED: '已停用'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    ACTIVE: 'success',
    CANCELLED: 'danger',
    MERGED: 'warning',
    DISABLED: 'info'
  }
  return statusMap[status] || 'info'
}

const formatDate = (date: string | undefined) => {
  if (!date) return '-'
  return new Date(date).toLocaleDateString('zh-CN')
}

// 加载详情
const loadDetail = async () => {
  if (!props.organizationId) return
  
  loading.value = true
  
  try {
    // 加载机构详情
    organization.value = await organizationStore.getOrganizationDetail(props.organizationId)
    
    // 加载编制信息
    establishment.value = await organizationStore.getOrganizationEstablishment(props.organizationId)
    
    // 加载统计信息
    statistics.value = await organizationStore.getOrganizationStatistics(props.organizationId)
    
    // 加载下属机构
    children.value = await organizationStore.getOrganizationChildren(props.organizationId)
  } catch (__error) {
    console.error('加载详情失败:', error)
    ElMessage.error('加载详情失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 导出
const handleExport = async () => {
  if (!organization.value) return
  
  try {
    const data = {
      基本信息: {
        机构名称: organization.value.institutionName,
        机构编码: organization.value.institutionCode,
        机构类型: getTypeName(organization.value.institutionType),
        上级机构: organization.value.parentInstitutionName || '无',
        机构状态: getStatusName(organization.value.status),
        成立日期: formatDate(organization.value.establishDate),
        生效日期: formatDate(organization.value.effectiveDate),
        负责人: organization.value.leaderName || '-',
        联系电话: organization.value.contactPhone || '-',
        邮箱: organization.value.email || '-',
        办公地址: organization.value.officeAddress || '-',
        职责描述: organization.value.description || '-'
      },
      编制信息: {
        编制数: establishment.value.establishmentCount || 0,
        实际人数: establishment.value.actualCount || 0,
        空缺数: (establishment.value.establishmentCount || 0) - (establishment.value.actualCount || 0),
        管理岗位: establishment.value.managementCount || 0,
        专技岗位: establishment.value.professionalCount || 0,
        工勤岗位: establishment.value.supportCount || 0
      },
      统计信息: {
        下级机构数: statistics.value.childrenCount || 0,
        员工总数: statistics.value.employeeCount || 0,
        在职人数: statistics.value.activeEmployeeCount || 0,
        岗位数: statistics.value.positionCount || 0
      }
    }
    
    // 转换为表格数据
   
    const tableData: unknown[] = []
    Object.entries(data).forEach(([section, items]) => {
      tableData.push({ section, key: '', value: '' })
      Object.entries(items).forEach(([key, value]) => {
        tableData.push({ section: '', key, value })
      })
    })
    
    const columns = [
      { header: '分类', key: 'section' },
      { header: '项目', key: 'key' },
      { header: '内容', key: 'value' }
    ]
    
    await exportToExcel(tableData, columns, `${organization.value.institutionName}_详情`)
    ElMessage.success('导出成功')
  } catch (__error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请稍后重试')
  }
}

// 关闭
const handleClose = () => {
  visible.value = false
}

// 监听
watch(() => props.organizationId, () => {
  if (props.organizationId && visible.value) {
    loadDetail()
  }
})

watch(visible, (val) => {
  if (val && props.organizationId) {
    loadDetail()
  }
})
</script>

<style lang="scss" scoped>
.detail-content {
  min-height: 400px;

  .section-title {
    margin: 20px 0 10px;
    font-size: 16px;
    font-weight: 500;
    color: #303133;
  }

  .mb-4 {
    margin-bottom: 16px;
  }

  .mt-4 {
    margin-top: 16px;
  }
}
</style>