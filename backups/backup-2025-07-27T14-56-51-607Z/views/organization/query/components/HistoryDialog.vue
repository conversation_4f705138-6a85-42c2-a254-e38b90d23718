<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="900px"
    :before-close="handleClose"
  >
    <div class="history-content">
      <!-- 查询条件 -->
      <el-form :inline="true" class="search-form">
        <el-form-item label="变更类型" v-if="queryType === 'history'">
          <el-select v-model="changeType" placeholder="全部" clearable>
            <el-option label="新建" value="CREATE"  />
            <el-option label="修改" value="UPDATE"  />
            <el-option label="删除" value="DELETE"  />
            <el-option label="移动" value="MOVE"  />
            <el-option label="合并" value="MERGE"  />
            <el-option label="拆分" value="SPLIT"  />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
           />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadHistory">查询</el-button>
        </el-form-item>
      </el-form>

      <!-- 历史记录列表 -->
      <el-table
        v-loading="loading"
        :data="historyList"
        stripe
        border
        max-height="500"
      >
        <!-- 变更历史列 -->
        <template v-if="queryType === 'history'">
          <el-table-column prop="changeType" label="变更类型" width="100">
            <template #default="{ row }">
              <el-tag :type="getChangeTypeTagType(row.changeType)">
                {{ getChangeTypeName(row.changeType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="changeDescription" label="变更说明" min-width="200"  />
          <el-table-column prop="changeDate" label="变更日期" width="150">
            <template #default="{ row }">
              {{ formatDateTime(row.changeDate) }}
            </template>
          </el-table-column>
          <el-table-column prop="operator" label="操作人" width="100"  />
          <el-table-column prop="remark" label="备注" min-width="150"  />
          <el-table-column label="变更详情" width="150">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                text
                @click="handleViewChangeDetail(row)"
              >
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </template>

        <!-- 人员分布列 -->
        <template v-else-if="queryType === 'employee'">
          <el-table-column prop="employeeId" label="工号" width="100"  />
          <el-table-column prop="name" label="姓名" width="100"  />
          <el-table-column prop="positionName" label="岗位" width="150"  />
          <el-table-column prop="employmentType" label="用工形式" width="100">
            <template #default="{ row }">
              {{ getEmploymentTypeName(row.employmentType) }}
            </template>
          </el-table-column>
          <el-table-column prop="joinDate" label="入职日期" width="120">
            <template #default="{ row }">
              {{ formatDate(row.joinDate) }}
            </template>
          </el-table-column>
          <el-table-column prop="contractEndDate" label="合同到期" width="120">
            <template #default="{ row }">
              {{ formatDate(row.contractEndDate) }}
            </template>
          </el-table-column>
          <el-table-column prop="phone" label="联系电话" width="120"  />
          <el-table-column prop="email" label="邮箱" min-width="150"  />
        </template>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
    </div>

    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" :icon="Download" @click="handleExport">
        导出记录
      </el-button>
    </template>

    <!-- 变更详情对话框 -->
    <ChangeDetailDialog
      v-model="changeDetailVisible"
      :change-record="currentChangeRecord"
    />
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'HistoryDialog'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Download } from '@element-plus/icons-vue'
import { useOrganizationStore } from '@/stores/modules/organization'
import ChangeDetailDialog from './ChangeDetailDialog.vue'
import type { OrganizationChange } from '@/types/organization'
import { exportToExcel } from '@/utils/export'

// props & emits
const props = defineProps<{
  modelValue: boolean
  organizationId: string
  queryType: string
}>()

const emits = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// store
const organizationStore = useOrganizationStore()

// 状态
const loading = ref(false)
const historyList = ref<any[]>([])
const changeType = ref('')
const dateRange = ref<string[]>([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const changeDetailVisible = ref(false)
const currentChangeRecord = ref<OrganizationChange | null>(null)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emits('update:modelValue', value)
})

const dialogTitle = computed(() => {
  return props.queryType === 'history' ? '变更历史' : '人员分布'
})

// 方法
const getChangeTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    CREATE: '新建',
    UPDATE: '修改',
    DELETE: '删除',
    MOVE: '移动',
    MERGE: '合并',
    SPLIT: '拆分'
  }
  return typeMap[type] || type
}

const getChangeTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    CREATE: 'success',
    UPDATE: 'primary',
    DELETE: 'danger',
    MOVE: 'warning',
    MERGE: 'info',
    SPLIT: 'warning'
  }
  return typeMap[type] || ''
}

const getEmploymentTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    FORMAL: '正式编制',
    CONTRACT: '合同制',
    TEMPORARY: '临时工',
    OUTSOURCED: '外包',
    INTERN: '实习生',
    RETIRED_REHIRE: '退休返聘'
  }
  return typeMap[type] || type
}

const formatDate = (date: string | undefined) => {
  if (!date) return '-'
  return new Date(date).toLocaleDateString('zh-CN')
}

const formatDateTime = (date: string | undefined) => {
  if (!date) return '-'
  return new Date(date).toLocaleString('zh-CN')
}

// 加载历史记录
const loadHistory = async () => {
  if (!props.organizationId) return
  
  loading.value = true
  
  try {
   
    const params: unknown = {
      organizationId: props.organizationId,
      page: currentPage.value,
      size: pageSize.value
    }
    
    if (changeType.value) {
      params.changeType = changeType.value
    }
    
    if (dateRange.value && dateRange.value.length === 2) {
      params.startDate = dateRange.value[0]
      params.endDate = dateRange.value[1]
    }
    
    let result
    if (props.queryType === 'history') {
      result = await organizationStore.getOrganizationChanges(props.organizationId, params)
    } else if (props.queryType === 'employee') {
      result = await organizationStore.getOrganizationEmployees(props.organizationId, params)
    }
    
    historyList.value = result.list
    total.value = result.total
  } catch (__error) {
    console.error('加载记录失败:', error)
    ElMessage.error('加载记录失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 查看变更详情
const handleViewChangeDetail = (record: OrganizationChange) => {
  currentChangeRecord.value = record
  changeDetailVisible.value = true
}

// 分页
const handleSizeChange = () => {
  loadHistory()
}

const handleCurrentChange = () => {
  loadHistory()
}

// 导出
const handleExport = async () => {
  try {
   
    let columns: unknown[] = []
    let filename = ''
    
    if (props.queryType === 'history') {
      columns = [
        { header: '变更类型', key: 'changeType', formatter: getChangeTypeName },
        { header: '变更说明', key: 'changeDescription' },
        { header: '变更日期', key: 'changeDate', formatter: formatDateTime },
        { header: '操作人', key: 'operator' },
        { header: '备注', key: 'remark' }
      ]
      filename = '组织变更历史'
    } else if (props.queryType === 'employee') {
      columns = [
        { header: '工号', key: 'employeeId' },
        { header: '姓名', key: 'name' },
        { header: '岗位', key: 'positionName' },
        { header: '用工形式', key: 'employmentType', formatter: getEmploymentTypeName },
        { header: '入职日期', key: 'joinDate', formatter: formatDate },
        { header: '合同到期', key: 'contractEndDate', formatter: formatDate },
        { header: '联系电话', key: 'phone' },
        { header: '邮箱', key: 'email' }
      ]
      filename = '组织人员分布'
    }
    
    await exportToExcel(historyList.value, columns, filename)
    ElMessage.success('导出成功')
  } catch (__error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请稍后重试')
  }
}

// 关闭
const handleClose = () => {
  visible.value = false
}

// 监听
watch(() => props.organizationId, () => {
  if (props.organizationId && visible.value) {
    loadHistory()
  }
})

watch(visible, (val) => {
  if (val && props.organizationId) {
    loadHistory()
  }
})
</script>

<style lang="scss" scoped>
.history-content {
  .search-form {
    margin-bottom: 16px;
    padding: 16px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }

  .el-pagination {
    margin-top: 16px;
    text-align: right;
  }
}
</style>