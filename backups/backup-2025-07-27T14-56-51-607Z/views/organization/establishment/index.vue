<template>
  <div class="establishment-management">
    <!-- 顶部统计卡片 -->
    <el-row :gutter="20" class="stat-cards">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card>
          <el-statistic title="总编制数" :value="establishmentStore.totalEstablishment">
            <template #suffix>人</template>
          </el-statistic>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <el-card>
          <el-statistic 
            title="实际在编数" 
            :value="establishmentStore.totalActual"
            :value-style="{ color: '#409eff' }"
          >
            <template #suffix>人</template>
          </el-statistic>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <el-card>
          <el-statistic 
            title="超编数" 
            :value="establishmentStore.totalOverstaffed"
            :value-style="{ color: establishmentStore.totalOverstaffed > 0 ? '#f56c6c' : '#67c23a' }"
          >
            <template #suffix>人</template>
          </el-statistic>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <el-card>
          <el-statistic 
            title="编制使用率" 
            :value="establishmentStore.overallUtilizationRate"
            :value-style="{ 
              color: establishmentStore.overallUtilizationRate > 100 ? '#f56c6c' : 
                     establishmentStore.overallUtilizationRate > 90 ? '#e6a23c' : '#67c23a' 
            }"
          >
            <template #suffix>%</template>
          </el-statistic>
        </el-card>
      </el-col>
    </el-row>

    <!-- 标签页 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <!-- 编制规划 -->
      <el-tab-pane label="编制规划" name="plan">
        <div class="tab-content">
          <!-- 搜索栏 -->
          <div class="search-bar">
            <el-form :inline="true" :model="searchForm" @submit.prevent="handleSearch">
              <el-form-item label="年度">
                <el-select v-model="searchForm.planYear" placeholder="请选择年度" clearable>
                  <el-option 
                    v-for="year in yearOptions" 
                    :key="year" 
                    :label="`${year}年`" 
                    :value="year" 
                   />
                </el-select>
              </el-form-item>
              <el-form-item label="机构">
                <HrOrgPicker 
                  v-model="searchForm.institutionId" 
                  placeholder="请选择机构" 
                  clearable
                />
              </el-form-item>
              <el-form-item label="审批状态">
                <el-select v-model="searchForm.approvalStatus" placeholder="请选择状态" clearable>
                  <el-option 
                    v-for="item in approvalStatusOptions" 
                    :key="item.value" 
                    :label="item.label" 
                    :value="item.value" 
                   />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleSearch">查询</el-button>
                <el-button @click="handleReset">重置</el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 工具栏 -->
          <div class="toolbar">
            <el-button type="primary" @click="handleAddPlan">
              <el-icon><Plus /></el-icon>
              新增规划
            </el-button>
            <el-button @click="handleImport">
              <el-icon><Upload /></el-icon>
              批量导入
            </el-button>
            <el-button @click="handleExport">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
            <el-button @click="handleDownloadTemplate">
              <el-icon><Document /></el-icon>
              下载模板
            </el-button>
          </div>

          <!-- 数据表格 -->
          <el-table 
            v-loading="establishmentStore.loading" 
            :data="establishmentStore.plans" 
            border
          >
            <el-table-column prop="planYear" label="年度" width="80"  />
            <el-table-column prop="planName" label="规划名称" min-width="150"  />
            <el-table-column prop="institutionName" label="机构" min-width="150">
              <template #default="{ row }">
                {{ row.institutionName || '全校' }}
              </template>
            </el-table-column>
            <el-table-column prop="positionCategory" label="岗位类别" width="120">
              <template #default="{ row }">
                <span v-if="row.positionCategory">
                  {{ getEstablishmentTypeLabel(row.positionCategory) }}
                </span>
                <span v-else>总编制</span>
              </template>
            </el-table-column>
            <el-table-column prop="approvedCount" label="核定编制数" width="100" align="right"  />
            <el-table-column prop="budgetedCount" label="预算编制数" width="100" align="right"  />
            <el-table-column prop="effectiveDate" label="生效日期" width="110"  />
            <el-table-column prop="approvalStatus" label="审批状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getApprovalStatusType(row.approvalStatus)">
                  {{ getApprovalStatusLabel(row.approvalStatus) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button link type="primary" @click="handleView(row)">查看</el-button>
                <el-button 
                  v-if="row.approvalStatus === 'DRAFT'" 
                  link 
                  type="primary" 
                  @click="handleEdit(row)"
                >
                  编辑
                </el-button>
                <el-button 
                  v-if="row.approvalStatus === 'DRAFT'" 
                  link 
                  type="primary" 
                  @click="handleSubmit(row)"
                >
                  提交
                </el-button>
                <el-button 
                  v-if="row.approvalStatus === 'DRAFT'" 
                  link 
                  type="danger" 
                  @click="handleDelete(row)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="establishmentStore.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
           />
        </div>
      </el-tab-pane>

      <!-- 编制统计 -->
      <el-tab-pane label="编制统计" name="statistics">
        <EstablishmentStatistics />
      </el-tab-pane>

      <!-- 编制调整 -->
      <el-tab-pane label="编制调整" name="adjustment">
        <EstablishmentAdjustment />
      </el-tab-pane>

      <!-- 编制报表 -->
      <el-tab-pane label="编制报表" name="report">
        <EstablishmentReport />
      </el-tab-pane>

      <!-- 编制预警 -->
      <el-tab-pane label="编制预警" name="warning">
        <EstablishmentWarning />
      </el-tab-pane>
    </el-tabs>

    <!-- 编制规划编辑对话框 -->
    <EstablishmentPlanDialog
      v-model="planDialogVisible"
      :plan-id="currentPlanId"
      :mode="dialogMode"
      @success="handlePlanSuccess"
    />

    <!-- 导入对话框 -->
    <HrImportDialog
      v-model="importDialogVisible"
      title="批量导入编制规划"
      template-name="编制规划"
      :upload-api="handleUpload"
      :template-api="downloadTemplate"
      @success="handleImportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
 
defineOptions({
  name: 'EstablishmentPage'
})

import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Upload, Download, Document } from '@element-plus/icons-vue'
import { useEstablishmentStore } from '@/stores/modules/establishment'
import { 
  approvalStatusOptions, 
  establishmentTypeOptions,
  EstablishmentApprovalStatus,
  EstablishmentType
} from '@/types/establishment'
import HrOrgPicker from '@/components/organization/HrOrgPicker.vue'
import HrImportDialog from '@/components/common/HrImportDialog.vue'
import EstablishmentPlanDialog from './components/EstablishmentPlanDialog.vue'
import EstablishmentStatistics from './components/EstablishmentStatistics.vue'
import EstablishmentAdjustment from './components/EstablishmentAdjustment.vue'
import EstablishmentReport from './components/EstablishmentReport.vue'
import EstablishmentWarning from './components/EstablishmentWarning.vue'

// 状态管理
const establishmentStore = useEstablishmentStore()

// 响应式数据
const activeTab = ref('plan')
const planDialogVisible = ref(false)
const importDialogVisible = ref(false)
const dialogMode = ref<'view' | 'create' | 'edit'>('create')
const currentPlanId = ref('')
const currentPage = ref(1)
const pageSize = ref(20)

// 搜索表单
const searchForm = reactive({
  planYear: new Date().getFullYear(),
  institutionId: '',
  approvalStatus: '' as EstablishmentApprovalStatus | ''
})

// 年度选项（当前年份前后5年）
const yearOptions = computed(() => {
  const currentYear = new Date().getFullYear()
  const years = []
  for (let i = currentYear - 5; i <= currentYear + 5; i++) {
    years.push(i)
  }
  return years
})

// 获取编制类型标签
const getEstablishmentTypeLabel = (type: EstablishmentType) => {
  const option = establishmentTypeOptions.find(item => item.value === type)
  return option?.label || type
}

// 获取审批状态标签
const getApprovalStatusLabel = (status: EstablishmentApprovalStatus) => {
  const option = approvalStatusOptions.find(item => item.value === status)
  return option?.label || status
}

// 获取审批状态类型
const getApprovalStatusType = (status: EstablishmentApprovalStatus) => {
  const typeMap: Record<EstablishmentApprovalStatus, string> = {
    DRAFT: 'info',
    PENDING: 'warning',
    APPROVED: 'success',
    REJECTED: 'danger'
  }
  return typeMap[status] || ''
}

// 标签页切换
const handleTabClick = () => {
  // 根据不同标签页加载对应数据
  if (activeTab.value === 'plan') {
    fetchPlans()
  }
}

// 获取编制规划列表
const fetchPlans = async () => {
  await establishmentStore.fetchPlans({
    ...searchForm,
    pageNum: currentPage.value,
    pageSize: pageSize.value
  })
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchPlans()
}

// 重置
const handleReset = () => {
  searchForm.planYear = new Date().getFullYear()
  searchForm.institutionId = ''
  searchForm.approvalStatus = ''
  handleSearch()
}

// 新增规划
const handleAddPlan = () => {
  dialogMode.value = 'create'
  currentPlanId.value = ''
  planDialogVisible.value = true
}

// 查看
   
const handleView = (row: unknown) => {
  dialogMode.value = 'view'
  currentPlanId.value = row.planId
  planDialogVisible.value = true
}

// 编辑
   
const handleEdit = (row: unknown) => {
  dialogMode.value = 'edit'
  currentPlanId.value = row.planId
  planDialogVisible.value = true
}

// 提交审批
   
const handleSubmit = async (row: unknown) => {
  try {
    await ElMessageBox.confirm(
      '确定要提交该编制规划进行审批吗？提交后将无法修改。',
      '提交确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await establishmentStore.submitForApproval(row.planId)
    ElMessage.success('提交成功')
    fetchPlans()
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('提交失败:', error)
    }
  }
}

// 删除
   
const handleDelete = async (row: unknown) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除该编制规划吗？删除后无法恢复。',
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await establishmentStore.deletePlan(row.planId)
    ElMessage.success('删除成功')
    fetchPlans()
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
    }
  }
}

// 导入
const handleImport = () => {
  importDialogVisible.value = true
}

// 处理文件上传
const handleUpload = async (formData: FormData) => {
  return establishmentStore.importPlans(formData.get('file') as File)
}

// 下载模板
const downloadTemplate = async () => {
  return establishmentStore.downloadTemplate('PLAN')
}

// 导出
const handleExport = () => {
  establishmentStore.exportPlans(searchForm)
}

// 下载模板
const handleDownloadTemplate = () => {
  establishmentStore.downloadTemplate('PLAN')
}

// 编制规划保存成功
const handlePlanSuccess = () => {
  fetchPlans()
}

// 导入成功
const handleImportSuccess = () => {
  fetchPlans()
  // 刷新统计数据
  establishmentStore.fetchStatistics()
}

// 分页大小变更
const handleSizeChange = () => {
  currentPage.value = 1
  fetchPlans()
}

// 页码变更
const handleCurrentChange = () => {
  fetchPlans()
}

// 组件挂载
onMounted(() => {
  fetchPlans()
  establishmentStore.fetchStatistics()
})
</script>

<style lang="scss" scoped>
.establishment-management {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100%;

  .stat-cards {
    margin-bottom: 20px;

    .el-card {
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }

    .el-statistic {
      text-align: center;
    }
  }

  :deep(.el-tabs) {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .tab-content {
    .search-bar {
      margin-bottom: 20px;
    }

    .toolbar {
      margin-bottom: 20px;
      display: flex;
      gap: 10px;
    }

    .el-table {
      margin-bottom: 20px;
    }
  }
}
</style>