<template>
  <div class="establishment-warning">
    <!-- 预警规则配置 -->
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>预警规则配置</span>
          <el-button type="primary" size="small" @click="handleAddRule">
            <el-icon><Plus /></el-icon>
            新增规则
          </el-button>
        </div>
      </template>

      <el-table :data="warningRules" border>
        <el-table-column prop="ruleName" label="规则名称" min-width="150"  />
        <el-table-column prop="warningType" label="预警类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getWarningTypeTag(row.warningType)">
              {{ getWarningTypeLabel(row.warningType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="triggerCondition" label="触发条件" min-width="200">
          <template #default="{ row }">
            <span>{{ formatTriggerCondition(row) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="warningLevel" label="预警级别" width="100">
          <template #default="{ row }">
            <el-tag :type="getWarningLevelTag(row.warningLevel)" size="small">
              {{ row.warningLevel }}级
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              active-value="ACTIVE"
              inactive-value="INACTIVE"
              @change="handleStatusChange(row)"
             />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleEditRule(row)">编辑</el-button>
            <el-button link type="danger" @click="handleDeleteRule(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 当前预警 -->
    <el-card class="warning-card">
      <template #header>
        <div class="card-header">
          <span>当前预警</span>
          <div class="header-actions">
            <el-badge :value="activeWarnings.length" :hidden="activeWarnings.length === 0">
              <el-button size="small" @click="handleRefresh">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </el-badge>
            <el-button size="small" @click="handleExportWarnings">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
          </div>
        </div>
      </template>

      <el-alert
        v-if="activeWarnings.length > 0"
        :title="`当前有 ${activeWarnings.length} 条编制预警需要处理`"
        type="warning"
        show-icon
        :closable="false"
        style="margin-bottom: 20px"
      />

      <el-table 
        :data="activeWarnings" 
        v-loading="loading"
        border
        row-class-name="warning-row"
      >
        <el-table-column type="expand">
          <template #default="{ row }">
            <div class="warning-detail">
              <el-descriptions :column="3" border>
                <el-descriptions-item label="预警编号">{{ row.warningId }}</el-descriptions-item>
                <el-descriptions-item label="预警时间">{{ row.warningTime }}</el-descriptions-item>
                <el-descriptions-item label="规则名称">{{ row.ruleName }}</el-descriptions-item>
                <el-descriptions-item label="当前状态" :span="3">
                  <el-tag :type="row.handled ? 'success' : 'warning'">
                    {{ row.handled ? '已处理' : '待处理' }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="详细说明" :span="3">
                  {{ row.description }}
                </el-descriptions-item>
                <el-descriptions-item label="处理建议" :span="3">
                  {{ row.suggestion }}
                </el-descriptions-item>
                <el-descriptions-item v-if="row.handled" label="处理人">
                  {{ row.handler }}
                </el-descriptions-item>
                <el-descriptions-item v-if="row.handled" label="处理时间">
                  {{ row.handleTime }}
                </el-descriptions-item>
                <el-descriptions-item v-if="row.handled" label="处理结果">
                  {{ row.handleResult }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="institutionName" label="机构名称" min-width="150"  />
        <el-table-column prop="warningType" label="预警类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getWarningTypeTag(row.warningType)">
              {{ getWarningTypeLabel(row.warningType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="positionCategory" label="岗位类别" width="100"  />
        <el-table-column label="编制情况" width="200">
          <template #default="{ row }">
            <div class="establishment-info-inline">
              <span>核定：{{ row.approved }}</span>
              <el-divider direction="vertical"   />
              <span>在编：{{ row.actual }}</span>
              <el-divider direction="vertical"   />
              <span :class="getRateClass(row.utilizationRate)">
                使用率：{{ row.utilizationRate }}%
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="warningLevel" label="预警级别" width="90">
          <template #default="{ row }">
            <el-tag :type="getWarningLevelTag(row.warningLevel)" size="small">
              {{ row.warningLevel }}级
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="handled" label="处理状态" width="90">
          <template #default="{ row }">
            <el-tag :type="row.handled ? 'success' : 'warning'" size="small">
              {{ row.handled ? '已处理' : '待处理' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button 
              v-if="!row.handled" 
              link 
              type="primary" 
              @click="handleWarning(row)"
            >
              处理
            </el-button>
            <el-button link type="primary" @click="handleViewHistory(row)">
              历史
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="fetchWarnings"
        @current-change="fetchWarnings"
       />
    </el-card>

    <!-- 预警处理对话框 -->
    <el-dialog
      v-model="handleDialogVisible"
      title="预警处理"
      width="600px"
      append-to-body
    >
      <el-form
        ref="handleFormRef"
        :model="handleForm"
        :rules="handleFormRules"
        label-width="100px"
      >
        <el-form-item label="预警信息">
          <el-descriptions :column="2" size="small">
            <el-descriptions-item label="机构">
              {{ currentWarning?.institutionName }}
            </el-descriptions-item>
            <el-descriptions-item label="类型">
              {{ getWarningTypeLabel(currentWarning?.warningType) }}
            </el-descriptions-item>
            <el-descriptions-item label="编制情况" :span="2">
              核定：{{ currentWarning?.approved }}，
              在编：{{ currentWarning?.actual }}，
              使用率：{{ currentWarning?.utilizationRate }}%
            </el-descriptions-item>
          </el-descriptions>
        </el-form-item>
        
        <el-form-item label="处理方式" prop="handleType">
          <el-radio-group v-model="handleForm.handleType">
            <el-radio value="ADJUST">申请编制调整</el-radio>
            <el-radio value="OPTIMIZE">优化人员配置</el-radio>
            <el-radio value="IGNORE">暂不处理</el-radio>
            <el-radio value="OTHER">其他</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="处理说明" prop="handleResult">
          <el-input
            v-model="handleForm.handleResult"
            type="textarea"
            :rows="4"
            placeholder="请输入处理说明"
            maxlength="500"
            show-word-limit
            />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="handleForm.remark"
            type="textarea"
            :rows="2"
            placeholder="选填"
            maxlength="200"
            show-word-limit
            />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="handleDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitHandle">确定</el-button>
      </template>
    </el-dialog>

    <!-- 规则配置对话框 -->
    <WarningRuleDialog
      v-model="ruleDialogVisible"
      :rule-id="currentRuleId"
      :mode="ruleDialogMode"
      @success="handleRuleSuccess"
    />
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'EstablishmentWarning'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import type { FormInstance, FormRules } from 'element-plus'
import { Plus, Refresh, Download } from '@element-plus/icons-vue'
import { useEstablishmentStore } from '@/stores/modules/establishment'
import { organizationApi } from '@/api/organization'
import WarningRuleDialog from './WarningRuleDialog.vue'

// 状态管理
const establishmentStore = useEstablishmentStore()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const warningRules = ref<any[]>([])
const activeWarnings = ref<any[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

// 对话框相关
const handleDialogVisible = ref(false)
const handleFormRef = ref<FormInstance>()
const currentWarning = ref<unknown>(null)
const ruleDialogVisible = ref(false)
const ruleDialogMode = ref<'create' | 'edit'>('create')
const currentRuleId = ref('')

// 处理表单
const handleForm = reactive({
  handleType: '',
  handleResult: '',
  remark: ''
})

// 表单验证规则
const handleFormRules = reactive<FormRules>({
  handleType: [
    { required: true, message: '请选择处理方式', trigger: 'change' }
  ],
  handleResult: [
    { required: true, message: '请输入处理说明', trigger: 'blur' },
    { min: 10, max: 500, message: '处理说明长度在10-500个字符', trigger: 'blur' }
  ]
})

// 获取预警类型标签
const getWarningTypeLabel = (type: string) => {
  const labelMap: Record<string, string> = {
    OVERSTAFFED: '超编预警',
    UNDERSTAFFED: '缺编预警',
    HIGH_USAGE: '高使用率',
    LOW_USAGE: '低使用率',
    NEAR_LIMIT: '接近上限'
  }
  return labelMap[type] || type
}

const getWarningTypeTag = (type: string) => {
  const tagMap: Record<string, string> = {
    OVERSTAFFED: 'danger',
    UNDERSTAFFED: 'warning',
    HIGH_USAGE: 'danger',
    LOW_USAGE: 'info',
    NEAR_LIMIT: 'warning'
  }
  return tagMap[type] || ''
}

// 获取预警级别标签样式
const getWarningLevelTag = (level: number) => {
  if (level === 1) return 'danger'
  if (level === 2) return 'warning'
  return 'info'
}

// 获取使用率样式类
const getRateClass = (rate: number) => {
  if (rate > 100) return 'text-danger'
  if (rate > 90) return 'text-warning'
  if (rate < 50) return 'text-info'
  return ''
}

// 格式化触发条件
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const formatTriggerCondition = (rule: unknown) => {
  const {warningType: _warningType, threshold: _threshold, operator: _operator} =  rule
  const operatorMap: Record<string, string> 

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .warning-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-actions {
        display: flex;
        gap: 10px;
      }
    }

    .establishment-info-inline {
      font-size: 12px;
      
      .text-danger {
        color: #f56c6c;
      }
      
      .text-warning {
        color: #e6a23c;
      }
      
      .text-info {
        color: #909399;
      }
    }

    .warning-detail {
      padding: 20px;
      background-color: #f5f7fa;
    }
  }

  .el-pagination {
    margin-top: 20px;
  }
}

:deep(.warning-row) {
  &.el-table__row--level-1 {
    background-color: #fff5f5;
  }
  
  &.el-table__row--level-2 {
    background-color: #fffbf0;
  }
}
</style>