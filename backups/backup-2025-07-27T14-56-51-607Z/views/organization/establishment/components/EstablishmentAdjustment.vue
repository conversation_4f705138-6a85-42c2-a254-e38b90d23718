<template>
  <div class="establishment-adjustment">
    <!-- 工具栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        新增调整申请
      </el-button>
      <el-button @click="handleBatchImport">
        <el-icon><Upload /></el-icon>
        批量导入
      </el-button>
      <el-button @click="handleTransfer">
        <el-icon><Switch /></el-icon>
        编制划转
      </el-button>
    </div>

    <!-- 调整申请列表 -->
    <el-table 
      :data="adjustments" 
      v-loading="loading"
      border
    >
      <el-table-column type="selection" width="55"  />
      <el-table-column prop="createTime" label="申请日期" width="110"  />
      <el-table-column prop="institutionName" label="机构" min-width="150"  />
      <el-table-column prop="positionCategory" label="岗位类别" width="100">
        <template #default="{ row }">
          <span v-if="row.positionCategory">
            {{ getEstablishmentTypeLabel(row.positionCategory) }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="adjustmentType" label="调整类型" width="100">
        <template #default="{ row }">
          <el-tag :type="getAdjustmentTypeTag(row.adjustmentType)">
            {{ getAdjustmentTypeLabel(row.adjustmentType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="编制变化" width="150" align="center">
        <template #default="{ row }">
          <div class="number-change">
            <span class="original">{{ row.originalCount }}</span>
            <el-icon><ArrowRight /></el-icon>
            <span 
              class="target" 
              :class="{
                'text-success': row.targetCount > row.originalCount,
                'text-danger': row.targetCount < row.originalCount
              }"
            >
              {{ row.targetCount }}
            </span>
            <span class="diff" v-if="row.targetCount !== row.originalCount">
              ({{ row.targetCount > row.originalCount ? '+' : '' }}{{ row.targetCount - row.originalCount }})
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="adjustmentReason" label="调整原因" min-width="200" show-overflow-tooltip  />
      <el-table-column prop="effectiveDate" label="生效日期" width="110"  />
      <el-table-column prop="approvalStatus" label="审批状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getApprovalStatusType(row.approvalStatus)">
            {{ getApprovalStatusLabel(row.approvalStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <el-button link type="primary" @click="handleView(row)">查看</el-button>
          <el-button 
            v-if="row.approvalStatus === 'DRAFT'" 
            link 
            type="primary" 
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button 
            v-if="row.approvalStatus === 'DRAFT'" 
            link 
            type="danger" 
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :total="total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="fetchAdjustments"
      @current-change="fetchAdjustments"
     />

    <!-- 调整申请对话框 -->
    <AdjustmentDialog
      v-model="adjustmentDialogVisible"
      :adjustment-id="currentAdjustmentId"
      :mode="dialogMode"
      @success="handleSuccess"
    />

    <!-- 编制划转对话框 -->
    <TransferDialog
      v-model="transferDialogVisible"
      @success="handleSuccess"
    />
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'EstablishmentAdjustment'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Upload, Switch, ArrowRight } from '@element-plus/icons-vue'
import { useEstablishmentStore } from '@/stores/modules/establishment'
import { 
  establishmentTypeOptions,
  approvalStatusOptions,
  EstablishmentApprovalStatus 
} from '@/types/establishment'
import AdjustmentDialog from './AdjustmentDialog.vue'
import TransferDialog from './TransferDialog.vue'

// 状态管理
const establishmentStore = useEstablishmentStore()

// 响应式数据
const loading = ref(false)
const adjustments = ref<any[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const adjustmentDialogVisible = ref(false)
const transferDialogVisible = ref(false)
const currentAdjustmentId = ref('')
const dialogMode = ref<'view' | 'create' | 'edit'>('create')

// 获取编制类型标签
const getEstablishmentTypeLabel = (type: string) => {
  const option = establishmentTypeOptions.find(item => item.value === type)
  return option?.label || type
}

// 获取调整类型标签
const getAdjustmentTypeLabel = (type: string) => {
  const labelMap: Record<string, string> = {
    INCREASE: '增加',
    DECREASE: '减少',
    TRANSFER: '划转'
  }
  return labelMap[type] || type
}

// 获取调整类型标签类型
const getAdjustmentTypeTag = (type: string) => {
  const typeMap: Record<string, string> = {
    INCREASE: 'success',
    DECREASE: 'danger',
    TRANSFER: 'warning'
  }
  return typeMap[type] || ''
}

// 获取审批状态标签
const getApprovalStatusLabel = (status: EstablishmentApprovalStatus) => {
  const option = approvalStatusOptions.find(item => item.value === status)
  return option?.label || status
}

// 获取审批状态类型
const getApprovalStatusType = (status: EstablishmentApprovalStatus) => {
  const typeMap: Record<EstablishmentApprovalStatus, string> = {
    DRAFT: 'info',
    PENDING: 'warning',
    APPROVED: 'success',
    REJECTED: 'danger'
  }
  return typeMap[status] || ''
}

// 获取调整申请列表
const fetchAdjustments = async () => {
  loading.value = true
  try {
    const {list: _list, total: _total} =  await establishmentStore.fetchAdjustments({
      pageNum: currentPage.value,
      pageSize: pageSize.value
    })
    
    // 模拟数据
    adjustments.value 
    display: flex;
    gap: 10px;
  }

  .number-change {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;

    .original {
      color: #909399;
    }

    .target {
      font-weight: bold;
      
      &.text-success {
        color: #67c23a;
      }
      
      &.text-danger {
        color: #f56c6c;
      }
    }

    .diff {
      font-size: 12px;
      color: #909399;
    }
  }

  .el-pagination {
    margin-top: 20px;
  }
}
</style>