<template>
  <el-dialog
    v-model="dialogVisible"
    title="编制划转"
    width="900px"
    append-to-body
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="编制计划" prop="planId">
            <el-select
              v-model="formData.planId"
              placeholder="请选择编制计划"
              style="width: 100%"
              @change="handlePlanChange"
            >
              <el-option
                v-for="plan in activePlans"
                :key="plan.planId"
                :label="plan.planName"
                :value="plan.planId"
               />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="生效日期" prop="effectiveDate">
            <el-date-picker
              v-model="formData.effectiveDate"
              type="date"
              placeholder="选择生效日期"
              value-format="YYYY-MM-DD"
              style="width: 100%"
              :disabled-date="disabledDate"
             />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 划出方信息 -->
      <el-card class="transfer-card">
        <template #header>
          <span>划出方信息</span>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="划出机构" prop="fromInstitutionId">
              <HrOrgPicker
                v-model="formData.fromInstitutionId"
                placeholder="请选择划出机构"
                :clearable="false"
                @change="handleFromInstitutionChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="岗位类别" prop="positionCategory">
              <el-select
                v-model="formData.positionCategory"
                placeholder="请选择岗位类别"
                style="width: 100%"
                @change="handleCategoryChange"
              >
                <el-option
                  v-for="item in establishmentTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                 />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="划转数量" prop="transferCount">
              <el-input-number
                v-model="formData.transferCount"
                :min="1"
                :max="fromEstablishment.available"
                placeholder="请输入划转数量"
                style="width: 100%"
                />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="当前编制">
              <div class="establishment-info">
                <el-tag>核定：{{ fromEstablishment.approved }}</el-tag>
                <el-tag type="success">在编：{{ fromEstablishment.actual }}</el-tag>
                <el-tag :type="fromEstablishment.available > 0 ? 'success' : 'danger'">
                  可用：{{ fromEstablishment.available }}
                </el-tag>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 划入方信息 -->
      <el-card class="transfer-card">
        <template #header>
          <span>划入方信息</span>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="划入机构" prop="toInstitutionId">
              <HrOrgPicker
                v-model="formData.toInstitutionId"
                placeholder="请选择划入机构"
                :clearable="false"
                :disabled-options="[formData.fromInstitutionId]"
                @change="handleToInstitutionChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="当前编制">
              <div class="establishment-info">
                <el-tag>核定：{{ toEstablishment.approved }}</el-tag>
                <el-tag type="success">在编：{{ toEstablishment.actual }}</el-tag>
                <el-tag type="info">使用率：{{ toEstablishment.rate }}%</el-tag>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 划转结果预览 -->
      <el-card class="transfer-result">
        <template #header>
          <span>划转结果预览</span>
        </template>
        
        <el-table :data="transferPreview" border>
          <el-table-column prop="institutionName" label="机构" width="200"  />
          <el-table-column label="划转前" align="center">
            <el-table-column prop="before.approved" label="核定" width="80" align="right"  />
            <el-table-column prop="before.actual" label="在编" width="80" align="right"  />
            <el-table-column prop="before.available" label="可用" width="80" align="right">
              <template #default="{ row }">
                <span :class="{ 'text-danger': row.before.available < 0 }">
                  {{ row.before.available }}
                </span>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="变化" width="100" align="center">
            <template #default="{ row }">
              <span :class="row.change > 0 ? 'text-success' : 'text-danger'">
                {{ row.change > 0 ? '+' : '' }}{{ row.change }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="划转后" align="center">
            <el-table-column prop="after.approved" label="核定" width="80" align="right"  />
            <el-table-column prop="after.actual" label="在编" width="80" align="right"  />
            <el-table-column prop="after.available" label="可用" width="80" align="right">
              <template #default="{ row }">
                <span :class="{ 'text-danger': row.after.available < 0 }">
                  {{ row.after.available }}
                </span>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </el-card>

      <el-form-item label="划转原因" prop="transferReason">
        <el-input
          v-model="formData.transferReason"
          type="textarea"
          :rows="3"
          placeholder="请输入划转原因"
          maxlength="300"
          show-word-limit
          />
      </el-form-item>

      <el-form-item label="附件" prop="attachments">
        <el-upload
          v-model:file-list="fileList"
          class="upload-demo"
          action="#"
          :auto-upload="false"
          :multiple="true"
          :limit="3"
          :on-exceed="handleExceed"
        >
          <el-button type="primary">选择文件</el-button>
          <template #tip>
            <div class="el-upload__tip">
              支持上传doc/docx/pdf文件，单个文件不超过10MB，最多3个文件
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">
        提交申请
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'TransferDialog'
})
 
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules, UploadProps, UploadUserFile } from 'element-plus'
import { useEstablishmentStore } from '@/stores/modules/establishment'
import { useOrganizationStore } from '@/stores/modules/organization'
import { establishmentTypeOptions } from '@/types/establishment'
import type { EstablishmentTransfer } from '@/types/establishment'
import HrOrgPicker from '@/components/organization/HrOrgPicker.vue'
import { uploadApi } from '@/api/upload'

// 组件属性
interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'success': []
}>()

// 状态管理
const establishmentStore = useEstablishmentStore()
const organizationStore = useOrganizationStore()

// 响应式数据
const formRef = ref<FormInstance>()
const loading = ref(false)
const fileList = ref<UploadUserFile[]>([])
const activePlans = computed(() => establishmentStore.activePlans)

// 编制情况
const fromEstablishment = ref({
  approved: 0,
  actual: 0,
  available: 0,
  rate: 0
})

const toEstablishment = ref({
  approved: 0,
  actual: 0,
  available: 0,
  rate: 0
})

// 表单数据
const formData = reactive<Partial<EstablishmentTransfer>>({
  planId: '',
  fromInstitutionId: '',
  toInstitutionId: '',
  positionCategory: '',
  transferCount: 1,
  transferReason: '',
  effectiveDate: '',
  approvalStatus: 'DRAFT'
})

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 划转预览数据
const transferPreview = computed(() => {
  const count = formData.transferCount || 0
  
  return [
    {
      institutionName: getInstitutionName(formData.fromInstitutionId),
      before: { ...fromEstablishment.value },
      change: -count,
      after: {
        approved: fromEstablishment.value.approved - count,
        actual: fromEstablishment.value.actual,
        available: fromEstablishment.value.available - count
      }
    },
    {
      institutionName: getInstitutionName(formData.toInstitutionId),
      before: { ...toEstablishment.value },
      change: count,
      after: {
        approved: toEstablishment.value.approved + count,
        actual: toEstablishment.value.actual,
        available: toEstablishment.value.available + count
      }
    }
  ]
})

// 表单验证规则
const formRules = reactive<FormRules>({
  planId: [
    { required: true, message: '请选择编制计划', trigger: 'change' }
  ],
  fromInstitutionId: [
    { required: true, message: '请选择划出机构', trigger: 'change' }
  ],
  toInstitutionId: [
    { required: true, message: '请选择划入机构', trigger: 'change' },
    {
      validator: (rule, value, callback) => {
        if (value === formData.fromInstitutionId) {
          callback(new Error('划入机构不能与划出机构相同'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  positionCategory: [
    { required: true, message: '请选择岗位类别', trigger: 'change' }
  ],
  transferCount: [
    { required: true, message: '请输入划转数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '划转数量必须大于0', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value > fromEstablishment.value.available) {
          callback(new Error('划转数量不能超过可用编制'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  transferReason: [
    { required: true, message: '请输入划转原因', trigger: 'blur' },
    { min: 10, max: 300, message: '划转原因长度在10-300个字符', trigger: 'blur' }
  ],
  effectiveDate: [
    { required: true, message: '请选择生效日期', trigger: 'change' }
  ]
})

// 获取机构名称
const getInstitutionName = (id?: string) => {
  if (!id) return '未选择'
  // 从组织store获取机构名称
  const organization = organizationStore.getOrganizationById(id)
  return organization?.name || '未知机构'
}

// 禁用过去的日期
const disabledDate = (time: Date) => {
  return time.getTime() < Date.now() - 8.64e7
}

// 处理计划变更
const handlePlanChange = () => {
  formData.fromInstitutionId = ''
  formData.toInstitutionId = ''
  formData.positionCategory = ''
  resetEstablishment()
}

// 处理划出机构变更
const handleFromInstitutionChange = () => {
  formData.positionCategory = ''
  updateFromEstablishment()
}

// 处理划入机构变更
const handleToInstitutionChange = () => {
  updateToEstablishment()
}

// 处理岗位类别变更
const handleCategoryChange = () => {
  updateFromEstablishment()
  updateToEstablishment()
}

// 更新划出方编制情况
const updateFromEstablishment = async () => {
  if (!formData.planId || !formData.fromInstitutionId || !formData.positionCategory) {
    resetFromEstablishment()
    return
  }

  try {
    const statistics = await establishmentStore.fetchStatistics({
      planId: formData.planId,
      institutionIds: [formData.fromInstitutionId],
      positionCategory: formData.positionCategory
    })

    if (statistics.length > 0) {
      const stat = statistics[0]
      fromEstablishment.value = {
        approved: stat.approvedCount,
        actual: stat.actualCount,
        available: stat.approvedCount - stat.actualCount,
        rate: stat.utilizationRate
      }
    }
  } catch (__error) {
    console.error('获取划出方编制统计失败:', error)
  }
}

// 更新划入方编制情况
const updateToEstablishment = async () => {
  if (!formData.planId || !formData.toInstitutionId || !formData.positionCategory) {
    resetToEstablishment()
    return
  }

  try {
    const statistics = await establishmentStore.fetchStatistics({
      planId: formData.planId,
      institutionIds: [formData.toInstitutionId],
      positionCategory: formData.positionCategory
    })

    if (statistics.length > 0) {
      const stat = statistics[0]
      toEstablishment.value = {
        approved: stat.approvedCount,
        actual: stat.actualCount,
        available: stat.approvedCount - stat.actualCount,
        rate: stat.utilizationRate
      }
    }
  } catch (__error) {
    console.error('获取划入方编制统计失败:', error)
  }
}

// 重置编制情况
const resetEstablishment = () => {
  resetFromEstablishment()
  resetToEstablishment()
}

const resetFromEstablishment = () => {
  fromEstablishment.value = {
    approved: 0,
    actual: 0,
    available: 0,
    rate: 0
  }
}

const resetToEstablishment = () => {
  toEstablishment.value = {
    approved: 0,
    actual: 0,
    available: 0,
    rate: 0
  }
}

// 处理文件超出限制
const handleExceed: UploadProps['onExceed'] = (files) => {
  ElMessage.warning(`最多只能上传3个文件，本次选择了${files.length}个文件`)
}

// 初始化表单
const initForm = async () => {
  // 加载活跃的编制计划
  await establishmentStore.fetchPlans({ status: 'ACTIVE' })
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (!valid) return

    // 验证划转后编制是否合理
    if (fromEstablishment.value.available - (formData.transferCount || 0) < 0) {
      ElMessage.warning('划转后划出方可用编制不能为负数')
      return
    }

    try {
      await ElMessageBox.confirm(
        '确认提交编制划转申请？提交后需要等待审批。',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      loading.value = true

      // 处理附件上传
      const attachmentUrls: string[] = []
      for (const file of fileList.value) {
        if (file.raw) {
          try {
            const uploadResult = await uploadApi.uploadFile(file.raw, {
              category: 'establishment-transfer',
              onProgress: (progress) => {
                // 更新上传进度
                file.percentage = progress.percent
              }
            })
            attachmentUrls.push(uploadResult.url)
          } catch (__error) {
            ElMessage.error(`文件 ${file.name} 上传失败`)
            throw error
          }
        } else if (file.url) {
          // 已上传的文件
          attachmentUrls.push(file.url)
        }
      }

      const data = {
        ...formData,
        attachments: attachmentUrls,
        fromOriginalCount: fromEstablishment.value.approved,
        fromTargetCount: fromEstablishment.value.approved - (formData.transferCount || 0),
        toOriginalCount: toEstablishment.value.approved,
        toTargetCount: toEstablishment.value.approved + (formData.transferCount || 0)
      }

      await establishmentStore.createTransfer(data)
      ElMessage.success('提交成功')
      
      emit('success')
      dialogVisible.value = false
    } catch (__error) {
      if (error !== 'cancel') {
        console.error('提交失败:', error)
        ElMessage.error('提交失败')
      }
    } finally {
      loading.value = false
    }
  })
}

// 取消
const handleCancel = () => {
  dialogVisible.value = false
}

// 监听对话框显示状态
watch(dialogVisible, (val) => {
  if (val) {
    initForm()
  } else {
    // 重置表单
    formRef.value?.resetFields()
    fileList.value = []
    resetEstablishment()
  }
})
</script>

<style lang="scss" scoped>
.transfer-card {
  margin-bottom: 20px;
}

.establishment-info {
  display: flex;
  gap: 10px;
  align-items: center;
}

.transfer-result {
  margin-bottom: 20px;
  
  .text-success {
    color: #67c23a;
  }
  
  .text-danger {
    color: #f56c6c;
  }
}

.upload-demo {
  width: 100%;
}
</style>