<template>
  <div class="establishment-statistics">
    <!-- 查询条件 -->
    <div class="search-bar">
      <el-form :inline="true" :model="searchForm">
        <el-form-item label="统计日期">
          <el-date-picker
            v-model="searchForm.statisticsDate"
            type="date"
            placeholder="选择日期"
            value-format="YYYY-MM-DD"
            :disabled-date="disabledDate"
           />
        </el-form-item>
        <el-form-item label="机构范围">
          <HrOrgPicker
            v-model="searchForm.institutionIds"
            placeholder="请选择机构"
            multiple
            collapse-tags
            clearable
          />
        </el-form-item>
        <el-form-item label="显示条件">
          <el-checkbox v-model="searchForm.showOverstaffed">只显示超编</el-checkbox>
          <el-checkbox v-model="searchForm.showUnderstaffed">只显示缺编</el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button @click="handleExport">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 统计图表 -->
    <el-row :gutter="20" class="chart-container">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>编制分布图</span>
          </template>
          <div ref="distributionChart" class="chart"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>编制使用率排行</span>
          </template>
          <div ref="utilizationChart" class="chart"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 统计表格 -->
    <el-card class="table-card">
      <template #header>
        <span>编制统计明细</span>
      </template>
      
      <el-table 
        :data="tableData" 
        v-loading="loading"
        border
        show-summary
        :summary-method="getSummaries"
      >
        <el-table-column prop="institutionName" label="机构名称" min-width="150" fixed  />
        <el-table-column label="管理岗" align="center">
          <el-table-column prop="management.approved" label="核定" width="80" align="right"  />
          <el-table-column prop="management.actual" label="实际" width="80" align="right">
            <template #default="{ row }">
              <span :class="getNumberClass(row.management)">
                {{ row.management.actual }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="management.rate" label="使用率" width="80" align="right">
            <template #default="{ row }">
              <span :class="getRateClass(row.management.rate)">
                {{ row.management.rate }}%
              </span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="专技岗" align="center">
          <el-table-column prop="professional.approved" label="核定" width="80" align="right"  />
          <el-table-column prop="professional.actual" label="实际" width="80" align="right">
            <template #default="{ row }">
              <span :class="getNumberClass(row.professional)">
                {{ row.professional.actual }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="professional.rate" label="使用率" width="80" align="right">
            <template #default="{ row }">
              <span :class="getRateClass(row.professional.rate)">
                {{ row.professional.rate }}%
              </span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="工勤岗" align="center">
          <el-table-column prop="worker.approved" label="核定" width="80" align="right"  />
          <el-table-column prop="worker.actual" label="实际" width="80" align="right">
            <template #default="{ row }">
              <span :class="getNumberClass(row.worker)">
                {{ row.worker.actual }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="worker.rate" label="使用率" width="80" align="right">
            <template #default="{ row }">
              <span :class="getRateClass(row.worker.rate)">
                {{ row.worker.rate }}%
              </span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="合计" align="center">
          <el-table-column prop="total.approved" label="核定" width="80" align="right"  />
          <el-table-column prop="total.actual" label="实际" width="80" align="right">
            <template #default="{ row }">
              <span :class="getNumberClass(row.total)">
                {{ row.total.actual }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="total.rate" label="使用率" width="90" align="right">
            <template #default="{ row }">
              <el-progress
                :percentage="row.total.rate"
                :color="getProgressColor(row.total.rate)"
                :stroke-width="6"
               />
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleView(row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 详情对话框 -->
    <EstablishmentDetailDialog
      v-model="detailDialogVisible"
      :institution-id="currentInstitutionId"
      :statistics-date="searchForm.statisticsDate"
    />
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'EstablishmentStatistics'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import { useEstablishmentStore } from '@/stores/modules/establishment'
import type { EstablishmentStatistics } from '@/types/establishment'
import HrOrgPicker from '@/components/organization/HrOrgPicker.vue'
import EstablishmentDetailDialog from './EstablishmentDetailDialog.vue'
import { exportToExcel, type ExportColumn } from '@/utils/export'

// 状态管理
const establishmentStore = useEstablishmentStore()

// 响应式数据
const loading = ref(false)
const tableData = ref<any[]>([])
const detailDialogVisible = ref(false)
const currentInstitutionId = ref('')

// 图表实例
let distributionChartInstance: echarts.ECharts | null = null
let utilizationChartInstance: echarts.ECharts | null = null

// refs
const distributionChart = ref<HTMLDivElement>()
const utilizationChart = ref<HTMLDivElement>()

// 搜索表单
const searchForm = reactive({
  statisticsDate: new Date().toISOString().split('T')[0],
  institutionIds: [] as string[],
  showOverstaffed: false,
  showUnderstaffed: false
})

// 禁用未来日期
const disabledDate = (time: Date) => {
  return time.getTime() > Date.now()
}

// 获取数字样式类
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const getNumberClass = (data: unknown) => {
  if (data.actual > data.approved) return 'text-danger'
  if (data.actual < data.approved) return 'text-warning'
  return 'text-success'
}

// 获取使用率样式类
const getRateClass = (rate: number) => {
  if (rate > 100) return 'text-danger'
  if (rate > 90) return 'text-warning'
  return 'text-success'
}

// 获取进度条颜色
const getProgressColor = (percentage: number) => {
  if (percentage > 100) return '#f56c6c'
  if (percentage > 90) return '#e6a23c'
  return '#67c23a'
}

// 获取统计数据
const fetchStatistics = async () => {
  loading.value = true
  try {
    const statistics = await establishmentStore.fetchStatistics({
      statisticsDate: searchForm.statisticsDate,
      institutionIds: searchForm.institutionIds.length > 0 ? searchForm.institutionIds : undefined,
      showOverstaffed: searchForm.showOverstaffed,
      showUnderstaffed: searchForm.showUnderstaffed
    })

    // 转换数据格式
    tableData.value = transformStatisticsData(statistics)
    
    // 更新图表
    await nextTick()
    updateCharts(statistics)
  } catch (__error) {
    console.error('获取编制统计失败:', error)
    ElMessage.error('获取编制统计失败')
  } finally {
    loading.value = false
  }
}

// 转换统计数据格式
const transformStatisticsData = (statistics: EstablishmentStatistics[]) => {
  const groupedData: Record<string, unknown> = {}

  statistics.forEach(stat => {
    const key = stat.institutionId || 'total'
    
    if (!groupedData[key]) {
      groupedData[key] = {
        institutionId: stat.institutionId,
        institutionName: stat.institutionName || '全校',
        management: { approved: 0, actual: 0, rate: 0 },
        professional: { approved: 0, actual: 0, rate: 0 },
        worker: { approved: 0, actual: 0, rate: 0 },
        total: { approved: 0, actual: 0, rate: 0 }
      }
    }

    const category = stat.positionCategory?.toLowerCase() || 'total'
    if (category !== 'total') {
      groupedData[key][category] = {
        approved: stat.approvedCount,
        actual: stat.actualCount,
        rate: stat.utilizationRate
      }
    }

    // 计算合计
    groupedData[key].total.approved += stat.approvedCount
    groupedData[key].total.actual += stat.actualCount
  })

  // 计算合计使用率
  Object.values(groupedData).forEach(item => {
    if (item.total.approved > 0) {
      item.total.rate = Math.round((item.total.actual / item.total.approved) * 100)
    }
  })

  return Object.values(groupedData)
}

// 获取汇总数据
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const getSummaries = (param: unknown) => {
  const {columns: _columns, data: _data} =  param
  const sums: string[] 
  }

  .chart-container {
    margin-bottom: 20px;

    .chart {
      width: 100%;
      height: 400px;
    }
  }

  .table-card {
    :deep(.el-table) {
      .text-danger {
        color: #f56c6c;
      }

      .text-warning {
        color: #e6a23c;
      }

      .text-success {
        color: #67c23a;
      }
    }
  }
}
</style>