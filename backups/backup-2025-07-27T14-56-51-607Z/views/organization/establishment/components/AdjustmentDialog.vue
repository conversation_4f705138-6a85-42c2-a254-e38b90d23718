<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    append-to-body
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :disabled="isViewMode"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="编制计划" prop="planId">
            <el-select
              v-model="formData.planId"
              placeholder="请选择编制计划"
              style="width: 100%"
              @change="handlePlanChange"
            >
              <el-option
                v-for="plan in activePlans"
                :key="plan.planId"
                :label="plan.planName"
                :value="plan.planId"
               />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="生效日期" prop="effectiveDate">
            <el-date-picker
              v-model="formData.effectiveDate"
              type="date"
              placeholder="选择生效日期"
              value-format="YYYY-MM-DD"
              style="width: 100%"
              :disabled-date="disabledDate"
             />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="调整机构" prop="institutionId">
            <HrOrgPicker
              v-model="formData.institutionId"
              placeholder="请选择机构"
              :clearable="false"
              @change="handleInstitutionChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="调整类型" prop="adjustmentType">
            <el-radio-group v-model="formData.adjustmentType">
              <el-radio value="INCREASE">增加</el-radio>
              <el-radio value="DECREASE">减少</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="岗位类别" prop="positionCategory">
            <el-select
              v-model="formData.positionCategory"
              placeholder="请选择岗位类别"
              style="width: 100%"
              @change="handleCategoryChange"
            >
              <el-option
                v-for="item in establishmentTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="调整数量" prop="adjustmentCount">
            <el-input-number
              v-model="formData.adjustmentCount"
              :min="1"
              :max="maxAdjustmentCount"
              placeholder="请输入调整数量"
              style="width: 100%"
              />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="当前编制情况">
            <div class="establishment-info">
              <el-tag>核定编制：{{ currentEstablishment.approved }}</el-tag>
              <el-tag type="success">实际在编：{{ currentEstablishment.actual }}</el-tag>
              <el-tag :type="currentEstablishment.available > 0 ? 'success' : 'danger'">
                可用编制：{{ currentEstablishment.available }}
              </el-tag>
              <el-tag type="info">使用率：{{ currentEstablishment.rate }}%</el-tag>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="调整后编制">
            <div class="establishment-info">
              <el-tag type="primary">
                核定编制：{{ afterAdjustment.approved }}
                <span v-if="formData.adjustmentType && formData.adjustmentCount">
                  ({{ formData.adjustmentType === 'INCREASE' ? '+' : '-' }}{{ formData.adjustmentCount }})
                </span>
              </el-tag>
              <el-tag type="success">实际在编：{{ afterAdjustment.actual }}</el-tag>
              <el-tag :type="afterAdjustment.available >= 0 ? 'success' : 'danger'">
                可用编制：{{ afterAdjustment.available }}
              </el-tag>
              <el-tag type="info">使用率：{{ afterAdjustment.rate }}%</el-tag>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="调整原因" prop="adjustmentReason">
        <el-input
          v-model="formData.adjustmentReason"
          type="textarea"
          :rows="4"
          placeholder="请输入调整原因"
          maxlength="500"
          show-word-limit
          />
      </el-form-item>

      <el-form-item label="附件" prop="attachments">
        <el-upload
          v-model:file-list="fileList"
          class="upload-demo"
          action="#"
          :auto-upload="false"
          :multiple="true"
          :limit="5"
          :on-exceed="handleExceed"
        >
          <el-button type="primary">选择文件</el-button>
          <template #tip>
            <div class="el-upload__tip">
              支持上传doc/docx/pdf/jpg/png文件，单个文件不超过10MB，最多5个文件
            </div>
          </template>
        </el-upload>
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="2"
          placeholder="请输入备注信息"
          maxlength="200"
          show-word-limit
          />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button v-if="!isViewMode" type="primary" @click="handleSubmit">
        {{ mode === 'create' ? '提交申请' : '保存修改' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
 
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules, UploadProps, UploadUserFile } from 'element-plus'
import { useEstablishmentStore } from '@/stores/modules/establishment'
import { useOrganizationStore } from '@/stores/modules/organization'
import { establishmentTypeOptions } from '@/types/establishment'
import type { EstablishmentAdjustment } from '@/types/establishment'
import HrOrgPicker from '@/components/organization/HrOrgPicker.vue'
import { uploadAttachment } from '@/api/common/attachment'

// 组件属性
interface Props {
  modelValue: boolean
  adjustmentId?: string
  mode: 'create' | 'edit' | 'view'
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'create'
})

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'success': []
}>()

// 状态管理
const establishmentStore = useEstablishmentStore()
const organizationStore = useOrganizationStore()

// 响应式数据
const formRef = ref<FormInstance>()
const loading = ref(false)
const fileList = ref<UploadUserFile[]>([])
const activePlans = computed(() => establishmentStore.activePlans)
const currentEstablishment = ref({
  approved: 0,
  actual: 0,
  available: 0,
  rate: 0
})

// 表单数据
const formData = reactive<Partial<EstablishmentAdjustment>>({
  planId: '',
  institutionId: '',
  positionCategory: '',
  adjustmentType: 'INCREASE',
  adjustmentCount: 1,
  adjustmentReason: '',
  effectiveDate: '',
  remark: '',
  approvalStatus: 'DRAFT'
})

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const dialogTitle = computed(() => {
  const titles = {
    create: '新增编制调整申请',
    edit: '编辑编制调整申请',
    view: '查看编制调整申请'
  }
  return titles[props.mode]
})

const isViewMode = computed(() => props.mode === 'view')

// 最大调整数量（减少时不能超过可用编制）
const maxAdjustmentCount = computed(() => {
  if (formData.adjustmentType === 'DECREASE') {
    return currentEstablishment.value.available
  }
  return 999
})

// 调整后的编制情况
const afterAdjustment = computed(() => {
  const adjustment = formData.adjustmentCount || 0
  const approved = formData.adjustmentType === 'INCREASE' 
    ? currentEstablishment.value.approved + adjustment
    : currentEstablishment.value.approved - adjustment
  
  const actual = currentEstablishment.value.actual
  const available = approved - actual
  const rate = approved > 0 ? Math.round((actual / approved) * 100) : 0

  return { approved, actual, available, rate }
})

// 表单验证规则
const formRules = reactive<FormRules>({
  planId: [
    { required: true, message: '请选择编制计划', trigger: 'change' }
  ],
  institutionId: [
    { required: true, message: '请选择调整机构', trigger: 'change' }
  ],
  positionCategory: [
    { required: true, message: '请选择岗位类别', trigger: 'change' }
  ],
  adjustmentType: [
    { required: true, message: '请选择调整类型', trigger: 'change' }
  ],
  adjustmentCount: [
    { required: true, message: '请输入调整数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '调整数量必须大于0', trigger: 'blur' }
  ],
  adjustmentReason: [
    { required: true, message: '请输入调整原因', trigger: 'blur' },
    { min: 10, max: 500, message: '调整原因长度在10-500个字符', trigger: 'blur' }
  ],
  effectiveDate: [
    { required: true, message: '请选择生效日期', trigger: 'change' }
  ]
})

// 禁用过去的日期
const disabledDate = (time: Date) => {
  return time.getTime() < Date.now() - 8.64e7
}

// 处理计划变更
const handlePlanChange = () => {
  // 清空相关字段
  formData.institutionId = ''
  formData.positionCategory = ''
  updateCurrentEstablishment()
}

// 处理机构变更
const handleInstitutionChange = () => {
  formData.positionCategory = ''
  updateCurrentEstablishment()
}

// 处理岗位类别变更
const handleCategoryChange = () => {
  updateCurrentEstablishment()
}

// 更新当前编制情况
const updateCurrentEstablishment = async () => {
  if (!formData.planId || !formData.institutionId || !formData.positionCategory) {
    currentEstablishment.value = {
      approved: 0,
      actual: 0,
      available: 0,
      rate: 0
    }
    return
  }

  try {
    // 获取该机构该类别的编制统计
    const statistics = await establishmentStore.fetchStatistics({
      planId: formData.planId,
      institutionIds: [formData.institutionId],
      positionCategory: formData.positionCategory
    })

    if (statistics.length > 0) {
      const stat = statistics[0]
      currentEstablishment.value = {
        approved: stat.approvedCount,
        actual: stat.actualCount,
        available: stat.approvedCount - stat.actualCount,
        rate: stat.utilizationRate
      }
    }
  } catch (__error) {
    console.error('获取编制统计失败:', error)
  }
}

// 处理文件超出限制
const handleExceed: UploadProps['onExceed'] = (files) => {
  ElMessage.warning(`最多只能上传5个文件，本次选择了${files.length}个文件`)
}

// 初始化表单
const initForm = async () => {
  if (props.mode !== 'create' && props.adjustmentId) {
    loading.value = true
    try {
      const adjustment = await establishmentStore.fetchAdjustmentDetail(props.adjustmentId)
      Object.assign(formData, adjustment)
      
      // 加载附件
      if (adjustment.attachments) {
        fileList.value = adjustment.attachments.map((url, index) => ({
          name: `附件${index + 1}`,
          url
        }))
      }

      // 更新当前编制情况
      await updateCurrentEstablishment()
    } catch (__error) {
      console.error('加载调整申请详情失败:', error)
      ElMessage.error('加载调整申请详情失败')
    } finally {
      loading.value = false
    }
  } else {
    // 新增时加载活跃的编制计划
    await establishmentStore.fetchPlans({ status: 'ACTIVE' })
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (!valid) return

    // 验证调整后编制是否合理
    if (afterAdjustment.value.available < 0) {
      ElMessage.warning('调整后可用编制不能为负数')
      return
    }

    try {
      await ElMessageBox.confirm(
        `确认${props.mode === 'create' ? '提交' : '保存'}编制调整申请？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      loading.value = true

      // 处理附件上传
      const attachmentUrls: string[] = []
      for (const file of fileList.value) {
        if (file.raw) {
          // 实际上传文件到服务器
          try {
            const formData = new FormData()
            formData.append('file', file.raw)
            formData.append('businessType', 'ESTABLISHMENT_ADJUSTMENT')
            formData.append('businessId', props.adjustmentId || 'new')
            
            const response = await uploadAttachment(formData)
            attachmentUrls.push(response.data.url)
          } catch (__uploadError) {
            console.error('文件上传失败:', uploadError)
            ElMessage.error(`文件 ${file.name} 上传失败`)
            loading.value = false
            return
          }
        } else if (file.url) {
          attachmentUrls.push(file.url)
        }
      }

      const data = {
        ...formData,
        attachments: attachmentUrls,
        originalCount: currentEstablishment.value.approved,
        targetCount: afterAdjustment.value.approved
      }

      if (props.mode === 'create') {
        await establishmentStore.createAdjustment(data)
        ElMessage.success('提交成功')
      } else {
        await establishmentStore.updateAdjustment(props.adjustmentId!, data)
        ElMessage.success('保存成功')
      }

      emit('success')
      dialogVisible.value = false
    } catch (__error) {
      if (error !== 'cancel') {
        console.error('操作失败:', error)
        ElMessage.error(props.mode === 'create' ? '提交失败' : '保存失败')
      }
    } finally {
      loading.value = false
    }
  })
}

// 取消
const handleCancel = () => {
  dialogVisible.value = false
}

// 监听对话框显示状态
watch(dialogVisible, (val) => {
  if (val) {
    initForm()
  } else {
    // 重置表单
    formRef.value?.resetFields()
    fileList.value = []
    currentEstablishment.value = {
      approved: 0,
      actual: 0,
      available: 0,
      rate: 0
    }
  }
})
</script>

<style lang="scss" scoped>
.establishment-info {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.upload-demo {
  width: 100%;
}
</style>