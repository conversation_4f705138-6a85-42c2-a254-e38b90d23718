<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`${institutionName}编制详情`"
    width="900px"
    append-to-body
  >
    <div class="establishment-detail" v-loading="loading">
      <!-- 岗位类别统计 -->
      <el-row :gutter="20" class="stat-row">
        <el-col :span="8" v-for="item in categoryStats" :key="item.category">
          <el-card>
            <div class="stat-item">
              <div class="stat-title">{{ item.label }}</div>
              <div class="stat-content">
                <div class="stat-numbers">
                  <span class="actual" :class="getNumberClass(item)">{{ item.actual }}</span>
                  <span class="separator">/</span>
                  <span class="approved">{{ item.approved }}</span>
                </div>
                <el-progress
                  :percentage="item.rate"
                  :color="getProgressColor(item.rate)"
                  :stroke-width="8"
                 />
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 人员列表 -->
      <el-card class="employee-card">
        <template #header>
          <div class="card-header">
            <span>在编人员列表</span>
            <el-radio-group v-model="filterCategory" @change="handleFilterChange">
              <el-radio-button value="">全部</el-radio-button>
              <el-radio-button 
                v-for="item in establishmentTypeOptions" 
                :key="item.value" 
                :value="item.value"
              >
                {{ item.label }}
              </el-radio-button>
            </el-radio-group>
          </div>
        </template>

        <el-table :data="filteredEmployees" max-height="400">
          <el-table-column prop="employeeCode" label="工号" width="100"  />
          <el-table-column prop="employeeName" label="姓名" width="100"  />
          <el-table-column prop="departmentName" label="部门" min-width="150"  />
          <el-table-column prop="positionName" label="岗位" min-width="120"  />
          <el-table-column prop="positionCategory" label="岗位类别" width="100">
            <template #default="{ row }">
              <el-tag size="small">{{ getPositionCategoryLabel(row.positionCategory) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="employmentDate" label="入职日期" width="110"  />
          <el-table-column prop="status" label="状态" width="80">
            <template #default="{ row }">
              <el-tag :type="row.status === 'ACTIVE' ? 'success' : 'info'" size="small">
                {{ row.status === 'ACTIVE' ? '在职' : '离职' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 编制变化趋势 -->
      <el-card class="trend-card">
        <template #header>
          <span>编制变化趋势（近12个月）</span>
        </template>
        <div ref="trendChart" class="trend-chart"></div>
      </el-card>
    </div>

    <template #footer>
      <el-button @click="dialogVisible = false">关闭</el-button>
      <el-button type="primary" @click="handleExport">导出报告</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import { useEstablishmentStore } from '@/stores/modules/establishment'
import { useOrganizationStore } from '@/stores/modules/organization'
import { establishmentTypeOptions, EstablishmentType } from '@/types/establishment'

// 组件属性
interface Props {
  modelValue: boolean
  institutionId: string
  statisticsDate?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  statisticsDate: () => new Date().toISOString().split('T')[0]
})

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 状态管理
const establishmentStore = useEstablishmentStore()
const organizationStore = useOrganizationStore()

// 响应式数据
const loading = ref(false)
const institutionName = ref('')
const categoryStats = ref<any[]>([])
const employees = ref<any[]>([])
const filterCategory = ref('')
let trendChartInstance: echarts.ECharts | null = null

// refs
const trendChart = ref<HTMLDivElement>()

// 对话框状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 过滤后的员工列表
const filteredEmployees = computed(() => {
  if (!filterCategory.value) return employees.value
  return employees.value.filter(emp => emp.positionCategory === filterCategory.value)
})

// 获取数字样式类
   
const getNumberClass = (data: unknown) => {
  if (data.actual > data.approved) return 'text-danger'
  if (data.actual < data.approved) return 'text-warning'
  return 'text-success'
}

// 获取进度条颜色
const getProgressColor = (percentage: number) => {
  if (percentage > 100) return '#f56c6c'
  if (percentage > 90) return '#e6a23c'
  return '#67c23a'
}

// 获取岗位类别标签
const getPositionCategoryLabel = (category: string) => {
  const option = establishmentTypeOptions.find(item => item.value === category)
  return option?.label || category
}

// 加载详情数据
const loadDetailData = async () => {
  if (!props.institutionId) return

  loading.value = true
  try {
    // 从store获取机构信息
    const organization = organizationStore.getOrganizationById(props.institutionId)
    institutionName.value = organization?.name || '未知机构'

    // 模拟获取各类别统计数据
    categoryStats.value = [
      {
        category: EstablishmentType.MANAGEMENT,
        label: '管理岗',
        approved: 10,
        actual: 8,
        rate: 80
      },
      {
        category: EstablishmentType.PROFESSIONAL,
        label: '专技岗',
        approved: 50,
        actual: 45,
        rate: 90
      },
      {
        category: EstablishmentType.WORKER,
        label: '工勤岗',
        approved: 5,
        actual: 5,
        rate: 100
      }
    ]

    // 模拟获取员工列表
    employees.value = generateMockEmployees()

    // 加载趋势数据并更新图表
    await nextTick()
    if (trendChart.value) {
      initTrendChart()
      updateTrendChart()
    }
  } catch (__error) {
    console.error('加载编制详情失败:', error)
    ElMessage.error('加载编制详情失败')
  } finally {
    loading.value = false
  }
}

// 生成模拟员工数据
const generateMockEmployees = () => {
  const mockData = []
  const categories = [EstablishmentType.MANAGEMENT, EstablishmentType.PROFESSIONAL, EstablishmentType.WORKER]
  const departments = ['办公室', '教务科', '学生科', '财务科', '后勤科']
  
  for (let i = 1; i <= 58; i++) {
    mockData.push({
      employeeId: `EMP${String(i).padStart(4, '0')}`,
      employeeCode: `2023${String(i).padStart(4, '0')}`,
      employeeName: `员工${i}`,
      departmentName: departments[i % departments.length],
      positionName: `岗位${i}`,
      positionCategory: categories[i % categories.length],
      employmentDate: '2023-01-01',
      status: i > 55 ? 'INACTIVE' : 'ACTIVE'
    })
  }
  
  return mockData
}

// 初始化趋势图表
const initTrendChart = () => {
  if (trendChart.value) {
    trendChartInstance = echarts.init(trendChart.value)
  }
}

// 更新趋势图表
const updateTrendChart = async () => {
  if (!trendChartInstance) return

  // 获取趋势数据
  const endDate = new Date()
  const startDate = new Date()
  startDate.setMonth(startDate.getMonth() - 11)

  try {
    const trendData = await establishmentStore.fetchTrends({
      institutionId: props.institutionId,
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0]
    })

    // 模拟数据
    const months = []
    const approvedData = []
    const actualData = []
    const utilizationData = []

    for (let i = 11; i >= 0; i--) {
      const date = new Date()
      date.setMonth(date.getMonth() - i)
      months.push(date.toLocaleDateString('zh-CN', { year: 'numeric', month: 'short' }))
      
      const base = 65
      approvedData.push(base)
      actualData.push(base - Math.floor(Math.random() * 10))
      utilizationData.push(90 + Math.random() * 10)
    }

    trendChartInstance.setOption({
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999'
          }
        }
      },
      legend: {
        data: ['核定编制', '实际在编', '使用率']
      },
      xAxis: [
        {
          type: 'category',
          data: months,
          axisPointer: {
            type: 'shadow'
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          name: 'HrHr人数',
          min: 0,
          max: 100,
          interval: 20,
          axisLabel: {
            formatter: '{value}'
          }
        },
        {
          type: 'value',
          name: '使用率',
          min: 0,
          max: 120,
          interval: 20,
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      series: [
        {
          name: '核定编制',
          type: 'bar',
          data: approvedData,
          itemStyle: {
            color: '#409eff'
          }
        },
        {
          name: '实际在编',
          type: 'bar',
          data: actualData,
          itemStyle: {
            color: '#67c23a'
          }
        },
        {
          name: '使用率',
          type: 'line',
          yAxisIndex: 1,
          data: utilizationData,
          itemStyle: {
            color: '#e6a23c'
          }
        }
      ]
    })
  } catch (__error) {
    console.error('获取趋势数据失败:', error)
  }
}

// 处理筛选变更
const handleFilterChange = () => {
  // 筛选逻辑已通过计算属性实现
}

// 导出报告
const handleExport = () => {
  ElMessage.info('导出功能开发中')
}

// 监听对话框显示
watch(dialogVisible, (val) => {
  if (val) {
    loadDetailData()
  }
})

// 监听窗口大小变化
window.addEventListener('resize', () => {
  trendChartInstance?.resize()
})
</script>

<style lang="scss" scoped>
.establishment-detail {
  .stat-row {
    margin-bottom: 20px;

    .stat-item {
      text-align: center;

      .stat-title {
        font-size: 14px;
        color: #909399;
        margin-bottom: 8px;
      }

      .stat-content {
        .stat-numbers {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 8px;

          .actual {
            &.text-danger {
              color: #f56c6c;
            }
            &.text-warning {
              color: #e6a23c;
            }
            &.text-success {
              color: #67c23a;
            }
          }

          .separator {
            margin: 0 4px;
            color: #909399;
          }

          .approved {
            color: #409eff;
          }
        }
      }
    }
  }

  .employee-card {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .trend-card {
    .trend-chart {
      width: 100%;
      height: 300px;
    }
  }
}
</style>