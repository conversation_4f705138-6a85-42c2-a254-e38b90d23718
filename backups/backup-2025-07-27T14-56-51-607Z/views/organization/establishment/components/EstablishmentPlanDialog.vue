<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    append-to-body
    @closed="handleClosed"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :disabled="mode === 'view'"
      label-width="120px"
      v-loading="loading"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="规划年度" prop="planYear">
            <el-select 
              v-model="formData.planYear" 
              placeholder="请选择年度"
              style="width: 100%"
            >
              <el-option 
                v-for="year in yearOptions" 
                :key="year" 
                :label="`${year}年`" 
                :value="year" 
               />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="规划名称" prop="planName">
            <el-input 
              v-model="formData.planName" 
              placeholder="请输入规划名称"
              maxlength="50"
              show-word-limit
              />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="周期类型" prop="cycleType">
            <el-select 
              v-model="formData.cycleType" 
              placeholder="请选择周期类型"
              style="width: 100%"
              @change="handleCycleTypeChange"
            >
              <el-option 
                v-for="item in planCycleTypeOptions" 
                :key="item.value" 
                :label="item.label" 
                :value="item.value" 
               />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="生效日期" prop="effectiveDate">
            <el-date-picker
              v-model="formData.effectiveDate"
              type="date"
              placeholder="请选择生效日期"
              value-format="YYYY-MM-DD"
              style="width: 100%"
             />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="开始日期" prop="startDate">
            <el-date-picker
              v-model="formData.startDate"
              type="date"
              placeholder="请选择开始日期"
              value-format="YYYY-MM-DD"
              style="width: 100%"
             />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结束日期" prop="endDate">
            <el-date-picker
              v-model="formData.endDate"
              type="date"
              placeholder="请选择结束日期"
              value-format="YYYY-MM-DD"
              style="width: 100%"
             />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属机构" prop="institutionId">
            <HrOrgPicker 
              v-model="formData.institutionId" 
              placeholder="不选择表示全校总编制"
              clearable
              @change="handleOrgChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="岗位类别" prop="positionCategory">
            <el-select 
              v-model="formData.positionCategory" 
              placeholder="不选择表示机构总编制"
              clearable
              style="width: 100%"
              :disabled="!formData.institutionId"
            >
              <el-option 
                v-for="item in establishmentTypeOptions" 
                :key="item.value" 
                :label="item.label" 
                :value="item.value" 
               />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="核定编制数" prop="approvedCount">
            <el-input-number
              v-model="formData.approvedCount"
              :min="0"
              :max="99999"
              placeholder="请输入核定编制数"
              style="width: 100%"
              />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="预算编制数" prop="budgetedCount">
            <el-input-number
              v-model="formData.budgetedCount"
              :min="0"
              :max="99999"
              placeholder="请输入预算编制数"
              style="width: 100%"
              />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="编制文件" prop="staffingDocumentUrl">
        <el-upload
          v-model:file-list="fileList"
          class="upload-demo"
          :action="uploadUrl"
          :headers="uploadHeaders"
          :limit="1"
          :on-success="handleUploadSuccess"
          :on-remove="handleRemove"
          :disabled="mode === 'view'"
        >
          <el-button type="primary">点击上传</el-button>
          <template #tip>
            <div class="el-upload__tip">
              支持上传编制批文扫描件，支持pdf、jpg、png格式，大小不超过10MB
            </div>
          </template>
        </el-upload>
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
          maxlength="500"
          show-word-limit
          />
      </el-form-item>

      <!-- 查看模式下显示审批信息 -->
      <template v-if="mode === 'view' && formData.approvalStatus !== 'DRAFT'">
        <el-divider   />
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="审批状态">
              <el-tag :type="getApprovalStatusType(formData.approvalStatus)">
                {{ getApprovalStatusLabel(formData.approvalStatus) }}
              </el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="创建时间">
              {{ formData.createTime }}
            </el-form-item>
          </el-col>
        </el-row>
      </template>
    </el-form>

    <template #footer>
      <el-button @click="handleCancel">{{ mode === 'view' ? '关闭' : '取消' }}</el-button>
      <el-button 
        v-if="mode !== 'view'" 
        type="primary" 
        @click="handleSubmit" 
        :loading="submitting"
      >
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useEstablishmentStore } from '@/stores/modules/establishment'
import type { EstablishmentPlan } from '@/types/establishment'
import {
  establishmentTypeOptions,
  approvalStatusOptions,
  planCycleTypeOptions,
  PlanCycleType,
  EstablishmentApprovalStatus
} from '@/types/establishment'
import HrOrgPicker from '@/components/organization/HrOrgPicker.vue'

// 组件属性
interface Props {
  modelValue: boolean
  planId?: string
  mode?: 'view' | 'create' | 'edit'
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  planId: '',
  mode: 'create'
})

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'success': []
}>()

// 状态管理
const establishmentStore = useEstablishmentStore()

// refs
const formRef = ref<FormInstance>()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const fileList = ref<any[]>([])

// 对话框状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 对话框标题
const dialogTitle = computed(() => {
  const titleMap = {
    view: '查看编制规划',
    create: '新增编制规划',
    edit: '编辑编制规划'
  }
  return titleMap[props.mode]
})

// 年度选项
const yearOptions = computed(() => {
  const currentYear = new Date().getFullYear()
  const years = []
  for (let i = currentYear - 5; i <= currentYear + 5; i++) {
    years.push(i)
  }
  return years
})

// 上传配置
const uploadUrl = computed(() => `${import.meta.env.VITE_API_BASE_URL}/api/common/upload`)
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${localStorage.getItem('token')}`
}))

// 表单数据
const formData = reactive<Partial<EstablishmentPlan>>({
  planYear: new Date().getFullYear(),
  planName: '',
  cycleType: PlanCycleType.ANNUAL,
  startDate: '',
  endDate: '',
  institutionId: '',
  institutionName: '',
  positionCategory: undefined,
  approvedCount: 0,
  budgetedCount: undefined,
  effectiveDate: '',
  staffingDocumentUrl: '',
  remark: '',
  approvalStatus: EstablishmentApprovalStatus.DRAFT
})

// 表单验证规则
const formRules: FormRules = {
  planYear: [
    { required: true, message: '请选择规划年度', trigger: 'change' }
  ],
  planName: [
    { required: true, message: '请输入规划名称', trigger: 'blur' }
  ],
  cycleType: [
    { required: true, message: '请选择周期类型', trigger: 'change' }
  ],
  startDate: [
    { required: true, message: '请选择开始日期', trigger: 'change' }
  ],
  endDate: [
    { required: true, message: '请选择结束日期', trigger: 'change' },
    {
      validator: (rule, value, callback) => {
        if (value && formData.startDate && value < formData.startDate) {
          callback(new Error('结束日期不能早于开始日期'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  approvedCount: [
    { required: true, message: '请输入核定编制数', trigger: 'blur' }
  ],
  effectiveDate: [
    { required: true, message: '请选择生效日期', trigger: 'change' }
  ]
}

// 获取审批状态标签
const getApprovalStatusLabel = (status: EstablishmentApprovalStatus) => {
  const option = approvalStatusOptions.find(item => item.value === status)
  return option?.label || status
}

// 获取审批状态类型
const getApprovalStatusType = (status: EstablishmentApprovalStatus) => {
  const typeMap: Record<EstablishmentApprovalStatus, string> = {
    DRAFT: 'info',
    PENDING: 'warning',
    APPROVED: 'success',
    REJECTED: 'danger'
  }
  return typeMap[status] || ''
}

// 获取编制规划详情
const fetchPlanDetail = async () => {
  if (!props.planId) return

  loading.value = true
  try {
    const plan = await establishmentStore.fetchPlanDetail(props.planId)
    
    // 填充表单数据
    Object.assign(formData, plan)
    
    // 处理文件列表
    if (plan.staffingDocumentUrl) {
      fileList.value = [{
        name: 'HrHr编制文件',
        url: plan.staffingDocumentUrl
      }]
    }
  } catch (__error) {
    console.error('获取编制规划详情失败:', error)
    ElMessage.error('获取编制规划详情失败')
  } finally {
    loading.value = false
  }
}

// 处理周期类型变更
const handleCycleTypeChange = (type: PlanCycleType) => {
  const currentYear = new Date().getFullYear()
  
  switch (type) {
    case PlanCycleType.ANNUAL:
      formData.startDate = `${currentYear}-01-01`
      formData.endDate = `${currentYear}-12-31`
      break
    case PlanCycleType.FIVE_YEAR:
      formData.startDate = `${currentYear}-01-01`
      formData.endDate = `${currentYear + 4}-12-31`
      break
    case PlanCycleType.TEN_YEAR:
      formData.startDate = `${currentYear}-01-01`
      formData.endDate = `${currentYear + 9}-12-31`
      break
  }
}

// 处理机构变更
   
const handleOrgChange = (orgId: string, orgData: unknown) => {
  formData.institutionName = orgData?.name || ''
}

// 文件上传成功
   
const handleUploadSuccess = (response: unknown) => {
  if (response.code === 200) {
    formData.staffingDocumentUrl = response.data.url
    ElMessage.success('文件上传成功')
  } else {
    ElMessage.error(response.message || '文件上传失败')
  }
}

// 文件移除
const handleRemove = () => {
  formData.staffingDocumentUrl = ''
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (!valid) return

    submitting.value = true
    try {
      if (props.mode === 'create') {
        await establishmentStore.createPlan(formData)
        ElMessage.success('编制规划创建成功')
      } else {
        await establishmentStore.updatePlan(props.planId, formData)
        ElMessage.success('编制规划更新成功')
      }

      dialogVisible.value = false
      emit('success')
    } catch (__error) {
      console.error('保存编制规划失败:', error)
    } finally {
      submitting.value = false
    }
  })
}

// 取消
const handleCancel = () => {
  dialogVisible.value = false
}

// 对话框关闭
const handleClosed = () => {
  formRef.value?.resetFields()
  fileList.value = []
  
  // 重置表单数据
  Object.assign(formData, {
    planYear: new Date().getFullYear(),
    planName: '',
    cycleType: PlanCycleType.ANNUAL,
    startDate: '',
    endDate: '',
    institutionId: '',
    institutionName: '',
    positionCategory: undefined,
    approvedCount: 0,
    budgetedCount: undefined,
    effectiveDate: '',
    staffingDocumentUrl: '',
    remark: '',
    approvalStatus: EstablishmentApprovalStatus.DRAFT
  })
}

// 监听对话框显示
watch(dialogVisible, (val) => {
  if (val && props.mode !== 'create') {
    fetchPlanDetail()
  }
})
</script>

<style lang="scss" scoped>
.upload-demo {
  width: 100%;
}
</style>