<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    append-to-body
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :disabled="isViewMode"
      label-width="100px"
    >
      <el-form-item label="规则名称" prop="ruleName">
        <el-input
          v-model="formData.ruleName"
          placeholder="请输入规则名称"
          maxlength="50"
          show-word-limit
          />
      </el-form-item>

      <el-form-item label="预警类型" prop="warningType">
        <el-select
          v-model="formData.warningType"
          placeholder="请选择预警类型"
          style="width: 100%"
          @change="handleTypeChange"
        >
          <el-option label="超编预警" value="OVERSTAFFED"  />
          <el-option label="缺编预警" value="UNDERSTAFFED"  />
          <el-option label="高使用率预警" value="HIGH_USAGE"  />
          <el-option label="低使用率预警" value="LOW_USAGE"  />
          <el-option label="接近上限预警" value="NEAR_LIMIT"  />
        </el-select>
      </el-form-item>

      <el-form-item label="适用范围" prop="scope">
        <el-radio-group v-model="formData.scope">
          <el-radio value="ALL">全部机构</el-radio>
          <el-radio value="SPECIFIC">指定机构</el-radio>
          <el-radio value="CATEGORY">指定类别</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item 
        v-if="formData.scope === 'SPECIFIC'" 
        label="选择机构" 
        prop="institutionIds"
      >
        <HrOrgPicker
          v-model="formData.institutionIds"
          placeholder="请选择机构"
          multiple
          collapse-tags
          clearable
        />
      </el-form-item>

      <el-form-item 
        v-if="formData.scope === 'CATEGORY'" 
        label="岗位类别" 
        prop="positionCategories"
      >
        <el-select
          v-model="formData.positionCategories"
          placeholder="请选择岗位类别"
          multiple
          style="width: 100%"
        >
          <el-option
            v-for="item in establishmentTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
           />
        </el-select>
      </el-form-item>

      <el-form-item label="触发条件" required>
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item prop="metric">
              <el-select v-model="formData.metric" placeholder="指标">
                <el-option label="使用率" value="UTILIZATION_RATE"  />
                <el-option label="实际在编数" value="ACTUAL_COUNT"  />
                <el-option label="可用编制数" value="AVAILABLE_COUNT"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="operator">
              <el-select v-model="formData.operator" placeholder="条件">
                <el-option label="大于" value="GT"  />
                <el-option label="大于等于" value="GTE"  />
                <el-option label="小于" value="LT"  />
                <el-option label="小于等于" value="LTE"  />
                <el-option label="等于" value="EQ"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="threshold">
              <el-input-number
                v-model="formData.threshold"
                :min="0"
                :max="formData.metric === 'UTILIZATION_RATE' ? 200 : 9999"
                :precision="formData.metric === 'UTILIZATION_RATE' ? 1 : 0"
                placeholder="阈值"
                style="width: 100%"
              >
                <template #append v-if="formData.metric === 'UTILIZATION_RATE'">%</template>
              </el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form-item>

      <el-form-item label="预警级别" prop="warningLevel">
        <el-radio-group v-model="formData.warningLevel">
          <el-radio :value="1">
            <el-tag type="danger" size="small">1级（严重）</el-tag>
          </el-radio>
          <el-radio :value="2">
            <el-tag type="warning" size="small">2级（警告）</el-tag>
          </el-radio>
          <el-radio :value="3">
            <el-tag type="info" size="small">3级（提示）</el-tag>
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="通知方式" prop="notifyMethods">
        <el-checkbox-group v-model="formData.notifyMethods">
          <el-checkbox value="SYSTEM">系统消息</el-checkbox>
          <el-checkbox value="EMAIL">邮件通知</el-checkbox>
          <el-checkbox value="SMS">短信通知</el-checkbox>
          <el-checkbox value="WECHAT">微信通知</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item label="通知对象" prop="notifyRoles">
        <el-select
          v-model="formData.notifyRoles"
          placeholder="请选择通知角色"
          multiple
          style="width: 100%"
        >
          <el-option label="系统管理员" value="ADMIN"  />
          <el-option label="人事主管" value="HR_MANAGER"  />
          <el-option label="机构负责人" value="INSTITUTION_LEADER"  />
          <el-option label="编制管理员" value="ESTABLISHMENT_MANAGER"  />
        </el-select>
      </el-form-item>

      <el-form-item label="处理建议" prop="suggestion">
        <el-input
          v-model="formData.suggestion"
          type="textarea"
          :rows="3"
          placeholder="请输入预警触发后的处理建议"
          maxlength="200"
          show-word-limit
          />
      </el-form-item>

      <el-form-item label="启用状态" prop="status">
        <el-switch
          v-model="formData.status"
          active-value="ACTIVE"
          inactive-value="INACTIVE"
          active-text="启用"
          inactive-text="停用"
         />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button v-if="!isViewMode" type="primary" @click="handleSubmit">
        {{ mode === 'create' ? '创建' : '保存' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'WarningRuleDialog'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { establishmentTypeOptions } from '@/types/establishment'
import HrOrgPicker from '@/components/organization/HrOrgPicker.vue'
import { useEstablishmentStore } from '@/stores/modules/establishment'

// 组件属性
interface Props {
  modelValue: boolean
  ruleId?: string
  mode: 'create' | 'edit' | 'view'
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'create'
})

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'success': []
}>()

// Store
const establishmentStore = useEstablishmentStore()

// 响应式数据
const formRef = ref<FormInstance>()
const loading = ref(false)

// 表单数据
const formData = reactive({
  ruleName: '',
  warningType: '',
  scope: 'ALL',
  institutionIds: [] as string[],
  positionCategories: [] as string[],
  metric: 'UTILIZATION_RATE',
  operator: 'GT',
  threshold: 0,
  warningLevel: 2,
  notifyMethods: ['SYSTEM'] as string[],
  notifyRoles: ['ESTABLISHMENT_MANAGER'] as string[],
  suggestion: '',
  status: 'ACTIVE'
})

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const dialogTitle = computed(() => {
  const titles = {
    create: '新增预警规则',
    edit: '编辑预警规则',
    view: '查看预警规则'
  }
  return titles[props.mode]
})

const isViewMode = computed(() => props.mode === 'view')

// 表单验证规则
const formRules = reactive<FormRules>({
  ruleName: [
    { required: true, message: '请输入规则名称', trigger: 'blur' },
    { min: 2, max: 50, message: '规则名称长度在2-50个字符', trigger: 'blur' }
  ],
  warningType: [
    { required: true, message: '请选择预警类型', trigger: 'change' }
  ],
  scope: [
    { required: true, message: '请选择适用范围', trigger: 'change' }
  ],
  institutionIds: [
    {
      type: 'array',
      required: true,
      message: '请选择机构',
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (formData.scope === 'SPECIFIC' && (!value || value.length === 0)) {
          callback(new Error('请选择机构'))
        } else {
          callback()
        }
      }
    }
  ],
  positionCategories: [
    {
      type: 'array',
      required: true,
      message: '请选择岗位类别',
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (formData.scope === 'CATEGORY' && (!value || value.length === 0)) {
          callback(new Error('请选择岗位类别'))
        } else {
          callback()
        }
      }
    }
  ],
  metric: [
    { required: true, message: '请选择监控指标', trigger: 'change' }
  ],
  operator: [
    { required: true, message: '请选择比较条件', trigger: 'change' }
  ],
  threshold: [
    { required: true, message: '请输入阈值', trigger: 'blur' },
    { type: 'number', min: 0, message: '阈值必须大于等于0', trigger: 'blur' }
  ],
  warningLevel: [
    { required: true, message: '请选择预警级别', trigger: 'change' }
  ],
  notifyMethods: [
    {
      type: 'array',
      required: true,
      message: '请选择通知方式',
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (!value || value.length === 0) {
          callback(new Error('请至少选择一种通知方式'))
        } else {
          callback()
        }
      }
    }
  ],
  notifyRoles: [
    {
      type: 'array',
      required: true,
      message: '请选择通知对象',
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (!value || value.length === 0) {
          callback(new Error('请至少选择一个通知对象'))
        } else {
          callback()
        }
      }
    }
  ],
  suggestion: [
    { required: true, message: '请输入处理建议', trigger: 'blur' },
    { min: 10, max: 200, message: '处理建议长度在10-200个字符', trigger: 'blur' }
  ]
})

// 处理预警类型变更
const handleTypeChange = () => {
  // 根据预警类型设置默认值
  const typeDefaults: Record<string, unknown> = {
    OVERSTAFFED: {
      metric: 'UTILIZATION_RATE',
      operator: 'GT',
      threshold: 100,
      warningLevel: 1,
      suggestion: '机构已超编，建议调整人员配置或申请增加编制'
    },
    UNDERSTAFFED: {
      metric: 'UTILIZATION_RATE',
      operator: 'LT',
      threshold: 80,
      warningLevel: 2,
      suggestion: '编制使用率偏低，建议评估实际需求或加强人员引进'
    },
    HIGH_USAGE: {
      metric: 'UTILIZATION_RATE',
      operator: 'GTE',
      threshold: 95,
      warningLevel: 2,
      suggestion: '编制使用率较高，建议关注人员变动情况，及时申请调整'
    },
    LOW_USAGE: {
      metric: 'UTILIZATION_RATE',
      operator: 'LT',
      threshold: 50,
      warningLevel: 3,
      suggestion: '编制使用率过低，存在资源浪费，建议优化编制配置'
    },
    NEAR_LIMIT: {
      metric: 'AVAILABLE_COUNT',
      operator: 'LTE',
      threshold: 2,
      warningLevel: 2,
      suggestion: '可用编制即将用完，建议提前做好编制规划'
    }
  }

  const defaults = typeDefaults[formData.warningType]
  if (defaults) {
    Object.assign(formData, defaults)
  }
}

// 初始化表单
const initForm = async () => {
  if (props.mode !== 'create' && props.ruleId) {
    loading.value = true
    try {
      // 获取规则详情
      const ruleDetail = await establishmentStore.fetchWarningRuleDetail(props.ruleId)
      
      // 填充表单数据
      Object.assign(formData, {
        ruleName: ruleDetail.ruleName,
        warningType: ruleDetail.warningType,
        scope: ruleDetail.scope,
        institutionIds: ruleDetail.institutionIds || [],
        positionCategories: ruleDetail.positionCategories || [],
        metric: ruleDetail.metric,
        operator: ruleDetail.operator,
        threshold: ruleDetail.threshold,
        warningLevel: ruleDetail.warningLevel,
        notifyMethods: ruleDetail.notifyMethods,
        notifyRoles: ruleDetail.notifyRoles,
        suggestion: ruleDetail.suggestion || '',
        status: ruleDetail.status
      })
    } catch (__error) {
      console.error('加载规则详情失败:', error)
      ElMessage.error('加载规则详情失败')
    } finally {
      loading.value = false
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (!valid) return

    loading.value = true
    try {
      // 构建提交数据
      const submitData = {
        ruleName: formData.ruleName,
        warningType: formData.warningType,
        scope: formData.scope,
        institutionIds: formData.scope === 'SPECIFIC' ? formData.institutionIds : undefined,
        positionCategories: formData.scope === 'CATEGORY' ? formData.positionCategories : undefined,
        metric: formData.metric,
        operator: formData.operator,
        threshold: formData.threshold,
        warningLevel: formData.warningLevel,
        notifyMethods: formData.notifyMethods,
        notifyRoles: formData.notifyRoles,
        suggestion: formData.suggestion,
        status: formData.status
      }

      if (props.mode === 'create') {
        // 创建规则
        await establishmentStore.createWarningRule(submitData)
        ElMessage.success('创建成功')
      } else {
        // 更新规则
        await establishmentStore.updateWarningRule(props.ruleId!, submitData)
        ElMessage.success('保存成功')
      }

      emit('success')
      dialogVisible.value = false
    } catch (__error) {
      console.error('操作失败:', error)
      ElMessage.error(props.mode === 'create' ? '创建失败' : '保存失败')
    } finally {
      loading.value = false
    }
  })
}

// 取消
const handleCancel = () => {
  dialogVisible.value = false
}

// 监听对话框显示状态
watch(dialogVisible, (val) => {
  if (val) {
    initForm()
  } else {
    // 重置表单
    formRef.value?.resetFields()
    Object.assign(formData, {
      ruleName: '',
      warningType: '',
      scope: 'ALL',
      institutionIds: [],
      positionCategories: [],
      metric: 'UTILIZATION_RATE',
      operator: 'GT',
      threshold: 0,
      warningLevel: 2,
      notifyMethods: ['SYSTEM'],
      notifyRoles: ['ESTABLISHMENT_MANAGER'],
      suggestion: '',
      status: 'ACTIVE'
    })
  }
})
</script>

<style lang="scss" scoped>
:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-radio-group) {
  .el-radio {
    margin-right: 20px;
  }
}
</style>