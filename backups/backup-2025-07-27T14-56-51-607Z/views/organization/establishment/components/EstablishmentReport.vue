<template>
  <div class="establishment-report">
    <!-- 查询条件 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm">
        <el-form-item label="报表类型">
          <el-select
            v-model="searchForm.reportType"
            placeholder="请选择报表类型"
            @change="handleReportTypeChange"
          >
            <el-option label="编制执行情况报表" value="execution"  />
            <el-option label="编制使用分析报表" value="utilization"  />
            <el-option label="编制变动明细报表" value="change"  />
            <el-option label="编制预警报表" value="warning"  />
            <el-option label="编制对比分析报表" value="comparison"  />
          </el-select>
        </el-form-item>
        
        <el-form-item label="统计时间">
          <el-date-picker
            v-if="searchForm.reportType !== 'comparison'"
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
           />
          <el-date-picker
            v-else
            v-model="searchForm.comparisonDates"
            type="month"
            placeholder="选择对比月份"
            value-format="YYYY-MM"
            multiple
            :multiple-limit="6"
           />
        </el-form-item>

        <el-form-item label="机构范围">
          <HrOrgPicker
            v-model="searchForm.institutionIds"
            placeholder="请选择机构"
            multiple
            collapse-tags
            clearable
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleGenerate">生成报表</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 报表内容 -->
    <el-card v-if="reportData" class="report-card">
      <template #header>
        <div class="report-header">
          <span>{{ reportTitle }}</span>
          <div class="report-actions">
            <el-button size="small" @click="handlePrint">
              <el-icon><Printer /></el-icon>
              打印
            </el-button>
            <el-button size="small" @click="handleExport">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
          </div>
        </div>
      </template>

      <!-- 报表信息 -->
      <div class="report-info">
        <el-row>
          <el-col :span="8">
            <span class="info-label">统计时间：</span>
            <span class="info-value">{{ reportTimeRange }}</span>
          </el-col>
          <el-col :span="8">
            <span class="info-label">生成时间：</span>
            <span class="info-value">{{ reportData.generateTime }}</span>
          </el-col>
          <el-col :span="8">
            <span class="info-label">生成人：</span>
            <span class="info-value">{{ reportData.generateUser }}</span>
          </el-col>
        </el-row>
      </div>

      <!-- 执行情况报表 -->
      <div v-if="searchForm.reportType === 'execution'" class="report-content">
        <el-table :data="reportData.data" border show-summary :summary-method="getSummaries">
          <el-table-column prop="institutionName" label="机构名称" min-width="150" fixed  />
          <el-table-column label="管理岗" align="center">
            <el-table-column prop="management.planned" label="计划" width="80" align="right"  />
            <el-table-column prop="management.approved" label="核定" width="80" align="right"  />
            <el-table-column prop="management.actual" label="实际" width="80" align="right"  />
            <el-table-column prop="management.executionRate" label="执行率" width="80" align="right">
              <template #default="{ row }">
                <span :class="getRateClass(row.management.executionRate)">
                  {{ row.management.executionRate }}%
                </span>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="专技岗" align="center">
            <el-table-column prop="professional.planned" label="计划" width="80" align="right"  />
            <el-table-column prop="professional.approved" label="核定" width="80" align="right"  />
            <el-table-column prop="professional.actual" label="实际" width="80" align="right"  />
            <el-table-column prop="professional.executionRate" label="执行率" width="80" align="right">
              <template #default="{ row }">
                <span :class="getRateClass(row.professional.executionRate)">
                  {{ row.professional.executionRate }}%
                </span>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="工勤岗" align="center">
            <el-table-column prop="worker.planned" label="计划" width="80" align="right"  />
            <el-table-column prop="worker.approved" label="核定" width="80" align="right"  />
            <el-table-column prop="worker.actual" label="实际" width="80" align="right"  />
            <el-table-column prop="worker.executionRate" label="执行率" width="80" align="right">
              <template #default="{ row }">
                <span :class="getRateClass(row.worker.executionRate)">
                  {{ row.worker.executionRate }}%
                </span>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
        
        <!-- 执行情况图表 -->
        <div ref="executionChart" class="report-chart"></div>
      </div>

      <!-- 使用分析报表 -->
      <div v-else-if="searchForm.reportType === 'utilization'" class="report-content">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="使用率分析" name="rate">
            <div ref="utilizationChart" class="report-chart"></div>
          </el-tab-pane>
          <el-tab-pane label="缺编/超编分析" name="shortage">
            <div ref="shortageChart" class="report-chart"></div>
          </el-tab-pane>
          <el-tab-pane label="使用趋势" name="trend">
            <div ref="trendChart" class="report-chart"></div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 变动明细报表 -->
      <div v-else-if="searchForm.reportType === 'change'" class="report-content">
        <el-table :data="reportData.data" border>
          <el-table-column prop="changeDate" label="变动日期" width="110"  />
          <el-table-column prop="institutionName" label="机构名称" min-width="150"  />
          <el-table-column prop="changeType" label="变动类型" width="100">
            <template #default="{ row }">
              <el-tag :type="getChangeTypeTag(row.changeType)">
                {{ getChangeTypeLabel(row.changeType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="positionCategory" label="岗位类别" width="100"  />
          <el-table-column prop="originalCount" label="原编制" width="80" align="right"  />
          <el-table-column prop="changeCount" label="变动数" width="80" align="right">
            <template #default="{ row }">
              <span :class="row.changeCount > 0 ? 'text-success' : 'text-danger'">
                {{ row.changeCount > 0 ? '+' : '' }}{{ row.changeCount }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="currentCount" label="现编制" width="80" align="right"  />
          <el-table-column prop="changeReason" label="变动原因" min-width="200" show-overflow-tooltip  />
          <el-table-column prop="approver" label="审批人" width="100"  />
        </el-table>
      </div>

      <!-- 预警报表 -->
      <div v-else-if="searchForm.reportType === 'warning'" class="report-content">
        <el-alert
          title="编制预警统计"
          type="warning"
          :description="`共有 ${reportData.warningCount} 个机构触发编制预警`"
          show-icon
          :closable="false"
          style="margin-bottom: 20px"
         />
        
        <el-table :data="reportData.data" border>
          <el-table-column prop="institutionName" label="机构名称" min-width="150"  />
          <el-table-column prop="warningType" label="预警类型" width="120">
            <template #default="{ row }">
              <el-tag :type="getWarningTypeTag(row.warningType)">
                {{ getWarningTypeLabel(row.warningType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="positionCategory" label="岗位类别" width="100"  />
          <el-table-column prop="approved" label="核定编制" width="90" align="right"  />
          <el-table-column prop="actual" label="实际在编" width="90" align="right"  />
          <el-table-column prop="utilizationRate" label="使用率" width="90" align="right">
            <template #default="{ row }">
              <span :class="getWarningClass(row.utilizationRate)">
                {{ row.utilizationRate }}%
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="warningLevel" label="预警等级" width="90">
            <template #default="{ row }">
              <el-tag :type="getWarningLevelTag(row.warningLevel)" size="small">
                {{ row.warningLevel }}级
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="suggestion" label="处理建议" min-width="200" show-overflow-tooltip  />
        </el-table>
      </div>

      <!-- 对比分析报表 -->
      <div v-else-if="searchForm.reportType === 'comparison'" class="report-content">
        <div ref="comparisonChart" class="report-chart"></div>
        
        <el-table :data="reportData.data" border style="margin-top: 20px">
          <el-table-column prop="institutionName" label="机构名称" min-width="150" fixed  />
          <el-table-column 
            v-for="month in searchForm.comparisonDates" 
            :key="month"
            :label="month"
            align="center"
          >
            <el-table-column :prop="`data.${month}.approved`" label="核定" width="80" align="right"  />
            <el-table-column :prop="`data.${month}.actual`" label="在编" width="80" align="right"  />
            <el-table-column :prop="`data.${month}.rate`" label="使用率" width="90" align="right">
              <template #default="{ row }">
                <span>{{ row.data[month]?.rate || 0 }}%</span>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Printer, Download } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { useEstablishmentStore } from '@/stores/modules/establishment'
import HrOrgPicker from '@/components/organization/HrOrgPicker.vue'

// 状态管理
const establishmentStore = useEstablishmentStore()

// 响应式数据
const searchForm = reactive({
  reportType: 'execution',
  dateRange: [] as string[],
  comparisonDates: [] as string[],
  institutionIds: [] as string[]
})

const loading = ref(false)
const reportData = ref<unknown>(null)
const activeTab = ref('rate')

// 图表实例
let executionChartInstance: echarts.ECharts | null = null
let utilizationChartInstance: echarts.ECharts | null = null
let shortageChartInstance: echarts.ECharts | null = null
let trendChartInstance: echarts.ECharts | null = null
let comparisonChartInstance: echarts.ECharts | null = null

// refs
const executionChart = ref<HTMLDivElement>()
const utilizationChart = ref<HTMLDivElement>()
const shortageChart = ref<HTMLDivElement>()
const trendChart = ref<HTMLDivElement>()
const comparisonChart = ref<HTMLDivElement>()

// 计算属性
const reportTitle = computed(() => {
  const titles: Record<string, string> = {
    execution: '编制执行情况报表',
    utilization: '编制使用分析报表',
    change: '编制变动明细报表',
    warning: '编制预警报表',
    comparison: '编制对比分析报表'
  }
  return titles[searchForm.reportType] || ''
})

const reportTimeRange = computed(() => {
  if (searchForm.reportType === 'comparison') {
    return searchForm.comparisonDates.join('、')
  }
  if (searchForm.dateRange?.length === 2) {
    return `${searchForm.dateRange[0]} 至 ${searchForm.dateRange[1]}`
  }
  return '全部时间'
})

// 获取使用率样式类
const getRateClass = (rate: number) => {
  if (rate >= 95) return 'text-danger'
  if (rate >= 80) return 'text-warning'
  return 'text-success'
}

// 获取变动类型标签
const getChangeTypeLabel = (type: string) => {
  const labelMap: Record<string, string> = {
    INCREASE: '增加',
    DECREASE: '减少',
    TRANSFER_IN: '划入',
    TRANSFER_OUT: '划出',
    ADJUSTMENT: '调整'
  }
  return labelMap[type] || type
}

const getChangeTypeTag = (type: string) => {
  const tagMap: Record<string, string> = {
    INCREASE: 'success',
    DECREASE: 'danger',
    TRANSFER_IN: 'success',
    TRANSFER_OUT: 'warning',
    ADJUSTMENT: 'info'
  }
  return tagMap[type] || ''
}

// 获取预警类型标签
const getWarningTypeLabel = (type: string) => {
  const labelMap: Record<string, string> = {
    OVERSTAFFED: '超编预警',
    UNDERSTAFFED: '缺编预警',
    HIGH_USAGE: '高使用率预警',
    LOW_USAGE: '低使用率预警'
  }
  return labelMap[type] || type
}

const getWarningTypeTag = (type: string) => {
  const tagMap: Record<string, string> = {
    OVERSTAFFED: 'danger',
    UNDERSTAFFED: 'warning',
    HIGH_USAGE: 'danger',
    LOW_USAGE: 'info'
  }
  return tagMap[type] || ''
}

const getWarningClass = (rate: number) => {
  if (rate > 100) return 'text-danger'
  if (rate > 90) return 'text-warning'
  if (rate < 50) return 'text-info'
  return ''
}

const getWarningLevelTag = (level: number) => {
  if (level === 1) return 'danger'
  if (level === 2) return 'warning'
  return 'info'
}

// 获取汇总数据
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const getSummaries = (param: unknown) => {
  const {columns: _columns, data} =  param
  const sums: string[]  i >= 0; i--) {
    const date = new Date()
    date.setMonth(date.getMonth() - i)
    months.push(date.toLocaleDateString('zh-CN', { year: 'numeric', month: 'short' }))
    
    const base = 500
    const approved = base + Math.floor(Math.random() * 20)
    const actual = base - 20 + Math.floor(Math.random() * 30)
    
    approvedData.push(approved)
    actualData.push(actual)
    rateData.push(Math.round((actual / approved) * 100))
  }

  trendChartInstance.setOption({
    title: {
      text: '编制使用趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['核定编制', '实际在编', '使用率'],
      top: 30
    },
    xAxis: {
      type: 'category',
      data: months
    },
    yAxis: [
      {
        type: 'value',
        name: 'HrHr人数',
        min: 450,
        max: 550
      },
      {
        type: 'value',
        name: '使用率（%）',
        min: 80,
        max: 100,
        axisLabel: {
          formatter: '{value}%'
        }
      }
    ],
    series: [
      {
        name: '核定编制',
        type: 'line',
        data: approvedData,
        itemStyle: { color: '#409eff' }
      },
      {
        name: '实际在编',
        type: 'line',
        data: actualData,
        itemStyle: { color: '#67c23a' }
      },
      {
        name: '使用率',
        type: 'line',
        yAxisIndex: 1,
        data: rateData,
        itemStyle: { color: '#e6a23c' }
      }
    ]
  })
}

// 更新对比分析图表
const updateComparisonChart = () => {
  if (!comparisonChartInstance) return

  const months = searchForm.comparisonDates
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const institutions = reportData.value.data.map((item: unknown) => item.institutionName)
  
  const series = months.map((month, index) => ({
    name: month,
    type: 'bar',
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
    data: reportData.value.data.map((item: unknown) => item.data[month]?.rate || 0)
  }))

  comparisonChartInstance.setOption({
    title: {
      text: '编制使用率月度对比',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: months,
      top: 30
    },
    xAxis: {
      type: 'category',
      data: institutions
    },
    yAxis: {
      type: 'value',
      name: '使用率（%）',
      max: 120
    },
    series
  })
}

// 重置
const handleReset = () => {
  searchForm.reportType = 'execution'
  searchForm.dateRange = []
  searchForm.comparisonDates = []
  searchForm.institutionIds = []
  reportData.value = null
}

// 打印
const handlePrint = () => {
  window.print()
}

// 导出
const handleExport = () => {
  ElMessage.info('导出功能开发中')
}

// 监听窗口大小变化
window.addEventListener('resize', () => {
  executionChartInstance?.resize()
  utilizationChartInstance?.resize()
  shortageChartInstance?.resize()
  trendChartInstance?.resize()
  comparisonChartInstance?.resize()
})
</script>

<style lang="scss" scoped>
.establishment-report {
  .search-card {
    margin-bottom: 20px;
  }

  .report-card {
    .report-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .report-actions {
        display: flex;
        gap: 10px;
      }
    }

    .report-info {
      padding: 15px;
      background-color: #f5f7fa;
      border-radius: 4px;
      margin-bottom: 20px;

      .info-label {
        color: #909399;
        margin-right: 8px;
      }

      .info-value {
        color: #303133;
        font-weight: 500;
      }
    }

    .report-content {
      .report-chart {
        width: 100%;
        height: 400px;
        margin: 20px 0;
      }

      .text-success {
        color: #67c23a;
      }

      .text-danger {
        color: #f56c6c;
      }

      .text-warning {
        color: #e6a23c;
      }

      .text-info {
        color: #909399;
      }
    }
  }
}

@media print {
  .search-card,
  .report-actions {
    display: none !important;
  }
}
</style>