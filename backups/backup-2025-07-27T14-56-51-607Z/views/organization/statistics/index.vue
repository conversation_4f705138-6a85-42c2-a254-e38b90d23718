<template>
  <div class="organization-statistics">
    <!-- 顶部统计卡片 -->
    <el-row :gutter="20" class="statistics-cards">
      <el-col :xs="24" :sm="12" :md="6" v-for="card in statisticsCards" :key="card.key">
        <el-card class="statistics-card" :class="`card-${card.type}`">
          <div class="card-icon">
            <el-icon :size="32">
              <component :is="card.icon" />
            </el-icon>
          </div>
          <div class="card-content">
            <div class="card-value">{{ card.value }}</div>
            <div class="card-label">{{ card.label }}</div>
            <div class="card-trend" v-if="card.trend">
              <el-icon :class="card.trend > 0 ? 'trend-up' : 'trend-down'">
                <ArrowUp v-if="card.trend > 0" />
                <ArrowDown v-else />
              </el-icon>
              <span>{{ Math.abs(card.trend) }}%</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 功能标签页 -->
    <el-tabs v-model="activeTab" class="statistics-tabs">
      <!-- 部门人员统计 -->
      <el-tab-pane label="部门人员统计" name="department">
        <DepartmentStatistics
          v-if="activeTab === 'department'"
          :data="statisticsData.department"
          @refresh="handleRefresh('department')"
        />
      </el-tab-pane>

      <!-- 岗位分布统计 -->
      <el-tab-pane label="岗位分布统计" name="position">
        <PositionStatistics
          v-if="activeTab === 'position'"
          :data="statisticsData.position"
          @refresh="handleRefresh('position')"
        />
      </el-tab-pane>

      <!-- 编制使用统计 -->
      <el-tab-pane label="编制使用统计" name="establishment">
        <EstablishmentStatistics
          v-if="activeTab === 'establishment'"
          :data="statisticsData.establishment"
          @refresh="handleRefresh('establishment')"
        />
      </el-tab-pane>

      <!-- 人员结构分析 -->
      <el-tab-pane label="人员结构分析" name="structure">
        <StructureAnalysis
          v-if="activeTab === 'structure'"
          :data="statisticsData.structure"
          @refresh="handleRefresh('structure')"
        />
      </el-tab-pane>

      <!-- 趋势分析 -->
      <el-tab-pane label="趋势分析" name="trend">
        <TrendAnalysis
          v-if="activeTab === 'trend'"
          :data="statisticsData.trend"
          @refresh="handleRefresh('trend')"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
 
defineOptions({
  name: 'StatisticsPage'
})

import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  User, 
  OfficeBuilding, 
  Position, 
  TrendCharts,
  ArrowUp,
  ArrowDown 
} from '@element-plus/icons-vue'
import { useOrganizationStore } from '@/stores/modules/organization'
import DepartmentStatistics from './components/DepartmentStatistics.vue'
import PositionStatistics from './components/PositionStatistics.vue'
import EstablishmentStatistics from './components/EstablishmentStatistics.vue'
import StructureAnalysis from './components/StructureAnalysis.vue'
import TrendAnalysis from './components/TrendAnalysis.vue'

// 使用组织管理Store
const organizationStore = useOrganizationStore()

// 当前激活的标签页
const activeTab = ref('department')

// 统计数据
const statisticsData = reactive({
  department: null,
  position: null,
  establishment: null,
  structure: null,
  trend: null
})

// 顶部统计卡片数据
const statisticsCards = computed(() => [
  {
    key: 'totalOrgs',
    type: 'primary',
    icon: OfficeBuilding,
    label: '组织机构总数',
    value: organizationStore.statistics?.totalOrganizations || 0,
    trend: organizationStore.statistics?.orgGrowthRate || 0
  },
  {
    key: 'totalEmployees',
    type: 'success',
    icon: User,
    label: '在职员工总数',
    value: organizationStore.statistics?.totalEmployees || 0,
    trend: organizationStore.statistics?.employeeGrowthRate || 0
  },
  {
    key: 'totalPositions',
    type: 'warning',
    icon: Position,
    label: '岗位总数',
    value: organizationStore.statistics?.totalPositions || 0,
    trend: organizationStore.statistics?.positionGrowthRate || 0
  },
  {
    key: 'establishmentRate',
    type: 'info',
    icon: TrendCharts,
    label: '编制使用率',
    value: `${organizationStore.statistics?.establishmentUsageRate || 0}%`,
    trend: organizationStore.statistics?.establishmentGrowthRate || 0
  }
])

// 加载统计数据
const loadStatistics = async () => {
  try {
    // 加载总体统计数据
    await organizationStore.fetchStatistics()
    
    // 根据当前标签页加载对应数据
    await handleRefresh(activeTab.value)
  } catch (__error) {
    ElMessage.error('加载统计数据失败')
  }
}

// 刷新指定类型的统计数据
const handleRefresh = async (type: string) => {
  try {
    switch (type) {
      case 'department':
        statisticsData.department = await organizationStore.fetchDepartmentStatistics()
        break
      case 'position':
        statisticsData.position = await organizationStore.fetchPositionStatistics()
        break
      case 'establishment':
        statisticsData.establishment = await organizationStore.fetchEstablishmentStatistics()
        break
      case 'structure':
        statisticsData.structure = await organizationStore.fetchStructureAnalysis()
        break
      case 'trend':
        statisticsData.trend = await organizationStore.fetchTrendAnalysis()
        break
    }
    ElMessage.success('数据刷新成功')
  } catch (__error) {
    ElMessage.error(`刷新${type}数据失败`)
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadStatistics()
})
</script>

<style lang="scss" scoped>
.organization-statistics {
  padding: 20px;

  // 统计卡片样式
  .statistics-cards {
    margin-bottom: 24px;

    .statistics-card {
      height: 120px;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      :deep(.el-card__body) {
        display: flex;
        align-items: center;
        height: 100%;
        padding: 20px;
      }

      .card-icon {
        margin-right: 16px;
        padding: 12px;
        border-radius: 8px;
        background-color: rgba(var(--el-color-primary-rgb), 0.1);
      }

      .card-content {
        flex: 1;

        .card-value {
          font-size: 28px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          line-height: 1.2;
        }

        .card-label {
          margin-top: 4px;
          font-size: 14px;
          color: var(--el-text-color-regular);
        }

        .card-trend {
          display: inline-flex;
          align-items: center;
          margin-top: 4px;
          font-size: 12px;

          .trend-up {
            color: var(--el-color-success);
          }

          .trend-down {
            color: var(--el-color-danger);
          }

          span {
            margin-left: 4px;
          }
        }
      }

      // 不同类型卡片的颜色
      &.card-primary .card-icon {
        background-color: rgba(var(--el-color-primary-rgb), 0.1);
        color: var(--el-color-primary);
      }

      &.card-success .card-icon {
        background-color: rgba(var(--el-color-success-rgb), 0.1);
        color: var(--el-color-success);
      }

      &.card-warning .card-icon {
        background-color: rgba(var(--el-color-warning-rgb), 0.1);
        color: var(--el-color-warning);
      }

      &.card-info .card-icon {
        background-color: rgba(var(--el-color-info-rgb), 0.1);
        color: var(--el-color-info);
      }
    }
  }

  // 标签页样式
  .statistics-tabs {
    background: #fff;
    padding: 20px;
    border-radius: 4px;

    :deep(.el-tabs__header) {
      margin-bottom: 20px;
    }
  }
}

// 响应式布局
@media (max-width: 768px) {
  .organization-statistics {
    padding: 12px;

    .statistics-card {
      margin-bottom: 12px;
    }
  }
}
</style>