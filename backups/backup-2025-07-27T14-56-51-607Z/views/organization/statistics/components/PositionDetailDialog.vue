<template>
  <el-dialog
    :model-value="modelValue"
    title="岗位详情"
    width="70%"
    @close="handleClose"
  >
    <div class="position-detail" v-if="position">
      <!-- 岗位基本信息 -->
      <el-card class="info-card">
        <template #header>
          <span>岗位基本信息</span>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="岗位名称">{{ position.positionName }}</el-descriptions-item>
          <el-descriptions-item label="岗位编码">{{ position.positionCode }}</el-descriptions-item>
          <el-descriptions-item label="岗位类别">
            <el-tag :type="getCategoryTagType(position.category)">
              {{ position.category }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="岗位级别">
            <el-tag :type="getLevelTagType(position.level)">
              {{ position.level }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="所属部门">{{ position.departmentName }}</el-descriptions-item>
          <el-descriptions-item label="直接上级">{{ position.superiorPosition || '无' }}</el-descriptions-item>
          <el-descriptions-item label="编制数量">{{ position.establishedCount }}</el-descriptions-item>
          <el-descriptions-item label="在岗人数">{{ position.actualCount }}</el-descriptions-item>
          <el-descriptions-item label="岗位职责" :span="2">
            <div class="responsibility-text">
              {{ position.responsibility || '暂无职责描述' }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="任职要求" :span="2">
            <div class="requirement-text">
              {{ position.requirement || '暂无任职要求' }}
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 在岗人员列表 -->
      <el-card class="employee-card">
        <template #header>
          <div class="card-header">
            <span>在岗人员列表</span>
            <el-tag type="info">共 {{ employees.length }} 人</el-tag>
          </div>
        </template>
        <el-table :data="employees" stripe max-height="300">
          <el-table-column prop="employeeId" label="工号" width="100"  />
          <el-table-column prop="name" label="姓名" width="100"  />
          <el-table-column prop="gender" label="性别" width="60"  />
          <el-table-column prop="education" label="学历" width="100"  />
          <el-table-column prop="entryDate" label="入职日期" width="120"  />
          <el-table-column prop="positionDate" label="任职日期" width="120"  />
          <el-table-column prop="status" label="状态" width="80">
            <template #default="{ row }">
              <el-tag :type="row.status === '在职' ? 'success' : 'info'">
                {{ row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="{ row }">
              <el-button link type="primary" @click="handleViewEmployee(row)">
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 历史变动记录 -->
      <el-card class="history-card">
        <template #header>
          <span>历史变动记录</span>
        </template>
        <el-timeline>
          <el-timeline-item
            v-for="(history, index) in historyList"
            :key="index"
            :timestamp="history.date"
            placement="top"
          >
            <el-card>
              <p>{{ history.content }}</p>
              <p class="history-operator">操作人：{{ history.operator }}</p>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>

    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="handleExport">导出岗位信息</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { exportToExcel } from '@/utils/export'

// Props
interface Props {
  modelValue: boolean
   
  position: unknown
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  close: []
}>()

// 在岗人员列表
const employees = ref<any[]>([])

// 历史变动记录
const historyList = ref<any[]>([])

// 监听岗位变化，加载数据
watch(() => props.position, (newVal) => {
  if (newVal) {
    loadPositionData()
  }
}, { immediate: true })

// 加载岗位数据
const loadPositionData = () => {
  // 模拟在岗人员数据
  employees.value = [
    {
      employeeId: 'E001',
      name: 'HrHr张三',
      gender: '男',
      education: '本科',
      entryDate: '2020-01-15',
      positionDate: '2021-03-01',
      status: '在职'
    },
    {
      employeeId: 'E002',
      name: '李四',
      gender: '女',
      education: '硕士',
      entryDate: '2019-06-20',
      positionDate: '2020-01-01',
      status: '在职'
    }
  ]

  // 模拟历史变动记录
  historyList.value = [
    {
      date: '2023-12-01 10:30:00',
      content: '岗位编制从8人调整为10人',
      operator: '王管理员'
    },
    {
      date: '2023-06-15 14:20:00',
      content: '更新岗位职责描述',
      operator: '李主管'
    },
    {
      date: '2023-01-10 09:00:00',
      content: '创建岗位',
      operator: '系统管理员'
    }
  ]
}

// 获取类别标签类型
const getCategoryTagType = (category: string) => {
  const types: Record<string, string> = {
    '管理岗': 'danger',
    '技术岗': 'primary',
    '业务岗': 'success',
    '行政岗': 'warning',
    '其他': 'info'
  }
  return types[category] || 'info'
}

// 获取级别标签类型
const getLevelTagType = (level: string) => {
  const types: Record<string, string> = {
    '高级': 'danger',
    '中级': 'warning',
    '初级': 'success'
  }
  return types[level] || 'info'
}

// 查看员工详情
   
const handleViewEmployee = (employee: unknown) => {
  // 实现查看员工详情逻辑
  ElMessage.info(`查看员工：${employee.name}`)
}

// 导出岗位信息
const handleExport = () => {
  // 准备导出数据
  const positionData = [
    ['岗位名称', props.position.positionName],
    ['岗位编码', props.position.positionCode],
    ['岗位类别', props.position.category],
    ['岗位级别', props.position.level],
    ['所属部门', props.position.departmentName],
    ['编制数量', props.position.establishedCount],
    ['在岗人数', props.position.actualCount],
    ['空缺数量', props.position.vacantCount],
    ['满编率', `${props.position.fillRate}%`]
  ]

  // 添加空行
  positionData.push(['', ''])
  positionData.push(['在岗人员列表', ''])
  positionData.push(['工号', '姓名', '性别', '学历', '入职日期', '任职日期', '状态'])

  // 添加人员数据
  employees.value.forEach(emp => {
    positionData.push([
      emp.employeeId,
      emp.name,
      emp.gender,
      emp.education,
      emp.entryDate,
      emp.positionDate,
      emp.status
    ])
  })

  // 使用特殊的导出格式
  const headers = positionData[0]
  const data = positionData.slice(1)
  
  exportToExcel(headers, data, `${props.position.positionName}岗位信息`)
  ElMessage.success('导出成功')
}

// 处理关闭
const handleClose = () => {
  emit('update:modelValue', false)
  emit('close')
}
</script>

<style lang="scss" scoped>
.position-detail {
  .info-card {
    margin-bottom: 20px;

    .responsibility-text,
    .requirement-text {
      white-space: pre-wrap;
      line-height: 1.6;
      color: #606266;
    }
  }

  .employee-card {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .history-card {
    :deep(.el-timeline) {
      padding-left: 0;
    }

    .history-operator {
      margin-top: 5px;
      font-size: 12px;
      color: #909399;
    }
  }
}
</style>