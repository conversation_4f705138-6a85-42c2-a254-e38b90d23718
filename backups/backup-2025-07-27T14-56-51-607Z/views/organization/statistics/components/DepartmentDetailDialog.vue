<template>
  <el-dialog
    :model-value="modelValue"
    title="部门人员详情"
    width="80%"
    @close="handleClose"
  >
    <div class="department-detail" v-if="department">
      <!-- 部门基本信息 -->
      <el-descriptions :column="3" border class="info-section">
        <el-descriptions-item label="部门名称">{{ department.departmentName }}</el-descriptions-item>
        <el-descriptions-item label="部门编码">{{ department.departmentCode }}</el-descriptions-item>
        <el-descriptions-item label="部门类型">{{ department.departmentType }}</el-descriptions-item>
        <el-descriptions-item label="总人数">{{ department.totalCount }}</el-descriptions-item>
        <el-descriptions-item label="正式员工">{{ department.regularCount }}</el-descriptions-item>
        <el-descriptions-item label="合同制">{{ department.contractCount }}</el-descriptions-item>
      </el-descriptions>

      <!-- 人员列表 -->
      <el-tabs v-model="activeTab" class="employee-tabs">
        <el-tab-pane label="全部人员" name="all">
          <el-table :data="employeeList" stripe height="400">
            <el-table-column prop="employeeId" label="工号" width="100"  />
            <el-table-column prop="name" label="姓名" width="100"  />
            <el-table-column prop="position" label="岗位" min-width="150"  />
            <el-table-column prop="employmentType" label="用工类型" width="100"  />
            <el-table-column prop="joinDate" label="入职日期" width="120"  />
            <el-table-column prop="workYears" label="工龄" width="80"  />
          </el-table>
        </el-tab-pane>

        <el-tab-pane label="按岗位分组" name="position">
          <div class="position-group">
            <el-collapse v-model="activePositions">
              <el-collapse-item
                v-for="(group, position) in positionGroups"
                :key="position"
                :title="`${position} (${group.length}人)`"
                :name="position"
              >
                <el-table :data="group" stripe>
                  <el-table-column prop="employeeId" label="工号" width="100"  />
                  <el-table-column prop="name" label="姓名" width="100"  />
                  <el-table-column prop="employmentType" label="用工类型" width="100"  />
                  <el-table-column prop="joinDate" label="入职日期" width="120"  />
                </el-table>
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-tab-pane>

        <el-tab-pane label="统计图表" name="charts">
          <el-row :gutter="20">
            <el-col :span="12">
              <div ref="positionChartRef" class="chart"></div>
            </el-col>
            <el-col :span="12">
              <div ref="ageChartRef" class="chart"></div>
            </el-col>
          </el-row>
        </el-tab-pane>
      </el-tabs>
    </div>

    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="handleExport">导出人员名单</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import { exportToExcel } from '@/utils/export'

// Props
interface Props {
  modelValue: boolean
   
  department: unknown
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  close: []
}>()

// 标签页
const activeTab = ref('all')
const activePositions = ref<string[]>([])

// 员工列表
const employeeList = ref<any[]>([])

// 图表引用
const positionChartRef = ref<HTMLDivElement>()
const ageChartRef = ref<HTMLDivElement>()

// 按岗位分组的数据
const positionGroups = computed(() => {
  const groups: Record<string, any[]> = {}
  employeeList.value.forEach(emp => {
    if (!groups[emp.position]) {
      groups[emp.position] = []
    }
    groups[emp.position].push(emp)
  })
  return groups
})

// 监听部门变化，加载数据
watch(() => props.department, (newVal) => {
  if (newVal) {
    loadEmployeeList()
  }
}, { immediate: true })

// 监听标签页切换，初始化图表
watch(activeTab, (newVal) => {
  if (newVal === 'charts') {
    nextTick(() => {
      initCharts()
    })
  }
})

// 加载员工列表
const loadEmployeeList = () => {
  // 模拟数据
  employeeList.value = [
    { employeeId: 'E001', name: 'HrHr张三', position: '高级工程师', employmentType: '正式', joinDate: '2020-01-15', workYears: 4 },
    { employeeId: 'E002', name: '李四', position: '工程师', employmentType: '正式', joinDate: '2021-03-20', workYears: 3 },
    { employeeId: 'E003', name: '王五', position: '初级工程师', employmentType: '合同制', joinDate: '2022-06-10', workYears: 2 },
    { employeeId: 'E004', name: '赵六', position: '部门经理', employmentType: '正式', joinDate: '2018-09-01', workYears: 6 },
    { employeeId: 'E005', name: '钱七', position: '高级工程师', employmentType: '正式', joinDate: '2019-11-15', workYears: 5 }
  ]
}

// 初始化图表
const initCharts = () => {
  // 岗位分布图
  if (positionChartRef.value) {
    const positionChart = echarts.init(positionChartRef.value)
    const positionData = Object.entries(positionGroups.value).map(([position, employees]) => ({
      name: position,
      value: employees.length
    }))
    
    positionChart.setOption({
      title: {
        text: '岗位分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item'
      },
      series: [{
        type: 'pie',
        radius: '50%',
        data: positionData
      }]
    })
  }

  // 工龄分布图
  if (ageChartRef.value) {
    const ageChart = echarts.init(ageChartRef.value)
    const ageGroups = {
      '0-2年': 0,
      '3-5年': 0,
      '6-10年': 0,
      '10年以上': 0
    }
    
    employeeList.value.forEach(emp => {
      if (emp.workYears <= 2) ageGroups['0-2年']++
      else if (emp.workYears <= 5) ageGroups['3-5年']++
      else if (emp.workYears <= 10) ageGroups['6-10年']++
      else ageGroups['10年以上']++
    })
    
    ageChart.setOption({
      title: {
        text: '工龄分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: Object.keys(ageGroups)
      },
      yAxis: {
        type: 'value'
      },
      series: [{
        type: 'bar',
        data: Object.values(ageGroups),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        }
      }]
    })
  }
}

// 处理关闭
const handleClose = () => {
  emit('update:modelValue', false)
  emit('close')
}

// 导出数据
const handleExport = () => {
  const headers = ['工号', '姓名', '岗位', '用工类型', '入职日期', '工龄']
  const data = employeeList.value.map(emp => [
    emp.employeeId,
    emp.name,
    emp.position,
    emp.employmentType,
    emp.joinDate,
    `${emp.workYears}年`
  ])
  
  exportToExcel(headers, data, `${props.department?.departmentName}人员名单`)
  ElMessage.success('导出成功')
}
</script>

<style lang="scss" scoped>
.department-detail {
  .info-section {
    margin-bottom: 20px;
  }

  .employee-tabs {
    :deep(.el-tabs__content) {
      padding-top: 20px;
    }
  }

  .position-group {
    max-height: 450px;
    overflow-y: auto;
  }

  .chart {
    width: 100%;
    height: 300px;
  }
}
</style>