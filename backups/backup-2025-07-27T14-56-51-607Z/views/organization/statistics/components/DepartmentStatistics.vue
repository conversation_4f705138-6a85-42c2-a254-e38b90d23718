<template>
  <div class="department-statistics">
    <!-- 查询条件 -->
    <el-form :model="queryForm" inline class="query-form">
      <el-form-item label="统计维度">
        <el-select v-model="queryForm.dimension" @change="handleDimensionChange">
          <el-option label="按部门类型" value="type"  />
          <el-option label="按部门层级" value="level"  />
          <el-option label="按上级部门" value="parent"  />
        </el-select>
      </el-form-item>
      <el-form-item label="统计日期">
        <el-date-picker
          v-model="queryForm.date"
          type="date"
          placeholder="选择日期"
          :default-value="new Date()"
         />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">
          <el-icon><Search /></el-icon>
          查询
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 图表展示区 -->
    <el-row :gutter="20" class="chart-container">
      <!-- 部门人员分布饼图 -->
      <el-col :xs="24" :md="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>部门人员分布</span>
              <el-button link @click="switchChartType('pie')">
                <el-icon><hr-pie-chart /></el-icon>
              </el-button>
            </div>
          </template>
          <div ref="pieChartRef" class="chart"></div>
        </el-card>
      </el-col>

      <!-- 部门人员数量柱状图 -->
      <el-col :xs="24" :md="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>部门人员数量排行</span>
              <el-button link @click="switchChartType('bar')">
                <el-icon><Histogram /></el-icon>
              </el-button>
            </div>
          </template>
          <div ref="barChartRef" class="chart"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细数据表格 -->
    <el-card class="data-table-card">
      <template #header>
        <div class="card-header">
          <span>部门人员统计明细</span>
          <div class="header-actions">
            <el-button link @click="toggleTableExpand">
              {{ tableExpanded ? '收起' : '展开' }}
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table
        :data="tableData"
        :max-height="tableExpanded ? 600 : 300"
        stripe
        border
        show-summary
        :summary-method="getSummaries"
      >
        <el-table-column type="index" label="序号" width="60"  />
        <el-table-column prop="departmentName" label="部门名称" min-width="180"  />
        <el-table-column prop="departmentCode" label="部门编码" width="120"  />
        <el-table-column prop="departmentType" label="部门类型" width="120"  />
        <el-table-column prop="totalCount" label="总人数" width="100" align="right">
          <template #default="{ row }">
            <el-link type="primary" @click="handleViewDetail(row)">
              {{ row.totalCount }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="regularCount" label="正式员工" width="100" align="right"  />
        <el-table-column prop="contractCount" label="合同制" width="100" align="right"  />
        <el-table-column prop="tempCount" label="临时工" width="100" align="right"  />
        <el-table-column prop="percentage" label="占比" width="100" align="right">
          <template #default="{ row }">
            <el-progress
              :percentage="row.percentage"
              :show-text="false"
              :stroke-width="10"
             />
            <span>{{ row.percentage }}%</span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 部门人员详情对话框 -->
    <DepartmentDetailDialog
      v-model="detailDialogVisible"
      :department="currentDepartment"
      @close="detailDialogVisible = false"
    />
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Download, HrPieChart, Histogram } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { exportToExcel } from '@/utils/export'
import DepartmentDetailDialog from './DepartmentDetailDialog.vue'

// Props
interface Props {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data?: unknown
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  refresh: []
}>()

// 查询表单
const queryForm = reactive({
  dimension: 'type',
  date: new Date()
})

// 图表引用
const pieChartRef = ref<HTMLDivElement>()
const barChartRef = ref<HTMLDivElement>()
let pieChart: echarts.ECharts | null = null
let barChart: echarts.ECharts | null = null

// 表格数据
const tableData = ref<any[]>([])
const tableExpanded = ref(false)

// 详情对话框
const detailDialogVisible = ref(false)
const currentDepartment = ref<unknown>(null)

// 初始化图表
const initCharts = () => {
  // 初始化饼图
  if (pieChartRef.value) {
    pieChart = echarts.init(pieChartRef.value)
    const pieOption = {
      title: {
        text: '部门人员分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        top: 'center'
      },
      series: [
        {
          name: 'HrHr人员分布',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 20,
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: []
        }
      ]
    }
    pieChart.setOption(pieOption)
  }

  // 初始化柱状图
  if (barChartRef.value) {
    barChart = echarts.init(barChartRef.value)
    const barOption = {
      title: {
        text: '部门人员数量TOP10',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        boundaryGap: [0, 0.01]
      },
      yAxis: {
        type: 'category',
        data: []
      },
      series: [
        {
          name: '人员数量',
          type: 'bar',
          data: [],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: '#83bff6' },
              { offset: 0.5, color: '#188df0' },
              { offset: 1, color: '#188df0' }
            ])
          }
        }
      ]
    }
    barChart.setOption(barOption)
  }

  // 监听窗口变化
  window.addEventListener('resize', handleResize)
}

// 处理窗口大小变化
const handleResize = () => {
  pieChart?.resize()
  barChart?.resize()
}

// 加载数据
const loadData = () => {
  // 模拟数据加载
  const mockData = [
    { departmentName: '信息技术部', departmentCode: 'IT001', departmentType: '职能部门', totalCount: 45, regularCount: 30, contractCount: 10, tempCount: 5, percentage: 15 },
    { departmentName: '人力资源部', departmentCode: 'HR001', departmentType: '职能部门', totalCount: 20, regularCount: 15, contractCount: 3, tempCount: 2, percentage: 6.7 },
    { departmentName: '市场营销部', departmentCode: 'MK001', departmentType: '业务部门', totalCount: 35, regularCount: 25, contractCount: 8, tempCount: 2, percentage: 11.7 },
    { departmentName: '财务部', departmentCode: 'FN001', departmentType: '职能部门', totalCount: 18, regularCount: 15, contractCount: 2, tempCount: 1, percentage: 6 },
    { departmentName: '研发中心', departmentCode: 'RD001', departmentType: '研发部门', totalCount: 80, regularCount: 60, contractCount: 15, tempCount: 5, percentage: 26.7 },
    { departmentName: '生产部', departmentCode: 'PD001', departmentType: '生产部门', totalCount: 120, regularCount: 80, contractCount: 30, tempCount: 10, percentage: 40 }
  ]

  tableData.value = mockData

  // 更新饼图数据
  if (pieChart) {
    const pieData = mockData.map(item => ({
      value: item.totalCount,
      name: item.departmentName
    }))
    pieChart.setOption({
      series: [{
        data: pieData
      }]
    })
  }

  // 更新柱状图数据（取前10）
  if (barChart) {
    const sortedData = [...mockData].sort((a, b) => b.totalCount - a.totalCount).slice(0, 10)
    barChart.setOption({
      yAxis: {
        data: sortedData.map(item => item.departmentName).reverse()
      },
      series: [{
        data: sortedData.map(item => item.totalCount).reverse()
      }]
    })
  }
}

// 处理查询
const handleQuery = () => {
  emit('refresh')
  loadData()
}

// 处理维度变化
const handleDimensionChange = () => {
  handleQuery()
}

// 切换图表类型
const switchChartType = (type: string) => {
  // 实现图表类型切换逻辑
  ElMessage.info(`切换到${type}图表`)
}

// 切换表格展开/收起
const toggleTableExpand = () => {
  tableExpanded.value = !tableExpanded.value
}

// 获取汇总数据
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const getSummaries = (param: unknown) => {
  const {columns: _columns, data} =  param
  const sums: string[] 
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 20px;
  }

  .chart-container {
    margin-bottom: 20px;

    .chart {
      width: 100%;
      height: 400px;
    }
  }

  .data-table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    :deep(.el-progress) {
      width: 60px;
      display: inline-block;
      margin-right: 8px;
    }
  }
}
</style>