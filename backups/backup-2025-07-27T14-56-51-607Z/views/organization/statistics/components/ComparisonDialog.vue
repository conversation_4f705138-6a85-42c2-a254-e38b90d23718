<template>
  <el-dialog
    :model-value="modelValue"
    title="编制对比分析"
    width="80%"
    @close="handleClose"
  >
    <div class="comparison-analysis">
      <!-- 对比条件设置 -->
      <el-form :model="comparisonForm" inline class="comparison-form">
        <el-form-item label="对比类型">
          <el-select v-model="comparisonForm.type">
            <el-option label="年度对比" value="year"  />
            <el-option label="部门对比" value="department"  />
            <el-option label="类别对比" value="category"  />
          </el-select>
        </el-form-item>
        <el-form-item label="对比时间" v-if="comparisonForm.type === 'year'">
          <el-date-picker
            v-model="comparisonForm.yearRange"
            type="yearrange"
            range-separator="至"
            start-placeholder="开始年份"
            end-placeholder="结束年份"
           />
        </el-form-item>
        <el-form-item label="选择部门" v-if="comparisonForm.type === 'department'">
          <HrOrgPicker
            v-model="comparisonForm.departments"
            multiple
            placeholder="请选择要对比的部门"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleAnalyze">
            <el-icon><DataAnalysis /></el-icon>
            开始分析
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 对比结果展示 -->
      <div v-if="showResult" class="comparison-result">
        <!-- 关键指标对比 -->
        <el-row :gutter="20" class="indicator-cards">
          <el-col :span="6" v-for="indicator in indicators" :key="indicator.key">
            <el-card class="indicator-card">
              <div class="indicator-title">{{ indicator.title }}</div>
              <div class="indicator-values">
                <div class="value-item" v-for="(value, index) in indicator.values" :key="index">
                  <span class="label">{{ value.label }}:</span>
                  <span class="value">{{ value.value }}</span>
                </div>
              </div>
              <div class="indicator-change" :class="{ 'positive': indicator.change > 0, 'negative': indicator.change < 0 }">
                <el-icon>
                  <ArrowUp v-if="indicator.change > 0" />
                  <ArrowDown v-else-if="indicator.change < 0" />
                  <Minus v-else />
                </el-icon>
                <span>{{ Math.abs(indicator.change) }}%</span>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 对比图表 -->
        <el-row :gutter="20" class="chart-container">
          <el-col :span="12">
            <el-card>
              <template #header>
                <span>编制数量对比</span>
              </template>
              <div ref="quantityChartRef" class="chart"></div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card>
              <template #header>
                <span>使用率对比</span>
              </template>
              <div ref="rateChartRef" class="chart"></div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 详细数据表格 -->
        <el-card class="data-table-card">
          <template #header>
            <div class="card-header">
              <span>详细对比数据</span>
              <el-button size="small" @click="handleExportData">
                导出数据
              </el-button>
            </div>
          </template>
          <el-table :data="comparisonData" stripe border>
            <el-table-column prop="name" label="对比项" fixed width="150"  />
            <el-table-column
              v-for="(column, index) in dynamicColumns"
              :key="index"
              :label="column.label"
              align="center"
            >
              <el-table-column prop="approved" :label="`核定编制`" width="100" align="right">
                <template #default="{ row }">
                  {{ row[column.key]?.approved || '-' }}
                </template>
              </el-table-column>
              <el-table-column prop="actual" :label="`实际在编`" width="100" align="right">
                <template #default="{ row }">
                  {{ row[column.key]?.actual || '-' }}
                </template>
              </el-table-column>
              <el-table-column prop="rate" :label="`使用率`" width="100" align="right">
                <template #default="{ row }">
                  <span v-if="row[column.key]?.rate">
                    {{ row[column.key].rate }}%
                  </span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 分析结论 -->
        <el-card class="conclusion-card">
          <template #header>
            <span>分析结论</span>
          </template>
          <div class="conclusion-content">
            <el-alert
              v-for="(conclusion, index) in conclusions"
              :key="index"
              :title="conclusion.title"
              :type="conclusion.type"
              :description="conclusion.content"
              show-icon
              :closable="false"
              class="conclusion-item"
             />
          </div>
        </el-card>
      </div>
    </div>

    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="handleExportReport" v-if="showResult">
        导出分析报告
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { DataAnalysis, ArrowUp, ArrowDown, Minus } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { exportToExcel } from '@/utils/export'
import HrOrgPicker from '@/components/common/HrOrgPicker.vue'

// Props
interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  close: []
}>()

// 对比表单
const comparisonForm = reactive({
  type: 'year',
  yearRange: [],
  departments: []
})

// 是否显示结果
const showResult = ref(false)

// 关键指标
const indicators = ref<any[]>([])

// 动态列
const dynamicColumns = ref<any[]>([])

// 对比数据
const comparisonData = ref<any[]>([])

// 分析结论
const conclusions = ref<any[]>([])

// 图表引用
const quantityChartRef = ref<HTMLDivElement>()
const rateChartRef = ref<HTMLDivElement>()
let quantityChart: echarts.ECharts | null = null
let rateChart: echarts.ECharts | null = null

// 开始分析
const handleAnalyze = () => {
  // 验证输入
  if (comparisonForm.type === 'year' && (!comparisonForm.yearRange || comparisonForm.yearRange.length !== 2)) {
    ElMessage.warning('请选择对比年份')
    return
  }
  if (comparisonForm.type === 'department' && comparisonForm.departments.length < 2) {
    ElMessage.warning('请至少选择两个部门进行对比')
    return
  }

  // 模拟分析过程
  ElMessage.info('正在分析数据...')
  
  setTimeout(() => {
    loadComparisonData()
    showResult.value = true
    nextTick(() => {
      initCharts()
    })
  }, 1000)
}

// 加载对比数据
const loadComparisonData = () => {
  // 根据对比类型生成模拟数据
  if (comparisonForm.type === 'year') {
    // 年度对比数据
    indicators.value = [
      {
        key: 'total',
        title: '总编制',
        values: [
          { label: '2023年', value: 480 },
          { label: '2024年', value: 500 }
        ],
        change: 4.2
      },
      {
        key: 'actual',
        title: '实际在编',
        values: [
          { label: '2023年', value: 420 },
          { label: '2024年', value: 432 }
        ],
        change: 2.9
      },
      {
        key: 'vacant',
        title: '空缺编制',
        values: [
          { label: '2023年', value: 60 },
          { label: '2024年', value: 68 }
        ],
        change: 13.3
      },
      {
        key: 'rate',
        title: '使用率',
        values: [
          { label: '2023年', value: '87.5%' },
          { label: '2024年', value: '86.4%' }
        ],
        change: -1.1
      }
    ]

    dynamicColumns.value = [
      { key: 'year2023', label: '2023年' },
      { key: 'year2024', label: '2024年' }
    ]

    comparisonData.value = [
      {
        name: 'HrHr教师编制',
        year2023: { approved: 170, actual: 150, rate: 88.2 },
        year2024: { approved: 180, actual: 160, rate: 88.9 }
      },
      {
        name: '行政编制',
        year2023: { approved: 115, actual: 105, rate: 91.3 },
        year2024: { approved: 120, actual: 110, rate: 91.7 }
      },
      {
        name: '教辅编制',
        year2023: { approved: 75, actual: 65, rate: 86.7 },
        year2024: { approved: 80, actual: 68, rate: 85.0 }
      },
      {
        name: '工勤编制',
        year2023: { approved: 50, actual: 45, rate: 90.0 },
        year2024: { approved: 52, actual: 48, rate: 92.3 }
      }
    ]

    conclusions.value = [
      {
        type: 'success',
        title: '编制规模增长',
        content: '2024年较2023年总编制增加20人，增长率4.2%，主要增加在教师编制和教辅编制。'
      },
      {
        type: 'warning',
        title: '使用率略有下降',
        content: '虽然实际在编人数增加，但由于编制增长更快，整体使用率从87.5%下降到86.4%。'
      },
      {
        type: 'info',
        title: '结构优化建议',
        content: '建议重点加强教辅岗位的招聘力度，其使用率相对较低，有较大的提升空间。'
      }
    ]
  } else if (comparisonForm.type === 'department') {
    // 部门对比数据
    indicators.value = [
      {
        key: 'avgRate',
        title: '平均使用率',
        values: [
          { label: '最高', value: '95.2%' },
          { label: '最低', value: '78.5%' }
        ],
        change: 0
      },
      {
        key: 'totalVacant',
        title: '总空缺数',
        values: [
          { label: '合计', value: 28 }
        ],
        change: 0
      }
    ]

    dynamicColumns.value = [
      { key: 'dept1', label: '教务处' },
      { key: 'dept2', label: '人事处' },
      { key: 'dept3', label: '财务处' }
    ]

    comparisonData.value = [
      {
        name: '编制情况',
        dept1: { approved: 20, actual: 18, rate: 90.0 },
        dept2: { approved: 15, actual: 15, rate: 100.0 },
        dept3: { approved: 18, actual: 17, rate: 94.4 }
      }
    ]
  }
}

// 初始化图表
const initCharts = () => {
  // 编制数量对比图
  if (quantityChartRef.value) {
    quantityChart = echarts.init(quantityChartRef.value)
    const quantityOption = {
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['核定编制', '实际在编']
      },
      xAxis: {
        type: 'category',
        data: dynamicColumns.value.map(col => col.label)
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '核定编制',
          type: 'bar',
          data: comparisonData.value[0] ? 
            dynamicColumns.value.map(col => comparisonData.value[0][col.key]?.approved || 0) : []
        },
        {
          name: '实际在编',
          type: 'bar',
          data: comparisonData.value[0] ? 
            dynamicColumns.value.map(col => comparisonData.value[0][col.key]?.actual || 0) : []
        }
      ]
    }
    quantityChart.setOption(quantityOption)
  }

  // 使用率对比图
  if (rateChartRef.value) {
    rateChart = echarts.init(rateChartRef.value)
    const rateOption = {
      tooltip: {
        trigger: 'axis',
        formatter: '{b}: {c}%'
      },
      xAxis: {
        type: 'category',
        data: dynamicColumns.value.map(col => col.label)
      },
      yAxis: {
        type: 'value',
        max: 100,
        axisLabel: {
          formatter: '{value}%'
        }
      },
      series: [{
        type: 'line',
        data: comparisonData.value[0] ? 
          dynamicColumns.value.map(col => comparisonData.value[0][col.key]?.rate || 0) : [],
        markLine: {
          data: [
            { type: 'average', name: '平均值' }
          ]
        }
      }]
    }
    rateChart.setOption(rateOption)
  }
}

// 导出数据
const handleExportData = () => {
  ElMessage.success('导出对比数据成功')
}

// 导出报告
const handleExportReport = () => {
  ElMessage.success('导出分析报告成功')
}

// 关闭对话框
const handleClose = () => {
  showResult.value = false
  quantityChart?.dispose()
  rateChart?.dispose()
  emit('update:modelValue', false)
  emit('close')
}
</script>

<style lang="scss" scoped>
.comparison-analysis {
  .comparison-form {
    background: #f5f7fa;
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 20px;
  }

  .comparison-result {
    .indicator-cards {
      margin-bottom: 20px;

      .indicator-card {
        :deep(.el-card__body) {
          padding: 16px;
        }

        .indicator-title {
          font-size: 14px;
          color: #909399;
          margin-bottom: 10px;
        }

        .indicator-values {
          margin-bottom: 10px;

          .value-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;

            .label {
              color: #606266;
              font-size: 13px;
            }

            .value {
              font-weight: 600;
              color: #303133;
            }
          }
        }

        .indicator-change {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;

          &.positive {
            color: var(--el-color-success);
          }

          &.negative {
            color: var(--el-color-danger);
          }

          .el-icon {
            margin-right: 4px;
          }
        }
      }
    }

    .chart-container {
      margin-bottom: 20px;

      .chart {
        width: 100%;
        height: 300px;
      }
    }

    .data-table-card {
      margin-bottom: 20px;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }

    .conclusion-card {
      .conclusion-item {
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>