<template>
  <div class="structure-analysis">
    <!-- 分析维度选择 -->
    <el-card class="dimension-card">
      <template #header>
        <span>选择分析维度</span>
      </template>
      <el-checkbox-group v-model="selectedDimensions" @change="handleDimensionChange">
        <el-checkbox-button label="age">年龄结构</el-checkbox-button>
        <el-checkbox-button label="education">学历结构</el-checkbox-button>
        <el-checkbox-button label="title">职称结构</el-checkbox-button>
        <el-checkbox-button label="gender">性别结构</el-checkbox-button>
        <el-checkbox-button label="workYears">工龄结构</el-checkbox-button>
        <el-checkbox-button label="employment">用工形式</el-checkbox-button>
      </el-checkbox-group>
    </el-card>

    <!-- 结构分析图表 -->
    <el-row :gutter="20" class="chart-container">
      <!-- 年龄结构 -->
      <el-col :xs="24" :md="12" v-if="selectedDimensions.includes('age')">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>年龄结构分析</span>
              <el-tag type="info">平均年龄: {{ avgAge }}岁</el-tag>
            </div>
          </template>
          <div ref="ageChartRef" class="chart"></div>
        </el-card>
      </el-col>

      <!-- 学历结构 -->
      <el-col :xs="24" :md="12" v-if="selectedDimensions.includes('education')">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>学历结构分析</span>
              <el-tag type="success">本科及以上: {{ educationRate }}%</el-tag>
            </div>
          </template>
          <div ref="educationChartRef" class="chart"></div>
        </el-card>
      </el-col>

      <!-- 职称结构 -->
      <el-col :xs="24" :md="12" v-if="selectedDimensions.includes('title')">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>职称结构分析</span>
              <el-tag type="warning">高级职称: {{ seniorTitleRate }}%</el-tag>
            </div>
          </template>
          <div ref="titleChartRef" class="chart"></div>
        </el-card>
      </el-col>

      <!-- 性别结构 -->
      <el-col :xs="24" :md="12" v-if="selectedDimensions.includes('gender')">
        <el-card>
          <template #header>
            <span>性别结构分析</span>
          </template>
          <div ref="genderChartRef" class="chart"></div>
        </el-card>
      </el-col>

      <!-- 工龄结构 -->
      <el-col :xs="24" :md="12" v-if="selectedDimensions.includes('workYears')">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>工龄结构分析</span>
              <el-tag>平均工龄: {{ avgWorkYears }}年</el-tag>
            </div>
          </template>
          <div ref="workYearsChartRef" class="chart"></div>
        </el-card>
      </el-col>

      <!-- 用工形式 -->
      <el-col :xs="24" :md="12" v-if="selectedDimensions.includes('employment')">
        <el-card>
          <template #header>
            <span>用工形式分析</span>
          </template>
          <div ref="employmentChartRef" class="chart"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 综合分析报告 -->
    <el-card class="analysis-report">
      <template #header>
        <div class="card-header">
          <span>人员结构综合分析</span>
          <el-button size="small" type="primary" @click="handleGenerateReport">
            生成分析报告
          </el-button>
        </div>
      </template>
      
      <!-- 关键指标 -->
      <el-row :gutter="20" class="key-indicators">
        <el-col :span="6">
          <div class="indicator-item">
            <div class="indicator-icon age-icon">
              <el-icon :size="24"><User /></el-icon>
            </div>
            <div class="indicator-content">
              <div class="indicator-value">{{ structureScore.age }}/100</div>
              <div class="indicator-label">年龄结构得分</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="indicator-item">
            <div class="indicator-icon education-icon">
              <el-icon :size="24"><Reading /></el-icon>
            </div>
            <div class="indicator-content">
              <div class="indicator-value">{{ structureScore.education }}/100</div>
              <div class="indicator-label">学历结构得分</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="indicator-item">
            <div class="indicator-icon title-icon">
              <el-icon :size="24"><Trophy /></el-icon>
            </div>
            <div class="indicator-content">
              <div class="indicator-value">{{ structureScore.title }}/100</div>
              <div class="indicator-label">职称结构得分</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="indicator-item">
            <div class="indicator-icon overall-icon">
              <el-icon :size="24"><DataLine /></el-icon>
            </div>
            <div class="indicator-content">
              <div class="indicator-value">{{ structureScore.overall }}/100</div>
              <div class="indicator-label">综合评分</div>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 问题与建议 -->
      <div class="suggestions">
        <h4>结构分析与优化建议</h4>
        <el-timeline>
          <el-timeline-item
            v-for="(item, index) in suggestions"
            :key="index"
            :type="item.type"
            :icon="item.icon"
          >
            <h5>{{ item.title }}</h5>
            <p>{{ item.content }}</p>
            <el-tag
              v-for="tag in item.tags"
              :key="tag"
              size="small"
              class="suggestion-tag"
            >
              {{ tag }}
            </el-tag>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { User, Reading, Trophy, DataLine, Warning, SuccessFilled, InfoFilled } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// Props
interface Props {
   
  data?: unknown
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  refresh: []
}>()

// 选中的分析维度
const selectedDimensions = ref(['age', 'education', 'title'])

// 统计数据
const avgAge = ref(38.5)
const educationRate = ref(85.6)
const seniorTitleRate = ref(22.3)
const avgWorkYears = ref(8.2)

// 结构评分
const structureScore = reactive({
  age: 85,
  education: 92,
  title: 78,
  overall: 85
})

// 优化建议
const suggestions = ref([
  {
    type: 'warning',
    icon: Warning,
    title: '年龄结构偏大',
    content: '35岁以上员工占比达68%，建议加强年轻人才引进，优化年龄梯队。',
    tags: ['人才引进', '梯队建设']
  },
  {
    type: 'success',
    icon: SuccessFilled,
    title: '学历结构良好',
    content: '本科及以上学历占比85.6%，研究生学历占比32%，整体学历水平较高。',
    tags: ['学历优势', '持续提升']
  },
  {
    type: 'primary',
    icon: InfoFilled,
    title: '职称结构待优化',
    content: '高级职称占比偏低，建议加强职称评聘工作，提升高层次人才比例。',
    tags: ['职称评聘', '人才培养']
  }
])

// 图表引用
const ageChartRef = ref<HTMLDivElement>()
const educationChartRef = ref<HTMLDivElement>()
const titleChartRef = ref<HTMLDivElement>()
const genderChartRef = ref<HTMLDivElement>()
const workYearsChartRef = ref<HTMLDivElement>()
const employmentChartRef = ref<HTMLDivElement>()

// 图表实例
const charts: Record<string, echarts.ECharts | null> = {
  age: null,
  education: null,
  title: null,
  gender: null,
  workYears: null,
  employment: null
}

// 初始化所有图表
const initAllCharts = () => {
  selectedDimensions.value.forEach(dimension => {
    nextTick(() => {
      switch (dimension) {
        case 'age':
          initAgeChart()
          break
        case 'education':
          initEducationChart()
          break
        case 'title':
          initTitleChart()
          break
        case 'gender':
          initGenderChart()
          break
        case 'workYears':
          initWorkYearsChart()
          break
        case 'employment':
          initEmploymentChart()
          break
      }
    })
  })
}

// 初始化年龄结构图表
const initAgeChart = () => {
  if (ageChartRef.value) {
    charts.age = echarts.init(ageChartRef.value)
    const option = {
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: ['25岁以下', '26-30岁', '31-35岁', '36-40岁', '41-45岁', '46-50岁', '51岁以上']
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}人'
        }
      },
      series: [{
        type: 'bar',
        data: [12, 45, 78, 92, 68, 42, 15],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#ffd700' },
            { offset: 1, color: '#ff8c00' }
          ])
        }
      }]
    }
    charts.age.setOption(option)
  }
}

// 初始化学历结构图表
const initEducationChart = () => {
  if (educationChartRef.value) {
    charts.education = echarts.init(educationChartRef.value)
    const option = {
      tooltip: {
        trigger: 'item'
      },
      legend: {
        bottom: '0%',
        left: 'center'
      },
      series: [{
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 138, name: 'HrHr博士' },
          { value: 156, name: '硕士' },
          { value: 243, name: '本科' },
          { value: 62, name: '专科及以下' }
        ]
      }]
    }
    charts.education.setOption(option)
  }
}

// 初始化职称结构图表
const initTitleChart = () => {
  if (titleChartRef.value) {
    charts.title = echarts.init(titleChartRef.value)
    const option = {
      tooltip: {
        trigger: 'item'
      },
      radar: {
        indicator: [
          { name: '正高级', max: 100 },
          { name: '副高级', max: 100 },
          { name: '中级', max: 200 },
          { name: '初级', max: 200 },
          { name: '无职称', max: 100 }
        ]
      },
      series: [{
        type: 'radar',
        data: [{
          value: [25, 68, 145, 82, 32],
          name: '职称分布',
          areaStyle: {
            color: new echarts.graphic.RadialGradient(0.1, 0.6, 1, [
              { color: 'rgba(255, 145, 124, 0.1)', offset: 0 },
              { color: 'rgba(255, 145, 124, 0.9)', offset: 1 }
            ])
          }
        }]
      }]
    }
    charts.title.setOption(option)
  }
}

// 初始化性别结构图表
const initGenderChart = () => {
  if (genderChartRef.value) {
    charts.gender = echarts.init(genderChartRef.value)
    const option = {
      tooltip: {
        trigger: 'item'
      },
      series: [{
        type: 'pie',
        radius: '60%',
        data: [
          { value: 268, name: '男性' },
          { value: 164, name: '女性' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    }
    charts.gender.setOption(option)
  }
}

// 初始化工龄结构图表
const initWorkYearsChart = () => {
  if (workYearsChartRef.value) {
    charts.workYears = echarts.init(workYearsChartRef.value)
    const option = {
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: ['1年以内', '1-3年', '3-5年', '5-10年', '10-15年', '15年以上']
      },
      yAxis: {
        type: 'value'
      },
      series: [{
        type: 'line',
        data: [35, 68, 82, 125, 68, 54],
        smooth: true,
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(128, 255, 165, 0.5)' },
            { offset: 1, color: 'rgba(1, 191, 236, 0.1)' }
          ])
        }
      }]
    }
    charts.workYears.setOption(option)
  }
}

// 初始化用工形式图表
const initEmploymentChart = () => {
  if (employmentChartRef.value) {
    charts.employment = echarts.init(employmentChartRef.value)
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'value'
      },
      yAxis: {
        type: 'category',
        data: ['正式编制', '合同制', '劳务派遣', '临时工', '其他']
      },
      series: [{
        type: 'bar',
        data: [285, 98, 25, 18, 6],
        label: {
          show: true,
          position: 'right',
          formatter: '{c}人'
        }
      }]
    }
    charts.employment.setOption(option)
  }
}

// 处理维度变化
const handleDimensionChange = () => {
  // 清理未选中维度的图表
  Object.keys(charts).forEach(key => {
    if (!selectedDimensions.value.includes(key) && charts[key]) {
      charts[key]?.dispose()
      charts[key] = null
    }
  })
  
  // 初始化新选中的图表
  nextTick(() => {
    initAllCharts()
  })
}

// 生成分析报告
const handleGenerateReport = () => {
  ElMessage.success('正在生成人员结构分析报告...')
  // 实现报告生成逻辑
}

// 监听窗口变化
const handleResize = () => {
  Object.values(charts).forEach(chart => {
    chart?.resize()
  })
}

// 组件挂载
onMounted(() => {
  initAllCharts()
  window.addEventListener('resize', handleResize)
})

// 组件卸载
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  Object.values(charts).forEach(chart => {
    chart?.dispose()
  })
})
</script>

<style lang="scss" scoped>
.structure-analysis {
  .dimension-card {
    margin-bottom: 20px;

    :deep(.el-checkbox-button) {
      margin-right: 10px;
      margin-bottom: 10px;
    }
  }

  .chart-container {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .chart {
      width: 100%;
      height: 350px;
    }
  }

  .analysis-report {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .key-indicators {
      margin-bottom: 30px;

      .indicator-item {
        display: flex;
        align-items: center;
        padding: 20px;
        background: #f5f7fa;
        border-radius: 8px;

        .indicator-icon {
          width: 50px;
          height: 50px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          margin-right: 15px;

          &.age-icon {
            background: rgba(var(--el-color-primary-rgb), 0.1);
            color: var(--el-color-primary);
          }

          &.education-icon {
            background: rgba(var(--el-color-success-rgb), 0.1);
            color: var(--el-color-success);
          }

          &.title-icon {
            background: rgba(var(--el-color-warning-rgb), 0.1);
            color: var(--el-color-warning);
          }

          &.overall-icon {
            background: rgba(var(--el-color-info-rgb), 0.1);
            color: var(--el-color-info);
          }
        }

        .indicator-content {
          .indicator-value {
            font-size: 24px;
            font-weight: 600;
            color: #303133;
          }

          .indicator-label {
            margin-top: 4px;
            font-size: 14px;
            color: #909399;
          }
        }
      }
    }

    .suggestions {
      h4 {
        margin-bottom: 20px;
        color: #303133;
      }

      h5 {
        margin-bottom: 8px;
        color: #303133;
      }

      p {
        margin-bottom: 10px;
        color: #606266;
        line-height: 1.6;
      }

      .suggestion-tag {
        margin-right: 8px;
      }
    }
  }
}
</style>