<template>
  <div class="establishment-statistics">
    <!-- 查询条件 -->
    <el-form :model="queryForm" inline class="query-form">
      <el-form-item label="统计年度">
        <el-date-picker
          v-model="queryForm.year"
          type="year"
          placeholder="选择年度"
          :default-value="new Date()"
         />
      </el-form-item>
      <el-form-item label="统计类型">
        <el-select v-model="queryForm.type">
          <el-option label="全校编制" value="all"  />
          <el-option label="部门编制" value="department"  />
          <el-option label="岗位类别" value="category"  />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">
          <el-icon><Search /></el-icon>
          查询
        </el-button>
        <el-button @click="handleCompare">
          <el-icon><Document /></el-icon>
          对比分析
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 编制概览卡片 -->
    <el-row :gutter="20" class="overview-cards">
      <el-col :xs="24" :sm="12" :md="6">
        <div class="overview-card card-primary">
          <div class="card-title">核定编制</div>
          <div class="card-value">{{ overview.approved }}</div>
          <div class="card-suffix">人</div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <div class="overview-card card-success">
          <div class="card-title">实际在编</div>
          <div class="card-value">{{ overview.actual }}</div>
          <div class="card-suffix">人</div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <div class="overview-card card-warning">
          <div class="card-title">空编数</div>
          <div class="card-value">{{ overview.vacant }}</div>
          <div class="card-suffix">人</div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <div class="overview-card card-info">
          <div class="card-title">使用率</div>
          <div class="card-value">{{ overview.usageRate }}</div>
          <div class="card-suffix">%</div>
        </div>
      </el-col>
    </el-row>

    <!-- 图表展示区 -->
    <el-row :gutter="20" class="chart-container">
      <!-- 编制使用率仪表盘 -->
      <el-col :xs="24" :md="8">
        <el-card>
          <template #header>
            <span>编制使用率</span>
          </template>
          <div ref="gaugeChartRef" class="chart"></div>
        </el-card>
      </el-col>

      <!-- 编制分布饼图 -->
      <el-col :xs="24" :md="8">
        <el-card>
          <template #header>
            <span>编制分布</span>
          </template>
          <div ref="distributionChartRef" class="chart"></div>
        </el-card>
      </el-col>

      <!-- 编制变化趋势 -->
      <el-col :xs="24" :md="8">
        <el-card>
          <template #header>
            <span>年度变化趋势</span>
          </template>
          <div ref="trendChartRef" class="chart"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 部门编制使用情况 -->
    <el-card class="department-usage-card">
      <template #header>
        <div class="card-header">
          <span>部门编制使用情况</span>
          <el-button-group>
            <el-button size="small" :type="viewType === 'table' ? 'primary' : ''" @click="viewType = 'table'">
              表格视图
            </el-button>
            <el-button size="small" :type="viewType === 'chart' ? 'primary' : ''" @click="viewType = 'chart'">
              图表视图
            </el-button>
          </el-button-group>
        </div>
      </template>

      <!-- 表格视图 -->
      <el-table
        v-if="viewType === 'table'"
        :data="departmentData"
        stripe
        border
        show-summary
        :summary-method="getSummaries"
        @row-click="handleRowClick"
      >
        <el-table-column type="index" label="序号" width="60"  />
        <el-table-column prop="departmentName" label="部门名称" min-width="180"  />
        <el-table-column prop="approved" label="核定编制" width="100" align="right"  />
        <el-table-column prop="actual" label="实际在编" width="100" align="right"  />
        <el-table-column prop="vacant" label="空编" width="80" align="right">
          <template #default="{ row }">
            <span :class="{ 'text-warning': row.vacant > 0 }">
              {{ row.vacant }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="over" label="超编" width="80" align="right">
          <template #default="{ row }">
            <span :class="{ 'text-danger': row.over > 0 }">
              {{ row.over || '-' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="usageRate" label="使用率" width="120" align="right">
          <template #default="{ row }">
            <el-progress
              :percentage="row.usageRate"
              :color="getUsageRateColor(row.usageRate)"
              :show-text="false"
              :stroke-width="10"
             />
            <span>{{ row.usageRate }}%</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click.stop="handleViewDetail(row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 图表视图 -->
      <div v-else ref="departmentChartRef" class="chart-large"></div>
    </el-card>

    <!-- 编制详情对话框 -->
    <EstablishmentDetailDialog
      v-model="detailDialogVisible"
      :department="currentDepartment"
      @close="detailDialogVisible = false"
    />

    <!-- 对比分析对话框 -->
    <ComparisonDialog
      v-model="comparisonDialogVisible"
      @close="comparisonDialogVisible = false"
    />
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Document } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import EstablishmentDetailDialog from './EstablishmentDetailDialog.vue'
import ComparisonDialog from './ComparisonDialog.vue'

// Props
interface Props {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data?: unknown
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  refresh: []
}>()

// 查询表单
const queryForm = reactive({
  year: new Date(),
  type: 'all'
})

// 编制概览数据
const overview = reactive({
  approved: 500,
  actual: 432,
  vacant: 68,
  usageRate: 86.4
})

// 视图类型
const viewType = ref('table')

// 部门数据
const departmentData = ref<any[]>([])

// 对话框
const detailDialogVisible = ref(false)
const comparisonDialogVisible = ref(false)
const currentDepartment = ref<unknown>(null)

// 图表引用
const gaugeChartRef = ref<HTMLDivElement>()
const distributionChartRef = ref<HTMLDivElement>()
const trendChartRef = ref<HTMLDivElement>()
const departmentChartRef = ref<HTMLDivElement>()
let gaugeChart: echarts.ECharts | null = null
let distributionChart: echarts.ECharts | null = null
let trendChart: echarts.ECharts | null = null
let departmentChart: echarts.ECharts | null = null

// 初始化图表
const initCharts = () => {
  // 使用率仪表盘
  if (gaugeChartRef.value) {
    gaugeChart = echarts.init(gaugeChartRef.value)
    const gaugeOption = {
      series: [
        {
          type: 'gauge',
          startAngle: 180,
          endAngle: 0,
          min: 0,
          max: 100,
          splitNumber: 10,
          itemStyle: {
            color: '#58D9F9',
            shadowColor: 'rgba(0,138,255,0.45)',
            shadowBlur: 10,
            shadowOffsetX: 2,
            shadowOffsetY: 2
          },
          progress: {
            show: true,
            roundCap: true,
            width: 18
          },
          pointer: {
            icon: 'path://M2090.36389,615.30999 L2090.36389,615.30999 C2091.48372,615.30999 2092.40383,616.194028 2092.44859,617.312956 L2096.90698,728.755929 C2097.05155,732.369577 2094.2393,735.416212 2090.62566,735.56078 C2090.53845,735.564269 2090.45117,735.566014 2090.36389,735.566014 L2090.36389,735.566014 C2086.74736,735.566014 2083.81557,732.63423 2083.81557,729.017692 C2083.81557,728.930412 2083.81732,728.84314 2083.82081,728.755929 L2088.2792,617.312956 C2088.32396,616.194028 2089.24407,615.30999 2090.36389,615.30999 Z',
            length: '75%',
            width: 16,
            offsetCenter: [0, '5%']
          },
          axisLine: {
            roundCap: true,
            lineStyle: {
              width: 18
            }
          },
          axisTick: {
            splitNumber: 2,
            lineStyle: {
              width: 2,
              color: '#999'
            }
          },
          splitLine: {
            length: 12,
            lineStyle: {
              width: 3,
              color: '#999'
            }
          },
          axisLabel: {
            distance: 30,
            color: '#999',
            fontSize: 14
          },
          title: {
            show: false
          },
          detail: {
            backgroundColor: '#fff',
            borderColor: '#999',
            borderWidth: 2,
            width: '60%',
            lineHeight: 40,
            height: 40,
            borderRadius: 8,
            offsetCenter: [0, '35%'],
            valueAnimation: true,
            formatter: function (value: number) {
              return '{value|' + value.toFixed(1) + '}{unit|%}'
            },
            rich: {
              value: {
                fontSize: 24,
                fontWeight: 'bolder',
                color: '#777'
              },
              unit: {
                fontSize: 16,
                color: '#999'
              }
            }
          },
          data: [
            {
              value: overview.usageRate
            }
          ]
        }
      ]
    }
    gaugeChart.setOption(gaugeOption)
  }

  // 编制分布饼图
  if (distributionChartRef.value) {
    distributionChart = echarts.init(distributionChartRef.value)
    const distributionOption = {
      tooltip: {
        trigger: 'item'
      },
      legend: {
        bottom: '0%',
        left: 'center'
      },
      series: [
        {
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 20,
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: [
            { value: 180, name: 'HrHr教师编制' },
            { value: 120, name: '行政编制' },
            { value: 80, name: '教辅编制' },
            { value: 52, name: '工勤编制' }
          ]
        }
      ]
    }
    distributionChart.setOption(distributionOption)
  }

  // 年度变化趋势
  if (trendChartRef.value) {
    trendChart = echarts.init(trendChartRef.value)
    const trendOption = {
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['核定编制', '实际在编']
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: ['1月', '2月', '3月', '4月', '5月', '6月']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '核定编制',
          type: 'line',
          data: [500, 500, 500, 500, 500, 500],
          itemStyle: {
            color: '#409EFF'
          }
        },
        {
          name: '实际在编',
          type: 'line',
          data: [420, 425, 428, 430, 435, 432],
          itemStyle: {
            color: '#67C23A'
          }
        }
      ]
    }
    trendChart.setOption(trendOption)
  }

  // 监听窗口变化
  window.addEventListener('resize', handleResize)
}

// 初始化部门图表
const initDepartmentChart = () => {
  if (departmentChartRef.value) {
    departmentChart = echarts.init(departmentChartRef.value)
    const departmentOption = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: ['核定编制', '实际在编']
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'value'
      },
      yAxis: {
        type: 'category',
        data: departmentData.value.map(item => item.departmentName)
      },
      series: [
        {
          name: '核定编制',
          type: 'bar',
          data: departmentData.value.map(item => item.approved)
        },
        {
          name: '实际在编',
          type: 'bar',
          data: departmentData.value.map(item => item.actual)
        }
      ]
    }
    departmentChart.setOption(departmentOption)
  }
}

// 处理窗口大小变化
const handleResize = () => {
  gaugeChart?.resize()
  distributionChart?.resize()
  trendChart?.resize()
  departmentChart?.resize()
}

// 加载数据
const loadData = () => {
  // 模拟部门数据
  departmentData.value = [
    { id: 1, departmentName: '教务处', approved: 20, actual: 18, vacant: 2, over: 0, usageRate: 90 },
    { id: 2, departmentName: '人事处', approved: 15, actual: 15, vacant: 0, over: 0, usageRate: 100 },
    { id: 3, departmentName: '财务处', approved: 18, actual: 17, vacant: 1, over: 0, usageRate: 94.4 },
    { id: 4, departmentName: '信息中心', approved: 25, actual: 28, vacant: 0, over: 3, usageRate: 112 },
    { id: 5, departmentName: '学生处', approved: 30, actual: 25, vacant: 5, over: 0, usageRate: 83.3 }
  ]
}

// 监听视图类型变化
watch(viewType, (newVal) => {
  if (newVal === 'chart') {
    nextTick(() => {
      initDepartmentChart()
    })
  }
})

// 查询处理
const handleQuery = () => {
  emit('refresh')
  loadData()
}

// 对比分析
const handleCompare = () => {
  comparisonDialogVisible.value = true
}

// 行点击
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleRowClick = (row: unknown) => {
  currentDepartment.value = row
  detailDialogVisible.value = true
}

// 查看详情
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleViewDetail = (row: unknown) => {
  currentDepartment.value = row
  detailDialogVisible.value = true
}

// 获取使用率颜色
const getUsageRateColor = (rate: number) => {
  if (rate > 100) return '#f56c6c'
  if (rate >= 90) return '#67c23a'
  if (rate >= 70) return '#e6a23c'
  return '#909399'
}

// 获取汇总数据
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
const getSummaries = (param: unknown) => {
  const {columns: _columns, data: _data} =  param
  const sums: string[] 
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 20px;
  }

  .overview-cards {
    margin-bottom: 20px;

    .overview-card {
      background: #fff;
      padding: 20px;
      border-radius: 4px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      text-align: center;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
      }

      &.card-primary::before {
        background: var(--el-color-primary);
      }

      &.card-success::before {
        background: var(--el-color-success);
      }

      &.card-warning::before {
        background: var(--el-color-warning);
      }

      &.card-info::before {
        background: var(--el-color-info);
      }

      .card-title {
        font-size: 14px;
        color: #909399;
        margin-bottom: 10px;
      }

      .card-value {
        font-size: 32px;
        font-weight: 600;
        color: #303133;
        display: inline-block;
      }

      .card-suffix {
        font-size: 14px;
        color: #909399;
        margin-left: 4px;
      }
    }
  }

  .chart-container {
    margin-bottom: 20px;

    .chart {
      width: 100%;
      height: 300px;
    }
  }

  .department-usage-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .chart-large {
      width: 100%;
      height: 400px;
    }

    .text-warning {
      color: var(--el-color-warning);
    }

    .text-danger {
      color: var(--el-color-danger);
    }
  }
}
</style>