<template>
  <el-dialog
    :model-value="modelValue"
    title="部门编制详情"
    width="70%"
    @close="handleClose"
  >
    <div class="establishment-detail" v-if="department">
      <!-- 基本信息 -->
      <el-card class="info-card">
        <template #header>
          <span>{{ department.departmentName }} - 编制情况</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-statistic title="核定编制" :value="department.approved">
              <template #suffix>人</template>
            </el-statistic>
          </el-col>
          <el-col :span="8">
            <el-statistic title="实际在编" :value="department.actual">
              <template #suffix>人</template>
            </el-statistic>
          </el-col>
          <el-col :span="8">
            <el-statistic 
              title="编制使用率" 
              :value="department.usageRate"
              :value-style="{ color: getUsageRateColor(department.usageRate) }"
            >
              <template #suffix>%</template>
            </el-statistic>
          </el-col>
        </el-row>
      </el-card>

      <!-- 编制分类统计 -->
      <el-card class="category-card">
        <template #header>
          <span>编制分类统计</span>
        </template>
        <el-table :data="categoryData" stripe>
          <el-table-column prop="category" label="编制类别"  />
          <el-table-column prop="approved" label="核定数" align="right"  />
          <el-table-column prop="actual" label="实际数" align="right"  />
          <el-table-column prop="vacant" label="空缺数" align="right">
            <template #default="{ row }">
              <span :class="{ 'text-warning': row.vacant > 0 }">
                {{ row.vacant }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="rate" label="使用率" align="right">
            <template #default="{ row }">
              <el-progress
                :percentage="row.rate"
                :color="getUsageRateColor(row.rate)"
                :show-text="false"
                :stroke-width="10"
               />
              <span>{{ row.rate }}%</span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 编制变动历史 -->
      <el-card class="history-card">
        <template #header>
          <div class="card-header">
            <span>编制变动历史</span>
            <el-date-picker
              v-model="historyDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
              @change="loadHistoryData"
             />
          </div>
        </template>
        <el-timeline>
          <el-timeline-item
            v-for="(item, index) in historyList"
            :key="index"
            :timestamp="item.date"
            placement="top"
            :type="item.type"
          >
            <el-card>
              <h4>{{ item.title }}</h4>
              <p>{{ item.content }}</p>
              <p class="history-meta">
                <span>操作人：{{ item.operator }}</span>
                <span>批文号：{{ item.documentNo || '无' }}</span>
              </p>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </el-card>

      <!-- 在编人员统计 -->
      <el-card class="employee-card">
        <template #header>
          <div class="card-header">
            <span>在编人员统计</span>
            <el-button size="small" @click="handleExportEmployees">
              导出名单
            </el-button>
          </div>
        </template>
        <el-row :gutter="20" class="stat-row">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ employeeStats.total }}</div>
              <div class="stat-label">总人数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ employeeStats.regular }}</div>
              <div class="stat-label">正式编制</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ employeeStats.contract }}</div>
              <div class="stat-label">合同制</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ employeeStats.temporary }}</div>
              <div class="stat-label">临时工</div>
            </div>
          </el-col>
        </el-row>
        <div ref="employeeChartRef" class="chart"></div>
      </el-card>
    </div>

    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="handleExport">导出报告</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import { exportToExcel } from '@/utils/export'

// Props
interface Props {
  modelValue: boolean
   
  department: unknown
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  close: []
}>()

// 数据
const categoryData = ref<any[]>([])
const historyList = ref<any[]>([])
const historyDateRange = ref<Date[]>([])
const employeeStats = reactive({
  total: 0,
  regular: 0,
  contract: 0,
  temporary: 0
})

// 图表
const employeeChartRef = ref<HTMLDivElement>()
let employeeChart: echarts.ECharts | null = null

// 监听部门变化
watch(() => props.department, (newVal) => {
  if (newVal) {
    loadDepartmentData()
  }
}, { immediate: true })

// 加载部门数据
const loadDepartmentData = () => {
  if (!props.department) return

  // 模拟编制分类数据
  categoryData.value = [
    { category: '教师编制', approved: 10, actual: 9, vacant: 1, rate: 90 },
    { category: '行政编制', approved: 5, actual: 5, vacant: 0, rate: 100 },
    { category: '教辅编制', approved: 3, actual: 2, vacant: 1, rate: 66.7 },
    { category: '工勤编制', approved: 2, actual: 2, vacant: 0, rate: 100 }
  ]

  // 模拟历史数据
  historyList.value = [
    {
      date: '2024-01-15 10:30:00',
      type: 'success',
      title: '编制调整',
      content: '根据学校发展需要，增加教师编制2名',
      operator: '人事处',
      documentNo: 'HR2024-001'
    },
    {
      date: '2023-09-01 09:00:00',
      type: 'primary',
      title: '年度编制核定',
      content: '2023年度编制核定完成，总编制20人',
      operator: '人事处',
      documentNo: 'HR2023-156'
    },
    {
      date: '2023-06-20 14:20:00',
      type: 'warning',
      title: '编制冻结',
      content: '因机构调整，暂时冻结行政编制1名',
      operator: '组织部',
      documentNo: 'ORG2023-089'
    }
  ]

  // 模拟员工统计数据
  employeeStats.total = props.department.actual || 0
  employeeStats.regular = Math.floor(employeeStats.total * 0.7)
  employeeStats.contract = Math.floor(employeeStats.total * 0.2)
  employeeStats.temporary = employeeStats.total - employeeStats.regular - employeeStats.contract

  // 初始化图表
  nextTick(() => {
    initEmployeeChart()
  })
}

// 初始化员工分布图表
const initEmployeeChart = () => {
  if (employeeChartRef.value) {
    if (employeeChart) {
      employeeChart.dispose()
    }
    employeeChart = echarts.init(employeeChartRef.value)
    
    const option = {
      tooltip: {
        trigger: 'item'
      },
      legend: {
        bottom: '0%',
        left: 'center'
      },
      series: [
        {
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 20,
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: [
            { value: employeeStats.regular, name: 'HrHr正式编制' },
            { value: employeeStats.contract, name: '合同制' },
            { value: employeeStats.temporary, name: '临时工' }
          ]
        }
      ]
    }
    
    employeeChart.setOption(option)
  }
}

// 加载历史数据
const loadHistoryData = () => {
  // 根据日期范围加载历史数据
  ElMessage.info('加载历史数据...')
}

// 获取使用率颜色
const getUsageRateColor = (rate: number) => {
  if (rate > 100) return '#f56c6c'
  if (rate >= 90) return '#67c23a'
  if (rate >= 70) return '#e6a23c'
  return '#909399'
}

// 导出员工名单
const handleExportEmployees = () => {
  ElMessage.success('导出员工名单成功')
}

// 导出报告
const handleExport = () => {
  const headers = ['项目', '数值']
  const data = [
    ['部门名称', props.department.departmentName],
    ['核定编制', props.department.approved],
    ['实际在编', props.department.actual],
    ['空编数', props.department.vacant || 0],
    ['超编数', props.department.over || 0],
    ['编制使用率', `${props.department.usageRate}%`],
    ['', ''],
    ['编制类别', '核定数', '实际数', '空缺数', '使用率'],
    ...categoryData.value.map(item => [
      item.category,
      item.approved,
      item.actual,
      item.vacant,
      `${item.rate}%`
    ])
  ]
  
  exportToExcel(headers, data, `${props.department.departmentName}编制详情报告`)
  ElMessage.success('导出成功')
}

// 关闭对话框
const handleClose = () => {
  emit('update:modelValue', false)
  emit('close')
}
</script>

<style lang="scss" scoped>
.establishment-detail {
  .info-card {
    margin-bottom: 20px;

    .el-statistic {
      text-align: center;
    }
  }

  .category-card {
    margin-bottom: 20px;

    .text-warning {
      color: var(--el-color-warning);
    }
  }

  .history-card {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .history-meta {
      margin-top: 10px;
      font-size: 12px;
      color: #909399;

      span {
        margin-right: 20px;
      }
    }
  }

  .employee-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .stat-row {
      margin-bottom: 20px;
    }

    .stat-item {
      text-align: center;
      padding: 10px;
      background: #f5f7fa;
      border-radius: 4px;

      .stat-value {
        font-size: 24px;
        font-weight: 600;
        color: #303133;
      }

      .stat-label {
        margin-top: 5px;
        font-size: 14px;
        color: #909399;
      }
    }

    .chart {
      width: 100%;
      height: 250px;
    }
  }
}
</style>