<template>
  <div class="position-statistics">
    <!-- 查询条件 -->
    <el-form :model="queryForm" inline class="query-form">
      <el-form-item label="统计范围">
        <HrOrgPicker
          v-model="queryForm.orgIds"
          multiple
          placeholder="选择部门"
        />
      </el-form-item>
      <el-form-item label="岗位类别">
        <el-select v-model="queryForm.positionCategory" multiple placeholder="请选择">
          <el-option label="管理岗" value="management"  />
          <el-option label="技术岗" value="technical"  />
          <el-option label="业务岗" value="business"  />
          <el-option label="行政岗" value="administrative"  />
          <el-option label="其他" value="other"  />
        </el-select>
      </el-form-item>
      <el-form-item label="岗位级别">
        <el-select v-model="queryForm.positionLevel" multiple placeholder="请选择">
          <el-option label="高级" value="senior"  />
          <el-option label="中级" value="middle"  />
          <el-option label="初级" value="junior"  />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">
          <el-icon><Search /></el-icon>
          查询
        </el-button>
        <el-button @click="handleReset">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stat-cards">
      <el-col :xs="24" :sm="12" :md="6">
        <el-statistic title="岗位总数" :value="statistics.totalPositions">
          <template #suffix>个</template>
        </el-statistic>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <el-statistic title="在岗人数" :value="statistics.totalEmployees">
          <template #suffix>人</template>
        </el-statistic>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <el-statistic title="空缺岗位" :value="statistics.vacantPositions">
          <template #suffix>个</template>
        </el-statistic>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <el-statistic title="岗位满编率" :value="statistics.fillRate" :precision="1">
          <template #suffix>%</template>
        </el-statistic>
      </el-col>
    </el-row>

    <!-- 图表展示 -->
    <el-row :gutter="20" class="chart-container">
      <!-- 岗位类别分布 -->
      <el-col :xs="24" :lg="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>岗位类别分布</span>
              <el-radio-group v-model="categoryChartType" size="small">
                <el-radio-button label="pie">饼图</el-radio-button>
                <el-radio-button label="bar">柱图</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div ref="categoryChartRef" class="chart"></div>
        </el-card>
      </el-col>

      <!-- 岗位级别分布 -->
      <el-col :xs="24" :lg="12">
        <el-card>
          <template #header>
            <span>岗位级别分布</span>
          </template>
          <div ref="levelChartRef" class="chart"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 岗位编制对比 -->
    <el-card class="comparison-card">
      <template #header>
        <div class="card-header">
          <span>岗位编制使用情况</span>
          <el-button link @click="handleExportComparison">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
        </div>
      </template>
      <div ref="comparisonChartRef" class="chart-large"></div>
    </el-card>

    <!-- 详细数据表格 -->
    <el-card class="data-table-card">
      <template #header>
        <div class="card-header">
          <span>岗位统计明细</span>
          <div class="header-actions">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索岗位名称"
              style="width: 200px"
              clearable
              @change="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-button link @click="handleExportTable">
              <el-icon><Download /></el-icon>
              导出表格
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        :data="filteredTableData"
        stripe
        border
        @sort-change="handleSortChange"
      >
        <el-table-column type="index" label="序号" width="60"  />
        <el-table-column prop="positionName" label="岗位名称" min-width="150"  />
        <el-table-column prop="positionCode" label="岗位编码" width="120"  />
        <el-table-column prop="category" label="岗位类别" width="100">
          <template #default="{ row }">
            <el-tag :type="getCategoryTagType(row.category)">
              {{ row.category }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="level" label="岗位级别" width="100">
          <template #default="{ row }">
            <el-tag :type="getLevelTagType(row.level)">
              {{ row.level }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="departmentName" label="所属部门" min-width="150"  />
        <el-table-column prop="establishedCount" label="编制数" width="80" align="right" sortable  />
        <el-table-column prop="actualCount" label="在岗数" width="80" align="right" sortable  />
        <el-table-column prop="vacantCount" label="空缺数" width="80" align="right">
          <template #default="{ row }">
            <span :class="{ 'text-danger': row.vacantCount > 0 }">
              {{ row.vacantCount }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="fillRate" label="满编率" width="100" align="right" sortable>
          <template #default="{ row }">
            <el-progress
              :percentage="row.fillRate"
              :color="getFillRateColor(row.fillRate)"
              :show-text="false"
              :stroke-width="10"
             />
            <span>{{ row.fillRate }}%</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleViewDetail(row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="tableData.length"
        layout="total, sizes, prev, pager, next, jumper"
        class="pagination"
       />
    </el-card>

    <!-- 岗位详情对话框 -->
    <PositionDetailDialog
      v-model="detailDialogVisible"
      :position="currentPosition"
      @close="detailDialogVisible = false"
    />
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh, Download } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { exportToExcel } from '@/utils/export'
import HrOrgPicker from '@/components/common/HrOrgPicker.vue'
import HrPositionDetailDialog from './HrPositionDetailDialog.vue'

// Props
interface Props {
   
  data?: unknown
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  refresh: []
}>()

// 查询表单
const queryForm = reactive({
  orgIds: [],
  positionCategory: [],
  positionLevel: []
})

// 统计数据
const statistics = reactive({
  totalPositions: 156,
  totalEmployees: 432,
  vacantPositions: 24,
  fillRate: 84.5
})

// 图表相关
const categoryChartType = ref('pie')
const categoryChartRef = ref<HTMLDivElement>()
const levelChartRef = ref<HTMLDivElement>()
const comparisonChartRef = ref<HTMLDivElement>()
let categoryChart: echarts.ECharts | null = null
let levelChart: echarts.ECharts | null = null
let comparisonChart: echarts.ECharts | null = null

// 表格相关
const tableData = ref<any[]>([])
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const detailDialogVisible = ref(false)
const currentPosition = ref<unknown>(null)

// 过滤后的表格数据
const filteredTableData = computed(() => {
  let filtered = tableData.value
  if (searchKeyword.value) {
    filtered = filtered.filter(item =>
      item.positionName.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filtered.slice(start, end)
})

// 初始化图表
const initCharts = () => {
  // 岗位类别分布图
  if (categoryChartRef.value) {
    categoryChart = echarts.init(categoryChartRef.value)
    updateCategoryChart()
  }

  // 岗位级别分布图
  if (levelChartRef.value) {
    levelChart = echarts.init(levelChartRef.value)
    const levelOption = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['高级', '中级', '初级']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: 'HrHr岗位数',
          type: 'bar',
          data: [25, 68, 63],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#ffd700' },
              { offset: 0.5, color: '#ffb347' },
              { offset: 1, color: '#ff8c00' }
            ])
          }
        },
        {
          name: '在岗人数',
          type: 'bar',
          data: [72, 189, 171],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#87ceeb' },
              { offset: 0.5, color: '#4682b4' },
              { offset: 1, color: '#1e90ff' }
            ])
          }
        }
      ]
    }
    levelChart.setOption(levelOption)
  }

  // 岗位编制对比图
  if (comparisonChartRef.value) {
    comparisonChart = echarts.init(comparisonChartRef.value)
    const comparisonOption = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999'
          }
        }
      },
      legend: {
        data: ['编制数', '在岗数', '满编率']
      },
      xAxis: {
        type: 'category',
        data: ['管理岗', '技术岗', '业务岗', '行政岗', '其他'],
        axisPointer: {
          type: 'shadow'
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '人数',
          min: 0,
          max: 200,
          interval: 50,
          axisLabel: {
            formatter: '{value}人'
          }
        },
        {
          type: 'value',
          name: '满编率',
          min: 0,
          max: 100,
          interval: 25,
          axisLabel: {
            formatter: '{value}%'
          }
        }
      ],
      series: [
        {
          name: '编制数',
          type: 'bar',
          data: [30, 80, 120, 40, 20]
        },
        {
          name: '在岗数',
          type: 'bar',
          data: [28, 75, 100, 35, 18]
        },
        {
          name: '满编率',
          type: 'line',
          yAxisIndex: 1,
          data: [93.3, 93.8, 83.3, 87.5, 90],
          itemStyle: {
            color: '#ff6b6b'
          }
        }
      ]
    }
    comparisonChart.setOption(comparisonOption)
  }

  // 监听窗口变化
  window.addEventListener('resize', handleResize)
}

// 更新类别图表
const updateCategoryChart = () => {
  if (!categoryChart) return

  const categoryData = [
    { name: '管理岗', value: 30 },
    { name: '技术岗', value: 80 },
    { name: '业务岗', value: 120 },
    { name: '行政岗', value: 40 },
    { name: '其他', value: 20 }
  ]

  if (categoryChartType.value === 'pie') {
    categoryChart.setOption({
      tooltip: {
        trigger: 'item'
      },
      series: [{
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: categoryData
      }]
    })
  } else {
    categoryChart.setOption({
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: categoryData.map(item => item.name)
      },
      yAxis: {
        type: 'value'
      },
      series: [{
        type: 'bar',
        data: categoryData.map(item => item.value),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        }
      }]
    })
  }
}

// 处理窗口大小变化
const handleResize = () => {
  categoryChart?.resize()
  levelChart?.resize()
  comparisonChart?.resize()
}

// 加载数据
const loadData = () => {
  // 模拟数据
  tableData.value = [
    { id: 1, positionName: '部门经理', positionCode: 'MGR001', category: '管理岗', level: '高级', departmentName: '人力资源部', establishedCount: 1, actualCount: 1, vacantCount: 0, fillRate: 100 },
    { id: 2, positionName: '高级软件工程师', positionCode: 'ENG001', category: '技术岗', level: '高级', departmentName: '技术部', establishedCount: 10, actualCount: 8, vacantCount: 2, fillRate: 80 },
    { id: 3, positionName: '销售专员', positionCode: 'SAL001', category: '业务岗', level: '初级', departmentName: '销售部', establishedCount: 20, actualCount: 18, vacantCount: 2, fillRate: 90 },
    { id: 4, positionName: '行政助理', positionCode: 'ADM001', category: '行政岗', level: '初级', departmentName: '行政部', establishedCount: 5, actualCount: 5, vacantCount: 0, fillRate: 100 },
    { id: 5, positionName: '项目经理', positionCode: 'PM001', category: '管理岗', level: '中级', departmentName: '项目管理部', establishedCount: 8, actualCount: 7, vacantCount: 1, fillRate: 87.5 }
  ]
}

// 监听图表类型变化
watch(categoryChartType, () => {
  updateCategoryChart()
})

// 查询处理
const handleQuery = () => {
  emit('refresh')
  loadData()
}

// 重置查询
const handleReset = () => {
  queryForm.orgIds = []
  queryForm.positionCategory = []
  queryForm.positionLevel = []
  handleQuery()
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
}

// 排序处理
   
const handleSortChange = ({ prop, order }: unknown) => {
  // 实现排序逻辑
  console.log('Sort:', prop, order)
}

// 查看详情
   
const handleViewDetail = (row: unknown) => {
  currentPosition.value = row
  detailDialogVisible.value = true
}

// 获取类别标签类型
const getCategoryTagType = (category: string) => {
  const types: Record<string, string> = {
    '管理岗': 'danger',
    '技术岗': 'primary',
    '业务岗': 'success',
    '行政岗': 'warning',
    '其他': 'info'
  }
  return types[category] || 'info'
}

// 获取级别标签类型
const getLevelTagType = (level: string) => {
  const types: Record<string, string> = {
    '高级': 'danger',
    '中级': 'warning',
    '初级': 'success'
  }
  return types[level] || 'info'
}

// 获取满编率颜色
const getFillRateColor = (rate: number) => {
  if (rate >= 90) return '#67c23a'
  if (rate >= 70) return '#e6a23c'
  return '#f56c6c'
}

// 导出对比数据
const handleExportComparison = () => {
  // 实现导出逻辑
  const headers = ['部门', '岗位名称', '编制数', '在岗数', '空缺数', '满编率']
  const data = tableData.value
    .sort((a, b) => b.fillRate - a.fillRate) // 按满编率排序
    .map(item => [
      item.departmentName,
      item.positionName,
      item.establishedCount,
      item.actualCount,
      item.vacantCount,
      `${item.fillRate}%`
    ])
  
  exportToExcel(headers, data, '岗位编制使用情况')
  ElMessage.success('导出成功')
}

// 导出表格数据
const handleExportTable = () => {
  const headers = ['岗位名称', '岗位编码', '岗位类别', '岗位级别', '所属部门', '编制数', '在岗数', '空缺数', '满编率']
  const data = tableData.value.map(item => [
    item.positionName,
    item.positionCode,
    item.category,
    item.level,
    item.departmentName,
    item.establishedCount,
    item.actualCount,
    item.vacantCount,
    `${item.fillRate}%`
  ])
  
  exportToExcel(headers, data, '岗位统计明细')
  ElMessage.success('导出成功')
}

// 组件挂载
onMounted(() => {
  nextTick(() => {
    initCharts()
    loadData()
  })
})

// 组件卸载
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  categoryChart?.dispose()
  levelChart?.dispose()
  comparisonChart?.dispose()
})
</script>

<style lang="scss" scoped>
.position-statistics {
  .query-form {
    background: #f5f7fa;
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 20px;
  }

  .stat-cards {
    margin-bottom: 20px;

    .el-statistic {
      text-align: center;
      padding: 20px;
      background: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }

  .chart-container {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .chart {
      width: 100%;
      height: 350px;
    }
  }

  .comparison-card {
    margin-bottom: 20px;

    .chart-large {
      width: 100%;
      height: 400px;
    }
  }

  .data-table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-actions {
        display: flex;
        gap: 10px;
        align-items: center;
      }
    }

    .text-danger {
      color: var(--el-color-danger);
    }

    .pagination {
      margin-top: 20px;
      justify-content: flex-end;
    }
  }
}
</style>