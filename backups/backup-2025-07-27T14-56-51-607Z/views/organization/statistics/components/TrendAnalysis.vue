<template>
  <div class="trend-analysis">
    <!-- 分析条件 -->
    <el-form :model="queryForm" inline class="query-form">
      <el-form-item label="分析周期">
        <el-select v-model="queryForm.period">
          <el-option label="按月" value="month"  />
          <el-option label="按季度" value="quarter"  />
          <el-option label="按年" value="year"  />
        </el-select>
      </el-form-item>
      <el-form-item label="时间范围">
        <el-date-picker
          v-model="queryForm.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :shortcuts="dateShortcuts"
         />
      </el-form-item>
      <el-form-item label="分析指标">
        <el-select v-model="queryForm.indicators" multiple placeholder="请选择">
          <el-option label="人员总数" value="total"  />
          <el-option label="部门数量" value="departments"  />
          <el-option label="编制使用率" value="establishmentRate"  />
          <el-option label="离职率" value="turnoverRate"  />
          <el-option label="入职率" value="hiringRate"  />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleAnalyze">
          <el-icon><TrendCharts /></el-icon>
          开始分析
        </el-button>
        <el-button @click="handlePredict">
          <el-icon><MagicStick /></el-icon>
          趋势预测
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 趋势图表 -->
    <el-card class="trend-chart-card">
      <template #header>
        <div class="card-header">
          <span>组织发展趋势图</span>
          <el-button-group size="small">
            <el-button :type="chartType === 'line' ? 'primary' : ''" @click="chartType = 'line'">
              折线图
            </el-button>
            <el-button :type="chartType === 'bar' ? 'primary' : ''" @click="chartType = 'bar'">
              柱状图
            </el-button>
            <el-button :type="chartType === 'mixed' ? 'primary' : ''" @click="chartType = 'mixed'">
              混合图
            </el-button>
          </el-button-group>
        </div>
      </template>
      <div ref="trendChartRef" class="trend-chart"></div>
    </el-card>

    <!-- 关键变化点 -->
    <el-card class="key-points-card">
      <template #header>
        <span>关键变化点分析</span>
      </template>
      <el-timeline>
        <el-timeline-item
          v-for="point in keyPoints"
          :key="point.id"
          :timestamp="point.date"
          :type="point.type"
          placement="top"
        >
          <el-card>
            <h4>{{ point.title }}</h4>
            <p>{{ point.description }}</p>
            <div class="point-data">
              <el-tag
                v-for="(data, index) in point.data"
                :key="index"
                :type="data.type"
                effect="plain"
                class="data-tag"
              >
                {{ data.label }}: {{ data.value }}
              </el-tag>
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </el-card>

    <!-- 预测分析 -->
    <el-card class="prediction-card" v-if="showPrediction">
      <template #header>
        <div class="card-header">
          <span>趋势预测分析</span>
          <el-tag type="info">基于历史数据的AI预测</el-tag>
        </div>
      </template>
      
      <!-- 预测图表 -->
      <div ref="predictionChartRef" class="prediction-chart"></div>
      
      <!-- 预测结果 -->
      <el-row :gutter="20" class="prediction-results">
        <el-col :span="8" v-for="result in predictionResults" :key="result.indicator">
          <div class="prediction-item">
            <div class="prediction-title">{{ result.title }}</div>
            <div class="prediction-value">
              <span class="current">当前: {{ result.current }}</span>
              <el-icon><ArrowRight /></el-icon>
              <span class="predicted">预测: {{ result.predicted }}</span>
            </div>
            <div class="prediction-change" :class="{ 'positive': result.change > 0, 'negative': result.change < 0 }">
              <el-icon>
                <ArrowUp v-if="result.change > 0" />
                <ArrowDown v-else-if="result.change < 0" />
                <Minus v-else />
              </el-icon>
              <span>{{ Math.abs(result.change) }}%</span>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 风险提示 -->
      <el-alert
        v-for="risk in riskAlerts"
        :key="risk.id"
        :title="risk.title"
        :type="risk.type"
        :description="risk.description"
        show-icon
        :closable="false"
        class="risk-alert"
       />
    </el-card>

    <!-- 对比分析 -->
    <el-card class="comparison-card">
      <template #header>
        <span>同比环比分析</span>
      </template>
      <el-table :data="comparisonData" stripe>
        <el-table-column prop="indicator" label="指标" width="150"  />
        <el-table-column prop="current" label="本期值" align="right"  />
        <el-table-column label="同比" align="center">
          <el-table-column prop="yoyValue" label="去年同期" align="right"  />
          <el-table-column prop="yoyChange" label="变化率" align="right">
            <template #default="{ row }">
              <span :class="{ 'text-success': row.yoyChange > 0, 'text-danger': row.yoyChange < 0 }">
                {{ row.yoyChange > 0 ? '+' : '' }}{{ row.yoyChange }}%
              </span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="环比" align="center">
          <el-table-column prop="momValue" label="上期值" align="right"  />
          <el-table-column prop="momChange" label="变化率" align="right">
            <template #default="{ row }">
              <span :class="{ 'text-success': row.momChange > 0, 'text-danger': row.momChange < 0 }">
                {{ row.momChange > 0 ? '+' : '' }}{{ row.momChange }}%
              </span>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { TrendCharts, MagicStick, ArrowRight, ArrowUp, ArrowDown, Minus } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// Props
interface Props {
   
  data?: unknown
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  refresh: []
}>()

// 查询表单
const queryForm = reactive({
  period: 'month',
  dateRange: [],
  indicators: ['total', 'establishmentRate']
})

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一年',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setFullYear(start.getFullYear() - 1)
      return [start, end]
    }
  },
  {
    text: '最近半年',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 6)
      return [start, end]
    }
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 3)
      return [start, end]
    }
  }
]

// 图表类型
const chartType = ref('line')
const showPrediction = ref(false)

// 图表引用
const trendChartRef = ref<HTMLDivElement>()
const predictionChartRef = ref<HTMLDivElement>()
let trendChart: echarts.ECharts | null = null
let predictionChart: echarts.ECharts | null = null

// 关键变化点
const keyPoints = ref([
  {
    id: 1,
    date: '2024-03-15',
    type: 'success',
    title: '组织扩张',
    description: '新成立技术研发中心，招聘技术人员30名',
    data: [
      { label: '新增部门', value: '1个', type: 'primary' },
      { label: '新增人员', value: '30人', type: 'success' },
      { label: '编制使用率', value: '+5%', type: 'warning' }
    ]
  },
  {
    id: 2,
    date: '2024-01-10',
    type: 'warning',
    title: '人员流动高峰',
    description: '年初离职高峰期，离职率达到8.5%',
    data: [
      { label: '离职人数', value: '18人', type: 'danger' },
      { label: '离职率', value: '8.5%', type: 'danger' },
      { label: '主要原因', value: '年终奖后', type: 'info' }
    ]
  }
])

// 预测结果
const predictionResults = ref([
  {
    indicator: 'total',
    title: '人员总数',
    current: 432,
    predicted: 456,
    change: 5.6
  },
  {
    indicator: 'departments',
    title: '部门数量',
    current: 24,
    predicted: 26,
    change: 8.3
  },
  {
    indicator: 'establishmentRate',
    title: '编制使用率',
    current: 86.4,
    predicted: 89.2,
    change: 3.2
  }
])

// 风险提示
const riskAlerts = ref([
  {
    id: 1,
    type: 'warning',
    title: '人员流失风险',
    description: '预测下季度可能出现技术人员流失高峰，建议提前做好人才储备和激励措施。'
  },
  {
    id: 2,
    type: 'info',
    title: '编制压力提示',
    description: '按照当前增长趋势，预计明年需要申请增加编制15-20人。'
  }
])

// 对比数据
const comparisonData = ref([
  {
    indicator: '人员总数',
    current: 432,
    yoyValue: 398,
    yoyChange: 8.5,
    momValue: 425,
    momChange: 1.6
  },
  {
    indicator: '部门数量',
    current: 24,
    yoyValue: 22,
    yoyChange: 9.1,
    momValue: 24,
    momChange: 0
  },
  {
    indicator: '编制使用率(%)',
    current: 86.4,
    yoyValue: 82.3,
    yoyChange: 5.0,
    momValue: 85.8,
    momChange: 0.7
  }
])

// 初始化趋势图表
const initTrendChart = () => {
  if (trendChartRef.value) {
    trendChart = echarts.init(trendChartRef.value)
    updateTrendChart()
  }
}

// 更新趋势图表
const updateTrendChart = () => {
  if (!trendChart) return

  const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
   
  const option: unknown = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['人员总数', '编制使用率']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: chartType.value === 'bar',
      data: months
    },
    yAxis: [
      {
        type: 'value',
        name: 'HrHr人数',
        min: 0,
        position: 'left',
        axisLabel: {
          formatter: '{value}人'
        }
      },
      {
        type: 'value',
        name: '使用率',
        min: 0,
        max: 100,
        position: 'right',
        axisLabel: {
          formatter: '{value}%'
        }
      }
    ],
    series: []
  }

  if (chartType.value === 'line') {
    option.series = [
      {
        name: '人员总数',
        type: 'line',
        data: [380, 385, 390, 398, 405, 410, 415, 420, 425, 428, 430, 432],
        smooth: true
      },
      {
        name: '编制使用率',
        type: 'line',
        yAxisIndex: 1,
        data: [80.2, 81.5, 82.3, 83.1, 84.0, 84.5, 85.2, 85.8, 86.0, 86.2, 86.3, 86.4],
        smooth: true
      }
    ]
  } else if (chartType.value === 'bar') {
    option.series = [
      {
        name: '人员总数',
        type: 'bar',
        data: [380, 385, 390, 398, 405, 410, 415, 420, 425, 428, 430, 432]
      },
      {
        name: '编制使用率',
        type: 'bar',
        yAxisIndex: 1,
        data: [80.2, 81.5, 82.3, 83.1, 84.0, 84.5, 85.2, 85.8, 86.0, 86.2, 86.3, 86.4]
      }
    ]
  } else {
    option.series = [
      {
        name: '人员总数',
        type: 'bar',
        data: [380, 385, 390, 398, 405, 410, 415, 420, 425, 428, 430, 432]
      },
      {
        name: '编制使用率',
        type: 'line',
        yAxisIndex: 1,
        data: [80.2, 81.5, 82.3, 83.1, 84.0, 84.5, 85.2, 85.8, 86.0, 86.2, 86.3, 86.4],
        smooth: true
      }
    ]
  }

  trendChart.setOption(option)
}

// 初始化预测图表
const initPredictionChart = () => {
  if (predictionChartRef.value) {
    predictionChart = echarts.init(predictionChartRef.value)
    
    const option = {
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['实际值', '预测值', '置信区间']
      },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月', '明年1月', '明年2月', '明年3月']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '实际值',
          type: 'line',
          data: [380, 385, 390, 398, 405, 410, 415, 420, 425, 428, 430, 432, null, null, null],
          smooth: true
        },
        {
          name: '预测值',
          type: 'line',
          data: [null, null, null, null, null, null, null, null, null, null, null, 432, 438, 445, 456],
          smooth: true,
          lineStyle: {
            type: 'dashed'
          }
        },
        {
          name: '置信区间',
          type: 'line',
          data: [null, null, null, null, null, null, null, null, null, null, null, 432, 435, 440, 448],
          smooth: true,
          lineStyle: {
            opacity: 0
          },
          stack: 'confidence',
          areaStyle: {
            color: 'rgba(0, 150, 255, 0.1)'
          },
          symbol: 'none'
        },
        {
          name: '置信区间',
          type: 'line',
          data: [null, null, null, null, null, null, null, null, null, null, null, 0, 6, 10, 16],
          smooth: true,
          lineStyle: {
            opacity: 0
          },
          stack: 'confidence',
          areaStyle: {
            color: 'rgba(0, 150, 255, 0.1)'
          },
          symbol: 'none'
        }
      ]
    }
    
    predictionChart.setOption(option)
  }
}

// 监听图表类型变化
watch(chartType, () => {
  updateTrendChart()
})

// 开始分析
const handleAnalyze = () => {
  emit('refresh')
  ElMessage.success('正在生成趋势分析报告...')
  initTrendChart()
}

// 趋势预测
const handlePredict = () => {
  showPrediction.value = true
  ElMessage.info('正在进行AI趋势预测...')
  nextTick(() => {
    initPredictionChart()
  })
}

// 监听窗口变化
const handleResize = () => {
  trendChart?.resize()
  predictionChart?.resize()
}

// 组件挂载
onMounted(() => {
  initTrendChart()
  window.addEventListener('resize', handleResize)
})

// 组件卸载
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  trendChart?.dispose()
  predictionChart?.dispose()
})
</script>

<style lang="scss" scoped>
.trend-analysis {
  .query-form {
    background: #f5f7fa;
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 20px;
  }

  .trend-chart-card {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .trend-chart {
      width: 100%;
      height: 400px;
    }
  }

  .key-points-card {
    margin-bottom: 20px;

    .point-data {
      margin-top: 10px;

      .data-tag {
        margin-right: 10px;
        margin-bottom: 5px;
      }
    }
  }

  .prediction-card {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .prediction-chart {
      width: 100%;
      height: 350px;
      margin-bottom: 20px;
    }

    .prediction-results {
      margin-bottom: 20px;

      .prediction-item {
        background: #f5f7fa;
        padding: 20px;
        border-radius: 8px;
        text-align: center;

        .prediction-title {
          font-size: 14px;
          color: #909399;
          margin-bottom: 10px;
        }

        .prediction-value {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 10px;

          .current {
            color: #606266;
          }

          .el-icon {
            margin: 0 10px;
            color: #909399;
          }

          .predicted {
            color: var(--el-color-primary);
            font-weight: 600;
          }
        }

        .prediction-change {
          font-size: 14px;

          &.positive {
            color: var(--el-color-success);
          }

          &.negative {
            color: var(--el-color-danger);
          }

          .el-icon {
            margin-right: 4px;
          }
        }
      }
    }

    .risk-alert {
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .comparison-card {
    .text-success {
      color: var(--el-color-success);
    }

    .text-danger {
      color: var(--el-color-danger);
    }
  }
}
</style>