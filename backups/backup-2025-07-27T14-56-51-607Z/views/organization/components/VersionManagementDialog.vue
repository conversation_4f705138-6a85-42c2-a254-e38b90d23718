<template>
  <el-dialog
    v-model="visible"
    title="组织架构版本管理"
    width="1000px"
    @open="handleOpen"
  >
    <div class="version-management">
      <!-- 工具栏 -->
      <div class="toolbar">
        <el-button type="primary" @click="createSnapshot">
          <el-icon><Camera /></el-icon>
          创建快照
        </el-button>
        <el-button @click="refreshVersions">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
      
      <!-- 版本列表 -->
      <el-table
        v-loading="loading"
        :data="versions"
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" :selectable="isSelectable"  />
        <el-table-column prop="versionNumber" label="版本号" width="120"  />
        <el-table-column prop="versionDate" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.versionDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="description" label="版本描述" show-overflow-tooltip  />
        <el-table-column prop="changeCount" label="变更数" width="80">
          <template #default="{ row }">
            <el-tag size="small">{{ row.changeCount }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdByName" label="创建人" width="100"  />
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag v-if="isCurrentVersion(row)" type="success">当前版本</el-tag>
            <el-tag v-else type="info">历史版本</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="viewSnapshot(row)">
              查看快照
            </el-button>
            <el-button
              v-if="selectedVersions.length === 1 && !isCurrentVersion(row)"
              type="text"
              size="small"
              @click="compareWithCurrent(row)"
            >
              与当前对比
            </el-button>
            <el-button
              v-if="!isCurrentVersion(row)"
              type="text"
              size="small"
              :disabled="!canRollback"
              @click="rollback(row)"
            >
              回滚
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 版本对比按钮 -->
      <div v-if="selectedVersions.length === 2" class="compare-toolbar">
        <el-button type="primary" @click="compareVersions">
          <el-icon><DocumentCopy /></el-icon>
          对比选中的两个版本
        </el-button>
      </div>
    </div>
    
    <!-- 创建快照对话框 -->
    <el-dialog
      v-model="snapshotDialogVisible"
      title="创建组织架构快照"
      width="500px"
      append-to-body
    >
      <el-form ref="snapshotFormRef" :model="snapshotForm" :rules="snapshotRules" label-width="100px">
        <el-form-item label="版本号" prop="versionNumber">
          <el-input v-model="snapshotForm.versionNumber" placeholder="如：v1.0.0"   />
        </el-form-item>
        <el-form-item label="版本描述" prop="description">
          <el-input
            v-model="snapshotForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入版本描述"
            />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="snapshotDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmCreateSnapshot">确定</el-button>
      </template>
    </el-dialog>
    
    <!-- 版本对比结果 -->
    <el-drawer
      v-model="compareDrawerVisible"
      title="版本对比结果"
      size="60%"
    >
      <div v-if="comparisonResult" class="comparison-result">
        <!-- 版本信息 -->
        <el-descriptions :column="2" border style="margin-bottom: 20px;">
          <el-descriptions-item label="版本1">
            {{ comparisonResult.version1.versionNumber }} ({{ formatDate(comparisonResult.version1.versionDate) }})
          </el-descriptions-item>
          <el-descriptions-item label="版本2">
            {{ comparisonResult.version2.versionNumber }} ({{ formatDate(comparisonResult.version2.versionDate) }})
          </el-descriptions-item>
        </el-descriptions>
        
        <!-- 变更统计 -->
        <el-row :gutter="20" style="margin-bottom: 20px;">
          <el-col :span="6">
            <el-statistic title="新增" :value="getChangeCount('ADD')"  />
          </el-col>
          <el-col :span="6">
            <el-statistic title="删除" :value="getChangeCount('DELETE')"  />
          </el-col>
          <el-col :span="6">
            <el-statistic title="修改" :value="getChangeCount('MODIFY')"  />
          </el-col>
          <el-col :span="6">
            <el-statistic title="移动" :value="getChangeCount('MOVE')"  />
          </el-col>
        </el-row>
        
        <!-- 变更详情 -->
        <el-table :data="comparisonResult.changes" max-height="500">
          <el-table-column prop="type" label="变更类型" width="100">
            <template #default="{ row }">
              <el-tag :type="getChangeTagType(row.type)" size="small">
                {{ getChangeTypeName(row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="nodeName" label="机构名称"  />
          <el-table-column prop="description" label="变更说明" show-overflow-tooltip  />
          <el-table-column label="变更前" width="150">
            <template #default="{ row }">
              <span v-if="row.before">{{ formatChangeValue(row.before) }}</span>
              <span v-else style="color: #909399;">-</span>
            </template>
          </el-table-column>
          <el-table-column label="变更后" width="150">
            <template #default="{ row }">
              <span v-if="row.after">{{ formatChangeValue(row.after) }}</span>
              <span v-else style="color: #909399;">-</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-drawer>
    
    <template #footer>
      <el-button @click="visible = false">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'VersionManagementDialog'
})
 
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Camera, Refresh, DocumentCopy } from '@element-plus/icons-vue'
import { useOrganizationStore } from '@/stores/modules/organization'
import { useUserStore } from '@/stores/modules/user'
import organizationApi from '@/api/modules/organization'
import type { OrganizationVersion, VersionComparison } from '@/types/organization'

const props = defineProps<{
  modelValue: boolean
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

const organizationStore = useOrganizationStore()
const userStore = useUserStore()

// 状态
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const loading = ref(false)
const versions = ref<OrganizationVersion[]>([])
const selectedVersions = ref<OrganizationVersion[]>([])
const currentVersionId = ref<string>('')

const snapshotDialogVisible = ref(false)
const snapshotFormRef = ref()
const snapshotForm = reactive({
  versionNumber: '',
  description: ''
})

const compareDrawerVisible = ref(false)
const comparisonResult = ref<VersionComparison | null>(null)

// 计算属性
const canRollback = computed(() => {
  // 根据用户权限判断
  const userPermissions = userStore.permissions || []
  return userPermissions.includes('organization:version:rollback') || userPermissions.includes('*')
})

// 验证规则
const snapshotRules = {
  versionNumber: [
    { required: true, message: '请输入版本号', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入版本描述', trigger: 'blur' }
  ]
}

// 方法
const handleOpen = () => {
  loadVersions()
}

const loadVersions = async () => {
  loading.value = true
  try {
    // 调用API获取版本列表
    const response = await organizationApi.getVersions()
    versions.value = response.data
    
    // 找到当前版本（通常是最新的版本）
    if (versions.value.length > 0) {
      // 按版本日期排序，最新的在前
      versions.value.sort((a, b) => new Date(b.versionDate).getTime() - new Date(a.versionDate).getTime())
      currentVersionId.value = versions.value[0].versionId
    }
  } catch (__error) {
    console.error('加载版本列表失败:', error)
    ElMessage.error('加载版本列表失败')
    // 使用空数组作为fallback
    versions.value = []
  } finally {
    loading.value = false
  }
}

const refreshVersions = () => {
  loadVersions()
}

const isCurrentVersion = (row: OrganizationVersion) => {
  return row.versionId === currentVersionId.value
}

const isSelectable = (row: OrganizationVersion) => {
  return selectedVersions.value.length < 2 || selectedVersions.value.some(v => v.versionId === row.versionId)
}

const handleSelectionChange = (selection: OrganizationVersion[]) => {
  selectedVersions.value = selection
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleString('zh-CN')
}

const createSnapshot = () => {
  snapshotForm.versionNumber = ''
  snapshotForm.description = ''
  snapshotDialogVisible.value = true
}

const confirmCreateSnapshot = async () => {
  const valid = await snapshotFormRef.value?.validate()
  if (!valid) return
  
  try {
    // 调用API创建快照
    loading.value = true
    const result = await organizationApi.createSnapshot({
      versionNumber: snapshotForm.versionNumber,
      description: snapshotForm.description
    })
    ElMessage.success('快照创建成功')
    snapshotDialogVisible.value = false
    loadVersions()
  } catch (__error) {
    console.error('创建快照失败:', error)
    ElMessage.error('创建快照失败')
  } finally {
    loading.value = false
  }
}

const viewSnapshot = (version: OrganizationVersion) => {
  // 实现查看快照功能 - 打开新窗口查看历史版本的组织结构
  // 由于没有单独的快照页面路由，使用对话框展示版本信息
  ElMessageBox.alert(
    `版本号：${version.versionNumber}\n` +
    `创建时间：${formatDate(version.versionDate)}\n` +
    `版本描述：${version.description}\n` +
    `变更数量：${version.changeCount}\n` +
    `创建人：${version.createdByName}`,
    '版本快照信息',
    {
      confirmButtonText: '确定',
      dangerouslyUseHTMLString: false
    }
  )
}

const compareWithCurrent = (version: OrganizationVersion) => {
  const currentVersion = versions.value.find(v => v.versionId === currentVersionId.value)
  if (!currentVersion) return
  
  doCompare(version, currentVersion)
}

const compareVersions = () => {
  if (selectedVersions.value.length !== 2) {
    ElMessage.warning('请选择两个版本进行对比')
    return
  }
  
  doCompare(selectedVersions.value[0], selectedVersions.value[1])
}

const doCompare = async (version1: OrganizationVersion, version2: OrganizationVersion) => {
  loading.value = true
  try {
    // 调用API进行版本对比
    const response = await organizationApi.compareVersions(version1.versionId, version2.versionId)
    comparisonResult.value = response.data
    compareDrawerVisible.value = true
  } catch (__error) {
    console.error('版本对比失败:', error)
    ElMessage.error('版本对比失败')
  } finally {
    loading.value = false
  }
}

const rollback = async (version: OrganizationVersion) => {
  try {
    await ElMessageBox.confirm(
      `确定要回滚到版本 ${version.versionNumber} 吗？此操作将覆盖当前的组织架构。`,
      '回滚确认',
      {
        confirmButtonText: '确定回滚',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 调用API执行回滚
    loading.value = true
    await organizationApi.rollbackToVersion(version.versionId)
    ElMessage.success('回滚成功')
    
    // 重新加载组织数据
    await organizationStore.fetchOrganizations()
    visible.value = false
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('回滚失败:', error)
      ElMessage.error('回滚失败')
    }
  } finally {
    loading.value = false
  }
}

const getChangeCount = (type: string) => {
  if (!comparisonResult.value) return 0
  return comparisonResult.value.changes.filter(c => c.type === type).length
}

const getChangeTagType = (type: string) => {
  const types: Record<string, string> = {
    ADD: 'success',
    DELETE: 'danger',
    MODIFY: 'warning',
    MOVE: 'info'
  }
  return types[type] || 'info'
}

const getChangeTypeName = (type: string) => {
  const names: Record<string, string> = {
    ADD: '新增',
    DELETE: '删除',
    MODIFY: '修改',
    MOVE: '移动'
  }
  return names[type] || type
}

   
const formatChangeValue = (value: unknown) => {
  if (typeof value === 'object') {
    return JSON.stringify(value, null, 2)
  }
  return String(value)
}
</script>

<style lang="scss" scoped>
.version-management {
  .toolbar {
    margin-bottom: 16px;
  }
  
  .compare-toolbar {
    margin-top: 16px;
    text-align: center;
  }
}

.comparison-result {
  .el-statistic {
    text-align: center;
  }
}
</style>