<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="800px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-form-item label="变更类型" prop="changeType">
        <el-select v-model="formData.changeType" placeholder="请选择变更类型" @change="handleChangeTypeChange">
          <el-option label="新增机构" value="CREATE"  />
          <el-option label="修改信息" value="UPDATE"  />
          <el-option label="划转机构" value="TRANSFER"  />
          <el-option label="撤销机构" value="CANCEL"  />
          <el-option label="合并机构" value="MERGE"  />
          <el-option label="拆分机构" value="SPLIT"  />
        </el-select>
      </el-form-item>
      
      <el-form-item label="变更机构" prop="institutionId">
        <el-tree-select
          v-model="formData.institutionId"
          :data="organizationTree"
          :props="{ label: 'institutionName', value: 'institutionId' }"
          placeholder="请选择要变更的机构"
          :disabled="changeType === 'CREATE'"
          @change="handleOrgChange"
         />
      </el-form-item>
      
      <!-- 新增机构表单 -->
      <template v-if="formData.changeType === 'CREATE'">
        <el-form-item label="机构名称" prop="changeDetails.afterData.institutionName">
          <el-input v-model="formData.changeDetails.afterData.institutionName" placeholder="请输入机构名称"   />
        </el-form-item>
        
        <el-form-item label="机构编码" prop="changeDetails.afterData.institutionCode">
          <el-input v-model="formData.changeDetails.afterData.institutionCode" placeholder="请输入机构编码">
            <template #append>
              <el-button @click="generateCode">自动生成</el-button>
            </template>
          </el-input>
        </el-form-item>
        
        <el-form-item label="机构类型" prop="changeDetails.afterData.institutionType">
          <el-select v-model="formData.changeDetails.afterData.institutionType" placeholder="请选择机构类型">
            <el-option
              v-for="item in organizationTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
             />
          </el-select>
        </el-form-item>
        
        <el-form-item label="上级机构" prop="changeDetails.afterData.parentInstitutionId">
          <el-tree-select
            v-model="formData.changeDetails.afterData.parentInstitutionId"
            :data="organizationTree"
            :props="{ label: 'institutionName', value: 'institutionId' }"
            placeholder="请选择上级机构"
            clearable
           />
        </el-form-item>
      </template>
      
      <!-- 修改信息表单 -->
      <template v-if="formData.changeType === 'UPDATE' && currentOrg">
        <el-divider content-position="left">变更内容</el-divider>
        
        <el-form-item label="机构名称">
          <el-col :span="11">
            <el-input :model-value="currentOrg.institutionName" disabled   />
          </el-col>
          <el-col :span="2" style="text-align: center;">
            <el-icon><ArrowRight /></el-icon>
          </el-col>
          <el-col :span="11">
            <el-input v-model="formData.changeDetails.afterData.institutionName" placeholder="新机构名称"   />
          </el-col>
        </el-form-item>
        
        <el-form-item label="机构类型">
          <el-col :span="11">
            <el-select :model-value="currentOrg.institutionType" disabled style="width: 100%">
              <el-option
                v-for="item in organizationTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-select>
          </el-col>
          <el-col :span="2" style="text-align: center;">
            <el-icon><ArrowRight /></el-icon>
          </el-col>
          <el-col :span="11">
            <el-select v-model="formData.changeDetails.afterData.institutionType" placeholder="新机构类型" style="width: 100%">
              <el-option
                v-for="item in organizationTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
               />
            </el-select>
          </el-col>
        </el-form-item>
      </template>
      
      <!-- 划转机构表单 -->
      <template v-if="formData.changeType === 'TRANSFER' && currentOrg">
        <el-form-item label="当前上级">
          <el-input :model-value="currentOrg.parentInstitutionName || '无'" disabled   />
        </el-form-item>
        
        <el-form-item label="目标上级" prop="changeDetails.targetParentId">
          <el-tree-select
            v-model="formData.changeDetails.targetParentId"
            :data="filteredOrgTree"
            :props="{ label: 'institutionName', value: 'institutionId' }"
            placeholder="请选择目标上级机构"
           />
        </el-form-item>
      </template>
      
      <!-- 合并机构表单 -->
      <template v-if="formData.changeType === 'MERGE'">
        <el-form-item label="源机构" prop="changeDetails.sourceInstitutions">
          <el-tree-select
            v-model="formData.changeDetails.sourceInstitutions"
            :data="organizationTree"
            :props="{ label: 'institutionName', value: 'institutionId' }"
            placeholder="请选择要合并的源机构"
            multiple
            :multiple-limit="5"
           />
        </el-form-item>
        
        <el-form-item label="目标机构" prop="institutionId">
          <el-tree-select
            v-model="formData.institutionId"
            :data="organizationTree"
            :props="{ label: 'institutionName', value: 'institutionId' }"
            placeholder="请选择合并到的目标机构"
           />
        </el-form-item>
      </template>
      
      <!-- 拆分机构表单 -->
      <template v-if="formData.changeType === 'SPLIT' && currentOrg">
        <el-form-item label="拆分方案">
          <el-button type="primary" @click="addSplitOrg">添加新机构</el-button>
        </el-form-item>
        
        <el-form-item
          v-for="(org, index) in formData.changeDetails.newInstitutions"
          :key="index"
          :label="`新机构${index + 1}`"
        >
          <el-row :gutter="10">
            <el-col :span="8">
              <el-input v-model="org.name" placeholder="机构名称"   />
            </el-col>
            <el-col :span="8">
              <el-input v-model="org.code" placeholder="机构编码"   />
            </el-col>
            <el-col :span="6">
              <el-select v-model="org.type" placeholder="机构类型" style="width: 100%">
                <el-option
                  v-for="item in organizationTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                 />
              </el-select>
            </el-col>
            <el-col :span="2">
              <el-button type="danger" :icon="Delete" @click="removeSplitOrg(index)"   />
            </el-col>
          </el-row>
        </el-form-item>
      </template>
      
      <el-divider   />
      
      <el-form-item label="变更标题" prop="title">
        <el-input v-model="formData.title" placeholder="请输入变更标题"   />
      </el-form-item>
      
      <el-form-item label="变更原因" prop="reason">
        <el-input
          v-model="formData.reason"
          type="textarea"
          :rows="3"
          placeholder="请详细说明变更原因"
          />
      </el-form-item>
      
      <el-form-item label="生效日期" prop="effectiveDate">
        <el-date-picker
          v-model="formData.effectiveDate"
          type="date"
          placeholder="请选择生效日期"
          value-format="YYYY-MM-DD"
          :disabled-date="disabledDate"
         />
      </el-form-item>
      
      <el-form-item label="备注说明" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="2"
          placeholder="其他需要说明的事项"
          />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSaveDraft">保存草稿</el-button>
      <el-button type="primary" @click="handleSubmit">提交申请</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
 
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowRight, Delete } from '@element-plus/icons-vue'
import { useOrganizationStore } from '@/stores/modules/organization'
import { useUserStore } from '@/stores/modules/user'
import organizationApi from '@/api/modules/organization'
import { generateOrgCode } from '@/utils/orgCodeGenerator'
import type { Organization, ChangeRequest, ChangeType } from '@/types/organization'
import { organizationTypeOptions } from '@/types/organization'

const props = defineProps<{
  modelValue: boolean
  mode?: 'create' | 'edit'
  changeType?: ChangeType
  organization?: Organization
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'success': [request: ChangeRequest]
}>()

const organizationStore = useOrganizationStore()
const userStore = useUserStore()

// 状态
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const dialogTitle = computed(() => {
  const titles = {
    CREATE: '新增机构申请',
    UPDATE: '修改机构申请',
    TRANSFER: '机构划转申请',
    CANCEL: '撤销机构申请',
    MERGE: '合并机构申请',
    SPLIT: '拆分机构申请'
  }
  return titles[formData.changeType as ChangeType] || '变更申请'
})

const formRef = ref()
const currentOrg = ref<Organization | null>(null)
const organizationTree = computed(() => organizationStore.organizationTree)

// 过滤后的组织树（排除当前机构及其子机构）
const filteredOrgTree = computed(() => {
  if (!currentOrg.value) return organizationTree.value
  
  const filterTree = (tree: Organization[], excludeId: string): Organization[] => {
    return tree.filter(node => {
      if (node.institutionId === excludeId) {
        return false
      }
      if (node.children) {
        node.children = filterTree(node.children, excludeId)
      }
      return true
    })
  }
  
  return filterTree(organizationTree.value, currentOrg.value.institutionId)
})

// 表单数据
const formData = reactive<{
  changeType: ChangeType | ''
  institutionId: string
  title: string
  reason: string
  description: string
  effectiveDate: string
   
  changeDetails: unknown
}>({
  changeType: props.changeType || '',
  institutionId: props.organization?.institutionId || '',
  title: '',
  reason: '',
  description: '',
  effectiveDate: '',
  changeDetails: {
    beforeData: {},
    afterData: {},
    sourceInstitutions: [],
    targetParentId: '',
    newInstitutions: []
  }
})

// 表单验证规则
const formRules = {
  changeType: [
    { required: true, message: '请选择变更类型', trigger: 'change' }
  ],
  institutionId: [
    { required: true, message: '请选择变更机构', trigger: 'change' }
  ],
  title: [
    { required: true, message: '请输入变更标题', trigger: 'blur' }
  ],
  reason: [
    { required: true, message: '请输入变更原因', trigger: 'blur' }
  ],
  effectiveDate: [
    { required: true, message: '请选择生效日期', trigger: 'change' }
  ],
  'changeDetails.afterData.institutionName': [
    { required: true, message: '请输入机构名称', trigger: 'blur' }
  ],
  'changeDetails.afterData.institutionCode': [
    { required: true, message: '请输入机构编码', trigger: 'blur' }
  ],
  'changeDetails.afterData.institutionType': [
    { required: true, message: '请选择机构类型', trigger: 'change' }
  ],
  'changeDetails.targetParentId': [
    { required: true, message: '请选择目标上级机构', trigger: 'change' }
  ],
  'changeDetails.sourceInstitutions': [
    { required: true, message: '请选择源机构', trigger: 'change' }
  ]
}

// 监听属性变化
watch(() => props.organization, (val) => {
  if (val) {
    currentOrg.value = val
    formData.institutionId = val.institutionId
  }
})

watch(() => props.changeType, (val) => {
  if (val) {
    formData.changeType = val
  }
})

// 处理变更类型改变
const handleChangeTypeChange = (type: ChangeType) => {
  // 重置相关字段
  formData.changeDetails = {
    beforeData: {},
    afterData: {},
    sourceInstitutions: [],
    targetParentId: '',
    newInstitutions: []
  }
  
  // 如果是拆分，初始化新机构列表
  if (type === 'SPLIT') {
    formData.changeDetails.newInstitutions = [
      { name: '', code: '', type: '' }
    ]
  }
}

// 处理机构选择改变
const handleOrgChange = (orgId: string) => {
  const org = findOrgById(orgId)
  if (org) {
    currentOrg.value = org
    // 记录原始数据
    formData.changeDetails.beforeData = {
      institutionName: org.institutionName,
      institutionType: org.institutionType,
      parentInstitutionId: org.parentInstitutionId
    }
    // 初始化修改后数据
    if (formData.changeType === 'UPDATE') {
      formData.changeDetails.afterData = {
        institutionName: org.institutionName,
        institutionType: org.institutionType
      }
    }
  }
}

// 查找机构
const findOrgById = (id: string): Organization | null => {
  const find = (tree: Organization[]): Organization | null => {
    for (const node of tree) {
      if (node.institutionId === id) {
        return node
      }
      if (node.children) {
        const found = find(node.children)
        if (found) return found
      }
    }
    return null
  }
  return find(organizationTree.value)
}

// 生成机构编码
const generateCode = () => {
  if (!formData.changeDetails.afterData.institutionType) {
    ElMessage.warning('请先选择机构类型')
    return
  }
  
  const parentCode = formData.changeDetails.afterData.parentInstitutionId
    ? findOrgById(formData.changeDetails.afterData.parentInstitutionId)?.institutionCode
    : undefined
    
  formData.changeDetails.afterData.institutionCode = generateOrgCode(
    formData.changeDetails.afterData.institutionType,
    parentCode
  )
}

// 添加拆分机构
const addSplitOrg = () => {
  formData.changeDetails.newInstitutions.push({
    name: '',
    code: '',
    type: ''
  })
}

// 删除拆分机构
const removeSplitOrg = (index: number) => {
  formData.changeDetails.newInstitutions.splice(index, 1)
}

// 禁用日期（不能选择过去的日期）
const disabledDate = (date: Date) => {
  return date.getTime() < Date.now() - 24 * 60 * 60 * 1000
}

// 保存草稿
const handleSaveDraft = async () => {
  const valid = await formRef.value?.validate()
  if (!valid) return
  
  try {
    // 调用API保存草稿
    const changeRequest: Partial<ChangeRequest> = {
      changeType: formData.changeType as ChangeType,
      institutionId: formData.institutionId,
      institutionName: currentOrg.value?.institutionName || formData.changeDetails?.afterData?.institutionName,
      title: formData.title,
      reason: formData.reason,
      effectiveDate: formData.effectiveDate,
      status: 'DRAFT',
      changeDetails: formData.changeDetails,
      createBy: userStore.userInfo?.id,
      createByName: userStore.userInfo?.name || userStore.userInfo?.username
    }
    
    await organizationApi.createChangeRequest(changeRequest)
    ElMessage.success('草稿保存成功')
    emit('success', changeRequest as ChangeRequest)
    visible.value = false
  } catch (__error) {
    console.error('保存草稿失败:', error)
    ElMessage.error('保存草稿失败，请重试')
  }
}

// 提交申请
const handleSubmit = async () => {
  const valid = await formRef.value?.validate()
  if (!valid) return
  
  try {
    await ElMessageBox.confirm(
      '提交后将进入审批流程，是否确认提交？',
      '提交确认',
      {
        confirmButtonText: '确认提交',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 调用API提交申请
    const changeRequest: Partial<ChangeRequest> = {
      changeType: formData.changeType as ChangeType,
      institutionId: formData.institutionId,
      institutionName: currentOrg.value?.institutionName || formData.changeDetails?.afterData?.institutionName,
      title: formData.title,
      reason: formData.reason,
      description: formData.description || '',
      effectiveDate: formData.effectiveDate,
      status: 'PENDING',
      changeDetails: formData.changeDetails,
      createBy: userStore.userInfo?.id,
      createByName: userStore.userInfo?.name || userStore.userInfo?.username
    }
    
    const result = await organizationApi.createChangeRequest(changeRequest)
    ElMessage.success('申请提交成功，已进入审批流程')
    emit('success', result.data)
    visible.value = false
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('提交申请失败:', error)
      ElMessage.error('提交申请失败，请重试')
    }
  }
}

// 取消
const handleCancel = () => {
  visible.value = false
}

// 关闭对话框
const handleClose = () => {
  formRef.value?.resetFields()
  currentOrg.value = null
}
</script>

<style lang="scss" scoped>
.el-col {
  display: flex;
  align-items: center;
}

.el-divider {
  margin: 20px 0;
}
</style>