<template>
  <el-dialog
    v-model="visible"
    title="机构合并"
    width="600px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-alert type="warning" :closable="false" style="margin-bottom: 20px">
        <template #default>
          <div>
            <p>机构合并注意事项：</p>
            <ul style="margin: 5px 0 0 20px; padding: 0;">
              <li>合并后，源机构将被标记为"已合并"状态</li>
              <li>源机构下的所有人员、编制、历史数据将迁移到目标机构</li>
              <li>此操作不可逆，请谨慎操作</li>
            </ul>
          </div>
        </template>
      </el-alert>
      
      <el-form-item label="源机构" prop="sourceIds">
        <el-select
          v-model="formData.sourceIds"
          multiple
          placeholder="请选择要合并的源机构"
          style="width: 100%"
        >
          <el-option
            v-for="org in availableOrgs"
            :key="org.institutionId"
            :label="`${org.institutionName} (${org.institutionCode})`"
            :value="org.institutionId"
            :disabled="org.institutionId === formData.targetId"
           />
        </el-select>
      </el-form-item>
      
      <el-form-item label="目标机构" prop="targetId">
        <el-tree-select
          v-model="formData.targetId"
          :data="treeData"
          :props="{ label: 'institutionName', value: 'institutionId' }"
          placeholder="请选择合并后的目标机构"
          style="width: 100%"
          :disabled-method="(node) => formData.sourceIds.includes(node.institutionId)"
        />
      </el-form-item>
      
      <el-form-item label="合并原因" prop="reason">
        <el-input
          v-model="formData.reason"
          type="textarea"
          :rows="3"
          placeholder="请输入合并原因"
          />
      </el-form-item>
      
      <el-form-item label="生效日期" prop="effectiveDate">
        <el-date-picker
          v-model="formData.effectiveDate"
          type="date"
          placeholder="请选择生效日期"
          value-format="YYYY-MM-DD"
          style="width: 100%"
         />
      </el-form-item>
      
      <!-- 影响分析 -->
      <el-form-item label="影响分析">
        <el-card v-if="impactAnalysis" shadow="never">
          <el-descriptions :column="2" size="small">
            <el-descriptions-item label="涉及人员">
              {{ impactAnalysis.affectedEmployees }} 人
            </el-descriptions-item>
            <el-descriptions-item label="涉及编制">
              {{ impactAnalysis.affectedEstablishment }} 个
            </el-descriptions-item>
            <el-descriptions-item label="历史记录">
              {{ impactAnalysis.historicalRecords }} 条
            </el-descriptions-item>
            <el-descriptions-item label="关联数据">
              {{ impactAnalysis.relatedData }} 项
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
        <el-button
          v-else
          type="primary"
          link
          @click="analyzeImpact"
          :loading="analyzing"
        >
          分析影响范围
        </el-button>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button
        type="danger"
        @click="handleConfirm"
        :loading="loading"
        :disabled="!impactAnalysis"
      >
        确认合并
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'MergeDialog'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useOrganizationStore } from '@/stores/modules/organization'
import { organizationApi } from '@/api/organization'
import type { Organization } from '@/types/organization'

const props = defineProps<{
  modelValue: boolean
  organizations: Organization[]
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'success': []
}>()

const organizationStore = useOrganizationStore()

// 状态
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const formRef = ref()
const loading = ref(false)
const analyzing = ref(false)
const impactAnalysis = ref<unknown>(null)

// 表单数据
const formData = reactive({
  sourceIds: [] as string[],
  targetId: '',
  reason: '',
  effectiveDate: ''
})

// 表单验证规则
const formRules = {
  sourceIds: [
    { 
      required: true, 
      message: '请选择要合并的源机构', 
      trigger: 'change',
      type: 'array',
      min: 1
    }
  ],
  targetId: [
    { required: true, message: '请选择目标机构', trigger: 'change' }
  ],
  reason: [
    { required: true, message: '请输入合并原因', trigger: 'blur' }
  ],
  effectiveDate: [
    { required: true, message: '请选择生效日期', trigger: 'change' }
  ]
}

// 计算属性
const availableOrgs = computed(() => {
  return props.organizations.filter(org => 
    org.status === 'ACTIVE' && 
    (!org.children || org.children.length === 0)
  )
})

const treeData = computed(() => {
  return organizationStore.organizationTree
})

// 监听表单变化，清除影响分析
watch([() => formData.sourceIds, () => formData.targetId], () => {
  impactAnalysis.value = null
})

// 分析影响
const analyzeImpact = async () => {
  if (!formData.sourceIds.length || !formData.targetId) {
    ElMessage.warning('请先选择源机构和目标机构')
    return
  }
  
  analyzing.value = true
  try {
    // 调用API分析影响
    const result = await organizationApi.analyzeMergeImpact({
      sourceIds: formData.sourceIds,
      targetId: formData.targetId
    })
    
    // 如果API调用失败，使用模拟数据
    // 模拟影响分析结果
    impactAnalysis.value = result?.data || {
      affectedEmployees: Math.floor(Math.random() * 100) + 10,
      affectedEstablishment: Math.floor(Math.random() * 20) + 5,
      historicalRecords: Math.floor(Math.random() * 1000) + 100,
      relatedData: Math.floor(Math.random() * 50) + 10
    }
  } catch (__error) {
    console.error('分析影响失败:', error)
    ElMessage.error('分析影响失败，请重试')
  } finally {
    analyzing.value = false
  }
}

// 确认合并
const handleConfirm = async () => {
  const valid = await formRef.value?.validate()
  if (!valid) return
  
  try {
    await ElMessageBox.confirm(
      `确定要将 ${formData.sourceIds.length} 个机构合并到目标机构吗？此操作不可逆。`,
      '二次确认',
      {
        confirmButtonText: '确定合并',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    loading.value = true
    
    // 调用API执行合并
    await organizationApi.merge({
      sourceIds: formData.sourceIds,
      targetId: formData.targetId,
      reason: formData.reason,
      effectiveDate: formData.effectiveDate
    })
    
    ElMessage.success('机构合并成功')
    emit('success')
    visible.value = false
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('机构合并失败:', error)
      ElMessage.error('机构合并失败，请重试')
    }
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  formRef.value?.resetFields()
  impactAnalysis.value = null
}
</script>

<style lang="scss" scoped>
.el-card {
  background-color: #f5f7fa;
  border: none;
  
  :deep(.el-descriptions__label) {
    font-weight: normal;
  }
}
</style>