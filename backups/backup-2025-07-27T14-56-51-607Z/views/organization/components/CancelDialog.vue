<template>
  <el-dialog
    v-model="visible"
    title="撤销机构"
    width="600px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-alert type="error" :closable="false" style="margin-bottom: 20px">
        <template #default>
          <div>
            <p>撤销机构注意事项：</p>
            <ul style="margin: 5px 0 0 20px; padding: 0;">
              <li>撤销后，机构将被标记为"已撤销"状态</li>
              <li>需要先处理机构下的所有人员和未完成业务</li>
              <li>历史数据将被固化保存，供后续查询</li>
              <li>此操作不可逆，请谨慎操作</li>
            </ul>
          </div>
        </template>
      </el-alert>
      
      <el-form-item label="撤销机构">
        <el-input :value="organization?.institutionName" disabled   />
      </el-form-item>
      
      <el-form-item label="机构编码">
        <el-input :value="organization?.institutionCode" disabled   />
      </el-form-item>
      
      <!-- 前置检查 -->
      <el-form-item label="前置检查">
        <el-card shadow="never">
          <el-result
            v-if="!preCheck"
            icon="info"
            title="请先进行前置检查"
          >
            <template #extra>
              <el-button type="primary" @click="runPreCheck" :loading="checking">
                执行检查
              </el-button>
            </template>
          </el-result>
          
          <div v-else>
            <el-descriptions :column="1" size="small">
              <el-descriptions-item label="下属人员">
                <el-tag :type="preCheck.hasEmployees ? 'danger' : 'success'">
                  {{ preCheck.employeeCount }} 人
                </el-tag>
                <el-button
                  v-if="preCheck.hasEmployees"
                  type="primary"
                  link
                  size="small"
                  @click="handleTransferEmployees"
                >
                  处理人员
                </el-button>
              </el-descriptions-item>
              
              <el-descriptions-item label="下级机构">
                <el-tag :type="preCheck.hasSubOrgs ? 'danger' : 'success'">
                  {{ preCheck.subOrgCount }} 个
                </el-tag>
                <el-button
                  v-if="preCheck.hasSubOrgs"
                  type="primary"
                  link
                  size="small"
                  @click="handleSubOrgs"
                >
                  处理机构
                </el-button>
              </el-descriptions-item>
              
              <el-descriptions-item label="未完成业务">
                <el-tag :type="preCheck.hasPendingBusiness ? 'danger' : 'success'">
                  {{ preCheck.pendingBusinessCount }} 项
                </el-tag>
                <el-button
                  v-if="preCheck.hasPendingBusiness"
                  type="primary"
                  link
                  size="small"
                  @click="handlePendingBusiness"
                >
                  查看详情
                </el-button>
              </el-descriptions-item>
            </el-descriptions>
            
            <el-alert
              v-if="canProceed"
              type="success"
              :closable="false"
              style="margin-top: 10px"
            >
              前置检查通过，可以继续撤销操作
            </el-alert>
            <el-alert
              v-else
              type="error"
              :closable="false"
              style="margin-top: 10px"
            >
              请先处理上述问题后再进行撤销操作
            </el-alert>
          </div>
        </el-card>
      </el-form-item>
      
      <template v-if="canProceed">
        <el-form-item label="撤销日期" prop="withdrawDate">
          <el-date-picker
            v-model="formData.withdrawDate"
            type="date"
            placeholder="请选择撤销日期"
            value-format="YYYY-MM-DD"
            style="width: 100%"
           />
        </el-form-item>
        
        <el-form-item label="撤销原因" prop="reason">
          <el-input
            v-model="formData.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入撤销原因"
            />
        </el-form-item>
        
        <el-form-item label="批文号" prop="approvalDocNumber">
          <el-input
            v-model="formData.approvalDocNumber"
            placeholder="请输入批文号或依据文件"
            />
        </el-form-item>
      </template>
    </el-form>
    
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button
        type="danger"
        @click="handleConfirm"
        :loading="loading"
        :disabled="!canProceed"
      >
        确认撤销
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'CancelDialog'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useOrganizationStore } from '@/stores/modules/organization'
import { organizationApi } from '@/api/organization'
import type { Organization } from '@/types/organization'

const props = defineProps<{
  modelValue: boolean
  organization: Organization | null
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'success': []
  'transferEmployees': [organization: Organization]
  'handleSubOrgs': [organization: Organization]
  'handlePendingBusiness': [organization: Organization]
}>()

const organizationStore = useOrganizationStore()

// 状态
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const formRef = ref()
const loading = ref(false)
const checking = ref(false)
const preCheck = ref<unknown>(null)

// 表单数据
const formData = reactive({
  withdrawDate: '',
  reason: '',
  approvalDocNumber: ''
})

// 表单验证规则
const formRules = {
  withdrawDate: [
    { required: true, message: '请选择撤销日期', trigger: 'change' }
  ],
  reason: [
    { required: true, message: '请输入撤销原因', trigger: 'blur' }
  ]
}

// 计算属性
const canProceed = computed(() => {
  if (!preCheck.value) return false
  return !preCheck.value.hasEmployees && 
         !preCheck.value.hasSubOrgs && 
         !preCheck.value.hasPendingBusiness
})

// 执行前置检查
const runPreCheck = async () => {
  if (!props.organization) return
  
  checking.value = true
  try {
    // 调用API进行前置检查
    const result = await organizationApi.preCheckCancel(props.organization.institutionId)
    
    preCheck.value = {
      hasEmployees: result.employeeCount > 0,
      employeeCount: result.employeeCount,
      hasSubOrgs: result.subOrgCount > 0,
      subOrgCount: result.subOrgCount,
      hasPendingBusiness: result.pendingBusinessCount > 0,
      pendingBusinessCount: result.pendingBusinessCount
    }
  } catch (__error) {
    console.error('前置检查失败:', error)
    ElMessage.error('前置检查失败，请重试')
  } finally {
    checking.value = false
  }
}

// 处理人员
const handleTransferEmployees = () => {
  ElMessage.info('请先将人员转移到其他机构')
  // 打开人员转移对话框
  emit('transferEmployees', props.organization!)
}

// 处理下级机构
const handleSubOrgs = () => {
  ElMessage.info('请先处理下级机构')
  // 打开机构处理对话框
  emit('handleSubOrgs', props.organization!)
}

// 处理未完成业务
const handlePendingBusiness = () => {
  ElMessage.info('请先处理未完成的业务')
  // 打开业务处理对话框
  emit('handlePendingBusiness', props.organization!)
}

// 确认撤销
const handleConfirm = async () => {
  const valid = await formRef.value?.validate()
  if (!valid) return
  
  if (!props.organization) return
  
  try {
    // 生成影响评估报告
    const impactReport = `
撤销机构：${props.organization.institutionName}
机构编码：${props.organization.institutionCode}
撤销日期：${formData.withdrawDate}
撤销原因：${formData.reason}

影响范围：
- 该机构的所有历史数据将被固化保存
- 相关人员的任职记录将被保留
- 所有引用该机构的地方将显示"已撤销"标记
    `.trim()
    
    await ElMessageBox.confirm(
      impactReport,
      '影响评估报告',
      {
        confirmButtonText: '确认撤销',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
        customClass: 'impact-report-dialog'
      }
    )
    
    loading.value = true
    
    // 调用API执行撤销
    await organizationApi.cancel({
      institutionId: props.organization.institutionId,
      reason: formData.reason,
      effectiveDate: formData.withdrawDate,
      transferStrategy: 'DEFAULT',
      attachments: []
    })
    
    await organizationStore.deleteOrganization(props.organization.institutionId)
    
    ElMessage.success('机构撤销成功')
    emit('success')
    visible.value = false
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('机构撤销失败:', error)
      ElMessage.error('机构撤销失败，请重试')
    }
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  formRef.value?.resetFields()
  preCheck.value = null
}
</script>

<style lang="scss" scoped>
.el-card {
  background-color: #f5f7fa;
  border: none;
  
  :deep(.el-result) {
    padding: 20px 0;
  }
  
  :deep(.el-descriptions__label) {
    font-weight: normal;
  }
}

:global(.impact-report-dialog) {
  white-space: pre-line;
}
</style>