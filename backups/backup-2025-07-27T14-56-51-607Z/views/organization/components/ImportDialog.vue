<template>
  <el-dialog
    v-model="visible"
    title="批量导入机构"
    width="700px"
    @close="handleClose"
  >
    <el-steps :active="currentStep" finish-status="success" style="margin-bottom: 30px">
      <el-step title="下载模板"  />
      <el-step title="上传文件"  />
      <el-step title="数据校验"  />
      <el-step title="导入确认"  />
    </el-steps>
    
    <!-- 步骤1：下载模板 -->
    <div v-show="currentStep === 0" class="step-content">
      <el-result icon="info">
        <template #title>
          <span>请先下载导入模板</span>
        </template>
        <template #sub-title>
          <span>请按照模板格式填写机构信息，确保数据准确无误</span>
        </template>
        <template #extra>
          <el-button type="primary" @click="downloadTemplate">
            <el-icon><Download /></el-icon>
            下载Excel模板
          </el-button>
        </template>
      </el-result>
      
      <el-alert type="info" :closable="false" style="margin-top: 20px">
        <template #default>
          <div>
            <p>模板填写说明：</p>
            <ul style="margin: 5px 0 0 20px; padding: 0;">
              <li>机构编码必须唯一，建议使用有规律的编码体系</li>
              <li>上级机构编码必须是已存在的机构编码，留空表示顶级机构</li>
              <li>机构类型必须是：学院、部门、教学部、科研机构、行政机构、直属单位、临时组织之一</li>
              <li>日期格式：YYYY-MM-DD，如：2024-01-01</li>
              <li>每次最多导入1000条记录</li>
            </ul>
          </div>
        </template>
      </el-alert>
    </div>
    
    <!-- 步骤2：上传文件 -->
    <div v-show="currentStep === 1" class="step-content">
      <el-upload
        ref="uploadRef"
        class="upload-demo"
        drag
        :action="'#'"
        :auto-upload="false"
        :limit="1"
        accept=".xlsx,.xls"
        :on-change="handleFileChange"
        :on-exceed="handleExceed"
      >
        <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">只支持 .xlsx/.xls 格式文件，文件大小不超过 10MB</div>
        </template>
      </el-upload>
    </div>
    
    <!-- 步骤3：数据校验 -->
    <div v-show="currentStep === 2" class="step-content">
      <div v-loading="validating">
        <el-alert
          v-if="validationResult"
          :type="validationResult.hasError ? 'error' : 'success'"
          :closable="false"
          style="margin-bottom: 20px"
        >
          <template #default>
            <div>
              <p>数据校验{{ validationResult.hasError ? '失败' : '成功' }}</p>
              <el-descriptions :column="2" size="small" style="margin-top: 10px">
                <el-descriptions-item label="总记录数">
                  {{ validationResult.total }}
                </el-descriptions-item>
                <el-descriptions-item label="有效记录">
                  <el-tag type="success">{{ validationResult.valid }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="错误记录">
                  <el-tag type="danger">{{ validationResult.error }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="警告记录">
                  <el-tag type="warning">{{ validationResult.warning }}</el-tag>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </template>
        </el-alert>
        
        <el-table
          v-if="validationResult?.errors?.length"
          :data="validationResult.errors"
          style="width: 100%"
          max-height="300"
        >
          <el-table-column prop="row" label="行号" width="80"  />
          <el-table-column prop="field" label="字段" width="120"  />
          <el-table-column prop="value" label="值"  />
          <el-table-column prop="message" label="错误信息"  />
        </el-table>
      </div>
    </div>
    
    <!-- 步骤4：导入确认 -->
    <div v-show="currentStep === 3" class="step-content">
      <el-result
        v-if="!importResult"
        icon="info"
        title="准备导入"
      >
        <template #sub-title>
          <span>共 {{ validationResult?.valid || 0 }} 条有效数据待导入</span>
        </template>
        <template #extra>
          <el-button type="primary" @click="handleImport" :loading="importing">
            确认导入
          </el-button>
        </template>
      </el-result>
      
      <el-result
        v-else
        :icon="importResult.success ? 'success' : 'error'"
        :title="importResult.success ? '导入成功' : '导入失败'"
      >
        <template #sub-title>
          <el-descriptions :column="2" size="small">
            <el-descriptions-item label="成功导入">
              {{ importResult.successCount }} 条
            </el-descriptions-item>
            <el-descriptions-item label="失败记录">
              {{ importResult.failCount }} 条
            </el-descriptions-item>
          </el-descriptions>
        </template>
        <template #extra>
          <el-button
            v-if="importResult.failCount > 0"
            type="primary"
            @click="downloadErrorFile"
          >
            下载错误数据
          </el-button>
        </template>
      </el-result>
    </div>
    
    <template #footer>
      <el-button @click="visible = false">关闭</el-button>
      <el-button
        v-if="currentStep > 0 && currentStep < 3"
        @click="currentStep--"
      >
        上一步
      </el-button>
      <el-button
        v-if="currentStep < 2"
        type="primary"
        @click="handleNext"
        :disabled="currentStep === 1 && !uploadFile"
      >
        下一步
      </el-button>
      <el-button
        v-if="currentStep === 3 && importResult?.success"
        type="primary"
        @click="handleComplete"
      >
        完成
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'ImportDialog'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, UploadFilled } from '@element-plus/icons-vue'
import { useOrganizationStore } from '@/stores/modules/organization'
import * as XLSX from 'xlsx'

const props = defineProps<{
  modelValue: boolean
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'success': []
}>()

const organizationStore = useOrganizationStore()

// 状态
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const currentStep = ref(0)
const uploadRef = ref()
const uploadFile = ref<File | null>(null)
const validating = ref(false)
const validationResult = ref<unknown>(null)
const importing = ref(false)
const importResult = ref<unknown>(null)

// 下载模板
const downloadTemplate = () => {
  // 创建模板数据
  const templateData = [
    {
      '机构名称': '示例学院',
      '机构编码': 'XY001',
      '机构类型': '学院',
      '上级机构编码': '',
      '成立日期': '2024-01-01',
      '职责描述': '负责XX专业教学和科研工作',
      '联系电话': '0571-12345678',
      '邮箱': '<EMAIL>',
      '办公地址': 'A栋301室'
    }
  ]
  
  // 创建工作簿
  const ws = XLSX.utils.json_to_sheet(templateData)
  const wb = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(wb, ws, '机构信息')
  
  // 设置列宽
  ws['!cols'] = [
    { wch: 20 }, // 机构名称
    { wch: 15 }, // 机构编码
    { wch: 15 }, // 机构类型
    { wch: 15 }, // 上级机构编码
    { wch: 15 }, // 成立日期
    { wch: 30 }, // 职责描述
    { wch: 15 }, // 联系电话
    { wch: 20 }, // 邮箱
    { wch: 25 }  // 办公地址
  ]
  
  // 导出文件
  XLSX.writeFile(wb, '机构批量导入模板.xlsx')
  ElMessage.success('模板下载成功')
}

// 文件选择
   
const handleFileChange = (file: unknown) => {
  uploadFile.value = file.raw
}

// 文件超出限制
const handleExceed = () => {
  ElMessage.warning('只能上传一个文件')
}

// 下一步
const handleNext = async () => {
  if (currentStep.value === 1 && uploadFile.value) {
    await validateFile()
  }
  if (currentStep.value < 3) {
    currentStep.value++
  }
}

// 校验文件
const validateFile = async () => {
  if (!uploadFile.value) return
  
  validating.value = true
  try {
    // 读取文件
    const reader = new FileReader()
    const fileData = await new Promise<any[]>((_resolve) => {
      reader.onload = (_e) => {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })
        const sheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[sheetName]
        const jsonData = XLSX.utils.sheet_to_json(worksheet)
        resolve(jsonData)
      }
      reader.readAsArrayBuffer(uploadFile.value!)
    })
    
    // 数据校验
   
    const errors: unknown[] = []
   
    const warnings: unknown[] = []
    const validTypes = ['学院', '部门', '教学部', '科研机构', '行政机构', '直属单位', '临时组织']
    let validCount = 0
    
   
    fileData.forEach((row: unknown, index: number) => {
      const rowNum = index + 2 // Excel行号从2开始（第1行是表头）
      let hasError = false
      
      // 必填字段校验
      if (!row['机构名称']) {
        errors.push({ row: rowNum, field: '机构名称', value: '', message: '机构名称不能为空' })
        hasError = true
      }
      if (!row['机构编码']) {
        errors.push({ row: rowNum, field: '机构编码', value: '', message: '机构编码不能为空' })
        hasError = true
      }
      if (!row['机构类型']) {
        errors.push({ row: rowNum, field: '机构类型', value: '', message: '机构类型不能为空' })
        hasError = true
      }
      
      // 机构类型校验
      if (row['机构类型'] && !validTypes.includes(row['机构类型'])) {
        errors.push({ 
          row: rowNum, 
          field: '机构类型', 
          value: row['机构类型'], 
          message: `机构类型无效，必须是以下之一：${validTypes.join('、')}` 
        })
        hasError = true
      }
      
      // 日期格式校验
      if (row['成立日期']) {
        const datePattern = /^\d{4}-\d{2}-\d{2}$/
        if (!datePattern.test(row['成立日期'])) {
          errors.push({ 
            row: rowNum, 
            field: '成立日期', 
            value: row['成立日期'], 
            message: '日期格式错误，应为YYYY-MM-DD' 
          })
          hasError = true
        }
      }
      
      // 邮箱格式校验
      if (row['邮箱']) {
        const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailPattern.test(row['邮箱'])) {
          warnings.push({ 
            row: rowNum, 
            field: '邮箱', 
            value: row['邮箱'], 
            message: '邮箱格式不正确' 
          })
        }
      }
      
      if (!hasError) {
        validCount++
      }
    })
    
    // 设置校验结果
    validationResult.value = {
      total: fileData.length,
      valid: validCount,
      error: errors.length,
      warning: warnings.length,
      hasError: errors.length > 0,
      errors: errors.slice(0, 10), // 最多显示10条错误
      data: fileData // 保存原始数据供导入使用
    }
    
    if (fileData.length === 0) {
      ElMessage.error('文件中没有数据')
      currentStep.value = 1
    } else if (fileData.length > 1000) {
      ElMessage.error('单次导入不能超过1000条记录')
      currentStep.value = 1
    }
  } catch (__error) {
    console.error('文件校验失败:', error)
    ElMessage.error('文件校验失败，请检查文件格式')
    currentStep.value = 1
  } finally {
    validating.value = false
  }
}

// 执行导入
const handleImport = async () => {
  importing.value = true
  try {
    // 调用API执行导入
    const formData = new FormData()
    formData.append('file', uploadFile.value!)
    
    const result = await organizationStore.importOrganizations(formData)
    
    // 使用API返回的实际结果
    importResult.value = {
      success: result.failCount === 0,
      successCount: result.successCount,
      failCount: result.failCount,
      errors: result.errors || []
    }
    
    if (importResult.value.success) {
      ElMessage.success(`成功导入 ${result.successCount} 条记录`)
    } else {
      ElMessage.warning(`导入完成：成功 ${result.successCount} 条，失败 ${result.failCount} 条`)
    }
  } catch (__error) {
    console.error('导入失败:', error)
    importResult.value = {
      success: false,
      successCount: 0,
      failCount: validationResult.value.valid,
      errors: ['导入过程中发生错误']
    }
    ElMessage.error('导入失败，请检查数据格式后重试')
  } finally {
    importing.value = false
  }
}

// 下载错误文件
const downloadErrorFile = () => {
  // 实现下载错误数据文件
  if (!importResult.value?.errors || importResult.value.errors.length === 0) {
    ElMessage.warning('没有错误数据')
    return
  }
  
  // 创建错误数据
  const errorData = importResult.value.errors.map((error: string, index: number) => ({
    '行号': index + 2, // Excel从第2行开始（第1行是表头）
    '错误信息': error
  }))
  
  // 创建工作簿
  const ws = XLSX.utils.json_to_sheet(errorData)
  const wb = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(wb, ws, '错误信息')
  
  // 导出文件
  const fileName = `导入错误信息_${new Date().toISOString().split('T')[0]}.xlsx`
  XLSX.writeFile(wb, fileName)
  ElMessage.success('错误信息已导出')
}

// 完成
const handleComplete = () => {
  emit('success')
  visible.value = false
}

// 关闭对话框
const handleClose = () => {
  currentStep.value = 0
  uploadFile.value = null
  validationResult.value = null
  importResult.value = null
  uploadRef.value?.clearFiles()
}
</script>

<style lang="scss" scoped>
.step-content {
  min-height: 300px;
  
  .upload-demo {
    text-align: center;
    
    :deep(.el-upload-dragger) {
      padding: 40px;
    }
  }
}

.el-result {
  padding: 20px 0;
}
</style>