<template>
  <el-dialog
    v-model="visible"
    title="变更影响分析"
    width="900px"
    @open="handleOpen"
  >
    <div v-loading="loading">
      <!-- 风险等级提示 -->
      <el-alert
        v-if="impactData"
        :type="alertType"
        :closable="false"
        style="margin-bottom: 20px"
      >
        <template #title>
          <span style="font-size: 16px; font-weight: bold;">
            风险等级：{{ riskLevelText }}
          </span>
        </template>
        <template #default>
          <div style="margin-top: 10px;">
            {{ impactData.riskDescription || riskLevelDescription }}
          </div>
        </template>
      </el-alert>
      
      <!-- 影响统计 -->
      <el-row :gutter="20" style="margin-bottom: 20px;">
        <el-col :span="6">
          <el-statistic
            title="受影响员工"
            :value="impactData?.impactSummary.affectedEmployees || 0"
            suffix="人"
          >
            <template #prefix>
              <el-icon><User /></el-icon>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic
            title="受影响岗位"
            :value="impactData?.impactSummary.affectedPositions || 0"
            suffix="个"
          >
            <template #prefix>
              <el-icon><Briefcase /></el-icon>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic
            title="下级机构"
            :value="impactData?.impactSummary.affectedSubOrgs || 0"
            suffix="个"
          >
            <template #prefix>
              <el-icon><OfficeBuilding /></el-icon>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic
            title="未完成业务"
            :value="impactData?.impactSummary.pendingBusiness || 0"
            suffix="项"
          >
            <template #prefix>
              <el-icon><Document /></el-icon>
            </template>
          </el-statistic>
        </el-col>
      </el-row>
      
      <!-- 详细影响分析 -->
      <el-tabs v-model="activeTab">
        <!-- 员工影响 -->
        <el-tab-pane label="员工影响" name="employees">
          <template #label>
            <span>
              员工影响
              <el-badge :value="impactData?.impactDetails.employees.length || 0"  />
            </span>
          </template>
          
          <el-table
            :data="impactData?.impactDetails.employees || []"
            stripe
            max-height="400"
          >
            <el-table-column prop="employeeId" label="员工编号" width="120"  />
            <el-table-column prop="name" label="姓名" width="100"  />
            <el-table-column prop="currentOrg" label="当前所属机构"  />
            <el-table-column prop="impact" label="影响说明">
              <template #default="{ row }">
                <el-tag :type="getImpactTagType(row.impact)">
                  {{ row.impact }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="{ row }">
                <el-button type="text" size="small" @click="viewEmployee(row)">
                  查看详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        
        <!-- 岗位影响 -->
        <el-tab-pane label="岗位影响" name="positions">
          <template #label>
            <span>
              岗位影响
              <el-badge :value="impactData?.impactDetails.positions.length || 0"  />
            </span>
          </template>
          
          <el-table
            :data="impactData?.impactDetails.positions || []"
            stripe
            max-height="400"
          >
            <el-table-column prop="positionId" label="岗位编号" width="120"  />
            <el-table-column prop="name" label="岗位名称"  />
            <el-table-column prop="impact" label="影响说明">
              <template #default="{ row }">
                <el-tag :type="getImpactTagType(row.impact)">
                  {{ row.impact }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        
        <!-- 下级机构影响 -->
        <el-tab-pane label="下级机构" name="organizations">
          <template #label>
            <span>
              下级机构
              <el-badge :value="impactData?.impactDetails.subOrganizations.length || 0"  />
            </span>
          </template>
          
          <el-table
            :data="impactData?.impactDetails.subOrganizations || []"
            stripe
            max-height="400"
          >
            <el-table-column prop="orgId" label="机构ID" width="120"  />
            <el-table-column prop="name" label="机构名称"  />
            <el-table-column prop="impact" label="影响说明">
              <template #default="{ row }">
                <el-tag :type="getImpactTagType(row.impact)">
                  {{ row.impact }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        
        <!-- 业务影响 -->
        <el-tab-pane label="业务影响" name="business">
          <template #label>
            <span>
              业务影响
              <el-badge :value="impactData?.impactDetails.business.length || 0"  />
            </span>
          </template>
          
          <el-table
            :data="impactData?.impactDetails.business || []"
            stripe
            max-height="400"
          >
            <el-table-column prop="businessId" label="业务ID" width="120"  />
            <el-table-column prop="type" label="业务类型" width="120">
              <template #default="{ row }">
                {{ getBusinessTypeName(row.type) }}
              </template>
            </el-table-column>
            <el-table-column prop="description" label="业务描述"  />
          </el-table>
        </el-tab-pane>
      </el-tabs>
      
      <!-- 处理建议 -->
      <div v-if="impactData?.suggestions?.length" style="margin-top: 20px;">
        <el-divider content-position="left">处理建议</el-divider>
        <el-card>
          <ul style="margin: 0; padding-left: 20px;">
            <li v-for="(suggestion, index) in impactData.suggestions" :key="index">
              {{ suggestion }}
            </li>
          </ul>
        </el-card>
      </div>
    </div>
    
    <template #footer>
      <el-button @click="visible = false">关闭</el-button>
      <el-button type="primary" @click="handleExport">导出报告</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
 
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { User, Briefcase, OfficeBuilding, Document } from '@element-plus/icons-vue'
import { useOrganizationStore } from '@/stores/modules/organization'
import { organizationApi } from '@/api/organization'
import type { ChangeRequest, ImpactAssessment } from '@/types/organization'

const props = defineProps<{
  modelValue: boolean
  changeRequest: ChangeRequest
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

const organizationStore = useOrganizationStore()

// 状态
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const loading = ref(false)
const activeTab = ref('employees')
const impactData = ref<ImpactAssessment | null>(null)

// 计算属性
const alertType = computed(() => {
  if (!impactData.value) return 'info'
  const riskLevel = impactData.value.riskLevel
  return riskLevel === 'HIGH' ? 'error' : riskLevel === 'MEDIUM' ? 'warning' : 'success'
})

const riskLevelText = computed(() => {
  if (!impactData.value) return '未知'
  const levels = {
    HIGH: '高风险',
    MEDIUM: '中风险',
    LOW: '低风险'
  }
  return levels[impactData.value.riskLevel] || '未知'
})

const riskLevelDescription = computed(() => {
  if (!impactData.value) return ''
  const descriptions = {
    HIGH: '此次变更将产生重大影响，请谨慎评估并制定详细的执行计划。',
    MEDIUM: '此次变更有一定影响，建议提前做好相关准备工作。',
    LOW: '此次变更影响较小，按正常流程执行即可。'
  }
  return descriptions[impactData.value.riskLevel] || ''
})

// 方法
const handleOpen = async () => {
  loading.value = true
  try {
    // 执行影响分析
    const assessment = await analyzeImpact(props.changeRequest)
    impactData.value = assessment
  } catch (__error) {
    console.error('影响分析失败:', error)
    ElMessage.error('影响分析失败，请重试')
  } finally {
    loading.value = false
  }
}

// 分析影响
const analyzeImpact = async (request: ChangeRequest): Promise<ImpactAssessment> => {
  // 调用后端API进行实际分析
  try {
    // 根据变更类型调用不同的API
    if (request.changeType === 'MERGE') {
      const result = await organizationApi.analyzeMergeImpact({
        sourceIds: request.sourceOrganizations.map(org => org.organizationId),
        targetId: request.targetOrganization.organizationId,
        mergeType: 'ABSORB'
      })
      
      return {
        affectedEmployees: result.affectedEmployees,
        affectedDepartments: result.affectedSubOrgs,
        relatedProcesses: [],
        requiredActions: result.otherImpacts,
        riskLevel: result.affectedEmployees.length > 50 ? 'HIGH' : 'MEDIUM',
        estimatedImpactDuration: 30
      }
    }
  } catch (__error) {
    console.error('影响分析失败:', error)
    // 使用模拟数据作为备用
  }
  
  // 模拟返回数据作为备用
  
  await new Promise(resolve => setTimeout(resolve, 1500))
  
  const assessment: ImpactAssessment = {
    assessmentId: `ASSESS-${Date.now()}`,
    assessTime: new Date().toISOString(),
    impactSummary: {
      affectedEmployees: 0,
      affectedPositions: 0,
      affectedSubOrgs: 0,
      pendingBusiness: 0
    },
    impactDetails: {
      employees: [],
      positions: [],
      subOrganizations: [],
      business: []
    },
    riskLevel: 'LOW',
    suggestions: []
  }
  
  // 根据变更类型模拟不同的影响
  switch (request.changeType) {
    case 'CANCEL':
      assessment.impactSummary = {
        affectedEmployees: 25,
        affectedPositions: 8,
        affectedSubOrgs: 3,
        pendingBusiness: 5
      }
      assessment.impactDetails.employees = [
        { employeeId: 'EMP001', name: 'HrHr张三', currentOrg: request.institutionName || '', impact: '需要重新分配' },
        { employeeId: 'EMP002', name: '李四', currentOrg: request.institutionName || '', impact: '需要重新分配' }
      ]
      assessment.riskLevel = 'HIGH'
      assessment.suggestions = [
        '建议先完成所有未完成的业务流程',
        '提前制定员工安置计划',
        '与相关部门协调后续工作交接'
      ]
      break
      
    case 'MERGE':
      assessment.impactSummary = {
        affectedEmployees: 50,
        affectedPositions: 15,
        affectedSubOrgs: 5,
        pendingBusiness: 10
      }
      assessment.impactDetails.employees = [
        { employeeId: 'EMP003', name: '王五', currentOrg: '部门A', impact: '部门调整' },
        { employeeId: 'EMP004', name: '赵六', currentOrg: '部门B', impact: '部门调整' }
      ]
      assessment.riskLevel = 'MEDIUM'
      assessment.suggestions = [
        '建议分阶段执行合并计划',
        '做好员工思想工作，确保平稳过渡',
        '统一相关制度和流程'
      ]
      break
      
    case 'TRANSFER':
      assessment.impactSummary = {
        affectedEmployees: 15,
        affectedPositions: 5,
        affectedSubOrgs: 2,
        pendingBusiness: 3
      }
      assessment.riskLevel = 'LOW'
      assessment.suggestions = [
        '确保新旧上级部门做好工作交接',
        '更新相关权限和流程配置'
      ]
      break
      
    default:
      assessment.riskLevel = 'LOW'
  }
  
  return assessment
}

const getImpactTagType = (impact: string) => {
  if (impact.includes('重新分配') || impact.includes('撤销')) {
    return 'danger'
  }
  if (impact.includes('调整') || impact.includes('变更')) {
    return 'warning'
  }
  return 'info'
}

const getBusinessTypeName = (type: string) => {
  const types: Record<string, string> = {
    APPROVAL: '审批流程',
    CONTRACT: '合同业务',
    SALARY: '薪资业务',
    ATTENDANCE: '考勤业务',
    RECRUITMENT: '招聘业务'
  }
  return types[type] || type
}

   
const viewEmployee = (employee: unknown) => {
  // 跳转到员工详情页
  const router = useRouter()
  router.push({
    name: 'EmployeeDetail',
    params: { id: employee.employeeId }
  })
}

const handleExport = () => {
  // 导出影响分析报告
  const reportContent = {
    request: request.value,
    impact: impact.value,
    generateTime: new Date().toISOString()
  }
  
  // 生成JSON文件下载
  const blob = new Blob([JSON.stringify(reportContent, null, 2)], { type: 'application/json' })
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `影响分析报告_${new Date().getTime()}.json`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
  
  ElMessage.success('报告导出成功')
}
</script>

<style lang="scss" scoped>
.el-statistic {
  text-align: center;
  
  :deep(.el-statistic__head) {
    color: #909399;
  }
  
  :deep(.el-statistic__content) {
    display: flex;
    align-items: center;
    justify-content: center;
    
    .el-icon {
      margin-right: 8px;
      font-size: 24px;
      color: #409eff;
    }
  }
}

.el-badge {
  margin-left: 5px;
  
  :deep(.el-badge__content) {
    height: 18px;
    line-height: 18px;
    padding: 0 6px;
  }
}
</style>