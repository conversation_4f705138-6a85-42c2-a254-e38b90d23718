<template>
  <el-dialog
    v-model="visible"
    title="审批流程"
    width="900px"
  >
    <div class="approval-flow-container">
      <!-- 流程进度 -->
      <div class="flow-progress">
        <el-steps
          :active="currentStep"
          :process-status="processStatus"
          finish-status="success"
        >
          <el-step
            v-for="step in approvalSteps"
            :key="step.stepId"
            :title="step.stepName"
            :description="getStepDescription(step)"
            :status="getStepStatus(step)"
           />
        </el-steps>
      </div>
      
      <!-- 当前待办信息 -->
      <el-alert
        v-if="showPendingInfo"
        type="info"
        :closable="false"
        style="margin: 20px 0;"
      >
        <template #title>
          当前待办人：{{ currentApproversText }}
        </template>
      </el-alert>
      
      <!-- 审批记录 -->
      <el-divider content-position="left">审批记录</el-divider>
      <el-timeline>
        <el-timeline-item
          v-for="record in approvalRecords"
          :key="record.stepId"
          :timestamp="record.approvalTime"
          :type="getTimelineType(record.action)"
          :hollow="false"
          placement="top"
        >
          <el-card shadow="hover">
            <div class="approval-record">
              <div class="record-header">
                <span class="approver">{{ record.approverName }}</span>
                <el-tag :type="getActionTagType(record.action)" size="small">
                  {{ getActionText(record.action) }}
                </el-tag>
              </div>
              <div v-if="record.comment" class="record-comment">
                <el-icon><Comment /></el-icon>
                {{ record.comment }}
              </div>
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
      
      <!-- 审批操作区（仅当前审批人可见） -->
      <div v-if="showApprovalActions" class="approval-actions">
        <el-divider content-position="left">审批操作</el-divider>
        <el-form ref="approvalFormRef" :model="approvalForm" label-width="100px">
          <el-form-item label="审批意见" prop="comment">
            <el-input
              v-model="approvalForm.comment"
              type="textarea"
              :rows="3"
              placeholder="请输入审批意见（选填）"
              />
          </el-form-item>
          <el-form-item>
            <el-button type="success" @click="handleApprove">
              <el-icon><Select /></el-icon>
              通过
            </el-button>
            <el-button type="danger" @click="handleReject">
              <el-icon><CloseBold /></el-icon>
              驳回
            </el-button>
            <el-button type="warning" @click="handleReturn">
              <el-icon><Back /></el-icon>
              退回
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    
    <template #footer>
      <el-button @click="visible = false">关闭</el-button>
      <el-button v-if="canWithdraw" type="warning" @click="handleWithdraw">
        撤回申请
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">

defineOptions({
  name: 'ApprovalFlowDialog'
})
 
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Select, CloseBold, Back, Comment } from '@element-plus/icons-vue'
import type { ChangeRequest, ApprovalFlowInfo, ApprovalStepInfo } from '@/types/organization'

const props = defineProps<{
  modelValue: boolean
  changeRequest: ChangeRequest
  isApprover?: boolean  // 是否为当前审批人
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'approve': [comment: string]
  'reject': [comment: string]
  'return': [comment: string]
  'withdraw': []
}>()

// 状态
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const approvalFormRef = ref()
const approvalForm = reactive({
  comment: ''
})

// 计算属性
const approvalFlow = computed(() => props.changeRequest.approvalFlow)

const approvalSteps = computed(() => {
  if (!approvalFlow.value) return []
  return approvalFlow.value.steps
})

const currentStep = computed(() => {
  if (!approvalFlow.value) return 0
  return approvalFlow.value.currentStep - 1
})

const processStatus = computed(() => {
  const status = props.changeRequest.status
  if (status === 'REJECTED') return 'error'
  if (status === 'APPROVED' || status === 'EXECUTED') return 'success'
  return 'process'
})

const approvalRecords = computed(() => {
  if (!approvalSteps.value) return []
  return approvalSteps.value
    .filter(step => step.approvalRecord)
    .map(step => ({
      stepId: step.stepId,
      stepName: step.stepName,
      approverName: step.approvalRecord!.approverName,
      action: step.approvalRecord!.action,
      comment: step.approvalRecord!.comment,
      approvalTime: formatTime(step.approvalRecord!.approvalTime)
    }))
})

const currentApproversText = computed(() => {
  if (!approvalFlow.value?.currentApprovers) return '无'
  return approvalFlow.value.currentApprovers.join('、')
})

const showPendingInfo = computed(() => {
  return props.changeRequest.status === 'APPROVING' && approvalFlow.value?.currentApprovers?.length
})

const showApprovalActions = computed(() => {
  return props.isApprover && props.changeRequest.status === 'APPROVING'
})

const canWithdraw = computed(() => {
  // 只有申请人在待审批状态可以撤回
  return props.changeRequest.status === 'PENDING' || 
         (props.changeRequest.status === 'APPROVING' && currentStep.value === 0)
})

// 方法
const getStepDescription = (step: ApprovalStepInfo) => {
  if (step.approvalRecord) {
    return `${step.approvalRecord.approverName} ${getActionText(step.approvalRecord.action)}`
  }
  return step.approvers.join('、')
}

const getStepStatus = (step: ApprovalStepInfo) => {
  if (!step.approvalRecord) return 'wait'
  
  switch (step.approvalRecord.action) {
    case 'APPROVE':
      return 'success'
    case 'REJECT':
      return 'error'
    case 'RETURN':
      return 'warning'
    default:
      return 'process'
  }
}

const getTimelineType = (action: string) => {
  switch (action) {
    case 'APPROVE':
      return 'success'
    case 'REJECT':
      return 'danger'
    case 'RETURN':
      return 'warning'
    default:
      return 'primary'
  }
}

const getActionTagType = (action: string) => {
  switch (action) {
    case 'APPROVE':
      return 'success'
    case 'REJECT':
      return 'danger'
    case 'RETURN':
      return 'warning'
    default:
      return 'info'
  }
}

const getActionText = (action: string) => {
  const actions: Record<string, string> = {
    APPROVE: '已通过',
    REJECT: '已驳回',
    RETURN: '已退回'
  }
  return actions[action] || action
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleString('zh-CN')
}

// 审批操作
const handleApprove = async () => {
  try {
    await ElMessageBox.confirm(
      '确定通过此申请吗？',
      '审批确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    emit('approve', approvalForm.comment)
    ElMessage.success('审批通过')
    visible.value = false
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('审批失败:', error)
    }
  }
}

const handleReject = async () => {
  if (!approvalForm.comment) {
    ElMessage.warning('驳回时必须填写审批意见')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      '确定驳回此申请吗？',
      '驳回确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    emit('reject', approvalForm.comment)
    ElMessage.success('已驳回')
    visible.value = false
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('驳回失败:', error)
    }
  }
}

const handleReturn = async () => {
  if (!approvalForm.comment) {
    ElMessage.warning('退回时必须填写审批意见')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      '确定退回此申请到申请人吗？',
      '退回确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    emit('return', approvalForm.comment)
    ElMessage.success('已退回')
    visible.value = false
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('退回失败:', error)
    }
  }
}

const handleWithdraw = async () => {
  try {
    await ElMessageBox.confirm(
      '确定撤回此申请吗？撤回后可以重新编辑并提交。',
      '撤回确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    emit('withdraw')
    ElMessage.success('申请已撤回')
    visible.value = false
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('撤回失败:', error)
    }
  }
}
</script>

<style lang="scss" scoped>
.approval-flow-container {
  .flow-progress {
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }
  
  .approval-record {
    .record-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      
      .approver {
        font-weight: 500;
        color: #303133;
      }
    }
    
    .record-comment {
      display: flex;
      align-items: flex-start;
      color: #606266;
      font-size: 14px;
      
      .el-icon {
        margin-right: 5px;
        margin-top: 2px;
        color: #909399;
      }
    }
  }
  
  .approval-actions {
    margin-top: 30px;
    
    .el-form-item:last-child {
      margin-bottom: 0;
    }
  }
}

:deep(.el-timeline-item__wrapper) {
  padding-left: 40px;
}

:deep(.el-timeline-item__timestamp) {
  color: #909399;
  font-size: 13px;
}
</style>