<template>
  <div class="organization-change-page">
    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" @submit.prevent="handleSearch">
        <el-form-item label="申请编号">
          <el-input v-model="searchForm.requestId" placeholder="请输入申请编号" clearable   />
        </el-form-item>
        <el-form-item label="变更类型">
          <el-select v-model="searchForm.changeType" placeholder="全部类型" clearable>
            <el-option label="新增机构" value="CREATE"  />
            <el-option label="修改信息" value="UPDATE"  />
            <el-option label="划转机构" value="TRANSFER"  />
            <el-option label="撤销机构" value="CANCEL"  />
            <el-option label="合并机构" value="MERGE"  />
            <el-option label="拆分机构" value="SPLIT"  />
          </el-select>
        </el-form-item>
        <el-form-item label="申请状态">
          <el-select v-model="searchForm.status" placeholder="全部状态" clearable>
            <el-option label="草稿" value="DRAFT"  />
            <el-option label="待审批" value="PENDING"  />
            <el-option label="审批中" value="APPROVING"  />
            <el-option label="已通过" value="APPROVED"  />
            <el-option label="已驳回" value="REJECTED"  />
            <el-option label="已取消" value="CANCELLED"  />
            <el-option label="已执行" value="EXECUTED"  />
          </el-select>
        </el-form-item>
        <el-form-item label="申请时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
           />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 操作区域 -->
    <el-card class="action-card">
      <div class="action-bar">
        <div class="left-actions">
          <el-button
            v-permission="'organization:change:create'"
            type="primary"
            :icon="Plus"
            @click="handleCreate"
          >
            发起变更申请
          </el-button>
          <el-button
            v-permission="'organization:version:view'"
            :icon="Document"
            @click="showVersionManagement"
          >
            版本管理
          </el-button>
        </div>
        <div class="right-actions">
          <el-button :icon="Download" @click="handleExport">导出</el-button>
        </div>
      </div>
    </el-card>
    
    <!-- 数据表格 -->
    <el-card>
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="requestId" label="申请编号" width="120" fixed  />
        <el-table-column prop="title" label="变更标题" min-width="200" show-overflow-tooltip  />
        <el-table-column prop="changeType" label="变更类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getChangeTypeTagType(row.changeType)">
              {{ getChangeTypeName(row.changeType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="institutionName" label="变更机构" width="150" show-overflow-tooltip  />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusName(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createByName" label="申请人" width="100"  />
        <el-table-column prop="createTime" label="申请时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="effectiveDate" label="生效日期" width="100">
          <template #default="{ row }">
            {{ row.effectiveDate || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button
              v-if="row.status === 'DRAFT'"
              type="text"
              size="small"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="canAnalyze(row)"
              type="text"
              size="small"
              @click="handleImpactAnalysis(row)"
            >
              影响分析
            </el-button>
            <el-button
              v-if="row.status !== 'DRAFT'"
              type="text"
              size="small"
              @click="handleApprovalFlow(row)"
            >
              审批流程
            </el-button>
            <el-button
              v-if="canExecute(row)"
              type="text"
              size="small"
              @click="handleExecute(row)"
            >
              执行
            </el-button>
            <el-button
              v-if="canCancel(row)"
              type="text"
              size="small"
              @click="handleCancel(row)"
            >
              取消
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pageInfo.currentPage"
        v-model:page-size="pageInfo.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pageInfo.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
    </el-card>
    
    <!-- 变更申请对话框 -->
    <ChangeRequestDialog
      v-model="changeRequestDialogVisible"
      :mode="editMode"
      :change-type="currentChangeType"
      :organization="currentOrganization"
      @success="handleChangeRequestSuccess"
    />
    
    <!-- 影响分析对话框 -->
    <ImpactAnalysisDialog
      v-model="impactAnalysisDialogVisible"
      :change-request="currentRequest!"
    />
    
    <!-- 审批流程对话框 -->
    <ApprovalFlowDialog
      v-model="approvalFlowDialogVisible"
      :change-request="currentRequest!"
      :is-approver="isCurrentUserApprover"
      @approve="handleApprove"
      @reject="handleReject"
      @return="handleReturn"
      @withdraw="handleWithdraw"
    />
    
    <!-- 版本管理对话框 -->
    <VersionManagementDialog v-model="versionManagementDialogVisible" />
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Document, Download } from '@element-plus/icons-vue'
import { useOrganizationStore } from '@/stores/modules/organization'
import { useUserStore } from '@/stores/modules/user'
import organizationApi from '@/api/modules/organization'
import ChangeRequestDialog from './components/ChangeRequestDialog.vue'
import ImpactAnalysisDialog from './components/ImpactAnalysisDialog.vue'
import ApprovalFlowDialog from './components/ApprovalFlowDialog.vue'
import VersionManagementDialog from './components/VersionManagementDialog.vue'
import type { ChangeRequest, ChangeType, Organization } from '@/types/organization'

// store
const organizationStore = useOrganizationStore()
const userStore = useUserStore()

// 状态
const loading = ref(false)
const tableData = ref<ChangeRequest[]>([])
const selectedRows = ref<ChangeRequest[]>([])

// 搜索表单
const searchForm = reactive({
  requestId: '',
  changeType: '',
  status: '',
  dateRange: []
})

// 分页信息
const pageInfo = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 对话框状态
const changeRequestDialogVisible = ref(false)
const impactAnalysisDialogVisible = ref(false)
const approvalFlowDialogVisible = ref(false)
const versionManagementDialogVisible = ref(false)

// 当前操作数据
const editMode = ref<'create' | 'edit'>('create')
const currentChangeType = ref<ChangeType>()
const currentOrganization = ref<Organization>()
const currentRequest = ref<ChangeRequest>()
const isCurrentUserApprover = ref(false)

// 方法
const handleSearch = () => {
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    requestId: '',
    changeType: '',
    status: '',
    dateRange: []
  })
  loadData()
}

const loadData = async () => {
  loading.value = true
  try {
    // 调用API获取数据
    const params = {
      page: pageInfo.currentPage - 1,
      size: pageInfo.pageSize,
      requestId: searchForm.requestId || undefined,
      changeType: searchForm.changeType || undefined,
      status: searchForm.status || undefined,
      startDate: searchForm.dateRange?.[0] || undefined,
      endDate: searchForm.dateRange?.[1] || undefined
    }
    
    const response = await organizationApi.getChangeRequests(params)
    tableData.value = response.data.list
    pageInfo.total = response.data.total
  } catch (__error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
    // 使用空数组作为fallback
    tableData.value = []
    pageInfo.total = 0
  } finally {
    loading.value = false
  }
}

const handleSelectionChange = (selection: ChangeRequest[]) => {
  selectedRows.value = selection
}

const handleSizeChange = () => {
  loadData()
}

const handleCurrentChange = () => {
  loadData()
}

const handleCreate = () => {
  editMode.value = 'create'
  currentChangeType.value = undefined
  currentOrganization.value = undefined
  changeRequestDialogVisible.value = true
}

const handleView = (row: ChangeRequest) => {
  currentRequest.value = row
  // 打开审批流程对话框以查看详情（只读模式）
  isCurrentUserApprover.value = false
  approvalFlowDialogVisible.value = true
}

const handleEdit = (row: ChangeRequest) => {
  editMode.value = 'edit'
  currentRequest.value = row
  currentChangeType.value = row.changeType
  // 获取相关组织信息
  if (row.institutionId) {
    currentOrganization.value = {
      id: row.institutionId,
      name: row.institutionName
    } as Organization
  }
  changeRequestDialogVisible.value = true
}

const handleImpactAnalysis = (row: ChangeRequest) => {
  currentRequest.value = row
  impactAnalysisDialogVisible.value = true
}

const handleApprovalFlow = (row: ChangeRequest) => {
  currentRequest.value = row
  // 判断当前用户是否为审批人
  const currentUserId = userStore.userInfo?.id
  if (currentUserId && row.approvalFlow?.currentApprovers) {
    isCurrentUserApprover.value = row.approvalFlow.currentApprovers.includes(currentUserId)
  } else {
    isCurrentUserApprover.value = false
  }
  approvalFlowDialogVisible.value = true
}

const handleExecute = async (row: ChangeRequest) => {
  try {
    await ElMessageBox.confirm(
      '确定要执行此变更吗？执行后将立即生效。',
      '执行确认',
      {
        confirmButtonText: '确定执行',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 调用API执行变更
    loading.value = true
    await organizationApi.executeChange(row.requestId)
    ElMessage.success('变更执行成功')
    loadData()
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('执行失败:', error)
      ElMessage.error('执行变更失败，请重试')
    }
  } finally {
    loading.value = false
  }
}

const handleCancel = async (row: ChangeRequest) => {
  try {
    await ElMessageBox.confirm(
      '确定要取消此申请吗？',
      '取消确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 根据状态选择不同的API
    loading.value = true
    if (row.status === 'DRAFT' || row.status === 'PENDING') {
      // 草稿或待审批状态，直接取消
      await organizationApi.withdrawChangeRequest(row.requestId)
    } else {
      // 审批中状态，需要审批流程中的取消操作
      await organizationApi.withdrawChangeRequest(row.requestId)
    }
    ElMessage.success('申请已取消')
    loadData()
  } catch (__error) {
    if (error !== 'cancel') {
      console.error('取消失败:', error)
      ElMessage.error('取消申请失败，请重试')
    }
  } finally {
    loading.value = false
  }
}

const handleChangeRequestSuccess = (request: ChangeRequest) => {
  loadData()
}

const handleApprove = async (comment: string) => {
  try {
    loading.value = true
    // 调用API审批通过
    await organizationApi.approveChangeRequest(currentRequest.value!.requestId, {
      comment: comment || '同意'
    })
    ElMessage.success('审批通过')
    approvalFlowDialogVisible.value = false
    loadData()
  } catch (__error) {
    console.error('审批失败:', error)
    ElMessage.error('审批失败，请重试')
  } finally {
    loading.value = false
  }
}

const handleReject = async (comment: string) => {
  try {
    loading.value = true
    // 调用API审批驳回
    await organizationApi.rejectChangeRequest(currentRequest.value!.requestId, {
      comment: comment
    })
    ElMessage.success('已驳回')
    approvalFlowDialogVisible.value = false
    loadData()
  } catch (__error) {
    console.error('驳回失败:', error)
    ElMessage.error('驳回失败，请重试')
  } finally {
    loading.value = false
  }
}

const handleReturn = async (comment: string) => {
  try {
    loading.value = true
    // 调用API退回
    await organizationApi.returnChangeRequest(currentRequest.value!.requestId, {
      comment: comment
    })
    ElMessage.success('已退回')
    approvalFlowDialogVisible.value = false
    loadData()
  } catch (__error) {
    console.error('退回失败:', error)
    ElMessage.error('退回失败，请重试')
  } finally {
    loading.value = false
  }
}

const handleWithdraw = async () => {
  try {
    loading.value = true
    // 调用API撤回
    await organizationApi.withdrawChangeRequest(currentRequest.value!.requestId)
    ElMessage.success('已撤回')
    approvalFlowDialogVisible.value = false
    loadData()
  } catch (__error) {
    console.error('撤回失败:', error)
    ElMessage.error('撤回失败，请重试')
  } finally {
    loading.value = false
  }
}

const handleExport = async () => {
  try {
    // 实现导出功能
    const params = {
      requestId: searchForm.requestId || undefined,
      changeType: searchForm.changeType || undefined,
      status: searchForm.status || undefined,
      startDate: searchForm.dateRange?.[0] || undefined,
      endDate: searchForm.dateRange?.[1] || undefined
    }
    
    ElMessage.info('正在准备导出数据...')
    const blob = await organizationApi.export(params)
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `组织变更申请_${new Date().toISOString().split('T')[0]}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('导出成功')
  } catch (__error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

const showVersionManagement = () => {
  versionManagementDialogVisible.value = true
}

// 辅助方法
const canAnalyze = (row: ChangeRequest) => {
  return ['CANCEL', 'MERGE', 'SPLIT', 'TRANSFER'].includes(row.changeType)
}

const canExecute = (row: ChangeRequest) => {
  return row.status === 'APPROVED'
}

const canCancel = (row: ChangeRequest) => {
  return ['DRAFT', 'PENDING', 'APPROVING'].includes(row.status)
}

const getChangeTypeName = (type: string) => {
  const types: Record<string, string> = {
    CREATE: '新增',
    UPDATE: '修改',
    TRANSFER: '划转',
    CANCEL: '撤销',
    MERGE: '合并',
    SPLIT: '拆分'
  }
  return types[type] || type
}

const getChangeTypeTagType = (type: string) => {
  const types: Record<string, string> = {
    CREATE: 'success',
    UPDATE: 'primary',
    TRANSFER: 'info',
    CANCEL: 'danger',
    MERGE: 'warning',
    SPLIT: 'warning'
  }
  return types[type] || 'info'
}

const getStatusName = (status: string) => {
  const statuses: Record<string, string> = {
    DRAFT: '草稿',
    PENDING: '待审批',
    APPROVING: '审批中',
    APPROVED: '已通过',
    REJECTED: '已驳回',
    CANCELLED: '已取消',
    EXECUTED: '已执行'
  }
  return statuses[status] || status
}

const getStatusTagType = (status: string) => {
  const types: Record<string, string> = {
    DRAFT: 'info',
    PENDING: 'warning',
    APPROVING: 'primary',
    APPROVED: 'success',
    REJECTED: 'danger',
    CANCELLED: 'info',
    EXECUTED: 'success'
  }
  return types[status] || 'info'
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.organization-change-page {
  .search-card {
    margin-bottom: 16px;
  }
  
  .action-card {
    margin-bottom: 16px;
    
    .action-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .el-pagination {
    margin-top: 16px;
    text-align: right;
  }
}
</style>