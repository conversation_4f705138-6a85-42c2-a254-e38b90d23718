<template>
  <div class="organization-management">
    <!-- 视图切换标签 -->
    <el-tabs v-model="activeTab" class="view-tabs" @tab-change="handleTabChange">
      <!-- 列表视图 -->
      <el-tab-pane label="列表视图" name="list">
        <el-container>
          <!-- 左侧组织架构树 -->
          <el-aside width="350px" class="tree-aside">
            <el-card class="tree-card">
              <template #header>
                <div class="card-header">
                  <span>组织架构</span>
                  <el-dropdown @command="handleHeaderAction">
                    <el-button type="primary" size="small">
                      操作 <el-icon class="el-icon--right"><arrow-down /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="create" :icon="Plus">新增根组织</el-dropdown-item>
                        <el-dropdown-item command="relation" :icon="Connection">关系管理</el-dropdown-item>
                        <el-dropdown-item command="change" :icon="Document">变更申请</el-dropdown-item>
                        <el-dropdown-item command="import" :icon="Upload">批量导入</el-dropdown-item>
                        <el-dropdown-item command="export" :icon="Download">数据导出</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </template>
          
          <el-input
            v-model="treeFilterText"
            placeholder="搜索组织"
            :prefix-icon="Search"
            clearable
            class="tree-filter"
            />
          
          <el-tree
            ref="treeRef"
            :data="organizationTree"
            :props="treeProps"
            :filter-node-method="filterNode as unknown"
            node-key="id"
            :expand-on-click-node="false"
            :default-expand-all="false"
            class="organization-tree"
            @node-click="handleNodeClick"
          >
            <template #default="{ node, data }">
              <div class="tree-node">
                <div class="node-content">
                  <el-icon class="node-icon">
                    <OfficeBuilding v-if="data.orgType === 'SCHOOL'" />
                    <School v-else-if="data.orgType === 'COLLEGE'" />
                    <House v-else />
                  </el-icon>
                  <span class="node-label">{{ data.orgName }}</span>
                  <el-tag 
                    v-if="data.positionCount > 0" 
                    size="small" 
                    type="info"
                    class="position-count"
                  >
                    {{ data.positionCount }}个岗位
                  </el-tag>
                </div>
                <div class="node-actions">
                  <el-button
                    type="primary"
                    size="small"
                    text
                    @click.stop="showCreateDialog(data)"
                    :icon="Plus"
                    />
                  <el-button
                    type="warning"
                    size="small"
                    text
                    @click.stop="showEditDialog(data)"
                    :icon="Edit"
                    />
                  <el-button
                    type="danger"
                    size="small"
                    text
                    @click.stop="handleDelete(data)"
                    :icon="Delete"
                    />
                </div>
              </div>
            </template>
          </el-tree>
            </el-card>
          </el-aside>

          <!-- 右侧详情和岗位列表 -->
          <el-main class="main-content">
            <el-card v-if="selectedOrganization">
              <template #header>
                <div class="card-header">
                  <span>{{ selectedOrganization.orgName }} - 详细信息</span>
                  <el-space>
                    <el-button
                      type="primary"
                      size="small"
                      @click="$router.push({ name: 'HrPosition', query: { organizationId: selectedOrganization.id } })"
                    >
                      管理岗位
                    </el-button>
                    <el-button
                      size="small"
                      @click="showChangeRequestDialog(selectedOrganization)"
                      :icon="Document"
                    >
                      申请变更
                    </el-button>
                  </el-space>
                </div>
              </template>

              <OrganizationDetail
                :organization="selectedOrganization"
                @refresh="loadOrganizationTree"
              />
            </el-card>

            <el-card v-else class="empty-card">
              <el-empty description="请选择一个组织查看详情"  />
            </el-card>
          </el-main>
        </el-container>
      </el-tab-pane>

      <!-- 架构图视图 -->
      <el-tab-pane label="架构图视图" name="chart">
        <div class="chart-view">
          <OrganizationChart
            :height="chartHeight"
            :editable="true"
            @node-click="handleChartNodeClick"
            @node-double-click="handleChartNodeDoubleClick"
            @layout-change="handleLayoutChange"
          />
        </div>
      </el-tab-pane>

      <!-- 关系管理视图 -->
      <el-tab-pane label="关系管理" name="relation">
        <OrganizationRelationManager />
      </el-tab-pane>
    </el-tabs>

    <!-- 创建/编辑组织对话框 -->
    <OrganizationDialog
      v-model="dialogVisible"
      :organization="currentOrganization"
      :parent-organization="parentOrganization"
      :is-edit="isEdit"
      @success="handleDialogSuccess"
    />

    <!-- 组织变更申请对话框 -->
    <OrganizationChangeRequestDialog
      v-model="changeRequestDialogVisible"
      :organization="currentOrganization"
      @success="handleChangeRequestSuccess"
    />

    <!-- 关系管理对话框 -->
    <el-dialog
      v-model="relationDialogVisible"
      title="组织关系管理"
      width="80%"
      :close-on-click-modal="false"
    >
      <OrganizationRelationManager />
    </el-dialog>

    <!-- 数据导入对话框 -->
    <OrganizationImportDialog
      v-model="importDialogVisible"
      @success="handleImportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
 
import { ref, onMounted, watch, nextTick, computed } from 'vue'
import { ElMessage, ElMessageBox, ElTree } from 'element-plus'
import {
  Plus, Edit, Delete, Search, OfficeBuilding, School, House,
  Connection, Document, Upload, Download, ArrowDown
} from '@element-plus/icons-vue'
import { organizationApi } from '@/api/organization'
import type { Organization, OrganizationNode } from '@/types/organization'
import HrOrganizationDetail from '@/components/organization/HrOrganizationDetail.vue'
import HrOrganizationDialog from '@/components/organization/HrOrganizationDialog.vue'
import HrOrganizationChart from '@/components/organization/HrOrganizationChart.vue'
import HrOrganizationRelationManager from '@/components/organization/HrOrganizationRelationManager.vue'
import HrOrganizationChangeRequestDialog from '@/components/organization/HrOrganizationChangeRequestDialog.vue'
import HrOrganizationImportDialog from '@/components/organization/HrOrganizationImportDialog.vue'

// 响应式数据
const activeTab = ref('list')
const organizationTree = ref<Organization[]>([])
const selectedOrganization = ref<Organization | null>(null)
const treeFilterText = ref('')
const dialogVisible = ref(false)
const changeRequestDialogVisible = ref(false)
const relationDialogVisible = ref(false)
const importDialogVisible = ref(false)
const currentOrganization = ref<Organization | null>(null)
const parentOrganization = ref<Organization | null>(null)
const isEdit = ref(false)
const treeRef = ref<InstanceType<typeof ElTree>>()

// 计算属性
const chartHeight = computed(() => {
  return window.innerHeight - 200 // 减去标签页和其他元素的高度
})

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'orgName'
}

// 过滤节点
const filterNode = (value: string, data: Organization) => {
  if (!value) return true
  return data.orgName.includes(value) || data.orgCode.includes(value)
}

// 监听搜索文本变化
watch(treeFilterText, (val) => {
  treeRef.value?.filter(val)
})

// 加载组织架构树
const loadOrganizationTree = async () => {
  try {
    organizationTree.value = await organizationApi.getTree()
  } catch (__error) {
    ElMessage.error('加载组织架构失败')
    console.error('Load organization tree error:', error)
  }
}

// 处理节点点击
const handleNodeClick = (data: Organization) => {
  selectedOrganization.value = data
}

// 显示创建对话框
const showCreateDialog = (parent: Organization | null) => {
  currentOrganization.value = null
  parentOrganization.value = parent
  isEdit.value = false
  dialogVisible.value = true
}

// 显示编辑对话框
const showEditDialog = (organization: Organization) => {
  currentOrganization.value = organization
  parentOrganization.value = null
  isEdit.value = true
  dialogVisible.value = true
}

// 处理删除
const handleDelete = async (organization: Organization) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除组织 "${organization.orgName}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await organizationApi.delete(organization.id)
    ElMessage.success('删除成功')
    
    // 如果删除的是当前选中的组织，清空选中状态
    if (selectedOrganization.value?.id === organization.id) {
      selectedOrganization.value = null
    }
    
    // 重新加载树
    await loadOrganizationTree()
   
  } catch (error: unknown) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error('Delete organization error:', error)
    }
  }
}

// 处理对话框成功
const handleDialogSuccess = async () => {
  await loadOrganizationTree()

  // 如果是编辑当前选中的组织，重新获取详情
  if (isEdit.value && selectedOrganization.value && currentOrganization.value) {
    try {
      selectedOrganization.value = await organizationApi.getById(currentOrganization.value.id)
    } catch (__error) {
      console.error('Refresh organization detail error:', error)
    }
  }
}

// 处理标签页切换
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
}

// 处理头部操作
const handleHeaderAction = (command: string) => {
  switch (command) {
    case 'create':
      showCreateDialog(null)
      break
    case 'relation':
      relationDialogVisible.value = true
      break
    case 'change':
      // 显示变更申请管理
      break
    case 'import':
      importDialogVisible.value = true
      break
    case 'export':
      handleExport()
      break
  }
}

// 显示变更申请对话框
const showChangeRequestDialog = (organization: Organization) => {
  currentOrganization.value = organization
  changeRequestDialogVisible.value = true
}

// 处理变更申请成功
const handleChangeRequestSuccess = () => {
  ElMessage.success('变更申请提交成功')
  loadOrganizationTree()
}

// 处理导入成功
const handleImportSuccess = () => {
  ElMessage.success('数据导入成功')
  loadOrganizationTree()
}

// 处理导出
const handleExport = async () => {
  try {
    const blob = await organizationApi.exportOrganizations({
      format: 'excel',
      includeChildren: true
    })

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `组织架构_${new Date().toISOString().split('T')[0]}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (__error) {
    ElMessage.error('导出失败')
    console.error('Export error:', error)
  }
}

// 处理架构图节点点击
const handleChartNodeClick = (node: OrganizationNode) => {
  // 在列表视图中选中对应的组织
  const organization = findOrganizationInTree(node.orgId)
  if (organization) {
    selectedOrganization.value = organization
  }
}

// 处理架构图节点双击
const handleChartNodeDoubleClick = (node: OrganizationNode) => {
  // 切换到列表视图并选中组织
  activeTab.value = 'list'
  handleChartNodeClick(node)
}

// 处理布局变化
   
const handleLayoutChange = (layout: unknown) => {
  console.log('Layout changed:', layout)
}

// 在树中查找组织
const findOrganizationInTree = (orgId: number): Organization | null => {
  const findInNodes = (nodes: Organization[]): Organization | null => {
    for (const node of nodes) {
      if (node.id === orgId) {
        return node
      }
      if (node.children && node.children.length > 0) {
        const found = findInNodes(node.children)
        if (found) return found
      }
    }
    return null
  }

  return findInNodes(organizationTree.value)
}

// 组件挂载时加载数据
onMounted(() => {
  loadOrganizationTree()
})
</script>

<style scoped>
.organization-management {
  height: 100vh;
  padding: 20px;
}

.view-tabs {
  height: calc(100vh - 40px);
}

.view-tabs .el-tab-pane {
  height: calc(100vh - 120px);
  overflow: hidden;
}

.chart-view {
  height: 100%;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.tree-aside {
  margin-right: 20px;
}

.tree-card {
  height: calc(100vh - 40px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tree-filter {
  margin-bottom: 15px;
}

.organization-tree {
  height: calc(100% - 120px);
  overflow-y: auto;
}

.tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-right: 10px;
}

.node-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.node-icon {
  margin-right: 8px;
  color: #409eff;
}

.node-label {
  margin-right: 8px;
}

.position-count {
  margin-left: auto;
  margin-right: 10px;
}

.node-actions {
  display: none;
}

.tree-node:hover .node-actions {
  display: flex;
}

.main-content {
  padding: 0;
}

.empty-card {
  height: calc(100vh - 40px);
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
