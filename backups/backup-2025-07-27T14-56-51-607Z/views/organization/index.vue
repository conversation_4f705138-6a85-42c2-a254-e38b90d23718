<template>
  <div class="organization-management">
    <!-- 顶部工具栏 -->
    <el-card class="toolbar-card" shadow="never">
      <div class="toolbar-content">
        <div class="toolbar-left">
          <el-button type="primary" @click="handleCreate">
            <el-icon><Plus /></el-icon>
            新增机构
          </el-button>
          <el-button @click="handleImport">
            <el-icon><Upload /></el-icon>
            批量导入
          </el-button>
          <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
          <el-button @click="showEstablishmentDialog = true">
            <el-icon><Document /></el-icon>
            编制管理
          </el-button>
        </div>
        <div class="toolbar-right">
          <el-radio-group v-model="viewMode" @change="handleViewChange">
            <el-radio-button value="tree">树形视图</el-radio-button>
            <el-radio-button value="chart">架构图</el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </el-card>

    <!-- 主体区域 - 树形视图 -->
    <el-card v-if="viewMode === 'tree'" class="content-card" shadow="never">
      <el-row :gutter="20">
        <!-- 左侧树形结构 -->
        <el-col :span="8">
          <div class="tree-header">
            <h4>组织架构</h4>
            <el-input
              v-model="treeSearchKeyword"
              placeholder="搜索机构"
              clearable
              @input="handleTreeSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
          <div class="tree-container">
            <HrOrgTree
              ref="orgTreeRef"
              v-model="selectedOrgId"
              :data="treeData"
              :default-expanded-keys="expandedKeys"
              :filter-node-method="filterNode"
              show-checkbox
              draggable
              @node-click="handleNodeClick"
              @node-drop="handleNodeDrop"
              @check-change="handleCheckChange"
            />
          </div>
        </el-col>

        <!-- 右侧详情/列表 -->
        <el-col :span="16">
          <div v-if="selectedOrg" class="detail-container">
            <!-- 机构基本信息 -->
            <div class="detail-header">
              <h3>{{ selectedOrg.name }}</h3>
              <el-space>
                <el-button type="primary" link @click="handleEdit(selectedOrg)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button type="danger" link @click="handleDelete(selectedOrg)">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
                <el-dropdown>
                  <el-button link>
                    更多
                    <el-icon><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="handleMove(selectedOrg)">机构划转</el-dropdown-item>
                      <el-dropdown-item @click="handleMerge">机构合并</el-dropdown-item>
                      <el-dropdown-item @click="handleRevoke(selectedOrg)">
                        机构撤销
                      </el-dropdown-item>
                      <el-dropdown-item divided @click="handleViewHistory">
                        变更历史
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </el-space>
            </div>

            <!-- 机构详情信息 -->
            <el-descriptions :column="2" border class="detail-info">
              <el-descriptions-item label="机构编码">{{ selectedOrg.code }}</el-descriptions-item>
              <el-descriptions-item label="机构类型">
                <el-tag>{{ getOrgTypeName(selectedOrg.type) }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="成立日期">
                {{ formatDate(selectedOrg.establishDate) }}
              </el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="getStatusType(selectedOrg.status) as any">
                  {{ getStatusName(selectedOrg.status) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="上级机构">
                {{ selectedOrg.parentName || '无' }}
              </el-descriptions-item>
              <el-descriptions-item label="机构层级">
                第{{ selectedOrg.level }}级
              </el-descriptions-item>
              <el-descriptions-item label="负责人">
                {{ selectedOrg.leader || '未设置' }}
              </el-descriptions-item>
              <el-descriptions-item label="联系电话">
                {{ selectedOrg.phone || '未设置' }}
              </el-descriptions-item>
            </el-descriptions>

            <!-- 编制信息 -->
            <div class="establishment-info">
              <h4>编制信息</h4>
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-statistic
                    title="核定编制"
                    :value="selectedOrg.establishment?.approved || 0"
                  />
                </el-col>
                <el-col :span="6">
                  <el-statistic
                    title="在编人数"
                    :value="selectedOrg.establishment?.current || 0"
                    :value-style="{ color: getEstablishmentColor(selectedOrg.establishment) }"
                  />
                </el-col>
                <el-col :span="6">
                  <el-statistic
                    title="缺编数"
                    :value="getVacancy(selectedOrg.establishment)"
                    :value-style="{ color: '#67C23A' }"
                  />
                </el-col>
                <el-col :span="6">
                  <el-progress
                    type="circle"
                    :percentage="getUsageRate(selectedOrg.establishment)"
                    :color="getProgressColor"
                  />
                </el-col>
              </el-row>
            </div>

            <!-- 下属机构列表 -->
            <div v-if="childOrgs.length > 0" class="child-orgs">
              <h4>下属机构 ({{ childOrgs.length }})</h4>
              <el-table :data="childOrgs" stripe>
                <el-table-column prop="name" label="机构名称" />
                <el-table-column prop="code" label="机构编码" width="120" />
                <el-table-column prop="type" label="类型" width="100">
                  <template #default="{ row }">
                    <el-tag size="small">{{ getOrgTypeName(row.type) }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="编制" width="120">
                  <template #default="{ row }">
                    {{ row.establishment?.current || 0 }}/{{ row.establishment?.approved || 0 }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120" fixed="right">
                  <template #default="{ row }">
                    <el-button type="primary" link @click="handleNodeClick(row)">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <el-empty v-else description="请选择一个机构查看详情" />
        </el-col>
      </el-row>
    </el-card>

    <!-- 主体区域 - 架构图视图 -->
    <el-card v-else-if="viewMode === 'chart'" class="content-card chart-card" shadow="never">
      <div class="chart-toolbar">
        <el-space>
          <el-button @click="chartRef?.zoomIn()">
            <el-icon><ZoomIn /></el-icon>
            放大
          </el-button>
          <el-button @click="chartRef?.zoomOut()">
            <el-icon><ZoomOut /></el-icon>
            缩小
          </el-button>
          <el-button @click="chartRef?.fitView()">
            <el-icon><FullScreen /></el-icon>
            适应窗口
          </el-button>
          <el-button @click="handleExportChart">
            <el-icon><Picture /></el-icon>
            导出图片
          </el-button>
        </el-space>
      </div>
      <HrOrgChart
        ref="chartRef"
        :data="chartData"
        :height="600"
        @node-click="handleChartNodeClick"
        @node-contextmenu="handleChartNodeContextMenu"
      />
    </el-card>

    <!-- 表单对话框 -->
    <HrOrganizationFormDialog
      :visible="dialogVisible"
      :mode="dialogMode"
      :tree-data="[]"
      :parent-data="null"
      :edit-data="currentOrg as any"
      @update:visible="dialogVisible = $event"
      @success="handleSuccess"
    />

    <!-- 导入对话框 -->
    <HrOrganizationImportDialog v-model="importDialogVisible" @success="handleImportSuccess" />

    <!-- 机构划转对话框 -->
    <el-dialog v-model="moveDialogVisible" title="机构划转" width="500px">
      <el-form :model="moveForm" label-width="100px">
        <el-form-item label="当前机构">
          <el-input :value="moveForm.sourceName" disabled />
        </el-form-item>
        <el-form-item label="目标上级" required>
          <HrOrganizationSelect
            v-model:value="moveForm.targetId"
            placeholder="请选择目标上级机构"
            :exclude-ids="[Number(moveForm.sourceId)]"
          />
        </el-form-item>
        <el-form-item label="划转原因">
          <el-input
            v-model="moveForm.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入划转原因"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="moveDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmMove">确认划转</el-button>
      </template>
    </el-dialog>

    <!-- 编制管理对话框 -->
    <el-dialog v-model="showEstablishmentDialog" title="编制管理" width="800px">
      <el-table :data="establishmentData" stripe>
        <el-table-column prop="name" label="机构名称" />
        <el-table-column prop="approved" label="核定编制" width="100">
          <template #default="{ row }">
            <el-input-number
              v-model="row.approved"
              :min="0"
              :max="9999"
              size="small"
              controls-position="right"
            />
          </template>
        </el-table-column>
        <el-table-column prop="current" label="在编人数" width="100" />
        <el-table-column label="状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getEstablishmentTagType(row)">
              {{ getEstablishmentStatus(row) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <el-button @click="showEstablishmentDialog = false">取消</el-button>
        <el-button type="primary" @click="saveEstablishment">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/**
 * @name 组织架构管理
 * @description 组织机构的增删改查、树形展示、架构图展示、编制管理等功能
 */
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Upload,
  Download,
  Document,
  Search,
  Edit,
  Delete,
  ArrowDown,
  ZoomIn,
  ZoomOut,
  FullScreen,
  Picture
} from '@element-plus/icons-vue'
import { organizationApi } from '@/api/modules/organization-view'
import type { Organization, Establishment } from '@/types/organization-view'
import {
  HrOrgTreeSimple as HrOrgTree,
  HrOrgChart,
  HrOrganizationFormDialog,
  HrOrganizationImportDialog,
  HrOrganizationSelect
} from '@/components/organization'
import { formatDate } from '@/utils/format'
import { exportToExcel } from '@/utils/export'

// ===== 响应式数据 =====
const viewMode = ref<'tree' | 'chart'>('tree')
const treeData = ref<Organization[]>([])
const chartData = ref<any>({ nodes: [], edges: [] })
const selectedOrgId = ref('')
const selectedOrg = ref<Organization>()
const childOrgs = ref<Organization[]>([])
const expandedKeys = ref<string[]>([])
const treeSearchKeyword = ref('')
const loading = ref(false)

// 对话框控制
const dialogVisible = ref(false)
const dialogTitle = ref('新增机构')
const dialogMode = ref<'add' | 'addChild' | 'edit'>('add')
const currentOrg = ref<Partial<Organization>>()
const importDialogVisible = ref(false)
const moveDialogVisible = ref(false)
const showEstablishmentDialog = ref(false)

// 表单数据
const moveForm = ref({
  sourceId: '',
  sourceName: '',
  targetId: '',
  reason: ''
})
const establishmentData = ref<any[]>([])

// 组件引用
const orgTreeRef = ref()
const chartRef = ref()

// ===== 计算属性 =====
const getProgressColor = computed(() => {
  return (percentage: number) => {
    if (percentage > 100) return '#F56C6C'
    if (percentage > 90) return '#E6A23C'
    return '#67C23A'
  }
})

// ===== 生命周期 =====
onMounted(() => {
  loadOrganizations()
})

// ===== 监听器 =====
watch(treeSearchKeyword, val => {
  orgTreeRef.value?.filter(val)
})

watch(showEstablishmentDialog, val => {
  if (val) {
    loadEstablishmentData()
  }
})

// ===== 方法 =====
// 加载组织数据
const loadOrganizations = async () => {
  loading.value = true
  try {
    const { data } = await organizationApi.getTree()
    treeData.value = data

    // 转换为图表数据
    if (viewMode.value === 'chart') {
      chartData.value = convertToChartData(data)
    }

    // 默认展开第一层
    expandedKeys.value = data.map((item: Organization) => item.id)
  } catch (error) {
    console.error('加载组织数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 树节点点击
const handleNodeClick = async (node: Organization) => {
  selectedOrgId.value = node.id
  selectedOrg.value = node

  // 加载详细信息
  try {
    const { data } = await organizationApi.getDetail(node.id)
    selectedOrg.value = data

    // 加载下属机构
    const children = await organizationApi.getChildren(node.id)
    childOrgs.value = children.data
  } catch (error) {
    console.error('加载机构详情失败:', error)
  }
}

// 树节点拖拽
const handleNodeDrop = async (draggingNode: any, dropNode: any, dropType: string) => {
  try {
    await ElMessageBox.confirm(
      `确定要将"${draggingNode.data.name}"移动到"${dropNode.data.name}"${dropType === 'inner' ? '下' : '旁边'}吗？`,
      '机构划转确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await organizationApi.move(draggingNode.data.id, {
      targetId: dropNode.data.id,
      position: dropType
    })

    ElMessage.success('机构划转成功')
    loadOrganizations()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('机构划转失败:', error)
    }
    // 重新加载数据以恢复原状
    loadOrganizations()
  }
}

// 新增机构
const handleCreate = () => {
  dialogTitle.value = '新增机构'
  dialogMode.value = 'add'
  currentOrg.value = {
    id: '',
    name: '',
    code: '',
    type: '',
    level: 1,
    status: 'normal',
    parentId: selectedOrgId.value
  } as Partial<Organization>
  dialogVisible.value = true
}

// 编辑机构
const handleEdit = (org: Organization) => {
  dialogTitle.value = '编辑机构'
  dialogMode.value = 'edit'
  currentOrg.value = { ...org }
  dialogVisible.value = true
}

// 删除机构
const handleDelete = async (org: Organization) => {
  try {
    await ElMessageBox.confirm(`确定要删除机构"${org.name}"吗？删除后不可恢复。`, '删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await organizationApi.delete(org.id)
    ElMessage.success('删除成功')
    loadOrganizations()

    // 清空选中状态
    if (selectedOrgId.value === org.id) {
      selectedOrgId.value = ''
      selectedOrg.value = undefined
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
    }
  }
}

// 机构划转
const handleMove = (org: Organization) => {
  moveForm.value = {
    sourceId: org.id,
    sourceName: org.name,
    targetId: '',
    reason: ''
  }
  moveDialogVisible.value = true
}

// 确认划转
const confirmMove = async () => {
  if (!moveForm.value.targetId) {
    ElMessage.warning('请选择目标上级机构')
    return
  }

  try {
    await organizationApi.move(moveForm.value.sourceId, {
      targetId: moveForm.value.targetId,
      reason: moveForm.value.reason
    })

    ElMessage.success('机构划转成功')
    moveDialogVisible.value = false
    loadOrganizations()
  } catch (error) {
    console.error('机构划转失败:', error)
  }
}

// 批量导入
const handleImport = () => {
  importDialogVisible.value = true
}

// 导出数据
const handleExport = async () => {
  try {
    const { data } = await organizationApi.query({ pageSize: 9999 })
    const exportData = data.items.map((item: Organization) => ({
      机构名称: item.name,
      机构编码: item.code,
      机构类型: getOrgTypeName(item.type),
      上级机构: item.parentName || '无',
      成立日期: formatDate(item.establishDate),
      状态: getStatusName(item.status),
      核定编制: item.establishment?.approved || 0,
      在编人数: item.establishment?.current || 0
    }))

    exportToExcel(exportData, '组织架构数据')
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
  }
}

// 查看变更历史
const handleViewHistory = () => {
  // TODO: 跳转到变更历史页面
  ElMessage.info('功能开发中')
}

// 机构合并
const handleMerge = () => {
  // TODO: 实现机构合并功能
  ElMessage.info('功能开发中')
}

// 机构撤销
const handleRevoke = async (org: Organization) => {
  try {
    await ElMessageBox.confirm(
      `确定要撤销机构"${org.name}"吗？撤销后该机构将不再可用。`,
      '撤销确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await organizationApi.update(org.id, { status: 'revoked' })
    ElMessage.success('机构撤销成功')
    loadOrganizations()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('撤销失败:', error)
    }
  }
}

// 表单提交成功
const handleSuccess = () => {
  dialogVisible.value = false
  loadOrganizations()
}

// 导入成功
const handleImportSuccess = () => {
  importDialogVisible.value = false
  loadOrganizations()
}

// 切换视图
const handleViewChange = () => {
  if (viewMode.value === 'chart') {
    chartData.value = convertToChartData(treeData.value)
  }
}

// 图表节点点击
const handleChartNodeClick = (node: any) => {
  const org = findOrgById(treeData.value, node.id)
  if (org) {
    handleNodeClick(org)
  }
}

// 图表节点右键菜单
const handleChartNodeContextMenu = (_node: any, event: MouseEvent) => {
  // TODO: 实现右键菜单
  event.preventDefault()
}

// 导出架构图
const handleExportChart = () => {
  chartRef.value?.exportImage('组织架构图')
}

// 树搜索过滤
const filterNode = (value: string, data: Organization) => {
  if (!value) return true
  return data.name.includes(value)
}

// 树搜索
const handleTreeSearch = (value: string) => {
  orgTreeRef.value?.filter(value)
}

// 节点选中变化
const handleCheckChange = () => {
  // TODO: 批量操作
}

// 编制管理 - 加载数据
const loadEstablishmentData = async () => {
  const orgs = flattenTree(treeData.value)
  establishmentData.value = orgs.map(org => ({
    id: org.id,
    name: org.name,
    approved: org.establishment?.approved || 0,
    current: org.establishment?.current || 0
  }))
}

// 编制管理 - 保存
const saveEstablishment = async () => {
  try {
    for (const item of establishmentData.value) {
      await organizationApi.updateEstablishment(item.id, {
        approved: item.approved
      })
    }
    ElMessage.success('编制信息保存成功')
    showEstablishmentDialog.value = false
    loadOrganizations()
  } catch (error) {
    console.error('保存编制信息失败:', error)
  }
}

// ===== 工具函数 =====
// 获取机构类型名称
const getOrgTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    college: '学院',
    department: '部门',
    office: '处室',
    center: '中心',
    institute: '研究所'
  }
  return typeMap[type] || type
}

// 获取状态名称
const getStatusName = (status: string) => {
  const statusMap: Record<string, string> = {
    normal: '正常',
    revoked: '已撤销',
    merged: '已合并'
  }
  return statusMap[status] || status
}

// 获取状态类型
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    normal: 'success',
    revoked: 'danger',
    merged: 'warning'
  }
  return typeMap[status] || 'info'
}

// 获取编制状态颜色
const getEstablishmentColor = (establishment?: Establishment) => {
  if (!establishment) return '#909399'
  const rate = ((establishment as any).current / (establishment as any).approved) * 100
  if (rate > 100) return '#F56C6C'
  if (rate > 90) return '#E6A23C'
  return '#67C23A'
}

// 获取缺编数
const getVacancy = (establishment?: Establishment) => {
  if (!establishment) return 0
  return Math.max(0, (establishment as any).approved - (establishment as any).current)
}

// 获取使用率
const getUsageRate = (establishment?: Establishment) => {
  if (!establishment || (establishment as any).approved === 0) return 0
  return Math.round(((establishment as any).current / (establishment as any).approved) * 100)
}

// 获取编制状态
const getEstablishmentStatus = (row: any) => {
  const rate = (row.current / row.approved) * 100
  if (rate > 100) return '超编'
  if (rate === 100) return '满编'
  if (rate > 90) return '接近满编'
  return '正常'
}

// 获取编制标签类型
const getEstablishmentTagType = (row: any) => {
  const rate = (row.current / row.approved) * 100
  if (rate > 100) return 'danger'
  if (rate > 90) return 'warning'
  return 'success'
}

// 转换为图表数据
const convertToChartData = (treeData: Organization[]) => {
  const nodes: any[] = []
  const edges: any[] = []

  const traverse = (data: Organization[], parentId?: string) => {
    data.forEach(item => {
      nodes.push({
        id: item.id,
        label: item.name,
        type: item.type,
        establishment: item.establishment
      })

      if (parentId) {
        edges.push({
          source: parentId,
          target: item.id
        })
      }

      if (item.children && item.children.length > 0) {
        traverse(item.children, item.id)
      }
    })
  }

  traverse(treeData)
  return { nodes, edges }
}

// 根据ID查找组织
const findOrgById = (tree: Organization[], id: string): Organization | null => {
  for (const node of tree) {
    if (node.id === id) return node
    if (node.children) {
      const found = findOrgById(node.children, id)
      if (found) return found
    }
  }
  return null
}

// 扁平化树结构
const flattenTree = (tree: Organization[]): Organization[] => {
  const result: Organization[] = []
  const traverse = (nodes: Organization[]) => {
    nodes.forEach(node => {
      result.push(node)
      if (node.children) {
        traverse(node.children)
      }
    })
  }
  traverse(tree)
  return result
}
</script>

<style lang="scss" scoped>
.organization-management {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;

  .toolbar-card {
    margin-bottom: 20px;

    .toolbar-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .content-card {
    flex: 1;
    overflow: hidden;

    &.chart-card {
      display: flex;
      flex-direction: column;

      .chart-toolbar {
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px solid #ebeef5;
      }
    }
  }

  .tree-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h4 {
      margin: 0;
    }

    .el-input {
      width: 200px;
    }
  }

  .tree-container {
    height: calc(100vh - 300px);
    overflow: auto;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 12px;
  }

  .detail-container {
    height: calc(100vh - 250px);
    overflow: auto;

    .detail-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 16px;
      border-bottom: 1px solid #ebeef5;

      h3 {
        margin: 0;
      }
    }

    .detail-info {
      margin-bottom: 24px;
    }

    .establishment-info {
      margin-bottom: 24px;

      h4 {
        margin: 0 0 16px 0;
      }
    }

    .child-orgs {
      h4 {
        margin: 0 0 16px 0;
      }
    }
  }
}
</style>
