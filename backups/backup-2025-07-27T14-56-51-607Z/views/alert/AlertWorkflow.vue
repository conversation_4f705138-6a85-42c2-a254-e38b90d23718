<template>
  <div class="alert-workflow">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>预警处理工作流</h2>
      <div class="header-actions">
        <el-button @click="showSettings = true">
          <el-icon><Setting /></el-icon>
          工作流设置
        </el-button>
        <el-button type="primary" @click="createWorkflow">
          <el-icon><Plus /></el-icon>
          新建工作流
        </el-button>
      </div>
    </div>

    <!-- 当前处理任务 -->
    <el-card class="current-tasks">
      <template #header>
        <div class="card-header">
          <span>待处理任务</span>
          <el-badge :value="pendingTasks.length" type="danger"  />
        </div>
      </template>
      
      <el-table
        :data="pendingTasks"
        style="width: 100%"
        @row-click="handleTaskClick"
      >
        <el-table-column prop="alertTitle" label="预警标题" min-width="200" show-overflow-tooltip  />
        <el-table-column prop="alertLevel" label="级别" width="80">
          <template #default="{ row }">
            <el-tag :type="getLevelTagType(row.alertLevel)" size="small">
              {{ getLevelName(row.alertLevel) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="currentStep" label="当前步骤" width="120"  />
        <el-table-column prop="assignee" label="处理人" width="100"  />
        <el-table-column prop="deadline" label="截止时间" width="160">
          <template #default="{ row }">
            <span :class="{ 'text-danger': isOverdue(row.deadline) }">
              {{ formatTime(row.deadline) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="{ row }">
            <el-button link type="primary" @click.stop="processTask(row)">
              处理
            </el-button>
            <el-button link type="primary" @click.stop="transferTask(row)">
              转派
            </el-button>
            <el-button link type="danger" @click.stop="escalateTask(row)">
              升级
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 工作流模板 -->
    <el-card class="workflow-templates">
      <template #header>
        <div class="card-header">
          <span>工作流模板</span>
          <el-input
            v-model="searchKeyword"
            placeholder="搜索模板"
            style="width: 200px"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col
          v-for="template in filteredTemplates"
          :key="template.id"
          :span="8"
        >
          <div class="template-card">
            <div class="template-header">
              <el-icon :size="24" :style="{ color: template.color }">
                <component :is="template.icon" />
              </el-icon>
              <h4>{{ template.name }}</h4>
            </div>
            <div class="template-desc">{{ template.description }}</div>
            <div class="template-steps">
              <el-tag
                v-for="(step, index) in template.steps"
                :key="index"
                size="small"
                class="step-tag"
              >
                {{ step }}
              </el-tag>
            </div>
            <div class="template-actions">
              <el-button link type="primary" @click="viewTemplate(template)">
                查看详情
              </el-button>
              <el-button link type="primary" @click="editTemplate(template)">
                编辑
              </el-button>
              <el-button link type="danger" @click="deleteTemplate(template)">
                删除
              </el-button>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 处理对话框 -->
    <el-dialog
      v-model="showProcessDialog"
      title="处理预警"
      width="800px"
    >
      <div v-if="currentTask" class="process-form">
        <el-form
          ref="processFormRef"
          :model="processForm"
          label-width="100px"
        >
          <el-form-item label="预警信息">
            <el-descriptions :column="2" border size="small">
              <el-descriptions-item label="标题">
                {{ currentTask.alertTitle }}
              </el-descriptions-item>
              <el-descriptions-item label="级别">
                <el-tag :type="getLevelTagType(currentTask.alertLevel)" size="small">
                  {{ getLevelName(currentTask.alertLevel) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="当前步骤" :span="2">
                {{ currentTask.currentStep }}
              </el-descriptions-item>
            </el-descriptions>
          </el-form-item>
          
          <el-form-item label="处理方式" required>
            <el-select v-model="processForm.action" placeholder="请选择处理方式">
              <el-option label="已解决" value="resolved"  />
              <el-option label="转下一步" value="next"  />
              <el-option label="退回" value="back"  />
              <el-option label="关闭" value="close"  />
            </el-select>
          </el-form-item>
          
          <el-form-item label="处理结果" v-if="processForm.action === 'resolved'">
            <el-input
              v-model="processForm.result"
              type="textarea"
              :rows="3"
              placeholder="请输入处理结果"
              />
          </el-form-item>
          
          <el-form-item label="下一步骤" v-if="processForm.action === 'next'">
            <el-select v-model="processForm.nextStep" placeholder="请选择下一步骤">
              <el-option
                v-for="step in availableNextSteps"
                :key="step.id"
                :label="step.name"
                :value="step.id"
               />
            </el-select>
          </el-form-item>
          
          <el-form-item label="指派给" v-if="processForm.action === 'next'">
            <el-select v-model="processForm.assignee" placeholder="请选择处理人">
              <el-option
                v-for="user in availableUsers"
                :key="user.id"
                :label="user.name"
                :value="user.id"
               />
            </el-select>
          </el-form-item>
          
          <el-form-item label="处理备注">
            <el-input
              v-model="processForm.comment"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
              />
          </el-form-item>
          
          <el-form-item label="附件">
            <el-upload
              class="upload-demo"
              action="#"
              :auto-upload="false"
              :file-list="processForm.fileList"
              :on-change="handleFileChange"
              multiple
            >
              <el-button size="small" type="primary">选择文件</el-button>
              <template #tip>
                <div class="el-upload__tip">支持上传多个文件</div>
              </template>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="showProcessDialog = false">取消</el-button>
        <el-button type="primary" @click="submitProcess">提交</el-button>
      </template>
    </el-dialog>

    <!-- 工作流设置对话框 -->
    <el-dialog
      v-model="showSettings"
      title="工作流设置"
      width="900px"
    >
      <WorkflowSettings @close="showSettings = false" />
    </el-dialog>

    <!-- 工作流设计器 -->
    <el-dialog
      v-model="showDesigner"
      title="工作流设计"
      width="90%"
      :close-on-click-modal="false"
    >
      <WorkflowDesigner
        :template="currentTemplate"
        @save="saveWorkflow"
        @close="showDesigner = false"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Setting,
  Plus,
  Search,
  Timer,
  Notification,
  Document,
  Connection,
  Warning
} from '@element-plus/icons-vue'
import WorkflowSettings from './components/WorkflowSettings.vue'
import HrWorkflowDesigner from './components/HrWorkflowDesigner.vue'

// 类型定义
interface WorkflowTask {
  id: string
  alertId: string
  alertTitle: string
  alertLevel: string
  currentStep: string
  assignee: string
  deadline: Date
  status: string
}

interface WorkflowTemplate {
  id: string
  name: string
  description: string
  icon: string
  color: string
  steps: string[]
   
  config: unknown
}

// 响应式数据
const searchKeyword = ref('')
const showProcessDialog = ref(false)
const showSettings = ref(false)
const showDesigner = ref(false)
const currentTask = ref<WorkflowTask | null>(null)
const currentTemplate = ref<WorkflowTemplate | null>(null)

// 表单数据
const processFormRef = ref()
const processForm = reactive({
  action: '',
  result: '',
  nextStep: '',
  assignee: '',
  comment: '',
  fileList: []
})

// 待处理任务
const pendingTasks = ref<WorkflowTask[]>([
  {
    id: 'TASK001',
    alertId: 'ALERT001',
    alertTitle: '批量任务处理超时预警',
    alertLevel: 'high',
    currentStep: '初步分析',
    assignee: '张三',
    deadline: new Date(Date.now() + 2 * 60 * 60 * 1000),
    status: 'pending'
  },
  {
    id: 'TASK002',
    alertId: 'ALERT002',
    alertTitle: '系统资源使用率过高',
    alertLevel: 'critical',
    currentStep: '紧急响应',
    assignee: '李四',
    deadline: new Date(Date.now() + 30 * 60 * 1000),
    status: 'pending'
  }
])

// 工作流模板
const workflowTemplates = ref<WorkflowTemplate[]>([
  {
    id: 'TPL001',
    name: 'HrHr一般预警处理',
    description: '适用于低级别预警的标准处理流程',
    icon: 'Notification',
    color: '#409EFF',
    steps: ['接收预警', '初步分析', '处理解决', '关闭归档'],
    config: {}
  },
  {
    id: 'TPL002',
    name: '紧急预警响应',
    description: '适用于高级别和严重级别预警的快速响应',
    icon: 'Warning',
    color: '#F56C6C',
    steps: ['紧急响应', '问题定位', '制定方案', '执行修复', '验证结果'],
    config: {}
  },
  {
    id: 'TPL003',
    name: '业务预警协作',
    description: '需要多部门协作处理的业务类预警',
    icon: 'Connection',
    color: '#67C23A',
    steps: ['任务分配', '部门协作', '方案审批', '执行处理', '效果评估'],
    config: {}
  }
])

// 可用的下一步骤
const availableNextSteps = ref([
  { id: 'step1', name: '问题定位' },
  { id: 'step2', name: '制定方案' },
  { id: 'step3', name: '执行修复' },
  { id: 'step4', name: '验证结果' }
])

// 可用用户
const availableUsers = ref([
  { id: 'user1', name: '张三' },
  { id: 'user2', name: '李四' },
  { id: 'user3', name: '王五' },
  { id: 'user4', name: '赵六' }
])

// 计算属性
const filteredTemplates = computed(() => {
  if (!searchKeyword.value) return workflowTemplates.value
  
  return workflowTemplates.value.filter(template =>
    template.name.includes(searchKeyword.value) ||
    template.description.includes(searchKeyword.value)
  )
})

// 方法
const getLevelName = (level: string) => {
  const levelMap: Record<string, string> = {
    low: '低级',
    medium: '中级',
    high: '高级',
    critical: '严重'
  }
  return levelMap[level] || level
}

const getLevelTagType = (level: string) => {
  const typeMap: Record<string, string> = {
    low: 'info',
    medium: 'warning',
    high: 'danger',
    critical: 'danger'
  }
  return typeMap[level] || 'info'
}

const formatTime = (time: Date | string) => {
  if (!time) return '-'
  const date = new Date(time)
  return new Intl.DateTimeFormat('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const isOverdue = (deadline: Date) => {
  return new Date(deadline) < new Date()
}

const handleTaskClick = (row: WorkflowTask) => {
  // 点击任务行，查看详情
  ElMessage.info(`查看任务详情: ${row.id}`)
}

const processTask = (task: WorkflowTask) => {
  currentTask.value = task
  processForm.action = ''
  processForm.result = ''
  processForm.nextStep = ''
  processForm.assignee = ''
  processForm.comment = ''
  processForm.fileList = []
  showProcessDialog.value = true
}

const transferTask = (task: WorkflowTask) => {
  ElMessageBox.prompt('请输入转派对象', '转派任务', {
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  }).then(({ value }) => {
    ElMessage.success(`任务已转派给: ${value}`)
  })
}

const escalateTask = (task: WorkflowTask) => {
  ElMessageBox.confirm(
    '确定要将该任务升级处理吗？升级后将通知上级管理人员。',
    '升级任务',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('任务已升级')
  })
}

   
const handleFileChange = (file: unknown, fileList: unknown) => {
  processForm.fileList = fileList
}

const submitProcess = async () => {
  if (!processForm.action) {
    ElMessage.warning('请选择处理方式')
    return
  }
  
  // 模拟提交
  ElMessage.success('处理成功')
  showProcessDialog.value = false
  
  // 从待处理列表中移除
  const index = pendingTasks.value.findIndex(t => t.id === currentTask.value?.id)
  if (index > -1) {
    pendingTasks.value.splice(index, 1)
  }
}

const createWorkflow = () => {
  currentTemplate.value = null
  showDesigner.value = true
}

const viewTemplate = (template: WorkflowTemplate) => {
  ElMessage.info(`查看模板: ${template.name}`)
}

const editTemplate = (template: WorkflowTemplate) => {
  currentTemplate.value = template
  showDesigner.value = true
}

const deleteTemplate = async (template: WorkflowTemplate) => {
  await ElMessageBox.confirm(
    '确定要删除该工作流模板吗？',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
  
  const index = workflowTemplates.value.findIndex(t => t.id === template.id)
  if (index > -1) {
    workflowTemplates.value.splice(index, 1)
    ElMessage.success('删除成功')
  }
}

   
const saveWorkflow = (data: unknown) => {
  if (currentTemplate.value) {
    // 编辑模式
    Object.assign(currentTemplate.value, data)
    ElMessage.success('保存成功')
  } else {
    // 新建模式
    workflowTemplates.value.push({
      id: `TPL${Date.now()}`,
      ...data
    })
    ElMessage.success('创建成功')
  }
  showDesigner.value = false
}

// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<style lang="scss" scoped>
.alert-workflow {
  padding: 20px;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      font-size: 24px;
    }
    
    .header-actions {
      display: flex;
      gap: 12px;
    }
  }
  
  .current-tasks {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .text-danger {
      color: var(--el-color-danger);
    }
  }
  
  .workflow-templates {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .template-card {
      border: 1px solid var(--el-border-color);
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      transition: all 0.3s;
      
      &:hover {
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }
      
      .template-header {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 12px;
        
        h4 {
          margin: 0;
          font-size: 16px;
        }
      }
      
      .template-desc {
        color: var(--el-text-color-secondary);
        font-size: 14px;
        margin-bottom: 16px;
        line-height: 1.5;
      }
      
      .template-steps {
        margin-bottom: 16px;
        
        .step-tag {
          margin-right: 8px;
          margin-bottom: 8px;
        }
      }
      
      .template-actions {
        display: flex;
        justify-content: flex-end;
        gap: 8px;
        padding-top: 12px;
        border-top: 1px solid var(--el-border-color-lighter);
      }
    }
  }
  
  .process-form {
    .el-descriptions {
      margin-bottom: 20px;
    }
  }
}
</style>