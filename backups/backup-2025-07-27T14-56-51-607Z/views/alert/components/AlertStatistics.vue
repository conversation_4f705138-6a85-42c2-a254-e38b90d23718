<template>
  <div class="alert-statistics">
    <!-- 统计指标 -->
    <el-row :gutter="20" class="metrics-row">
      <el-col :span="6">
        <hr-metric-card
          title="今日预警"
          :value="todayStats.count"
          unit="条"
          :trend="todayStats.trend"
          icon="WarningFilled"
          color="#E6A23C"
        />
      </el-col>
      <el-col :span="6">
        <hr-metric-card
          title="处理率"
          :value="todayStats.resolveRate"
          unit="%"
          :trend="todayStats.resolveTrend"
          icon="CircleCheck"
          color="#67C23A"
        />
      </el-col>
      <el-col :span="6">
        <hr-metric-card
          title="平均响应"
          :value="todayStats.avgResponse"
          unit="分钟"
          :trend="todayStats.responseTrend"
          icon="Timer"
          color="#409EFF"
        />
      </el-col>
      <el-col :span="6">
        <hr-metric-card
          title="严重预警"
          :value="todayStats.criticalCount"
          unit="条"
          :trend="0"
          icon="CircleCloseFilled"
          color="#F56C6C"
        />
      </el-col>
    </el-row>

    <!-- 图表分析 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>预警趋势分析</span>
              <el-radio-group v-model="trendPeriod" size="small">
                <el-radio-button label="day">24小时</el-radio-button>
                <el-radio-button label="week">近7天</el-radio-button>
                <el-radio-button label="month">近30天</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div class="chart-container">
            <LineChart
              :data="trendData"
              :height="300"
              :option="trendChartOption"
            />
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>预警类型分布</template>
          <div class="chart-container">
            <PieChart
              :data="typeDistribution"
              :height="300"
              :option="pieChartOption"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <el-card>
          <template #header>部门预警排行</template>
          <div class="chart-container">
            <BarChart
              :data="departmentRanking"
              :height="300"
              :option="barChartOption"
            />
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>处理效率统计</template>
          <div class="efficiency-stats">
            <div class="stat-item" v-for="item in efficiencyStats" :key="item.label">
              <div class="stat-label">{{ item.label }}</div>
              <div class="stat-value">
                <el-progress
                  :percentage="item.percentage"
                  :color="item.color"
                  :stroke-width="10"
                 />
              </div>
              <div class="stat-info">
                {{ item.value }} / {{ item.total }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 预警热力图 -->
    <el-card class="heatmap-card">
      <template #header>
        <div class="card-header">
          <span>预警时间热力图</span>
          <span class="subtitle">按小时统计近7天预警分布</span>
        </div>
      </template>
      <div class="heatmap-container">
        <HeatmapChart
          :data="heatmapData"
          :height="200"
          :option="heatmapOption"
        />
      </div>
    </el-card>

    <!-- 操作按钮 -->
    <div class="actions">
      <el-button @click="exportReport">
        <el-icon><Download /></el-icon>
        导出报告
      </el-button>
      <el-button type="primary" @click="$emit('close')">
        关闭
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
 
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Download } from '@element-plus/icons-vue'
import HrMetricCard from '@/components/common/HrMetricCard.vue'
import HrLineChart from '@/components/charts/HrLineChart.vue'
import HrPieChart from '@/components/charts/HrPieChart.vue'
import HrBarChart from '@/components/charts/HrBarChart.vue'
import HeatmapChart from '@/components/charts/HeatmapChart.vue'

interface Props {
  dateRange?: string[] | null
}

const props = defineProps<Props>()
defineEmits(['close'])

// 响应式数据
const trendPeriod = ref('week')

// 今日统计
const todayStats = ref({
  count: 23,
  trend: 15.2,
  resolveRate: 87.5,
  resolveTrend: 5.3,
  avgResponse: 32,
  responseTrend: -12.1,
  criticalCount: 2
})

// 趋势数据
const trendData = computed(() => {
  const days = trendPeriod.value === 'day' ? 24 : trendPeriod.value === 'week' ? 7 : 30
  const data = []
  
  for (let i = 0; i < days; i++) {
    const date = new Date()
    if (trendPeriod.value === 'day') {
      date.setHours(date.getHours() - (days - i - 1))
    } else {
      date.setDate(date.getDate() - (days - i - 1))
    }
    
    data.push({
      time: date.toLocaleDateString('zh-CN'),
      新增预警: Math.floor(Math.random() * 30 + 10),
      已处理: Math.floor(Math.random() * 25 + 8),
      待处理: Math.floor(Math.random() * 10 + 2)
    })
  }
  
  return data
})

// 趋势图表配置
const trendChartOption = {
  xAxis: {
    type: 'category',
    boundaryGap: false
  },
  yAxis: {
    name: 'HrHr预警数量'
  },
  series: [
    {
      name: '新增预警',
      type: 'line',
      smooth: true,
      areaStyle: { opacity: 0.1 }
    },
    {
      name: '已处理',
      type: 'line',
      smooth: true
    },
    {
      name: '待处理',
      type: 'line',
      smooth: true
    }
  ]
}

// 类型分布数据
const typeDistribution = ref([
  { name: '超时预警', value: 156 },
  { name: '异常预警', value: 128 },
  { name: '积压预警', value: 89 },
  { name: '业务预警', value: 234 },
  { name: '性能预警', value: 67 },
  { name: '安全预警', value: 45 }
])

// 饼图配置
const pieChartOption = {
  series: [{
    type: 'pie',
    radius: ['40%', '70%'],
    label: {
      formatter: '{b}: {c} ({d}%)'
    }
  }]
}

// 部门排行数据
const departmentRanking = ref([
  { department: '技术部', count: 45 },
  { department: '运营部', count: 38 },
  { department: '人事部', count: 32 },
  { department: '财务部', count: 28 },
  { department: '市场部', count: 25 },
  { department: '行政部', count: 18 }
])

// 柱状图配置
const barChartOption = {
  xAxis: {
    type: 'value',
    name: '预警数量'
  },
  yAxis: {
    type: 'category',
    data: departmentRanking.value.map(item => item.department).reverse()
  },
  series: [{
    type: 'bar',
    label: {
      show: true,
      position: 'right'
    },
    itemStyle: {
      borderRadius: [0, 4, 4, 0]
    }
  }],
  grid: {
    left: 80
  }
}

// 处理效率统计
const efficiencyStats = ref([
  {
    label: '15分钟内响应',
    value: 156,
    total: 200,
    percentage: 78,
    color: '#67C23A'
  },
  {
    label: '1小时内处理',
    value: 120,
    total: 180,
    percentage: 66.7,
    color: '#409EFF'
  },
  {
    label: '24小时内解决',
    value: 145,
    total: 160,
    percentage: 90.6,
    color: '#E6A23C'
  },
  {
    label: '自动化处理',
    value: 89,
    total: 200,
    percentage: 44.5,
    color: '#909399'
  }
])

// 热力图数据
const heatmapData = computed(() => {
  const data = []
  const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  
  for (let hour = 0; hour < 24; hour++) {
    for (let day = 0; day < 7; day++) {
      data.push({
        hour: hour + '时',
        day: days[day],
        value: Math.floor(Math.random() * 20)
      })
    }
  }
  
  return data
})

// 热力图配置
const heatmapOption = {
  xAxis: {
    type: 'category',
    data: ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  },
  yAxis: {
    type: 'category',
    data: Array.from({ length: 24 }, (_, i) => i + '时')
  },
  visualMap: {
    min: 0,
    max: 20,
    calculable: true,
    orient: 'horizontal',
    left: 'center',
    bottom: '0%',
    inRange: {
      color: ['#f0f0f0', '#ffeda0', '#feb24c', '#fd8d3c', '#fc4e2a', '#e31a1c', '#bd0026']
    }
  },
  series: [{
    type: 'heatmap',
    label: {
      show: true,
      fontSize: 10
    },
    emphasis: {
      itemStyle: {
        shadowBlur: 10,
        shadowColor: 'rgba(0, 0, 0, 0.5)'
      }
    }
  }]
}

// 方法
const exportReport = () => {
  ElMessage.success('统计报告已导出')
}

// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<style lang="scss" scoped>
.alert-statistics {
  .metrics-row {
    margin-bottom: 20px;
  }
  
  .charts-row {
    margin-bottom: 20px;
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .subtitle {
      font-size: 12px;
      color: var(--el-text-color-secondary);
      margin-left: 8px;
    }
  }
  
  .chart-container {
    padding: 10px;
  }
  
  .efficiency-stats {
    padding: 20px;
    
    .stat-item {
      margin-bottom: 24px;
      
      .stat-label {
        font-size: 14px;
        color: var(--el-text-color-secondary);
        margin-bottom: 8px;
      }
      
      .stat-value {
        margin-bottom: 4px;
      }
      
      .stat-info {
        font-size: 12px;
        color: var(--el-text-color-secondary);
        text-align: right;
      }
    }
  }
  
  .heatmap-card {
    margin-bottom: 20px;
  }
  
  .heatmap-container {
    padding: 20px;
  }
  
  .actions {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid var(--el-border-color);
  }
}
</style>