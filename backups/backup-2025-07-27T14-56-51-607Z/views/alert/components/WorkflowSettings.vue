<template>
  <div class="workflow-settings">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="通用设置" name="general">
        <el-form :model="settings.general" label-width="120px">
          <el-form-item label="自动分配">
            <el-switch v-model="settings.general.autoAssign"  />
            <div class="form-tip">启用后系统将根据规则自动分配处理人</div>
          </el-form-item>
          
          <el-form-item label="超时提醒">
            <el-input-number
              v-model="settings.general.timeoutMinutes"
              :min="10"
              :max="1440"
              :step="10"
              />
            <span class="unit">分钟</span>
          </el-form-item>
          
          <el-form-item label="升级策略">
            <el-select v-model="settings.general.escalationStrategy">
              <el-option label="超时自动升级" value="timeout"  />
              <el-option label="多次失败升级" value="failure"  />
              <el-option label="手动升级" value="manual"  />
            </el-select>
          </el-form-item>
          
          <el-form-item label="通知方式">
            <el-checkbox-group v-model="settings.general.notificationMethods">
              <el-checkbox label="system">系统消息</el-checkbox>
              <el-checkbox label="email">邮件通知</el-checkbox>
              <el-checkbox label="sms">短信通知</el-checkbox>
              <el-checkbox label="wechat">企业微信</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane label="分配规则" name="assignment">
        <div class="rules-header">
          <el-button type="primary" size="small" @click="addRule">
            <el-icon><Plus /></el-icon>
            添加规则
          </el-button>
        </div>
        
        <el-table :data="settings.assignmentRules" style="width: 100%">
          <el-table-column prop="name" label="规则名称" width="150"  />
          <el-table-column prop="condition" label="条件" min-width="200">
            <template #default="{ row }">
              <el-tag
                v-for="(cond, index) in row.conditions"
                :key="index"
                class="condition-tag"
              >
                {{ formatCondition(cond) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="assignee" label="分配给" width="120"  />
          <el-table-column prop="priority" label="优先级" width="80">
            <template #default="{ row }">
              {{ row.priority }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="{ row }">
              <el-button link type="primary" @click="editRule(row)">
                编辑
              </el-button>
              <el-button link type="danger" @click="deleteRule(row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      
      <el-tab-pane label="级别配置" name="levels">
        <el-form label-width="120px">
          <div v-for="level in alertLevels" :key="level.value" class="level-config">
            <h4>
              <el-tag :type="level.tagType">{{ level.label }}</el-tag>
            </h4>
            <el-form-item label="响应时限">
              <el-input-number
                v-model="settings.levelConfig[level.value].responseTime"
                :min="5"
                :max="240"
                />
              <span class="unit">分钟</span>
            </el-form-item>
            <el-form-item label="处理时限">
              <el-input-number
                v-model="settings.levelConfig[level.value].resolveTime"
                :min="30"
                :max="1440"
                />
              <span class="unit">分钟</span>
            </el-form-item>
            <el-form-item label="通知人员">
              <el-select
                v-model="settings.levelConfig[level.value].notifyUsers"
                multiple
                placeholder="选择通知人员"
              >
                <el-option
                  v-for="user in availableUsers"
                  :key="user.id"
                  :label="user.name"
                  :value="user.id"
                 />
              </el-select>
            </el-form-item>
          </div>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane label="工作流程" name="workflow">
        <el-alert
          title="工作流程配置"
          type="info"
          description="可以为不同类型的预警配置不同的处理流程"
          show-icon
          :closable="false"
          class="mb-20"
         />
        
        <el-table :data="settings.workflows" style="width: 100%">
          <el-table-column prop="alertType" label="预警类型" width="150">
            <template #default="{ row }">
              {{ getAlertTypeName(row.alertType) }}
            </template>
          </el-table-column>
          <el-table-column prop="workflowId" label="关联流程" min-width="200">
            <template #default="{ row }">
              <el-select
                v-model="row.workflowId"
                placeholder="选择工作流程"
                style="width: 100%"
              >
                <el-option
                  v-for="workflow in availableWorkflows"
                  :key="workflow.id"
                  :label="workflow.name"
                  :value="workflow.id"
                 />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="enabled" label="启用" width="80">
            <template #default="{ row }">
              <el-switch v-model="row.enabled"  />
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
    
    <div class="actions">
      <el-button @click="$emit('close')">取消</el-button>
      <el-button type="primary" @click="saveSettings">保存设置</el-button>
    </div>
    
    <!-- 规则编辑对话框 -->
    <el-dialog
      v-model="showRuleDialog"
      :title="currentRule ? '编辑规则' : '添加规则'"
      width="600px"
    >
      <el-form :model="ruleForm" label-width="100px">
        <el-form-item label="规则名称" required>
          <el-input v-model="ruleForm.name" placeholder="请输入规则名称"   />
        </el-form-item>
        
        <el-form-item label="条件设置" required>
          <div class="condition-builder">
            <div
              v-for="(condition, index) in ruleForm.conditions"
              :key="index"
              class="condition-item"
            >
              <el-select
                v-model="condition.field"
                placeholder="选择字段"
                style="width: 120px"
              >
                <el-option label="预警类型" value="alertType"  />
                <el-option label="预警级别" value="level"  />
                <el-option label="部门" value="department"  />
                <el-option label="时间段" value="timeRange"  />
              </el-select>
              
              <el-select
                v-model="condition.operator"
                placeholder="操作符"
                style="width: 100px"
              >
                <el-option label="等于" value="eq"  />
                <el-option label="包含" value="contains"  />
                <el-option label="大于" value="gt"  />
                <el-option label="小于" value="lt"  />
              </el-select>
              
              <el-input
                v-model="condition.value"
                placeholder="值"
                style="width: 150px"
                />
              
              <el-button
                link
                type="danger"
                @click="removeCondition(index)"
              >
                删除
              </el-button>
            </div>
            
            <el-button
              type="primary"
              link
              @click="addCondition"
            >
              <el-icon><Plus /></el-icon>
              添加条件
            </el-button>
          </div>
        </el-form-item>
        
        <el-form-item label="分配给" required>
          <el-select v-model="ruleForm.assignee" placeholder="选择处理人">
            <el-option
              v-for="user in availableUsers"
              :key="user.id"
              :label="user.name"
              :value="user.id"
             />
          </el-select>
        </el-form-item>
        
        <el-form-item label="优先级">
          <el-input-number
            v-model="ruleForm.priority"
            :min="1"
            :max="100"
            />
          <div class="form-tip">数值越小优先级越高</div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showRuleDialog = false">取消</el-button>
        <el-button type="primary" @click="saveRule">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

// 定义emits
defineEmits(['close'])

// 响应式数据
const activeTab = ref('general')
const showRuleDialog = ref(false)
const currentRule = ref<unknown>(null)

// 设置数据
const settings = reactive({
  general: {
    autoAssign: true,
    timeoutMinutes: 60,
    escalationStrategy: 'timeout',
    notificationMethods: ['system', 'email']
  },
  assignmentRules: [
    {
      id: 'RULE001',
      name: 'HrHr高级预警分配',
      conditions: [
        { field: 'level', operator: 'eq', value: 'high' }
      ],
      assignee: 'user1',
      priority: 1
    },
    {
      id: 'RULE002',
      name: '技术部预警',
      conditions: [
        { field: 'department', operator: 'eq', value: '技术部' }
      ],
      assignee: 'user2',
      priority: 2
    }
  ],
  levelConfig: {
    low: {
      responseTime: 120,
      resolveTime: 480,
      notifyUsers: []
    },
    medium: {
      responseTime: 60,
      resolveTime: 240,
      notifyUsers: ['user1']
    },
    high: {
      responseTime: 30,
      resolveTime: 120,
      notifyUsers: ['user1', 'user3']
    },
    critical: {
      responseTime: 15,
      resolveTime: 60,
      notifyUsers: ['user1', 'user3', 'user4']
    }
  },
  workflows: [
    { alertType: 'task_timeout', workflowId: 'WF001', enabled: true },
    { alertType: 'system_error', workflowId: 'WF002', enabled: true },
    { alertType: 'task_backlog', workflowId: 'WF003', enabled: false },
    { alertType: 'license_expire', workflowId: '', enabled: false }
  ]
})

// 规则表单
const ruleForm = reactive({
  name: '',
  conditions: [
    { field: '', operator: '', value: '' }
  ],
  assignee: '',
  priority: 10
})

// 静态数据
const alertLevels = [
  { value: 'low', label: '低级', tagType: 'info' },
  { value: 'medium', label: '中级', tagType: 'warning' },
  { value: 'high', label: '高级', tagType: 'danger' },
  { value: 'critical', label: '严重', tagType: 'danger' }
]

const availableUsers = ref([
  { id: 'user1', name: '张三' },
  { id: 'user2', name: '李四' },
  { id: 'user3', name: '王五' },
  { id: 'user4', name: '赵六' }
])

const availableWorkflows = ref([
  { id: 'WF001', name: '一般预警处理流程' },
  { id: 'WF002', name: '紧急预警响应流程' },
  { id: 'WF003', name: '业务预警协作流程' }
])

// 方法
   
const formatCondition = (condition: unknown) => {
  const fieldMap: Record<string, string> = {
    alertType: '预警类型',
    level: '预警级别',
    department: '部门',
    timeRange: '时间段'
  }
  const operatorMap: Record<string, string> = {
    eq: '等于',
    contains: '包含',
    gt: '大于',
    lt: '小于'
  }
  return `${fieldMap[condition.field]} ${operatorMap[condition.operator]} ${condition.value}`
}

const getAlertTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    task_timeout: '任务超时',
    system_error: '系统异常',
    task_backlog: '任务积压',
    license_expire: '证照到期'
  }
  return typeMap[type] || type
}

const addRule = () => {
  currentRule.value = null
  ruleForm.name = ''
  ruleForm.conditions = [{ field: '', operator: '', value: '' }]
  ruleForm.assignee = ''
  ruleForm.priority = 10
  showRuleDialog.value = true
}

   
const editRule = (rule: unknown) => {
  currentRule.value = rule
  Object.assign(ruleForm, {
    name: rule.name,
    conditions: [...rule.conditions],
    assignee: rule.assignee,
    priority: rule.priority
  })
  showRuleDialog.value = true
}

   
const deleteRule = async (rule: unknown) => {
  await ElMessageBox.confirm(
    '确定要删除该规则吗？',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
  
  const index = settings.assignmentRules.findIndex(r => r.id === rule.id)
  if (index > -1) {
    settings.assignmentRules.splice(index, 1)
    ElMessage.success('删除成功')
  }
}

const addCondition = () => {
  ruleForm.conditions.push({ field: '', operator: '', value: '' })
}

const removeCondition = (index: number) => {
  if (ruleForm.conditions.length > 1) {
    ruleForm.conditions.splice(index, 1)
  }
}

const saveRule = () => {
  if (!ruleForm.name || !ruleForm.assignee) {
    ElMessage.warning('请填写必填项')
    return
  }
  
  if (currentRule.value) {
    // 编辑模式
    Object.assign(currentRule.value, {
      name: ruleForm.name,
      conditions: [...ruleForm.conditions],
      assignee: ruleForm.assignee,
      priority: ruleForm.priority
    })
  } else {
    // 新增模式
    settings.assignmentRules.push({
      id: `RULE${Date.now()}`,
      name: ruleForm.name,
      conditions: [...ruleForm.conditions],
      assignee: ruleForm.assignee,
      priority: ruleForm.priority
    })
  }
  
  showRuleDialog.value = false
  ElMessage.success('保存成功')
}

const saveSettings = () => {
  // 保存设置
  ElMessage.success('设置已保存')
}
</script>

<style lang="scss" scoped>
.workflow-settings {
  padding: 20px;
  
  .form-tip {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-top: 4px;
  }
  
  .unit {
    margin-left: 8px;
    color: var(--el-text-color-secondary);
  }
  
  .rules-header {
    margin-bottom: 16px;
  }
  
  .condition-tag {
    margin-right: 8px;
    margin-bottom: 4px;
  }
  
  .level-config {
    margin-bottom: 24px;
    padding: 16px;
    background: var(--el-bg-page);
    border-radius: 4px;
    
    h4 {
      margin-bottom: 16px;
    }
  }
  
  .mb-20 {
    margin-bottom: 20px;
  }
  
  .condition-builder {
    .condition-item {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
    }
  }
  
  .actions {
    margin-top: 30px;
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid var(--el-border-color);
  }
}
</style>