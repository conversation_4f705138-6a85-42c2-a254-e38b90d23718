<template>
  <div class="workflow-designer">
    <div class="designer-toolbar">
      <div class="toolbar-left">
        <el-button-group>
          <el-button size="small" @click="undo" :disabled="!canUndo">
            <el-icon><RefreshLeft /></el-icon>
            撤销
          </el-button>
          <el-button size="small" @click="redo" :disabled="!canRedo">
            <el-icon><RefreshRight /></el-icon>
            重做
          </el-button>
        </el-button-group>
        
        <el-divider direction="vertical"   />
        
        <el-button-group>
          <el-button size="small" @click="zoomIn">
            <el-icon><ZoomIn /></el-icon>
          </el-button>
          <el-button size="small" @click="zoomOut">
            <el-icon><ZoomOut /></el-icon>
          </el-button>
          <el-button size="small" @click="fitView">
            <el-icon><FullScreen /></el-icon>
          </el-button>
        </el-button-group>
      </div>
      
      <div class="toolbar-right">
        <el-button size="small" @click="previewWorkflow">
          <el-icon><View /></el-icon>
          预览
        </el-button>
        <el-button size="small" type="primary" @click="saveWorkflow">
          <el-icon><Check /></el-icon>
          保存
        </el-button>
      </div>
    </div>
    
    <div class="designer-container">
      <div class="designer-sidebar">
        <h4>工作流组件</h4>
        <div class="component-list">
          <div
            v-for="component in workflowComponents"
            :key="component.type"
            class="component-item"
            draggable="true"
            @dragstart="handleDragStart($event, component)"
          >
            <el-icon :size="20">
              <component :is="component.icon" />
            </el-icon>
            <span>{{ component.name }}</span>
          </div>
        </div>
        
        <el-divider   />
        
        <h4>属性设置</h4>
        <div v-if="selectedNode" class="properties-panel">
          <el-form label-width="80px" size="small">
            <el-form-item label="节点名称">
              <el-input v-model="selectedNode.name"   />
            </el-form-item>
            
            <el-form-item label="处理人" v-if="selectedNode.type !== 'start' && selectedNode.type !== 'end'">
              <el-select v-model="selectedNode.assignee" placeholder="选择处理人">
                <el-option
                  v-for="user in availableUsers"
                  :key="user.id"
                  :label="user.name"
                  :value="user.id"
                 />
              </el-select>
            </el-form-item>
            
            <el-form-item label="时限" v-if="selectedNode.type === 'task'">
              <el-input-number
                v-model="selectedNode.timeout"
                :min="10"
                :max="1440"
                />
              <span class="unit">分钟</span>
            </el-form-item>
            
            <el-form-item label="条件" v-if="selectedNode.type === 'condition'">
              <el-input
                v-model="selectedNode.expression"
                type="textarea"
                :rows="3"
                placeholder="输入条件表达式"
                />
            </el-form-item>
          </el-form>
        </div>
        <div v-else class="no-selection">
          请选择一个节点进行配置
        </div>
      </div>
      
      <div class="designer-canvas" ref="canvasRef">
        <svg
          ref="svgRef"
          width="100%"
          height="100%"
          @drop="handleDrop"
          @dragover.prevent
        >
          <!-- 网格背景 -->
          <defs>
            <pattern
              id="grid"
              width="20"
              height="20"
              patternUnits="userSpaceOnUse"
            >
              <path
                d="M 20 0 L 0 0 0 20"
                fill="none"
                stroke="#e0e0e0"
                stroke-width="1"
              />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />
          
          <!-- 连接线 -->
          <g class="connections">
            <path
              v-for="connection in connections"
              :key="connection.id"
              :d="getConnectionPath(connection)"
              stroke="#409EFF"
              stroke-width="2"
              fill="none"
              marker-end="url(#arrowhead)"
            />
          </g>
          
          <!-- 箭头定义 -->
          <defs>
            <marker
              id="arrowhead"
              markerWidth="10"
              markerHeight="10"
              refX="9"
              refY="3"
              orient="auto"
            >
              <polygon
                points="0 0, 10 3, 0 6"
                fill="#409EFF"
              />
            </marker>
          </defs>
          
          <!-- 节点 -->
          <g class="nodes">
            <g
              v-for="node in nodes"
              :key="node.id"
              :transform="`translate(${node.x}, ${node.y})`"
              @click="selectNode(node)"
              class="node-group"
            >
              <rect
                v-if="node.type === 'task'"
                width="120"
                height="60"
                rx="4"
                fill="#409EFF"
                stroke="#409EFF"
                stroke-width="2"
                :class="{ selected: selectedNode?.id === node.id }"
              />
              <circle
                v-else-if="node.type === 'start' || node.type === 'end'"
                r="30"
                :fill="node.type === 'start' ? '#67C23A' : '#F56C6C'"
                :stroke="node.type === 'start' ? '#67C23A' : '#F56C6C'"
                stroke-width="2"
                :class="{ selected: selectedNode?.id === node.id }"
              />
              <polygon
                v-else-if="node.type === 'condition'"
                points="60,0 120,30 60,60 0,30"
                fill="#E6A23C"
                stroke="#E6A23C"
                stroke-width="2"
                :class="{ selected: selectedNode?.id === node.id }"
              />
              
              <text
                :x="node.type === 'task' ? 60 : node.type === 'condition' ? 60 : 0"
                :y="node.type === 'task' ? 35 : node.type === 'condition' ? 35 : 5"
                text-anchor="middle"
                fill="white"
                font-size="14"
              >
                {{ node.name }}
              </text>
            </g>
          </g>
        </svg>
      </div>
    </div>
    
    <!-- 预览对话框 -->
    <el-dialog
      v-model="showPreview"
      title="工作流预览"
      width="80%"
    >
      <div class="preview-content">
        <h4>工作流信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="流程名称">
            {{ workflowData.name }}
          </el-descriptions-item>
          <el-descriptions-item label="节点数量">
            {{ nodes.length }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ new Date().toLocaleString() }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag type="success">正常</el-tag>
          </el-descriptions-item>
        </el-descriptions>
        
        <h4 style="margin-top: 20px">流程步骤</h4>
        <el-steps :active="0" align-center>
          <el-step
            v-for="step in workflowSteps"
            :key="step.id"
            :title="step.name"
            :description="step.description"
           />
        </el-steps>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  RefreshLeft,
  RefreshRight,
  ZoomIn,
  ZoomOut,
  FullScreen,
  View,
  Check,
  CircleFilled,
  Stamp,
  Coin,
  Operation
} from '@element-plus/icons-vue'

// Props
interface Props {
   
  template?: unknown
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits(['save', 'close'])

// 响应式数据
const canvasRef = ref<HTMLElement>()
const svgRef = ref<SVGElement>()
const selectedNode = ref<unknown>(null)
const showPreview = ref(false)
const zoom = ref(1)

// 工作流数据
const workflowData = reactive({
  name: props.template?.name || '新建工作流',
  description: props.template?.description || '',
  steps: props.template?.steps || []
})

// 节点数据
const nodes = ref<any[]>([
  {
    id: 'start',
    type: 'start',
    name: 'HrHr开始',
    x: 100,
    y: 200
  },
  {
    id: 'task1',
    type: 'task',
    name: '初步分析',
    x: 300,
    y: 170,
    assignee: '',
    timeout: 60
  },
  {
    id: 'condition1',
    type: 'condition',
    name: '判断级别',
    x: 500,
    y: 170,
    expression: 'level === "high"'
  },
  {
    id: 'task2',
    type: 'task', 
    name: '紧急处理',
    x: 700,
    y: 100,
    assignee: '',
    timeout: 30
  },
  {
    id: 'task3',
    type: 'task',
    name: '常规处理',
    x: 700,
    y: 240,
    assignee: '',
    timeout: 120
  },
  {
    id: 'end',
    type: 'end',
    name: '结束',
    x: 900,
    y: 200
  }
])

// 连接线数据
const connections = ref([
  { id: 'c1', from: 'start', to: 'task1' },
  { id: 'c2', from: 'task1', to: 'condition1' },
  { id: 'c3', from: 'condition1', to: 'task2' },
  { id: 'c4', from: 'condition1', to: 'task3' },
  { id: 'c5', from: 'task2', to: 'end' },
  { id: 'c6', from: 'task3', to: 'end' }
])

// 历史记录
const history = ref<any[]>([])
const historyIndex = ref(-1)

// 静态数据
const workflowComponents = [
  { type: 'start', name: '开始节点', icon: 'CircleFilled' },
  { type: 'task', name: '任务节点', icon: 'Stamp' },
  { type: 'condition', name: '条件节点', icon: 'Coin' },
  { type: 'end', name: '结束节点', icon: 'CircleFilled' }
]

const availableUsers = ref([
  { id: 'user1', name: '张三' },
  { id: 'user2', name: '李四' },
  { id: 'user3', name: '王五' },
  { id: 'user4', name: '赵六' }
])

// 计算属性
const canUndo = computed(() => historyIndex.value > 0)
const canRedo = computed(() => historyIndex.value < history.value.length - 1)

const workflowSteps = computed(() => {
  return nodes.value
    .filter(node => node.type === 'task')
    .map(node => ({
      id: node.id,
      name: node.name,
      description: node.assignee ? `处理人: ${getAssigneeName(node.assignee)}` : '未分配'
    }))
})

// 方法
   
const handleDragStart = (event: DragEvent, component: unknown) => {
  event.dataTransfer!.setData('componentType', component.type)
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  const componentType = event.dataTransfer!.getData('componentType')
  
  if (!componentType) return
  
  const rect = canvasRef.value!.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top
  
  const newNode = {
    id: `node_${Date.now()}`,
    type: componentType,
    name: `新${getNodeTypeName(componentType)}`,
    x: x - 60,
    y: y - 30,
    assignee: '',
    timeout: 60
  }
  
  nodes.value.push(newNode)
  saveHistory()
}

const getNodeTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    start: '开始',
    task: '任务',
    condition: '条件',
    end: '结束'
  }
  return typeMap[type] || type
}

const getAssigneeName = (userId: string) => {
  const user = availableUsers.value.find(u => u.id === userId)
  return user?.name || '未知'
}

   
const selectNode = (node: unknown) => {
  selectedNode.value = node
}

   
const getConnectionPath = (connection: unknown) => {
  const fromNode = nodes.value.find(n => n.id === connection.from)
  const toNode = nodes.value.find(n => n.id === connection.to)
  
  if (!fromNode || !toNode) return ''
  
  let fromX = fromNode.x
  let fromY = fromNode.y
  const toX = toNode.x
  let toY = toNode.y
  
  // 调整连接点位置
  if (fromNode.type === 'task') {
    fromX += 120
    fromY += 30
  } else if (fromNode.type === 'condition') {
    fromX += 120
    fromY += 30
  } else {
    fromX += 30
  }
  
  if (toNode.type === 'task') {
    toY += 30
  } else if (toNode.type === 'condition') {
    toY += 30
  }
  
  // 贝塞尔曲线
  const midX = (fromX + toX) / 2
  return `M ${fromX} ${fromY} Q ${midX} ${fromY} ${midX} ${toY} T ${toX} ${toY}`
}

const saveHistory = () => {
  history.value = history.value.slice(0, historyIndex.value + 1)
  history.value.push({
    nodes: JSON.parse(JSON.stringify(nodes.value)),
    connections: JSON.parse(JSON.stringify(connections.value))
  })
  historyIndex.value++
}

const undo = () => {
  if (canUndo.value) {
    historyIndex.value--
    const state = history.value[historyIndex.value]
    nodes.value = JSON.parse(JSON.stringify(state.nodes))
    connections.value = JSON.parse(JSON.stringify(state.connections))
  }
}

const redo = () => {
  if (canRedo.value) {
    historyIndex.value++
    const state = history.value[historyIndex.value]
    nodes.value = JSON.parse(JSON.stringify(state.nodes))
    connections.value = JSON.parse(JSON.stringify(state.connections))
  }
}

const zoomIn = () => {
  zoom.value = Math.min(zoom.value * 1.2, 3)
  if (svgRef.value) {
    svgRef.value.style.transform = `scale(${zoom.value})`
  }
}

const zoomOut = () => {
  zoom.value = Math.max(zoom.value / 1.2, 0.3)
  if (svgRef.value) {
    svgRef.value.style.transform = `scale(${zoom.value})`
  }
}

const fitView = () => {
  zoom.value = 1
  if (svgRef.value) {
    svgRef.value.style.transform = 'scale(1)'
  }
}

const previewWorkflow = () => {
  showPreview.value = true
}

const saveWorkflow = () => {
  const data = {
    name: workflowData.name,
    description: workflowData.description,
    nodes: nodes.value,
    connections: connections.value,
    steps: workflowSteps.value.map(step => step.name),
    config: {
      nodes: nodes.value,
      connections: connections.value
    }
  }
  
  emit('save', data)
  ElMessage.success('工作流保存成功')
}

// 生命周期
onMounted(() => {
  saveHistory()
})
</script>

<style lang="scss" scoped>
.workflow-designer {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .designer-toolbar {
    height: 50px;
    padding: 0 20px;
    border-bottom: 1px solid var(--el-border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--el-bg-color);
    
    .toolbar-left,
    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }
  
  .designer-container {
    flex: 1;
    display: flex;
    overflow: hidden;
    
    .designer-sidebar {
      width: 280px;
      border-right: 1px solid var(--el-border-color);
      padding: 20px;
      overflow-y: auto;
      background: var(--el-bg-color);
      
      h4 {
        margin: 0 0 12px 0;
        font-size: 14px;
        color: var(--el-text-color-primary);
      }
      
      .component-list {
        margin-bottom: 20px;
        
        .component-item {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 12px;
          margin-bottom: 8px;
          border: 1px solid var(--el-border-color);
          border-radius: 4px;
          cursor: move;
          transition: all 0.3s;
          
          &:hover {
            border-color: var(--el-color-primary);
            background: var(--el-color-primary-light-9);
          }
        }
      }
      
      .properties-panel {
        .unit {
          margin-left: 8px;
          color: var(--el-text-color-secondary);
        }
      }
      
      .no-selection {
        text-align: center;
        color: var(--el-text-color-secondary);
        padding: 40px 0;
      }
    }
    
    .designer-canvas {
      flex: 1;
      overflow: auto;
      background: #f5f5f5;
      position: relative;
      
      svg {
        transform-origin: center;
        transition: transform 0.3s;
      }
      
      .node-group {
        cursor: pointer;
        
        rect,
        circle,
        polygon {
          transition: all 0.3s;
          
          &.selected {
            filter: drop-shadow(0 0 8px rgba(64, 158, 255, 0.6));
          }
        }
        
        &:hover {
          rect,
          circle,
          polygon {
            opacity: 0.8;
          }
        }
      }
    }
  }
  
  .preview-content {
    h4 {
      margin: 20px 0 12px 0;
      color: var(--el-text-color-primary);
    }
  }
}
</style>