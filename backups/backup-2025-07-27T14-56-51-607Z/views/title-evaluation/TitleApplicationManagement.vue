<template>
  <div class="title-application-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon><Document /></el-icon>
        职称申报管理
      </h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增申报
        </el-button>
        <el-button @click="handleBatchReview">
          <el-icon><Edit /></el-icon>
          批量审核
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="申请人">
          <el-input
            v-model="searchForm.applicantName"
            placeholder="请输入申请人姓名"
            clearable
            style="width: 150px"
            />
        </el-form-item>
        <el-form-item label="申报职称">
          <el-select
            v-model="searchForm.titleLevel"
            placeholder="请选择职称级别"
            clearable
            style="width: 150px"
          >
            <el-option label="正高级" value="senior"  />
            <el-option label="副高级" value="associate"  />
            <el-option label="中级" value="intermediate"  />
            <el-option label="初级" value="junior"  />
          </el-select>
        </el-form-item>
        <el-form-item label="申报状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="待提交" value="draft"  />
            <el-option label="待审核" value="pending"  />
            <el-option label="审核中" value="reviewing"  />
            <el-option label="已通过" value="approved"  />
            <el-option label="已拒绝" value="rejected"  />
          </el-select>
        </el-form-item>
        <el-form-item label="申报年度">
          <el-select
            v-model="searchForm.year"
            placeholder="请选择年度"
            clearable
            style="width: 120px"
          >
            <el-option label="2024年" value="2024"  />
            <el-option label="2023年" value="2023"  />
            <el-option label="2022年" value="2022"  />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.total }}</div>
              <div class="stat-label">总申报数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon pending">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.pending }}</div>
              <div class="stat-label">待审核</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon approved">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.approved }}</div>
              <div class="stat-label">已通过</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon rate">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.passRate }}%</div>
              <div class="stat-label">通过率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 申报列表 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="applicationId" label="申报编号" width="120"  />
        <el-table-column prop="applicantName" label="申请人" width="100"  />
        <el-table-column prop="employeeId" label="员工工号" width="120"  />
        <el-table-column prop="department" label="所属部门" width="120"  />
        <el-table-column prop="currentTitle" label="现有职称" width="120"  />
        <el-table-column prop="appliedTitle" label="申报职称" width="120">
          <template #default="{ row }">
            <el-tag :type="getTitleColor(row.titleLevel)">
              {{ getTitleLabel(row.titleLevel) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="titleCategory" label="职称类别" width="120"  />
        <el-table-column prop="applicationYear" label="申报年度" width="100"  />
        <el-table-column prop="submitTime" label="提交时间" width="120"  />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button type="text" size="small" @click="handleEdit(row)" v-if="row.status === 'draft'">
              编辑
            </el-button>
            <el-button type="text" size="small" @click="handleReview(row)" v-if="row.status === 'pending'">
              审核
            </el-button>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
            <el-dropdown @command="(command: unknown) => handleCommand(command, row)">
              <el-button type="text" size="small">
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="submit" v-if="row.status === 'draft'">
                    提交申报
                  </el-dropdown-item>
                  <el-dropdown-item command="materials">申报材料</el-dropdown-item>
                  <el-dropdown-item command="progress">审核进度</el-dropdown-item>
                  <el-dropdown-item command="feedback">审核意见</el-dropdown-item>
                  <el-dropdown-item command="print">打印申报表</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 申报详情/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="900px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="申报编号" prop="applicationId">
              <el-input v-model="formData.applicationId" :disabled="isView"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请人" prop="applicantName">
              <el-select v-model="formData.applicantName" :disabled="isView" style="width: 100%">
                <el-option label="张三" value="张三"  />
                <el-option label="李四" value="李四"  />
                <el-option label="王五" value="王五"  />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="现有职称" prop="currentTitle">
              <el-input v-model="formData.currentTitle" :disabled="isView"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申报职称" prop="appliedTitle">
              <el-input v-model="formData.appliedTitle" :disabled="isView"   />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="职称级别" prop="titleLevel">
              <el-select v-model="formData.titleLevel" :disabled="isView" style="width: 100%">
                <el-option label="正高级" value="senior"  />
                <el-option label="副高级" value="associate"  />
                <el-option label="中级" value="intermediate"  />
                <el-option label="初级" value="junior"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职称类别" prop="titleCategory">
              <el-select v-model="formData.titleCategory" :disabled="isView" style="width: 100%">
                <el-option label="教学科研" value="teaching"  />
                <el-option label="工程技术" value="engineering"  />
                <el-option label="管理" value="management"  />
                <el-option label="其他专业技术" value="other"  />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="申报年度" prop="applicationYear">
              <el-select v-model="formData.applicationYear" :disabled="isView" style="width: 100%">
                <el-option label="2024年" value="2024"  />
                <el-option label="2023年" value="2023"  />
                <el-option label="2022年" value="2022"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申报理由" prop="reason">
              <el-input v-model="formData.reason" :disabled="isView" placeholder="请输入申报理由"   />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="工作业绩" prop="achievements">
          <el-input
            v-model="formData.achievements"
            type="textarea"
            :rows="4"
            :disabled="isView"
            placeholder="请输入主要工作业绩"
            />
        </el-form-item>
        <el-form-item label="科研成果" prop="researchResults">
          <el-input
            v-model="formData.researchResults"
            type="textarea"
            :rows="4"
            :disabled="isView"
            placeholder="请输入科研成果"
            />
        </el-form-item>
        <el-form-item label="教学情况" prop="teachingExperience">
          <el-input
            v-model="formData.teachingExperience"
            type="textarea"
            :rows="3"
            :disabled="isView"
            placeholder="请输入教学情况"
            />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="formData.notes"
            type="textarea"
            :rows="2"
            :disabled="isView"
            placeholder="请输入备注信息"
            />
        </el-form-item>
      </el-form>
      <template #footer v-if="!isView">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'TitleApplicationManagement'
})
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Document,
  Plus,
  Edit,
  Download,
  Search,
  Refresh,
  Clock,
  CircleCheck,
  TrendCharts,
  ArrowDown
} from '@element-plus/icons-vue'
import { titleApplicationApi } from '@/api/titleEvaluation'

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isView = ref(false)
const selectedApplications = ref<any[]>([])

const searchForm = reactive({
  applicantName: '',
  titleLevel: '',
  status: '',
  year: ''
})

const stats = reactive({
  total: 156,
  pending: 45,
  approved: 89,
  passRate: 68.5
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

const tableData = ref([
  {
    id: '1',
    applicationId: 'TA2024001',
    applicantName: '张三',
    employeeId: 'EMP001',
    department: '计算机学院',
    currentTitle: '副教授',
    appliedTitle: '教授',
    titleLevel: 'senior',
    titleCategory: '教学科研',
    applicationYear: '2024',
    submitTime: '2024-06-15',
    status: 'reviewing',
    reason: '符合教授职称评定条件',
    achievements: '主持国家级项目2项，发表SCI论文15篇',
    researchResults: '获得省部级科技进步奖2项，专利授权8项',
    teachingExperience: '承担本科生和研究生教学任务，教学效果优秀',
    notes: '申报材料齐全，符合条件'
  }
])

const formData = reactive({
  applicationId: '',
  applicantName: '',
  currentTitle: '',
  appliedTitle: '',
  titleLevel: '',
  titleCategory: '',
  applicationYear: '',
  reason: '',
  achievements: '',
  researchResults: '',
  teachingExperience: '',
  notes: ''
})

const formRules = {
  applicationId: [{ required: true, message: '请输入申报编号', trigger: 'blur' }],
  applicantName: [{ required: true, message: '请选择申请人', trigger: 'change' }],
  appliedTitle: [{ required: true, message: '请输入申报职称', trigger: 'blur' }],
  titleLevel: [{ required: true, message: '请选择职称级别', trigger: 'change' }],
  titleCategory: [{ required: true, message: '请选择职称类别', trigger: 'change' }],
  applicationYear: [{ required: true, message: '请选择申报年度', trigger: 'change' }]
}

// 方法
const handleSearch = () => {
  console.log('搜索:', searchForm)
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    applicantName: '',
    titleLevel: '',
    status: '',
    year: ''
  })
  loadData()
}

   
const handleSelectionChange = (selection: unknown[]) => {
  selectedApplications.value = selection
}

const handleAdd = () => {
  dialogTitle.value = '新增职称申报'
  isView.value = false
  resetForm()
  dialogVisible.value = true
}

   
const handleView = (row: unknown) => {
  dialogTitle.value = '查看职称申报'
  isView.value = true
  Object.assign(formData, row)
  dialogVisible.value = true
}

   
const handleEdit = (row: unknown) => {
  dialogTitle.value = '编辑职称申报'
  isView.value = false
  Object.assign(formData, row)
  dialogVisible.value = true
}

   
const handleReview = (row: unknown) => {
  ElMessage.info(`审核 ${row.applicantName} 的职称申报`)
}

const handleBatchReview = () => {
  if (selectedApplications.value.length === 0) {
    ElMessage.warning('请先选择要批量审核的申报')
    return
  }
  ElMessage.info('批量审核功能开发中')
}

   
const handleCommand = (command: string, row: unknown) => {
  switch (command) {
    case 'submit':
      ElMessage.success(`${row.applicantName} 的申报已提交`)
      break
    case 'materials':
      ElMessage.info(`查看 ${row.applicantName} 的申报材料`)
      break
    case 'progress':
      ElMessage.info(`查看 ${row.applicantName} 的审核进度`)
      break
    case 'feedback':
      ElMessage.info(`查看 ${row.applicantName} 的审核意见`)
      break
    case 'print':
      ElMessage.info(`打印 ${row.applicantName} 的申报表`)
      break
    case 'delete':
      ElMessageBox.confirm(`确定要删除 ${row.applicantName} 的职称申报吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ElMessage.success('删除成功')
      })
      break
  }
}

const handleExport = () => {
  ElMessage.info('导出数据功能开发中')
}

const handleSubmit = () => {
  ElMessage.success('保存成功')
  dialogVisible.value = false
}

const handleDialogClose = () => {
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    applicationId: '',
    applicantName: '',
    currentTitle: '',
    appliedTitle: '',
    titleLevel: '',
    titleCategory: '',
    applicationYear: '',
    reason: '',
    achievements: '',
    researchResults: '',
    teachingExperience: '',
    notes: ''
  })
}

const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.currentPage - 1,
      size: pagination.pageSize,
      status: searchForm.status,
      titleCategory: searchForm.targetTitle === 'senior' ? 'teacher' : 
                    searchForm.targetTitle === 'associate' ? 'teacher' : 
                    searchForm.targetTitle === 'intermediate' ? 'engineering' : 
                    searchForm.targetTitle === 'junior' ? 'other' : undefined,
      applicantName: searchForm.applicantName,
      year: searchForm.dateRange && searchForm.dateRange.length > 0 ? 
            new Date(searchForm.dateRange[0]).getFullYear() : undefined
    }
    
    const response = await titleApplicationApi.getPage(params)
    if (response.data.success) {
      tableData.value = response.data.data.content.map(item => ({
        id: item.id,
        applicationNo: item.applicationCode,
        applicantName: item.applicantName,
        department: item.departmentName,
        currentTitle: item.currentTitle || '讲师',
        targetTitle: item.targetTitle,
        applicationDate: item.applicationDate,
        status: item.status.toLowerCase(),
        progress: item.status === 'UNDER_REVIEW' ? 60 :
                 item.status === 'APPROVED' ? 100 :
                 item.status === 'REJECTED' ? 100 :
                 item.status === 'SUBMITTED' ? 30 : 0,
        reviewer: item.status === 'APPROVED' || item.status === 'REJECTED' ? '评审委员会' : '',
        reviewDate: item.status === 'APPROVED' || item.status === 'REJECTED' ? 
                   new Date().toISOString().split('T')[0] : '',
        educationBackground: item.educationBackground || '博士研究生',
        workYears: '10年',
        achievements: item.researchAchievements || '发表论文10篇，主持项目3个',
        researchResults: item.researchAchievements,
        teachingExperience: item.teachingAchievements,
        notes: item.applicationReason
      }))
      pagination.total = response.data.data.totalElements
      
      // 更新统计数据
   
      const approvedCount = response.data.data.content.filter((item: unknown) => item.status === 'APPROVED').length
      stats.total = response.data.data.totalElements
   
      stats.reviewing = response.data.data.content.filter((item: unknown) => item.status === 'UNDER_REVIEW').length
      stats.approved = approvedCount
      stats.approvalRate = stats.total > 0 ? ((approvedCount / stats.total) * 100).toFixed(1) : '0.0'
    } else {
      throw new Error(response.data.message)
    }
  } catch (__error) {
    console.error('加载申请数据失败:', error)
    // 使用模拟数据
    tableData.value = [
      {
        id: '1',
        applicationNo: 'TA2024001',
        applicantName: '张三',
        department: '计算机学院',
        currentTitle: '副教授',
        targetTitle: '教授',
        applicationDate: '2024-03-15',
        status: 'reviewing',
        progress: 60,
        reviewer: '',
        reviewDate: '',
        educationBackground: '博士研究生',
        workYears: '10年',
        achievements: '发表SCI论文15篇，主持国家级项目2项',
        researchResults: '在人工智能领域取得突破性成果',
        teachingExperience: '教学评价优秀，指导学生获奖',
        notes: '符合晋升条件，申请职称晋升'
      }
    ]
    pagination.total = 156
    ElMessage.warning('数据加载失败，显示默认数据')
  } finally {
    loading.value = false
  }
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadData()
}

const getTitleColor = (level: string) => {
  const colors: Record<string, string> = {
    senior: 'danger',
    associate: 'warning',
    intermediate: 'primary',
    junior: 'success'
  }
  return colors[level] || ''
}

const getTitleLabel = (level: string) => {
  const labels: Record<string, string> = {
    senior: '正高级',
    associate: '副高级',
    intermediate: '中级',
    junior: '初级'
  }
  return labels[level] || level
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    draft: 'info',
    pending: 'warning',
    reviewing: 'primary',
    approved: 'success',
    rejected: 'danger'
  }
  return types[status] || ''
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    draft: '待提交',
    pending: '待审核',
    reviewing: '审核中',
    approved: '已通过',
    rejected: '已拒绝'
  }
  return labels[status] || status
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.title-application-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.pending {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.approved {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-icon.rate {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
