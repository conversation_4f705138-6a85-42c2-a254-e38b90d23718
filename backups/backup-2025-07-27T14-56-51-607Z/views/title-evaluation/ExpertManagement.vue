<template>
  <div class="expert-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon><Avatar /></el-icon>
        评审专家管理
      </h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增专家
        </el-button>
        <el-button @click="handleImport">
          <el-icon><Upload /></el-icon>
          批量导入
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出专家库
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="专家姓名">
          <el-input
            v-model="searchForm.expertName"
            placeholder="请输入专家姓名"
            clearable
            style="width: 150px"
            />
        </el-form-item>
        <el-form-item label="专业领域">
          <el-select
            v-model="searchForm.field"
            placeholder="请选择专业领域"
            clearable
            style="width: 150px"
          >
            <el-option label="计算机科学" value="computer"  />
            <el-option label="机械工程" value="mechanical"  />
            <el-option label="经济管理" value="economics"  />
            <el-option label="教育学" value="education"  />
          </el-select>
        </el-form-item>
        <el-form-item label="专家级别">
          <el-select
            v-model="searchForm.level"
            placeholder="请选择专家级别"
            clearable
            style="width: 150px"
          >
            <el-option label="国家级" value="national"  />
            <el-option label="省级" value="provincial"  />
            <el-option label="市级" value="municipal"  />
            <el-option label="校级" value="university"  />
          </el-select>
        </el-form-item>
        <el-form-item label="专家状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="在库" value="active"  />
            <el-option label="停用" value="inactive"  />
            <el-option label="退休" value="retired"  />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><Avatar /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.total }}</div>
              <div class="stat-label">专家总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon active">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.active }}</div>
              <div class="stat-label">在库专家</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon external">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.external }}</div>
              <div class="stat-label">外聘专家</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon reviews">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.reviews }}</div>
              <div class="stat-label">评审次数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 专家列表 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="expertId" label="专家编号" width="120"  />
        <el-table-column prop="expertName" label="专家姓名" width="100"  />
        <el-table-column prop="gender" label="性别" width="80">
          <template #default="{ row }">
            {{ row.gender === 'male' ? '男' : '女' }}
          </template>
        </el-table-column>
        <el-table-column prop="title" label="职称" width="120"  />
        <el-table-column prop="workUnit" label="工作单位" min-width="180"  />
        <el-table-column prop="field" label="专业领域" width="120">
          <template #default="{ row }">
            <el-tag :type="getFieldColor(row.field)">
              {{ getFieldLabel(row.field) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="level" label="专家级别" width="100">
          <template #default="{ row }">
            <el-tag :type="getLevelColor(row.level)">
              {{ getLevelLabel(row.level) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="reviewCount" label="评审次数" width="100"  />
        <el-table-column prop="phone" label="联系电话" width="130"  />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button type="text" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="text" size="small" @click="handleAssign(row)">
              分配评审
            </el-button>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
            <el-dropdown @command="(command: unknown) => handleCommand(command, row)">
              <el-button type="text" size="small">
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="activate" v-if="row.status === 'inactive'">
                    启用专家
                  </el-dropdown-item>
                  <el-dropdown-item command="deactivate" v-if="row.status === 'active'">
                    停用专家
                  </el-dropdown-item>
                  <el-dropdown-item command="history">评审历史</el-dropdown-item>
                  <el-dropdown-item command="certificate">专家证书</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 专家详情/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="900px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="专家编号" prop="expertId">
              <el-input v-model="formData.expertId" :disabled="isView"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="专家姓名" prop="expertName">
              <el-input v-model="formData.expertName" :disabled="isView"   />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="性别" prop="gender">
              <el-select v-model="formData.gender" :disabled="isView" style="width: 100%">
                <el-option label="男" value="male"  />
                <el-option label="女" value="female"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出生日期" prop="birthDate">
              <el-date-picker
                v-model="formData.birthDate"
                type="date"
                :disabled="isView"
                style="width: 100%"
               />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="职称" prop="title">
              <el-input v-model="formData.title" :disabled="isView"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="学历" prop="education">
              <el-select v-model="formData.education" :disabled="isView" style="width: 100%">
                <el-option label="博士" value="phd"  />
                <el-option label="硕士" value="master"  />
                <el-option label="本科" value="bachelor"  />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="专业领域" prop="field">
              <el-select v-model="formData.field" :disabled="isView" style="width: 100%">
                <el-option label="计算机科学" value="computer"  />
                <el-option label="机械工程" value="mechanical"  />
                <el-option label="经济管理" value="economics"  />
                <el-option label="教育学" value="education"  />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="专家级别" prop="level">
              <el-select v-model="formData.level" :disabled="isView" style="width: 100%">
                <el-option label="国家级" value="national"  />
                <el-option label="省级" value="provincial"  />
                <el-option label="市级" value="municipal"  />
                <el-option label="校级" value="university"  />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="工作单位" prop="workUnit">
          <el-input v-model="formData.workUnit" :disabled="isView"   />
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="formData.phone" :disabled="isView"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="电子邮箱" prop="email">
              <el-input v-model="formData.email" :disabled="isView"   />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="专业特长" prop="specialties">
          <el-input
            v-model="formData.specialties"
            type="textarea"
            :rows="3"
            :disabled="isView"
            placeholder="请输入专业特长"
            />
        </el-form-item>
        <el-form-item label="主要成就" prop="achievements">
          <el-input
            v-model="formData.achievements"
            type="textarea"
            :rows="4"
            :disabled="isView"
            placeholder="请输入主要成就"
            />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="formData.notes"
            type="textarea"
            :rows="2"
            :disabled="isView"
            placeholder="请输入备注信息"
            />
        </el-form-item>
      </el-form>
      <template #footer v-if="!isView">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">

defineOptions({
  name: 'ExpertManagement'
})
 
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Avatar,
  Plus,
  Upload,
  Download,
  Search,
  Refresh,
  CircleCheck,
  User,
  Document,
  ArrowDown
} from '@element-plus/icons-vue'
import { reviewExpertApi } from '@/api/titleEvaluation'

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isView = ref(false)

const searchForm = reactive({
  expertName: '',
  field: '',
  level: '',
  status: ''
})

const stats = reactive({
  total: 89,
  active: 76,
  external: 23,
  reviews: 245
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

const tableData = ref([
  {
    id: '1',
    expertId: '*********',
    expertName: '李教授',
    gender: 'male',
    birthDate: '1970-05-15',
    title: '教授',
    education: 'phd',
    workUnit: '清华大学计算机系',
    field: 'computer',
    level: 'national',
    reviewCount: 15,
    phone: '13800138001',
    email: '<EMAIL>',
    status: 'active',
    specialties: '人工智能、机器学习、数据挖掘',
    achievements: '发表SCI论文50余篇，主持国家自然科学基金重点项目3项',
    notes: '国内知名专家，评审经验丰富'
  }
])

const formData = reactive({
  expertId: '',
  expertName: '',
  gender: '',
  birthDate: '',
  title: '',
  education: '',
  workUnit: '',
  field: '',
  level: '',
  phone: '',
  email: '',
  specialties: '',
  achievements: '',
  notes: ''
})

const formRules = {
  expertId: [{ required: true, message: '请输入专家编号', trigger: 'blur' }],
  expertName: [{ required: true, message: '请输入专家姓名', trigger: 'blur' }],
  gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
  title: [{ required: true, message: '请输入职称', trigger: 'blur' }],
  workUnit: [{ required: true, message: '请输入工作单位', trigger: 'blur' }],
  field: [{ required: true, message: '请选择专业领域', trigger: 'change' }],
  level: [{ required: true, message: '请选择专家级别', trigger: 'change' }],
  phone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }]
}

// 方法
const handleSearch = () => {
  console.log('搜索:', searchForm)
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    expertName: '',
    field: '',
    level: '',
    status: ''
  })
  loadData()
}

const handleAdd = () => {
  dialogTitle.value = '新增评审专家'
  isView.value = false
  resetForm()
  dialogVisible.value = true
}

   
const handleView = (row: unknown) => {
  dialogTitle.value = '查看评审专家'
  isView.value = true
  Object.assign(formData, row)
  dialogVisible.value = true
}

   
const handleEdit = (row: unknown) => {
  dialogTitle.value = '编辑评审专家'
  isView.value = false
  Object.assign(formData, row)
  dialogVisible.value = true
}

   
const handleAssign = (row: unknown) => {
  ElMessage.info(`为专家 ${row.expertName} 分配评审任务`)
}

   
const handleCommand = (command: string, row: unknown) => {
  switch (command) {
    case 'activate':
      ElMessage.success(`专家 ${row.expertName} 已启用`)
      break
    case 'deactivate':
      ElMessage.success(`专家 ${row.expertName} 已停用`)
      break
    case 'history':
      ElMessage.info(`查看专家 ${row.expertName} 的评审历史`)
      break
    case 'certificate':
      ElMessage.info(`查看专家 ${row.expertName} 的专家证书`)
      break
    case 'delete':
      ElMessageBox.confirm(`确定要删除专家 ${row.expertName} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ElMessage.success('删除成功')
      })
      break
  }
}

const handleImport = () => {
  ElMessage.info('批量导入功能开发中')
}

const handleExport = () => {
  ElMessage.info('导出专家库功能开发中')
}

const handleSubmit = () => {
  ElMessage.success('保存成功')
  dialogVisible.value = false
}

const handleDialogClose = () => {
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    expertId: '',
    expertName: '',
    gender: '',
    birthDate: '',
    title: '',
    education: '',
    workUnit: '',
    field: '',
    level: '',
    phone: '',
    email: '',
    specialties: '',
    achievements: '',
    notes: ''
  })
}

const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.currentPage - 1,
      size: pagination.pageSize,
      specialization: searchForm.field,
      isActive: searchForm.status === 'active' ? true : searchForm.status === 'inactive' ? false : undefined
    }
    
    const response = await reviewExpertApi.getPage(params)
    if (response.data.success) {
      tableData.value = response.data.data.content.map(item => ({
        id: item.id,
        expertId: item.expertId,
        expertName: item.expertName,
        gender: 'male', // 默认值，因为API接口中没有性别字段
        birthDate: '1970-01-01', // 默认值
        title: item.expertTitle,
        education: item.expertTitle.includes('教授') ? 'phd' : 'master',
        workUnit: item.institution,
        field: item.specialization === '计算机科学' ? 'computer' :
               item.specialization === '机械工程' ? 'mechanical' :
               item.specialization === '经济管理' ? 'economics' :
               item.specialization === '教育学' ? 'education' : 'other',
        level: item.averageScore >= 9 ? 'national' :
               item.averageScore >= 8 ? 'provincial' :
               item.averageScore >= 7 ? 'municipal' : 'university',
        reviewCount: item.reviewCount,
        phone: item.phone,
        email: item.email,
        status: item.isActive ? 'active' : 'inactive',
        specialties: item.specialization,
        achievements: `评审${item.reviewCount}次，平均评分${item.averageScore}分`,
        notes: ''
      }))
      pagination.total = response.data.data.totalElements
      
      // 更新统计数据
      stats.total = response.data.data.totalElements
   
      stats.active = response.data.data.content.filter((item: unknown) => item.isActive).length
      stats.external = Math.floor(response.data.data.totalElements * 0.26) // 约26%外部专家
   
      stats.reviews = response.data.data.content.reduce((sum: number, item: unknown) => sum + item.reviewCount, 0)
    } else {
      throw new Error(response.data.message)
    }
  } catch (__error) {
    console.error('加载专家数据失败:', error)
    // 使用模拟数据
    tableData.value = [
      {
        id: '1',
        expertId: '*********',
        expertName: '李教授',
        gender: 'male',
        birthDate: '1970-05-15',
        title: '教授',
        education: 'phd',
        workUnit: '清华大学计算机系',
        field: 'computer',
        level: 'national',
        reviewCount: 15,
        phone: '13800138001',
        email: '<EMAIL>',
        status: 'active',
        specialties: '人工智能、机器学习、数据挖掘',
        achievements: '发表SCI论文50余篇，主持国家自然科学基金重点项目3项',
        notes: '国内知名专家，评审经验丰富'
      }
    ]
    pagination.total = 89
    ElMessage.warning('数据加载失败，显示默认数据')
  } finally {
    loading.value = false
  }
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadData()
}

const getFieldColor = (field: string) => {
  const colors: Record<string, string> = {
    computer: 'primary',
    mechanical: 'success',
    economics: 'warning',
    education: 'info'
  }
  return colors[field] || ''
}

const getFieldLabel = (field: string) => {
  const labels: Record<string, string> = {
    computer: '计算机科学',
    mechanical: '机械工程',
    economics: '经济管理',
    education: '教育学'
  }
  return labels[field] || field
}

const getLevelColor = (level: string) => {
  const colors: Record<string, string> = {
    national: 'danger',
    provincial: 'warning',
    municipal: 'primary',
    university: 'success'
  }
  return colors[level] || ''
}

const getLevelLabel = (level: string) => {
  const labels: Record<string, string> = {
    national: '国家级',
    provincial: '省级',
    municipal: '市级',
    university: '校级'
  }
  return labels[level] || level
}

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    active: 'success',
    inactive: 'warning',
    retired: 'info'
  }
  return types[status] || ''
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    active: '在库',
    inactive: '停用',
    retired: '退休'
  }
  return labels[status] || status
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.expert-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.active {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-icon.external {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.reviews {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
