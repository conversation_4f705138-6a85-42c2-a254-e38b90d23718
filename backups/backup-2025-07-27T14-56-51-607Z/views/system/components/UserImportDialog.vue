<template>
  <el-dialog
    v-model="visible"
    title="批量导入用户"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="user-import">
      <!-- 上传区域 -->
      <el-upload
        ref="uploadRef"
        class="upload-area"
        drag
        :auto-upload="false"
        :on-change="handleFileChange"
        :on-remove="handleFileRemove"
        :before-upload="beforeUpload"
        accept=".xlsx,.csv"
        :limit="1"
      >
        <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            支持 .xlsx、.csv 格式文件，单个文件不超过 10MB
          </div>
        </template>
      </el-upload>

      <!-- 文件信息 -->
      <div v-if="uploadFile" class="file-info">
        <h4>文件信息</h4>
        <el-descriptions :column="2" border size="small">
          <el-descriptions-item label="文件名">{{ uploadFile.name }}</el-descriptions-item>
          <el-descriptions-item label="文件大小">{{ formatFileSize(uploadFile.size) }}</el-descriptions-item>
          <el-descriptions-item label="文件类型">{{ getFileType(uploadFile.name) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(uploadFile.status)" size="small">
              {{ getStatusLabel(uploadFile.status) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 导入选项 -->
      <div v-if="uploadFile" class="import-options">
        <h4>导入选项</h4>
        <el-form :model="importForm" label-width="120px">
          <el-form-item label="导入模式">
            <el-radio-group v-model="importForm.mode">
              <el-radio value="CREATE">仅创建新用户</el-radio>
              <el-radio value="UPDATE">更新已存在用户</el-radio>
              <el-radio value="MERGE">合并模式</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="冲突处理">
            <el-radio-group v-model="importForm.conflictStrategy">
              <el-radio value="SKIP">跳过冲突项</el-radio>
              <el-radio value="OVERWRITE">覆盖已存在</el-radio>
              <el-radio value="RENAME">自动重命名</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="默认密码">
            <el-input
              v-model="importForm.defaultPassword"
              type="password"
              placeholder="新用户的默认密码"
              show-password
              />
          </el-form-item>

          <el-form-item label="默认角色">
            <el-select
              v-model="importForm.defaultRoles"
              multiple
              placeholder="为新用户分配默认角色"
              style="width: 100%"
            >
              <el-option
                v-for="role in roles"
                :key="role.id"
                :label="role.name"
                :value="role.id"
               />
            </el-select>
          </el-form-item>

          <el-form-item label="数据验证">
            <el-switch v-model="importForm.validate"  />
            <el-text size="small" type="info" style="margin-left: 8px">
              导入前验证用户数据的完整性
            </el-text>
          </el-form-item>

          <el-form-item label="发送通知">
            <el-switch v-model="importForm.sendNotification"  />
            <el-text size="small" type="info" style="margin-left: 8px">
              向新用户发送账号信息通知
            </el-text>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据预览 -->
      <div v-if="previewData.length > 0" class="data-preview">
        <h4>数据预览 (前5条)</h4>
        <el-table :data="previewData.slice(0, 5)" stripe size="small">
          <el-table-column prop="username" label="用户名" width="120"  />
          <el-table-column prop="name" label="姓名" width="100"  />
          <el-table-column prop="email" label="邮箱" width="180"  />
          <el-table-column prop="phone" label="手机号" width="120"  />
          <el-table-column prop="department" label="部门" width="100"  />
          <el-table-column prop="position" label="职位" show-overflow-tooltip  />
        </el-table>
        <div class="preview-summary">
          <el-text size="small" type="info">
            共 {{ previewData.length }} 条数据，预览前 5 条
          </el-text>
        </div>
      </div>

      <!-- 导入结果 -->
      <div v-if="importResults.length > 0" class="import-results">
        <h4>导入结果</h4>
        <div class="result-summary">
          <el-statistic title="成功" :value="successCount"  />
          <el-statistic title="失败" :value="failedCount"  />
          <el-statistic title="跳过" :value="skippedCount"  />
          <el-statistic title="总计" :value="importResults.length"  />
        </div>
        <el-table :data="importResults" stripe size="small" max-height="200">
          <el-table-column prop="username" label="用户名" width="120"  />
          <el-table-column prop="name" label="姓名" width="100"  />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getResultTagType(scope.row.status)" size="small">
                {{ getResultLabel(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="message" label="结果信息" show-overflow-tooltip  />
        </el-table>
      </div>

      <!-- 进度条 -->
      <div v-if="importing" class="import-progress">
        <el-progress
          :percentage="importProgress"
          :status="importProgress === 100 ? 'success' : undefined"
         />
        <div class="progress-text">
          {{ importProgressText }}
        </div>
      </div>

      <!-- 模板下载 -->
      <el-card class="template-download" shadow="never">
        <template #header>
          <span>模板下载</span>
        </template>
        <div class="template-actions">
          <el-button @click="downloadTemplate">
            <el-icon><Download /></el-icon>
            下载Excel模板
          </el-button>
          <el-button @click="downloadCsvTemplate">
            <el-icon><Download /></el-icon>
            下载CSV模板
          </el-button>
        </div>
        <div class="template-description">
          <p>模板包含以下字段：</p>
          <ul>
            <li>username - 用户名（必填，唯一）</li>
            <li>name - 姓名（必填）</li>
            <li>email - 邮箱（必填，唯一）</li>
            <li>phone - 手机号（可选）</li>
            <li>department - 部门（可选）</li>
            <li>position - 职位（可选）</li>
            <li>remark - 备注（可选）</li>
          </ul>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">
          {{ importing ? '后台运行' : '取消' }}
        </el-button>
        <el-button
          v-if="uploadFile && !importing"
          @click="previewFile"
        >
          预览数据
        </el-button>
        <el-button
          v-if="previewData.length > 0 && !importing"
          type="primary"
          @click="startImport"
        >
          开始导入
        </el-button>
        <el-button
          v-if="importing"
          type="warning"
          @click="cancelImport"
        >
          取消导入
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled, Download } from '@element-plus/icons-vue'
import { userAPI } from '@/api/system/user'
import { roleAPI } from '@/api/system/role'

// Props
interface Props {
  modelValue: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
   
  'imported': [results: unknown[]]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const uploadFile = ref<unknown>(null)
const previewData = ref<any[]>([])
const importing = ref(false)
const importProgress = ref(0)
const importProgressText = ref('')
const importResults = ref<any[]>([])
const roles = ref<any[]>([])

// 导入表单
const importForm = reactive({
  mode: 'CREATE',
  conflictStrategy: 'SKIP',
  defaultPassword: '123456',
  defaultRoles: [],
  validate: true,
  sendNotification: false
})

// 计算属性
const successCount = computed(() => 
  importResults.value.filter(r => r.status === 'SUCCESS').length
)
const failedCount = computed(() => 
  importResults.value.filter(r => r.status === 'FAILED').length
)
const skippedCount = computed(() => 
  importResults.value.filter(r => r.status === 'SKIPPED').length
)

const uploadRef = ref()

// 方法
const handleClose = () => {
  if (!importing.value) {
    visible.value = false
    resetData()
  }
}

const resetData = () => {
  uploadFile.value = null
  previewData.value = []
  importResults.value = []
  importProgress.value = 0
  importProgressText.value = ''
  Object.assign(importForm, {
    mode: 'CREATE',
    conflictStrategy: 'SKIP',
    defaultPassword: '123456',
    defaultRoles: [],
    validate: true,
    sendNotification: false
  })
}

   
const handleFileChange = (file: unknown, files: unknown[]) => {
  uploadFile.value = {
    name: file.name,
    size: file.size,
    type: getFileType(file.name),
    status: 'PENDING',
    raw: file.raw
  }
  previewData.value = []
  importResults.value = []
}

const handleFileRemove = () => {
  uploadFile.value = null
  previewData.value = []
  importResults.value = []
}

const beforeUpload = (file: File) => {
  const isValidType = /\.(xlsx|csv)$/i.test(file.name)
  const isValidSize = file.size / 1024 / 1024 < 10

  if (!isValidType) {
    ElMessage.error('只支持 .xlsx、.csv 格式文件')
    return false
  }
  
  if (!isValidSize) {
    ElMessage.error('文件大小不能超过 10MB')
    return false
  }
  
  return true
}

const previewFile = async () => {
  if (!uploadFile.value) return

  try {
    // 调用API解析文件并预览数据
    const data = await userAPI.previewImportFile(uploadFile.value.raw)
    
    if (data && Array.isArray(data)) {
      previewData.value = data
      uploadFile.value.status = 'PARSED'
      ElMessage.success('文件解析成功')
    } else {
      // 如果API返回格式不符，使用备用数据
      previewData.value = [
        {
          username: 'HrZhangsan',
          name: '张三',
          email: '<EMAIL>',
          phone: '13800138000',
          department: '技术部',
          position: '软件工程师'
        },
        {
          username: 'lisi',
          name: '李四',
          email: '<EMAIL>',
          phone: '13800138001',
          department: '人事部',
          position: '人事专员'
        }
      ]
      uploadFile.value.status = 'PARSED'
      ElMessage.warning('使用模拟数据预览')
    }
  } catch (__error) {
    console.error('文件预览失败:', error)
    ElMessage.warning('文件预览失败，使用模拟数据')
    // 使用备用数据
    previewData.value = [
      {
        username: 'zhangsan',
        name: '张三',
        email: '<EMAIL>',
        phone: '13800138000',
        department: '技术部',
        position: '软件工程师'
      },
      {
        username: 'lisi',
        name: '李四',
        email: '<EMAIL>',
        phone: '13800138001',
        department: '人事部',
        position: '人事专员'
      }
    ]
    uploadFile.value.status = 'PARSED'
  }
}

const startImport = async () => {
  if (previewData.value.length === 0) {
    ElMessage.warning('请先预览数据')
    return
  }

  importing.value = true
  importProgress.value = 0
  importResults.value = []
  importProgressText.value = '正在准备导入...'

  try {
    // 调用API批量导入用户
    const response = await userAPI.importUsers({
      file: uploadFile.value.raw,
      mode: importForm.mode as 'CREATE' | 'UPDATE' | 'MERGE',
      conflictStrategy: importForm.conflictStrategy as 'SKIP' | 'OVERWRITE' | 'RENAME',
      defaultPassword: importForm.defaultPassword,
      defaultRoles: importForm.defaultRoles,
      validate: importForm.validate,
      sendNotification: importForm.sendNotification
    })

    if (response && response.results) {
      importResults.value = response.results
      importProgress.value = 100
      importProgressText.value = '导入完成'
      ElMessage.success(`用户导入完成：成功 ${response.success} 个，失败 ${response.failed} 个，跳过 ${response.skipped} 个`)
      emit('imported', importResults.value)
    } else {
      // 使用模拟导入过程
      for (let i = 0; i < previewData.value.length; i++) {
        const user = previewData.value[i]
        importProgressText.value = `正在导入用户 ${user.name}...`
        
        await simulateImport(user)
        
        importProgress.value = Math.round(((i + 1) / previewData.value.length) * 100)
      }

      importProgressText.value = '导入完成'
      ElMessage.success('用户导入完成')
      emit('imported', importResults.value)
    }
  } catch (__error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败')
    importProgressText.value = '导入失败'
  } finally {
    importing.value = false
  }
}

   
const simulateImport = async (user: unknown) => {
  // 模拟导入延迟
  await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000))
  
  // 模拟导入结果
  const success = Math.random() > 0.2 // 80% 成功率
  const result = {
    username: user.username,
    name: user.name,
    status: success ? 'SUCCESS' : 'FAILED',
    message: success ? '导入成功' : '邮箱已存在或数据格式错误'
  }
  
  importResults.value.push(result)
}

const cancelImport = () => {
  importing.value = false
  importProgress.value = 0
  importProgressText.value = '导入已取消'
  ElMessage.info('导入已取消')
}

const downloadTemplate = async () => {
  try {
    // 调用API下载Excel模板
    const blob = await userAPI.downloadImportTemplate('excel')
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `用户导入模板_${new Date().getTime()}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('Excel模板下载成功')
  } catch (__error) {
    console.error('Excel模板下载失败:', error)
    ElMessage.error('Excel模板下载失败')
  }
}

const downloadCsvTemplate = async () => {
  try {
    // 调用API下载CSV模板
    const blob = await userAPI.downloadImportTemplate('csv')
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `用户导入模板_${new Date().getTime()}.csv`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('CSV模板下载成功')
  } catch (__error) {
    console.error('CSV模板下载失败:', error)
    ElMessage.error('CSV模板下载失败')
  }
}

const loadRoles = async () => {
  try {
    // 调用API获取角色列表
    const response = await roleAPI.list({ type: 'all', status: 1 })
    
    if (response && response.data) {
   
      roles.value = response.data.map((role: unknown) => ({
        id: role.id,
        name: role.name
      }))
    } else {
      // 使用备用数据
      roles.value = [
        { id: '1', name: '系统管理员' },
        { id: '2', name: '人事专员' },
        { id: '3', name: '普通用户' }
      ]
    }
  } catch (__error) {
    console.error('加载角色列表失败:', error)
    ElMessage.warning('加载角色列表失败，使用默认数据')
    // 使用备用数据
    roles.value = [
      { id: '1', name: '系统管理员' },
      { id: '2', name: '人事丣员' },
      { id: '3', name: '普通用户' }
    ]
  }
}

// 辅助方法
const getFileType = (fileName: string) => {
  const ext = fileName.split('.').pop()?.toLowerCase()
  switch (ext) {
    case 'xlsx': return 'Excel'
    case 'csv': return 'CSV'
    default: return 'UNKNOWN'
  }
}

const formatFileSize = (size: number) => {
  if (size < 1024) return `${size}B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`
  return `${(size / 1024 / 1024).toFixed(1)}MB`
}

const getStatusTagType = (status: string) => {
  switch (status) {
    case 'PENDING': return 'info'
    case 'PARSED': return 'success'
    case 'ERROR': return 'danger'
    default: return ''
  }
}

const getStatusLabel = (status: string) => {
  switch (status) {
    case 'PENDING': return '待处理'
    case 'PARSED': return '已解析'
    case 'ERROR': return '错误'
    default: return status
  }
}

const getResultTagType = (status: string) => {
  switch (status) {
    case 'SUCCESS': return 'success'
    case 'FAILED': return 'danger'
    case 'SKIPPED': return 'warning'
    default: return ''
  }
}

const getResultLabel = (status: string) => {
  switch (status) {
    case 'SUCCESS': return '成功'
    case 'FAILED': return '失败'
    case 'SKIPPED': return '跳过'
    default: return status
  }
}

// 初始化
onMounted(() => {
  loadRoles()
})
</script>

<style scoped>
.user-import {
  padding: 10px 0;
}

.upload-area {
  margin-bottom: 20px;
}

.file-info,
.import-options,
.data-preview,
.import-results,
.template-download {
  margin-bottom: 20px;
}

.file-info h4,
.import-options h4,
.data-preview h4,
.import-results h4 {
  margin-bottom: 12px;
  color: #303133;
  font-size: 16px;
}

.preview-summary {
  margin-top: 8px;
  text-align: center;
}

.result-summary {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.import-progress {
  margin: 20px 0;
}

.progress-text {
  text-align: center;
  margin-top: 8px;
  color: #606266;
  font-size: 14px;
}

.template-actions {
  margin-bottom: 16px;
  display: flex;
  gap: 12px;
}

.template-description p {
  margin-bottom: 8px;
  color: #606266;
}

.template-description ul {
  margin: 0;
  padding-left: 20px;
}

.template-description li {
  margin-bottom: 4px;
  color: #606266;
  font-size: 12px;
}

.dialog-footer {
  text-align: right;
}
</style>
