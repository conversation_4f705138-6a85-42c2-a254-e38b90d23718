<template>
  <div class="performance-dashboard">
    <!-- 看板头部 -->
    <div class="dashboard-header">
      <h2>绩效管理看板</h2>
      <div class="header-controls">
        <el-select v-model="selectedPeriod" placeholder="选择考核周期" @change="handlePeriodChange">
          <el-option label="2025年度" value="2025"  />
          <el-option label="2024年度" value="2024"  />
          <el-option label="2025年Q1" value="2025Q1"  />
          <el-option label="2024年Q4" value="2024Q4"  />
        </el-select>
        <el-radio-group v-model="viewMode" @change="handleViewModeChange">
          <el-radio-button label="personal">个人视角</el-radio-button>
          <el-radio-button label="team">团队视角</el-radio-button>
          <el-radio-button label="department">部门视角</el-radio-button>
        </el-radio-group>
        <el-button :icon="Refresh" @click="refreshData">刷新</el-button>
      </div>
    </div>

    <!-- 个人视角 -->
    <div v-if="viewMode === 'personal'" class="personal-view">
      <!-- 个人绩效概览 -->
      <el-row :gutter="20" class="overview-row">
        <el-col :span="6">
          <el-card class="metric-card">
            <div class="metric-content">
              <div class="metric-value">{{ personalData.currentScore }}</div>
              <div class="metric-label">当前绩效分数</div>
              <div class="metric-trend">
                <el-icon :color="getTrendColor(personalData.trend)">
                  <ArrowUp v-if="personalData.trend > 0" />
                  <ArrowDown v-else />
                </el-icon>
                <span>{{ Math.abs(personalData.trend) }}%</span>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="metric-card">
            <div class="metric-content">
              <el-tag :type="getGradeType(personalData.currentGrade)" size="large">
                {{ personalData.currentGrade }}级
              </el-tag>
              <div class="metric-label">当前等级</div>
              <el-rate 
                v-model="personalData.gradeStars" 
                disabled 
                :colors="['#F56C6C', '#E6A23C', '#67C23A']"
               />
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="metric-card">
            <div class="metric-content">
              <div class="metric-value">{{ personalData.departmentRank }}/{{ personalData.departmentTotal }}</div>
              <div class="metric-label">部门排名</div>
              <el-progress 
                :percentage="(1 - personalData.departmentRank / personalData.departmentTotal) * 100" 
                :color="getRankColor"
               />
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="metric-card">
            <div class="metric-content">
              <div class="metric-value">{{ personalData.completionRate }}%</div>
              <div class="metric-label">目标完成率</div>
              <el-progress 
                type="circle" 
                :percentage="personalData.completionRate" 
                :width="80"
               />
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 个人绩效趋势和维度分析 -->
      <el-row :gutter="20" class="chart-row">
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>绩效趋势</span>
            </template>
            <div id="personalTrendChart" style="height: 300px"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>能力维度分析</span>
            </template>
            <div id="personalRadarChart" style="height: 300px"></div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 优势与改进 -->
      <el-row :gutter="20" class="feedback-row">
        <el-col :span="12">
          <el-card class="feedback-card">
            <template #header>
              <span>个人优势</span>
              <el-icon color="#67c23a"><CircleCheck /></el-icon>
            </template>
            <el-tag 
              v-for="(strength, index) in personalData.strengths" 
              :key="index"
              type="success"
              style="margin: 5px"
            >
              {{ strength }}
            </el-tag>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="feedback-card">
            <template #header>
              <span>改进建议</span>
              <el-icon color="#e6a23c"><Warning /></el-icon>
            </template>
            <el-tag 
              v-for="(weakness, index) in personalData.weaknesses" 
              :key="index"
              type="warning"
              style="margin: 5px"
            >
              {{ weakness }}
            </el-tag>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 团队视角 -->
    <div v-if="viewMode === 'team'" class="team-view">
      <!-- 团队概览 -->
      <el-row :gutter="20" class="overview-row">
        <el-col :span="6">
          <el-card class="metric-card">
            <div class="metric-content">
              <div class="metric-value">{{ teamData.averageScore }}</div>
              <div class="metric-label">团队平均分</div>
              <el-progress :percentage="teamData.averageScore"  />
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="metric-card">
            <div class="metric-content">
              <div class="metric-value">{{ teamData.completionRate }}%</div>
              <div class="metric-label">考核完成率</div>
              <el-progress 
                :percentage="teamData.completionRate" 
                :color="getCompletionColor"
               />
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="metric-card">
            <div class="metric-content">
              <div class="metric-value">{{ teamData.memberCount }}</div>
              <div class="metric-label">团队人数</div>
              <div class="member-icons">
                <el-avatar 
                  v-for="i in Math.min(5, teamData.memberCount)" 
                  :key="i"
                  size="small"
                  style="margin-left: -10px"
                >
                  {{ i }}
                </el-avatar>
                <span v-if="teamData.memberCount > 5">+{{ teamData.memberCount - 5 }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="metric-card">
            <div class="metric-content">
              <div class="metric-value">{{ teamData.excellentRate }}%</div>
              <div class="metric-label">优秀率</div>
              <div class="grade-distribution">
                <el-tag type="success" size="small">A级: {{ teamData.gradeDistribution.A }}人</el-tag>
                <el-tag type="primary" size="small">B级: {{ teamData.gradeDistribution.B }}人</el-tag>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 等级分布和成员绩效 -->
      <el-row :gutter="20" class="chart-row">
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>等级分布</span>
            </template>
            <div id="gradeDistributionChart" style="height: 300px"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>成员绩效对比</span>
            </template>
            <div id="memberComparisonChart" style="height: 300px"></div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 团队成员列表 -->
      <el-card class="member-list-card">
        <template #header>
          <div class="list-header">
            <span>团队成员绩效</span>
            <div>
              <el-button size="small" @click="sortBy('score')">按分数排序</el-button>
              <el-button size="small" @click="sortBy('trend')">按趋势排序</el-button>
            </div>
          </div>
        </template>
        <el-table :data="teamMembers" stripe>
          <el-table-column prop="name" label="姓名" width="120"  />
          <el-table-column prop="position" label="岗位" width="150"  />
          <el-table-column prop="score" label="绩效分数" width="120" sortable>
            <template #default="{ row }">
              <span class="score-value">{{ row.score }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="grade" label="等级" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getGradeType(row.grade)">{{ row.grade }}级</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="趋势" width="120" align="center">
            <template #default="{ row }">
              <div class="trend-indicator">
                <el-icon :color="getTrendColor(row.trend)">
                  <ArrowUp v-if="row.trend > 0" />
                  <ArrowDown v-else-if="row.trend < 0" />
                  <Minus v-else />
                </el-icon>
                <span>{{ Math.abs(row.trend) }}%</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="目标完成" width="150">
            <template #default="{ row }">
              <el-progress :percentage="row.completion" :color="getCompletionColor"  />
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="row.needsImprovement ? 'warning' : 'success'" size="small">
                {{ row.needsImprovement ? '需改进' : '正常' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="{ row }">
              <el-button link type="primary" size="small" @click="viewMemberDetail(row)">
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 部门视角 -->
    <div v-if="viewMode === 'department'" class="department-view">
      <!-- 部门概览 -->
      <el-row :gutter="20" class="overview-row">
        <el-col :span="6">
          <el-card class="metric-card">
            <div class="metric-content">
              <div class="metric-value">{{ departmentData.averageScore }}</div>
              <div class="metric-label">部门平均分</div>
              <el-progress :percentage="departmentData.averageScore"  />
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="metric-card">
            <div class="metric-content">
              <div class="metric-value">{{ departmentData.ranking }}/{{ departmentData.totalDepartments }}</div>
              <div class="metric-label">公司排名</div>
              <el-tag :type="getDepartmentRankType(departmentData.ranking)">
                {{ getDepartmentRankLabel(departmentData.ranking) }}
              </el-tag>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="metric-card">
            <div class="metric-content">
              <div class="metric-value">{{ departmentData.totalMembers }}</div>
              <div class="metric-label">部门人数</div>
              <div class="sub-info">
                <span>在职: {{ departmentData.activeMembers }}</span>
                <el-divider direction="vertical"   />
                <span>新员工: {{ departmentData.newMembers }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="metric-card">
            <div class="metric-content">
              <div class="metric-value">{{ departmentData.targetCompletion }}%</div>
              <div class="metric-label">部门目标达成</div>
              <el-progress 
                type="dashboard" 
                :percentage="departmentData.targetCompletion" 
                :width="80"
               />
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 部门对比和趋势 -->
      <el-row :gutter="20" class="chart-row">
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>部门绩效对比</span>
            </template>
            <div id="departmentComparisonChart" style="height: 350px"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>部门绩效趋势</span>
            </template>
            <div id="departmentTrendChart" style="height: 350px"></div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 团队绩效分布 -->
      <el-card>
        <template #header>
          <span>各团队绩效分布</span>
        </template>
        <div id="teamDistributionChart" style="height: 400px"></div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Refresh, 
  ArrowUp, 
  ArrowDown, 
  Minus, 
  CircleCheck, 
  Warning 
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import type { PerformanceDashboard } from '@/types/performance'

// 视图模式
const viewMode = ref<'personal' | 'team' | 'department'>('personal')
const selectedPeriod = ref('2025')

// 个人数据
const personalData = reactive({
  currentScore: 88.5,
  currentGrade: 'B',
  gradeStars: 4,
  trend: 5.2,
  departmentRank: 12,
  departmentTotal: 45,
  completionRate: 92,
  strengths: ['技术能力强', '学习积极性高', '团队协作好', '创新意识强'],
  weaknesses: ['时间管理需加强', '沟通表达待提升', '风险意识不足']
})

// 团队数据
const teamData = reactive({
  averageScore: 85.3,
  completionRate: 87,
  memberCount: 12,
  excellentRate: 25,
  gradeDistribution: {
    A: 3,
    B: 5,
    C: 3,
    D: 1,
    E: 0
  }
})

// 部门数据
const departmentData = reactive({
  averageScore: 84.2,
  ranking: 3,
  totalDepartments: 8,
  totalMembers: 45,
  activeMembers: 42,
  newMembers: 5,
  targetCompletion: 89
})

// 团队成员数据
const teamMembers = ref([
  {
    id: '1',
    name: 'HrHr张三',
    position: '前端工程师',
    score: 92,
    grade: 'A',
    trend: 8.5,
    completion: 95,
    needsImprovement: false
  },
  {
    id: '2',
    name: '李四',
    position: '后端工程师',
    score: 88,
    grade: 'B',
    trend: -2.3,
    completion: 88,
    needsImprovement: false
  },
  {
    id: '3',
    name: '王五',
    position: '测试工程师',
    score: 78,
    grade: 'C',
    trend: 3.1,
    completion: 75,
    needsImprovement: true
  }
])

// 颜色配置
const getRankColor = computed(() => {
  const percentage = (1 - personalData.departmentRank / personalData.departmentTotal) * 100
  if (percentage >= 80) return '#67c23a'
  if (percentage >= 60) return '#409eff'
  if (percentage >= 40) return '#e6a23c'
  return '#f56c6c'
})

const getCompletionColor = (percentage: number) => {
  if (percentage >= 90) return '#67c23a'
  if (percentage >= 70) return '#409eff'
  if (percentage >= 50) return '#e6a23c'
  return '#f56c6c'
}

// 工具函数
const getGradeType = (grade: string) => {
  const map: Record<string, string> = {
    A: 'success',
    B: 'primary',
    C: '',
    D: 'warning',
    E: 'danger'
  }
  return map[grade] || 'info'
}

const getTrendColor = (trend: number) => {
  if (trend > 0) return '#67c23a'
  if (trend < 0) return '#f56c6c'
  return '#909399'
}

const getDepartmentRankType = (rank: number) => {
  if (rank <= 3) return 'success'
  if (rank <= 5) return 'primary'
  return 'warning'
}

const getDepartmentRankLabel = (rank: number) => {
  if (rank === 1) return '第一名'
  if (rank === 2) return '第二名'
  if (rank === 3) return '第三名'
  return `第${rank}名`
}

// 处理函数
const handlePeriodChange = () => {
  refreshData()
}

const handleViewModeChange = () => {
  setTimeout(() => {
    initCharts()
  }, 100)
}

const refreshData = () => {
  ElMessage.success('数据已刷新')
  initCharts()
}

const sortBy = (field: string) => {
  teamMembers.value.sort((a, b) => {
    if (field === 'score') return b.score - a.score
    if (field === 'trend') return b.trend - a.trend
    return 0
  })
}

   
const viewMemberDetail = (member: unknown) => {
  ElMessage.info(`查看${member.name}的绩效详情`)
}

// 初始化图表
const initCharts = () => {
  if (viewMode.value === 'personal') {
    initPersonalTrendChart()
    initPersonalRadarChart()
  } else if (viewMode.value === 'team') {
    initGradeDistributionChart()
    initMemberComparisonChart()
  } else if (viewMode.value === 'department') {
    initDepartmentComparisonChart()
    initDepartmentTrendChart()
    initTeamDistributionChart()
  }
}

// 个人绩效趋势图
const initPersonalTrendChart = () => {
  const chartDom = document.getElementById('personalTrendChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['2024Q1', '2024Q2', '2024Q3', '2024Q4', '2025Q1']
    },
    yAxis: {
      type: 'value',
      min: 70,
      max: 100
    },
    series: [
      {
        name: '绩效分数',
        type: 'line',
        smooth: true,
        data: [82, 84, 85, 84, 88.5],
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
            { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
          ])
        },
        itemStyle: {
          color: '#409eff'
        },
        markLine: {
          data: [
            { type: 'average', name: '平均值' }
          ]
        }
      }
    ]
  }
  
  myChart.setOption(option)
}

// 个人能力雷达图
const initPersonalRadarChart = () => {
  const chartDom = document.getElementById('personalRadarChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  
  const option = {
    tooltip: {},
    radar: {
      indicator: [
        { name: '工作业绩', max: 100 },
        { name: '工作能力', max: 100 },
        { name: '工作态度', max: 100 },
        { name: '团队协作', max: 100 },
        { name: '创新能力', max: 100 }
      ]
    },
    series: [
      {
        type: 'radar',
        data: [
          {
            value: [90, 85, 88, 92, 86],
            name: '当前能力',
            areaStyle: {
              color: 'rgba(64, 158, 255, 0.3)'
            },
            itemStyle: {
              color: '#409eff'
            }
          },
          {
            value: [85, 85, 85, 85, 85],
            name: '部门平均',
            lineStyle: {
              type: 'dashed'
            },
            itemStyle: {
              color: '#909399'
            }
          }
        ]
      }
    ]
  }
  
  myChart.setOption(option)
}

// 等级分布图
const initGradeDistributionChart = () => {
  const chartDom = document.getElementById('gradeDistributionChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  
  const option = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          formatter: '{b}: {c}人\n({d}%)'
        },
        data: [
          { value: 3, name: 'A级', itemStyle: { color: '#67c23a' } },
          { value: 5, name: 'B级', itemStyle: { color: '#409eff' } },
          { value: 3, name: 'C级', itemStyle: { color: '#e6a23c' } },
          { value: 1, name: 'D级', itemStyle: { color: '#f56c6c' } }
        ]
      }
    ]
  }
  
  myChart.setOption(option)
}

// 成员绩效对比图
const initMemberComparisonChart = () => {
  const chartDom = document.getElementById('memberComparisonChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: teamMembers.value.map(m => m.name)
    },
    yAxis: {
      type: 'value',
      min: 60,
      max: 100
    },
    series: [
      {
        name: '绩效分数',
        type: 'bar',
        data: teamMembers.value.map(m => ({
          value: m.score,
          itemStyle: {
            color: m.score >= 90 ? '#67c23a' : 
                   m.score >= 80 ? '#409eff' : 
                   m.score >= 70 ? '#e6a23c' : '#f56c6c'
          }
        })),
        markLine: {
          data: [
            { type: 'average', name: '平均值' }
          ]
        }
      }
    ]
  }
  
  myChart.setOption(option)
}

// 部门对比图
const initDepartmentComparisonChart = () => {
  const chartDom = document.getElementById('departmentComparisonChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      min: 75,
      max: 90
    },
    yAxis: {
      type: 'category',
      data: ['财务部', '人力资源部', '技术部', '产品部', '市场部', '运营部', '销售部', '客服部']
    },
    series: [
      {
        type: 'bar',
        data: [82.5, 83.1, 84.2, 85.3, 86.2, 81.5, 87.8, 80.3],
        itemStyle: {
   
          color: function(params: unknown) {
            return params.dataIndex === 2 ? '#409eff' : '#ddd'
          }
        },
        label: {
          show: true,
          position: 'right',
          formatter: '{c}分'
        }
      }
    ]
  }
  
  myChart.setOption(option)
}

// 部门趋势图
const initDepartmentTrendChart = () => {
  const chartDom = document.getElementById('departmentTrendChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['技术部', '公司平均']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value',
      min: 80,
      max: 90
    },
    series: [
      {
        name: '技术部',
        type: 'line',
        data: [82.1, 82.8, 83.5, 83.2, 84.0, 84.2],
        smooth: true,
        itemStyle: { color: '#409eff' }
      },
      {
        name: '公司平均',
        type: 'line',
        data: [81.5, 82.0, 82.3, 82.8, 83.2, 83.5],
        smooth: true,
        lineStyle: { type: 'dashed' },
        itemStyle: { color: '#909399' }
      }
    ]
  }
  
  myChart.setOption(option)
}

// 团队分布图
const initTeamDistributionChart = () => {
  const chartDom = document.getElementById('teamDistributionChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  
  const option = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        type: 'treemap',
        data: [
          {
            name: '前端团队\n平均: 86.5',
            value: 8,
            itemStyle: { color: '#409eff' },
            children: [
              { name: 'A级: 2人', value: 2 },
              { name: 'B级: 4人', value: 4 },
              { name: 'C级: 2人', value: 2 }
            ]
          },
          {
            name: '后端团队\n平均: 84.2',
            value: 10,
            itemStyle: { color: '#67c23a' },
            children: [
              { name: 'A级: 3人', value: 3 },
              { name: 'B级: 5人', value: 5 },
              { name: 'C级: 2人', value: 2 }
            ]
          },
          {
            name: '测试团队\n平均: 82.8',
            value: 6,
            itemStyle: { color: '#e6a23c' },
            children: [
              { name: 'A级: 1人', value: 1 },
              { name: 'B级: 3人', value: 3 },
              { name: 'C级: 2人', value: 2 }
            ]
          },
          {
            name: '产品团队\n平均: 85.0',
            value: 5,
            itemStyle: { color: '#f56c6c' },
            children: [
              { name: 'A级: 1人', value: 1 },
              { name: 'B级: 3人', value: 3 },
              { name: 'C级: 1人', value: 1 }
            ]
          }
        ]
      }
    ]
  }
  
  myChart.setOption(option)
}

// 初始化
onMounted(() => {
  initCharts()
  
  // 响应式处理
  window.addEventListener('resize', () => {
    const charts = [
      'personalTrendChart',
      'personalRadarChart',
      'gradeDistributionChart',
      'memberComparisonChart',
      'departmentComparisonChart',
      'departmentTrendChart',
      'teamDistributionChart'
    ]
    
    charts.forEach(id => {
      const chartDom = document.getElementById(id)
      if (chartDom) {
        const chart = echarts.getInstanceByDom(chartDom)
        chart?.resize()
      }
    })
  })
})
</script>

<style lang="scss" scoped>
.performance-dashboard {
  padding: 20px;
  
  .dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      font-size: 24px;
      color: #303133;
    }
    
    .header-controls {
      display: flex;
      gap: 15px;
      align-items: center;
    }
  }
  
  .overview-row {
    margin-bottom: 20px;
    
    .metric-card {
      height: 100%;
      
      .metric-content {
        text-align: center;
        padding: 10px;
        
        .metric-value {
          font-size: 36px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 10px;
        }
        
        .metric-label {
          color: #909399;
          font-size: 14px;
          margin-bottom: 10px;
        }
        
        .metric-trend {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 5px;
          font-size: 14px;
        }
        
        .sub-info {
          font-size: 12px;
          color: #606266;
          margin-top: 10px;
        }
        
        .member-icons {
          display: flex;
          justify-content: center;
          align-items: center;
          margin-top: 10px;
        }
        
        .grade-distribution {
          margin-top: 10px;
          
          .el-tag {
            margin: 2px;
          }
        }
      }
    }
  }
  
  .chart-row {
    margin-bottom: 20px;
  }
  
  .feedback-row {
    margin-bottom: 20px;
    
    .feedback-card {
      :deep(.el-card__header) {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
  }
  
  .member-list-card {
    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .score-value {
      font-weight: bold;
      color: #409eff;
    }
    
    .trend-indicator {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 5px;
    }
  }
}
</style>