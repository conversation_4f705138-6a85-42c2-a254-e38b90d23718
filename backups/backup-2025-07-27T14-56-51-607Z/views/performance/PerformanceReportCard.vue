<template>
  <div class="performance-report-card">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>绩效成绩单</h3>
          <div class="header-actions">
            <el-button @click="handlePreview">预览</el-button>
            <el-button type="primary" @click="handleGenerate">生成报告</el-button>
          </div>
        </div>
      </template>

      <!-- 筛选区域 -->
      <div class="filter-section">
        <el-form :model="filterForm" :inline="true">
          <el-form-item label="考核周期">
            <el-date-picker
              v-model="filterForm.assessmentPeriod"
              type="month"
              placeholder="选择考核周期"
              @change="handleFilterChange"
             />
          </el-form-item>
          <el-form-item label="部门">
            <el-select
              v-model="filterForm.department"
              placeholder="请选择部门"
              @change="handleFilterChange"
              clearable
            >
              <el-option label="全部部门" value=""  />
              <el-option
                v-for="dept in departments"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
               />
            </el-select>
          </el-form-item>
          <el-form-item label="员工">
            <el-select
              v-model="filterForm.employee"
              placeholder="请选择员工"
              @change="handleFilterChange"
              filterable
            >
              <el-option
                v-for="emp in employees"
                :key="emp.id"
                :label="emp.name"
                :value="emp.id"
               />
            </el-select>
          </el-form-item>
          <el-form-item label="报告格式">
            <el-select v-model="filterForm.format">
              <el-option label="PDF" value="pdf"  />
              <el-option label="Word" value="word"  />
              <el-option label="Excel" value="excel"  />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 员工列表 -->
      <div class="employee-list-section">
        <el-table
          ref="tableRef"
          :data="employeeList"
          @selection-change="handleSelectionChange"
          v-loading="loading"
        >
          <el-table-column type="selection" width="55"  />
          <el-table-column prop="employeeName" label="员工姓名"  />
          <el-table-column prop="employeeId" label="员工编号"  />
          <el-table-column prop="department" label="部门"  />
          <el-table-column prop="position" label="岗位"  />
          <el-table-column prop="finalScore" label="最终得分" align="center">
            <template #default="scope">
              <el-tag :type="getScoreType(scope.row.finalScore)">
                {{ scope.row.finalScore }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="finalGrade" label="最终等级" align="center">
            <template #default="scope">
              <el-tag :type="getGradeType(scope.row.finalGrade)">
                {{ scope.row.finalGrade }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" align="center">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button
                type="text"
                @click="handleViewReport(scope.row)"
              >
                查看详情
              </el-button>
              <el-button
                type="text"
                @click="handleGenerateSingle(scope.row)"
              >
                生成成绩单
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
           />
        </div>
      </div>
    </el-card>

    <!-- 报告预览对话框 -->
    <el-dialog
      v-model="previewDialog.visible"
      title="绩效成绩单预览"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="report-preview" v-if="previewDialog.data">
        <!-- 报告头部 -->
        <div class="report-header">
          <div class="company-logo">
            <img src="/logo.png" alt="公司Logo" style="height: 40px;" />
          </div>
          <div class="report-title">
            <h2>绩效考核成绩单</h2>
            <p>{{ previewDialog.data.assessmentPeriod }}</p>
          </div>
          <div class="report-date">
            <p>生成日期：{{ formatDate(new Date()) }}</p>
          </div>
        </div>

        <!-- 员工基本信息 -->
        <div class="employee-info-section">
          <h3>员工基本信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="姓名">
              {{ previewDialog.data.employeeName }}
            </el-descriptions-item>
            <el-descriptions-item label="员工编号">
              {{ previewDialog.data.employeeId }}
            </el-descriptions-item>
            <el-descriptions-item label="部门">
              {{ previewDialog.data.department }}
            </el-descriptions-item>
            <el-descriptions-item label="岗位">
              {{ previewDialog.data.position }}
            </el-descriptions-item>
            <el-descriptions-item label="直接上级">
              {{ previewDialog.data.supervisor }}
            </el-descriptions-item>
            <el-descriptions-item label="考核周期">
              {{ previewDialog.data.assessmentPeriod }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 绩效结果概览 -->
        <div class="performance-summary-section">
          <h3>绩效结果概览</h3>
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="summary-item">
                <div class="summary-value final-score">{{ previewDialog.data.finalScore }}</div>
                <div class="summary-label">最终得分</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item">
                <div class="summary-value final-grade">{{ previewDialog.data.finalGrade }}</div>
                <div class="summary-label">最终等级</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item">
                <div class="summary-value rank">{{ previewDialog.data.departmentRank }}</div>
                <div class="summary-label">部门排名</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item">
                <div class="summary-value rank">{{ previewDialog.data.companyRank }}</div>
                <div class="summary-label">公司排名</div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 详细评分 -->
        <div class="detailed-scores-section">
          <h3>详细评分</h3>
          <el-table :data="previewDialog.data.detailedScores" border>
            <el-table-column prop="category" label="评价类别"  />
            <el-table-column prop="weight" label="权重" align="center">
              <template #default="scope">
                {{ scope.row.weight }}%
              </template>
            </el-table-column>
            <el-table-column prop="selfScore" label="自评分数" align="center"  />
            <el-table-column prop="supervisorScore" label="上级评分" align="center"  />
            <el-table-column prop="peerScore" label="同事评分" align="center"  />
            <el-table-column prop="finalScore" label="最终得分" align="center">
              <template #default="scope">
                <strong>{{ scope.row.finalScore }}</strong>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 能力雷达图 -->
        <div class="ability-radar-section">
          <h3>能力雷达图</h3>
          <div ref="abilityRadarRef" style="height: 300px;"></div>
        </div>

        <!-- 评价意见 -->
        <div class="evaluation-comments-section">
          <h3>评价意见</h3>
          <div class="comments-list">
            <div
              v-for="comment in previewDialog.data.evaluationComments"
              :key="comment.id"
              class="comment-item"
            >
              <div class="comment-header">
                <span class="evaluator">{{ comment.evaluatorName }}</span>
                <span class="role">{{ comment.evaluatorRole }}</span>
                <span class="date">{{ comment.evaluateDate }}</span>
              </div>
              <div class="comment-content">{{ comment.comments }}</div>
            </div>
          </div>
        </div>

        <!-- 改进建议 -->
        <div class="improvement-suggestions-section">
          <h3>改进建议</h3>
          <div class="suggestions-content">
            <h4>优势</h4>
            <ul>
              <li v-for="strength in previewDialog.data.strengths" :key="strength">
                {{ strength }}
              </li>
            </ul>
            <h4>待改进方面</h4>
            <ul>
              <li v-for="improvement in previewDialog.data.improvements" :key="improvement">
                {{ improvement }}
              </li>
            </ul>
            <h4>发展建议</h4>
            <ul>
              <li v-for="suggestion in previewDialog.data.suggestions" :key="suggestion">
                {{ suggestion }}
              </li>
            </ul>
          </div>
        </div>

        <!-- 签名区域 -->
        <div class="signature-section">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="signature-box">
                <p>员工签名：</p>
                <div class="signature-line"></div>
                <p>日期：</p>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="signature-box">
                <p>直接上级签名：</p>
                <div class="signature-line"></div>
                <p>日期：</p>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="signature-box">
                <p>HR签名：</p>
                <div class="signature-line"></div>
                <p>日期：</p>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <template #footer>
        <el-button @click="previewDialog.visible = false">关闭</el-button>
        <el-button type="primary" @click="handleDownloadReport">
          下载报告
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'

// 接口定义
interface EmployeeReportData {
  id: string
  employeeName: string
  employeeId: string
  department: string
  position: string
  supervisor: string
  assessmentPeriod: string
  finalScore: number
  finalGrade: string
  departmentRank: number
  companyRank: number
  status: string
  detailedScores: Array<{
    category: string
    weight: number
    selfScore: number
    supervisorScore: number
    peerScore: number
    finalScore: number
  }>
  evaluationComments: Array<{
    id: string
    evaluatorName: string
    evaluatorRole: string
    evaluateDate: string
    comments: string
  }>
  strengths: string[]
  improvements: string[]
  suggestions: string[]
}

// 响应式数据
const loading = ref(false)
const selectedEmployees = ref<EmployeeReportData[]>([])

const departments = ref([
  { id: '1', name: 'HrHr技术部' },
  { id: '2', name: '销售部' },
  { id: '3', name: '市场部' },
  { id: '4', name: '人事部' }
])

const employees = ref([
  { id: '1', name: '张三' },
  { id: '2', name: '李四' },
  { id: '3', name: '王五' },
  { id: '4', name: '赵六' }
])

const filterForm = reactive({
  assessmentPeriod: '2024-12',
  department: '',
  employee: '',
  format: 'pdf'
})

const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

const employeeList = ref<EmployeeReportData[]>([])

const previewDialog = reactive({
  visible: false,
  data: null as EmployeeReportData | null
})

const abilityRadarRef = ref<HTMLElement>()

// 方法
const handleFilterChange = () => {
  loadEmployeeList()
}

const handleSelectionChange = (selection: EmployeeReportData[]) => {
  selectedEmployees.value = selection
}

const handlePreview = () => {
  if (selectedEmployees.value.length === 0) {
    ElMessage.warning('请先选择员工')
    return
  }
  
  // 预览第一个选中的员工
  handleViewReport(selectedEmployees.value[0])
}

const handleGenerate = async () => {
  if (selectedEmployees.value.length === 0) {
    ElMessage.warning('请先选择员工')
    return
  }
  
  try {
    loading.value = true
    
    // 模拟批量生成
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success(`成功生成${selectedEmployees.value.length}份绩效成绩单`)
  } catch (__error) {
    ElMessage.error('生成失败')
  } finally {
    loading.value = false
  }
}

const handleViewReport = (row: EmployeeReportData) => {
  previewDialog.data = {
    ...row,
    detailedScores: [
      { category: '工作质量', weight: 30, selfScore: 85, supervisorScore: 88, peerScore: 86, finalScore: 87 },
      { category: '工作效率', weight: 25, selfScore: 82, supervisorScore: 85, peerScore: 83, finalScore: 84 },
      { category: '团队协作', weight: 20, selfScore: 90, supervisorScore: 88, peerScore: 92, finalScore: 90 },
      { category: '创新能力', weight: 15, selfScore: 80, supervisorScore: 82, peerScore: 81, finalScore: 81 },
      { category: '学习成长', weight: 10, selfScore: 88, supervisorScore: 85, peerScore: 87, finalScore: 87 }
    ],
    evaluationComments: [
      {
        id: '1',
        evaluatorName: '李经理',
        evaluatorRole: '直接上级',
        evaluateDate: '2024-12-20',
        comments: '该员工在本考核周期内表现优秀，工作认真负责，具有良好的团队协作精神。'
      },
      {
        id: '2',
        evaluatorName: '王主管',
        evaluatorRole: '项目负责人',
        evaluateDate: '2024-12-18',
        comments: '技术能力强，能够独立完成复杂任务，建议在领导能力方面进一步提升。'
      }
    ],
    strengths: [
      '专业技能扎实，能够熟练运用各种技术工具',
      '工作态度积极主动，责任心强',
      '团队协作能力突出，善于沟通'
    ],
    improvements: [
      '时间管理能力有待提高',
      '在处理紧急任务时容易焦虑',
      '需要加强业务知识的学习'
    ],
    suggestions: [
      '参加时间管理相关培训',
      '多承担跨部门协作项目',
      '定期参与业务知识分享会',
      '建议申请高级职位发展通道'
    ]
  }
  
  previewDialog.visible = true
  
  nextTick(() => {
    initAbilityRadarChart()
  })
}

const handleGenerateSingle = async (row: EmployeeReportData) => {
  try {
    loading.value = true
    
    // 模拟单个生成
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success(`${row.employeeName}的绩效成绩单生成成功`)
  } catch (__error) {
    ElMessage.error('生成失败')
  } finally {
    loading.value = false
  }
}

const handleDownloadReport = () => {
  // 模拟下载
  ElMessage.success('报告下载中...')
}

const loadEmployeeList = async () => {
  try {
    loading.value = true
    
    // 模拟数据加载
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    employeeList.value = [
      {
        id: '1',
        employeeName: '张三',
        employeeId: 'EMP001',
        department: '技术部',
        position: '前端工程师',
        supervisor: '李经理',
        assessmentPeriod: '2024-12',
        finalScore: 87,
        finalGrade: 'B',
        departmentRank: 3,
        companyRank: 15,
        status: 'completed',
        detailedScores: [],
        evaluationComments: [],
        strengths: [],
        improvements: [],
        suggestions: []
      },
      {
        id: '2',
        employeeName: '李四',
        employeeId: 'EMP002',
        department: '销售部',
        position: '销售经理',
        supervisor: '王总监',
        assessmentPeriod: '2024-12',
        finalScore: 92,
        finalGrade: 'A',
        departmentRank: 1,
        companyRank: 5,
        status: 'completed',
        detailedScores: [],
        evaluationComments: [],
        strengths: [],
        improvements: [],
        suggestions: []
      },
      {
        id: '3',
        employeeName: '王五',
        employeeId: 'EMP003',
        department: '市场部',
        position: '市场专员',
        supervisor: '陈经理',
        assessmentPeriod: '2024-12',
        finalScore: 78,
        finalGrade: 'C',
        departmentRank: 8,
        companyRank: 45,
        status: 'pending',
        detailedScores: [],
        evaluationComments: [],
        strengths: [],
        improvements: [],
        suggestions: []
      }
    ]
    
    pagination.total = employeeList.value.length
  } catch (__error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  loadEmployeeList()
}

const handleCurrentChange = (current: number) => {
  pagination.current = current
  loadEmployeeList()
}

const initAbilityRadarChart = () => {
  if (!abilityRadarRef.value || !previewDialog.data) return
  
  const chart = echarts.init(abilityRadarRef.value)
  
  const option = {
    title: {
      text: '能力评价雷达图'
    },
    tooltip: {},
    radar: {
      indicator: [
        { name: '工作质量', max: 100 },
        { name: '工作效率', max: 100 },
        { name: '团队协作', max: 100 },
        { name: '创新能力', max: 100 },
        { name: '学习成长', max: 100 }
      ]
    },
    series: [
      {
        name: '能力评价',
        type: 'radar',
        data: [
          {
            value: [87, 84, 90, 81, 87],
            name: '最终得分',
            itemStyle: {
              color: '#409eff'
            },
            areaStyle: {
              color: 'rgba(64, 158, 255, 0.3)'
            }
          },
          {
            value: [85, 82, 90, 80, 88],
            name: '自评分数',
            itemStyle: {
              color: '#67c23a'
            },
            lineStyle: {
              type: 'dashed'
            }
          }
        ]
      }
    ]
  }
  
  chart.setOption(option)
}

const formatDate = (date: Date) => {
  return date.toLocaleDateString('zh-CN')
}

const getScoreType = (score: number) => {
  if (score >= 90) return 'success'
  if (score >= 80) return 'primary'
  if (score >= 70) return 'warning'
  return 'danger'
}

const getGradeType = (grade: string) => {
  const gradeTypes = { A: 'success', B: 'primary', C: 'warning', D: 'danger' }
  return gradeTypes[grade] || 'info'
}

const getStatusType = (status: string) => {
  const statusMap = {
    completed: 'success',
    pending: 'warning',
    draft: 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap = {
    completed: '已完成',
    pending: '待确认',
    draft: '草稿'
  }
  return statusMap[status] || '未知'
}

// 生命周期
onMounted(() => {
  loadEmployeeList()
})
</script>

<style scoped>
.performance-report-card {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.employee-list-section {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.report-preview {
  padding: 30px;
  background-color: #fff;
  color: #333;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20px;
  border-bottom: 2px solid #409eff;
  margin-bottom: 30px;
}

.report-title {
  text-align: center;
  flex: 1;
}

.report-title h2 {
  margin: 0;
  color: #409eff;
}

.report-title p {
  margin: 5px 0 0;
  color: #666;
}

.report-date {
  text-align: right;
}

.employee-info-section,
.performance-summary-section,
.detailed-scores-section,
.ability-radar-section,
.evaluation-comments-section,
.improvement-suggestions-section,
.signature-section {
  margin-bottom: 30px;
}

.employee-info-section h3,
.performance-summary-section h3,
.detailed-scores-section h3,
.ability-radar-section h3,
.evaluation-comments-section h3,
.improvement-suggestions-section h3 {
  color: #409eff;
  border-bottom: 1px solid #ddd;
  padding-bottom: 10px;
  margin-bottom: 20px;
}

.summary-item {
  text-align: center;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

.summary-value {
  font-size: 2em;
  font-weight: bold;
  margin-bottom: 10px;
}

.final-score {
  color: #409eff;
}

.final-grade {
  color: #67c23a;
}

.rank {
  color: #e6a23c;
}

.summary-label {
  color: #666;
  font-size: 14px;
}

.comments-list {
  space-y: 15px;
}

.comment-item {
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
  margin-bottom: 15px;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 12px;
  color: #666;
}

.evaluator {
  font-weight: bold;
  color: #333;
}

.comment-content {
  line-height: 1.6;
}

.suggestions-content h4 {
  color: #409eff;
  margin: 15px 0 10px;
}

.suggestions-content ul {
  margin: 0 0 15px;
  padding-left: 20px;
}

.suggestions-content li {
  margin-bottom: 5px;
  line-height: 1.6;
}

.signature-section {
  margin-top: 50px;
  padding-top: 30px;
  border-top: 1px solid #ddd;
}

.signature-box {
  text-align: center;
}

.signature-line {
  border-bottom: 1px solid #333;
  margin: 20px 0;
  height: 20px;
}
</style>