<template>
  <div class="performance-improvement-plan">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>绩效改进计划</h3>
          <div class="header-actions">
            <el-button @click="handlePreview">预览计划</el-button>
            <el-button type="primary" @click="handleGenerate">生成计划</el-button>
          </div>
        </div>
      </template>

      <!-- 筛选区域 -->
      <div class="filter-section">
        <el-form :model="filterForm" :inline="true">
          <el-form-item label="考核周期">
            <el-date-picker
              v-model="filterForm.assessmentPeriod"
              type="month"
              placeholder="选择考核周期"
              @change="handleFilterChange"
             />
          </el-form-item>
          <el-form-item label="部门">
            <el-select
              v-model="filterForm.department"
              placeholder="请选择部门"
              @change="handleFilterChange"
              clearable
            >
              <el-option label="全部部门" value=""  />
              <el-option
                v-for="dept in departments"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
               />
            </el-select>
          </el-form-item>
          <el-form-item label="绩效等级">
            <el-select
              v-model="filterForm.performanceGrade"
              placeholder="请选择等级"
              @change="handleFilterChange"
              multiple
            >
              <el-option label="D级(待改进)" value="D"  />
              <el-option label="C级(合格)" value="C"  />
              <el-option label="B级(良好)" value="B"  />
            </el-select>
          </el-form-item>
          <el-form-item label="计划类型">
            <el-select v-model="filterForm.planType">
              <el-option label="个人改进计划" value="individual"  />
              <el-option label="团队改进计划" value="team"  />
              <el-option label="部门改进计划" value="department"  />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 员工列表 -->
      <div class="employee-list-section">
        <el-table
          ref="tableRef"
          :data="employeeList"
          @selection-change="handleSelectionChange"
          v-loading="loading"
        >
          <el-table-column type="selection" width="55"  />
          <el-table-column prop="employeeName" label="员工姓名"  />
          <el-table-column prop="employeeId" label="员工编号"  />
          <el-table-column prop="department" label="部门"  />
          <el-table-column prop="position" label="岗位"  />
          <el-table-column prop="currentGrade" label="当前等级" align="center">
            <template #default="scope">
              <el-tag :type="getGradeType(scope.row.currentGrade)">
                {{ scope.row.currentGrade }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="targetGrade" label="目标等级" align="center">
            <template #default="scope">
              <el-tag :type="getGradeType(scope.row.targetGrade)">
                {{ scope.row.targetGrade }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="planStatus" label="计划状态" align="center">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.planStatus)">
                {{ getStatusText(scope.row.planStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="progress" label="完成进度" align="center">
            <template #default="scope">
              <el-progress
                :percentage="scope.row.progress"
                :stroke-width="8"
                :color="getProgressColor(scope.row.progress)"
               />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button
                type="text"
                @click="handleViewPlan(scope.row)"
              >
                查看计划
              </el-button>
              <el-button
                type="text"
                @click="handleEditPlan(scope.row)"
              >
                编辑计划
              </el-button>
              <el-button
                v-if="scope.row.planStatus === 'draft'"
                type="text"
                @click="handleActivatePlan(scope.row)"
              >
                启动计划
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
           />
        </div>
      </div>
    </el-card>

    <!-- 改进计划预览/编辑对话框 -->
    <el-dialog
      v-model="planDialog.visible"
      :title="planDialog.title"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="plan-content" v-if="planDialog.data">
        <!-- 基本信息 -->
        <div class="basic-info-section">
          <h4>基本信息</h4>
          <el-form :model="planDialog.data" :rules="planRules" ref="planFormRef" label-width="120px">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="员工姓名">
                  <el-input v-model="planDialog.data.employeeName" readonly   />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="部门岗位">
                  <el-input :value="`${planDialog.data.department} - ${planDialog.data.position}`" readonly   />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="当前等级">
                  <el-tag :type="getGradeType(planDialog.data.currentGrade)">
                    {{ planDialog.data.currentGrade }}
                  </el-tag>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="目标等级" prop="targetGrade">
                  <el-select 
                    v-model="planDialog.data.targetGrade" 
                    :disabled="planDialog.mode === 'view'"
                  >
                    <el-option
                      v-for="grade in performanceGrades"
                      :key="grade.value"
                      :label="grade.label"
                      :value="grade.value"
                     />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="计划周期" prop="planPeriod">
                  <el-date-picker
                    v-model="planDialog.data.planPeriod"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :disabled="planDialog.mode === 'view'"
                   />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="负责人" prop="supervisor">
                  <el-select 
                    v-model="planDialog.data.supervisor"
                    :disabled="planDialog.mode === 'view'"
                  >
                    <el-option
                      v-for="supervisor in supervisors"
                      :key="supervisor.id"
                      :label="supervisor.name"
                      :value="supervisor.id"
                     />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>

        <!-- 问题分析 -->
        <div class="problem-analysis-section">
          <h4>问题分析</h4>
          <el-form :model="planDialog.data" label-width="120px">
            <el-form-item label="主要问题" prop="mainProblems">
              <el-input
                v-model="planDialog.data.mainProblems"
                type="textarea"
                :rows="3"
                placeholder="请描述当前绩效存在的主要问题"
                :readonly="planDialog.mode === 'view'"
                />
            </el-form-item>
            <el-form-item label="根本原因" prop="rootCauses">
              <el-input
                v-model="planDialog.data.rootCauses"
                type="textarea"
                :rows="3"
                placeholder="请分析问题的根本原因"
                :readonly="planDialog.mode === 'view'"
                />
            </el-form-item>
          </el-form>
        </div>

        <!-- 改进目标 -->
        <div class="improvement-goals-section">
          <h4>改进目标</h4>
          <div class="goals-list">
            <div
              v-for="(goal, index) in planDialog.data.improvementGoals"
              :key="index"
              class="goal-item"
            >
              <el-card shadow="never">
                <div class="goal-header">
                  <span class="goal-title">目标 {{ index + 1 }}</span>
                  <el-button
                    v-if="planDialog.mode === 'edit'"
                    type="text"
                    icon="Delete"
                    @click="removeGoal(index)"
                    />
                </div>
                <el-form :model="goal" label-width="80px">
                  <el-form-item label="目标描述">
                    <el-input
                      v-model="goal.description"
                      placeholder="请描述具体的改进目标"
                      :readonly="planDialog.mode === 'view'"
                      />
                  </el-form-item>
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="目标值">
                        <el-input
                          v-model="goal.targetValue"
                          placeholder="如：85分"
                          :readonly="planDialog.mode === 'view'"
                          />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="完成期限">
                        <el-date-picker
                          v-model="goal.deadline"
                          type="date"
                          placeholder="选择完成日期"
                          :disabled="planDialog.mode === 'view'"
                         />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="权重">
                        <el-input-number
                          v-model="goal.weight"
                          :min="1"
                          :max="100"
                          :disabled="planDialog.mode === 'view'"
                          />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-card>
            </div>
            <el-button
              v-if="planDialog.mode === 'edit'"
              type="dashed"
              @click="addGoal"
              style="width: 100%; margin-top: 10px;"
            >
              + 添加目标
            </el-button>
          </div>
        </div>

        <!-- 改进措施 -->
        <div class="improvement-actions-section">
          <h4>改进措施</h4>
          <div class="actions-list">
            <div
              v-for="(action, index) in planDialog.data.improvementActions"
              :key="index"
              class="action-item"
            >
              <el-card shadow="never">
                <div class="action-header">
                  <span class="action-title">措施 {{ index + 1 }}</span>
                  <el-button
                    v-if="planDialog.mode === 'edit'"
                    type="text"
                    icon="Delete"
                    @click="removeAction(index)"
                    />
                </div>
                <el-form :model="action" label-width="80px">
                  <el-form-item label="措施描述">
                    <el-input
                      v-model="action.description"
                      placeholder="请描述具体的改进措施"
                      :readonly="planDialog.mode === 'view'"
                      />
                  </el-form-item>
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="负责人">
                        <el-select
                          v-model="action.responsible"
                          :disabled="planDialog.mode === 'view'"
                        >
                          <el-option
                            v-for="person in responsiblePersons"
                            :key="person.id"
                            :label="person.name"
                            :value="person.id"
                           />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="完成期限">
                        <el-date-picker
                          v-model="action.deadline"
                          type="date"
                          placeholder="选择完成日期"
                          :disabled="planDialog.mode === 'view'"
                         />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="资源需求">
                        <el-input
                          v-model="action.resources"
                          placeholder="如：培训费用、设备等"
                          :readonly="planDialog.mode === 'view'"
                          />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-form-item label="成功标准">
                    <el-input
                      v-model="action.successCriteria"
                      placeholder="请描述如何判断措施是否成功"
                      :readonly="planDialog.mode === 'view'"
                      />
                  </el-form-item>
                </el-form>
              </el-card>
            </div>
            <el-button
              v-if="planDialog.mode === 'edit'"
              type="dashed"
              @click="addAction"
              style="width: 100%; margin-top: 10px;"
            >
              + 添加措施
            </el-button>
          </div>
        </div>

        <!-- 监控与评估 -->
        <div class="monitoring-section">
          <h4>监控与评估</h4>
          <el-form :model="planDialog.data" label-width="120px">
            <el-form-item label="检查频率">
              <el-select 
                v-model="planDialog.data.checkFrequency"
                :disabled="planDialog.mode === 'view'"
              >
                <el-option label="每周" value="weekly"  />
                <el-option label="每月" value="monthly"  />
                <el-option label="每季度" value="quarterly"  />
              </el-select>
            </el-form-item>
            <el-form-item label="评估方式">
              <el-checkbox-group 
                v-model="planDialog.data.evaluationMethods"
                :disabled="planDialog.mode === 'view'"
              >
                <el-checkbox label="self">自我评估</el-checkbox>
                <el-checkbox label="supervisor">上级评估</el-checkbox>
                <el-checkbox label="peer">同事评估</el-checkbox>
                <el-checkbox label="data">数据指标</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="支持资源">
              <el-input
                v-model="planDialog.data.supportResources"
                type="textarea"
                :rows="2"
                placeholder="请描述公司将提供的支持资源"
                :readonly="planDialog.mode === 'view'"
                />
            </el-form-item>
          </el-form>
        </div>

        <!-- 进度跟踪 -->
        <div v-if="planDialog.data.progressTracking" class="progress-tracking-section">
          <h4>进度跟踪</h4>
          <el-timeline>
            <el-timeline-item
              v-for="record in planDialog.data.progressTracking"
              :key="record.id"
              :timestamp="record.date"
              :type="record.type"
            >
              <el-card>
                <h4>{{ record.title }}</h4>
                <p>{{ record.description }}</p>
                <div v-if="record.attachment" class="attachment">
                  <el-button type="text" icon="Paperclip">
                    {{ record.attachment }}
                  </el-button>
                </div>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>

      <template #footer>
        <el-button @click="planDialog.visible = false">
          {{ planDialog.mode === 'view' ? '关闭' : '取消' }}
        </el-button>
        <el-button
          v-if="planDialog.mode === 'edit'"
          type="primary"
          @click="handleSavePlan"
        >
          保存计划
        </el-button>
        <el-button
          v-if="planDialog.mode === 'view'"
          type="primary"
          @click="handleDownloadPlan"
        >
          下载计划
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 接口定义
interface EmployeeImprovementData {
  id: string
  employeeName: string
  employeeId: string
  department: string
  position: string
  currentGrade: string
  targetGrade: string
  planStatus: string
  progress: number
  planPeriod?: [Date, Date]
  supervisor?: string
  mainProblems?: string
  rootCauses?: string
  improvementGoals?: Array<{
    description: string
    targetValue: string
    deadline: Date
    weight: number
  }>
  improvementActions?: Array<{
    description: string
    responsible: string
    deadline: Date
    resources: string
    successCriteria: string
  }>
  checkFrequency?: string
  evaluationMethods?: string[]
  supportResources?: string
  progressTracking?: Array<{
    id: string
    date: string
    title: string
    description: string
    type: string
    attachment?: string
  }>
}

// 响应式数据
const loading = ref(false)
const selectedEmployees = ref<EmployeeImprovementData[]>([])

const departments = ref([
  { id: '1', name: 'HrHr技术部' },
  { id: '2', name: '销售部' },
  { id: '3', name: '市场部' },
  { id: '4', name: '人事部' }
])

const performanceGrades = ref([
  { value: 'A', label: 'A - 优秀' },
  { value: 'B', label: 'B - 良好' },
  { value: 'C', label: 'C - 合格' },
  { value: 'D', label: 'D - 待改进' }
])

const supervisors = ref([
  { id: '1', name: '李经理' },
  { id: '2', name: '王总监' },
  { id: '3', name: '陈主管' }
])

const responsiblePersons = ref([
  { id: '1', name: '员工本人' },
  { id: '2', name: '直接上级' },
  { id: '3', name: 'HR部门' },
  { id: '4', name: '培训部门' }
])

const filterForm = reactive({
  assessmentPeriod: '2024-12',
  department: '',
  performanceGrade: ['D', 'C'],
  planType: 'individual'
})

const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

const employeeList = ref<EmployeeImprovementData[]>([])

const planDialog = reactive({
  visible: false,
  title: '',
  mode: 'view' as 'view' | 'edit',
  data: null as EmployeeImprovementData | null
})

const planRules = {
  targetGrade: [{ required: true, message: '请选择目标等级', trigger: 'change' }],
  planPeriod: [{ required: true, message: '请选择计划周期', trigger: 'change' }],
  supervisor: [{ required: true, message: '请选择负责人', trigger: 'change' }]
}

const planFormRef = ref()

// 方法
const handleFilterChange = () => {
  loadEmployeeList()
}

const handleSelectionChange = (selection: EmployeeImprovementData[]) => {
  selectedEmployees.value = selection
}

const handlePreview = () => {
  if (selectedEmployees.value.length === 0) {
    ElMessage.warning('请先选择员工')
    return
  }
  
  // 预览第一个选中的员工
  handleViewPlan(selectedEmployees.value[0])
}

const handleGenerate = async () => {
  if (selectedEmployees.value.length === 0) {
    ElMessage.warning('请先选择员工')
    return
  }
  
  try {
    loading.value = true
    
    // 模拟批量生成
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success(`成功为${selectedEmployees.value.length}名员工生成改进计划`)
    loadEmployeeList()
  } catch (__error) {
    ElMessage.error('生成失败')
  } finally {
    loading.value = false
  }
}

const handleViewPlan = (row: EmployeeImprovementData) => {
  planDialog.data = {
    ...row,
    improvementGoals: row.improvementGoals || [
      {
        description: '提升工作质量，减少错误率',
        targetValue: '错误率<5%',
        deadline: new Date('2025-03-31'),
        weight: 40
      },
      {
        description: '提高工作效率，按时完成任务',
        targetValue: '任务完成率>95%',
        deadline: new Date('2025-02-28'),
        weight: 35
      },
      {
        description: '加强团队协作，改善沟通',
        targetValue: '团队评价>85分',
        deadline: new Date('2025-04-30'),
        weight: 25
      }
    ],
    improvementActions: row.improvementActions || [
      {
        description: '参加专业技能培训课程',
        responsible: '1',
        deadline: new Date('2025-02-15'),
        resources: '培训费用5000元',
        successCriteria: '通过培训考试，获得证书'
      },
      {
        description: '建立工作检查清单，每日自检',
        responsible: '1',
        deadline: new Date('2025-01-31'),
        resources: '无',
        successCriteria: '坚持使用30天，错误率明显下降'
      },
      {
        description: '与团队成员建立定期沟通机制',
        responsible: '2',
        deadline: new Date('2025-02-01'),
        resources: '会议室资源',
        successCriteria: '每周至少一次团队会议'
      }
    ],
    progressTracking: [
      {
        id: '1',
        date: '2024-12-25',
        title: '制定改进计划',
        description: '与员工和直接上级共同制定了详细的改进计划',
        type: 'primary'
      },
      {
        id: '2',
        date: '2025-01-05',
        title: '开始执行计划',
        description: '员工开始按照计划执行各项改进措施',
        type: 'success'
      }
    ]
  }
  
  planDialog.title = `${row.employeeName}的绩效改进计划`
  planDialog.mode = 'view'
  planDialog.visible = true
}

const handleEditPlan = (row: EmployeeImprovementData) => {
  planDialog.data = { ...row }
  planDialog.title = `编辑${row.employeeName}的改进计划`
  planDialog.mode = 'edit'
  planDialog.visible = true
}

const handleActivatePlan = async (row: EmployeeImprovementData) => {
  try {
    await ElMessageBox.confirm(
      `确定要启动${row.employeeName}的改进计划吗？`,
      '确认启动',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    loading.value = true
    
    // 模拟启动计划
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('改进计划已启动')
    loadEmployeeList()
  } catch (__error) {
    // 用户取消
  } finally {
    loading.value = false
  }
}

const handleSavePlan = async () => {
  if (!planFormRef.value) return
  
  try {
    await planFormRef.value.validate()
    loading.value = true
    
    // 模拟保存
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('改进计划保存成功')
    planDialog.visible = false
    loadEmployeeList()
  } catch (__error) {
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

const handleDownloadPlan = () => {
  // 模拟下载
  ElMessage.success('改进计划下载中...')
}

const addGoal = () => {
  if (!planDialog.data?.improvementGoals) {
    planDialog.data!.improvementGoals = []
  }
  
  planDialog.data.improvementGoals.push({
    description: '',
    targetValue: '',
    deadline: new Date(),
    weight: 0
  })
}

const removeGoal = (index: number) => {
  planDialog.data?.improvementGoals?.splice(index, 1)
}

const addAction = () => {
  if (!planDialog.data?.improvementActions) {
    planDialog.data!.improvementActions = []
  }
  
  planDialog.data.improvementActions.push({
    description: '',
    responsible: '',
    deadline: new Date(),
    resources: '',
    successCriteria: ''
  })
}

const removeAction = (index: number) => {
  planDialog.data?.improvementActions?.splice(index, 1)
}

const loadEmployeeList = async () => {
  try {
    loading.value = true
    
    // 模拟数据加载
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    employeeList.value = [
      {
        id: '1',
        employeeName: '张三',
        employeeId: 'EMP001',
        department: '技术部',
        position: '前端工程师',
        currentGrade: 'C',
        targetGrade: 'B',
        planStatus: 'active',
        progress: 35,
        planPeriod: [new Date('2024-12-25'), new Date('2025-04-30')],
        supervisor: '1',
        mainProblems: '工作质量不稳定，偶有错误；工作效率有待提高；与团队沟通不够主动',
        rootCauses: '技术基础不够扎实；时间管理能力欠缺；缺乏主动沟通意识',
        checkFrequency: 'monthly',
        evaluationMethods: ['self', 'supervisor', 'data'],
        supportResources: '提供专业培训机会；配置学习资源；安排经验丰富的同事指导'
      },
      {
        id: '2',
        employeeName: '王五',
        employeeId: 'EMP003',
        department: '市场部',
        position: '市场专员',
        currentGrade: 'D',
        targetGrade: 'C',
        planStatus: 'draft',
        progress: 0,
        planPeriod: [new Date('2025-01-01'), new Date('2025-06-30')],
        supervisor: '3',
        mainProblems: '市场分析能力不足；客户沟通技巧需要提升；项目执行力有待加强',
        rootCauses: '缺乏市场分析经验；沟通技巧培训不足；项目管理方法不当',
        checkFrequency: 'weekly',
        evaluationMethods: ['self', 'supervisor', 'peer'],
        supportResources: '市场分析工具培训；沟通技巧培训；项目管理指导'
      },
      {
        id: '3',
        employeeName: '李四',
        employeeId: 'EMP002',
        department: '销售部',
        position: '销售代表',
        currentGrade: 'C',
        targetGrade: 'B',
        planStatus: 'completed',
        progress: 100,
        planPeriod: [new Date('2024-09-01'), new Date('2024-12-31')],
        supervisor: '2',
        mainProblems: '销售技巧需要提升；客户关系维护不够；数据分析能力欠缺',
        rootCauses: '销售培训不够系统；客户管理方法不当；数据分析工具使用不熟练',
        checkFrequency: 'monthly',
        evaluationMethods: ['supervisor', 'data'],
        supportResources: '销售技巧培训；CRM系统培训；数据分析工具使用指导'
      }
    ]
    
    pagination.total = employeeList.value.length
  } catch (__error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  loadEmployeeList()
}

const handleCurrentChange = (current: number) => {
  pagination.current = current
  loadEmployeeList()
}

const getGradeType = (grade: string) => {
  const gradeTypes = { A: 'success', B: 'primary', C: 'warning', D: 'danger' }
  return gradeTypes[grade] || 'info'
}

const getStatusType = (status: string) => {
  const statusMap = {
    draft: 'info',
    active: 'primary',
    paused: 'warning',
    completed: 'success',
    cancelled: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap = {
    draft: '草稿',
    active: '执行中',
    paused: '已暂停',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || '未知'
}

const getProgressColor = (progress: number) => {
  if (progress >= 80) return '#67c23a'
  if (progress >= 60) return '#409eff'
  if (progress >= 40) return '#e6a23c'
  return '#f56c6c'
}

// 生命周期
onMounted(() => {
  loadEmployeeList()
})
</script>

<style scoped>
.performance-improvement-plan {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.employee-list-section {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.plan-content {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.basic-info-section,
.problem-analysis-section,
.improvement-goals-section,
.improvement-actions-section,
.monitoring-section,
.progress-tracking-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.basic-info-section h4,
.problem-analysis-section h4,
.improvement-goals-section h4,
.improvement-actions-section h4,
.monitoring-section h4,
.progress-tracking-section h4 {
  color: #409eff;
  margin-bottom: 20px;
  border-bottom: 2px solid #409eff;
  padding-bottom: 5px;
}

.goals-list,
.actions-list {
  space-y: 15px;
}

.goal-item,
.action-item {
  margin-bottom: 15px;
}

.goal-header,
.action-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.goal-title,
.action-title {
  font-weight: bold;
  color: #333;
}

.attachment {
  margin-top: 10px;
}
</style>