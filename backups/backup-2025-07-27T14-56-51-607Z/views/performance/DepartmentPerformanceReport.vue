<template>
  <div class="department-performance-report">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>部门绩效报表</h3>
          <div class="header-actions">
            <el-button @click="handlePreview">预览报表</el-button>
            <el-button type="primary" @click="handleGenerate">生成报表</el-button>
          </div>
        </div>
      </template>

      <!-- 筛选区域 -->
      <div class="filter-section">
        <el-form :model="filterForm" :inline="true">
          <el-form-item label="考核周期">
            <el-date-picker
              v-model="filterForm.assessmentPeriod"
              type="month"
              placeholder="选择考核周期"
              @change="handleFilterChange"
             />
          </el-form-item>
          <el-form-item label="部门">
            <el-select
              v-model="filterForm.departments"
              placeholder="请选择部门"
              multiple
              @change="handleFilterChange"
              style="width: 250px;"
            >
              <el-option
                v-for="dept in allDepartments"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
               />
            </el-select>
          </el-form-item>
          <el-form-item label="报表类型">
            <el-select v-model="filterForm.reportType" @change="handleFilterChange">
              <el-option label="综合报表" value="comprehensive"  />
              <el-option label="对比报表" value="comparison"  />
              <el-option label="趋势报表" value="trend"  />
              <el-option label="详细报表" value="detailed"  />
            </el-select>
          </el-form-item>
          <el-form-item label="导出格式">
            <el-select v-model="filterForm.format">
              <el-option label="PDF" value="pdf"  />
              <el-option label="Excel" value="excel"  />
              <el-option label="PowerPoint" value="ppt"  />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 部门列表 -->
      <div class="department-list-section">
        <el-table
          ref="tableRef"
          :data="departmentList"
          @selection-change="handleSelectionChange"
          v-loading="loading"
        >
          <el-table-column type="selection" width="55"  />
          <el-table-column prop="rank" label="排名" width="80" align="center">
            <template #default="scope">
              <el-tag :type="getRankType(scope.row.rank)" effect="dark">
                {{ scope.row.rank }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="departmentName" label="部门名称"  />
          <el-table-column prop="managerName" label="部门负责人"  />
          <el-table-column prop="totalEmployees" label="总人数" align="center"  />
          <el-table-column prop="participantCount" label="参评人数" align="center"  />
          <el-table-column prop="averageScore" label="平均分" align="center">
            <template #default="scope">
              <el-tag :type="getScoreType(scope.row.averageScore)">
                {{ scope.row.averageScore?.toFixed(1) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="gradeDistribution" label="等级分布" align="center">
            <template #default="scope">
              <el-tooltip :content="getGradeTooltip(scope.row)" placement="top">
                <div class="grade-bar">
                  <div 
                    class="grade-segment grade-a" 
                    :style="{ width: scope.row.gradeAPercent + '%' }"
                  ></div>
                  <div 
                    class="grade-segment grade-b" 
                    :style="{ width: scope.row.gradeBPercent + '%' }"
                  ></div>
                  <div 
                    class="grade-segment grade-c" 
                    :style="{ width: scope.row.gradeCPercent + '%' }"
                  ></div>
                  <div 
                    class="grade-segment grade-d" 
                    :style="{ width: scope.row.gradeDPercent + '%' }"
                  ></div>
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="improvement" label="环比变化" align="center">
            <template #default="scope">
              <span :class="getImprovementClass(scope.row.improvement)">
                {{ scope.row.improvement > 0 ? '+' : '' }}{{ scope.row.improvement }}%
              </span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150">
            <template #default="scope">
              <el-button
                type="text"
                @click="handleViewDetail(scope.row)"
              >
                查看详情
              </el-button>
              <el-button
                type="text"
                @click="handleGenerateSingle(scope.row)"
              >
                生成报表
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 报表预览对话框 -->
    <el-dialog
      v-model="previewDialog.visible"
      title="部门绩效报表预览"
      width="90%"
      :close-on-click-modal="false"
    >
      <div class="report-preview" v-if="previewDialog.data">
        <!-- 报表头部 -->
        <div class="report-header">
          <div class="company-info">
            <img src="/logo.png" alt="公司Logo" style="height: 50px;" />
            <div class="company-text">
              <h2>杭州科技大学人事综合管理系统</h2>
              <h3>部门绩效分析报表</h3>
            </div>
          </div>
          <div class="report-meta">
            <p>报表周期：{{ previewDialog.data.period }}</p>
            <p>生成时间：{{ formatDateTime(new Date()) }}</p>
            <p>报表类型：{{ getReportTypeText() }}</p>
          </div>
        </div>

        <!-- 执行摘要 -->
        <div class="executive-summary-section">
          <h3>执行摘要</h3>
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="summary-card">
                <div class="summary-icon">📊</div>
                <div class="summary-content">
                  <div class="summary-value">{{ reportSummary.totalDepartments }}</div>
                  <div class="summary-label">参与部门数</div>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-card">
                <div class="summary-icon">👥</div>
                <div class="summary-content">
                  <div class="summary-value">{{ reportSummary.totalParticipants }}</div>
                  <div class="summary-label">参评总人数</div>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-card">
                <div class="summary-icon">⭐</div>
                <div class="summary-content">
                  <div class="summary-value">{{ reportSummary.averageScore }}</div>
                  <div class="summary-label">整体平均分</div>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-card">
                <div class="summary-icon">📈</div>
                <div class="summary-content">
                  <div class="summary-value">{{ reportSummary.improvementRate }}%</div>
                  <div class="summary-label">整体改进率</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 部门排名表 -->
        <div class="ranking-table-section">
          <h3>部门绩效排名</h3>
          <el-table :data="previewDialog.data.departments" border size="small">
            <el-table-column prop="rank" label="排名" width="80" align="center">
              <template #default="scope">
                <el-tag :type="getRankType(scope.row.rank)" effect="dark">
                  第{{ scope.row.rank }}名
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="部门名称"  />
            <el-table-column prop="manager" label="负责人"  />
            <el-table-column prop="participantCount" label="参评人数" align="center"  />
            <el-table-column prop="averageScore" label="平均分" align="center">
              <template #default="scope">
                {{ scope.row.averageScore?.toFixed(1) }}
              </template>
            </el-table-column>
            <el-table-column label="等级分布" align="center">
              <template #default="scope">
                A:{{ scope.row.gradeA }} B:{{ scope.row.gradeB }} C:{{ scope.row.gradeC }} D:{{ scope.row.gradeD }}
              </template>
            </el-table-column>
            <el-table-column prop="improvement" label="环比变化" align="center">
              <template #default="scope">
                <span :class="getImprovementClass(scope.row.improvement)">
                  {{ scope.row.improvement > 0 ? '+' : '' }}{{ scope.row.improvement }}%
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 图表分析 -->
        <div class="charts-section">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="chart-container">
                <h4>部门平均分对比</h4>
                <div ref="scoreComparisonRef" style="height: 300px;"></div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="chart-container">
                <h4>等级分布情况</h4>
                <div ref="gradeDistributionRef" style="height: 300px;"></div>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="12">
              <div class="chart-container">
                <h4>绩效趋势分析</h4>
                <div ref="trendAnalysisRef" style="height: 300px;"></div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="chart-container">
                <h4>部门绩效雷达图</h4>
                <div ref="departmentRadarRef" style="height: 300px;"></div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 部门分析 -->
        <div class="department-analysis-section">
          <h3>部门分析</h3>
          <el-tabs type="border-card">
            <el-tab-pane label="优秀部门">
              <div class="analysis-content">
                <div v-for="dept in excellentDepartments" :key="dept.id" class="dept-analysis-item">
                  <h4>{{ dept.name }} <el-tag type="success">{{ dept.rank }}名</el-tag></h4>
                  <p><strong>主要优势：</strong></p>
                  <ul>
                    <li v-for="strength in dept.strengths" :key="strength">{{ strength }}</li>
                  </ul>
                  <p><strong>成功经验：</strong></p>
                  <ul>
                    <li v-for="experience in dept.experiences" :key="experience">{{ experience }}</li>
                  </ul>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="待改进部门">
              <div class="analysis-content">
                <div v-for="dept in improvementDepartments" :key="dept.id" class="dept-analysis-item">
                  <h4>{{ dept.name }} <el-tag type="warning">{{ dept.rank }}名</el-tag></h4>
                  <p><strong>主要问题：</strong></p>
                  <ul>
                    <li v-for="issue in dept.issues" :key="issue">{{ issue }}</li>
                  </ul>
                  <p><strong>改进建议：</strong></p>
                  <ul>
                    <li v-for="suggestion in dept.suggestions" :key="suggestion">{{ suggestion }}</li>
                  </ul>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>

        <!-- 总结与建议 -->
        <div class="conclusion-section">
          <h3>总结与建议</h3>
          <div class="conclusion-content">
            <div class="conclusion-item">
              <h4>整体评价</h4>
              <p>本期绩效考核整体情况良好，各部门平均分为{{ reportSummary.averageScore }}分，
              相比上期提升了{{ reportSummary.improvementRate }}%。其中{{ reportSummary.excellentCount }}个部门表现优秀，
              {{ reportSummary.improvementCount }}个部门需要重点关注和改进。</p>
            </div>
            <div class="conclusion-item">
              <h4>主要发现</h4>
              <ul>
                <li>技术类部门整体表现优于职能部门</li>
                <li>新员工较多的部门绩效有所下降</li>
                <li>培训投入与部门绩效呈正相关</li>
                <li>跨部门协作较好的部门整体绩效更高</li>
              </ul>
            </div>
            <div class="conclusion-item">
              <h4>管理建议</h4>
              <ul>
                <li>加强优秀部门经验分享，建立最佳实践推广机制</li>
                <li>对落后部门提供专项培训和指导支持</li>
                <li>完善新员工培养体系，缩短适应期</li>
                <li>建立跨部门协作激励机制</li>
                <li>加大培训投入，特别是针对性技能培训</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- 附录 -->
        <div class="appendix-section">
          <h3>附录</h3>
          <div class="appendix-content">
            <h4>数据说明</h4>
            <ul>
              <li>本报表基于{{ previewDialog.data.period }}绩效考核数据生成</li>
              <li>平均分计算方式：部门所有参评员工分数的算术平均值</li>
              <li>环比变化基于上一考核周期数据计算</li>
              <li>等级分布按照公司统一标准执行</li>
            </ul>
            <h4>联系方式</h4>
            <p>如对报表数据有疑问，请联系人事部：内线8888</p>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="previewDialog.visible = false">关闭</el-button>
        <el-button type="primary" @click="handleDownloadReport">
          下载报表
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'

// 接口定义
interface DepartmentReportData {
  id: string
  departmentName: string
  managerName: string
  totalEmployees: number
  participantCount: number
  averageScore: number
  rank: number
  gradeA: number
  gradeB: number
  gradeC: number
  gradeD: number
  gradeAPercent: number
  gradeBPercent: number
  gradeCPercent: number
  gradeDPercent: number
  improvement: number
}

// 响应式数据
const loading = ref(false)
const selectedDepartments = ref<DepartmentReportData[]>([])

const allDepartments = ref([
  { id: '1', name: 'HrHr技术部' },
  { id: '2', name: '销售部' },
  { id: '3', name: '市场部' },
  { id: '4', name: '人事部' },
  { id: '5', name: '财务部' },
  { id: '6', name: '运营部' }
])

const filterForm = reactive({
  assessmentPeriod: '2024-12',
  departments: [],
  reportType: 'comprehensive',
  format: 'pdf'
})

const departmentList = ref<DepartmentReportData[]>([])

const previewDialog = reactive({
  visible: false,
  data: null as unknown
})

const reportSummary = ref({
  totalDepartments: 6,
  totalParticipants: 120,
  averageScore: 82.3,
  improvementRate: 3.5,
  excellentCount: 2,
  improvementCount: 1
})

const excellentDepartments = ref([
  {
    id: '1',
    name: '技术部',
    rank: 1,
    strengths: [
      '团队技术能力强，创新氛围浓厚',
      '项目交付质量高，客户满意度高',
      '员工学习积极性强，技能提升快'
    ],
    experiences: [
      '建立了完善的技术分享制度',
      '实施敏捷开发流程，提高效率',
      '注重员工个人发展规划'
    ]
  },
  {
    id: '4',
    name: '人事部',
    rank: 2,
    strengths: [
      '制度建设完善，执行到位',
      '服务意识强，响应及时',
      '数据分析能力不断提升'
    ],
    experiences: [
      '建立了标准化工作流程',
      '加强了跨部门沟通协调',
      '持续优化人才管理体系'
    ]
  }
])

const improvementDepartments = ref([
  {
    id: '5',
    name: '财务部',
    rank: 6,
    issues: [
      '工作效率有待提高',
      '新系统适应性不足',
      '跨部门配合不够积极'
    ],
    suggestions: [
      '加强财务系统培训',
      '优化工作流程和标准',
      '建立跨部门协作机制',
      '提升服务意识和响应速度'
    ]
  }
])

// Chart refs
const scoreComparisonRef = ref<HTMLElement>()
const gradeDistributionRef = ref<HTMLElement>()
const trendAnalysisRef = ref<HTMLElement>()
const departmentRadarRef = ref<HTMLElement>()

// 方法
const handleFilterChange = () => {
  loadDepartmentList()
}

const handleSelectionChange = (selection: DepartmentReportData[]) => {
  selectedDepartments.value = selection
}

const handlePreview = () => {
  previewDialog.data = {
    period: filterForm.assessmentPeriod,
    departments: departmentList.value
  }
  previewDialog.visible = true
  
  nextTick(() => {
    initAllCharts()
  })
}

const handleGenerate = async () => {
  if (selectedDepartments.value.length === 0) {
    ElMessage.warning('请先选择部门')
    return
  }
  
  try {
    loading.value = true
    
    // 模拟批量生成
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success(`成功生成${selectedDepartments.value.length}个部门的绩效报表`)
  } catch (__error) {
    ElMessage.error('生成失败')
  } finally {
    loading.value = false
  }
}

const handleViewDetail = (row: DepartmentReportData) => {
  ElMessage.info(`查看${row.departmentName}详情`)
}

const handleGenerateSingle = async (row: DepartmentReportData) => {
  try {
    loading.value = true
    
    // 模拟单个生成
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success(`${row.departmentName}的绩效报表生成成功`)
  } catch (__error) {
    ElMessage.error('生成失败')
  } finally {
    loading.value = false
  }
}

const handleDownloadReport = () => {
  // 模拟下载
  ElMessage.success('报表下载中...')
}

const loadDepartmentList = async () => {
  try {
    loading.value = true
    
    // 模拟数据加载
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    departmentList.value = [
      {
        id: '1',
        departmentName: '技术部',
        managerName: '李总监',
        totalEmployees: 30,
        participantCount: 28,
        averageScore: 85.2,
        rank: 1,
        gradeA: 8,
        gradeB: 12,
        gradeC: 7,
        gradeD: 1,
        gradeAPercent: 28.6,
        gradeBPercent: 42.9,
        gradeCPercent: 25.0,
        gradeDPercent: 3.6,
        improvement: 2.1
      },
      {
        id: '4',
        departmentName: '人事部',
        managerName: '王经理',
        totalEmployees: 16,
        participantCount: 15,
        averageScore: 83.8,
        rank: 2,
        gradeA: 3,
        gradeB: 7,
        gradeC: 4,
        gradeD: 1,
        gradeAPercent: 20.0,
        gradeBPercent: 46.7,
        gradeCPercent: 26.7,
        gradeDPercent: 6.7,
        improvement: 1.5
      },
      {
        id: '3',
        departmentName: '市场部',
        managerName: '陈经理',
        totalEmployees: 24,
        participantCount: 22,
        averageScore: 82.1,
        rank: 3,
        gradeA: 4,
        gradeB: 9,
        gradeC: 8,
        gradeD: 1,
        gradeAPercent: 18.2,
        gradeBPercent: 40.9,
        gradeCPercent: 36.4,
        gradeDPercent: 4.5,
        improvement: 0.8
      },
      {
        id: '2',
        departmentName: '销售部',
        managerName: '赵总监',
        totalEmployees: 38,
        participantCount: 35,
        averageScore: 80.5,
        rank: 4,
        gradeA: 5,
        gradeB: 14,
        gradeC: 13,
        gradeD: 3,
        gradeAPercent: 14.3,
        gradeBPercent: 40.0,
        gradeCPercent: 37.1,
        gradeDPercent: 8.6,
        improvement: -0.5
      },
      {
        id: '6',
        departmentName: '运营部',
        managerName: '孙经理',
        totalEmployees: 20,
        participantCount: 18,
        averageScore: 79.8,
        rank: 5,
        gradeA: 2,
        gradeB: 8,
        gradeC: 7,
        gradeD: 1,
        gradeAPercent: 11.1,
        gradeBPercent: 44.4,
        gradeCPercent: 38.9,
        gradeDPercent: 5.6,
        improvement: 1.2
      },
      {
        id: '5',
        departmentName: '财务部',
        managerName: '周经理',
        totalEmployees: 14,
        participantCount: 12,
        averageScore: 78.9,
        rank: 6,
        gradeA: 1,
        gradeB: 5,
        gradeC: 5,
        gradeD: 1,
        gradeAPercent: 8.3,
        gradeBPercent: 41.7,
        gradeCPercent: 41.7,
        gradeDPercent: 8.3,
        improvement: -1.8
      }
    ]
  } catch (__error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const initAllCharts = () => {
  initScoreComparisonChart()
  initGradeDistributionChart()
  initTrendAnalysisChart()
  initDepartmentRadarChart()
}

const initScoreComparisonChart = () => {
  if (!scoreComparisonRef.value) return
  
  const chart = echarts.init(scoreComparisonRef.value)
  
  const departments = departmentList.value.map(item => item.departmentName)
  const scores = departmentList.value.map(item => item.averageScore)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: departments
    },
    yAxis: {
      type: 'value',
      name: '平均分'
    },
    series: [
      {
        name: '平均分',
        type: 'bar',
        data: scores,
        itemStyle: {
   
          color: (params: unknown) => {
            const colors = ['#67c23a', '#409eff', '#e6a23c', '#f56c6c', '#909399', '#606266']
            return colors[params.dataIndex] || '#909399'
          }
        }
      }
    ]
  }
  
  chart.setOption(option)
}

const initGradeDistributionChart = () => {
  if (!gradeDistributionRef.value) return
  
  const chart = echarts.init(gradeDistributionRef.value)
  
  const totalA = departmentList.value.reduce((sum, item) => sum + item.gradeA, 0)
  const totalB = departmentList.value.reduce((sum, item) => sum + item.gradeB, 0)
  const totalC = departmentList.value.reduce((sum, item) => sum + item.gradeC, 0)
  const totalD = departmentList.value.reduce((sum, item) => sum + item.gradeD, 0)
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      bottom: '0',
      left: 'center'
    },
    series: [
      {
        name: '等级分布',
        type: 'pie',
        radius: ['40%', '70%'],
        data: [
          { value: totalA, name: 'A级优秀', itemStyle: { color: '#67c23a' } },
          { value: totalB, name: 'B级良好', itemStyle: { color: '#409eff' } },
          { value: totalC, name: 'C级合格', itemStyle: { color: '#e6a23c' } },
          { value: totalD, name: 'D级待改进', itemStyle: { color: '#f56c6c' } }
        ]
      }
    ]
  }
  
  chart.setOption(option)
}

const initTrendAnalysisChart = () => {
  if (!trendAnalysisRef.value) return
  
  const chart = echarts.init(trendAnalysisRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['技术部', '人事部', '市场部', '销售部', '运营部', '财务部']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['2024-09', '2024-10', '2024-11', '2024-12']
    },
    yAxis: {
      type: 'value',
      name: '平均分'
    },
    series: [
      {
        name: '技术部',
        type: 'line',
        data: [82.1, 83.5, 84.2, 85.2]
      },
      {
        name: '人事部',
        type: 'line',
        data: [81.5, 82.1, 83.0, 83.8]
      },
      {
        name: '市场部',
        type: 'line',
        data: [80.8, 81.2, 81.8, 82.1]
      },
      {
        name: '销售部',
        type: 'line',
        data: [81.2, 80.8, 80.9, 80.5]
      },
      {
        name: '运营部',
        type: 'line',
        data: [78.5, 78.8, 79.2, 79.8]
      },
      {
        name: '财务部',
        type: 'line',
        data: [80.1, 79.5, 79.2, 78.9]
      }
    ]
  }
  
  chart.setOption(option)
}

const initDepartmentRadarChart = () => {
  if (!departmentRadarRef.value) return
  
  const chart = echarts.init(departmentRadarRef.value)
  
  const option = {
    tooltip: {},
    legend: {
      data: ['技术部', '人事部', '市场部'],
      bottom: 0
    },
    radar: {
      indicator: [
        { name: '工作质量', max: 100 },
        { name: '工作效率', max: 100 },
        { name: '团队协作', max: 100 },
        { name: '创新能力', max: 100 },
        { name: '客户满意度', max: 100 }
      ]
    },
    series: [
      {
        name: '部门能力',
        type: 'radar',
        data: [
          {
            value: [88, 85, 90, 92, 85],
            name: '技术部'
          },
          {
            value: [85, 88, 95, 75, 90],
            name: '人事部'
          },
          {
            value: [82, 80, 85, 88, 85],
            name: '市场部'
          }
        ]
      }
    ]
  }
  
  chart.setOption(option)
}

const formatDateTime = (date: Date) => {
  return date.toLocaleString('zh-CN')
}

const getReportTypeText = () => {
  const typeMap = {
    comprehensive: '综合报表',
    comparison: '对比报表',
    trend: '趋势报表',
    detailed: '详细报表'
  }
  return typeMap[filterForm.reportType] || '综合报表'
}

const getRankType = (rank: number) => {
  if (rank === 1) return 'success'
  if (rank <= 3) return 'primary'
  if (rank <= 5) return 'warning'
  return 'danger'
}

const getScoreType = (score: number) => {
  if (score >= 85) return 'success'
  if (score >= 80) return 'primary'
  if (score >= 75) return 'warning'
  return 'danger'
}

const getGradeTooltip = (row: DepartmentReportData) => {
  return `A级:${row.gradeA}人(${row.gradeAPercent}%) B级:${row.gradeB}人(${row.gradeBPercent}%) C级:${row.gradeC}人(${row.gradeCPercent}%) D级:${row.gradeD}人(${row.gradeDPercent}%)`
}

const getImprovementClass = (improvement: number) => {
  if (improvement > 0) return 'text-success'
  if (improvement < 0) return 'text-danger'
  return 'text-info'
}

// 生命周期
onMounted(() => {
  loadDepartmentList()
})
</script>

<style scoped>
.department-performance-report {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.department-list-section {
  margin-bottom: 20px;
}

.grade-bar {
  display: flex;
  width: 80px;
  height: 8px;
  border-radius: 4px;
  overflow: hidden;
}

.grade-segment {
  height: 100%;
}

.grade-a {
  background-color: #67c23a;
}

.grade-b {
  background-color: #409eff;
}

.grade-c {
  background-color: #e6a23c;
}

.grade-d {
  background-color: #f56c6c;
}

.text-success {
  color: #67c23a;
}

.text-danger {
  color: #f56c6c;
}

.text-info {
  color: #909399;
}

.report-preview {
  padding: 30px;
  background-color: #fff;
  color: #333;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding-bottom: 20px;
  border-bottom: 2px solid #409eff;
  margin-bottom: 30px;
}

.company-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.company-text h2,
.company-text h3 {
  margin: 0;
  color: #409eff;
}

.company-text h3 {
  font-size: 18px;
  margin-top: 5px;
  color: #666;
}

.report-meta {
  text-align: right;
  color: #666;
}

.executive-summary-section,
.ranking-table-section,
.charts-section,
.department-analysis-section,
.conclusion-section,
.appendix-section {
  margin-bottom: 30px;
}

.executive-summary-section h3,
.ranking-table-section h3,
.charts-section h3,
.department-analysis-section h3,
.conclusion-section h3,
.appendix-section h3 {
  color: #409eff;
  border-bottom: 1px solid #ddd;
  padding-bottom: 10px;
  margin-bottom: 20px;
}

.summary-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.summary-icon {
  font-size: 2em;
  margin-right: 15px;
}

.summary-value {
  font-size: 1.8em;
  font-weight: bold;
  color: #409eff;
}

.summary-label {
  color: #666;
  font-size: 14px;
}

.chart-container {
  padding: 15px;
  background-color: #fff;
  border: 1px solid #eee;
  border-radius: 4px;
}

.chart-container h4 {
  margin: 0 0 15px;
  color: #333;
  text-align: center;
}

.analysis-content {
  padding: 20px;
}

.dept-analysis-item {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.dept-analysis-item h4 {
  margin: 0 0 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.dept-analysis-item p {
  margin: 10px 0 5px;
  font-weight: bold;
}

.dept-analysis-item ul {
  margin: 0 0 15px;
  padding-left: 20px;
}

.dept-analysis-item li {
  margin-bottom: 5px;
  line-height: 1.6;
}

.conclusion-content {
  padding: 20px;
}

.conclusion-item {
  margin-bottom: 25px;
}

.conclusion-item h4 {
  color: #409eff;
  margin: 0 0 10px;
}

.conclusion-item p {
  line-height: 1.8;
  margin-bottom: 10px;
}

.conclusion-item ul {
  margin: 0;
  padding-left: 20px;
}

.conclusion-item li {
  margin-bottom: 8px;
  line-height: 1.6;
}

.appendix-content h4 {
  color: #409eff;
  margin: 15px 0 10px;
}

.appendix-content ul {
  margin: 0 0 20px;
  padding-left: 20px;
}

.appendix-content li {
  margin-bottom: 5px;
  line-height: 1.6;
}
</style>