<template>
  <div class="performance-distribution-analysis">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>绩效分布分析</h3>
          <el-button type="primary" @click="handleExport">
            导出报告
          </el-button>
        </div>
      </template>

      <!-- 筛选区域 -->
      <div class="filter-section">
        <el-form :model="filterForm" :inline="true">
          <el-form-item label="考核周期">
            <el-date-picker
              v-model="filterForm.assessmentPeriod"
              type="month"
              placeholder="选择考核周期"
              @change="handleFilterChange"
             />
          </el-form-item>
          <el-form-item label="部门">
            <el-select
              v-model="filterForm.department"
              placeholder="请选择部门"
              @change="handleFilterChange"
              clearable
            >
              <el-option label="全部部门" value=""  />
              <el-option
                v-for="dept in departments"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
               />
            </el-select>
          </el-form-item>
          <el-form-item label="职级">
            <el-select
              v-model="filterForm.jobLevel"
              placeholder="请选择职级"
              @change="handleFilterChange"
              clearable
            >
              <el-option label="全部职级" value=""  />
              <el-option
                v-for="level in jobLevels"
                :key="level.id"
                :label="level.name"
                :value="level.id"
               />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 统计概览 -->
      <div class="summary-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic
              title="总参评人数"
              :value="summaryData.totalCount"
              suffix="人"
             />
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="平均分"
              :value="summaryData.averageScore"
              :precision="1"
              suffix="分"
             />
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="标准差"
              :value="summaryData.standardDeviation"
              :precision="2"
             />
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="正态分布符合度"
              :value="summaryData.normalityRate"
              :precision="1"
              suffix="%"
             />
          </el-col>
        </el-row>
      </div>

      <!-- 图表展示区域 -->
      <div class="charts-section">
        <el-row :gutter="20">
          <!-- 等级分布饼图 -->
          <el-col :span="12">
            <el-card shadow="never">
              <template #header>
                <h4>绩效等级分布</h4>
              </template>
              <div ref="gradeDistributionRef" style="height: 300px;"></div>
            </el-card>
          </el-col>
          
          <!-- 分数分布直方图 -->
          <el-col :span="12">
            <el-card shadow="never">
              <template #header>
                <h4>分数分布直方图</h4>
              </template>
              <div ref="scoreHistogramRef" style="height: 300px;"></div>
            </el-card>
          </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-top: 20px;">
          <!-- 正态分布拟合图 -->
          <el-col :span="12">
            <el-card shadow="never">
              <template #header>
                <h4>正态分布拟合</h4>
              </template>
              <div ref="normalDistributionRef" style="height: 300px;"></div>
            </el-card>
          </el-col>
          
          <!-- 部门对比雷达图 -->
          <el-col :span="12">
            <el-card shadow="never">
              <template #header>
                <h4>部门分布对比</h4>
              </template>
              <div ref="departmentRadarRef" style="height: 300px;"></div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 详细数据表格 -->
      <div class="data-table-section">
        <el-card shadow="never">
          <template #header>
            <h4>等级分布详情</h4>
          </template>
          <el-table :data="distributionTableData" border>
            <el-table-column prop="grade" label="绩效等级" align="center"  />
            <el-table-column prop="count" label="人数" align="center"  />
            <el-table-column prop="percentage" label="占比" align="center">
              <template #default="scope">
                {{ scope.row.percentage }}%
              </template>
            </el-table-column>
            <el-table-column prop="standardPercentage" label="标准占比" align="center">
              <template #default="scope">
                {{ scope.row.standardPercentage }}%
              </template>
            </el-table-column>
            <el-table-column prop="deviation" label="偏差" align="center">
              <template #default="scope">
                <span :class="getDeviationClass(scope.row.deviation)">
                  {{ scope.row.deviation > 0 ? '+' : '' }}{{ scope.row.deviation }}%
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="scoreRange" label="分数区间" align="center"  />
            <el-table-column prop="averageScore" label="平均分" align="center">
              <template #default="scope">
                {{ scope.row.averageScore?.toFixed(1) }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>

      <!-- 统计分析 -->
      <div class="analysis-section">
        <el-card shadow="never">
          <template #header>
            <h4>分析结论</h4>
          </template>
          <div class="analysis-content">
            <el-alert
              v-for="insight in analysisInsights"
              :key="insight.id"
              :title="insight.title"
              :description="insight.description"
              :type="insight.type"
              :closable="false"
              style="margin-bottom: 10px;"
             />
          </div>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'

// 接口定义
interface SummaryData {
  totalCount: number
  averageScore: number
  standardDeviation: number
  normalityRate: number
}

interface DistributionData {
  grade: string
  count: number
  percentage: number
  standardPercentage: number
  deviation: number
  scoreRange: string
  averageScore: number
}

interface AnalysisInsight {
  id: string
  title: string
  description: string
  type: 'success' | 'warning' | 'error' | 'info'
}

// 响应式数据
const loading = ref(false)
const departments = ref([
  { id: '1', name: 'HrHr技术部' },
  { id: '2', name: '销售部' },
  { id: '3', name: '市场部' },
  { id: '4', name: '人事部' }
])

const jobLevels = ref([
  { id: 'junior', name: '初级' },
  { id: 'middle', name: '中级' },
  { id: 'senior', name: '高级' },
  { id: 'expert', name: '专家' }
])

const filterForm = reactive({
  assessmentPeriod: '2024-12',
  department: '',
  jobLevel: ''
})

const summaryData = ref<SummaryData>({
  totalCount: 120,
  averageScore: 82.5,
  standardDeviation: 8.3,
  normalityRate: 85.2
})

const distributionTableData = ref<DistributionData[]>([
  {
    grade: 'A优秀',
    count: 24,
    percentage: 20.0,
    standardPercentage: 20.0,
    deviation: 0,
    scoreRange: '90-100',
    averageScore: 93.5
  },
  {
    grade: 'B良好',
    count: 42,
    percentage: 35.0,
    standardPercentage: 30.0,
    deviation: 5.0,
    scoreRange: '80-89',
    averageScore: 84.2
  },
  {
    grade: 'C合格',
    count: 42,
    percentage: 35.0,
    standardPercentage: 40.0,
    deviation: -5.0,
    scoreRange: '70-79',
    averageScore: 74.8
  },
  {
    grade: 'D待改进',
    count: 12,
    percentage: 10.0,
    standardPercentage: 10.0,
    deviation: 0,
    scoreRange: '60-69',
    averageScore: 65.3
  }
])

const analysisInsights = ref<AnalysisInsight[]>([
  {
    id: '1',
    title: '整体分布合理',
    description: '绩效等级分布基本符合正态分布规律，与标准分布偏差较小。',
    type: 'success'
  },
  {
    id: '2',
    title: 'B级人员偏多',
    description: 'B级（良好）人员比例为35%，高于标准30%，建议关注评价标准的执行。',
    type: 'warning'
  },
  {
    id: '3',
    title: '分数集中度较高',
    description: '标准差为8.3，分数相对集中，说明团队整体水平较为均衡。',
    type: 'info'
  }
])

// Chart refs
const gradeDistributionRef = ref<HTMLElement>()
const scoreHistogramRef = ref<HTMLElement>()
const normalDistributionRef = ref<HTMLElement>()
const departmentRadarRef = ref<HTMLElement>()

// 方法
const handleFilterChange = () => {
  loadAnalysisData()
}

const handleExport = () => {
  ElMessage.success('导出功能开发中...')
}

const loadAnalysisData = async () => {
  try {
    loading.value = true
    
    // 模拟数据加载
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 更新图表
    nextTick(() => {
      initAllCharts()
    })
  } catch (__error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const initAllCharts = () => {
  initGradeDistributionChart()
  initScoreHistogramChart()
  initNormalDistributionChart()
  initDepartmentRadarChart()
}

const initGradeDistributionChart = () => {
  if (!gradeDistributionRef.value) return
  
  const chart = echarts.init(gradeDistributionRef.value)
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      bottom: '0',
      left: 'center'
    },
    series: [
      {
        name: '绩效等级',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        data: [
          { value: 24, name: 'A优秀', itemStyle: { color: '#67c23a' } },
          { value: 42, name: 'B良好', itemStyle: { color: '#409eff' } },
          { value: 42, name: 'C合格', itemStyle: { color: '#e6a23c' } },
          { value: 12, name: 'D待改进', itemStyle: { color: '#f56c6c' } }
        ]
      }
    ]
  }
  
  chart.setOption(option)
}

const initScoreHistogramChart = () => {
  if (!scoreHistogramRef.value) return
  
  const chart = echarts.init(scoreHistogramRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: ['60-65', '65-70', '70-75', '75-80', '80-85', '85-90', '90-95', '95-100'],
        axisTick: {
          alignWithLabel: true
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '人数'
      }
    ],
    series: [
      {
        name: '人数分布',
        type: 'bar',
        barWidth: '60%',
        data: [3, 9, 18, 24, 28, 20, 15, 3],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        }
      }
    ]
  }
  
  chart.setOption(option)
}

const initNormalDistributionChart = () => {
  if (!normalDistributionRef.value) return
  
  const chart = echarts.init(normalDistributionRef.value)
  
  // 生成正态分布曲线数据
  const normalData = []
  const actualData = []
  for (let i = 60; i <= 100; i++) {
    // 模拟正态分布
    const normal = Math.exp(-Math.pow(i - 82.5, 2) / (2 * Math.pow(8.3, 2))) / (8.3 * Math.sqrt(2 * Math.PI))
    normalData.push([i, normal * 120 * 5]) // 缩放为合适的显示范围
    
    // 模拟实际数据
    const actual = normal * 120 * 5 + (Math.random() - 0.5) * 2
    actualData.push([i, Math.max(0, actual)])
  }
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['实际分布', '理论正态分布']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '分数'
    },
    yAxis: {
      type: 'value',
      name: '密度'
    },
    series: [
      {
        name: '实际分布',
        data: actualData,
        type: 'line',
        smooth: true,
        itemStyle: {
          color: '#409eff'
        }
      },
      {
        name: '理论正态分布',
        data: normalData,
        type: 'line',
        smooth: true,
        itemStyle: {
          color: '#67c23a'
        },
        lineStyle: {
          type: 'dashed'
        }
      }
    ]
  }
  
  chart.setOption(option)
}

const initDepartmentRadarChart = () => {
  if (!departmentRadarRef.value) return
  
  const chart = echarts.init(departmentRadarRef.value)
  
  const option = {
    tooltip: {},
    legend: {
      data: ['技术部', '销售部', '市场部', '人事部']
    },
    radar: {
      indicator: [
        { name: 'A级占比', max: 40 },
        { name: 'B级占比', max: 50 },
        { name: 'C级占比', max: 50 },
        { name: 'D级占比', max: 20 },
        { name: '平均分', max: 100 },
        { name: '标准差', max: 15 }
      ]
    },
    series: [
      {
        name: '部门绩效分布',
        type: 'radar',
        data: [
          {
            value: [25, 35, 30, 10, 85, 7.5],
            name: '技术部'
          },
          {
            value: [15, 40, 35, 10, 80, 9.2],
            name: '销售部'
          },
          {
            value: [20, 30, 40, 10, 82, 8.8],
            name: '市场部'
          },
          {
            value: [18, 38, 32, 12, 78, 10.1],
            name: '人事部'
          }
        ]
      }
    ]
  }
  
  chart.setOption(option)
}

const getDeviationClass = (deviation: number) => {
  if (Math.abs(deviation) <= 2) return 'text-success'
  if (Math.abs(deviation) <= 5) return 'text-warning'
  return 'text-danger'
}

// 生命周期
onMounted(() => {
  loadAnalysisData()
})
</script>

<style scoped>
.performance-distribution-analysis {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.summary-section {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fff;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.charts-section {
  margin-bottom: 20px;
}

.data-table-section {
  margin-bottom: 20px;
}

.analysis-section {
  margin-bottom: 20px;
}

.analysis-content {
  padding: 10px;
}

.text-success {
  color: #67c23a;
}

.text-warning {
  color: #e6a23c;
}

.text-danger {
  color: #f56c6c;
}
</style>