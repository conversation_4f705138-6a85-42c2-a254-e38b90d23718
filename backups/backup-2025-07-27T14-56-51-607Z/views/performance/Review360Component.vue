<template>
  <div class="review-360-component">
    <!-- 评价概览 -->
    <el-card class="overview-card">
      <template #header>
        <div class="card-header">
          <span>360度评价概览</span>
          <el-tag :type="getStatusType(evaluationData.status)">
            {{ getStatusLabel(evaluationData.status) }}
          </el-tag>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="info-item">
            <label>被评价人</label>
            <div class="info-value">
              <el-avatar :src="evaluationData.employee.avatar"   />
              <div class="employee-info">
                <div class="name">{{ evaluationData.employee.name }}</div>
                <div class="position">{{ evaluationData.employee.position }}</div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>考核周期</label>
            <div class="info-value">{{ evaluationData.period }}</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>评价进度</label>
            <div class="info-value">
              <el-progress 
                :percentage="evaluationProgress" 
                :color="progressColors"
               />
              <span class="progress-text">
                {{ completedCount }}/{{ totalCount }} 已完成
              </span>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 评价维度雷达图 -->
    <el-row :gutter="20" class="chart-row">
      <el-col :span="12">
        <el-card class="radar-card">
          <template #header>
            <span>评价维度雷达图</span>
            <el-switch 
              v-model="showComparison" 
              inactive-text="单独显示" 
              active-text="对比显示"
              style="float: right"
             />
          </template>
          <div id="radarChart" style="height: 400px"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="score-card">
          <template #header>
            <span>综合评分分析</span>
          </template>
          <div class="score-summary">
            <div class="total-score">
              <div class="score-value">{{ totalScore }}</div>
              <div class="score-label">综合得分</div>
              <el-rate
                v-model="scoreRate"
                disabled
                :colors="['#F56C6C', '#E6A23C', '#67C23A']"
               />
            </div>
            <el-divider   />
            <div class="dimension-scores">
              <div 
                v-for="dimension in dimensionScores" 
                :key="dimension.name"
                class="dimension-item"
              >
                <span class="dimension-name">{{ dimension.name }}</span>
                <div class="dimension-score">
                  <el-progress 
                    :percentage="dimension.score" 
                    :color="getDimensionColor(dimension.score)"
                   />
                  <span class="score-text">{{ dimension.score }}分</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 评价人矩阵 -->
    <el-card class="matrix-card">
      <template #header>
        <div class="card-header">
          <span>评价人矩阵</span>
          <div>
            <el-button size="small" @click="exportMatrix" :icon="Download">
              导出矩阵
            </el-button>
            <el-button size="small" type="primary" @click="sendReminder" :icon="Bell">
              催办提醒
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table :data="evaluatorMatrix" stripe>
        <el-table-column prop="role" label="评价角色" width="120" fixed>
          <template #default="{ row }">
            <el-tag :type="getRoleType(row.role)">
              {{ getRoleLabel(row.role) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="评价人" width="120"  />
        <el-table-column prop="department" label="部门" width="120"  />
        <el-table-column label="评价状态" width="100" align="center">
          <template #default="{ row }">
            <el-icon v-if="row.status === 'completed'" color="#67c23a" :size="20">
              <CircleCheck />
            </el-icon>
            <el-icon v-else-if="row.status === 'in_progress'" color="#e6a23c" :size="20">
              <Loading />
            </el-icon>
            <el-icon v-else color="#909399" :size="20">
              <Circle />
            </el-icon>
          </template>
        </el-table-column>
        <el-table-column 
          v-for="dimension in dimensions" 
          :key="dimension.id"
          :label="dimension.name"
          :prop="dimension.id"
          width="100"
          align="center"
        >
          <template #default="{ row }">
            <span v-if="row.scores[dimension.id]" class="score-cell">
              {{ row.scores[dimension.id] }}
            </span>
            <span v-else class="empty-cell">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="averageScore" label="平均分" width="100" align="center">
          <template #default="{ row }">
            <span class="average-score" v-if="row.averageScore">
              {{ row.averageScore.toFixed(1) }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="submitTime" label="提交时间" width="160"  />
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button 
              v-if="row.status === 'completed'" 
              link 
              type="primary" 
              @click="viewDetail(row)"
            >
              查看详情
            </el-button>
            <el-button 
              v-else-if="row.role === 'self' && currentUser.id === evaluationData.employee.id"
              link 
              type="primary" 
              @click="doEvaluation(row)"
            >
              去评价
            </el-button>
            <el-button 
              v-else-if="row.evaluatorId === currentUser.id"
              link 
              type="primary" 
              @click="doEvaluation(row)"
            >
              去评价
            </el-button>
            <el-button 
              v-else
              link 
              type="warning" 
              @click="remind(row)"
              :disabled="row.status === 'completed'"
            >
              提醒
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 评价详情对比 -->
    <el-card class="comparison-card" v-if="showComparison">
      <template #header>
        <span>评价详情对比</span>
      </template>
      
      <el-table :data="comparisonData" stripe>
        <el-table-column prop="dimension" label="评价维度" width="150" fixed  />
        <el-table-column prop="metric" label="评价指标" min-width="200"  />
        <el-table-column 
          v-for="evaluator in selectedEvaluators" 
          :key="evaluator.id"
          :label="evaluator.name"
          width="120"
          align="center"
        >
          <template #header>
            <div>
              <div>{{ evaluator.name }}</div>
              <el-tag size="small" :type="getRoleType(evaluator.role)">
                {{ getRoleLabel(evaluator.role) }}
              </el-tag>
            </div>
          </template>
          <template #default="{ row }">
            <div class="comparison-cell">
              <div class="score">{{ row.scores[evaluator.id] || '-' }}</div>
              <el-rate
                v-if="row.scores[evaluator.id]"
                v-model="row.scores[evaluator.id]"
                disabled
                :max="5"
                size="small"
               />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="平均分" width="100" align="center">
          <template #default="{ row }">
            <span class="average-score">{{ calculateRowAverage(row) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="标准差" width="100" align="center">
          <template #default="{ row }">
            <span :class="{ 'high-variance': calculateRowStdDev(row) > 20 }">
              {{ calculateRowStdDev(row).toFixed(1) }}
            </span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 评语汇总 -->
    <el-card class="comments-card">
      <template #header>
        <span>评语汇总</span>
      </template>
      
      <el-tabs v-model="activeCommentTab">
        <el-tab-pane label="按评价人" name="byEvaluator">
          <div class="comments-list">
            <div 
              v-for="comment in commentsByEvaluator" 
              :key="comment.id"
              class="comment-item"
            >
              <div class="comment-header">
                <el-avatar :src="comment.avatar" size="small"   />
                <span class="evaluator-name">{{ comment.name }}</span>
                <el-tag size="small" :type="getRoleType(comment.role)">
                  {{ getRoleLabel(comment.role) }}
                </el-tag>
                <span class="comment-time">{{ comment.time }}</span>
              </div>
              <div class="comment-content">
                <p><strong>优点：</strong>{{ comment.strengths }}</p>
                <p><strong>不足：</strong>{{ comment.weaknesses }}</p>
                <p><strong>建议：</strong>{{ comment.suggestions }}</p>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="按维度分类" name="byDimension">
          <el-collapse v-model="activeCollapse">
            <el-collapse-item 
              v-for="dimension in dimensionComments" 
              :key="dimension.name"
              :title="dimension.name"
              :name="dimension.name"
            >
              <div class="dimension-comments">
                <div 
                  v-for="comment in dimension.comments" 
                  :key="comment.id"
                  class="comment-snippet"
                >
                  <el-tag size="small" :type="getRoleType(comment.role)">
                    {{ comment.evaluator }}
                  </el-tag>
                  <span>{{ comment.content }}</span>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Download, Bell, CircleCheck, Loading, Circle } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// Props
const props = defineProps<{
  taskId: string
  viewMode?: 'employee' | 'manager' | 'hr'
}>()

// 当前用户
const currentUser = ref({
  id: 'current-user',
  name: 'HrHr当前用户',
  role: 'manager'
})

// 评价数据
const evaluationData = reactive({
  employee: {
    id: 'emp001',
    name: '张三',
    position: '前端工程师',
    avatar: '/avatar.jpg'
  },
  period: '2025年度考核',
  status: 'in_progress'
})

// 维度定义
const dimensions = ref([
  { id: 'performance', name: '工作业绩' },
  { id: 'ability', name: '工作能力' },
  { id: 'attitude', name: '工作态度' },
  { id: 'teamwork', name: '团队协作' },
  { id: 'innovation', name: '创新能力' }
])

// 评价人矩阵数据
const evaluatorMatrix = ref([
  {
    id: '1',
    role: 'self',
    evaluatorId: 'emp001',
    name: '张三',
    department: '技术部',
    status: 'completed',
    scores: {
      performance: 90,
      ability: 85,
      attitude: 88,
      teamwork: 92,
      innovation: 86
    },
    averageScore: 88.2,
    submitTime: '2025-01-20 10:30'
  },
  {
    id: '2',
    role: 'supervisor',
    evaluatorId: 'emp002',
    name: '李经理',
    department: '技术部',
    status: 'completed',
    scores: {
      performance: 88,
      ability: 90,
      attitude: 85,
      teamwork: 88,
      innovation: 84
    },
    averageScore: 87,
    submitTime: '2025-01-21 15:20'
  },
  {
    id: '3',
    role: 'peer',
    evaluatorId: 'emp003',
    name: '王五',
    department: '技术部',
    status: 'completed',
    scores: {
      performance: 85,
      ability: 88,
      attitude: 90,
      teamwork: 95,
      innovation: 88
    },
    averageScore: 89.2,
    submitTime: '2025-01-21 09:15'
  },
  {
    id: '4',
    role: 'peer',
    evaluatorId: 'emp004',
    name: '赵六',
    department: '产品部',
    status: 'in_progress',
    scores: {},
    averageScore: null,
    submitTime: null
  },
  {
    id: '5',
    role: 'subordinate',
    evaluatorId: 'emp005',
    name: '钱七',
    department: '技术部',
    status: 'pending',
    scores: {},
    averageScore: null,
    submitTime: null
  }
])

// 其他状态
const showComparison = ref(false)
const activeCommentTab = ref('byEvaluator')
const activeCollapse = ref(['工作业绩'])
const selectedEvaluators = ref([])

// 维度评分数据
const dimensionScores = computed(() => {
  return dimensions.value.map(dim => {
    const scores = evaluatorMatrix.value
      .filter(e => e.status === 'completed')
      .map(e => e.scores[dim.id])
      .filter(s => s !== undefined)
    
    const average = scores.length > 0 
      ? scores.reduce((a, b) => a + b, 0) / scores.length 
      : 0
    
    return {
      name: dim.name,
      score: Math.round(average)
    }
  })
})

// 总分
const totalScore = computed(() => {
  const allScores = evaluatorMatrix.value
    .filter(e => e.status === 'completed' && e.averageScore)
    .map(e => e.averageScore)
  
  return allScores.length > 0
    ? Math.round(allScores.reduce((a, b) => a + b, 0) / allScores.length)
    : 0
})

// 评分等级
const scoreRate = computed(() => Math.round(totalScore.value / 20))

// 评价进度
const completedCount = computed(() => 
  evaluatorMatrix.value.filter(e => e.status === 'completed').length
)
const totalCount = computed(() => evaluatorMatrix.value.length)
const evaluationProgress = computed(() => 
  Math.round((completedCount.value / totalCount.value) * 100)
)

// 进度颜色
const progressColors = [
  { color: '#f56c6c', percentage: 20 },
  { color: '#e6a23c', percentage: 40 },
  { color: '#5cb87a', percentage: 60 },
  { color: '#1989fa', percentage: 80 },
  { color: '#6f7ad3', percentage: 100 }
]

// 对比数据
const comparisonData = computed(() => {
  const data = []
  dimensions.value.forEach(dim => {
    // 这里简化处理，实际应该有更详细的指标
    data.push({
      dimension: dim.name,
      metric: `${dim.name}综合表现`,
      scores: evaluatorMatrix.value.reduce((acc, e) => {
        if (e.scores[dim.id]) {
          acc[e.id] = e.scores[dim.id]
        }
        return acc
      }, {})
    })
  })
  return data
})

// 评语数据
const commentsByEvaluator = ref([
  {
    id: '1',
    name: '张三',
    role: 'self',
    avatar: '/avatar1.jpg',
    time: '2025-01-20 10:30',
    strengths: '本年度积极承担团队核心开发任务，技术能力有显著提升，能够独立解决复杂问题。',
    weaknesses: '在项目时间评估上还需要加强，偶尔会出现延期情况。',
    suggestions: '希望能够参与更多的技术分享，提升团队影响力。'
  },
  {
    id: '2',
    name: '李经理',
    role: 'supervisor',
    avatar: '/avatar2.jpg',
    time: '2025-01-21 15:20',
    strengths: '技术扎实，学习能力强，能够快速掌握新技术并应用到项目中。团队协作精神好。',
    weaknesses: '沟通表达能力还有提升空间，在跨部门协作时需要更主动。',
    suggestions: '建议承担更多的项目管理职责，培养全局观。'
  }
])

const dimensionComments = computed(() => {
  return dimensions.value.map(dim => ({
    name: dim.name,
    comments: [
      {
        id: '1',
        evaluator: '张三',
        role: 'self',
        content: '在该维度表现良好，能够按时完成任务。'
      },
      {
        id: '2',
        evaluator: '李经理',
        role: 'supervisor',
        content: '表现超出预期，展现了良好的专业素养。'
      }
    ]
  }))
})

// 方法
const getStatusType = (status: string) => {
  const map: Record<string, string> = {
    completed: 'success',
    in_progress: 'warning',
    pending: 'info'
  }
  return map[status] || 'info'
}

const getStatusLabel = (status: string) => {
  const map: Record<string, string> = {
    completed: '已完成',
    in_progress: '进行中',
    pending: '待开始'
  }
  return map[status] || status
}

const getRoleType = (role: string) => {
  const map: Record<string, string> = {
    self: '',
    supervisor: 'warning',
    peer: 'primary',
    subordinate: 'success',
    other: 'info'
  }
  return map[role] || 'info'
}

const getRoleLabel = (role: string) => {
  const map: Record<string, string> = {
    self: '自评',
    supervisor: '上级',
    peer: '同事',
    subordinate: '下属',
    other: '其他'
  }
  return map[role] || role
}

const getDimensionColor = (score: number) => {
  if (score >= 90) return '#67c23a'
  if (score >= 80) return '#409eff'
  if (score >= 70) return '#e6a23c'
  return '#f56c6c'
}

   
const calculateRowAverage = (row: unknown) => {
   
  const scores = Object.values(row.scores).filter((s: unknown) => typeof s === 'number')
  return scores.length > 0
   
    ? (scores.reduce((a: unknown, b: unknown) => a + b, 0) / scores.length).toFixed(1)
    : '-'
}

   
const calculateRowStdDev = (row: unknown) => {
   
  const scores = Object.values(row.scores).filter((s: unknown) => typeof s === 'number') as number[]
  if (scores.length < 2) return 0
  
  const avg = scores.reduce((a, b) => a + b, 0) / scores.length
  const variance = scores.reduce((a, b) => a + Math.pow(b - avg, 2), 0) / scores.length
  return Math.sqrt(variance)
}

// 初始化雷达图
const initRadarChart = () => {
  const chartDom = document.getElementById('radarChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  
  // 准备数据
  const indicator = dimensions.value.map(dim => ({
    name: dim.name,
    max: 100
  }))
  
  const series = []
  
  // 添加已完成的评价数据
  evaluatorMatrix.value
    .filter(e => e.status === 'completed')
    .forEach(evaluator => {
      const data = dimensions.value.map(dim => evaluator.scores[dim.id] || 0)
      series.push({
        name: `${evaluator.name}(${getRoleLabel(evaluator.role)})`,
        value: data,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          width: 2
        }
      })
    })
  
  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      data: series.map(s => s.name),
      bottom: 0
    },
    radar: {
      indicator: indicator,
      center: ['50%', '45%'],
      radius: '65%'
    },
    series: [
      {
        type: 'radar',
        data: series,
        emphasis: {
          lineStyle: {
            width: 4
          }
        }
      }
    ]
  }
  
  myChart.setOption(option)
}

// 导出矩阵
const exportMatrix = () => {
  ElMessage.success('正在导出评价矩阵...')
}

// 发送提醒
const sendReminder = async () => {
  const pending = evaluatorMatrix.value.filter(e => e.status !== 'completed')
  if (pending.length === 0) {
    ElMessage.info('所有评价已完成，无需提醒')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `还有 ${pending.length} 位评价人未完成评价，确定要发送提醒吗？`,
      '提醒确认'
    )
    ElMessage.success('提醒已发送')
  } catch {
    // 用户取消
  }
}

// 查看详情
   
const viewDetail = (row: unknown) => {
  ElMessage.info(`查看${row.name}的评价详情`)
}

// 去评价
   
const doEvaluation = (row: unknown) => {
  ElMessage.info('跳转到评价页面')
}

// 提醒单个评价人
   
const remind = async (row: unknown) => {
  try {
    await ElMessageBox.confirm(
      `确定要提醒${row.name}完成评价吗？`,
      '提醒确认'
    )
    ElMessage.success('提醒已发送')
  } catch {
    // 用户取消
  }
}

// 监听对比开关
watch(showComparison, (val) => {
  if (val) {
    // 默认选择已完成的评价人进行对比
    selectedEvaluators.value = evaluatorMatrix.value
      .filter(e => e.status === 'completed')
      .slice(0, 4) // 最多显示4个
  }
})

// 初始化
onMounted(() => {
  initRadarChart()
  
  // 响应式处理
  window.addEventListener('resize', () => {
    const chart = echarts.getInstanceByDom(document.getElementById('radarChart')!)
    chart?.resize()
  })
})
</script>

<style lang="scss" scoped>
.review-360-component {
  padding: 20px;
  
  .overview-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .info-item {
      label {
        display: block;
        color: #909399;
        font-size: 14px;
        margin-bottom: 8px;
      }
      
      .info-value {
        display: flex;
        align-items: center;
        gap: 10px;
        
        .employee-info {
          .name {
            font-size: 16px;
            font-weight: bold;
            color: #303133;
          }
          
          .position {
            font-size: 14px;
            color: #606266;
          }
        }
        
        .progress-text {
          margin-left: 10px;
          font-size: 14px;
          color: #606266;
        }
      }
    }
  }
  
  .chart-row {
    margin-bottom: 20px;
  }
  
  .score-card {
    .score-summary {
      .total-score {
        text-align: center;
        padding: 20px;
        
        .score-value {
          font-size: 48px;
          font-weight: bold;
          color: #409eff;
        }
        
        .score-label {
          font-size: 14px;
          color: #909399;
          margin-top: 10px;
        }
      }
      
      .dimension-scores {
        padding: 0 20px;
        
        .dimension-item {
          margin-bottom: 15px;
          
          .dimension-name {
            display: block;
            margin-bottom: 8px;
            color: #606266;
            font-size: 14px;
          }
          
          .dimension-score {
            display: flex;
            align-items: center;
            gap: 10px;
            
            .score-text {
              font-weight: bold;
              color: #303133;
            }
          }
        }
      }
    }
  }
  
  .matrix-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .score-cell {
      font-weight: bold;
      color: #409eff;
    }
    
    .empty-cell {
      color: #c0c4cc;
    }
    
    .average-score {
      font-weight: bold;
      color: #67c23a;
      font-size: 16px;
    }
  }
  
  .comparison-card {
    margin-bottom: 20px;
    
    .comparison-cell {
      .score {
        font-weight: bold;
        margin-bottom: 5px;
      }
    }
    
    .high-variance {
      color: #f56c6c;
      font-weight: bold;
    }
  }
  
  .comments-card {
    .comments-list {
      .comment-item {
        padding: 15px;
        border: 1px solid #ebeef5;
        border-radius: 8px;
        margin-bottom: 15px;
        
        .comment-header {
          display: flex;
          align-items: center;
          gap: 10px;
          margin-bottom: 10px;
          
          .evaluator-name {
            font-weight: bold;
            color: #303133;
          }
          
          .comment-time {
            margin-left: auto;
            color: #909399;
            font-size: 13px;
          }
        }
        
        .comment-content {
          p {
            margin: 8px 0;
            line-height: 1.6;
            
            strong {
              color: #606266;
            }
          }
        }
      }
    }
    
    .dimension-comments {
      .comment-snippet {
        display: flex;
        align-items: flex-start;
        gap: 10px;
        margin-bottom: 10px;
        padding: 10px;
        background-color: #f5f7fa;
        border-radius: 4px;
        
        span {
          flex: 1;
          line-height: 1.5;
        }
      }
    }
  }
}
</style>