<template>
  <div class="assessment-plan-management">
    <!-- 搜索栏 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="方案名称">
          <el-input v-model="searchForm.name" placeholder="请输入方案名称" clearable   />
        </el-form-item>
        <el-form-item label="考核类型">
          <el-select v-model="searchForm.type" placeholder="请选择" clearable>
            <el-option label="年度考核" value="annual"  />
            <el-option label="季度考核" value="quarterly"  />
            <el-option label="月度考核" value="monthly"  />
            <el-option label="项目考核" value="project"  />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择" clearable>
            <el-option label="草稿" value="draft"  />
            <el-option label="已发布" value="published"  />
            <el-option label="进行中" value="active"  />
            <el-option label="已完成" value="completed"  />
          </el-select>
        </el-form-item>
        <el-form-item label="考核周期">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
           />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :icon="Search">查询</el-button>
          <el-button @click="handleReset" :icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作栏 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>考核方案列表</span>
          <div>
            <el-button type="primary" @click="handleCreate" :icon="Plus">新建方案</el-button>
            <el-button @click="handleBatchDelete" :disabled="!multipleSelection.length" :icon="Delete">
              批量删除
            </el-button>
            <el-button @click="handleExport" :icon="Download">导出</el-button>
          </div>
        </div>
      </template>

      <!-- 数据表格 -->
      <el-table
        :data="tableData"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="name" label="方案名称" min-width="150" show-overflow-tooltip  />
        <el-table-column prop="type" label="考核类型" width="100">
          <template #default="{ row }">
            <el-tag>{{ getTypeLabel(row.type) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="period" label="考核周期" min-width="120"  />
        <el-table-column label="考核范围" min-width="150">
          <template #default="{ row }">
            <div class="scope-info">
              <span v-if="row.scope.departments.length">
                {{ row.scope.departments.length }}个部门
              </span>
              <span v-if="row.scope.positions?.length">
                {{ row.scope.positions.length }}个岗位
              </span>
              <span v-if="row.scope.employees?.length">
                {{ row.scope.employees.length }}名员工
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="考核配置" min-width="200">
          <template #default="{ row }">
            <div class="config-tags">
              <el-tag v-if="row.config.allowSelfEval" type="success" size="small">自评</el-tag>
              <el-tag v-if="row.config.allow360Review" type="primary" size="small">360度</el-tag>
              <el-tag v-if="row.config.requireCalibration" type="warning" size="small">强制分布</el-tag>
              <el-tag v-if="row.config.enableAppeal" type="info" size="small">申诉</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">{{ getStatusLabel(row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdBy" label="创建人" width="100"  />
        <el-table-column prop="createdAt" label="创建时间" width="160"  />
        <el-table-column label="操作" fixed="right" width="200">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleView(row)">查看</el-button>
            <el-button link type="primary" @click="handleEdit(row)" v-if="row.status === 'draft'">
              编辑
            </el-button>
            <el-button link type="primary" @click="handleCopy(row)">复制</el-button>
            <el-button link type="primary" @click="handlePublish(row)" v-if="row.status === 'draft'">
              发布
            </el-button>
            <el-button link type="danger" @click="handleDelete(row)" v-if="row.status === 'draft'">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
    </el-card>

    <!-- 新建/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="方案名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入方案名称"   />
        </el-form-item>
        <el-form-item label="考核类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择考核类型">
            <el-option label="年度考核" value="annual"  />
            <el-option label="季度考核" value="quarterly"  />
            <el-option label="月度考核" value="monthly"  />
            <el-option label="项目考核" value="project"  />
          </el-select>
        </el-form-item>
        <el-form-item label="使用模板">
          <el-select v-model="form.templateId" placeholder="选择考核模板（可选）" clearable>
            <el-option v-for="tpl in templates" :key="tpl.id" :label="tpl.name" :value="tpl.id"  />
          </el-select>
        </el-form-item>
        <el-form-item label="考核周期" prop="period">
          <el-date-picker
            v-model="periodRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            @change="handlePeriodChange"
           />
        </el-form-item>
        <el-form-item label="方案说明">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入方案说明"
            />
        </el-form-item>
        
        <el-divider content-position="left">考核范围设置</el-divider>
        
        <el-form-item label="适用部门">
          <el-tree-select
            v-model="form.scope.departments"
            :data="departmentTree"
            multiple
            :render-after-expand="false"
            show-checkbox
            check-strictly
            placeholder="请选择适用部门"
            style="width: 100%"
           />
        </el-form-item>
        <el-form-item label="适用岗位">
          <el-select v-model="form.scope.positions" multiple placeholder="请选择适用岗位">
            <el-option v-for="pos in positions" :key="pos.id" :label="pos.name" :value="pos.id"  />
          </el-select>
        </el-form-item>
        <el-form-item label="指定员工">
          <el-select
            v-model="form.scope.employees"
            multiple
            filterable
            remote
            :remote-method="searchEmployees"
            placeholder="搜索并选择员工"
          >
            <el-option
              v-for="emp in employeeOptions"
              :key="emp.id"
              :label="`${emp.name} (${emp.code})`"
              :value="emp.id"
             />
          </el-select>
        </el-form-item>
        <el-form-item label="排除员工">
          <el-select
            v-model="form.scope.excludeEmployees"
            multiple
            filterable
            remote
            :remote-method="searchExcludeEmployees"
            placeholder="搜索并排除员工"
          >
            <el-option
              v-for="emp in excludeEmployeeOptions"
              :key="emp.id"
              :label="`${emp.name} (${emp.code})`"
              :value="emp.id"
             />
          </el-select>
        </el-form-item>
        
        <el-divider content-position="left">考核配置</el-divider>
        
        <el-form-item label="自我评价">
          <el-switch v-model="form.config.allowSelfEval" active-text="启用" inactive-text="禁用"  />
        </el-form-item>
        <el-form-item label="360度评价">
          <el-switch v-model="form.config.allow360Review" active-text="启用" inactive-text="禁用"  />
        </el-form-item>
        <el-form-item label="强制分布">
          <el-switch v-model="form.config.requireCalibration" active-text="启用" inactive-text="禁用"  />
        </el-form-item>
        <el-form-item label="等级分布" v-if="form.config.requireCalibration">
          <div class="grade-distribution">
            <el-row :gutter="10">
              <el-col :span="6">
                <el-input-number
                  v-model="gradeDistribution.A.min"
                  :min="0"
                  :max="100"
                  placeholder="A级最小%"
                  />
                <span> - </span>
                <el-input-number
                  v-model="gradeDistribution.A.max"
                  :min="0"
                  :max="100"
                  placeholder="A级最大%"
                  />
              </el-col>
              <el-col :span="6">
                <el-input-number
                  v-model="gradeDistribution.B.min"
                  :min="0"
                  :max="100"
                  placeholder="B级最小%"
                  />
                <span> - </span>
                <el-input-number
                  v-model="gradeDistribution.B.max"
                  :min="0"
                  :max="100"
                  placeholder="B级最大%"
                  />
              </el-col>
              <el-col :span="6">
                <el-input-number
                  v-model="gradeDistribution.C.min"
                  :min="0"
                  :max="100"
                  placeholder="C级最小%"
                  />
                <span> - </span>
                <el-input-number
                  v-model="gradeDistribution.C.max"
                  :min="0"
                  :max="100"
                  placeholder="C级最大%"
                  />
              </el-col>
              <el-col :span="6">
                <el-input-number
                  v-model="gradeDistribution.D.min"
                  :min="0"
                  :max="100"
                  placeholder="D级最小%"
                  />
                <span> - </span>
                <el-input-number
                  v-model="gradeDistribution.D.max"
                  :min="0"
                  :max="100"
                  placeholder="D级最大%"
                  />
              </el-col>
            </el-row>
          </div>
        </el-form-item>
        <el-form-item label="申诉机制">
          <el-switch v-model="form.config.enableAppeal" active-text="启用" inactive-text="禁用"  />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </template>
    </el-dialog>

    <!-- 查看详情抽屉 -->
    <el-drawer v-model="detailVisible" :title="detailTitle" size="50%">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="方案名称">{{ currentPlan?.name }}</el-descriptions-item>
        <el-descriptions-item label="考核类型">
          <el-tag>{{ getTypeLabel(currentPlan?.type || '') }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="考核周期">{{ currentPlan?.period }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(currentPlan?.status || '')">
            {{ getStatusLabel(currentPlan?.status || '') }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建人">{{ currentPlan?.createdBy }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ currentPlan?.createdAt }}</el-descriptions-item>
        <el-descriptions-item label="方案说明" :span="2">
          {{ currentPlan?.description || '无' }}
        </el-descriptions-item>
      </el-descriptions>
      
      <el-divider   />
      
      <h3>考核范围</h3>
      <el-descriptions :column="1" border>
        <el-descriptions-item label="适用部门">
          <el-tag v-for="dept in currentPlan?.scope.departments" :key="dept" style="margin-right: 5px">
            {{ dept }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="适用岗位" v-if="currentPlan?.scope.positions?.length">
          <el-tag v-for="pos in currentPlan?.scope.positions" :key="pos" style="margin-right: 5px">
            {{ pos }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="指定员工" v-if="currentPlan?.scope.employees?.length">
          {{ currentPlan?.scope.employees.length }}人
        </el-descriptions-item>
      </el-descriptions>
      
      <el-divider   />
      
      <h3>考核配置</h3>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="自我评价">
          {{ currentPlan?.config.allowSelfEval ? '启用' : '禁用' }}
        </el-descriptions-item>
        <el-descriptions-item label="360度评价">
          {{ currentPlan?.config.allow360Review ? '启用' : '禁用' }}
        </el-descriptions-item>
        <el-descriptions-item label="强制分布">
          {{ currentPlan?.config.requireCalibration ? '启用' : '禁用' }}
        </el-descriptions-item>
        <el-descriptions-item label="申诉机制">
          {{ currentPlan?.config.enableAppeal ? '启用' : '禁用' }}
        </el-descriptions-item>
      </el-descriptions>
      
      <template v-if="currentPlan?.config.gradeDistribution">
        <el-divider   />
        <h3>等级分布</h3>
        <el-table :data="gradeDistributionData" stripe>
          <el-table-column prop="grade" label="等级" width="80"  />
          <el-table-column prop="name" label="名称"  />
          <el-table-column prop="range" label="比例范围"  />
        </el-table>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus, Delete, Download } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import type { AssessmentPlan, GradeDistribution } from '@/types/performance'

// 搜索表单
const searchForm = reactive({
  name: '',
  type: '',
  status: '',
  dateRange: []
})

// 表格数据
const loading = ref(false)
const tableData = ref<AssessmentPlan[]>([])
const multipleSelection = ref<AssessmentPlan[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 对话框
const dialogVisible = ref(false)
const dialogTitle = computed(() => form.id ? '编辑考核方案' : '新建考核方案')
const formRef = ref<FormInstance>()
const form = reactive<Partial<AssessmentPlan>>({
  name: '',
  type: 'annual',
  templateId: '',
  status: 'draft',
  period: '',
  startDate: '',
  endDate: '',
  description: '',
  scope: {
    departments: [],
    positions: [],
    employees: [],
    excludeEmployees: []
  },
  config: {
    allowSelfEval: true,
    allow360Review: false,
    requireCalibration: false,
    enableAppeal: true
  }
})

// 等级分布
const gradeDistribution = reactive<GradeDistribution>({
  A: { min: 5, max: 15 },
  B: { min: 25, max: 35 },
  C: { min: 40, max: 60 },
  D: { min: 5, max: 15 }
})

// 详情抽屉
const detailVisible = ref(false)
const detailTitle = ref('')
const currentPlan = ref<AssessmentPlan | null>(null)

// 辅助数据
const templates = ref([
  { id: '1', name: 'HrHr年度考核标准模板' },
  { id: '2', name: '季度考核标准模板' },
  { id: '3', name: '技术岗位考核模板' },
  { id: '4', name: '管理岗位考核模板' }
])

const departmentTree = ref([
  {
    value: 'tech',
    label: '技术部',
    children: [
      { value: 'frontend', label: '前端组' },
      { value: 'backend', label: '后端组' },
      { value: 'test', label: '测试组' }
    ]
  },
  {
    value: 'product',
    label: '产品部'
  },
  {
    value: 'hr',
    label: '人力资源部'
  }
])

const positions = ref([
  { id: 'p1', name: '前端工程师' },
  { id: 'p2', name: '后端工程师' },
  { id: 'p3', name: '产品经理' },
  { id: 'p4', name: '项目经理' },
  { id: 'p5', name: '技术总监' }
])

const employeeOptions = ref([])
const excludeEmployeeOptions = ref([])
const periodRange = ref([])

// 表单验证规则
const rules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入方案名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择考核类型', trigger: 'change' }
  ],
  period: [
    { required: true, message: '请选择考核周期', trigger: 'change' }
  ]
})

// 等级分布表格数据
const gradeDistributionData = computed(() => {
  if (!currentPlan.value?.config.gradeDistribution) return []
  const dist = currentPlan.value.config.gradeDistribution
  return [
    { grade: 'A', name: '优秀', range: `${dist.A.min}% - ${dist.A.max}%` },
    { grade: 'B', name: '良好', range: `${dist.B.min}% - ${dist.B.max}%` },
    { grade: 'C', name: '合格', range: `${dist.C.min}% - ${dist.C.max}%` },
    { grade: 'D', name: '需改进', range: `${dist.D.min}% - ${dist.D.max}%` }
  ]
})

// 获取类型标签
const getTypeLabel = (type: string) => {
  const map: Record<string, string> = {
    annual: '年度考核',
    quarterly: '季度考核',
    monthly: '月度考核',
    project: '项目考核'
  }
  return map[type] || type
}

// 获取状态标签
const getStatusLabel = (status: string) => {
  const map: Record<string, string> = {
    draft: '草稿',
    published: '已发布',
    active: '进行中',
    completed: '已完成'
  }
  return map[status] || status
}

// 获取状态类型
const getStatusType = (status: string) => {
  const map: Record<string, string> = {
    draft: 'info',
    published: 'warning',
    active: 'success',
    completed: ''
  }
  return map[status] || 'info'
}

// 搜索
const handleSearch = () => {
  loadData()
}

// 重置
const handleReset = () => {
  searchForm.name = ''
  searchForm.type = ''
  searchForm.status = ''
  searchForm.dateRange = []
  loadData()
}

// 新建
const handleCreate = () => {
  resetForm()
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row: AssessmentPlan) => {
  Object.assign(form, row)
  if (row.config.gradeDistribution) {
    Object.assign(gradeDistribution, row.config.gradeDistribution)
  }
  periodRange.value = [row.startDate, row.endDate]
  dialogVisible.value = true
}

// 查看
const handleView = (row: AssessmentPlan) => {
  currentPlan.value = row
  detailTitle.value = `考核方案详情 - ${row.name}`
  detailVisible.value = true
}

// 复制
const handleCopy = async (row: AssessmentPlan) => {
  try {
    await ElMessageBox.confirm('确定要复制该考核方案吗？', '提示')
    const newPlan = { ...row, id: '', name: `${row.name} - 副本`, status: 'draft' }
    delete newPlan.id
    tableData.value.unshift(newPlan as AssessmentPlan)
    ElMessage.success('复制成功')
  } catch {
    // 用户取消
  }
}

// 发布
const handlePublish = async (row: AssessmentPlan) => {
  try {
    await ElMessageBox.confirm(
      '发布后将无法修改方案内容，确定要发布吗？',
      '提示',
      { type: 'warning' }
    )
    row.status = 'published'
    ElMessage.success('发布成功')
  } catch {
    // 用户取消
  }
}

// 删除
const handleDelete = async (row: AssessmentPlan) => {
  try {
    await ElMessageBox.confirm('确定要删除该考核方案吗？', '提示', { type: 'warning' })
    const index = tableData.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      tableData.value.splice(index, 1)
    }
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${multipleSelection.value.length} 个考核方案吗？`,
      '提示',
      { type: 'warning' }
    )
    ElMessage.success('批量删除成功')
    loadData()
  } catch {
    // 用户取消
  }
}

// 导出
const handleExport = () => {
  ElMessage.success('正在导出考核方案...')
}

// 保存
const handleSave = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate()
  
  // 设置等级分布
  if (form.config?.requireCalibration) {
    form.config.gradeDistribution = { ...gradeDistribution }
  }
  
  ElMessage.success(form.id ? '修改成功' : '创建成功')
  dialogVisible.value = false
  loadData()
}

// 选择变化
const handleSelectionChange = (val: AssessmentPlan[]) => {
  multipleSelection.value = val
}

// 周期变化
   
const handlePeriodChange = (val: unknown) => {
  if (val && val.length === 2) {
    form.startDate = val[0]
    form.endDate = val[1]
    form.period = `${val[0]} 至 ${val[1]}`
  }
}

// 搜索员工
const searchEmployees = (query: string) => {
  if (query) {
    employeeOptions.value = [
      { id: 'e1', name: '张三', code: 'EMP001' },
      { id: 'e2', name: '李四', code: 'EMP002' },
      { id: 'e3', name: '王五', code: 'EMP003' }
    ]
  }
}

// 搜索排除员工
const searchExcludeEmployees = (query: string) => {
  if (query) {
    excludeEmployeeOptions.value = [
      { id: 'e4', name: '赵六', code: 'EMP004' },
      { id: 'e5', name: '钱七', code: 'EMP005' }
    ]
  }
}

// 分页变化
const handleSizeChange = (val: number) => {
  pageSize.value = val
  loadData()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  loadData()
}

// 重置表单
const resetForm = () => {
  form.id = ''
  form.name = ''
  form.type = 'annual'
  form.templateId = ''
  form.status = 'draft'
  form.period = ''
  form.startDate = ''
  form.endDate = ''
  form.description = ''
  form.scope = {
    departments: [],
    positions: [],
    employees: [],
    excludeEmployees: []
  }
  form.config = {
    allowSelfEval: true,
    allow360Review: false,
    requireCalibration: false,
    enableAppeal: true
  }
  periodRange.value = []
  Object.assign(gradeDistribution, {
    A: { min: 5, max: 15 },
    B: { min: 25, max: 35 },
    C: { min: 40, max: 60 },
    D: { min: 5, max: 15 }
  })
}

// 加载数据
const loadData = async () => {
  loading.value = true
  
  // 模拟数据
  setTimeout(() => {
    tableData.value = [
      {
        id: '1',
        name: '2025年度绩效考核方案',
        type: 'annual',
        status: 'active',
        period: '2025-01-01 至 2025-12-31',
        startDate: '2025-01-01',
        endDate: '2025-12-31',
        description: '全公司年度绩效考核',
        scope: {
          departments: ['技术部', '产品部', '市场部'],
          positions: ['p1', 'p2', 'p3'],
          employees: []
        },
        config: {
          allowSelfEval: true,
          allow360Review: true,
          requireCalibration: true,
          enableAppeal: true,
          gradeDistribution: {
            A: { min: 10, max: 15 },
            B: { min: 25, max: 35 },
            C: { min: 40, max: 55 },
            D: { min: 5, max: 10 }
          }
        },
        createdBy: '系统管理员',
        createdAt: '2024-12-15 10:00:00',
        updatedAt: '2024-12-15 10:00:00'
      },
      {
        id: '2',
        name: 'Q1季度考核方案',
        type: 'quarterly',
        status: 'draft',
        period: '2025-01-01 至 2025-03-31',
        startDate: '2025-01-01',
        endDate: '2025-03-31',
        scope: {
          departments: ['技术部'],
          positions: [],
          employees: []
        },
        config: {
          allowSelfEval: true,
          allow360Review: false,
          requireCalibration: false,
          enableAppeal: true
        },
        createdBy: 'HR经理',
        createdAt: '2024-12-20 14:30:00',
        updatedAt: '2024-12-20 14:30:00'
      }
    ]
    
    total.value = 2
    loading.value = false
  }, 500)
}

// 初始化
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.assessment-plan-management {
  padding: 20px;
  
  .search-card {
    margin-bottom: 20px;
  }
  
  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .scope-info {
      span {
        margin-right: 10px;
        color: #909399;
        font-size: 12px;
      }
    }
    
    .config-tags {
      .el-tag {
        margin-right: 5px;
      }
    }
    
    .el-pagination {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
  
  .grade-distribution {
    .el-input-number {
      width: 80px;
    }
  }
}
</style>