<template>
  <div class="assessment-form-component">
    <!-- 表单头部 -->
    <el-card class="form-header-card">
      <div class="form-header">
        <div class="header-info">
          <h2>{{ formData.title || '绩效考核表' }}</h2>
          <div class="sub-info">
            <span>被考核人：{{ formData.employeeName }}</span>
            <span>考核周期：{{ formData.period }}</span>
            <span>当前步骤：{{ getCurrentStepLabel() }}</span>
          </div>
        </div>
        <div class="header-actions">
          <el-button @click="handleSaveDraft" :icon="Document">保存草稿</el-button>
          <el-button type="primary" @click="handleSubmit" :icon="Check">
            {{ submitButtonText }}
          </el-button>
        </div>
      </div>
      
      <!-- 进度条 -->
      <el-progress 
        :percentage="formProgress" 
        :stroke-width="10"
        :color="progressColor"
        style="margin-top: 20px"
       />
    </el-card>

    <!-- 表单主体 -->
    <el-form ref="formRef" :model="formData" label-position="top">
      <!-- 考核维度 -->
      <div v-for="(dimension, dimIndex) in formData.dimensions" :key="dimension.id">
        <el-card class="dimension-card">
          <template #header>
            <div class="dimension-header">
              <span class="dimension-title">
                {{ dimension.name }}
                <el-tag size="small" type="info">权重：{{ dimension.weight }}%</el-tag>
              </span>
              <span class="dimension-score">
                得分：<strong>{{ calculateDimensionScore(dimension) }}</strong>
              </span>
            </div>
          </template>
          
          <!-- 考核指标 -->
          <div v-for="(metric, metricIndex) in dimension.metrics" :key="metric.id" class="metric-item">
            <div class="metric-header">
              <span class="metric-name">
                {{ metricIndex + 1 }}. {{ metric.name }}
                <el-tag size="small">{{ metric.weight }}%</el-tag>
              </span>
              <el-tooltip :content="metric.description" placement="top" v-if="metric.description">
                <el-icon><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
            
            <!-- 定量指标 -->
            <div v-if="metric.type === 'quantitative'" class="metric-content">
              <el-row :gutter="20" align="middle">
                <el-col :span="8">
                  <el-form-item :label="`实际值(${metric.unit || ''})`">
                    <el-input-number
                      v-model="metric.actualValue"
                      :precision="2"
                      @change="calculateMetricScore(metric)"
                      style="width: 100%"
                      />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="目标值">
                    <el-input-number
                      :model-value="metric.targetValue"
                      disabled
                      style="width: 100%"
                      />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="完成率">
                    <el-progress
                      :percentage="calculateCompletionRate(metric)"
                      :color="getProgressColor(calculateCompletionRate(metric))"
                     />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
            
            <!-- 定性指标 -->
            <div v-else class="metric-content">
              <el-form-item label="评分等级">
                <el-radio-group v-model="metric.scoreLevel" @change="calculateMetricScore(metric)">
                  <el-radio-button
                    v-for="criterion in metric.scoringCriteria"
                    :key="criterion.level"
                    :label="criterion.level"
                  >
                    <div class="score-option">
                      <span>{{ criterion.level }}级</span>
                      <span class="score-value">{{ criterion.score }}分</span>
                    </div>
                  </el-radio-button>
                </el-radio-group>
              </el-form-item>
              
              <!-- 评分标准说明 -->
              <div class="scoring-criteria">
                <el-collapse>
                  <el-collapse-item title="查看评分标准">
                    <div
                      v-for="criterion in metric.scoringCriteria"
                      :key="criterion.level"
                      class="criterion-item"
                      :class="{ active: metric.scoreLevel === criterion.level }"
                    >
                      <el-tag :type="getScoreLevelType(criterion.level)">
                        {{ criterion.level }}级（{{ criterion.score }}分）
                      </el-tag>
                      <span>{{ criterion.description }}</span>
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </div>
            
            <!-- 评价说明 -->
            <el-form-item label="评价说明" style="margin-top: 10px">
              <el-input
                v-model="metric.comment"
                type="textarea"
                :rows="2"
                placeholder="请输入具体的评价说明或改进建议"
                maxlength="500"
                show-word-limit
                />
            </el-form-item>
            
            <!-- 附件上传 -->
            <el-form-item label="佐证材料" v-if="metric.allowAttachment">
              <el-upload
                :file-list="metric.attachments"
                :on-preview="handlePreview"
                :on-remove="(file) => handleRemove(file, metric)"
                :before-upload="(file) => beforeUpload(file, metric)"
                multiple
                :limit="3"
              >
                <el-button size="small" type="primary">上传文件</el-button>
                <template #tip>
                  <div class="el-upload__tip">支持上传图片、文档等，单个文件不超过10MB</div>
                </template>
              </el-upload>
            </el-form-item>
          </div>
        </el-card>
      </div>
      
      <!-- 总体评价 -->
      <el-card class="overall-card">
        <template #header>
          <span>总体评价</span>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="score-summary">
              <div class="score-label">总分</div>
              <div class="score-value">{{ totalScore }}</div>
              <el-rate
                v-model="scoreRate"
                disabled
                :max="5"
                :colors="['#F56C6C', '#E6A23C', '#67C23A']"
               />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="score-summary">
              <div class="score-label">预计等级</div>
              <div class="score-value">
                <el-tag :type="getGradeType(predictedGrade)" size="large">
                  {{ predictedGrade }}级
                </el-tag>
              </div>
              <div class="grade-desc">{{ getGradeDescription(predictedGrade) }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="score-summary">
              <div class="score-label">完成进度</div>
              <div class="score-value">{{ filledMetrics }}/{{ totalMetrics }}</div>
              <el-progress
                type="circle"
                :percentage="Math.round((filledMetrics / totalMetrics) * 100)"
                :width="80"
               />
            </div>
          </el-col>
        </el-row>
        
        <el-divider   />
        
        <!-- 总体评语 -->
        <el-form-item :label="overallCommentLabel">
          <el-input
            v-model="formData.overallComment"
            type="textarea"
            :rows="4"
            :placeholder="overallCommentPlaceholder"
            maxlength="1000"
            show-word-limit
            />
        </el-form-item>
        
        <!-- 改进建议 -->
        <el-form-item label="改进建议" v-if="showImprovements">
          <div class="improvement-list">
            <el-tag
              v-for="(improvement, index) in formData.improvements"
              :key="index"
              closable
              @close="removeImprovement(index)"
              style="margin-right: 10px; margin-bottom: 10px"
            >
              {{ improvement }}
            </el-tag>
            <el-input
              v-if="showImprovementInput"
              ref="improvementInputRef"
              v-model="newImprovement"
              size="small"
              style="width: 200px"
              @keyup.enter="addImprovement"
              @blur="addImprovement"
              />
            <el-button
              v-else
              size="small"
              @click="showImprovementInput = true"
              :icon="Plus"
            >
              添加建议
            </el-button>
          </div>
        </el-form-item>
      </el-card>
    </el-form>

    <!-- 快速导航 -->
    <div class="quick-nav">
      <el-affix :offset="100">
        <el-card>
          <div class="nav-title">快速导航</div>
          <el-menu>
            <el-menu-item
              v-for="(dimension, index) in formData.dimensions"
              :key="dimension.id"
              :index="String(index)"
              @click="scrollToDimension(index)"
            >
              <span>{{ dimension.name }}</span>
              <el-tag size="small" :type="getDimensionStatus(dimension)">
                {{ getDimensionStatusText(dimension) }}
              </el-tag>
            </el-menu-item>
          </el-menu>
        </el-card>
      </el-affix>
    </div>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document, Check, InfoFilled, Plus } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'

// Props
const props = defineProps<{
  taskId: string
  evaluationType: 'self' | 'supervisor' | 'peer' | 'subordinate'
}>()

// 表单数据
const formRef = ref<FormInstance>()
const formData = reactive({
  title: '2025年度绩效考核',
  employeeName: '张三',
  period: '2025-01-01 至 2025-12-31',
  dimensions: [
    {
      id: '1',
      name: 'HrHr工作业绩',
      weight: 50,
      metrics: [
        {
          id: '1-1',
          name: '销售额完成率',
          type: 'quantitative',
          unit: '%',
          weight: 60,
          targetValue: 100,
          actualValue: null,
          score: 0,
          scoringCriteria: [],
          comment: '',
          allowAttachment: true,
          attachments: []
        },
        {
          id: '1-2',
          name: '客户满意度',
          type: 'qualitative',
          weight: 40,
          scoreLevel: null,
          score: 0,
          scoringCriteria: [
            { level: 5, score: 100, description: '客户高度满意，主动推荐' },
            { level: 4, score: 85, description: '客户满意，合作顺畅' },
            { level: 3, score: 70, description: '客户基本满意，偶有抱怨' },
            { level: 2, score: 50, description: '客户不太满意，投诉较多' },
            { level: 1, score: 0, description: '客户很不满意，合作困难' }
          ],
          comment: '',
          allowAttachment: false
        }
      ]
    },
    {
      id: '2',
      name: '工作能力',
      weight: 30,
      metrics: [
        {
          id: '2-1',
          name: '专业技能',
          type: 'qualitative',
          weight: 50,
          scoreLevel: null,
          score: 0,
          scoringCriteria: [
            { level: 5, score: 100, description: '技能精湛，能独立解决复杂问题' },
            { level: 4, score: 85, description: '技能熟练，能处理大部分问题' },
            { level: 3, score: 70, description: '技能合格，能完成基本任务' },
            { level: 2, score: 50, description: '技能欠缺，需要指导' },
            { level: 1, score: 0, description: '技能不足，难以胜任' }
          ],
          comment: ''
        },
        {
          id: '2-2',
          name: '创新能力',
          type: 'qualitative',
          weight: 50,
          scoreLevel: null,
          score: 0,
          scoringCriteria: [
            { level: 5, score: 100, description: '经常提出创新方案并实施' },
            { level: 4, score: 85, description: '能提出有价值的改进建议' },
            { level: 3, score: 70, description: '偶尔有创新想法' },
            { level: 2, score: 50, description: '创新意识薄弱' },
            { level: 1, score: 0, description: '墨守成规，抵触变化' }
          ],
          comment: ''
        }
      ]
    },
    {
      id: '3',
      name: '工作态度',
      weight: 20,
      metrics: [
        {
          id: '3-1',
          name: '团队协作',
          type: 'qualitative',
          weight: 50,
          scoreLevel: null,
          score: 0,
          scoringCriteria: [
            { level: 5, score: 100, description: '团队核心，积极帮助他人' },
            { level: 4, score: 85, description: '配合良好，乐于分享' },
            { level: 3, score: 70, description: '基本配合，完成分内工作' },
            { level: 2, score: 50, description: '配合欠佳，偶有摩擦' },
            { level: 1, score: 0, description: '独来独往，影响团队' }
          ],
          comment: ''
        },
        {
          id: '3-2',
          name: '责任心',
          type: 'qualitative',
          weight: 50,
          scoreLevel: null,
          score: 0,
          scoringCriteria: [
            { level: 5, score: 100, description: '高度负责，主动承担' },
            { level: 4, score: 85, description: '认真负责，按时完成' },
            { level: 3, score: 70, description: '基本负责，偶有延误' },
            { level: 2, score: 50, description: '责任心不强，需要督促' },
            { level: 1, score: 0, description: '推诿责任，消极怠工' }
          ],
          comment: ''
        }
      ]
    }
  ],
  overallComment: '',
  improvements: []
})

// 其他状态
const showImprovementInput = ref(false)
const newImprovement = ref('')
const improvementInputRef = ref()

// 计算属性
const totalScore = computed(() => {
  let total = 0
  formData.dimensions.forEach(dimension => {
    const dimensionScore = calculateDimensionScore(dimension)
    total += dimensionScore * dimension.weight / 100
  })
  return Number(total.toFixed(1))
})

const scoreRate = computed(() => {
  return Math.round(totalScore.value / 20)
})

const predictedGrade = computed(() => {
  if (totalScore.value >= 90) return 'A'
  if (totalScore.value >= 80) return 'B'
  if (totalScore.value >= 70) return 'C'
  if (totalScore.value >= 60) return 'D'
  return 'E'
})

const formProgress = computed(() => {
  if (totalMetrics.value === 0) return 0
  return Math.round((filledMetrics.value / totalMetrics.value) * 100)
})

const progressColor = computed(() => {
  if (formProgress.value >= 80) return '#67c23a'
  if (formProgress.value >= 60) return '#409eff'
  if (formProgress.value >= 40) return '#e6a23c'
  return '#f56c6c'
})

const totalMetrics = computed(() => {
  let count = 0
  formData.dimensions.forEach(dimension => {
    count += dimension.metrics.length
  })
  return count
})

const filledMetrics = computed(() => {
  let count = 0
  formData.dimensions.forEach(dimension => {
    dimension.metrics.forEach(metric => {
      if (metric.type === 'quantitative' && metric.actualValue !== null) {
        count++
      } else if (metric.type === 'qualitative' && metric.scoreLevel !== null) {
        count++
      }
    })
  })
  return count
})

const submitButtonText = computed(() => {
  const map = {
    self: '提交自评',
    supervisor: '提交评价',
    peer: '提交同事评价',
    subordinate: '提交下属评价'
  }
  return map[props.evaluationType] || '提交'
})

const overallCommentLabel = computed(() => {
  const map = {
    self: '自我总结',
    supervisor: '主管评语',
    peer: '同事评价',
    subordinate: '下属评价'
  }
  return map[props.evaluationType] || '总体评价'
})

const overallCommentPlaceholder = computed(() => {
  const map = {
    self: '请总结本考核周期的工作表现、主要成就和不足之处',
    supervisor: '请对员工的整体表现进行评价，指出优点和需要改进的地方',
    peer: '请客观评价同事的工作表现和团队协作情况',
    subordinate: '请评价上级的领导能力和管理风格'
  }
  return map[props.evaluationType] || '请输入总体评价'
})

const showImprovements = computed(() => {
  return props.evaluationType === 'self' || props.evaluationType === 'supervisor'
})

// 方法
const getCurrentStepLabel = () => {
  const map = {
    self: '自我评价',
    supervisor: '主管评价',
    peer: '同事评价',
    subordinate: '下属评价'
  }
  return map[props.evaluationType] || '评价'
}

   
const calculateDimensionScore = (dimension: unknown) => {
  let total = 0
  let weightSum = 0
  
   
  dimension.metrics.forEach((metric: unknown) => {
    if (metric.score > 0) {
      total += metric.score * metric.weight
      weightSum += metric.weight
    }
  })
  
  return weightSum > 0 ? Number((total / weightSum).toFixed(1)) : 0
}

   
const calculateMetricScore = (metric: unknown) => {
  if (metric.type === 'quantitative') {
    // 定量指标：根据完成率计算分数
    const rate = calculateCompletionRate(metric)
    if (rate >= 100) metric.score = 100
    else if (rate >= 90) metric.score = 85
    else if (rate >= 80) metric.score = 70
    else if (rate >= 70) metric.score = 50
    else metric.score = 0
  } else {
    // 定性指标：根据选择的等级获取分数
   
    const criterion = metric.scoringCriteria.find((c: unknown) => c.level === metric.scoreLevel)
    metric.score = criterion ? criterion.score : 0
  }
}

   
const calculateCompletionRate = (metric: unknown) => {
  if (!metric.actualValue || !metric.targetValue) return 0
  return Math.min(Math.round((metric.actualValue / metric.targetValue) * 100), 200)
}

const getProgressColor = (percentage: number) => {
  if (percentage >= 100) return '#67c23a'
  if (percentage >= 80) return '#409eff'
  if (percentage >= 60) return '#e6a23c'
  return '#f56c6c'
}

const getScoreLevelType = (level: number) => {
  if (level >= 5) return 'success'
  if (level >= 4) return 'primary'
  if (level >= 3) return ''
  if (level >= 2) return 'warning'
  return 'danger'
}

const getGradeType = (grade: string) => {
  const map: Record<string, string> = {
    A: 'success',
    B: 'primary',
    C: '',
    D: 'warning',
    E: 'danger'
  }
  return map[grade] || 'info'
}

const getGradeDescription = (grade: string) => {
  const map: Record<string, string> = {
    A: '优秀',
    B: '良好',
    C: '合格',
    D: '需改进',
    E: '不合格'
  }
  return map[grade] || ''
}

   
const getDimensionStatus = (dimension: unknown) => {
  const metrics = dimension.metrics
   
  const filled = metrics.filter((m: unknown) => 
    (m.type === 'quantitative' && m.actualValue !== null) ||
    (m.type === 'qualitative' && m.scoreLevel !== null)
  ).length
  
  if (filled === 0) return 'info'
  if (filled === metrics.length) return 'success'
  return 'warning'
}

   
const getDimensionStatusText = (dimension: unknown) => {
  const metrics = dimension.metrics
   
  const filled = metrics.filter((m: unknown) => 
    (m.type === 'quantitative' && m.actualValue !== null) ||
    (m.type === 'qualitative' && m.scoreLevel !== null)
  ).length
  
  return `${filled}/${metrics.length}`
}

const scrollToDimension = (index: number) => {
  const cards = document.querySelectorAll('.dimension-card')
  if (cards[index]) {
    cards[index].scrollIntoView({ behavior: 'smooth', block: 'start' })
  }
}

   
const handlePreview = (file: unknown) => {
  ElMessage.info(`预览文件：${file.name}`)
}

   
const handleRemove = (file: unknown, metric: unknown) => {
  const index = metric.attachments.indexOf(file)
  if (index > -1) {
    metric.attachments.splice(index, 1)
  }
}

   
const beforeUpload = (file: unknown, metric: unknown) => {
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!')
    return false
  }
  
  // 模拟上传
  metric.attachments.push({
    name: file.name,
    url: URL.createObjectURL(file)
  })
  
  return false // 阻止自动上传
}

const addImprovement = () => {
  if (newImprovement.value.trim()) {
    formData.improvements.push(newImprovement.value.trim())
    newImprovement.value = ''
    showImprovementInput.value = false
  }
}

const removeImprovement = (index: number) => {
  formData.improvements.splice(index, 1)
}

const handleSaveDraft = async () => {
  ElMessage.success('草稿已保存')
}

const handleSubmit = async () => {
  // 检查必填项
  if (filledMetrics.value < totalMetrics.value) {
    await ElMessageBox.alert(
      `还有 ${totalMetrics.value - filledMetrics.value} 个指标未评分，请完成所有评分后再提交。`,
      '提示',
      { type: 'warning' }
    )
    return
  }
  
  if (!formData.overallComment.trim()) {
    await ElMessageBox.alert('请填写总体评价后再提交。', '提示', { type: 'warning' })
    return
  }
  
  try {
    await ElMessageBox.confirm(
      '提交后将无法修改，确定要提交吗？',
      '提交确认',
      { type: 'warning' }
    )
    
    ElMessage.success('提交成功')
  } catch {
    // 用户取消
  }
}

// 初始化
onMounted(() => {
  // 监听输入框显示
  watch(showImprovementInput, (val) => {
    if (val) {
      nextTick(() => {
        improvementInputRef.value?.focus()
      })
    }
  })
})
</script>

<style lang="scss" scoped>
.assessment-form-component {
  display: flex;
  gap: 20px;
  padding: 20px;
  
  .el-form {
    flex: 1;
  }
  
  .form-header-card {
    margin-bottom: 20px;
    
    .form-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      
      .header-info {
        h2 {
          margin: 0 0 10px;
          font-size: 20px;
          color: #303133;
        }
        
        .sub-info {
          display: flex;
          gap: 20px;
          color: #606266;
          font-size: 14px;
        }
      }
    }
  }
  
  .dimension-card {
    margin-bottom: 20px;
    
    .dimension-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .dimension-title {
        font-size: 16px;
        font-weight: bold;
        
        .el-tag {
          margin-left: 10px;
        }
      }
      
      .dimension-score {
        color: #606266;
        
        strong {
          font-size: 18px;
          color: #409eff;
        }
      }
    }
    
    .metric-item {
      padding: 20px;
      margin-bottom: 20px;
      background-color: #f5f7fa;
      border-radius: 8px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .metric-header {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 15px;
        
        .metric-name {
          font-size: 15px;
          font-weight: bold;
          color: #303133;
        }
      }
      
      .metric-content {
        .score-option {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 5px;
          padding: 5px 10px;
          
          .score-value {
            font-size: 12px;
            color: #909399;
          }
        }
        
        .scoring-criteria {
          margin-top: 10px;
          
          .criterion-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px;
            margin-bottom: 5px;
            background-color: white;
            border-radius: 4px;
            
            &.active {
              background-color: #e6f2ff;
              border: 1px solid #409eff;
            }
          }
        }
      }
    }
  }
  
  .overall-card {
    .score-summary {
      text-align: center;
      padding: 20px;
      
      .score-label {
        font-size: 14px;
        color: #909399;
        margin-bottom: 10px;
      }
      
      .score-value {
        font-size: 36px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 10px;
      }
      
      .grade-desc {
        font-size: 14px;
        color: #606266;
        margin-top: 10px;
      }
    }
    
    .improvement-list {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
    }
  }
  
  .quick-nav {
    width: 200px;
    
    .nav-title {
      font-size: 14px;
      font-weight: bold;
      color: #303133;
      margin-bottom: 10px;
    }
    
    :deep(.el-menu) {
      border: none;
      
      .el-menu-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 36px;
        line-height: 36px;
      }
    }
  }
}
</style>