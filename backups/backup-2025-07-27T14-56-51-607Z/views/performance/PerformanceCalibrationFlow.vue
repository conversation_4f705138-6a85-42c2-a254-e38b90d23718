<template>
  <div class="performance-calibration-flow">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>绩效校准流程</h3>
          <el-button 
            type="primary" 
            @click="handleStartCalibration"
            :disabled="selectedItems.length === 0"
          >
            开始校准
          </el-button>
        </div>
      </template>

      <!-- 筛选区域 -->
      <div class="filter-section">
        <el-form :model="filterForm" :inline="true">
          <el-form-item label="部门">
            <el-select v-model="filterForm.department" placeholder="请选择部门">
              <el-option
                v-for="dept in departments"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
               />
            </el-select>
          </el-form-item>
          <el-form-item label="考核周期">
            <el-date-picker
              v-model="filterForm.assessmentPeriod"
              type="month"
              placeholder="选择考核周期"
             />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleFilter">查询</el-button>
            <el-button @click="resetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 待校准列表 -->
      <el-table
        ref="tableRef"
        :data="calibrationList"
        @selection-change="handleSelectionChange"
        v-loading="loading"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="employeeName" label="员工姓名"  />
        <el-table-column prop="department" label="部门"  />
        <el-table-column prop="position" label="岗位"  />
        <el-table-column prop="selfScore" label="自评分数"  />
        <el-table-column prop="supervisorScore" label="上级评分"  />
        <el-table-column prop="preliminaryGrade" label="初步等级"  />
        <el-table-column prop="status" label="状态">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <el-button
              type="text"
              @click="handleViewDetail(scope.row)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 校准对话框 -->
    <el-dialog
      v-model="calibrationDialog.visible"
      title="绩效校准"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="calibration-content">
        <div class="calibration-header">
          <h4>校准会议信息</h4>
          <el-form :model="calibrationDialog.meetingInfo" :inline="true">
            <el-form-item label="会议主题">
              <el-input v-model="calibrationDialog.meetingInfo.subject"   />
            </el-form-item>
            <el-form-item label="会议时间">
              <el-date-picker
                v-model="calibrationDialog.meetingInfo.meetingTime"
                type="datetime"
                placeholder="选择会议时间"
               />
            </el-form-item>
            <el-form-item label="参会人员">
              <el-select
                v-model="calibrationDialog.meetingInfo.attendees"
                multiple
                placeholder="选择参会人员"
              >
                <el-option
                  v-for="user in users"
                  :key="user.id"
                  :label="user.name"
                  :value="user.id"
                 />
              </el-select>
            </el-form-item>
          </el-form>
        </div>

        <div class="calibration-grid">
          <div class="distribution-chart">
            <h5>当前等级分布</h5>
            <div ref="distributionChartRef" style="height: 300px;"></div>
          </div>
          <div class="calibration-matrix">
            <h5>校准矩阵</h5>
            <el-table :data="calibrationMatrix" size="small">
              <el-table-column prop="level" label="等级"  />
              <el-table-column prop="currentCount" label="当前人数"  />
              <el-table-column prop="targetPercentage" label="目标比例"  />
              <el-table-column prop="targetCount" label="目标人数"  />
              <el-table-column prop="deviation" label="偏差">
                <template #default="scope">
                  <span :class="getDeviationClass(scope.row.deviation)">
                    {{ scope.row.deviation > 0 ? '+' : '' }}{{ scope.row.deviation }}
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <div class="calibration-list">
          <h5>员工校准列表</h5>
          <el-table :data="calibrationDialog.employees" size="small">
            <el-table-column prop="name" label="员工姓名"  />
            <el-table-column prop="department" label="部门"  />
            <el-table-column prop="preliminaryGrade" label="初步等级"  />
            <el-table-column prop="adjustedGrade" label="调整后等级">
              <template #default="scope">
                <el-select
                  v-model="scope.row.adjustedGrade"
                  @change="handleGradeChange(scope.row)"
                >
                  <el-option
                    v-for="grade in performanceGrades"
                    :key="grade.value"
                    :label="grade.label"
                    :value="grade.value"
                   />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="reason" label="调整理由">
              <template #default="scope">
                <el-input
                  v-model="scope.row.reason"
                  type="textarea"
                  placeholder="请输入调整理由"
                  :rows="2"
                  />
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="meeting-record">
          <h5>会议记录</h5>
          <el-input
            v-model="calibrationDialog.meetingRecord"
            type="textarea"
            placeholder="请输入会议记录"
            :rows="4"
            />
        </div>
      </div>

      <template #footer>
        <el-button @click="calibrationDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveCalibration">
          保存校准结果
        </el-button>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialog.visible"
      title="员工绩效详情"
      width="60%"
    >
      <div class="detail-content" v-if="detailDialog.data">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="员工姓名">
            {{ detailDialog.data.employeeName }}
          </el-descriptions-item>
          <el-descriptions-item label="部门">
            {{ detailDialog.data.department }}
          </el-descriptions-item>
          <el-descriptions-item label="岗位">
            {{ detailDialog.data.position }}
          </el-descriptions-item>
          <el-descriptions-item label="考核周期">
            {{ detailDialog.data.assessmentPeriod }}
          </el-descriptions-item>
          <el-descriptions-item label="自评分数">
            {{ detailDialog.data.selfScore }}
          </el-descriptions-item>
          <el-descriptions-item label="上级评分">
            {{ detailDialog.data.supervisorScore }}
          </el-descriptions-item>
          <el-descriptions-item label="初步等级">
            {{ detailDialog.data.preliminaryGrade }}
          </el-descriptions-item>
          <el-descriptions-item label="调整后等级">
            {{ detailDialog.data.adjustedGrade || '暂无' }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="evaluation-history" style="margin-top: 20px;">
          <h6>评价历史</h6>
          <el-timeline>
            <el-timeline-item
              v-for="item in detailDialog.data.evaluationHistory"
              :key="item.id"
              :timestamp="item.timestamp"
            >
              <el-card>
                <h4>{{ item.evaluatorName }} - {{ item.type }}</h4>
                <p>{{ item.comments }}</p>
                <p>评分: {{ item.score }}</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as echarts from 'echarts'

// 接口定义
interface CalibrationItem {
  id: string
  employeeName: string
  department: string
  position: string
  selfScore: number
  supervisorScore: number
  preliminaryGrade: string
  adjustedGrade?: string
  status: string
  assessmentPeriod: string
  evaluationHistory: Array<{
    id: string
    evaluatorName: string
    type: string
    comments: string
    score: number
    timestamp: string
  }>
}

interface CalibrationMatrix {
  level: string
  currentCount: number
  targetPercentage: number
  targetCount: number
  deviation: number
}

// 响应式数据
const loading = ref(false)
const selectedItems = ref<CalibrationItem[]>([])
const calibrationList = ref<CalibrationItem[]>([])
const departments = ref([
  { id: '1', name: 'HrHr人事部' },
  { id: '2', name: '技术部' },
  { id: '3', name: '销售部' },
  { id: '4', name: '市场部' }
])

const users = ref([
  { id: '1', name: '张三' },
  { id: '2', name: '李四' },
  { id: '3', name: '王五' }
])

const performanceGrades = ref([
  { value: 'A', label: 'A - 优秀' },
  { value: 'B', label: 'B - 良好' },
  { value: 'C', label: 'C - 合格' },
  { value: 'D', label: 'D - 待改进' }
])

const filterForm = reactive({
  department: '',
  assessmentPeriod: ''
})

const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

const calibrationDialog = reactive({
  visible: false,
  meetingInfo: {
    subject: '',
    meetingTime: '',
    attendees: []
  },
  employees: [] as CalibrationItem[],
  meetingRecord: ''
})

const detailDialog = reactive({
  visible: false,
  data: null as CalibrationItem | null
})

const calibrationMatrix = ref<CalibrationMatrix[]>([
  { level: 'A', currentCount: 5, targetPercentage: 20, targetCount: 4, deviation: 1 },
  { level: 'B', currentCount: 8, targetPercentage: 30, targetCount: 6, deviation: 2 },
  { level: 'C', currentCount: 6, targetPercentage: 40, targetCount: 8, deviation: -2 },
  { level: 'D', currentCount: 1, targetPercentage: 10, targetCount: 2, deviation: -1 }
])

const distributionChartRef = ref<HTMLElement>()

// 方法
const handleSelectionChange = (selection: CalibrationItem[]) => {
  selectedItems.value = selection
}

const handleStartCalibration = () => {
  calibrationDialog.visible = true
  calibrationDialog.employees = [...selectedItems.value]
  
  // 初始化调整后等级为初步等级
  calibrationDialog.employees.forEach(emp => {
    emp.adjustedGrade = emp.preliminaryGrade
  })
  
  nextTick(() => {
    initDistributionChart()
  })
}

const handleFilter = () => {
  // 实现筛选逻辑
  loadCalibrationList()
}

const resetFilter = () => {
  filterForm.department = ''
  filterForm.assessmentPeriod = ''
  loadCalibrationList()
}

const handleViewDetail = (row: CalibrationItem) => {
  detailDialog.data = row
  detailDialog.visible = true
}

const handleGradeChange = (employee: CalibrationItem) => {
  // 重新计算分布
  updateCalibrationMatrix()
}

const handleSaveCalibration = async () => {
  if (!calibrationDialog.meetingInfo.subject) {
    ElMessage.error('请填写会议主题')
    return
  }
  
  if (!calibrationDialog.meetingInfo.meetingTime) {
    ElMessage.error('请选择会议时间')
    return
  }
  
  if (calibrationDialog.meetingInfo.attendees.length === 0) {
    ElMessage.error('请选择参会人员')
    return
  }
  
  try {
    loading.value = true
    
    // 模拟保存校准结果
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('校准结果保存成功')
    calibrationDialog.visible = false
    loadCalibrationList()
  } catch (__error) {
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

const initDistributionChart = () => {
  if (!distributionChartRef.value) return
  
  const chart = echarts.init(distributionChartRef.value)
  
  const option = {
    title: {
      text: '绩效等级分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '等级分布',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 5, name: 'A级' },
          { value: 8, name: 'B级' },
          { value: 6, name: 'C级' },
          { value: 1, name: 'D级' }
        ]
      }
    ]
  }
  
  chart.setOption(option)
}

const updateCalibrationMatrix = () => {
  const gradeCount = { A: 0, B: 0, C: 0, D: 0 }
  
  calibrationDialog.employees.forEach(emp => {
    if (emp.adjustedGrade && gradeCount[emp.adjustedGrade] !== undefined) {
      gradeCount[emp.adjustedGrade]++
    }
  })
  
  calibrationMatrix.value = calibrationMatrix.value.map(item => ({
    ...item,
    currentCount: gradeCount[item.level] || 0,
    deviation: (gradeCount[item.level] || 0) - item.targetCount
  }))
}

const loadCalibrationList = async () => {
  try {
    loading.value = true
    
    // 模拟加载数据
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    calibrationList.value = [
      {
        id: '1',
        employeeName: '张三',
        department: '技术部',
        position: '前端工程师',
        selfScore: 85,
        supervisorScore: 88,
        preliminaryGrade: 'B',
        status: 'pending',
        assessmentPeriod: '2024-12',
        evaluationHistory: [
          {
            id: '1',
            evaluatorName: '李四',
            type: '上级评价',
            comments: '工作认真负责，技术能力强',
            score: 88,
            timestamp: '2024-12-20 10:00'
          }
        ]
      },
      {
        id: '2',
        employeeName: '王五',
        department: '销售部',
        position: '销售经理',
        selfScore: 92,
        supervisorScore: 90,
        preliminaryGrade: 'A',
        status: 'pending',
        assessmentPeriod: '2024-12',
        evaluationHistory: [
          {
            id: '2',
            evaluatorName: '赵六',
            type: '上级评价',
            comments: '销售业绩优秀，团队管理能力强',
            score: 90,
            timestamp: '2024-12-20 14:00'
          }
        ]
      }
    ]
    
    pagination.total = calibrationList.value.length
  } catch (__error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  loadCalibrationList()
}

const handleCurrentChange = (current: number) => {
  pagination.current = current
  loadCalibrationList()
}

const getStatusType = (status: string) => {
  const statusMap = {
    pending: 'warning',
    calibrated: 'success',
    rejected: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap = {
    pending: '待校准',
    calibrated: '已校准',
    rejected: '已拒绝'
  }
  return statusMap[status] || '未知'
}

const getDeviationClass = (deviation: number) => {
  if (deviation > 0) return 'text-danger'
  if (deviation < 0) return 'text-warning'
  return 'text-success'
}

// 生命周期
onMounted(() => {
  loadCalibrationList()
})
</script>

<style scoped>
.performance-calibration-flow {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-section {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.calibration-content {
  padding: 20px;
}

.calibration-header {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.calibration-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.calibration-list {
  margin-bottom: 20px;
}

.meeting-record {
  margin-top: 20px;
}

.text-danger {
  color: #f56c6c;
}

.text-warning {
  color: #e6a23c;
}

.text-success {
  color: #67c23a;
}

.detail-content {
  padding: 20px;
}

.evaluation-history {
  margin-top: 20px;
}

.evaluation-history h6 {
  margin-bottom: 10px;
}
</style>