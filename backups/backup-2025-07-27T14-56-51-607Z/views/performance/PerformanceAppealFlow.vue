<template>
  <div class="performance-appeal-flow">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>绩效申诉流程</h3>
          <el-button type="primary" @click="handleCreateAppeal">
            发起申诉
          </el-button>
        </div>
      </template>

      <!-- 筛选区域 -->
      <div class="filter-section">
        <el-form :model="filterForm" :inline="true">
          <el-form-item label="申诉状态">
            <el-select v-model="filterForm.status" placeholder="请选择状态">
              <el-option label="全部" value=""  />
              <el-option label="待处理" value="pending"  />
              <el-option label="处理中" value="processing"  />
              <el-option label="已完成" value="completed"  />
              <el-option label="已拒绝" value="rejected"  />
            </el-select>
          </el-form-item>
          <el-form-item label="申诉类型">
            <el-select v-model="filterForm.appealType" placeholder="请选择类型">
              <el-option label="全部" value=""  />
              <el-option label="评分异议" value="score"  />
              <el-option label="等级异议" value="grade"  />
              <el-option label="流程异议" value="process"  />
              <el-option label="其他" value="other"  />
            </el-select>
          </el-form-item>
          <el-form-item label="考核周期">
            <el-date-picker
              v-model="filterForm.assessmentPeriod"
              type="month"
              placeholder="选择考核周期"
             />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleFilter">查询</el-button>
            <el-button @click="resetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 申诉列表 -->
      <el-table
        :data="appealList"
        v-loading="loading"
        @row-click="handleRowClick"
      >
        <el-table-column prop="appealNo" label="申诉编号"  />
        <el-table-column prop="employeeName" label="申诉人"  />
        <el-table-column prop="department" label="部门"  />
        <el-table-column prop="appealType" label="申诉类型">
          <template #default="scope">
            {{ getAppealTypeText(scope.row.appealType) }}
          </template>
        </el-table-column>
        <el-table-column prop="originalGrade" label="原等级"  />
        <el-table-column prop="appealGrade" label="申诉等级"  />
        <el-table-column prop="createTime" label="申诉时间"  />
        <el-table-column prop="status" label="状态">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <el-button type="text" @click.stop="handleViewDetail(scope.row)">
              查看详情
            </el-button>
            <el-button 
              v-if="scope.row.status === 'pending'" 
              type="text" 
              @click.stop="handleProcess(scope.row)"
            >
              处理
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
         />
      </div>
    </el-card>

    <!-- 申诉创建对话框 -->
    <el-dialog
      v-model="appealDialog.visible"
      title="发起绩效申诉"
      width="60%"
      :close-on-click-modal="false"
    >
      <el-form
        ref="appealFormRef"
        :model="appealDialog.form"
        :rules="appealDialog.rules"
        label-width="120px"
      >
        <el-form-item label="考核周期" prop="assessmentPeriod">
          <el-date-picker
            v-model="appealDialog.form.assessmentPeriod"
            type="month"
            placeholder="选择考核周期"
            style="width: 100%"
           />
        </el-form-item>
        <el-form-item label="申诉类型" prop="appealType">
          <el-select
            v-model="appealDialog.form.appealType"
            placeholder="请选择申诉类型"
            style="width: 100%"
          >
            <el-option label="评分异议" value="score"  />
            <el-option label="等级异议" value="grade"  />
            <el-option label="流程异议" value="process"  />
            <el-option label="其他" value="other"  />
          </el-select>
        </el-form-item>
        <el-form-item label="原始等级" prop="originalGrade">
          <el-input v-model="appealDialog.form.originalGrade" readonly   />
        </el-form-item>
        <el-form-item label="申诉等级" prop="appealGrade">
          <el-select
            v-model="appealDialog.form.appealGrade"
            placeholder="请选择期望等级"
            style="width: 100%"
          >
            <el-option
              v-for="grade in performanceGrades"
              :key="grade.value"
              :label="grade.label"
              :value="grade.value"
             />
          </el-select>
        </el-form-item>
        <el-form-item label="申诉理由" prop="reason">
          <el-input
            v-model="appealDialog.form.reason"
            type="textarea"
            :rows="4"
            placeholder="请详细说明申诉理由"
            />
        </el-form-item>
        <el-form-item label="支撑材料">
          <el-upload
            v-model:file-list="appealDialog.form.attachments"
            :action="uploadUrl"
            multiple
            :limit="5"
            :before-upload="beforeUpload"
          >
            <el-button type="primary">上传文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                只能上传jpg/png/pdf文件，且不超过2MB，最多5个文件
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="appealDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmitAppeal">
          提交申诉
        </el-button>
      </template>
    </el-dialog>

    <!-- 申诉处理对话框 -->
    <el-dialog
      v-model="processDialog.visible"
      title="处理绩效申诉"
      width="70%"
      :close-on-click-modal="false"
    >
      <div class="process-content" v-if="processDialog.data">
        <div class="appeal-info">
          <h4>申诉信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="申诉编号">
              {{ processDialog.data.appealNo }}
            </el-descriptions-item>
            <el-descriptions-item label="申诉人">
              {{ processDialog.data.employeeName }}
            </el-descriptions-item>
            <el-descriptions-item label="部门">
              {{ processDialog.data.department }}
            </el-descriptions-item>
            <el-descriptions-item label="申诉类型">
              {{ getAppealTypeText(processDialog.data.appealType) }}
            </el-descriptions-item>
            <el-descriptions-item label="原始等级">
              {{ processDialog.data.originalGrade }}
            </el-descriptions-item>
            <el-descriptions-item label="申诉等级">
              {{ processDialog.data.appealGrade }}
            </el-descriptions-item>
            <el-descriptions-item label="申诉时间" span="2">
              {{ processDialog.data.createTime }}
            </el-descriptions-item>
          </el-descriptions>
          
          <div class="appeal-reason" style="margin-top: 15px;">
            <h5>申诉理由</h5>
            <p>{{ processDialog.data.reason }}</p>
          </div>
          
          <div class="appeal-attachments" style="margin-top: 15px;" v-if="processDialog.data.attachments?.length">
            <h5>支撑材料</h5>
            <el-space wrap>
              <el-button
                v-for="file in processDialog.data.attachments"
                :key="file.uid"
                type="text"
                @click="handleDownloadFile(file)"
              >
                {{ file.name }}
              </el-button>
            </el-space>
          </div>
        </div>

        <div class="original-evaluation" style="margin-top: 20px;">
          <h4>原始评价信息</h4>
          <el-table :data="processDialog.data.originalEvaluations" size="small">
            <el-table-column prop="evaluatorName" label="评价人"  />
            <el-table-column prop="evaluatorRole" label="角色"  />
            <el-table-column prop="score" label="评分"  />
            <el-table-column prop="grade" label="等级"  />
            <el-table-column prop="comments" label="评价意见" show-overflow-tooltip  />
            <el-table-column prop="evaluateTime" label="评价时间"  />
          </el-table>
        </div>

        <div class="process-form" style="margin-top: 20px;">
          <h4>处理意见</h4>
          <el-form :model="processDialog.form" label-width="120px">
            <el-form-item label="处理结果" required>
              <el-radio-group v-model="processDialog.form.result">
                <el-radio label="approve">同意申诉</el-radio>
                <el-radio label="reject">拒绝申诉</el-radio>
                <el-radio label="partial">部分同意</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item 
              v-if="processDialog.form.result === 'approve' || processDialog.form.result === 'partial'"
              label="调整后等级"
            >
              <el-select
                v-model="processDialog.form.adjustedGrade"
                placeholder="请选择调整后等级"
                style="width: 200px"
              >
                <el-option
                  v-for="grade in performanceGrades"
                  :key="grade.value"
                  :label="grade.label"
                  :value="grade.value"
                 />
              </el-select>
            </el-form-item>
            <el-form-item label="处理意见" required>
              <el-input
                v-model="processDialog.form.comments"
                type="textarea"
                :rows="4"
                placeholder="请输入处理意见"
                />
            </el-form-item>
            <el-form-item label="处理人">
              <el-input v-model="processDialog.form.processor" readonly   />
            </el-form-item>
          </el-form>
        </div>
      </div>
      <template #footer>
        <el-button @click="processDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmitProcess">
          提交处理结果
        </el-button>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialog.visible"
      title="申诉详情"
      width="70%"
    >
      <div class="detail-content" v-if="detailDialog.data">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="申诉编号">
            {{ detailDialog.data.appealNo }}
          </el-descriptions-item>
          <el-descriptions-item label="申诉人">
            {{ detailDialog.data.employeeName }}
          </el-descriptions-item>
          <el-descriptions-item label="部门">
            {{ detailDialog.data.department }}
          </el-descriptions-item>
          <el-descriptions-item label="申诉类型">
            {{ getAppealTypeText(detailDialog.data.appealType) }}
          </el-descriptions-item>
          <el-descriptions-item label="原始等级">
            {{ detailDialog.data.originalGrade }}
          </el-descriptions-item>
          <el-descriptions-item label="申诉等级">
            {{ detailDialog.data.appealGrade }}
          </el-descriptions-item>
          <el-descriptions-item label="申诉时间">
            {{ detailDialog.data.createTime }}
          </el-descriptions-item>
          <el-descriptions-item label="处理状态">
            <el-tag :type="getStatusType(detailDialog.data.status)">
              {{ getStatusText(detailDialog.data.status) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="process-timeline" style="margin-top: 20px;">
          <h5>处理流程</h5>
          <el-timeline>
            <el-timeline-item
              v-for="item in detailDialog.data.processHistory"
              :key="item.id"
              :timestamp="item.timestamp"
              :type="item.type"
            >
              <el-card>
                <h6>{{ item.title }}</h6>
                <p>{{ item.description }}</p>
                <p v-if="item.processor">处理人: {{ item.processor }}</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getCurrentInstance } from 'vue'

// 接口定义
interface AppealItem {
  id: string
  appealNo: string
  employeeName: string
  department: string
  appealType: string
  originalGrade: string
  appealGrade: string
  reason: string
  createTime: string
  status: string
   
  attachments?: unknown[]
   
  originalEvaluations?: unknown[]
   
  processHistory?: unknown[]
}

// 响应式数据
const loading = ref(false)
const appealList = ref<AppealItem[]>([])
const uploadUrl = ref('/api/upload')

const performanceGrades = ref([
  { value: 'A', label: 'A - 优秀' },
  { value: 'B', label: 'B - 良好' },
  { value: 'C', label: 'C - 合格' },
  { value: 'D', label: 'D - 待改进' }
])

const filterForm = reactive({
  status: '',
  appealType: '',
  assessmentPeriod: ''
})

const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

const appealDialog = reactive({
  visible: false,
  form: {
    assessmentPeriod: '',
    appealType: '',
    originalGrade: 'B',
    appealGrade: '',
    reason: '',
    attachments: []
  },
  rules: {
    assessmentPeriod: [{ required: true, message: '请选择考核周期', trigger: 'change' }],
    appealType: [{ required: true, message: '请选择申诉类型', trigger: 'change' }],
    appealGrade: [{ required: true, message: '请选择申诉等级', trigger: 'change' }],
    reason: [{ required: true, message: '请输入申诉理由', trigger: 'blur' }]
  }
})

const processDialog = reactive({
  visible: false,
  data: null as AppealItem | null,
  form: {
    result: '',
    adjustedGrade: '',
    comments: '',
    processor: '当前用户'
  }
})

const detailDialog = reactive({
  visible: false,
  data: null as AppealItem | null
})

const appealFormRef = ref()

// 方法
const handleCreateAppeal = () => {
  appealDialog.visible = true
  resetAppealForm()
}

const resetAppealForm = () => {
  appealDialog.form = {
    assessmentPeriod: '',
    appealType: '',
    originalGrade: 'B',
    appealGrade: '',
    reason: '',
    attachments: []
  }
}

const handleSubmitAppeal = async () => {
  if (!appealFormRef.value) return
  
  try {
    await appealFormRef.value.validate()
    loading.value = true
    
    // 模拟提交申诉
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('申诉提交成功')
    appealDialog.visible = false
    loadAppealList()
  } catch (__error) {
    ElMessage.error('提交失败')
  } finally {
    loading.value = false
  }
}

const handleProcess = (row: AppealItem) => {
  processDialog.data = row
  processDialog.form = {
    result: '',
    adjustedGrade: '',
    comments: '',
    processor: '当前用户'
  }
  processDialog.visible = true
}

const handleSubmitProcess = async () => {
  if (!processDialog.form.result) {
    ElMessage.error('请选择处理结果')
    return
  }
  
  if (!processDialog.form.comments) {
    ElMessage.error('请输入处理意见')
    return
  }
  
  try {
    loading.value = true
    
    // 模拟提交处理结果
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('处理结果提交成功')
    processDialog.visible = false
    loadAppealList()
  } catch (__error) {
    ElMessage.error('提交失败')
  } finally {
    loading.value = false
  }
}

const handleViewDetail = (row: AppealItem) => {
  detailDialog.data = {
    ...row,
    processHistory: [
      {
        id: '1',
        title: '申诉提交',
        description: '员工提交绩效申诉申请',
        timestamp: row.createTime,
        type: 'primary'
      },
      {
        id: '2',
        title: '申诉受理',
        description: '人事部门受理申诉申请',
        timestamp: '2024-12-21 10:00',
        type: 'success',
        processor: '人事专员'
      }
    ]
  }
  detailDialog.visible = true
}

const handleRowClick = (row: AppealItem) => {
  handleViewDetail(row)
}

const handleFilter = () => {
  loadAppealList()
}

const resetFilter = () => {
  filterForm.status = ''
  filterForm.appealType = ''
  filterForm.assessmentPeriod = ''
  loadAppealList()
}

const beforeUpload = (file: File) => {
  const isValidType = ['image/jpeg', 'image/png', 'application/pdf'].includes(file.type)
  const isLt2M = file.size / 1024 / 1024 < 2
  
  if (!isValidType) {
    ElMessage.error('只能上传JPG/PNG/PDF格式的文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('文件大小不能超过2MB!')
    return false
  }
  
  return true
}

   
const handleDownloadFile = (file: unknown) => {
  // 模拟文件下载
  ElMessage.info(`下载文件: ${file.name}`)
}

const loadAppealList = async () => {
  try {
    loading.value = true
    
    // 模拟加载数据
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    appealList.value = [
      {
        id: '1',
        appealNo: 'AP202412001',
        employeeName: '张三',
        department: '技术部',
        appealType: 'grade',
        originalGrade: 'B',
        appealGrade: 'A',
        reason: '本人在本考核周期内完成了多项重要技术项目，认为评级偏低',
        createTime: '2024-12-20 14:30',
        status: 'pending',
        attachments: [
          { uid: '1', name: 'HrHr项目完成证明.pdf' },
          { uid: '2', name: '技术贡献总结.docx' }
        ],
        originalEvaluations: [
          {
            evaluatorName: '李四',
            evaluatorRole: '直接上级',
            score: 85,
            grade: 'B',
            comments: '工作表现良好，但还有提升空间',
            evaluateTime: '2024-12-15 16:00'
          }
        ]
      },
      {
        id: '2',
        appealNo: 'AP202412002',
        employeeName: '王五',
        department: '销售部',
        appealType: 'score',
        originalGrade: 'C',
        appealGrade: 'B',
        reason: '评分标准不够明确，存在主观性',
        createTime: '2024-12-19 09:15',
        status: 'processing',
        attachments: [],
        originalEvaluations: [
          {
            evaluatorName: '赵六',
            evaluatorRole: '直接上级',
            score: 75,
            grade: 'C',
            comments: '销售业绩一般，需要加强',
            evaluateTime: '2024-12-14 11:30'
          }
        ]
      }
    ]
    
    pagination.total = appealList.value.length
  } catch (__error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  loadAppealList()
}

const handleCurrentChange = (current: number) => {
  pagination.current = current
  loadAppealList()
}

const getAppealTypeText = (type: string) => {
  const typeMap = {
    score: '评分异议',
    grade: '等级异议',
    process: '流程异议',
    other: '其他'
  }
  return typeMap[type] || '未知'
}

const getStatusType = (status: string) => {
  const statusMap = {
    pending: 'warning',
    processing: 'primary',
    completed: 'success',
    rejected: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    rejected: '已拒绝'
  }
  return statusMap[status] || '未知'
}

// 生命周期
onMounted(() => {
  loadAppealList()
})
</script>

<style scoped>
.performance-appeal-flow {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-section {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.process-content {
  padding: 20px;
}

.appeal-info,
.original-evaluation,
.process-form {
  margin-bottom: 20px;
}

.appeal-reason,
.appeal-attachments {
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.appeal-reason h5,
.appeal-attachments h5 {
  margin-bottom: 10px;
  color: #606266;
}

.detail-content {
  padding: 20px;
}

.process-timeline h5 {
  margin-bottom: 15px;
}
</style>