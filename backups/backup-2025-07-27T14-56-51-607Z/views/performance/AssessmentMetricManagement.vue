<template>
  <div class="assessment-metric-management">
    <!-- 搜索栏 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="指标名称">
          <el-input v-model="searchForm.name" placeholder="请输入指标名称" clearable   />
        </el-form-item>
        <el-form-item label="指标类别">
          <el-select v-model="searchForm.category" placeholder="请选择" clearable>
            <el-option label="KPI指标" value="kpi"  />
            <el-option label="OKR目标" value="okr"  />
            <el-option label="行为指标" value="behavior"  />
            <el-option label="技能指标" value="skill"  />
            <el-option label="自定义" value="custom"  />
          </el-select>
        </el-form-item>
        <el-form-item label="指标类型">
          <el-select v-model="searchForm.type" placeholder="请选择" clearable>
            <el-option label="定量指标" value="quantitative"  />
            <el-option label="定性指标" value="qualitative"  />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择" clearable>
            <el-option label="启用" value="active"  />
            <el-option label="停用" value="inactive"  />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :icon="Search">查询</el-button>
          <el-button @click="handleReset" :icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作栏 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>考核指标库</span>
          <div>
            <el-button type="primary" @click="handleCreate" :icon="Plus">新建指标</el-button>
            <el-button @click="handleBatchImport" :icon="Upload">批量导入</el-button>
            <el-button @click="handleExport" :icon="Download">导出</el-button>
          </div>
        </div>
      </template>

      <!-- 数据表格 -->
      <el-table
        :data="tableData"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="name" label="指标名称" min-width="150" show-overflow-tooltip  />
        <el-table-column prop="category" label="指标类别" width="100">
          <template #default="{ row }">
            <el-tag :type="getCategoryType(row.category)">
              {{ getCategoryLabel(row.category) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="指标类型" width="100">
          <template #default="{ row }">
            <el-tag :type="row.type === 'quantitative' ? 'success' : 'warning'">
              {{ row.type === 'quantitative' ? '定量' : '定性' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="unit" label="单位" width="80" align="center">
          <template #default="{ row }">
            {{ row.unit || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="targetValue" label="目标值" width="100" align="center">
          <template #default="{ row }">
            {{ row.targetValue ? row.targetValue + (row.unit || '') : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="weight" label="权重" width="80" align="center">
          <template #default="{ row }">
            {{ row.weight }}%
          </template>
        </el-table-column>
        <el-table-column label="评分标准" min-width="200">
          <template #default="{ row }">
            <div class="scoring-preview">
              共{{ row.scoringCriteria.length }}个等级
              <el-button link type="primary" @click="viewScoringCriteria(row)">查看</el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="适用范围" min-width="150">
          <template #default="{ row }">
            <div class="scope-tags">
              <el-tag v-if="row.applicableTo.departments?.length" size="small">
                {{ row.applicableTo.departments.length }}个部门
              </el-tag>
              <el-tag v-if="row.applicableTo.positions?.length" size="small">
                {{ row.applicableTo.positions.length }}个岗位
              </el-tag>
              <el-tag v-if="row.applicableTo.levels?.length" size="small">
                {{ row.applicableTo.levels.length }}个级别
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              active-value="active"
              inactive-value="inactive"
              @change="handleStatusChange(row)"
             />
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="150">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleEdit(row)">编辑</el-button>
            <el-button link type="primary" @click="handleCopy(row)">复制</el-button>
            <el-button link type="danger" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
    </el-card>

    <!-- 新建/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="指标名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入指标名称"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="指标类别" prop="category">
              <el-select v-model="form.category" placeholder="请选择指标类别">
                <el-option label="KPI指标" value="kpi"  />
                <el-option label="OKR目标" value="okr"  />
                <el-option label="行为指标" value="behavior"  />
                <el-option label="技能指标" value="skill"  />
                <el-option label="自定义" value="custom"  />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="指标类型" prop="type">
              <el-radio-group v-model="form.type" @change="handleTypeChange">
                <el-radio value="quantitative">定量指标</el-radio>
                <el-radio value="qualitative">定性指标</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="权重" prop="weight">
              <el-input-number
                v-model="form.weight"
                :min="0"
                :max="100"
                :step="5"
                placeholder="权重"
                />
              <span style="margin-left: 10px">%</span>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" v-if="form.type === 'quantitative'">
          <el-col :span="12">
            <el-form-item label="单位">
              <el-input v-model="form.unit" placeholder="如：个、%、元等"   />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="目标值">
              <el-input-number
                v-model="form.targetValue"
                :precision="2"
                placeholder="目标值"
                style="width: 100%"
                />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="指标说明">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入指标说明"
            />
        </el-form-item>
        
        <el-divider content-position="left">评分标准设置</el-divider>
        
        <el-form-item label="评分等级" required>
          <div class="scoring-criteria-list">
            <div
              v-for="(criterion, index) in form.scoringCriteria"
              :key="index"
              class="criterion-item"
            >
              <el-row :gutter="10" align="middle">
                <el-col :span="3">
                  <el-input v-model.number="criterion.level" placeholder="等级" disabled   />
                </el-col>
                <el-col :span="4" v-if="form.type === 'quantitative'">
                  <el-input-number
                    v-model="criterion.minValue"
                    placeholder="最小值"
                    :precision="2"
                    style="width: 100%"
                    />
                </el-col>
                <el-col :span="4" v-if="form.type === 'quantitative'">
                  <el-input-number
                    v-model="criterion.maxValue"
                    placeholder="最大值"
                    :precision="2"
                    style="width: 100%"
                    />
                </el-col>
                <el-col :span="8">
                  <el-input v-model="criterion.description" placeholder="等级描述"   />
                </el-col>
                <el-col :span="3">
                  <el-input-number
                    v-model="criterion.score"
                    :min="0"
                    :max="100"
                    placeholder="得分"
                    style="width: 100%"
                    />
                </el-col>
                <el-col :span="2">
                  <el-button
                    type="danger"
                    :icon="Delete"
                    circle
                    size="small"
                    @click="removeCriterion(index)"
                    :disabled="form.scoringCriteria.length <= 2"
                    />
                </el-col>
              </el-row>
            </div>
            <el-button type="primary" @click="addCriterion" :icon="Plus" style="width: 100%">
              添加评分等级
            </el-button>
          </div>
        </el-form-item>
        
        <el-divider content-position="left">数据来源配置</el-divider>
        
        <el-form-item label="数据来源">
          <el-radio-group v-model="form.dataSource.type">
            <el-radio value="manual">手动录入</el-radio>
            <el-radio value="system">系统采集</el-radio>
            <el-radio value="formula">公式计算</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="系统字段" v-if="form.dataSource.type === 'system'">
          <el-select v-model="form.dataSource.systemField" placeholder="选择系统字段">
            <el-option label="销售额" value="sales_amount"  />
            <el-option label="客户数量" value="customer_count"  />
            <el-option label="项目完成率" value="project_completion"  />
            <el-option label="出勤率" value="attendance_rate"  />
          </el-select>
        </el-form-item>
        
        <el-form-item label="计算公式" v-if="form.dataSource.type === 'formula'">
          <el-input
            v-model="form.dataSource.formula"
            placeholder="输入计算公式，如：{销售额}/{目标额}*100"
            />
        </el-form-item>
        
        <el-divider content-position="left">适用范围设置</el-divider>
        
        <el-form-item label="适用部门">
          <el-tree-select
            v-model="form.applicableTo.departments"
            :data="departmentTree"
            multiple
            :render-after-expand="false"
            show-checkbox
            check-strictly
            placeholder="不选择则适用于所有部门"
            style="width: 100%"
           />
        </el-form-item>
        
        <el-form-item label="适用岗位">
          <el-select v-model="form.applicableTo.positions" multiple placeholder="不选择则适用于所有岗位">
            <el-option v-for="pos in positions" :key="pos.id" :label="pos.name" :value="pos.id"  />
          </el-select>
        </el-form-item>
        
        <el-form-item label="适用级别">
          <el-select v-model="form.applicableTo.levels" multiple placeholder="不选择则适用于所有级别">
            <el-option label="初级" value="junior"  />
            <el-option label="中级" value="middle"  />
            <el-option label="高级" value="senior"  />
            <el-option label="专家" value="expert"  />
            <el-option label="管理层" value="management"  />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </template>
    </el-dialog>

    <!-- 评分标准预览对话框 -->
    <el-dialog
      v-model="criteriaVisible"
      title="评分标准详情"
      width="600px"
    >
      <el-table :data="currentCriteria" stripe>
        <el-table-column prop="level" label="等级" width="80" align="center"  />
        <el-table-column label="分值范围" width="150" align="center" v-if="criteriaType === 'quantitative'">
          <template #default="{ row }">
            {{ row.minValue }} - {{ row.maxValue }}
          </template>
        </el-table-column>
        <el-table-column prop="description" label="等级描述"  />
        <el-table-column prop="score" label="得分" width="80" align="center"  />
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus, Delete, Download, Upload } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import type { AssessmentMetric, ScoringCriterion } from '@/types/performance'

// 搜索表单
const searchForm = reactive({
  name: '',
  category: '',
  type: '',
  status: ''
})

// 表格数据
const loading = ref(false)
const tableData = ref<AssessmentMetric[]>([])
const multipleSelection = ref<AssessmentMetric[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 对话框
const dialogVisible = ref(false)
const dialogTitle = computed(() => form.id ? '编辑考核指标' : '新建考核指标')
const formRef = ref<FormInstance>()
const form = reactive<Partial<AssessmentMetric>>({
  name: '',
  category: 'kpi',
  type: 'quantitative',
  unit: '',
  targetValue: undefined,
  weight: 20,
  description: '',
  scoringCriteria: [
    { level: 5, minValue: 90, maxValue: 100, description: '优秀', score: 100 },
    { level: 4, minValue: 80, maxValue: 89, description: '良好', score: 85 },
    { level: 3, minValue: 70, maxValue: 79, description: '合格', score: 70 },
    { level: 2, minValue: 60, maxValue: 69, description: '需改进', score: 50 },
    { level: 1, minValue: 0, maxValue: 59, description: '不合格', score: 0 }
  ],
  dataSource: {
    type: 'manual'
  },
  status: 'active',
  applicableTo: {
    departments: [],
    positions: [],
    levels: []
  }
})

// 评分标准预览
const criteriaVisible = ref(false)
const currentCriteria = ref<ScoringCriterion[]>([])
const criteriaType = ref('')

// 辅助数据
const departmentTree = ref([
  {
    value: 'tech',
    label: '技术部',
    children: [
      { value: 'frontend', label: '前端组' },
      { value: 'backend', label: '后端组' },
      { value: 'test', label: '测试组' }
    ]
  },
  {
    value: 'product',
    label: '产品部'
  },
  {
    value: 'hr',
    label: '人力资源部'
  }
])

const positions = ref([
  { id: 'p1', name: 'HrHr前端工程师' },
  { id: 'p2', name: '后端工程师' },
  { id: 'p3', name: '产品经理' },
  { id: 'p4', name: '项目经理' },
  { id: 'p5', name: '技术总监' }
])

// 表单验证规则
const rules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入指标名称', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择指标类别', trigger: 'change' }
  ],
  type: [
    { required: true, message: '请选择指标类型', trigger: 'change' }
  ],
  weight: [
    { required: true, message: '请设置权重', trigger: 'blur' }
  ]
})

// 获取类别标签
const getCategoryLabel = (category: string) => {
  const map: Record<string, string> = {
    kpi: 'KPI',
    okr: 'OKR',
    behavior: '行为',
    skill: '技能',
    custom: '自定义'
  }
  return map[category] || category
}

// 获取类别类型
const getCategoryType = (category: string) => {
  const map: Record<string, string> = {
    kpi: 'primary',
    okr: 'success',
    behavior: 'warning',
    skill: 'info',
    custom: ''
  }
  return map[category] || ''
}

// 搜索
const handleSearch = () => {
  loadData()
}

// 重置
const handleReset = () => {
  searchForm.name = ''
  searchForm.category = ''
  searchForm.type = ''
  searchForm.status = ''
  loadData()
}

// 新建
const handleCreate = () => {
  resetForm()
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row: AssessmentMetric) => {
  Object.assign(form, row)
  dialogVisible.value = true
}

// 复制
const handleCopy = (row: AssessmentMetric) => {
  const newMetric = { ...row, id: '', name: `${row.name} - 副本` }
  delete newMetric.id
  Object.assign(form, newMetric)
  dialogVisible.value = true
}

// 删除
const handleDelete = async (row: AssessmentMetric) => {
  try {
    await ElMessageBox.confirm('确定要删除该考核指标吗？', '提示', { type: 'warning' })
    const index = tableData.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      tableData.value.splice(index, 1)
    }
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

// 批量导入
const handleBatchImport = () => {
  ElMessage.info('请选择Excel文件进行导入')
}

// 导出
const handleExport = () => {
  ElMessage.success('正在导出考核指标...')
}

// 状态变更
const handleStatusChange = (row: AssessmentMetric) => {
  ElMessage.success(`指标已${row.status === 'active' ? '启用' : '停用'}`)
}

// 查看评分标准
const viewScoringCriteria = (row: AssessmentMetric) => {
  currentCriteria.value = row.scoringCriteria
  criteriaType.value = row.type
  criteriaVisible.value = true
}

// 类型变更
const handleTypeChange = (val: string) => {
  if (val === 'qualitative') {
    // 定性指标不需要数值范围
    form.scoringCriteria.forEach(criterion => {
      delete criterion.minValue
      delete criterion.maxValue
    })
  } else {
    // 定量指标需要数值范围
    form.scoringCriteria.forEach((criterion, index) => {
      criterion.minValue = (4 - index) * 20
      criterion.maxValue = (5 - index) * 20
    })
  }
}

// 添加评分等级
const addCriterion = () => {
  const level = form.scoringCriteria.length + 1
  form.scoringCriteria.push({
    level,
    minValue: 0,
    maxValue: 0,
    description: '',
    score: 0
  })
}

// 删除评分等级
const removeCriterion = (index: number) => {
  form.scoringCriteria.splice(index, 1)
  // 重新编号
  form.scoringCriteria.forEach((criterion, idx) => {
    criterion.level = form.scoringCriteria.length - idx
  })
}

// 保存
const handleSave = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate()
  
  ElMessage.success(form.id ? '修改成功' : '创建成功')
  dialogVisible.value = false
  loadData()
}

// 选择变化
const handleSelectionChange = (val: AssessmentMetric[]) => {
  multipleSelection.value = val
}

// 分页变化
const handleSizeChange = (val: number) => {
  pageSize.value = val
  loadData()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  loadData()
}

// 重置表单
const resetForm = () => {
  form.id = ''
  form.name = ''
  form.category = 'kpi'
  form.type = 'quantitative'
  form.unit = ''
  form.targetValue = undefined
  form.weight = 20
  form.description = ''
  form.scoringCriteria = [
    { level: 5, minValue: 90, maxValue: 100, description: '优秀', score: 100 },
    { level: 4, minValue: 80, maxValue: 89, description: '良好', score: 85 },
    { level: 3, minValue: 70, maxValue: 79, description: '合格', score: 70 },
    { level: 2, minValue: 60, maxValue: 69, description: '需改进', score: 50 },
    { level: 1, minValue: 0, maxValue: 59, description: '不合格', score: 0 }
  ]
  form.dataSource = { type: 'manual' }
  form.status = 'active'
  form.applicableTo = {
    departments: [],
    positions: [],
    levels: []
  }
}

// 加载数据
const loadData = async () => {
  loading.value = true
  
  // 模拟数据
  setTimeout(() => {
    tableData.value = [
      {
        id: '1',
        name: '销售完成率',
        category: 'kpi',
        type: 'quantitative',
        unit: '%',
        targetValue: 100,
        weight: 30,
        description: '月度销售目标完成率',
        scoringCriteria: [
          { level: 5, minValue: 95, maxValue: 100, description: '超额完成', score: 100 },
          { level: 4, minValue: 90, maxValue: 94, description: '完成目标', score: 85 },
          { level: 3, minValue: 80, maxValue: 89, description: '基本完成', score: 70 },
          { level: 2, minValue: 70, maxValue: 79, description: '未完成', score: 50 },
          { level: 1, minValue: 0, maxValue: 69, description: '严重不足', score: 0 }
        ],
        dataSource: {
          type: 'formula',
          formula: '{实际销售额}/{目标销售额}*100'
        },
        status: 'active',
        applicableTo: {
          departments: ['销售部'],
          positions: ['p1', 'p2']
        }
      },
      {
        id: '2',
        name: '团队协作',
        category: 'behavior',
        type: 'qualitative',
        weight: 20,
        description: '团队协作能力和精神',
        scoringCriteria: [
          { level: 5, description: '积极主动，团队贡献突出', score: 100 },
          { level: 4, description: '配合良好，有团队精神', score: 85 },
          { level: 3, description: '基本配合，偶有摩擦', score: 70 },
          { level: 2, description: '配合欠佳，需要改进', score: 50 },
          { level: 1, description: '缺乏团队精神', score: 0 }
        ],
        dataSource: {
          type: 'manual'
        },
        status: 'active',
        applicableTo: {
          departments: [],
          positions: []
        }
      },
      {
        id: '3',
        name: '代码质量',
        category: 'skill',
        type: 'quantitative',
        unit: '分',
        targetValue: 85,
        weight: 25,
        description: '代码质量评分',
        scoringCriteria: [
          { level: 5, minValue: 90, maxValue: 100, description: '优秀', score: 100 },
          { level: 4, minValue: 80, maxValue: 89, description: '良好', score: 85 },
          { level: 3, minValue: 70, maxValue: 79, description: '合格', score: 70 },
          { level: 2, minValue: 60, maxValue: 69, description: '需改进', score: 50 },
          { level: 1, minValue: 0, maxValue: 59, description: '不合格', score: 0 }
        ],
        dataSource: {
          type: 'system',
          systemField: 'code_quality_score'
        },
        status: 'active',
        applicableTo: {
          departments: ['技术部'],
          positions: ['p1', 'p2'],
          levels: ['junior', 'middle', 'senior']
        }
      }
    ]
    
    total.value = 3
    loading.value = false
  }, 500)
}

// 初始化
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.assessment-metric-management {
  padding: 20px;
  
  .search-card {
    margin-bottom: 20px;
  }
  
  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .scoring-preview {
      display: flex;
      align-items: center;
      gap: 10px;
      font-size: 13px;
      color: #909399;
    }
    
    .scope-tags {
      .el-tag {
        margin-right: 5px;
      }
    }
    
    .el-pagination {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
  
  .scoring-criteria-list {
    width: 100%;
    
    .criterion-item {
      margin-bottom: 10px;
      padding: 10px;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      
      &:hover {
        background-color: #f5f7fa;
      }
    }
  }
}
</style>