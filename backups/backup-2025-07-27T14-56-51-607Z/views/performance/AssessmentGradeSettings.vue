<template>
  <div class="assessment-grade-settings">
    <!-- 页面标题 -->
    <el-card class="header-card">
      <div class="page-header">
        <h2>考核等级设置</h2>
        <el-button type="primary" @click="handleSave" :icon="Check">保存设置</el-button>
      </div>
    </el-card>

    <!-- 等级分布策略 -->
    <el-card class="section-card">
      <template #header>
        <span>等级分布策略</span>
      </template>
      
      <el-form :model="settings" label-width="120px">
        <el-form-item label="分布类型">
          <el-radio-group v-model="settings.distributionType" @change="handleDistributionTypeChange">
            <el-radio value="normal">正态分布</el-radio>
            <el-radio value="forced">强制分布</el-radio>
            <el-radio value="free">自由分布</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="启用自动校准" v-if="settings.distributionType !== 'free'">
          <el-switch v-model="settings.autoCalibration"  />
          <span class="form-item-tip">开启后系统将自动调整分数以符合分布要求</span>
        </el-form-item>
        
        <el-form-item label="校准精度" v-if="settings.autoCalibration">
          <el-slider v-model="settings.calibrationPrecision" :min="1" :max="5" :step="1" show-stops  />
          <span class="form-item-tip">精度越高，调整越精细</span>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 等级定义 -->
    <el-card class="section-card">
      <template #header>
        <div class="card-header">
          <span>等级定义</span>
          <el-button type="primary" size="small" @click="handleAddGrade" :icon="Plus">
            添加等级
          </el-button>
        </div>
      </template>
      
      <el-table :data="settings.grades" stripe>
        <el-table-column prop="level" label="等级" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="getGradeType(row.level)" size="large">{{ row.level }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="等级名称" width="120">
          <template #default="{ row }">
            <el-input v-model="row.name" placeholder="输入名称"   />
          </template>
        </el-table-column>
        <el-table-column label="分数范围" width="200">
          <template #default="{ row }">
            <div class="score-range">
              <el-input-number 
                v-model="row.minScore" 
                :min="0" 
                :max="100" 
                :precision="1"
                size="small"
                placeholder="最低分"
                />
              <span>-</span>
              <el-input-number 
                v-model="row.maxScore" 
                :min="0" 
                :max="100" 
                :precision="1"
                size="small"
                placeholder="最高分"
                />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="人数比例(%)" width="150" v-if="settings.distributionType !== 'free'">
          <template #default="{ row }">
            <div class="ratio-range">
              <el-input-number 
                v-model="row.minRatio" 
                :min="0" 
                :max="100" 
                :precision="1"
                size="small"
                />
              <span>-</span>
              <el-input-number 
                v-model="row.maxRatio" 
                :min="0" 
                :max="100" 
                :precision="1"
                size="small"
                />
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="coefficient" label="绩效系数" width="120">
          <template #default="{ row }">
            <el-input-number 
              v-model="row.coefficient" 
              :min="0" 
              :max="2" 
              :precision="2"
              :step="0.1"
              size="small"
              />
          </template>
        </el-table-column>
        <el-table-column prop="description" label="等级说明" min-width="200">
          <template #default="{ row }">
            <el-input 
              v-model="row.description" 
              type="textarea"
              :rows="2"
              placeholder="输入等级说明"
              />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="{ row, $index }">
            <el-button 
              link 
              type="danger" 
              @click="handleRemoveGrade($index)"
              :disabled="settings.grades.length <= 3"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分布预览 -->
      <div class="distribution-preview" v-if="settings.distributionType !== 'free'">
        <h4>分布预览（基于100人）</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <div id="distributionChart" style="height: 300px"></div>
          </el-col>
          <el-col :span="12">
            <el-table :data="distributionPreview" stripe>
              <el-table-column prop="grade" label="等级" width="80">
                <template #default="{ row }">
                  <el-tag :type="getGradeType(row.grade)">{{ row.grade }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="名称"  />
              <el-table-column prop="ratio" label="比例">
                <template #default="{ row }">
                  {{ row.minRatio }}% - {{ row.maxRatio }}%
                </template>
              </el-table-column>
              <el-table-column prop="count" label="人数">
                <template #default="{ row }">
                  {{ row.minCount }} - {{ row.maxCount }}人
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 特殊规则 -->
    <el-card class="section-card">
      <template #header>
        <span>特殊规则</span>
      </template>
      
      <el-form :model="settings" label-width="150px">
        <el-form-item label="新员工考核规则">
          <el-radio-group v-model="settings.newEmployeeRule">
            <el-radio value="exclude">不参与强制分布</el-radio>
            <el-radio value="separate">单独分布</el-radio>
            <el-radio value="include">参与整体分布</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="试用期定义" v-if="settings.newEmployeeRule !== 'include'">
          <el-input-number v-model="settings.probationMonths" :min="1" :max="12"   />
          <span style="margin-left: 10px">个月</span>
        </el-form-item>
        
        <el-form-item label="连续低分处理">
          <el-switch v-model="settings.consecutiveLowScoreRule"  />
          <span class="form-item-tip">连续获得最低等级的处理规则</span>
        </el-form-item>
        
        <el-form-item label="触发条件" v-if="settings.consecutiveLowScoreRule">
          <el-select v-model="settings.lowScoreCondition" style="width: 200px">
            <el-option label="连续2次D级及以下" value="2D"  />
            <el-option label="连续3次C级及以下" value="3C"  />
            <el-option label="累计3次D级" value="total3D"  />
          </el-select>
        </el-form-item>
        
        <el-form-item label="处理措施" v-if="settings.consecutiveLowScoreRule">
          <el-checkbox-group v-model="settings.lowScoreActions">
            <el-checkbox label="improvement">制定改进计划</el-checkbox>
            <el-checkbox label="training">强制培训</el-checkbox>
            <el-checkbox label="transfer">调岗考察</el-checkbox>
            <el-checkbox label="dismiss">解除劳动合同</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item label="申诉机制">
          <el-switch v-model="settings.appealEnabled"  />
          <span class="form-item-tip">允许员工对考核结果申诉</span>
        </el-form-item>
        
        <el-form-item label="申诉时限" v-if="settings.appealEnabled">
          <el-input-number v-model="settings.appealDays" :min="1" :max="30"   />
          <span style="margin-left: 10px">工作日</span>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 历史设置 -->
    <el-card class="section-card">
      <template #header>
        <span>历史设置记录</span>
      </template>
      
      <el-table :data="historyRecords" stripe>
        <el-table-column prop="version" label="版本" width="100"  />
        <el-table-column prop="effectiveDate" label="生效日期" width="120"  />
        <el-table-column prop="distributionType" label="分布类型" width="100">
          <template #default="{ row }">
            {{ getDistributionTypeLabel(row.distributionType) }}
          </template>
        </el-table-column>
        <el-table-column prop="description" label="变更说明"  />
        <el-table-column prop="operator" label="操作人" width="100"  />
        <el-table-column prop="createdAt" label="设置时间" width="160"  />
        <el-table-column label="操作" width="100">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleViewHistory(row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Check, Plus } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 设置数据
const settings = reactive({
  distributionType: 'forced', // normal | forced | free
  autoCalibration: true,
  calibrationPrecision: 3,
  grades: [
    {
      level: 'A',
      name: 'HrHr优秀',
      minScore: 90,
      maxScore: 100,
      minRatio: 10,
      maxRatio: 15,
      coefficient: 1.3,
      description: '工作表现卓越，超出预期'
    },
    {
      level: 'B',
      name: '良好',
      minScore: 80,
      maxScore: 89.9,
      minRatio: 25,
      maxRatio: 35,
      coefficient: 1.1,
      description: '工作表现良好，达到预期'
    },
    {
      level: 'C',
      name: '合格',
      minScore: 70,
      maxScore: 79.9,
      minRatio: 40,
      maxRatio: 50,
      coefficient: 1.0,
      description: '工作表现合格，基本达标'
    },
    {
      level: 'D',
      name: '需改进',
      minScore: 60,
      maxScore: 69.9,
      minRatio: 10,
      maxRatio: 15,
      coefficient: 0.8,
      description: '工作表现不佳，需要改进'
    },
    {
      level: 'E',
      name: '不合格',
      minScore: 0,
      maxScore: 59.9,
      minRatio: 0,
      maxRatio: 5,
      coefficient: 0.5,
      description: '工作表现差，不符合要求'
    }
  ],
  newEmployeeRule: 'exclude',
  probationMonths: 3,
  consecutiveLowScoreRule: true,
  lowScoreCondition: '2D',
  lowScoreActions: ['improvement', 'training'],
  appealEnabled: true,
  appealDays: 7
})

// 历史记录
const historyRecords = ref([
  {
    version: 'v2.0',
    effectiveDate: '2025-01-01',
    distributionType: 'forced',
    description: '调整优秀比例上限至15%',
    operator: '系统管理员',
    createdAt: '2024-12-20 15:30:00'
  },
  {
    version: 'v1.0',
    effectiveDate: '2024-01-01',
    distributionType: 'normal',
    description: '初始设置',
    operator: 'HR经理',
    createdAt: '2023-12-15 10:00:00'
  }
])

// 分布预览
const distributionPreview = computed(() => {
  return settings.grades.map(grade => ({
    grade: grade.level,
    name: grade.name,
    minRatio: grade.minRatio,
    maxRatio: grade.maxRatio,
    minCount: Math.floor(grade.minRatio),
    maxCount: Math.ceil(grade.maxRatio)
  }))
})

// 获取等级类型
const getGradeType = (level: string) => {
  const map: Record<string, string> = {
    A: 'success',
    B: 'primary',
    C: '',
    D: 'warning',
    E: 'danger'
  }
  return map[level] || 'info'
}

// 获取分布类型标签
const getDistributionTypeLabel = (type: string) => {
  const map: Record<string, string> = {
    normal: '正态分布',
    forced: '强制分布',
    free: '自由分布'
  }
  return map[type] || type
}

// 分布类型变更
const handleDistributionTypeChange = (val: string) => {
  if (val === 'free') {
    ElMessageBox.confirm(
      '自由分布将不限制各等级人数比例，可能导致等级分布失衡，确定要使用吗？',
      '提示',
      { type: 'warning' }
    ).catch(() => {
      settings.distributionType = 'forced'
    })
  }
}

// 添加等级
const handleAddGrade = () => {
  const lastGrade = settings.grades[settings.grades.length - 1]
  settings.grades.push({
    level: String.fromCharCode(lastGrade.level.charCodeAt(0) + 1),
    name: '',
    minScore: 0,
    maxScore: lastGrade.minScore - 0.1,
    minRatio: 0,
    maxRatio: 5,
    coefficient: 0.5,
    description: ''
  })
}

// 删除等级
const handleRemoveGrade = async (index: number) => {
  try {
    await ElMessageBox.confirm('确定要删除该等级吗？', '提示', { type: 'warning' })
    settings.grades.splice(index, 1)
  } catch {
    // 用户取消
  }
}

// 保存设置
const handleSave = async () => {
  // 验证比例总和
  if (settings.distributionType !== 'free') {
    const minTotal = settings.grades.reduce((sum, grade) => sum + grade.minRatio, 0)
    const maxTotal = settings.grades.reduce((sum, grade) => sum + grade.maxRatio, 0)
    
    if (minTotal > 100 || maxTotal < 100) {
      ElMessage.error('等级比例设置不合理，请检查')
      return
    }
  }
  
  // 验证分数范围
  for (let i = 0; i < settings.grades.length - 1; i++) {
    if (settings.grades[i].minScore <= settings.grades[i + 1].maxScore) {
      ElMessage.error('等级分数范围存在重叠，请检查')
      return
    }
  }
  
  try {
    await ElMessageBox.confirm(
      '保存后将作为新的考核等级标准，确定要保存吗？',
      '提示'
    )
    
    ElMessage.success('考核等级设置已保存')
    
    // 添加到历史记录
    historyRecords.value.unshift({
      version: `v${historyRecords.value.length + 1}.0`,
      effectiveDate: new Date().toISOString().split('T')[0],
      distributionType: settings.distributionType,
      description: '手动更新设置',
      operator: '当前用户',
      createdAt: new Date().toLocaleString()
    })
  } catch {
    // 用户取消
  }
}

// 查看历史
   
const handleViewHistory = (row: unknown) => {
  ElMessage.info(`查看版本 ${row.version} 的详细设置`)
}

// 初始化分布图表
const initDistributionChart = () => {
  const chartDom = document.getElementById('distributionChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: settings.grades.map(g => g.level + '级')
    },
    yAxis: {
      type: 'value',
      name: '人数',
      max: 60
    },
    series: [
      {
        name: '最少人数',
        type: 'bar',
        stack: 'total',
        data: settings.grades.map(g => Math.floor(g.minRatio)),
        itemStyle: {
          color: '#67c23a'
        }
      },
      {
        name: '浮动范围',
        type: 'bar',
        stack: 'total',
        data: settings.grades.map(g => Math.ceil(g.maxRatio) - Math.floor(g.minRatio)),
        itemStyle: {
          color: '#409eff',
          opacity: 0.6
        }
      }
    ]
  }
  
  myChart.setOption(option)
}

// 监听分布类型变化
watch(() => settings.distributionType, () => {
  if (settings.distributionType !== 'free') {
    setTimeout(() => {
      initDistributionChart()
    }, 100)
  }
})

// 初始化
onMounted(() => {
  if (settings.distributionType !== 'free') {
    initDistributionChart()
  }
  
  // 响应式处理
  window.addEventListener('resize', () => {
    const chart = echarts.getInstanceByDom(document.getElementById('distributionChart')!)
    chart?.resize()
  })
})
</script>

<style lang="scss" scoped>
.assessment-grade-settings {
  padding: 20px;
  
  .header-card {
    margin-bottom: 20px;
    
    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      h2 {
        margin: 0;
        font-size: 20px;
        color: #303133;
      }
    }
  }
  
  .section-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .form-item-tip {
      margin-left: 10px;
      color: #909399;
      font-size: 12px;
    }
    
    .score-range,
    .ratio-range {
      display: flex;
      align-items: center;
      gap: 8px;
      
      span {
        color: #909399;
      }
    }
    
    .distribution-preview {
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #ebeef5;
      
      h4 {
        margin: 0 0 20px;
        font-size: 16px;
        color: #303133;
      }
    }
  }
}
</style>