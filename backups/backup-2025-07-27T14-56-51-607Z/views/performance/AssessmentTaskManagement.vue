<template>
  <div class="assessment-task-management">
    <!-- 搜索栏 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="员工">
          <el-input v-model="searchForm.employeeName" placeholder="员工姓名/工号" clearable   />
        </el-form-item>
        <el-form-item label="考核方案">
          <el-select v-model="searchForm.planId" placeholder="请选择" clearable>
            <el-option
              v-for="plan in assessmentPlans"
              :key="plan.id"
              :label="plan.name"
              :value="plan.id"
             />
          </el-select>
        </el-form-item>
        <el-form-item label="部门">
          <el-tree-select
            v-model="searchForm.department"
            :data="departmentTree"
            placeholder="请选择部门"
            clearable
           />
        </el-form-item>
        <el-form-item label="任务状态">
          <el-select v-model="searchForm.status" placeholder="请选择" clearable>
            <el-option label="待开始" value="pending"  />
            <el-option label="自评中" value="self_evaluating"  />
            <el-option label="主管评价中" value="supervisor_evaluating"  />
            <el-option label="校准中" value="calibrating"  />
            <el-option label="已完成" value="completed"  />
            <el-option label="申诉中" value="appealing"  />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :icon="Search">查询</el-button>
          <el-button @click="handleReset" :icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计概览 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <el-statistic title="总任务数" :value="stats.total">
            <template #suffix>个</template>
          </el-statistic>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <el-statistic title="进行中" :value="stats.ongoing">
            <template #suffix>个</template>
          </el-statistic>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <el-statistic title="已完成" :value="stats.completed">
            <template #suffix>个</template>
          </el-statistic>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <el-statistic title="完成率" :value="stats.completionRate" :precision="1">
            <template #suffix>%</template>
          </el-statistic>
        </el-card>
      </el-col>
    </el-row>

    <!-- 操作栏 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>考核任务列表</span>
          <div>
            <el-button type="primary" @click="handleBatchAssign" :icon="User">批量分配</el-button>
            <el-button type="warning" @click="handleBatchRemind" :icon="Bell">批量提醒</el-button>
            <el-button @click="handleExport" :icon="Download">导出</el-button>
          </div>
        </div>
      </template>

      <!-- 数据表格 -->
      <el-table
        :data="tableData"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55"  />
        <el-table-column prop="employeeName" label="员工姓名" width="100" fixed  />
        <el-table-column prop="department" label="部门" width="120"  />
        <el-table-column prop="position" label="岗位" width="120"  />
        <el-table-column prop="planName" label="考核方案" min-width="150" show-overflow-tooltip  />
        <el-table-column label="考核进度" width="200">
          <template #default="{ row }">
            <div class="progress-wrapper">
              <el-progress
                :percentage="getProgressPercentage(row)"
                :color="getProgressColor(row)"
                :stroke-width="8"
               />
              <span class="progress-text">{{ row.currentStep }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="截止时间" width="260">
          <template #default="{ row }">
            <div class="deadline-info">
              <div v-if="row.deadlines.selfEval" class="deadline-item">
                <el-tag size="small">自评</el-tag>
                <span :class="{ overdue: isOverdue(row.deadlines.selfEval) }">
                  {{ row.deadlines.selfEval }}
                </span>
              </div>
              <div v-if="row.deadlines.supervisorEval" class="deadline-item">
                <el-tag size="small" type="warning">主管</el-tag>
                <span :class="{ overdue: isOverdue(row.deadlines.supervisorEval) }">
                  {{ row.deadlines.supervisorEval }}
                </span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="结果" width="100" align="center">
          <template #default="{ row }">
            <div v-if="row.result">
              <el-tag :type="getGradeType(row.result.grade)">
                {{ row.result.grade }}级
              </el-tag>
              <div class="score-text">{{ row.result.totalScore }}分</div>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="180">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleView(row)">查看</el-button>
            <el-button link type="primary" @click="handleProcess(row)">流程</el-button>
            <el-button link type="primary" @click="handleEvaluate(row)" v-if="canEvaluate(row)">
              评价
            </el-button>
            <el-button link type="warning" @click="handleRemind(row)" v-if="row.status !== 'completed'">
              提醒
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
    </el-card>

    <!-- 任务详情抽屉 -->
    <el-drawer v-model="detailVisible" :title="detailTitle" size="60%">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="基本信息" name="basic">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="员工姓名">{{ currentTask?.employeeName }}</el-descriptions-item>
            <el-descriptions-item label="员工工号">{{ currentTask?.employeeId }}</el-descriptions-item>
            <el-descriptions-item label="所属部门">{{ currentTask?.department }}</el-descriptions-item>
            <el-descriptions-item label="岗位">{{ currentTask?.position }}</el-descriptions-item>
            <el-descriptions-item label="考核方案">{{ currentTask?.planName }}</el-descriptions-item>
            <el-descriptions-item label="考核周期">2025年度</el-descriptions-item>
            <el-descriptions-item label="当前状态">
              <el-tag :type="getStatusType(currentTask?.status || '')">
                {{ getStatusLabel(currentTask?.status || '') }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="当前环节">{{ currentTask?.currentStep }}</el-descriptions-item>
          </el-descriptions>
          
          <el-divider   />
          
          <h3>评价人信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="自评人">
              {{ currentTask?.evaluators.self || currentTask?.employeeName }}
            </el-descriptions-item>
            <el-descriptions-item label="主管评价">
              {{ currentTask?.evaluators.supervisor || '待分配' }}
            </el-descriptions-item>
            <el-descriptions-item label="同事评价" v-if="currentTask?.evaluators.peers?.length">
              {{ currentTask?.evaluators.peers.join('、') }}
            </el-descriptions-item>
            <el-descriptions-item label="下属评价" v-if="currentTask?.evaluators.subordinates?.length">
              {{ currentTask?.evaluators.subordinates.join('、') }}
            </el-descriptions-item>
          </el-descriptions>
          
          <el-divider   />
          
          <h3>时间节点</h3>
          <el-timeline>
            <el-timeline-item timestamp="2025-01-01" placement="top">
              考核开始
            </el-timeline-item>
            <el-timeline-item
              timestamp="2025-01-10"
              placement="top"
              :type="currentTask?.deadlines.selfEval ? 'primary' : 'info'"
            >
              自评截止
            </el-timeline-item>
            <el-timeline-item
              timestamp="2025-01-20"
              placement="top"
              :type="currentTask?.deadlines.supervisorEval ? 'warning' : 'info'"
            >
              主管评价截止
            </el-timeline-item>
            <el-timeline-item timestamp="2025-01-25" placement="top" type="success">
              考核结束
            </el-timeline-item>
          </el-timeline>
        </el-tab-pane>
        
        <el-tab-pane label="考核结果" name="result" v-if="currentTask?.result">
          <div class="result-summary">
            <el-row :gutter="20">
              <el-col :span="6">
                <div class="result-item">
                  <div class="label">总分</div>
                  <div class="value">{{ currentTask.result.totalScore }}</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="result-item">
                  <div class="label">等级</div>
                  <div class="value">
                    <el-tag :type="getGradeType(currentTask.result.grade)" size="large">
                      {{ currentTask.result.grade }}级
                    </el-tag>
                  </div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="result-item">
                  <div class="label">部门排名</div>
                  <div class="value">{{ currentTask.result.ranking || '-' }}</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="result-item">
                  <div class="label">确定时间</div>
                  <div class="value">{{ currentTask.result.finalizedAt || '-' }}</div>
                </div>
              </el-col>
            </el-row>
          </div>
          
          <el-divider   />
          
          <h3>各维度得分</h3>
          <el-table :data="currentTask.result.dimensionScores" stripe>
            <el-table-column prop="dimension" label="考核维度"  />
            <el-table-column prop="weight" label="权重">
              <template #default="{ row }">{{ row.weight }}%</template>
            </el-table-column>
            <el-table-column prop="score" label="得分"  />
            <el-table-column label="加权得分">
              <template #default="{ row }">
                {{ (row.score * row.weight / 100).toFixed(2) }}
              </template>
            </el-table-column>
          </el-table>
          
          <el-divider   />
          
          <h3>评语汇总</h3>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="自我评价" v-if="currentTask.result.comments.self">
              {{ currentTask.result.comments.self }}
            </el-descriptions-item>
            <el-descriptions-item label="主管评价" v-if="currentTask.result.comments.supervisor">
              {{ currentTask.result.comments.supervisor }}
            </el-descriptions-item>
            <el-descriptions-item label="校准意见" v-if="currentTask.result.comments.calibration">
              {{ currentTask.result.comments.calibration }}
            </el-descriptions-item>
          </el-descriptions>
          
          <div v-if="currentTask.result.improvements?.length" style="margin-top: 20px">
            <h3>改进建议</h3>
            <el-alert
              v-for="(improvement, index) in currentTask.result.improvements"
              :key="index"
              :title="`建议${index + 1}`"
              :description="improvement"
              type="info"
              :closable="false"
              style="margin-bottom: 10px"
             />
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="流程记录" name="flow">
          <el-timeline>
            <el-timeline-item
              v-for="record in flowRecords"
              :key="record.id"
              :timestamp="record.createdAt"
              placement="top"
            >
              <div class="flow-content">
                <div class="flow-action">
                  <strong>{{ record.operatorName }}</strong>
                  {{ record.action }}
                </div>
                <div v-if="record.comment" class="flow-comment">
                  备注：{{ record.comment }}
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </el-tab-pane>
      </el-tabs>
    </el-drawer>

    <!-- 批量分配对话框 -->
    <el-dialog v-model="assignVisible" title="批量分配评价人" width="600px">
      <el-form :model="assignForm" label-width="100px">
        <el-form-item label="主管评价人">
          <el-select
            v-model="assignForm.supervisor"
            filterable
            remote
            :remote-method="searchSupervisor"
            placeholder="搜索并选择主管"
          >
            <el-option
              v-for="user in supervisorOptions"
              :key="user.id"
              :label="`${user.name} (${user.position})`"
              :value="user.id"
             />
          </el-select>
        </el-form-item>
        <el-form-item label="分配策略">
          <el-radio-group v-model="assignForm.strategy">
            <el-radio value="replace">替换现有</el-radio>
            <el-radio value="fill">仅填充空缺</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="assignVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirmAssign">确定分配</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, User, Bell, Download } from '@element-plus/icons-vue'
import type { AssessmentTask, AssessmentFlowRecord } from '@/types/performance'

// 搜索表单
const searchForm = reactive({
  employeeName: '',
  planId: '',
  department: '',
  status: ''
})

// 统计数据
const stats = reactive({
  total: 120,
  ongoing: 45,
  completed: 75,
  completionRate: 62.5
})

// 表格数据
const loading = ref(false)
const tableData = ref<AssessmentTask[]>([])
const multipleSelection = ref<AssessmentTask[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 详情抽屉
const detailVisible = ref(false)
const detailTitle = ref('')
const currentTask = ref<AssessmentTask | null>(null)
const activeTab = ref('basic')
const flowRecords = ref<AssessmentFlowRecord[]>([])

// 批量分配
const assignVisible = ref(false)
const assignForm = reactive({
  supervisor: '',
  strategy: 'fill'
})
const supervisorOptions = ref([])

// 辅助数据
const assessmentPlans = ref([
  { id: '1', name: 'HrHr2025年度绩效考核方案' },
  { id: '2', name: 'Q1季度考核方案' },
  { id: '3', name: '技术部专项考核' }
])

const departmentTree = ref([
  {
    value: 'tech',
    label: '技术部',
    children: [
      { value: 'frontend', label: '前端组' },
      { value: 'backend', label: '后端组' },
      { value: 'test', label: '测试组' }
    ]
  },
  {
    value: 'product',
    label: '产品部'
  },
  {
    value: 'hr',
    label: '人力资源部'
  }
])

// 获取进度百分比
const getProgressPercentage = (row: AssessmentTask) => {
  const statusMap: Record<string, number> = {
    pending: 0,
    self_evaluating: 25,
    supervisor_evaluating: 50,
    calibrating: 75,
    completed: 100,
    appealing: 90
  }
  return statusMap[row.status] || 0
}

// 获取进度颜色
const getProgressColor = (row: AssessmentTask) => {
  if (row.status === 'completed') return '#67c23a'
  if (row.status === 'appealing') return '#e6a23c'
  return '#409eff'
}

// 获取状态标签
const getStatusLabel = (status: string) => {
  const map: Record<string, string> = {
    pending: '待开始',
    self_evaluating: '自评中',
    supervisor_evaluating: '主管评价中',
    calibrating: '校准中',
    completed: '已完成',
    appealing: '申诉中'
  }
  return map[status] || status
}

// 获取状态类型
const getStatusType = (status: string) => {
  const map: Record<string, string> = {
    pending: 'info',
    self_evaluating: 'primary',
    supervisor_evaluating: 'warning',
    calibrating: 'warning',
    completed: 'success',
    appealing: 'danger'
  }
  return map[status] || 'info'
}

// 获取等级类型
const getGradeType = (grade: string) => {
  const map: Record<string, string> = {
    A: 'success',
    B: 'primary',
    C: '',
    D: 'warning',
    E: 'danger'
  }
  return map[grade] || 'info'
}

// 判断是否逾期
const isOverdue = (deadline: string) => {
  return new Date(deadline) < new Date()
}

// 判断是否可以评价
const canEvaluate = (row: AssessmentTask) => {
  // 这里应该根据当前用户角色和任务状态判断
  return row.status === 'self_evaluating' || row.status === 'supervisor_evaluating'
}

// 搜索
const handleSearch = () => {
  loadData()
}

// 重置
const handleReset = () => {
  searchForm.employeeName = ''
  searchForm.planId = ''
  searchForm.department = ''
  searchForm.status = ''
  loadData()
}

// 查看详情
const handleView = (row: AssessmentTask) => {
  currentTask.value = row
  detailTitle.value = `考核任务详情 - ${row.employeeName}`
  activeTab.value = 'basic'
  loadFlowRecords(row.id)
  detailVisible.value = true
}

// 查看流程
const handleProcess = (row: AssessmentTask) => {
  currentTask.value = row
  detailTitle.value = `考核流程 - ${row.employeeName}`
  activeTab.value = 'flow'
  loadFlowRecords(row.id)
  detailVisible.value = true
}

// 评价
const handleEvaluate = (row: AssessmentTask) => {
  ElMessage.info(`进入${row.employeeName}的评价页面`)
}

// 提醒
const handleRemind = async (row: AssessmentTask) => {
  try {
    await ElMessageBox.confirm(
      `确定要向${row.employeeName}发送考核提醒吗？`,
      '提醒确认'
    )
    ElMessage.success('提醒已发送')
  } catch {
    // 用户取消
  }
}

// 批量分配
const handleBatchAssign = () => {
  if (!multipleSelection.value.length) {
    ElMessage.warning('请先选择要分配的任务')
    return
  }
  assignVisible.value = true
}

// 批量提醒
const handleBatchRemind = async () => {
  if (!multipleSelection.value.length) {
    ElMessage.warning('请先选择要提醒的任务')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要向选中的${multipleSelection.value.length}个员工发送考核提醒吗？`,
      '批量提醒确认'
    )
    ElMessage.success('批量提醒已发送')
  } catch {
    // 用户取消
  }
}

// 导出
const handleExport = () => {
  ElMessage.success('正在导出考核任务...')
}

// 确认分配
const handleConfirmAssign = () => {
  ElMessage.success(`已为${multipleSelection.value.length}个任务分配评价人`)
  assignVisible.value = false
  loadData()
}

// 搜索主管
const searchSupervisor = (query: string) => {
  if (query) {
    supervisorOptions.value = [
      { id: 'm1', name: '张经理', position: '技术经理' },
      { id: 'm2', name: '李总监', position: '技术总监' },
      { id: 'm3', name: '王主管', position: '项目主管' }
    ]
  }
}

// 选择变化
const handleSelectionChange = (val: AssessmentTask[]) => {
  multipleSelection.value = val
}

// 分页变化
const handleSizeChange = (val: number) => {
  pageSize.value = val
  loadData()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  loadData()
}

// 加载流程记录
const loadFlowRecords = (taskId: string) => {
  flowRecords.value = [
    {
      id: '1',
      taskId,
      action: '创建考核任务',
      operator: 'system',
      operatorName: '系统',
      createdAt: '2025-01-01 09:00:00'
    },
    {
      id: '2',
      taskId,
      action: '开始自评',
      operator: 'emp001',
      operatorName: '张三',
      comment: '已开始填写自评',
      createdAt: '2025-01-05 10:30:00'
    },
    {
      id: '3',
      taskId,
      action: '提交自评',
      operator: 'emp001',
      operatorName: '张三',
      comment: '自评完成，提交主管评价',
      createdAt: '2025-01-08 16:20:00'
    }
  ]
}

// 加载数据
const loadData = async () => {
  loading.value = true
  
  // 模拟数据
  setTimeout(() => {
    tableData.value = [
      {
        id: '1',
        planId: '1',
        planName: '2025年度绩效考核方案',
        employeeId: 'EMP001',
        employeeName: '张三',
        department: '技术部',
        position: '前端工程师',
        status: 'supervisor_evaluating',
        currentStep: '主管评价',
        deadlines: {
          selfEval: '2025-01-10',
          supervisorEval: '2025-01-20',
          calibration: '2025-01-25'
        },
        evaluators: {
          self: '张三',
          supervisor: '李经理',
          peers: ['王五', '赵六']
        },
        result: {
          totalScore: 88.5,
          grade: 'B',
          ranking: 12,
          dimensionScores: [
            { dimension: '工作业绩', score: 90, weight: 50, metrics: [] },
            { dimension: '工作能力', score: 85, weight: 30, metrics: [] },
            { dimension: '工作态度', score: 88, weight: 20, metrics: [] }
          ],
          comments: {
            self: '本年度工作完成情况良好，积极参与团队项目',
            supervisor: '工作认真负责，技术能力有显著提升'
          },
          improvements: ['加强代码规范意识', '提升沟通协调能力']
        },
        createdAt: '2025-01-01 09:00:00',
        updatedAt: '2025-01-08 16:20:00'
      },
      {
        id: '2',
        planId: '1',
        planName: '2025年度绩效考核方案',
        employeeId: 'EMP002',
        employeeName: '李四',
        department: '技术部',
        position: '后端工程师',
        status: 'self_evaluating',
        currentStep: '自我评价',
        deadlines: {
          selfEval: '2025-01-10',
          supervisorEval: '2025-01-20'
        },
        evaluators: {
          self: '李四',
          supervisor: '王总监'
        },
        createdAt: '2025-01-01 09:00:00',
        updatedAt: '2025-01-05 14:30:00'
      },
      {
        id: '3',
        planId: '1',
        planName: '2025年度绩效考核方案',
        employeeId: 'EMP003',
        employeeName: '王五',
        department: '产品部',
        position: '产品经理',
        status: 'completed',
        currentStep: '已完成',
        deadlines: {
          selfEval: '2025-01-10',
          supervisorEval: '2025-01-20'
        },
        evaluators: {
          self: '王五',
          supervisor: '赵总'
        },
        result: {
          totalScore: 92,
          grade: 'A',
          ranking: 5,
          dimensionScores: [
            { dimension: '工作业绩', score: 95, weight: 50, metrics: [] },
            { dimension: '工作能力', score: 90, weight: 30, metrics: [] },
            { dimension: '工作态度', score: 88, weight: 20, metrics: [] }
          ],
          comments: {
            self: '完成多个重点项目，产品用户满意度提升明显',
            supervisor: '优秀的产品经理，具备出色的项目管理能力',
            calibration: '同意评定为A级'
          },
          finalizedAt: '2025-01-25 18:00:00'
        },
        createdAt: '2025-01-01 09:00:00',
        updatedAt: '2025-01-25 18:00:00'
      }
    ]
    
    total.value = 3
    loading.value = false
  }, 500)
}

// 初始化
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.assessment-task-management {
  padding: 20px;
  
  .search-card {
    margin-bottom: 20px;
  }
  
  .stats-row {
    margin-bottom: 20px;
    
    .stat-card {
      text-align: center;
      
      :deep(.el-statistic) {
        .el-statistic__head {
          color: #909399;
          font-size: 14px;
        }
        
        .el-statistic__content {
          .el-statistic__number {
            font-size: 28px;
            color: #303133;
          }
        }
      }
    }
  }
  
  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .progress-wrapper {
      .progress-text {
        display: block;
        font-size: 12px;
        color: #909399;
        margin-top: 5px;
      }
    }
    
    .deadline-info {
      .deadline-item {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 5px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .overdue {
          color: #f56c6c;
        }
      }
    }
    
    .score-text {
      font-size: 12px;
      color: #909399;
      margin-top: 5px;
    }
    
    .el-pagination {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
  
  // 详情抽屉样式
  .result-summary {
    background-color: #f5f7fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    
    .result-item {
      text-align: center;
      
      .label {
        font-size: 14px;
        color: #909399;
        margin-bottom: 8px;
      }
      
      .value {
        font-size: 24px;
        font-weight: bold;
        color: #303133;
      }
    }
  }
  
  .flow-content {
    .flow-action {
      font-size: 14px;
      color: #303133;
      margin-bottom: 5px;
    }
    
    .flow-comment {
      font-size: 13px;
      color: #909399;
    }
  }
}
</style>