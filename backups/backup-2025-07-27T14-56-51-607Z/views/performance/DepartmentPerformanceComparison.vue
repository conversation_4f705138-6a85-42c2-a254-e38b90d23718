<template>
  <div class="department-performance-comparison">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>部门绩效对比</h3>
          <el-button type="primary" @click="handleExport">
            导出报告
          </el-button>
        </div>
      </template>

      <!-- 筛选区域 -->
      <div class="filter-section">
        <el-form :model="filterForm" :inline="true">
          <el-form-item label="考核周期">
            <el-date-picker
              v-model="filterForm.assessmentPeriod"
              type="month"
              placeholder="选择考核周期"
              @change="handleFilterChange"
             />
          </el-form-item>
          <el-form-item label="对比部门">
            <el-select
              v-model="filterForm.departments"
              placeholder="选择对比部门"
              multiple
              @change="handleFilterChange"
              style="width: 300px;"
            >
              <el-option
                v-for="dept in allDepartments"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
               />
            </el-select>
          </el-form-item>
          <el-form-item label="对比维度">
            <el-checkbox-group v-model="filterForm.metrics" @change="handleFilterChange">
              <el-checkbox label="averageScore">平均分</el-checkbox>
              <el-checkbox label="gradeDistribution">等级分布</el-checkbox>
              <el-checkbox label="improvement">改进幅度</el-checkbox>
              <el-checkbox label="efficiency">评估效率</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </div>

      <!-- 对比概览 -->
      <div class="comparison-overview">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic
              title="最高平均分"
              :value="comparisonSummary.highest.score"
              :precision="1"
              suffix="分"
            >
              <template #suffix>
                <span>分</span>
                <div class="department-name">{{ comparisonSummary.highest.department }}</div>
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="最低平均分"
              :value="comparisonSummary.lowest.score"
              :precision="1"
              suffix="分"
            >
              <template #suffix>
                <span>分</span>
                <div class="department-name">{{ comparisonSummary.lowest.department }}</div>
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="分数差距"
              :value="comparisonSummary.scoreGap"
              :precision="1"
              suffix="分"
             />
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="标准差"
              :value="comparisonSummary.standardDeviation"
              :precision="2"
             />
          </el-col>
        </el-row>
      </div>

      <!-- 图表展示区域 -->
      <div class="charts-section">
        <el-row :gutter="20">
          <!-- 部门排名图 -->
          <el-col :span="12">
            <el-card shadow="never">
              <template #header>
                <h4>部门绩效排名</h4>
              </template>
              <div ref="rankingChartRef" style="height: 350px;"></div>
            </el-card>
          </el-col>
          
          <!-- 雷达对比图 -->
          <el-col :span="12">
            <el-card shadow="never">
              <template #header>
                <h4>多维度对比雷达图</h4>
              </template>
              <div ref="radarChartRef" style="height: 350px;"></div>
            </el-card>
          </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-top: 20px;">
          <!-- 等级分布对比 -->
          <el-col :span="12">
            <el-card shadow="never">
              <template #header>
                <h4>等级分布对比</h4>
              </template>
              <div ref="gradeComparisonRef" style="height: 300px;"></div>
            </el-card>
          </el-col>
          
          <!-- 箱线图分析 -->
          <el-col :span="12">
            <el-card shadow="never">
              <template #header>
                <h4>分数分布箱线图</h4>
              </template>
              <div ref="boxPlotRef" style="height: 300px;"></div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 详细对比表格 -->
      <div class="comparison-table-section">
        <el-card shadow="never">
          <template #header>
            <h4>部门绩效详细对比</h4>
          </template>
          <el-table :data="comparisonTableData" border>
            <el-table-column prop="rank" label="排名" width="80" align="center">
              <template #default="scope">
                <el-tag
                  :type="getRankType(scope.row.rank)"
                  effect="dark"
                >
                  {{ scope.row.rank }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="department" label="部门"  />
            <el-table-column prop="participantCount" label="参评人数" align="center"  />
            <el-table-column prop="averageScore" label="平均分" align="center">
              <template #default="scope">
                {{ scope.row.averageScore?.toFixed(1) }}
              </template>
            </el-table-column>
            <el-table-column prop="gradeA" label="A级" align="center">
              <template #default="scope">
                {{ scope.row.gradeA }}人 ({{ scope.row.gradeAPercent }}%)
              </template>
            </el-table-column>
            <el-table-column prop="gradeB" label="B级" align="center">
              <template #default="scope">
                {{ scope.row.gradeB }}人 ({{ scope.row.gradeBPercent }}%)
              </template>
            </el-table-column>
            <el-table-column prop="gradeC" label="C级" align="center">
              <template #default="scope">
                {{ scope.row.gradeC }}人 ({{ scope.row.gradeCPercent }}%)
              </template>
            </el-table-column>
            <el-table-column prop="gradeD" label="D级" align="center">
              <template #default="scope">
                {{ scope.row.gradeD }}人 ({{ scope.row.gradeDPercent }}%)
              </template>
            </el-table-column>
            <el-table-column prop="improvement" label="环比变化" align="center">
              <template #default="scope">
                <span :class="getImprovementClass(scope.row.improvement)">
                  {{ scope.row.improvement > 0 ? '+' : '' }}{{ scope.row.improvement }}%
                </span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" align="center">
              <template #default="scope">
                <el-button
                  type="text"
                  @click="handleViewDepartmentDetail(scope.row)"
                >
                  查看详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>

      <!-- 对比分析 -->
      <div class="analysis-section">
        <el-card shadow="never">
          <template #header>
            <h4>对比分析</h4>
          </template>
          <div class="analysis-content">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="analysis-item">
                  <h5>优秀部门 <el-tag type="success">{{ bestDepartment.name }}</el-tag></h5>
                  <ul>
                    <li v-for="strength in bestDepartment.strengths" :key="strength">
                      {{ strength }}
                    </li>
                  </ul>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="analysis-item">
                  <h5>需改进部门 <el-tag type="warning">{{ improvementDepartment.name }}</el-tag></h5>
                  <ul>
                    <li v-for="issue in improvementDepartment.issues" :key="issue">
                      {{ issue }}
                    </li>
                  </ul>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="analysis-item">
                  <h5>改进建议</h5>
                  <ul>
                    <li v-for="suggestion in improvementSuggestions" :key="suggestion">
                      {{ suggestion }}
                    </li>
                  </ul>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </div>
    </el-card>

    <!-- 部门详情对话框 -->
    <el-dialog
      v-model="detailDialog.visible"
      title="部门绩效详情"
      width="70%"
    >
      <div class="detail-content" v-if="detailDialog.data">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="部门名称">
            {{ detailDialog.data.department }}
          </el-descriptions-item>
          <el-descriptions-item label="部门排名">
            第{{ detailDialog.data.rank }}名
          </el-descriptions-item>
          <el-descriptions-item label="参评人数">
            {{ detailDialog.data.participantCount }}人
          </el-descriptions-item>
          <el-descriptions-item label="平均分">
            {{ detailDialog.data.averageScore?.toFixed(1) }}分
          </el-descriptions-item>
          <el-descriptions-item label="A级人数">
            {{ detailDialog.data.gradeA }}人 ({{ detailDialog.data.gradeAPercent }}%)
          </el-descriptions-item>
          <el-descriptions-item label="B级人数">
            {{ detailDialog.data.gradeB }}人 ({{ detailDialog.data.gradeBPercent }}%)
          </el-descriptions-item>
          <el-descriptions-item label="C级人数">
            {{ detailDialog.data.gradeC }}人 ({{ detailDialog.data.gradeCPercent }}%)
          </el-descriptions-item>
          <el-descriptions-item label="D级人数">
            {{ detailDialog.data.gradeD }}人 ({{ detailDialog.data.gradeDPercent }}%)
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="department-trend" style="margin-top: 20px;">
          <h5>历史趋势</h5>
          <div ref="departmentTrendRef" style="height: 300px;"></div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'

// 接口定义
interface ComparisonSummary {
  highest: { score: number; department: string }
  lowest: { score: number; department: string }
  scoreGap: number
  standardDeviation: number
}

interface DepartmentData {
  rank: number
  department: string
  participantCount: number
  averageScore: number
  gradeA: number
  gradeAPercent: number
  gradeB: number
  gradeBPercent: number
  gradeC: number
  gradeCPercent: number
  gradeD: number
  gradeDPercent: number
  improvement: number
}

// 响应式数据
const loading = ref(false)

const allDepartments = ref([
  { id: '1', name: 'HrHr技术部' },
  { id: '2', name: '销售部' },
  { id: '3', name: '市场部' },
  { id: '4', name: '人事部' },
  { id: '5', name: '财务部' },
  { id: '6', name: '运营部' }
])

const filterForm = reactive({
  assessmentPeriod: '2024-12',
  departments: ['1', '2', '3', '4'],
  metrics: ['averageScore', 'gradeDistribution']
})

const comparisonSummary = ref<ComparisonSummary>({
  highest: { score: 85.2, department: '技术部' },
  lowest: { score: 78.9, department: '财务部' },
  scoreGap: 6.3,
  standardDeviation: 2.8
})

const comparisonTableData = ref<DepartmentData[]>([
  {
    rank: 1,
    department: '技术部',
    participantCount: 28,
    averageScore: 85.2,
    gradeA: 8,
    gradeAPercent: 28.6,
    gradeB: 12,
    gradeBPercent: 42.9,
    gradeC: 7,
    gradeCPercent: 25.0,
    gradeD: 1,
    gradeDPercent: 3.6,
    improvement: 2.1
  },
  {
    rank: 2,
    department: '人事部',
    participantCount: 15,
    averageScore: 83.8,
    gradeA: 3,
    gradeAPercent: 20.0,
    gradeB: 7,
    gradeBPercent: 46.7,
    gradeC: 4,
    gradeCPercent: 26.7,
    gradeD: 1,
    gradeDPercent: 6.7,
    improvement: 1.5
  },
  {
    rank: 3,
    department: '市场部',
    participantCount: 22,
    averageScore: 82.1,
    gradeA: 4,
    gradeAPercent: 18.2,
    gradeB: 9,
    gradeBPercent: 40.9,
    gradeC: 8,
    gradeCPercent: 36.4,
    gradeD: 1,
    gradeDPercent: 4.5,
    improvement: 0.8
  },
  {
    rank: 4,
    department: '销售部',
    participantCount: 35,
    averageScore: 80.5,
    gradeA: 5,
    gradeAPercent: 14.3,
    gradeB: 14,
    gradeBPercent: 40.0,
    gradeC: 13,
    gradeCPercent: 37.1,
    gradeD: 3,
    gradeDPercent: 8.6,
    improvement: -0.5
  }
])

const bestDepartment = ref({
  name: '技术部',
  strengths: [
    '平均分最高，达到85.2分',
    'A级人员占比最高，达到28.6%',
    '团队整体水平较为均衡',
    '持续改进效果明显'
  ]
})

const improvementDepartment = ref({
  name: '销售部',
  issues: [
    '平均分相对较低',
    'D级人员占比偏高',
    '环比出现下滑趋势',
    '等级分布不够理想'
  ]
})

const improvementSuggestions = ref([
  '加强跨部门经验交流',
  '建立部门绩效改进计划',
  '完善员工培训体系',
  '优化绩效评价标准'
])

const detailDialog = reactive({
  visible: false,
  data: null as DepartmentData | null
})

// Chart refs
const rankingChartRef = ref<HTMLElement>()
const radarChartRef = ref<HTMLElement>()
const gradeComparisonRef = ref<HTMLElement>()
const boxPlotRef = ref<HTMLElement>()
const departmentTrendRef = ref<HTMLElement>()

// 方法
const handleFilterChange = () => {
  loadComparisonData()
}

const handleExport = () => {
  ElMessage.success('导出功能开发中...')
}

const handleViewDepartmentDetail = (row: DepartmentData) => {
  detailDialog.data = row
  detailDialog.visible = true
  
  nextTick(() => {
    initDepartmentTrendChart()
  })
}

const loadComparisonData = async () => {
  try {
    loading.value = true
    
    // 模拟数据加载
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 更新图表
    nextTick(() => {
      initAllCharts()
    })
  } catch (__error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const initAllCharts = () => {
  initRankingChart()
  initRadarChart()
  initGradeComparisonChart()
  initBoxPlotChart()
}

const initRankingChart = () => {
  if (!rankingChartRef.value) return
  
  const chart = echarts.init(rankingChartRef.value)
  
  const departments = comparisonTableData.value.map(item => item.department)
  const scores = comparisonTableData.value.map(item => item.averageScore)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '平均分'
    },
    yAxis: {
      type: 'category',
      data: departments.reverse(),
      inverse: true
    },
    series: [
      {
        name: '平均分',
        type: 'bar',
        data: scores.reverse(),
        itemStyle: {
   
          color: (params: unknown) => {
            const colors = ['#67c23a', '#409eff', '#e6a23c', '#f56c6c']
            return colors[params.dataIndex] || '#909399'
          }
        },
        label: {
          show: true,
          position: 'right',
          formatter: '{c}分'
        }
      }
    ]
  }
  
  chart.setOption(option)
}

const initRadarChart = () => {
  if (!radarChartRef.value) return
  
  const chart = echarts.init(radarChartRef.value)
  
  const option = {
    tooltip: {},
    legend: {
      data: ['技术部', '销售部', '市场部', '人事部'],
      bottom: 0
    },
    radar: {
      indicator: [
        { name: '平均分', max: 90 },
        { name: 'A级占比', max: 35 },
        { name: '参评人数', max: 40 },
        { name: '改进幅度', max: 5 },
        { name: '评估效率', max: 100 }
      ],
      radius: '60%'
    },
    series: [
      {
        name: '部门对比',
        type: 'radar',
        data: [
          {
            value: [85.2, 28.6, 28, 2.1, 95],
            name: '技术部',
            itemStyle: { color: '#67c23a' }
          },
          {
            value: [80.5, 14.3, 35, -0.5, 88],
            name: '销售部',
            itemStyle: { color: '#409eff' }
          },
          {
            value: [82.1, 18.2, 22, 0.8, 92],
            name: '市场部',
            itemStyle: { color: '#e6a23c' }
          },
          {
            value: [83.8, 20.0, 15, 1.5, 98],
            name: '人事部',
            itemStyle: { color: '#f56c6c' }
          }
        ]
      }
    ]
  }
  
  chart.setOption(option)
}

const initGradeComparisonChart = () => {
  if (!gradeComparisonRef.value) return
  
  const chart = echarts.init(gradeComparisonRef.value)
  
  const departments = comparisonTableData.value.map(item => item.department)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['A级', 'B级', 'C级', 'D级']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: departments
    },
    yAxis: {
      type: 'value',
      name: '占比(%)'
    },
    series: [
      {
        name: 'A级',
        type: 'bar',
        stack: 'total',
        data: comparisonTableData.value.map(item => item.gradeAPercent),
        itemStyle: { color: '#67c23a' }
      },
      {
        name: 'B级',
        type: 'bar',
        stack: 'total',
        data: comparisonTableData.value.map(item => item.gradeBPercent),
        itemStyle: { color: '#409eff' }
      },
      {
        name: 'C级',
        type: 'bar',
        stack: 'total',
        data: comparisonTableData.value.map(item => item.gradeCPercent),
        itemStyle: { color: '#e6a23c' }
      },
      {
        name: 'D级',
        type: 'bar',
        stack: 'total',
        data: comparisonTableData.value.map(item => item.gradeDPercent),
        itemStyle: { color: '#f56c6c' }
      }
    ]
  }
  
  chart.setOption(option)
}

const initBoxPlotChart = () => {
  if (!boxPlotRef.value) return
  
  const chart = echarts.init(boxPlotRef.value)
  
  // 模拟箱线图数据 [min, Q1, median, Q3, max]
  const boxData = [
    [75, 80, 83, 87, 92], // 技术部
    [70, 75, 78, 82, 88], // 销售部
    [72, 77, 81, 85, 90], // 市场部
    [76, 81, 84, 86, 89]  // 人事部
  ]
  
  const option = {
    title: {
      text: '各部门分数分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '10%',
      right: '10%',
      bottom: '15%'
    },
    xAxis: {
      type: 'category',
      data: comparisonTableData.value.map(item => item.department),
      boundaryGap: true,
      nameGap: 30,
      splitArea: {
        show: false
      },
      splitLine: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      name: '分数',
      splitArea: {
        show: true
      }
    },
    series: [
      {
        name: 'boxplot',
        type: 'boxplot',
        data: boxData,
        tooltip: {
   
          formatter: (param: unknown) => {
            return [
              '部门: ' + param.name,
              '最小值: ' + param.data[0],
              '下四分位数: ' + param.data[1],
              '中位数: ' + param.data[2],
              '上四分位数: ' + param.data[3],
              '最大值: ' + param.data[4]
            ].join('<br/>')
          }
        }
      }
    ]
  }
  
  chart.setOption(option)
}

const initDepartmentTrendChart = () => {
  if (!departmentTrendRef.value) return
  
  const chart = echarts.init(departmentTrendRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['平均分', '参评人数']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['2024-07', '2024-08', '2024-09', '2024-10', '2024-11', '2024-12']
    },
    yAxis: [
      {
        type: 'value',
        name: '平均分',
        position: 'left'
      },
      {
        type: 'value',
        name: '参评人数',
        position: 'right'
      }
    ],
    series: [
      {
        name: '平均分',
        type: 'line',
        yAxisIndex: 0,
        data: [78.5, 79.8, 81.2, 82.1, 83.1, 85.2],
        smooth: true,
        itemStyle: {
          color: '#409eff'
        }
      },
      {
        name: '参评人数',
        type: 'bar',
        yAxisIndex: 1,
        data: [25, 26, 27, 28, 28, 28],
        itemStyle: {
          color: '#67c23a'
        }
      }
    ]
  }
  
  chart.setOption(option)
}

const getRankType = (rank: number) => {
  if (rank === 1) return 'success'
  if (rank === 2) return 'primary'
  if (rank === 3) return 'warning'
  return 'info'
}

const getImprovementClass = (improvement: number) => {
  if (improvement > 0) return 'text-success'
  if (improvement < 0) return 'text-danger'
  return 'text-info'
}

// 生命周期
onMounted(() => {
  loadComparisonData()
})
</script>

<style scoped>
.department-performance-comparison {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.comparison-overview {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fff;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.department-name {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.charts-section {
  margin-bottom: 20px;
}

.comparison-table-section {
  margin-bottom: 20px;
}

.analysis-section {
  margin-bottom: 20px;
}

.analysis-content {
  padding: 20px;
}

.analysis-item {
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  height: 100%;
}

.analysis-item h5 {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.analysis-item ul {
  margin: 0;
  padding-left: 20px;
}

.analysis-item li {
  margin-bottom: 5px;
}

.text-success {
  color: #67c23a;
}

.text-danger {
  color: #f56c6c;
}

.text-info {
  color: #909399;
}

.detail-content {
  padding: 20px;
}

.department-trend h5 {
  margin-bottom: 15px;
}
</style>