<template>
  <div class="performance-trend-analysis">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>绩效趋势分析</h3>
          <el-button type="primary" @click="handleExport">
            导出报告
          </el-button>
        </div>
      </template>

      <!-- 筛选区域 -->
      <div class="filter-section">
        <el-form :model="filterForm" :inline="true">
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="monthrange"
              range-separator="至"
              start-placeholder="开始月份"
              end-placeholder="结束月份"
              @change="handleFilterChange"
             />
          </el-form-item>
          <el-form-item label="部门">
            <el-select
              v-model="filterForm.departments"
              placeholder="请选择部门"
              multiple
              @change="handleFilterChange"
              style="width: 200px;"
            >
              <el-option
                v-for="dept in departments"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
               />
            </el-select>
          </el-form-item>
          <el-form-item label="分析维度">
            <el-select
              v-model="filterForm.dimension"
              @change="handleFilterChange"
            >
              <el-option label="整体趋势" value="overall"  />
              <el-option label="部门对比" value="department"  />
              <el-option label="等级分布" value="grade"  />
              <el-option label="个人跟踪" value="individual"  />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 趋势概览 -->
      <div class="trend-overview">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic
              title="最新平均分"
              :value="trendSummary.latestAverage"
              :precision="1"
              suffix="分"
            >
              <template #suffix>
                <span>分</span>
                <el-icon :class="getTrendIcon(trendSummary.averageTrend)">
                  <ArrowUp v-if="trendSummary.averageTrend > 0" />
                  <ArrowDown v-else-if="trendSummary.averageTrend < 0" />
                  <Minus v-else />
                </el-icon>
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="同比变化"
              :value="trendSummary.yearOverYear"
              :precision="1"
              suffix="%"
            >
              <template #suffix>
                <span>%</span>
                <el-icon :class="getTrendIcon(trendSummary.yearOverYear)">
                  <ArrowUp v-if="trendSummary.yearOverYear > 0" />
                  <ArrowDown v-else-if="trendSummary.yearOverYear < 0" />
                  <Minus v-else />
                </el-icon>
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="环比变化"
              :value="trendSummary.monthOverMonth"
              :precision="1"
              suffix="%"
            >
              <template #suffix>
                <span>%</span>
                <el-icon :class="getTrendIcon(trendSummary.monthOverMonth)">
                  <ArrowUp v-if="trendSummary.monthOverMonth > 0" />
                  <ArrowDown v-else-if="trendSummary.monthOverMonth < 0" />
                  <Minus v-else />
                </el-icon>
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="趋势稳定性"
              :value="trendSummary.stability"
              :precision="1"
              suffix="%"
             />
          </el-col>
        </el-row>
      </div>

      <!-- 图表展示区域 -->
      <div class="charts-section">
        <el-row :gutter="20">
          <!-- 整体趋势图 -->
          <el-col :span="24">
            <el-card shadow="never">
              <template #header>
                <div class="chart-header">
                  <h4>绩效趋势图</h4>
                  <el-radio-group v-model="chartType" @change="updateTrendChart">
                    <el-radio-button label="line">折线图</el-radio-button>
                    <el-radio-button label="area">面积图</el-radio-button>
                    <el-radio-button label="bar">柱状图</el-radio-button>
                  </el-radio-group>
                </div>
              </template>
              <div ref="trendChartRef" style="height: 400px;"></div>
            </el-card>
          </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-top: 20px;">
          <!-- 等级分布趋势 -->
          <el-col :span="12">
            <el-card shadow="never">
              <template #header>
                <h4>等级分布趋势</h4>
              </template>
              <div ref="gradesTrendRef" style="height: 300px;"></div>
            </el-card>
          </el-col>
          
          <!-- 部门对比趋势 -->
          <el-col :span="12">
            <el-card shadow="never">
              <template #header>
                <h4>部门趋势对比</h4>
              </template>
              <div ref="departmentTrendRef" style="height: 300px;"></div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 个人跟踪（仅在个人维度显示） -->
        <el-row v-if="filterForm.dimension === 'individual'" style="margin-top: 20px;">
          <el-col :span="24">
            <el-card shadow="never">
              <template #header>
                <div class="chart-header">
                  <h4>个人绩效跟踪</h4>
                  <el-select
                    v-model="selectedEmployee"
                    placeholder="选择员工"
                    @change="updateIndividualChart"
                    style="width: 200px;"
                  >
                    <el-option
                      v-for="emp in employees"
                      :key="emp.id"
                      :label="emp.name"
                      :value="emp.id"
                     />
                  </el-select>
                </div>
              </template>
              <div ref="individualChartRef" style="height: 300px;"></div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 趋势分析表格 -->
      <div class="trend-table-section">
        <el-card shadow="never">
          <template #header>
            <h4>趋势数据详情</h4>
          </template>
          <el-table :data="trendTableData" border>
            <el-table-column prop="period" label="考核周期" align="center"  />
            <el-table-column prop="averageScore" label="平均分" align="center">
              <template #default="scope">
                {{ scope.row.averageScore?.toFixed(1) }}
              </template>
            </el-table-column>
            <el-table-column prop="gradeA" label="A级占比" align="center">
              <template #default="scope">
                {{ scope.row.gradeA }}%
              </template>
            </el-table-column>
            <el-table-column prop="gradeB" label="B级占比" align="center">
              <template #default="scope">
                {{ scope.row.gradeB }}%
              </template>
            </el-table-column>
            <el-table-column prop="gradeC" label="C级占比" align="center">
              <template #default="scope">
                {{ scope.row.gradeC }}%
              </template>
            </el-table-column>
            <el-table-column prop="gradeD" label="D级占比" align="center">
              <template #default="scope">
                {{ scope.row.gradeD }}%
              </template>
            </el-table-column>
            <el-table-column prop="participantCount" label="参评人数" align="center"  />
            <el-table-column prop="monthOverMonth" label="环比变化" align="center">
              <template #default="scope">
                <span :class="getTrendClass(scope.row.monthOverMonth)">
                  {{ scope.row.monthOverMonth > 0 ? '+' : '' }}{{ scope.row.monthOverMonth }}%
                </span>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>

      <!-- 趋势洞察 -->
      <div class="insights-section">
        <el-card shadow="never">
          <template #header>
            <h4>趋势洞察</h4>
          </template>
          <div class="insights-content">
            <el-timeline>
              <el-timeline-item
                v-for="insight in trendInsights"
                :key="insight.id"
                :timestamp="insight.period"
                :type="insight.type"
              >
                <el-card>
                  <h4>{{ insight.title }}</h4>
                  <p>{{ insight.description }}</p>
                  <div v-if="insight.recommendations" class="recommendations">
                    <h5>建议措施：</h5>
                    <ul>
                      <li v-for="rec in insight.recommendations" :key="rec">{{ rec }}</li>
                    </ul>
                  </div>
                </el-card>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowUp, ArrowDown, Minus } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 接口定义
interface TrendSummary {
  latestAverage: number
  averageTrend: number
  yearOverYear: number
  monthOverMonth: number
  stability: number
}

interface TrendData {
  period: string
  averageScore: number
  gradeA: number
  gradeB: number
  gradeC: number
  gradeD: number
  participantCount: number
  monthOverMonth: number
}

interface TrendInsight {
  id: string
  period: string
  title: string
  description: string
  type: 'primary' | 'success' | 'warning' | 'danger'
  recommendations?: string[]
}

// 响应式数据
const loading = ref(false)
const chartType = ref('line')
const selectedEmployee = ref('')

const departments = ref([
  { id: '1', name: 'HrHr技术部' },
  { id: '2', name: '销售部' },
  { id: '3', name: '市场部' },
  { id: '4', name: '人事部' }
])

const employees = ref([
  { id: '1', name: '张三' },
  { id: '2', name: '李四' },
  { id: '3', name: '王五' },
  { id: '4', name: '赵六' }
])

const filterForm = reactive({
  dateRange: ['2024-06', '2024-12'],
  departments: [],
  dimension: 'overall'
})

const trendSummary = ref<TrendSummary>({
  latestAverage: 82.5,
  averageTrend: 1.2,
  yearOverYear: 3.5,
  monthOverMonth: 0.8,
  stability: 88.2
})

const trendTableData = ref<TrendData[]>([
  {
    period: '2024-12',
    averageScore: 82.5,
    gradeA: 20,
    gradeB: 35,
    gradeC: 35,
    gradeD: 10,
    participantCount: 120,
    monthOverMonth: 0.8
  },
  {
    period: '2024-11',
    averageScore: 81.8,
    gradeA: 18,
    gradeB: 38,
    gradeC: 34,
    gradeD: 10,
    participantCount: 118,
    monthOverMonth: 1.2
  },
  {
    period: '2024-10',
    averageScore: 80.8,
    gradeA: 15,
    gradeB: 40,
    gradeC: 35,
    gradeD: 10,
    participantCount: 115,
    monthOverMonth: -0.5
  },
  {
    period: '2024-09',
    averageScore: 81.2,
    gradeA: 17,
    gradeB: 36,
    gradeC: 37,
    gradeD: 10,
    participantCount: 112,
    monthOverMonth: 2.1
  },
  {
    period: '2024-08',
    averageScore: 79.5,
    gradeA: 16,
    gradeB: 34,
    gradeC: 38,
    gradeD: 12,
    participantCount: 110,
    monthOverMonth: -1.0
  },
  {
    period: '2024-07',
    averageScore: 80.3,
    gradeA: 19,
    gradeB: 32,
    gradeC: 37,
    gradeD: 12,
    participantCount: 108,
    monthOverMonth: 1.8
  },
  {
    period: '2024-06',
    averageScore: 78.9,
    gradeA: 14,
    gradeB: 35,
    gradeC: 39,
    gradeD: 12,
    participantCount: 105,
    monthOverMonth: 0.0
  }
])

const trendInsights = ref<TrendInsight[]>([
  {
    id: '1',
    period: '2024-12',
    title: '整体绩效稳步提升',
    description: '本月平均分达到82.5分，创下半年来新高，A级人员占比提升至20%。',
    type: 'success',
    recommendations: [
      '继续保持当前激励机制',
      '总结成功经验并推广',
      '关注B级员工的进一步提升'
    ]
  },
  {
    id: '2',
    period: '2024-10',
    title: '绩效出现小幅下滑',
    description: '本月平均分下降至80.8分，需要关注团队状态和工作压力。',
    type: 'warning',
    recommendations: [
      '加强团队沟通和支持',
      '分析下滑原因并制定改进措施',
      '适当调整工作安排'
    ]
  },
  {
    id: '3',
    period: '2024-09',
    title: '绩效回升明显',
    description: '经过调整后绩效明显回升，环比增长2.1%，团队状态良好。',
    type: 'primary'
  }
])

// Chart refs
const trendChartRef = ref<HTMLElement>()
const gradesTrendRef = ref<HTMLElement>()
const departmentTrendRef = ref<HTMLElement>()
const individualChartRef = ref<HTMLElement>()

// 方法
const handleFilterChange = () => {
  loadTrendData()
}

const handleExport = () => {
  ElMessage.success('导出功能开发中...')
}

const loadTrendData = async () => {
  try {
    loading.value = true
    
    // 模拟数据加载
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 更新图表
    nextTick(() => {
      initAllCharts()
    })
  } catch (__error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const initAllCharts = () => {
  updateTrendChart()
  initGradesTrendChart()
  initDepartmentTrendChart()
  if (filterForm.dimension === 'individual') {
    updateIndividualChart()
  }
}

const updateTrendChart = () => {
  if (!trendChartRef.value) return
  
  const chart = echarts.init(trendChartRef.value)
  
  const periods = trendTableData.value.map(item => item.period).reverse()
  const scores = trendTableData.value.map(item => item.averageScore).reverse()
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['平均分', '参评人数']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        data: periods
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '平均分',
        min: 75,
        max: 85,
        axisLabel: {
          formatter: '{value} 分'
        }
      },
      {
        type: 'value',
        name: '参评人数',
        min: 100,
        max: 130,
        axisLabel: {
          formatter: '{value} 人'
        }
      }
    ],
    series: [
      {
        name: '平均分',
        type: chartType.value,
        smooth: true,
        data: scores,
        itemStyle: {
          color: '#409eff'
        },
        areaStyle: chartType.value === 'area' ? {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(64, 158, 255, 0.4)' },
            { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
          ])
        } : undefined
      },
      {
        name: '参评人数',
        type: 'line',
        yAxisIndex: 1,
        data: trendTableData.value.map(item => item.participantCount).reverse(),
        itemStyle: {
          color: '#67c23a'
        }
      }
    ]
  }
  
  chart.setOption(option)
}

const initGradesTrendChart = () => {
  if (!gradesTrendRef.value) return
  
  const chart = echarts.init(gradesTrendRef.value)
  
  const periods = trendTableData.value.map(item => item.period).reverse()
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['A级', 'B级', 'C级', 'D级']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: periods
      }
    ],
    yAxis: [
      {
        type: 'value',
        axisLabel: {
          formatter: '{value}%'
        }
      }
    ],
    series: [
      {
        name: 'A级',
        type: 'bar',
        stack: 'grades',
        data: trendTableData.value.map(item => item.gradeA).reverse(),
        itemStyle: { color: '#67c23a' }
      },
      {
        name: 'B级',
        type: 'bar',
        stack: 'grades',
        data: trendTableData.value.map(item => item.gradeB).reverse(),
        itemStyle: { color: '#409eff' }
      },
      {
        name: 'C级',
        type: 'bar',
        stack: 'grades',
        data: trendTableData.value.map(item => item.gradeC).reverse(),
        itemStyle: { color: '#e6a23c' }
      },
      {
        name: 'D级',
        type: 'bar',
        stack: 'grades',
        data: trendTableData.value.map(item => item.gradeD).reverse(),
        itemStyle: { color: '#f56c6c' }
      }
    ]
  }
  
  chart.setOption(option)
}

const initDepartmentTrendChart = () => {
  if (!departmentTrendRef.value) return
  
  const chart = echarts.init(departmentTrendRef.value)
  
  const periods = trendTableData.value.slice(0, 6).map(item => item.period).reverse()
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['技术部', '销售部', '市场部', '人事部']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: periods
    },
    yAxis: {
      type: 'value',
      name: '平均分'
    },
    series: [
      {
        name: '技术部',
        type: 'line',
        data: [78.5, 80.8, 79.2, 81.5, 82.1, 83.2],
        smooth: true
      },
      {
        name: '销售部',
        type: 'line',
        data: [79.2, 79.8, 80.5, 80.8, 81.2, 82.0],
        smooth: true
      },
      {
        name: '市场部',
        type: 'line',
        data: [78.0, 80.1, 81.2, 80.5, 81.8, 82.5],
        smooth: true
      },
      {
        name: '人事部',
        type: 'line',
        data: [79.8, 80.2, 81.0, 81.8, 82.0, 82.8],
        smooth: true
      }
    ]
  }
  
  chart.setOption(option)
}

const updateIndividualChart = () => {
  if (!individualChartRef.value || !selectedEmployee.value) return
  
  const chart = echarts.init(individualChartRef.value)
  
  const periods = trendTableData.value.map(item => item.period).reverse()
  
  // 模拟个人数据
  const personalScores = [76, 78, 80, 82, 84, 85, 87]
  const teamAverage = trendTableData.value.map(item => item.averageScore).reverse()
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['个人分数', '团队平均分']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: periods
    },
    yAxis: {
      type: 'value',
      name: '分数'
    },
    series: [
      {
        name: '个人分数',
        type: 'line',
        data: personalScores,
        itemStyle: {
          color: '#409eff'
        },
        markPoint: {
          data: [
            { type: 'max', name: '最高分' },
            { type: 'min', name: '最低分' }
          ]
        },
        markLine: {
          data: [
            { type: 'average', name: '平均值' }
          ]
        }
      },
      {
        name: '团队平均分',
        type: 'line',
        data: teamAverage,
        itemStyle: {
          color: '#67c23a'
        },
        lineStyle: {
          type: 'dashed'
        }
      }
    ]
  }
  
  chart.setOption(option)
}

const getTrendIcon = (value: number) => {
  if (value > 0) return 'trend-up'
  if (value < 0) return 'trend-down'
  return 'trend-stable'
}

const getTrendClass = (value: number) => {
  if (value > 0) return 'text-success'
  if (value < 0) return 'text-danger'
  return 'text-info'
}

// 生命周期
onMounted(() => {
  loadTrendData()
})
</script>

<style scoped>
.performance-trend-analysis {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.trend-overview {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fff;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.charts-section {
  margin-bottom: 20px;
}

.trend-table-section {
  margin-bottom: 20px;
}

.insights-section {
  margin-bottom: 20px;
}

.insights-content {
  padding: 10px;
}

.recommendations {
  margin-top: 10px;
}

.recommendations h5 {
  margin-bottom: 5px;
  color: #606266;
}

.recommendations ul {
  margin: 0;
  padding-left: 20px;
}

.text-success {
  color: #67c23a;
}

.text-danger {
  color: #f56c6c;
}

.text-info {
  color: #909399;
}

.trend-up {
  color: #67c23a;
}

.trend-down {
  color: #f56c6c;
}

.trend-stable {
  color: #909399;
}
</style>