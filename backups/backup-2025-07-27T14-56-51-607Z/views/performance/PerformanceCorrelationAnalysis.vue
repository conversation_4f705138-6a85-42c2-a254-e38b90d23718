<template>
  <div class="performance-correlation-analysis">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>绩效相关性分析</h3>
          <el-button type="primary" @click="handleExport">
            导出报告
          </el-button>
        </div>
      </template>

      <!-- 筛选区域 -->
      <div class="filter-section">
        <el-form :model="filterForm" :inline="true">
          <el-form-item label="分析周期">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="monthrange"
              range-separator="至"
              start-placeholder="开始月份"
              end-placeholder="结束月份"
              @change="handleFilterChange"
             />
          </el-form-item>
          <el-form-item label="分析维度">
            <el-select
              v-model="filterForm.analysisType"
              @change="handleFilterChange"
            >
              <el-option label="综合相关性" value="comprehensive"  />
              <el-option label="工作年限" value="workYear"  />
              <el-option label="学历水平" value="education"  />
              <el-option label="培训投入" value="training"  />
              <el-option label="薪资水平" value="salary"  />
            </el-select>
          </el-form-item>
          <el-form-item label="相关性阈值">
            <el-slider
              v-model="filterForm.correlationThreshold"
              :min="0"
              :max="1"
              :step="0.1"
              :format-tooltip="formatTooltip"
              @change="handleFilterChange"
              style="width: 200px;"
             />
          </el-form-item>
        </el-form>
      </div>

      <!-- 相关性概览 -->
      <div class="correlation-overview">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic
              title="最强正相关"
              :value="correlationSummary.strongestPositive.value"
              :precision="3"
            >
              <template #suffix>
                <div class="correlation-factor">{{ correlationSummary.strongestPositive.factor }}</div>
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="最强负相关"
              :value="correlationSummary.strongestNegative.value"
              :precision="3"
            >
              <template #suffix>
                <div class="correlation-factor">{{ correlationSummary.strongestNegative.factor }}</div>
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="显著相关因子数"
              :value="correlationSummary.significantCount"
              suffix="个"
             />
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="平均相关性"
              :value="correlationSummary.averageCorrelation"
              :precision="3"
             />
          </el-col>
        </el-row>
      </div>

      <!-- 图表展示区域 -->
      <div class="charts-section">
        <el-row :gutter="20">
          <!-- 相关性矩阵热力图 -->
          <el-col :span="12">
            <el-card shadow="never">
              <template #header>
                <h4>相关性矩阵热力图</h4>
              </template>
              <div ref="heatmapRef" style="height: 400px;"></div>
            </el-card>
          </el-col>
          
          <!-- 相关性系数图 -->
          <el-col :span="12">
            <el-card shadow="never">
              <template #header>
                <h4>相关性系数排名</h4>
              </template>
              <div ref="correlationBarRef" style="height: 400px;"></div>
            </el-card>
          </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-top: 20px;">
          <!-- 散点图分析 -->
          <el-col :span="12">
            <el-card shadow="never">
              <template #header>
                <div class="chart-header">
                  <h4>散点图分析</h4>
                  <el-select
                    v-model="scatterForm.xAxis"
                    placeholder="选择X轴"
                    @change="updateScatterChart"
                    style="width: 120px; margin-right: 10px;"
                  >
                    <el-option
                      v-for="factor in correlationFactors"
                      :key="factor.key"
                      :label="factor.name"
                      :value="factor.key"
                     />
                  </el-select>
                  <el-select
                    v-model="scatterForm.yAxis"
                    placeholder="选择Y轴"
                    @change="updateScatterChart"
                    style="width: 120px;"
                  >
                    <el-option
                      v-for="factor in correlationFactors"
                      :key="factor.key"
                      :label="factor.name"
                      :value="factor.key"
                     />
                  </el-select>
                </div>
              </template>
              <div ref="scatterRef" style="height: 350px;"></div>
            </el-card>
          </el-col>
          
          <!-- 回归分析 -->
          <el-col :span="12">
            <el-card shadow="never">
              <template #header>
                <h4>线性回归分析</h4>
              </template>
              <div ref="regressionRef" style="height: 350px;"></div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 相关性分析表格 -->
      <div class="correlation-table-section">
        <el-card shadow="never">
          <template #header>
            <h4>相关性分析详情</h4>
          </template>
          <el-table :data="correlationTableData" border>
            <el-table-column prop="factor" label="影响因子"  />
            <el-table-column prop="correlation" label="相关系数" align="center">
              <template #default="scope">
                <span :class="getCorrelationClass(scope.row.correlation)">
                  {{ scope.row.correlation?.toFixed(3) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="pValue" label="P值" align="center">
              <template #default="scope">
                {{ scope.row.pValue?.toFixed(4) }}
              </template>
            </el-table-column>
            <el-table-column prop="significance" label="显著性" align="center">
              <template #default="scope">
                <el-tag :type="getSignificanceType(scope.row.pValue)">
                  {{ getSignificanceText(scope.row.pValue) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="strength" label="相关强度" align="center">
              <template #default="scope">
                <el-progress
                  :percentage="Math.abs(scope.row.correlation) * 100"
                  :color="getProgressColor(scope.row.correlation)"
                  :stroke-width="8"
                 />
              </template>
            </el-table-column>
            <el-table-column prop="interpretation" label="解释" show-overflow-tooltip  />
          </el-table>
        </el-card>
      </div>

      <!-- 因子详细分析 -->
      <div class="factor-analysis-section">
        <el-card shadow="never">
          <template #header>
            <h4>重要因子详细分析</h4>
          </template>
          <el-collapse v-model="activeFactors">
            <el-collapse-item
              v-for="factor in importantFactors"
              :key="factor.name"
              :title="factor.name"
              :name="factor.name"
            >
              <div class="factor-detail">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <h5>统计描述</h5>
                    <el-descriptions :column="1" border size="small">
                      <el-descriptions-item label="相关系数">
                        {{ factor.correlation?.toFixed(3) }}
                      </el-descriptions-item>
                      <el-descriptions-item label="决定系数(R²)">
                        {{ factor.rSquared?.toFixed(3) }}
                      </el-descriptions-item>
                      <el-descriptions-item label="样本量">
                        {{ factor.sampleSize }}
                      </el-descriptions-item>
                      <el-descriptions-item label="置信区间">
                        [{{ factor.confidenceInterval[0]?.toFixed(3) }}, {{ factor.confidenceInterval[1]?.toFixed(3) }}]
                      </el-descriptions-item>
                    </el-descriptions>
                  </el-col>
                  <el-col :span="12">
                    <h5>影响分析</h5>
                    <div class="factor-impact">
                      <p><strong>影响方向：</strong>{{ factor.direction }}</p>
                      <p><strong>影响程度：</strong>{{ factor.impactLevel }}</p>
                      <p><strong>实际意义：</strong>{{ factor.practicalSignificance }}</p>
                      <div class="recommendations">
                        <p><strong>管理建议：</strong></p>
                        <ul>
                          <li v-for="rec in factor.recommendations" :key="rec">{{ rec }}</li>
                        </ul>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </el-collapse-item>
          </el-collapse>
        </el-card>
      </div>

      <!-- 预测模型 -->
      <div class="prediction-section">
        <el-card shadow="never">
          <template #header>
            <h4>绩效预测模型</h4>
          </template>
          <div class="prediction-content">
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="model-info">
                  <h5>模型信息</h5>
                  <el-descriptions :column="1" border>
                    <el-descriptions-item label="模型类型">
                      多元线性回归
                    </el-descriptions-item>
                    <el-descriptions-item label="R²值">
                      {{ predictionModel.rSquared?.toFixed(3) }}
                    </el-descriptions-item>
                    <el-descriptions-item label="调整R²">
                      {{ predictionModel.adjustedRSquared?.toFixed(3) }}
                    </el-descriptions-item>
                    <el-descriptions-item label="F统计量">
                      {{ predictionModel.fStatistic?.toFixed(2) }}
                    </el-descriptions-item>
                    <el-descriptions-item label="P值">
                      {{ predictionModel.pValue?.toFixed(4) }}
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="prediction-formula">
                  <h5>预测公式</h5>
                  <div class="formula">
                    <p>绩效分数 = {{ predictionModel.intercept?.toFixed(2) }}</p>
                    <p v-for="coef in predictionModel.coefficients" :key="coef.factor">
                      {{ coef.value > 0 ? '+' : '' }}{{ coef.value?.toFixed(3) }} × {{ coef.factor }}
                    </p>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'

// 接口定义
interface CorrelationSummary {
  strongestPositive: { value: number; factor: string }
  strongestNegative: { value: number; factor: string }
  significantCount: number
  averageCorrelation: number
}

interface CorrelationData {
  factor: string
  correlation: number
  pValue: number
  interpretation: string
}

interface ImportantFactor {
  name: string
  correlation: number
  rSquared: number
  sampleSize: number
  confidenceInterval: [number, number]
  direction: string
  impactLevel: string
  practicalSignificance: string
  recommendations: string[]
}

interface PredictionModel {
  rSquared: number
  adjustedRSquared: number
  fStatistic: number
  pValue: number
  intercept: number
  coefficients: Array<{ factor: string; value: number }>
}

// 响应式数据
const loading = ref(false)
const activeFactors = ref(['工作年限', '培训投入'])

const correlationFactors = ref([
  { key: 'performance', name: 'HrHr绩效分数' },
  { key: 'workYear', name: '工作年限' },
  { key: 'education', name: '学历水平' },
  { key: 'training', name: '培训投入' },
  { key: 'salary', name: '薪资水平' },
  { key: 'age', name: '年龄' }
])

const filterForm = reactive({
  dateRange: ['2024-06', '2024-12'],
  analysisType: 'comprehensive',
  correlationThreshold: 0.3
})

const scatterForm = reactive({
  xAxis: 'workYear',
  yAxis: 'performance'
})

const correlationSummary = ref<CorrelationSummary>({
  strongestPositive: { value: 0.652, factor: '培训投入' },
  strongestNegative: { value: -0.234, factor: '年龄' },
  significantCount: 4,
  averageCorrelation: 0.318
})

const correlationTableData = ref<CorrelationData[]>([
  {
    factor: '培训投入',
    correlation: 0.652,
    pValue: 0.001,
    interpretation: '培训投入与绩效呈现强正相关，投入越多绩效越好'
  },
  {
    factor: '工作年限',
    correlation: 0.445,
    pValue: 0.003,
    interpretation: '工作经验丰富的员工通常绩效表现更佳'
  },
  {
    factor: '学历水平',
    correlation: 0.312,
    pValue: 0.015,
    interpretation: '学历与绩效存在中等正相关关系'
  },
  {
    factor: '薪资水平',
    correlation: 0.278,
    pValue: 0.028,
    interpretation: '薪资激励对绩效有一定促进作用'
  },
  {
    factor: '年龄',
    correlation: -0.234,
    pValue: 0.045,
    interpretation: '年龄与绩效呈现轻微负相关'
  },
  {
    factor: '团队规模',
    correlation: 0.156,
    pValue: 0.124,
    interpretation: '相关性不显著'
  }
])

const importantFactors = ref<ImportantFactor[]>([
  {
    name: '培训投入',
    correlation: 0.652,
    rSquared: 0.425,
    sampleSize: 120,
    confidenceInterval: [0.52, 0.75],
    direction: '正向影响',
    impactLevel: '强影响',
    practicalSignificance: '培训投入每增加10小时，绩效分数平均提升2.3分',
    recommendations: [
      '继续加大培训投入，特别是技能培训',
      '建立个性化培训计划',
      '建立培训效果跟踪机制',
      '优化培训内容和方式'
    ]
  },
  {
    name: '工作年限',
    correlation: 0.445,
    rSquared: 0.198,
    sampleSize: 120,
    confidenceInterval: [0.28, 0.58],
    direction: '正向影响',
    impactLevel: '中等影响',
    practicalSignificance: '工作年限每增加1年，绩效分数平均提升1.2分',
    recommendations: [
      '重视老员工的经验传承',
      '为资深员工提供更大发展空间',
      '建立导师制度',
      '优化新员工培养体系'
    ]
  }
])

const predictionModel = ref<PredictionModel>({
  rSquared: 0.673,
  adjustedRSquared: 0.651,
  fStatistic: 23.45,
  pValue: 0.000,
  intercept: 65.23,
  coefficients: [
    { factor: '培训投入', value: 0.425 },
    { factor: '工作年限', value: 1.234 },
    { factor: '学历水平', value: 2.156 },
    { factor: '薪资水平', value: 0.0012 }
  ]
})

// Chart refs
const heatmapRef = ref<HTMLElement>()
const correlationBarRef = ref<HTMLElement>()
const scatterRef = ref<HTMLElement>()
const regressionRef = ref<HTMLElement>()

// 方法
const handleFilterChange = () => {
  loadCorrelationData()
}

const handleExport = () => {
  ElMessage.success('导出功能开发中...')
}

const formatTooltip = (value: number) => {
  return value.toFixed(1)
}

const loadCorrelationData = async () => {
  try {
    loading.value = true
    
    // 模拟数据加载
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 更新图表
    nextTick(() => {
      initAllCharts()
    })
  } catch (__error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const initAllCharts = () => {
  initHeatmapChart()
  initCorrelationBarChart()
  updateScatterChart()
  initRegressionChart()
}

const initHeatmapChart = () => {
  if (!heatmapRef.value) return
  
  const chart = echarts.init(heatmapRef.value)
  
  const factors = ['绩效', '年限', '学历', '培训', '薪资', '年龄']
  const data = [
    [0, 0, 1.000], [0, 1, 0.445], [0, 2, 0.312], [0, 3, 0.652], [0, 4, 0.278], [0, 5, -0.234],
    [1, 0, 0.445], [1, 1, 1.000], [1, 2, 0.234], [1, 3, 0.123], [1, 4, 0.456], [1, 5, 0.678],
    [2, 0, 0.312], [2, 1, 0.234], [2, 2, 1.000], [2, 3, 0.345], [2, 4, 0.567], [2, 5, 0.123],
    [3, 0, 0.652], [3, 1, 0.123], [3, 2, 0.345], [3, 3, 1.000], [3, 4, 0.234], [3, 5, -0.156],
    [4, 0, 0.278], [4, 1, 0.456], [4, 2, 0.567], [4, 3, 0.234], [4, 4, 1.000], [4, 5, 0.345],
    [5, 0, -0.234], [5, 1, 0.678], [5, 2, 0.123], [5, 3, -0.156], [5, 4, 0.345], [5, 5, 1.000]
  ]
  
  const option = {
    tooltip: {
      position: 'top',
   
      formatter: (params: unknown) => {
        return factors[params.data[1]] + ' vs ' + factors[params.data[0]] + '<br/>相关系数: ' + params.data[2].toFixed(3)
      }
    },
    grid: {
      height: '50%',
      top: '10%'
    },
    xAxis: {
      type: 'category',
      data: factors,
      splitArea: {
        show: true
      }
    },
    yAxis: {
      type: 'category',
      data: factors,
      splitArea: {
        show: true
      }
    },
    visualMap: {
      min: -1,
      max: 1,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '15%',
      inRange: {
        color: ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#ffffcc', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026']
      }
    },
    series: [
      {
        name: '相关性',
        type: 'heatmap',
        data: data,
        label: {
          show: true,
   
          formatter: (params: unknown) => params.data[2].toFixed(2)
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  chart.setOption(option)
}

const initCorrelationBarChart = () => {
  if (!correlationBarRef.value) return
  
  const chart = echarts.init(correlationBarRef.value)
  
  const factors = correlationTableData.value.map(item => item.factor)
  const correlations = correlationTableData.value.map(item => item.correlation)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '相关系数'
    },
    yAxis: {
      type: 'category',
      data: factors,
      inverse: true
    },
    series: [
      {
        name: '相关系数',
        type: 'bar',
        data: correlations,
        itemStyle: {
   
          color: (params: unknown) => {
            const value = params.data
            if (value > 0.5) return '#67c23a'
            if (value > 0.3) return '#409eff'
            if (value > 0) return '#e6a23c'
            return '#f56c6c'
          }
        },
        label: {
          show: true,
          position: 'right',
   
          formatter: (params: unknown) => params.data.toFixed(3)
        }
      }
    ]
  }
  
  chart.setOption(option)
}

const updateScatterChart = () => {
  if (!scatterRef.value) return
  
  const chart = echarts.init(scatterRef.value)
  
  // 模拟散点数据
  const scatterData = []
  for (let i = 0; i < 100; i++) {
    const x = Math.random() * 20 + 1 // 工作年限 1-20年
    const y = 70 + x * 1.2 + Math.random() * 10 // 绩效分数
    scatterData.push([x, y])
  }
  
  const option = {
    tooltip: {
      trigger: 'item',
   
      formatter: (params: unknown) => {
        return `${scatterForm.xAxis}: ${params.data[0].toFixed(1)}<br/>${scatterForm.yAxis}: ${params.data[1].toFixed(1)}`
      }
    },
    grid: {
      left: '3%',
      right: '7%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: scatterForm.xAxis,
      splitLine: {
        lineStyle: {
          type: 'dashed'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: scatterForm.yAxis,
      splitLine: {
        lineStyle: {
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '数据点',
        type: 'scatter',
        data: scatterData,
        symbolSize: 6,
        itemStyle: {
          color: '#409eff',
          opacity: 0.7
        }
      }
    ]
  }
  
  chart.setOption(option)
}

const initRegressionChart = () => {
  if (!regressionRef.value) return
  
  const chart = echarts.init(regressionRef.value)
  
  // 模拟回归数据
  const actualData = []
  const predictedData = []
  
  for (let i = 0; i < 50; i++) {
    const x = i
    const actual = 70 + Math.random() * 20
    const predicted = 65.23 + 0.425 * (i * 0.5) + 1.234 * (i * 0.3)
    actualData.push([x, actual])
    predictedData.push([x, predicted])
  }
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['实际值', '预测值']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '样本'
    },
    yAxis: {
      type: 'value',
      name: '绩效分数'
    },
    series: [
      {
        name: '实际值',
        type: 'scatter',
        data: actualData,
        symbolSize: 4,
        itemStyle: {
          color: '#409eff',
          opacity: 0.7
        }
      },
      {
        name: '预测值',
        type: 'line',
        data: predictedData,
        smooth: true,
        itemStyle: {
          color: '#67c23a'
        },
        lineStyle: {
          width: 2
        }
      }
    ]
  }
  
  chart.setOption(option)
}

const getCorrelationClass = (correlation: number) => {
  const abs = Math.abs(correlation)
  if (abs >= 0.5) return 'correlation-strong'
  if (abs >= 0.3) return 'correlation-medium'
  if (abs >= 0.1) return 'correlation-weak'
  return 'correlation-none'
}

const getSignificanceType = (pValue: number) => {
  if (pValue < 0.01) return 'success'
  if (pValue < 0.05) return 'warning'
  return 'info'
}

const getSignificanceText = (pValue: number) => {
  if (pValue < 0.01) return '高度显著'
  if (pValue < 0.05) return '显著'
  return '不显著'
}

const getProgressColor = (correlation: number) => {
  const abs = Math.abs(correlation)
  if (abs >= 0.5) return '#67c23a'
  if (abs >= 0.3) return '#409eff'
  if (abs >= 0.1) return '#e6a23c'
  return '#f56c6c'
}

// 生命周期
onMounted(() => {
  loadCorrelationData()
})
</script>

<style scoped>
.performance-correlation-analysis {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.correlation-overview {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fff;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.correlation-factor {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.charts-section {
  margin-bottom: 20px;
}

.correlation-table-section {
  margin-bottom: 20px;
}

.factor-analysis-section {
  margin-bottom: 20px;
}

.prediction-section {
  margin-bottom: 20px;
}

.factor-detail {
  padding: 20px;
}

.factor-impact {
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.factor-impact p {
  margin-bottom: 10px;
}

.recommendations {
  margin-top: 15px;
}

.recommendations ul {
  margin: 0;
  padding-left: 20px;
}

.recommendations li {
  margin-bottom: 5px;
}

.prediction-content {
  padding: 20px;
}

.model-info, .prediction-formula {
  height: 100%;
}

.formula {
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
}

.formula p {
  margin-bottom: 5px;
  padding-left: 20px;
}

.formula p:first-child {
  padding-left: 0;
}

.correlation-strong {
  color: #67c23a;
  font-weight: bold;
}

.correlation-medium {
  color: #409eff;
  font-weight: bold;
}

.correlation-weak {
  color: #e6a23c;
}

.correlation-none {
  color: #909399;
}
</style>