<template>
  <div class="supervisor-evaluation-flow">
    <!-- 流程头部 -->
    <div class="flow-header">
      <h2>上级评价流程</h2>
      <div class="header-actions">
        <el-select v-model="selectedEmployee" placeholder="选择被评价人" @change="handleEmployeeChange">
          <el-option
            v-for="emp in subordinates"
            :key="emp.id"
            :label="`${emp.name} - ${emp.position}`"
            :value="emp.id"
           />
        </el-select>
        <el-tag type="info">考核周期：{{ evaluationPeriod }}</el-tag>
        <el-tag type="warning">待评价：{{ pendingCount }} 人</el-tag>
      </div>
    </div>

    <!-- 评价进度概览 -->
    <el-card class="progress-overview">
      <div class="progress-content">
        <div class="progress-stat">
          <div class="stat-value">{{ completedCount }}</div>
          <div class="stat-label">已完成</div>
        </div>
        <div class="progress-bar">
          <el-progress :percentage="evaluationProgress" :stroke-width="20" :color="customColors">
            <span class="progress-text">{{ completedCount }}/{{ totalCount }}</span>
          </el-progress>
        </div>
        <div class="progress-stat">
          <div class="stat-value">{{ totalCount - completedCount }}</div>
          <div class="stat-label">待评价</div>
        </div>
      </div>
    </el-card>

    <!-- 主要内容 -->
    <div v-if="currentEmployee" class="main-content">
      <el-row :gutter="20">
        <!-- 左侧：员工信息和自评内容 -->
        <el-col :span="8">
          <!-- 员工信息卡片 -->
          <el-card class="employee-info-card">
            <template #header>
              <span>员工信息</span>
            </template>
            <div class="info-content">
              <el-avatar :src="currentEmployee.avatar" :size="80"   />
              <div class="info-details">
                <h3>{{ currentEmployee.name }}</h3>
                <p>{{ currentEmployee.position }}</p>
                <p>{{ currentEmployee.department }}</p>
                <el-tag size="small">工号：{{ currentEmployee.employeeId }}</el-tag>
              </div>
            </div>
            <el-divider   />
            <div class="performance-summary">
              <div class="summary-item">
                <span class="label">自评总分</span>
                <span class="value">{{ currentEmployee.selfScore }} 分</span>
              </div>
              <div class="summary-item">
                <span class="label">目标完成率</span>
                <span class="value">{{ currentEmployee.completionRate }}%</span>
              </div>
              <div class="summary-item">
                <span class="label">上期绩效</span>
                <span class="value">{{ currentEmployee.lastPerformance }}</span>
              </div>
            </div>
          </el-card>

          <!-- 自评内容查看 -->
          <el-card class="self-evaluation-card">
            <template #header>
              <span>员工自评内容</span>
              <el-button style="float: right" type="text" @click="expandSelfEvaluation = !expandSelfEvaluation">
                {{ expandSelfEvaluation ? '收起' : '展开' }}
              </el-button>
            </template>
            <el-collapse-transition>
              <div v-show="expandSelfEvaluation">
                <div class="self-eval-section">
                  <h4>工作亮点</h4>
                  <p>{{ currentEmployee.selfEvaluation.highlights }}</p>
                </div>
                <div class="self-eval-section">
                  <h4>存在不足</h4>
                  <p>{{ currentEmployee.selfEvaluation.improvements }}</p>
                </div>
                <div class="self-eval-section">
                  <h4>下期计划</h4>
                  <p>{{ currentEmployee.selfEvaluation.nextPlan }}</p>
                </div>
              </div>
            </el-collapse-transition>
          </el-card>

          <!-- 历史绩效趋势 -->
          <el-card class="history-card">
            <template #header>
              <span>历史绩效趋势</span>
            </template>
            <div id="historyChart" style="height: 200px"></div>
          </el-card>
        </el-col>

        <!-- 右侧：评价表单 -->
        <el-col :span="16">
          <el-card class="evaluation-form-card">
            <template #header>
              <div class="card-header">
                <span>绩效评价表</span>
                <div class="header-actions">
                  <el-button size="small" @click="loadLastEvaluation" :icon="Refresh">参考上期</el-button>
                  <el-button size="small" @click="saveDraft" :icon="DocumentCopy">保存草稿</el-button>
                </div>
              </div>
            </template>

            <!-- 目标评价 -->
            <div class="goals-evaluation">
              <h3>目标完成情况评价</h3>
              <div v-for="(goal, index) in evaluationData.goals" :key="goal.id" class="goal-eval-item">
                <div class="goal-header">
                  <span class="goal-name">{{ index + 1 }}. {{ goal.name }}</span>
                  <el-tag size="small">权重: {{ goal.weight }}%</el-tag>
                </div>

                <!-- 目标详情对比 -->
                <el-descriptions :column="2" border size="small" class="goal-comparison">
                  <el-descriptions-item label="目标值">{{ goal.targetValue }}</el-descriptions-item>
                  <el-descriptions-item label="实际值">{{ goal.actualValue }}</el-descriptions-item>
                  <el-descriptions-item label="自评分">{{ goal.selfScore }} 分</el-descriptions-item>
                  <el-descriptions-item label="完成率">{{ goal.completionRate }}%</el-descriptions-item>
                </el-descriptions>

                <!-- 上级评分 -->
                <el-form :model="goal" label-width="100px">
                  <el-form-item label="上级评分" required>
                    <el-row :gutter="20">
                      <el-col :span="12">
                        <el-slider
                          v-model="goal.supervisorScore"
                          :min="0"
                          :max="100"
                          :step="5"
                          show-input
                         />
                      </el-col>
                      <el-col :span="12">
                        <el-select v-model="goal.scoreReason" placeholder="评分理由">
                          <el-option label="超额完成" value="exceeded"  />
                          <el-option label="全面完成" value="completed"  />
                          <el-option label="基本完成" value="basically"  />
                          <el-option label="部分完成" value="partially"  />
                          <el-option label="未完成" value="incomplete"  />
                        </el-select>
                      </el-col>
                    </el-row>
                  </el-form-item>

                  <el-form-item label="评价说明">
                    <el-input
                      v-model="goal.supervisorComment"
                      type="textarea"
                      :rows="2"
                      placeholder="请输入对该目标完成情况的具体评价"
                      maxlength="300"
                      show-word-limit
                      />
                  </el-form-item>
                </el-form>

                <el-divider   />
              </div>
            </div>

            <!-- 维度评价 -->
            <div class="dimension-evaluation">
              <h3>能力维度评价</h3>
              <el-form :model="evaluationData.dimensions" label-width="120px">
                <el-row :gutter="20">
                  <el-col :span="12" v-for="dim in dimensionList" :key="dim.key">
                    <el-form-item :label="dim.label" required>
                      <el-rate
                        v-model="evaluationData.dimensions[dim.key]"
                        :max="5"
                        :texts="['差', '较差', '一般', '良好', '优秀']"
                        show-text
                       />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>

            <!-- 综合评价 -->
            <div class="overall-evaluation">
              <h3>综合评价</h3>
              <el-form :model="evaluationData.overall" label-width="120px">
                <el-form-item label="总体表现" required>
                  <el-radio-group v-model="evaluationData.overall.performance">
                    <el-radio label="excellent">优秀（超出预期）</el-radio>
                    <el-radio label="good">良好（达到预期）</el-radio>
                    <el-radio label="average">一般（基本达到预期）</el-radio>
                    <el-radio label="poor">较差（低于预期）</el-radio>
                  </el-radio-group>
                </el-form-item>

                <el-form-item label="主要优点" required>
                  <el-input
                    v-model="evaluationData.overall.strengths"
                    type="textarea"
                    :rows="3"
                    placeholder="请描述员工的主要优点和突出表现"
                    maxlength="500"
                    show-word-limit
                    />
                </el-form-item>

                <el-form-item label="改进建议" required>
                  <el-input
                    v-model="evaluationData.overall.improvements"
                    type="textarea"
                    :rows="3"
                    placeholder="请提供具体的改进建议和发展方向"
                    maxlength="500"
                    show-word-limit
                    />
                </el-form-item>

                <el-form-item label="培养计划">
                  <el-input
                    v-model="evaluationData.overall.developmentPlan"
                    type="textarea"
                    :rows="2"
                    placeholder="请说明下一阶段的培养重点（选填）"
                    maxlength="300"
                    show-word-limit
                    />
                </el-form-item>

                <el-form-item label="建议等级" required>
                  <el-select v-model="evaluationData.overall.suggestedGrade" placeholder="请选择">
                    <el-option label="A - 卓越" value="A"  />
                    <el-option label="B - 优秀" value="B"  />
                    <el-option label="C - 良好" value="C"  />
                    <el-option label="D - 合格" value="D"  />
                    <el-option label="E - 待改进" value="E"  />
                  </el-select>
                  <el-tooltip content="该等级建议将提交到绩效校准会议" placement="top">
                    <el-icon style="margin-left: 10px"><InfoFilled /></el-icon>
                  </el-tooltip>
                </el-form-item>

                <el-form-item label="是否推荐">
                  <el-checkbox-group v-model="evaluationData.overall.recommendations">
                    <el-checkbox label="promotion">推荐晋升</el-checkbox>
                    <el-checkbox label="salary_increase">推荐加薪</el-checkbox>
                    <el-checkbox label="key_talent">列为关键人才</el-checkbox>
                    <el-checkbox label="training">推荐培训</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-form>
            </div>

            <!-- 反馈沟通记录 -->
            <div class="feedback-section">
              <h3>反馈沟通</h3>
              <el-form :model="evaluationData.feedback" label-width="120px">
                <el-form-item label="沟通方式">
                  <el-radio-group v-model="evaluationData.feedback.method">
                    <el-radio label="face_to_face">面对面沟通</el-radio>
                    <el-radio label="online">线上沟通</el-radio>
                    <el-radio label="written">书面反馈</el-radio>
                  </el-radio-group>
                </el-form-item>

                <el-form-item label="沟通时间" v-if="evaluationData.feedback.method !== 'written'">
                  <el-date-picker
                    v-model="evaluationData.feedback.time"
                    type="datetime"
                    placeholder="选择沟通时间"
                   />
                </el-form-item>

                <el-form-item label="沟通要点">
                  <el-input
                    v-model="evaluationData.feedback.keyPoints"
                    type="textarea"
                    :rows="2"
                    placeholder="记录沟通的主要内容和员工反馈"
                    />
                </el-form-item>
              </el-form>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 空状态 -->
    <el-empty v-else description="请选择要评价的员工"  />

    <!-- 底部操作 -->
    <div class="flow-footer" v-if="currentEmployee">
      <div class="footer-left">
        <el-button @click="prevEmployee" :disabled="currentIndex === 0">
          上一个
        </el-button>
        <span class="nav-info">{{ currentIndex + 1 }} / {{ subordinates.length }}</span>
        <el-button @click="nextEmployee" :disabled="currentIndex === subordinates.length - 1">
          下一个
        </el-button>
      </div>
      <div class="footer-right">
        <el-button @click="saveDraft">保存草稿</el-button>
        <el-button @click="preview" type="primary" plain>预览</el-button>
        <el-button type="primary" @click="submit" :loading="submitting">提交评价</el-button>
      </div>
    </div>

    <!-- 预览对话框 -->
    <el-dialog v-model="showPreview" title="评价预览" width="800px">
      <div class="preview-content">
        <h3>{{ currentEmployee?.name }} - 绩效评价结果</h3>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="考核周期">{{ evaluationPeriod }}</el-descriptions-item>
          <el-descriptions-item label="建议等级">
            <el-tag :type="getGradeType(evaluationData.overall.suggestedGrade)">
              {{ evaluationData.overall.suggestedGrade }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="目标加权得分">{{ calculateWeightedScore() }} 分</el-descriptions-item>
          <el-descriptions-item label="总体表现">{{ getPerformanceLabel(evaluationData.overall.performance) }}</el-descriptions-item>
        </el-descriptions>

        <h4>各项目标评分</h4>
        <el-table :data="evaluationData.goals" stripe>
          <el-table-column prop="name" label="目标名称"  />
          <el-table-column prop="weight" label="权重" width="80">
            <template #default="{ row }">{{ row.weight }}%</template>
          </el-table-column>
          <el-table-column prop="selfScore" label="自评分" width="80"  />
          <el-table-column prop="supervisorScore" label="上级评分" width="100"  />
          <el-table-column prop="supervisorComment" label="评价说明"  />
        </el-table>

        <h4>综合评价</h4>
        <div class="preview-overall">
          <p><strong>主要优点：</strong>{{ evaluationData.overall.strengths }}</p>
          <p><strong>改进建议：</strong>{{ evaluationData.overall.improvements }}</p>
          <p v-if="evaluationData.overall.developmentPlan">
            <strong>培养计划：</strong>{{ evaluationData.overall.developmentPlan }}
          </p>
        </div>
      </div>
      <template #footer>
        <el-button @click="showPreview = false">关闭</el-button>
        <el-button type="primary" @click="confirmSubmit">确认提交</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, DocumentCopy, InfoFilled } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 下属列表
const subordinates = ref([
  {
    id: 'emp001',
    name: 'HrHr张三',
    position: '前端工程师',
    department: '技术部',
    employeeId: 'E001',
    avatar: '/avatar1.jpg',
    selfScore: 88,
    completionRate: 92,
    lastPerformance: 'B',
    selfEvaluation: {
      highlights: '本期完成了所有既定目标，特别是在系统架构优化方面取得了显著成果...',
      improvements: '在跨部门沟通协调方面还需要加强，时间管理能力有待提升...',
      nextPlan: '下期将重点提升技术深度，承担更多的技术分享和团队培养工作...'
    }
  },
  {
    id: 'emp002',
    name: '李四',
    position: '后端工程师',
    department: '技术部',
    employeeId: 'E002',
    avatar: '/avatar2.jpg',
    selfScore: 85,
    completionRate: 88,
    lastPerformance: 'B',
    selfEvaluation: {
      highlights: '成功完成了核心模块的重构工作，提升了系统性能30%...',
      improvements: '文档编写不够及时，需要加强代码注释和技术文档的输出...',
      nextPlan: '计划深入学习微服务架构，提升系统设计能力...'
    }
  }
])

// 当前选中员工
const selectedEmployee = ref('')
const currentIndex = ref(0)
const currentEmployee = computed(() => {
  return subordinates.value.find(emp => emp.id === selectedEmployee.value)
})

// 评价数据
const evaluationData = reactive({
  goals: [
    {
      id: 'goal1',
      name: '系统架构优化',
      weight: 40,
      targetValue: '完成3个核心模块重构',
      actualValue: '完成4个核心模块重构',
      selfScore: 95,
      completionRate: 133,
      supervisorScore: 90,
      scoreReason: 'exceeded',
      supervisorComment: ''
    },
    {
      id: 'goal2',
      name: '团队技术分享',
      weight: 30,
      targetValue: '12次',
      actualValue: '10次',
      selfScore: 83,
      completionRate: 83,
      supervisorScore: 80,
      scoreReason: 'basically',
      supervisorComment: ''
    },
    {
      id: 'goal3',
      name: '代码质量提升',
      weight: 30,
      targetValue: '单测覆盖率80%',
      actualValue: '单测覆盖率85%',
      selfScore: 90,
      completionRate: 106,
      supervisorScore: 88,
      scoreReason: 'completed',
      supervisorComment: ''
    }
  ],
  dimensions: {
    workQuality: 0,
    workEfficiency: 0,
    teamwork: 0,
    innovation: 0,
    learning: 0,
    communication: 0
  },
  overall: {
    performance: '',
    strengths: '',
    improvements: '',
    developmentPlan: '',
    suggestedGrade: '',
    recommendations: []
  },
  feedback: {
    method: 'face_to_face',
    time: '',
    keyPoints: ''
  }
})

// 维度列表
const dimensionList = [
  { key: 'workQuality', label: '工作质量' },
  { key: 'workEfficiency', label: '工作效率' },
  { key: 'teamwork', label: '团队协作' },
  { key: 'innovation', label: '创新能力' },
  { key: 'learning', label: '学习能力' },
  { key: 'communication', label: '沟通能力' }
]

// 其他状态
const evaluationPeriod = ref('2025年度考核')
const expandSelfEvaluation = ref(true)
const showPreview = ref(false)
const submitting = ref(false)

// 计算属性
const completedCount = computed(() => {
  // 这里应该从实际数据计算
  return 2
})

const totalCount = computed(() => subordinates.value.length)
const pendingCount = computed(() => totalCount.value - completedCount.value)
const evaluationProgress = computed(() => {
  return Math.round((completedCount.value / totalCount.value) * 100)
})

// 自定义进度条颜色
const customColors = [
  { color: '#f56c6c', percentage: 20 },
  { color: '#e6a23c', percentage: 40 },
  { color: '#5cb87a', percentage: 60 },
  { color: '#1989fa', percentage: 80 },
  { color: '#67c23a', percentage: 100 }
]

// 方法
const handleEmployeeChange = (empId: string) => {
  currentIndex.value = subordinates.value.findIndex(emp => emp.id === empId)
  loadEmployeeData(empId)
  initHistoryChart()
}

const loadEmployeeData = (empId: string) => {
  // 加载员工相关数据，包括目标、自评等
  // 这里应该调用API获取数据
}

const loadLastEvaluation = () => {
  ElMessage.info('已加载上期评价作为参考')
  // 加载上期评价数据作为参考
}

const saveDraft = () => {
  // 保存草稿
  const draftKey = `supervisor_eval_${selectedEmployee.value}`
  localStorage.setItem(draftKey, JSON.stringify(evaluationData))
  ElMessage.success('草稿已保存')
}

const calculateWeightedScore = () => {
  const totalScore = evaluationData.goals.reduce((sum, goal) => {
    return sum + (goal.supervisorScore * goal.weight / 100)
  }, 0)
  return totalScore.toFixed(1)
}

const getGradeType = (grade: string) => {
  const map: Record<string, string> = {
    A: 'success',
    B: 'primary',
    C: '',
    D: 'warning',
    E: 'danger'
  }
  return map[grade] || 'info'
}

const getPerformanceLabel = (performance: string) => {
  const map: Record<string, string> = {
    excellent: '优秀（超出预期）',
    good: '良好（达到预期）',
    average: '一般（基本达到预期）',
    poor: '较差（低于预期）'
  }
  return map[performance] || ''
}

const prevEmployee = () => {
  if (currentIndex.value > 0) {
    currentIndex.value--
    selectedEmployee.value = subordinates.value[currentIndex.value].id
  }
}

const nextEmployee = () => {
  if (currentIndex.value < subordinates.value.length - 1) {
    currentIndex.value++
    selectedEmployee.value = subordinates.value[currentIndex.value].id
  }
}

const preview = () => {
  // 验证必填项
  const hasEmptyScore = evaluationData.goals.some(g => !g.supervisorScore)
  const hasEmptyDimension = Object.values(evaluationData.dimensions).some(v => v === 0)
  const hasEmptyOverall = !evaluationData.overall.performance || 
                          !evaluationData.overall.strengths || 
                          !evaluationData.overall.improvements ||
                          !evaluationData.overall.suggestedGrade
  
  if (hasEmptyScore || hasEmptyDimension || hasEmptyOverall) {
    ElMessage.warning('请完整填写所有必填项')
    return
  }
  
  showPreview.value = true
}

const submit = () => {
  preview()
}

const confirmSubmit = async () => {
  try {
    await ElMessageBox.confirm(
      '提交后将进入绩效校准流程，确定要提交吗？',
      '提交确认',
      {
        confirmButtonText: '确定提交',
        cancelButtonText: '再想想',
        type: 'warning'
      }
    )
    
    submitting.value = true
    
    // 模拟提交
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('评价已提交成功')
    showPreview.value = false
    
    // 清除草稿
    localStorage.removeItem(`supervisor_eval_${selectedEmployee.value}`)
    
    // 自动跳转到下一个
    if (currentIndex.value < subordinates.value.length - 1) {
      nextEmployee()
    }
    
  } catch {
    // 用户取消
  } finally {
    submitting.value = false
  }
}

// 初始化历史绩效图表
const initHistoryChart = () => {
  const chartDom = document.getElementById('historyChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['2023Q1', '2023Q2', '2023Q3', '2023Q4', '2024Q1']
    },
    yAxis: {
      type: 'value',
      min: 70,
      max: 100
    },
    series: [
      {
        name: '绩效得分',
        type: 'line',
        data: [82, 84, 85, 84, 88],
        smooth: true,
        itemStyle: { color: '#409eff' }
      }
    ]
  }
  
  myChart.setOption(option)
}

// 初始化
onMounted(() => {
  // 默认选择第一个员工
  if (subordinates.value.length > 0) {
    selectedEmployee.value = subordinates.value[0].id
    handleEmployeeChange(selectedEmployee.value)
  }
})
</script>

<style lang="scss" scoped>
.supervisor-evaluation-flow {
  padding: 20px;
  
  .flow-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      font-size: 24px;
      color: #303133;
    }
    
    .header-actions {
      display: flex;
      align-items: center;
      gap: 15px;
      
      .el-select {
        width: 200px;
      }
    }
  }
  
  .progress-overview {
    margin-bottom: 20px;
    
    .progress-content {
      display: flex;
      align-items: center;
      gap: 30px;
      
      .progress-stat {
        text-align: center;
        
        .stat-value {
          font-size: 32px;
          font-weight: bold;
          color: #409eff;
        }
        
        .stat-label {
          font-size: 14px;
          color: #909399;
          margin-top: 5px;
        }
      }
      
      .progress-bar {
        flex: 1;
        
        .progress-text {
          font-size: 14px;
          color: #606266;
        }
      }
    }
  }
  
  .main-content {
    margin-bottom: 20px;
  }
  
  .employee-info-card {
    .info-content {
      display: flex;
      gap: 20px;
      
      .info-details {
        flex: 1;
        
        h3 {
          margin: 0 0 5px;
          font-size: 18px;
          color: #303133;
        }
        
        p {
          margin: 5px 0;
          color: #606266;
          font-size: 14px;
        }
      }
    }
    
    .performance-summary {
      .summary-item {
        display: flex;
        justify-content: space-between;
        padding: 8px 0;
        
        .label {
          color: #909399;
        }
        
        .value {
          font-weight: bold;
          color: #303133;
        }
      }
    }
  }
  
  .self-evaluation-card {
    margin-top: 20px;
    
    .self-eval-section {
      margin-bottom: 15px;
      
      h4 {
        margin: 0 0 8px;
        font-size: 14px;
        color: #606266;
      }
      
      p {
        margin: 0;
        line-height: 1.6;
        color: #909399;
        font-size: 13px;
      }
    }
  }
  
  .history-card {
    margin-top: 20px;
  }
  
  .evaluation-form-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .goals-evaluation,
    .dimension-evaluation,
    .overall-evaluation,
    .feedback-section {
      margin-bottom: 30px;
      
      h3 {
        margin: 0 0 20px;
        font-size: 16px;
        color: #303133;
      }
    }
    
    .goal-eval-item {
      margin-bottom: 20px;
      padding: 15px;
      background-color: #f5f7fa;
      border-radius: 4px;
      
      .goal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        
        .goal-name {
          font-weight: bold;
          color: #303133;
        }
      }
      
      .goal-comparison {
        margin-bottom: 15px;
      }
    }
  }
  
  .flow-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
    
    .footer-left {
      display: flex;
      align-items: center;
      gap: 15px;
      
      .nav-info {
        color: #606266;
        font-size: 14px;
      }
    }
    
    .footer-right {
      display: flex;
      gap: 10px;
    }
  }
  
  .preview-content {
    h3 {
      margin: 0 0 20px;
      font-size: 18px;
      color: #303133;
    }
    
    h4 {
      margin: 20px 0 10px;
      font-size: 16px;
      color: #606266;
    }
    
    .preview-overall {
      p {
        margin: 10px 0;
        line-height: 1.6;
        
        strong {
          color: #606266;
        }
      }
    }
  }
}
</style>