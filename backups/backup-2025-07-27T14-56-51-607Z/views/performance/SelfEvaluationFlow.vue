<template>
  <div class="self-evaluation-flow">
    <!-- 流程头部 -->
    <div class="flow-header">
      <h2>绩效自评流程</h2>
      <div class="header-info">
        <el-tag type="info">考核周期：{{ evaluationPeriod }}</el-tag>
        <el-tag :type="getStatusType(evaluationStatus)">{{ getStatusLabel(evaluationStatus) }}</el-tag>
        <el-tag type="warning" v-if="remainingDays > 0">剩余 {{ remainingDays }} 天</el-tag>
        <el-tag type="danger" v-else>已逾期</el-tag>
      </div>
    </div>

    <!-- 流程进度 -->
    <el-progress :percentage="completionProgress" :status="progressStatus">
      <span class="progress-text">已完成 {{ completedItems }}/{{ totalItems }} 项</span>
    </el-progress>

    <!-- 主要内容区 -->
    <el-row :gutter="20" class="main-content">
      <!-- 左侧：目标完成情况 -->
      <el-col :span="16">
        <el-card class="evaluation-card">
          <template #header>
            <div class="card-header">
              <span>目标完成情况自评</span>
              <div class="header-actions">
                <el-button size="small" @click="saveDraft" :icon="DocumentCopy">保存草稿</el-button>
                <el-button size="small" @click="loadDraft" :icon="Refresh">加载草稿</el-button>
              </div>
            </div>
          </template>

          <!-- 目标列表 -->
          <div v-for="(goal, index) in goals" :key="goal.id" class="goal-evaluation">
            <div class="goal-header">
              <div class="goal-info">
                <span class="goal-index">{{ goal.type === 'OKR' ? 'O' : 'KPI' }}{{ index + 1 }}</span>
                <span class="goal-name">{{ goal.name }}</span>
                <el-tag size="small">权重: {{ goal.weight }}%</el-tag>
              </div>
              <div class="goal-status">
                <el-progress
                  :percentage="goal.completion"
                  :color="getProgressColor(goal.completion)"
                  style="width: 150px"
                 />
              </div>
            </div>

            <!-- 完成情况描述 -->
            <el-form :model="goal" label-position="top">
              <el-form-item label="完成情况说明" required>
                <el-input
                  v-model="goal.completionDescription"
                  type="textarea"
                  :rows="3"
                  placeholder="请详细描述目标的完成情况，包括具体成果、数据支撑等"
                  maxlength="500"
                  show-word-limit
                  />
              </el-form-item>

              <!-- OKR关键结果 -->
              <div v-if="goal.type === 'OKR' && goal.keyResults" class="key-results">
                <h4>关键结果完成情况</h4>
                <div v-for="(kr, krIndex) in goal.keyResults" :key="kr.id" class="kr-evaluation">
                  <div class="kr-header">
                    <span>KR{{ krIndex + 1 }}: {{ kr.name }}</span>
                    <span class="kr-target">目标: {{ kr.targetValue }}{{ kr.unit }}</span>
                  </div>
                  <el-row :gutter="10">
                    <el-col :span="8">
                      <el-form-item label="实际完成值">
                        <el-input v-model="kr.actualValue" placeholder="请输入实际值">
                          <template #append>{{ kr.unit }}</template>
                        </el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="完成率">
                        <el-input :value="calculateKRCompletion(kr)" readonly>
                          <template #append>%</template>
                        </el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="自评分">
                        <el-rate v-model="kr.selfScore" :max="5" show-score  />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
              </div>

              <!-- KPI指标 -->
              <div v-else-if="goal.type === 'KPI'" class="kpi-evaluation">
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="目标值">
                      <el-input :value="`${goal.targetValue} ${goal.unit}`" readonly   />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="实际完成值">
                      <el-input v-model="goal.actualValue" placeholder="请输入实际值">
                        <template #append>{{ goal.unit }}</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="完成率">
                      <el-input :value="calculateGoalCompletion(goal)" readonly>
                        <template #append>%</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>

              <!-- 自评分数 -->
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="自评分数（0-100分）" required>
                    <el-slider
                      v-model="goal.selfScore"
                      :min="0"
                      :max="100"
                      :step="5"
                      show-input
                      :marks="{
                        0: '0',
                        25: '25',
                        50: '50',
                        75: '75',
                        100: '100'
                      }"
                     />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="分数说明">
                    <el-input
                      v-model="goal.scoreReason"
                      placeholder="请说明给出该分数的理由"
                      maxlength="200"
                      show-word-limit
                      />
                  </el-form-item>
                </el-col>
              </el-row>

              <!-- 证明材料 -->
              <el-form-item label="证明材料">
                <el-upload
                  v-model:file-list="goal.attachments"
                  :action="uploadUrl"
                  multiple
                  :limit="5"
                  :on-exceed="handleExceed"
                  :before-upload="beforeUpload"
                >
                  <el-button size="small" type="primary" :icon="Upload">上传文件</el-button>
                  <template #tip>
                    <div class="el-upload__tip">
                      支持 jpg/png/pdf/doc/xls 格式，单个文件不超过 10MB
                    </div>
                  </template>
                </el-upload>
              </el-form-item>
            </el-form>

            <el-divider   />
          </div>

          <!-- 整体自评 -->
          <div class="overall-evaluation">
            <h3>整体自评</h3>
            <el-form :model="overallEvaluation" label-position="top">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="工作亮点" required>
                    <el-input
                      v-model="overallEvaluation.highlights"
                      type="textarea"
                      :rows="4"
                      placeholder="请总结本考核期的工作亮点和主要成就"
                      maxlength="500"
                      show-word-limit
                      />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="存在不足" required>
                    <el-input
                      v-model="overallEvaluation.improvements"
                      type="textarea"
                      :rows="4"
                      placeholder="请反思存在的不足和需要改进的地方"
                      maxlength="500"
                      show-word-limit
                      />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="下期计划" required>
                <el-input
                  v-model="overallEvaluation.nextPlan"
                  type="textarea"
                  :rows="3"
                  placeholder="请简述下一考核期的工作计划和改进措施"
                  maxlength="500"
                  show-word-limit
                  />
              </el-form-item>

              <el-form-item label="其他说明">
                <el-input
                  v-model="overallEvaluation.otherNotes"
                  type="textarea"
                  :rows="2"
                  placeholder="如有其他需要说明的情况，请在此填写"
                  maxlength="300"
                  show-word-limit
                  />
              </el-form-item>
            </el-form>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：辅助信息 -->
      <el-col :span="8">
        <!-- 评分参考 -->
        <el-card class="reference-card">
          <template #header>
            <span>评分参考标准</span>
          </template>
          <div class="score-reference">
            <div class="reference-item">
              <el-tag type="success">90-100分</el-tag>
              <span>超额完成，表现卓越</span>
            </div>
            <div class="reference-item">
              <el-tag type="primary">80-89分</el-tag>
              <span>全面完成，表现优秀</span>
            </div>
            <div class="reference-item">
              <el-tag>70-79分</el-tag>
              <span>基本完成，表现良好</span>
            </div>
            <div class="reference-item">
              <el-tag type="warning">60-69分</el-tag>
              <span>部分完成，需要改进</span>
            </div>
            <div class="reference-item">
              <el-tag type="danger">0-59分</el-tag>
              <span>未完成，表现不佳</span>
            </div>
          </div>
        </el-card>

        <!-- 填写进度 -->
        <el-card class="progress-card">
          <template #header>
            <span>填写进度跟踪</span>
          </template>
          <div class="progress-list">
            <div
              v-for="(item, index) in progressItems"
              :key="index"
              class="progress-item"
              :class="{ completed: item.completed }"
            >
              <el-icon v-if="item.completed" color="#67c23a"><CircleCheck /></el-icon>
              <el-icon v-else color="#909399"><Circle /></el-icon>
              <span>{{ item.label }}</span>
              <span class="progress-time" v-if="item.time">{{ item.time }}</span>
            </div>
          </div>
        </el-card>

        <!-- 操作提示 -->
        <el-card class="tips-card">
          <template #header>
            <span>温馨提示</span>
          </template>
          <el-alert :closable="false" type="info" show-icon>
            <template #default>
              <ul class="tips-list">
                <li>请如实填写自评内容，提供客观数据支撑</li>
                <li>建议使用STAR法则描述工作成果</li>
                <li>系统会自动保存草稿，可随时中断</li>
                <li>提交后将无法修改，请仔细检查</li>
              </ul>
            </template>
          </el-alert>
        </el-card>

        <!-- 历史记录 -->
        <el-card class="history-card">
          <template #header>
            <span>操作历史</span>
          </template>
          <el-timeline>
            <el-timeline-item
              v-for="(record, index) in operationHistory"
              :key="index"
              :timestamp="record.time"
              :type="record.type"
              size="small"
            >
              {{ record.action }}
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>
    </el-row>

    <!-- 底部操作 -->
    <div class="flow-footer">
      <el-button @click="saveDraft" :icon="DocumentCopy">保存草稿</el-button>
      <el-button @click="preview" :icon="View">预览</el-button>
      <el-button type="primary" @click="submit" :icon="CircleCheck" :loading="submitting">
        提交自评
      </el-button>
    </div>

    <!-- 预览对话框 -->
    <el-dialog v-model="showPreview" title="自评预览" width="800px">
      <div class="preview-content">
        <h3>目标完成情况</h3>
        <el-table :data="previewData" stripe>
          <el-table-column prop="name" label="目标名称"  />
          <el-table-column prop="weight" label="权重" width="80">
            <template #default="{ row }">{{ row.weight }}%</template>
          </el-table-column>
          <el-table-column prop="completion" label="完成率" width="100">
            <template #default="{ row }">
              <el-progress :percentage="row.completion" :width="60" type="circle"  />
            </template>
          </el-table-column>
          <el-table-column prop="selfScore" label="自评分" width="100">
            <template #default="{ row }">{{ row.selfScore }}分</template>
          </el-table-column>
        </el-table>

        <h3>整体评价</h3>
        <div class="preview-overall">
          <p><strong>工作亮点：</strong>{{ overallEvaluation.highlights }}</p>
          <p><strong>存在不足：</strong>{{ overallEvaluation.improvements }}</p>
          <p><strong>下期计划：</strong>{{ overallEvaluation.nextPlan }}</p>
        </div>

        <div class="weighted-score">
          <span>加权总分：</span>
          <span class="score">{{ calculateWeightedScore() }} 分</span>
        </div>
      </div>
      <template #footer>
        <el-button @click="showPreview = false">关闭</el-button>
        <el-button type="primary" @click="confirmSubmit">确认提交</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  DocumentCopy,
  Refresh,
  Upload,
  View,
  CircleCheck,
  Circle
} from '@element-plus/icons-vue'
import type { UploadProps } from 'element-plus'

// 评价期间和状态
const evaluationPeriod = ref('2025年度考核')
const evaluationStatus = ref('in_progress')
const deadline = ref(new Date('2025-02-01'))
const remainingDays = computed(() => {
  const days = Math.ceil((deadline.value.getTime() - Date.now()) / (1000 * 60 * 60 * 24))
  return Math.max(0, days)
})

// 目标数据
const goals = ref([
  {
    id: 'goal1',
    type: 'OKR',
    name: 'HrHr提升产品技术架构稳定性',
    weight: 40,
    completion: 0,
    completionDescription: '',
    selfScore: 0,
    scoreReason: '',
    attachments: [],
    keyResults: [
      {
        id: 'kr1',
        name: '系统可用性达到99.9%',
        targetValue: 99.9,
        unit: '%',
        actualValue: '',
        selfScore: 0
      },
      {
        id: 'kr2',
        name: '核心模块单测覆盖率达到80%',
        targetValue: 80,
        unit: '%',
        actualValue: '',
        selfScore: 0
      }
    ]
  },
  {
    id: 'goal2',
    type: 'KPI',
    name: '项目交付及时率',
    weight: 30,
    targetValue: 95,
    unit: '%',
    actualValue: '',
    completion: 0,
    completionDescription: '',
    selfScore: 0,
    scoreReason: '',
    attachments: []
  },
  {
    id: 'goal3',
    type: 'KPI',
    name: '技术分享次数',
    weight: 30,
    targetValue: 12,
    unit: '次',
    actualValue: '',
    completion: 0,
    completionDescription: '',
    selfScore: 0,
    scoreReason: '',
    attachments: []
  }
])

// 整体评价
const overallEvaluation = reactive({
  highlights: '',
  improvements: '',
  nextPlan: '',
  otherNotes: ''
})

// 进度项
const progressItems = computed(() => [
  {
    label: '填写目标完成情况',
    completed: goals.value.every(g => g.completionDescription),
    time: ''
  },
  {
    label: '填写实际完成值',
    completed: goals.value.every(g => {
      if (g.type === 'KPI') return g.actualValue
      return g.keyResults?.every(kr => kr.actualValue)
    }),
    time: ''
  },
  {
    label: '完成自评打分',
    completed: goals.value.every(g => g.selfScore > 0),
    time: ''
  },
  {
    label: '填写整体评价',
    completed: overallEvaluation.highlights && overallEvaluation.improvements && overallEvaluation.nextPlan,
    time: ''
  },
  {
    label: '上传证明材料',
    completed: goals.value.some(g => g.attachments.length > 0),
    time: ''
  }
])

// 操作历史
const operationHistory = ref([
  {
    time: '2025-01-22 09:00',
    action: '开始填写自评',
    type: 'primary'
  },
  {
    time: '2025-01-22 10:30',
    action: '自动保存草稿',
    type: 'info'
  }
])

// 其他状态
const submitting = ref(false)
const showPreview = ref(false)
const uploadUrl = '/api/performance/upload'

// 计算属性
const completedItems = computed(() => progressItems.value.filter(item => item.completed).length)
const totalItems = computed(() => progressItems.value.length)
const completionProgress = computed(() => Math.round((completedItems.value / totalItems.value) * 100))
const progressStatus = computed(() => {
  if (completionProgress.value === 100) return 'success'
  if (remainingDays.value <= 3) return 'warning'
  return ''
})

const previewData = computed(() => {
  return goals.value.map(goal => ({
    name: goal.name,
    weight: goal.weight,
    completion: goal.type === 'KPI' 
      ? calculateGoalCompletion(goal)
      : Math.round(goal.keyResults?.reduce((sum, kr) => sum + calculateKRCompletion(kr), 0) / goal.keyResults?.length || 0),
    selfScore: goal.selfScore
  }))
})

// 方法
const getStatusType = (status: string) => {
  const map: Record<string, string> = {
    draft: 'info',
    in_progress: 'warning',
    submitted: 'success',
    overdue: 'danger'
  }
  return map[status] || 'info'
}

const getStatusLabel = (status: string) => {
  const map: Record<string, string> = {
    draft: '草稿',
    in_progress: '填写中',
    submitted: '已提交',
    overdue: '已逾期'
  }
  return map[status] || status
}

const getProgressColor = (percentage: number) => {
  if (percentage >= 100) return '#67c23a'
  if (percentage >= 80) return '#409eff'
  if (percentage >= 60) return '#e6a23c'
  return '#f56c6c'
}

   
const calculateKRCompletion = (kr: unknown) => {
  if (!kr.actualValue || !kr.targetValue) return 0
  return Math.min(100, Math.round((Number(kr.actualValue) / Number(kr.targetValue)) * 100))
}

   
const calculateGoalCompletion = (goal: unknown) => {
  if (!goal.actualValue || !goal.targetValue) return 0
  return Math.min(100, Math.round((Number(goal.actualValue) / Number(goal.targetValue)) * 100))
}

const calculateWeightedScore = () => {
  const totalScore = goals.value.reduce((sum, goal) => {
    return sum + (goal.selfScore * goal.weight / 100)
  }, 0)
  return totalScore.toFixed(1)
}

const saveDraft = async () => {
  try {
    // 保存到本地存储
    const draftData = {
      goals: goals.value,
      overallEvaluation,
      savedAt: new Date().toISOString()
    }
    localStorage.setItem('self_evaluation_draft', JSON.stringify(draftData))
    
    // 记录历史
    operationHistory.value.unshift({
      time: new Date().toLocaleString(),
      action: '手动保存草稿',
      type: 'success'
    })
    
    ElMessage.success('草稿已保存')
  } catch (__error) {
    ElMessage.error('保存失败，请重试')
  }
}

const loadDraft = () => {
  const draft = localStorage.getItem('self_evaluation_draft')
  if (draft) {
    try {
      const parsed = JSON.parse(draft)
      goals.value = parsed.goals
      Object.assign(overallEvaluation, parsed.overallEvaluation)
      
      ElMessage.success(`已加载 ${new Date(parsed.savedAt).toLocaleString()} 的草稿`)
    } catch (__error) {
      ElMessage.error('草稿加载失败')
    }
  } else {
    ElMessage.info('暂无草稿')
  }
}

const handleExceed: UploadProps['onExceed'] = () => {
  ElMessage.warning('最多只能上传5个文件')
}

const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过 10MB!')
  }
  return isLt10M
}

const preview = () => {
  showPreview.value = true
}

const submit = async () => {
  // 验证必填项
  const hasEmptyDescription = goals.value.some(g => !g.completionDescription)
  const hasEmptyScore = goals.value.some(g => g.selfScore === 0)
  const hasEmptyOverall = !overallEvaluation.highlights || !overallEvaluation.improvements || !overallEvaluation.nextPlan
  
  if (hasEmptyDescription || hasEmptyScore || hasEmptyOverall) {
    ElMessage.warning('请完整填写所有必填项')
    return
  }
  
  showPreview.value = true
}

const confirmSubmit = async () => {
  try {
    await ElMessageBox.confirm(
      '提交后将无法修改，确定要提交自评吗？',
      '提交确认',
      {
        confirmButtonText: '确定提交',
        cancelButtonText: '再想想',
        type: 'warning'
      }
    )
    
    submitting.value = true
    
    // 模拟提交
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('自评已提交成功')
    evaluationStatus.value = 'submitted'
    showPreview.value = false
    
    // 清除草稿
    localStorage.removeItem('self_evaluation_draft')
    
    // 记录历史
    operationHistory.value.unshift({
      time: new Date().toLocaleString(),
      action: '提交自评',
      type: 'success'
    })
    
  } catch {
    // 用户取消
  } finally {
    submitting.value = false
  }
}

// 自动保存
   
let autoSaveTimer: unknown = null
watch([goals, overallEvaluation], () => {
  clearTimeout(autoSaveTimer)
  autoSaveTimer = setTimeout(() => {
    saveDraft()
  }, 30000) // 30秒自动保存
}, { deep: true })

// 初始化
onMounted(() => {
  // 自动加载草稿
  const hasDraft = localStorage.getItem('self_evaluation_draft')
  if (hasDraft) {
    ElMessage.info('检测到未提交的草稿，已自动加载')
    loadDraft()
  }
})
</script>

<style lang="scss" scoped>
.self-evaluation-flow {
  padding: 20px;
  
  .flow-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      font-size: 24px;
      color: #303133;
    }
    
    .header-info {
      display: flex;
      gap: 10px;
    }
  }
  
  .el-progress {
    margin-bottom: 20px;
    
    .progress-text {
      font-size: 14px;
      color: #606266;
    }
  }
  
  .main-content {
    margin-bottom: 20px;
  }
  
  .evaluation-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .goal-evaluation {
      margin-bottom: 30px;
      
      .goal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        
        .goal-info {
          display: flex;
          align-items: center;
          gap: 10px;
          
          .goal-index {
            font-size: 18px;
            font-weight: bold;
            color: #409eff;
          }
          
          .goal-name {
            font-size: 16px;
            font-weight: bold;
            color: #303133;
          }
        }
      }
      
      .key-results {
        background-color: #f5f7fa;
        padding: 15px;
        border-radius: 4px;
        margin: 15px 0;
        
        h4 {
          margin: 0 0 15px;
          color: #606266;
        }
        
        .kr-evaluation {
          margin-bottom: 15px;
          padding: 10px;
          background-color: white;
          border-radius: 4px;
          
          .kr-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 14px;
            color: #606266;
            
            .kr-target {
              color: #909399;
            }
          }
        }
      }
      
      .kpi-evaluation {
        background-color: #f5f7fa;
        padding: 15px;
        border-radius: 4px;
        margin: 15px 0;
      }
    }
    
    .overall-evaluation {
      h3 {
        margin: 0 0 20px;
        font-size: 18px;
        color: #303133;
      }
    }
  }
  
  .reference-card {
    .score-reference {
      .reference-item {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 10px;
        
        .el-tag {
          width: 90px;
        }
        
        span {
          font-size: 14px;
          color: #606266;
        }
      }
    }
  }
  
  .progress-card {
    margin-top: 20px;
    
    .progress-list {
      .progress-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 0;
        
        &.completed {
          span {
            color: #67c23a;
          }
        }
        
        .progress-time {
          margin-left: auto;
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
  
  .tips-card {
    margin-top: 20px;
    
    .tips-list {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 5px;
        font-size: 14px;
        color: #606266;
      }
    }
  }
  
  .history-card {
    margin-top: 20px;
  }
  
  .flow-footer {
    text-align: center;
    padding: 20px 0;
    border-top: 1px solid #ebeef5;
  }
  
  .preview-content {
    h3 {
      margin: 20px 0 10px;
      font-size: 16px;
      color: #303133;
    }
    
    .preview-overall {
      p {
        margin: 10px 0;
        line-height: 1.6;
        
        strong {
          color: #606266;
        }
      }
    }
    
    .weighted-score {
      text-align: center;
      margin-top: 30px;
      padding: 20px;
      background-color: #f5f7fa;
      border-radius: 4px;
      
      span {
        font-size: 16px;
        color: #606266;
        
        &.score {
          font-size: 36px;
          font-weight: bold;
          color: #409eff;
          margin-left: 10px;
        }
      }
    }
  }
}
</style>