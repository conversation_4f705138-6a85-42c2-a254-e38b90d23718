<template>
  <div class="goal-setting-flow">
    <!-- 流程头部 -->
    <div class="flow-header">
      <h2>目标制定流程</h2>
      <div class="header-actions">
        <el-button @click="saveDraft" :icon="DocumentCopy">保存草稿</el-button>
        <el-button type="primary" @click="submitGoals" :icon="CircleCheck">提交目标</el-button>
      </div>
    </div>

    <!-- 流程步骤 -->
    <el-steps :active="currentStep" finish-status="success" align-center>
      <el-step title="选择目标类型"  />
      <el-step title="设定目标内容"  />
      <el-step title="权重与对齐"  />
      <el-step title="确认提交"  />
    </el-steps>

    <!-- 步骤内容 -->
    <div class="step-content">
      <!-- 步骤1：选择目标类型 -->
      <div v-if="currentStep === 0" class="step-type-selection">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card class="type-card" :class="{ active: goalType === 'OKR' }" @click="selectGoalType('OKR')">
              <div class="type-icon">
                <el-icon :size="48" color="#409eff"><Aim /></el-icon>
              </div>
              <h3>OKR目标</h3>
              <p>目标与关键结果法，适用于挑战性目标</p>
              <ul>
                <li>强调目标的挑战性</li>
                <li>关注关键结果的可衡量性</li>
                <li>鼓励创新和突破</li>
              </ul>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card class="type-card" :class="{ active: goalType === 'KPI' }" @click="selectGoalType('KPI')">
              <div class="type-icon">
                <el-icon :size="48" color="#67c23a"><DataLine /></el-icon>
              </div>
              <h3>KPI指标</h3>
              <p>关键绩效指标，适用于常规业务目标</p>
              <ul>
                <li>聚焦核心业务指标</li>
                <li>强调可量化的结果</li>
                <li>便于绩效考核</li>
              </ul>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 步骤2：设定目标内容 -->
      <div v-if="currentStep === 1" class="step-goal-content">
        <el-row :gutter="20">
          <!-- 左侧：目标层级树 -->
          <el-col :span="8">
            <el-card class="hierarchy-card">
              <template #header>
                <span>目标层级关系</span>
              </template>
              <el-tree
                :data="goalHierarchy"
                :props="treeProps"
                default-expand-all
                highlight-current
                @node-click="handleNodeClick"
              >
                <template #default="{ node, data }">
                  <span class="tree-node">
                    <el-icon v-if="data.level === 'company'" color="#f56c6c"><Office /></el-icon>
                    <el-icon v-else-if="data.level === 'department'" color="#e6a23c"><Folder /></el-icon>
                    <el-icon v-else color="#409eff"><User /></el-icon>
                    <span class="node-label">{{ node.label }}</span>
                    <el-tag v-if="data.completed" type="success" size="small">已设定</el-tag>
                  </span>
                </template>
              </el-tree>
            </el-card>
          </el-col>

          <!-- 右侧：目标编辑表单 -->
          <el-col :span="16">
            <el-card>
              <template #header>
                <span>{{ goalType === 'OKR' ? '目标与关键结果' : '关键绩效指标' }}</span>
                <el-button style="float: right" type="primary" size="small" @click="addGoal">
                  <el-icon><Plus /></el-icon>添加{{ goalType === 'OKR' ? '目标' : '指标' }}
                </el-button>
              </template>

              <div v-for="(goal, index) in goals" :key="goal.id" class="goal-item">
                <div class="goal-header">
                  <span class="goal-index">{{ goalType === 'OKR' ? 'O' : 'KPI' }}{{ index + 1 }}</span>
                  <el-button v-if="goals.length > 1" link type="danger" @click="removeGoal(index)">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>

                <el-form :model="goal" label-width="100px">
                  <el-form-item :label="goalType === 'OKR' ? '目标名称' : '指标名称'" required>
                    <el-input v-model="goal.name" placeholder="请输入目标/指标名称"   />
                  </el-form-item>

                  <el-form-item label="描述说明">
                    <el-input v-model="goal.description" type="textarea" :rows="2" placeholder="请输入详细描述"   />
                  </el-form-item>

                  <el-form-item v-if="goalType === 'KPI'" label="目标值" required>
                    <el-row :gutter="10">
                      <el-col :span="8">
                        <el-input v-model="goal.targetValue" placeholder="目标值"   />
                      </el-col>
                      <el-col :span="8">
                        <el-select v-model="goal.unit" placeholder="单位">
                          <el-option label="%" value="percent"  />
                          <el-option label="个" value="count"  />
                          <el-option label="万元" value="money"  />
                          <el-option label="天" value="day"  />
                        </el-select>
                      </el-col>
                      <el-col :span="8">
                        <el-select v-model="goal.compareType" placeholder="对比方式">
                          <el-option label="大于等于" value="gte"  />
                          <el-option label="小于等于" value="lte"  />
                          <el-option label="等于" value="eq"  />
                        </el-select>
                      </el-col>
                    </el-row>
                  </el-form-item>

                  <el-form-item label="完成期限" required>
                    <el-date-picker
                      v-model="goal.deadline"
                      type="date"
                      placeholder="选择完成期限"
                      :disabled-date="disabledDate"
                     />
                  </el-form-item>

                  <!-- OKR的关键结果 -->
                  <div v-if="goalType === 'OKR'" class="key-results">
                    <div class="kr-header">
                      <span>关键结果</span>
                      <el-button size="small" @click="addKeyResult(goal)">
                        <el-icon><Plus /></el-icon>添加KR
                      </el-button>
                    </div>
                    <div v-for="(kr, krIndex) in goal.keyResults" :key="kr.id" class="kr-item">
                      <el-row :gutter="10">
                        <el-col :span="1">
                          <span class="kr-index">KR{{ krIndex + 1 }}</span>
                        </el-col>
                        <el-col :span="10">
                          <el-input v-model="kr.name" placeholder="关键结果描述" size="small"   />
                        </el-col>
                        <el-col :span="4">
                          <el-input v-model="kr.targetValue" placeholder="目标值" size="small"   />
                        </el-col>
                        <el-col :span="4">
                          <el-select v-model="kr.unit" placeholder="单位" size="small">
                            <el-option label="%" value="percent"  />
                            <el-option label="个" value="count"  />
                            <el-option label="万元" value="money"  />
                          </el-select>
                        </el-col>
                        <el-col :span="4">
                          <el-input v-model="kr.weight" placeholder="权重%" size="small">
                            <template #append>%</template>
                          </el-input>
                        </el-col>
                        <el-col :span="1">
                          <el-button link type="danger" size="small" @click="removeKeyResult(goal, krIndex)">
                            <el-icon><Delete /></el-icon>
                          </el-button>
                        </el-col>
                      </el-row>
                    </div>
                  </div>

                  <!-- SMART原则检查 -->
                  <el-form-item label="SMART检查">
                    <div class="smart-check">
                      <el-tag :type="goal.smart.specific ? 'success' : 'danger'">
                        {{ goal.smart.specific ? '✓' : '✗' }} 具体明确
                      </el-tag>
                      <el-tag :type="goal.smart.measurable ? 'success' : 'danger'">
                        {{ goal.smart.measurable ? '✓' : '✗' }} 可衡量
                      </el-tag>
                      <el-tag :type="goal.smart.achievable ? 'success' : 'danger'">
                        {{ goal.smart.achievable ? '✓' : '✗' }} 可达成
                      </el-tag>
                      <el-tag :type="goal.smart.relevant ? 'success' : 'danger'">
                        {{ goal.smart.relevant ? '✓' : '✗' }} 相关性
                      </el-tag>
                      <el-tag :type="goal.smart.timeBound ? 'success' : 'danger'">
                        {{ goal.smart.timeBound ? '✓' : '✗' }} 有时限
                      </el-tag>
                    </div>
                  </el-form-item>
                </el-form>
              </div>

              <!-- AI建议 -->
              <el-alert v-if="showSuggestions" title="智能建议" type="info" :closable="false">
                <ul class="suggestions">
                  <li v-for="(suggestion, index) in aiSuggestions" :key="index">{{ suggestion }}</li>
                </ul>
              </el-alert>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 步骤3：权重与对齐 -->
      <div v-if="currentStep === 2" class="step-weight-alignment">
        <el-row :gutter="20">
          <!-- 权重设置 -->
          <el-col :span="12">
            <el-card>
              <template #header>
                <span>目标权重分配</span>
                <el-tag :type="totalWeight === 100 ? 'success' : 'danger'" style="float: right">
                  总权重: {{ totalWeight }}%
                </el-tag>
              </template>

              <div class="weight-distribution">
                <div v-for="(goal, index) in goals" :key="goal.id" class="weight-item">
                  <div class="weight-info">
                    <span class="goal-name">{{ goal.name }}</span>
                    <span class="weight-value">{{ goal.weight }}%</span>
                  </div>
                  <el-slider
                    v-model="goal.weight"
                    :min="0"
                    :max="100"
                    :step="5"
                    :marks="weightMarks"
                    @change="handleWeightChange"
                   />
                </div>

                <el-divider   />

                <div class="weight-actions">
                  <el-button size="small" @click="autoBalanceWeights">自动平衡权重</el-button>
                  <el-button size="small" type="primary" @click="validateWeights">验证权重</el-button>
                </div>
              </div>
            </el-card>
          </el-col>

          <!-- 目标对齐 -->
          <el-col :span="12">
            <el-card>
              <template #header>
                <span>目标对齐关系</span>
              </template>

              <div class="goal-alignment">
                <div class="alignment-section">
                  <h4>上级目标对齐</h4>
                  <el-select v-model="alignmentData.parentGoals" multiple placeholder="选择对齐的上级目标">
                    <el-option
                      v-for="goal in parentGoalOptions"
                      :key="goal.id"
                      :label="goal.name"
                      :value="goal.id"
                     />
                  </el-select>
                  <div class="alignment-preview">
                    <div v-for="goalId in alignmentData.parentGoals" :key="goalId" class="aligned-goal">
                      <el-icon><Link /></el-icon>
                      <span>{{ getGoalNameById(goalId) }}</span>
                    </div>
                  </div>
                </div>

                <el-divider   />

                <div class="alignment-section">
                  <h4>横向协同目标</h4>
                  <el-select v-model="alignmentData.relatedGoals" multiple placeholder="选择相关协同目标">
                    <el-option
                      v-for="goal in relatedGoalOptions"
                      :key="goal.id"
                      :label="goal.name"
                      :value="goal.id"
                     />
                  </el-select>
                  <div class="alignment-preview">
                    <div v-for="goalId in alignmentData.relatedGoals" :key="goalId" class="aligned-goal">
                      <el-icon><Connection /></el-icon>
                      <span>{{ getGoalNameById(goalId) }}</span>
                    </div>
                  </div>
                </div>

                <!-- 对齐度分析 -->
                <div class="alignment-analysis">
                  <el-progress
                    :percentage="alignmentScore"
                    :color="customColors"
                    :format="(percentage) => `对齐度: ${percentage}%`"
                  />
                  <p class="analysis-tips">
                    {{ alignmentScore < 60 ? '建议加强与上级目标的关联' : '目标对齐度良好' }}
                  </p>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 步骤4：确认提交 -->
      <div v-if="currentStep === 3" class="step-confirmation">
        <el-card>
          <template #header>
            <span>目标确认</span>
          </template>

          <div class="confirmation-content">
            <!-- 目标总览 -->
            <div class="goals-summary">
              <h3>目标总览</h3>
              <el-table :data="goals" stripe>
                <el-table-column prop="name" label="目标名称"  />
                <el-table-column prop="type" label="类型" width="80">
                  <template #default>{{ goalType }}</template>
                </el-table-column>
                <el-table-column prop="weight" label="权重" width="80">
                  <template #default="{ row }">{{ row.weight }}%</template>
                </el-table-column>
                <el-table-column prop="deadline" label="完成期限" width="120">
                  <template #default="{ row }">
                    {{ new Date(row.deadline).toLocaleDateString() }}
                  </template>
                </el-table-column>
                <el-table-column label="SMART检查" width="120">
                  <template #default="{ row }">
                    <el-icon v-if="isSmartCompliant(row)" color="#67c23a"><CircleCheck /></el-icon>
                    <el-icon v-else color="#f56c6c"><CircleClose /></el-icon>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- 提交前检查 -->
            <div class="pre-submit-check">
              <h3>提交前检查</h3>
              <el-alert
                v-for="(check, index) in preSubmitChecks"
                :key="index"
                :title="check.title"
                :type="check.passed ? 'success' : 'warning'"
                :closable="false"
                show-icon
              >
                {{ check.message }}
              </el-alert>
            </div>

            <!-- 提交说明 -->
            <div class="submit-notes">
              <h3>提交说明</h3>
              <el-input
                v-model="submitNotes"
                type="textarea"
                :rows="3"
                placeholder="请输入提交说明（选填）"
                />
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 底部操作 -->
    <div class="flow-footer">
      <el-button v-if="currentStep > 0" @click="prevStep">上一步</el-button>
      <el-button v-if="currentStep < 3" type="primary" @click="nextStep" :disabled="!canProceed">
        下一步
      </el-button>
      <el-button v-if="currentStep === 3" type="success" @click="submitGoals" :loading="submitting">
        确认提交
      </el-button>
    </div>

    <!-- 目标变更历史 -->
    <el-dialog v-model="showHistory" title="目标变更历史" width="800px">
      <el-timeline>
        <el-timeline-item
          v-for="(record, index) in changeHistory"
          :key="index"
          :timestamp="record.time"
          :type="record.type"
        >
          <div class="history-content">
            <p><strong>{{ record.operator }}</strong> {{ record.action }}</p>
            <p v-if="record.detail">{{ record.detail }}</p>
          </div>
        </el-timeline-item>
      </el-timeline>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
 
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  DocumentCopy,
  CircleCheck,
  Aim,
  DataLine,
  Office,
  Folder,
  User,
  Plus,
  Delete,
  Link,
  Connection,
  CircleClose
} from '@element-plus/icons-vue'
import type { Goal, GoalType, AlignmentData } from '@/types/performance'

// 状态定义
const currentStep = ref(0)
const goalType = ref<GoalType>('OKR')
const goals = ref<Goal[]>([])
const submitting = ref(false)
const showHistory = ref(false)
const showSuggestions = ref(false)
const submitNotes = ref('')

// 目标层级数据
const goalHierarchy = ref([
  {
    id: 'company',
    label: '公司年度目标',
    level: 'company',
    completed: true,
    children: [
      {
        id: 'dept1',
        label: '技术部目标',
        level: 'department',
        completed: true,
        children: [
          {
            id: 'personal',
            label: '个人目标（待制定）',
            level: 'personal',
            completed: false
          }
        ]
      }
    ]
  }
])

// 对齐数据
const alignmentData = reactive<AlignmentData>({
  parentGoals: [],
  relatedGoals: []
})

// 树形结构配置
const treeProps = {
  children: 'children',
  label: 'label'
}

// AI建议
const aiSuggestions = ref([
  '建议将目标细化为3-5个可衡量的关键结果',
  '目标描述应包含具体的数量指标和时间节点',
  '考虑与部门重点工作的关联性'
])

// 权重标记
const weightMarks = {
  0: '0%',
  25: '25%',
  50: '50%',
  75: '75%',
  100: '100%'
}

// 上级目标选项
const parentGoalOptions = ref([
  { id: 'pg1', name: 'HrHr提升产品技术架构稳定性' },
  { id: 'pg2', name: '加快产品迭代速度' },
  { id: 'pg3', name: '提高团队技术能力' }
])

// 协同目标选项
const relatedGoalOptions = ref([
  { id: 'rg1', name: '产品部：Q1新功能上线计划' },
  { id: 'rg2', name: '测试部：自动化测试覆盖率提升' },
  { id: 'rg3', name: '运维部：系统稳定性保障' }
])

// 变更历史
const changeHistory = ref([
  {
    time: '2025-01-22 10:30',
    operator: '张三',
    action: '创建了目标草稿',
    type: 'primary'
  },
  {
    time: '2025-01-22 14:20',
    operator: '系统',
    action: '自动保存草稿',
    detail: '检测到内容变更，已自动保存',
    type: 'info'
  }
])

// 自定义进度条颜色
const customColors = [
  { color: '#f56c6c', percentage: 20 },
  { color: '#e6a23c', percentage: 40 },
  { color: '#5cb87a', percentage: 60 },
  { color: '#1989fa', percentage: 80 },
  { color: '#67c23a', percentage: 100 }
]

// 计算属性
const totalWeight = computed(() => {
  return goals.value.reduce((sum, goal) => sum + (goal.weight || 0), 0)
})

const alignmentScore = computed(() => {
  const hasParent = alignmentData.parentGoals.length > 0
  const hasRelated = alignmentData.relatedGoals.length > 0
  const weightBalanced = totalWeight.value === 100
  
  let score = 40 // 基础分
  if (hasParent) score += 30
  if (hasRelated) score += 20
  if (weightBalanced) score += 10
  
  return score
})

const canProceed = computed(() => {
  switch (currentStep.value) {
    case 0:
      return !!goalType.value
    case 1:
      return goals.value.length > 0 && goals.value.every(g => g.name && g.deadline)
    case 2:
      return totalWeight.value === 100
    case 3:
      return true
    default:
      return false
  }
})

const preSubmitChecks = computed(() => {
  return [
    {
      title: '目标完整性检查',
      passed: goals.value.length > 0 && goals.value.every(g => g.name && g.deadline),
      message: goals.value.length > 0 ? '所有目标信息已填写完整' : '请至少设定一个目标'
    },
    {
      title: '权重分配检查',
      passed: totalWeight.value === 100,
      message: totalWeight.value === 100 ? '权重分配正确' : `当前总权重为${totalWeight.value}%，需要调整为100%`
    },
    {
      title: 'SMART原则检查',
      passed: goals.value.every(g => isSmartCompliant(g)),
      message: goals.value.every(g => isSmartCompliant(g)) ? '所有目标符合SMART原则' : '部分目标未完全符合SMART原则'
    },
    {
      title: '目标对齐检查',
      passed: alignmentScore.value >= 60,
      message: alignmentScore.value >= 60 ? '目标对齐度良好' : '建议加强目标对齐'
    }
  ]
})

// 方法
const selectGoalType = (type: GoalType) => {
  goalType.value = type
  if (goals.value.length === 0) {
    addGoal()
  }
}

const addGoal = () => {
  const newGoal: Goal = {
    id: `goal_${Date.now()}`,
    name: '',
    description: '',
    type: goalType.value,
    weight: 0,
    deadline: '',
    status: 'draft',
    smart: {
      specific: false,
      measurable: false,
      achievable: false,
      relevant: false,
      timeBound: false
    }
  }
  
  if (goalType.value === 'OKR') {
    newGoal.keyResults = []
  } else {
    newGoal.targetValue = ''
    newGoal.unit = ''
    newGoal.compareType = 'gte'
  }
  
  goals.value.push(newGoal)
}

const removeGoal = (index: number) => {
  goals.value.splice(index, 1)
}

const addKeyResult = (goal: Goal) => {
  if (!goal.keyResults) goal.keyResults = []
  goal.keyResults.push({
    id: `kr_${Date.now()}`,
    name: '',
    targetValue: '',
    unit: 'percent',
    weight: 0
  })
}

const removeKeyResult = (goal: Goal, index: number) => {
  goal.keyResults?.splice(index, 1)
}

   
const handleNodeClick = (data: unknown) => {
  if (data.level === 'personal') {
    showSuggestions.value = true
  }
}

const handleWeightChange = () => {
  // 实时更新SMART检查
  checkSmartPrinciples()
}

const autoBalanceWeights = () => {
  const count = goals.value.length
  if (count === 0) return
  
  const avgWeight = Math.floor(100 / count)
  const remainder = 100 - avgWeight * count
  
  goals.value.forEach((goal, index) => {
    goal.weight = avgWeight + (index < remainder ? 1 : 0)
  })
  
  ElMessage.success('权重已自动平衡')
}

const validateWeights = () => {
  if (totalWeight.value === 100) {
    ElMessage.success('权重分配验证通过')
  } else {
    ElMessage.error(`当前总权重为${totalWeight.value}%，需要调整为100%`)
  }
}

const checkSmartPrinciples = () => {
  goals.value.forEach(goal => {
    // 具体明确：名称和描述都填写
    goal.smart.specific = !!(goal.name && goal.description)
    
    // 可衡量：有目标值或关键结果
    goal.smart.measurable = goalType.value === 'KPI' 
      ? !!(goal.targetValue && goal.unit)
      : !!(goal.keyResults && goal.keyResults.length > 0)
    
    // 可达成：这里简化处理，实际需要更复杂的判断
    goal.smart.achievable = true
    
    // 相关性：有对齐关系
    goal.smart.relevant = alignmentData.parentGoals.length > 0
    
    // 有时限：设置了截止日期
    goal.smart.timeBound = !!goal.deadline
  })
}

const isSmartCompliant = (goal: Goal) => {
  return Object.values(goal.smart).every(v => v === true)
}

const getGoalNameById = (id: string) => {
  const allGoals = [...parentGoalOptions.value, ...relatedGoalOptions.value]
  return allGoals.find(g => g.id === id)?.name || ''
}

const disabledDate = (date: Date) => {
  return date < new Date()
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const nextStep = () => {
  if (currentStep.value < 3 && canProceed.value) {
    currentStep.value++
    
    // 进入权重设置步骤时，自动检查SMART原则
    if (currentStep.value === 2) {
      checkSmartPrinciples()
      if (totalWeight.value === 0) {
        autoBalanceWeights()
      }
    }
  }
}

const saveDraft = async () => {
  try {
    // 模拟保存
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    changeHistory.value.unshift({
      time: new Date().toLocaleString(),
      operator: '当前用户',
      action: '保存了目标草稿',
      type: 'success'
    })
    
    ElMessage.success('草稿已保存')
  } catch (__error) {
    ElMessage.error('保存失败，请重试')
  }
}

const submitGoals = async () => {
  // 最终确认
  try {
    await ElMessageBox.confirm(
      '确认提交目标后将进入审批流程，是否继续？',
      '提交确认',
      {
        confirmButtonText: '确认提交',
        cancelButtonText: '再想想',
        type: 'warning'
      }
    )
    
    submitting.value = true
    
    // 模拟提交
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('目标已提交，等待上级审批')
    
    // 记录历史
    changeHistory.value.unshift({
      time: new Date().toLocaleString(),
      operator: '当前用户',
      action: '提交了目标',
      detail: submitNotes.value || '无',
      type: 'success'
    })
    
  } catch {
    // 用户取消
  } finally {
    submitting.value = false
  }
}

// 监听目标变化，实时检查SMART原则
watch(goals, () => {
  checkSmartPrinciples()
}, { deep: true })

// 初始化
onMounted(() => {
  // 如果有草稿，加载草稿
  const draft = localStorage.getItem('goal_draft')
  if (draft) {
    const parsed = JSON.parse(draft)
    goals.value = parsed.goals || []
    goalType.value = parsed.goalType || 'OKR'
    alignmentData.parentGoals = parsed.alignmentData?.parentGoals || []
    alignmentData.relatedGoals = parsed.alignmentData?.relatedGoals || []
  }
})
</script>

<style lang="scss" scoped>
.goal-setting-flow {
  padding: 20px;
  
  .flow-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    
    h2 {
      margin: 0;
      font-size: 24px;
      color: #303133;
    }
  }
  
  .step-content {
    min-height: 500px;
    margin: 30px 0;
  }
  
  // 步骤1：类型选择
  .step-type-selection {
    .type-card {
      cursor: pointer;
      transition: all 0.3s;
      height: 100%;
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
      
      &.active {
        border-color: #409eff;
        
        .type-icon {
          background-color: #ecf5ff;
        }
      }
      
      .type-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background-color: #f5f7fa;
      }
      
      h3 {
        margin: 0 0 10px;
        font-size: 20px;
        text-align: center;
      }
      
      p {
        color: #909399;
        margin-bottom: 20px;
        text-align: center;
      }
      
      ul {
        list-style: none;
        padding: 0;
        
        li {
          padding: 5px 0;
          color: #606266;
          
          &:before {
            content: '✓';
            margin-right: 8px;
            color: #67c23a;
          }
        }
      }
    }
  }
  
  // 步骤2：目标内容
  .step-goal-content {
    .hierarchy-card {
      height: 100%;
      
      .tree-node {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .node-label {
          flex: 1;
        }
      }
    }
    
    .goal-item {
      border: 1px solid #ebeef5;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      
      .goal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        
        .goal-index {
          font-size: 18px;
          font-weight: bold;
          color: #409eff;
        }
      }
      
      .key-results {
        background-color: #f5f7fa;
        padding: 15px;
        border-radius: 4px;
        margin-top: 15px;
        
        .kr-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
          
          span {
            font-weight: bold;
            color: #606266;
          }
        }
        
        .kr-item {
          margin-bottom: 10px;
          
          .kr-index {
            font-weight: bold;
            color: #67c23a;
          }
        }
      }
      
      .smart-check {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
      }
    }
    
    .suggestions {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 5px;
        color: #606266;
      }
    }
  }
  
  // 步骤3：权重对齐
  .step-weight-alignment {
    .weight-distribution {
      .weight-item {
        margin-bottom: 20px;
        
        .weight-info {
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;
          
          .goal-name {
            font-weight: bold;
            color: #303133;
          }
          
          .weight-value {
            color: #409eff;
            font-weight: bold;
          }
        }
      }
      
      .weight-actions {
        text-align: center;
      }
    }
    
    .goal-alignment {
      .alignment-section {
        margin-bottom: 20px;
        
        h4 {
          margin: 0 0 10px;
          color: #606266;
        }
        
        .alignment-preview {
          margin-top: 10px;
          
          .aligned-goal {
            display: flex;
            align-items: center;
            gap: 5px;
            padding: 5px 10px;
            background-color: #f5f7fa;
            border-radius: 4px;
            margin-bottom: 5px;
            
            span {
              font-size: 14px;
              color: #606266;
            }
          }
        }
      }
      
      .alignment-analysis {
        margin-top: 20px;
        padding: 15px;
        background-color: #f5f7fa;
        border-radius: 4px;
        
        .analysis-tips {
          margin-top: 10px;
          color: #909399;
          font-size: 14px;
          text-align: center;
        }
      }
    }
  }
  
  // 步骤4：确认提交
  .step-confirmation {
    .confirmation-content {
      .goals-summary,
      .pre-submit-check,
      .submit-notes {
        margin-bottom: 30px;
        
        h3 {
          margin: 0 0 15px;
          font-size: 16px;
          color: #303133;
        }
      }
      
      .pre-submit-check {
        .el-alert {
          margin-bottom: 10px;
        }
      }
    }
  }
  
  .flow-footer {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #ebeef5;
  }
  
  .history-content {
    p {
      margin: 5px 0;
      
      &:first-child {
        color: #303133;
      }
      
      &:last-child {
        color: #909399;
        font-size: 14px;
      }
    }
  }
}
</style>